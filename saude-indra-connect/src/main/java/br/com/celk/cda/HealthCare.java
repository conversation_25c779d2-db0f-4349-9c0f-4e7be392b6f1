package br.com.celk.cda;

import java.util.ArrayList;
import java.util.Date;

public class HealthCare {
    
    private String healthCareCode;
    private String typeHealthCareCode;
    private String typeHealthCareDisplayName;
    private Date initialDate;
    private Date finalDate;

    private Company company;
    private Author author;

    private Patient patient;

    private ArrayList<Procedure> procedures;
    private ArrayList<Diagnosis> diagnostics;
    private ClinicalNote clinicalNote;

    public HealthCare() {
    }

    public HealthCare(String healthCareCode, String typeHealthCareCode, String typeHealthCareDisplayName, Date initialDate,
            Date finalDate, Company company, Author author, Patient patient, ArrayList<Procedure> procedures,
            ArrayList<Diagnosis> diagnostics, ClinicalNote clinicalNote) {
        this.healthCareCode = healthCareCode;
        this.typeHealthCareCode = typeHealthCareCode;
        this.typeHealthCareDisplayName = typeHealthCareDisplayName;
        this.initialDate = initialDate;
        this.finalDate = finalDate;
        this.company = company;
        this.author = author;
        this.patient = patient;
        this.procedures = procedures;
        this.diagnostics = diagnostics;
        this.clinicalNote = clinicalNote;
    }

    public String getHealthCareCode() {
        return healthCareCode;
    }

    public void setHealthCareCode(String healthCareCode) {
        this.healthCareCode = healthCareCode;
    }

    public String getTypeHealthCareCode() {
        return typeHealthCareCode;
    }

    public void setTypeHealthCareCode(String typeHealthCareCode) {
        this.typeHealthCareCode = typeHealthCareCode;
    }

    public String getTypeHealthCareDisplayName() {
        return typeHealthCareDisplayName;
    }

    public void setTypeHealthCareDisplayName(String typeHealthCareDisplayName) {
        this.typeHealthCareDisplayName = typeHealthCareDisplayName;
    }

    public Date getInitialDate() {
        return initialDate;
    }

    public void setInitialDate(Date initialDate) {
        this.initialDate = initialDate;
    }

    public Date getFinalDate() {
        return finalDate;
    }

    public void setFinalDate(Date finalDate) {
        this.finalDate = finalDate;
    }

    public Company getCompany() {
        return company;
    }

    public void setCompany(Company company) {
        this.company = company;
    }

    public Author getAuthor() {
        return author;
    }

    public void setAuthor(Author author) {
        this.author = author;
    }

    public Patient getPatient() {
        return patient;
    }

    public void setPatient(Patient patient) {
        this.patient = patient;
    }

    public ArrayList<Procedure> getProcedures() {
        return procedures;
    }

    public void setProcedures(ArrayList<Procedure> procedures) {
        this.procedures = procedures;
    }

    public ArrayList<Diagnosis> getDiagnostics() {
        return diagnostics;
    }

    public void setDiagnostics(ArrayList<Diagnosis> diagnostics) {
        this.diagnostics = diagnostics;
    }

    public ClinicalNote getClinicalNote() {
        return clinicalNote;
    }

    public void setClinicalNote(ClinicalNote clinicalNote) {
        this.clinicalNote = clinicalNote;
    }

}