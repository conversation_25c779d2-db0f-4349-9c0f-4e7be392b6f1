# Validade do Token reCAPTCHA v3

## ⏰ Características do Token

### **Tempo de Vida:**
- **Duração:** ~2 minutos (120 segundos)
- **Uso:** Único (one-time use)
- **Geração:** Automática pelo JavaScript
- **Expiração:** Automática pelo Google

### **Comportamento do Google:**
```json
// Token válido
{
  "success": true,
  "score": 0.9,
  "action": "search",
  "hostname": "localhost",
  "challenge_ts": "2025-06-18T17:07:09Z"
}

// Token expirado
{
  "success": false,
  "error-codes": ["timeout-or-duplicate"]
}
```

## 🚨 Problema Identificado

### **Cache Sem Expiração (Anterior):**
```java
// PROBLEMA: Cache nunca expira
private Boolean recaptchaValidado = false;

if (!recaptchaValidado) {
    // Valida uma vez
    recaptchaValidado = true; // ← Nunca expira!
}
```

### **Cenário Problemático:**
1. **T=0:** Usuário acessa página, token gerado
2. **T=30s:** Primeira consulta, token validado, cache=true
3. **T=150s:** Segunda consulta, token expirado, mas cache=true
4. **Resultado:** Consulta passa sem validação real!

## ✅ Solução: Cache com Expiração

### **Implementação Corrigida:**
```java
// Cache com expiração
private Boolean recaptchaValidado = false;
private Long recaptchaValidadoTimestamp = null;
private static final long RECAPTCHA_CACHE_DURATION_MS = 90000; // 90 segundos

private void executarAntesProcurar() throws ValidacaoException {
    long currentTime = System.currentTimeMillis();
    boolean cacheValido = recaptchaValidado && 
                          recaptchaValidadoTimestamp != null && 
                          (currentTime - recaptchaValidadoTimestamp) < RECAPTCHA_CACHE_DURATION_MS;
    
    if (!cacheValido) {
        // Cache expirado ou primeira vez - validar novamente
        validarRecaptcha();
        recaptchaValidado = true;
        recaptchaValidadoTimestamp = currentTime;
    } else {
        // Cache ainda válido - pular validação
        System.out.println("Cache reCAPTCHA ainda válido");
    }
}
```

### **Benefícios da Solução:**

1. **Segurança:** Cache expira antes do token
2. **Performance:** Evita validações desnecessárias
3. **Flexibilidade:** Duração configurável
4. **Logs:** Monitoramento detalhado

## 📊 Cronograma de Validade

### **Timeline Típica:**
```
T=0s    │ Página carregada, JavaScript executa
        │ Token gerado pelo Google
        │
T=30s   │ Primeira consulta
        │ ✅ Token válido, validação OK
        │ 📝 Cache definido (válido até T=120s)
        │
T=60s   │ Segunda consulta
        │ ✅ Cache válido, pula validação
        │
T=90s   │ Terceira consulta
        │ ✅ Cache válido, pula validação
        │
T=120s  │ Quarta consulta
        │ ⚠️ Cache expirado, nova validação necessária
        │ ❌ Token original expirado
        │ 🔄 JavaScript gera novo token
        │ ✅ Nova validação OK
```

### **Configuração de Tempos:**
```java
// Tempos recomendados
private static final long RECAPTCHA_CACHE_DURATION_MS = 90000;  // 90s (seguro)
// private static final long RECAPTCHA_CACHE_DURATION_MS = 60000;  // 60s (conservador)
// private static final long RECAPTCHA_CACHE_DURATION_MS = 110000; // 110s (agressivo)
```

## 🔧 Configurações Avançadas

### **Estratégia 1: Cache Conservador (60s)**
```java
private static final long RECAPTCHA_CACHE_DURATION_MS = 60000; // 1 minuto
```
- **Vantagem:** Máxima segurança
- **Desvantagem:** Mais validações

### **Estratégia 2: Cache Balanceado (90s)**
```java
private static final long RECAPTCHA_CACHE_DURATION_MS = 90000; // 1.5 minutos
```
- **Vantagem:** Equilibrio segurança/performance
- **Desvantagem:** Nenhuma significativa

### **Estratégia 3: Cache Agressivo (110s)**
```java
private static final long RECAPTCHA_CACHE_DURATION_MS = 110000; // 1.8 minutos
```
- **Vantagem:** Máxima performance
- **Desvantagem:** Risco de token expirar

## 📋 Logs de Monitoramento

### **Primeira Validação:**
```
DEBUG: Executando validação reCAPTCHA v3 pela primeira vez
DEBUG: reCAPTCHA v3 validado com sucesso - Score: 0.9
```

### **Cache Válido:**
```
DEBUG: reCAPTCHA v3 ainda válido por mais 45 segundos, pulando validação
```

### **Cache Expirado:**
```
DEBUG: Cache reCAPTCHA expirado após 95 segundos, revalidando...
DEBUG: reCAPTCHA v3 validado com sucesso - Score: 0.8
```

## 🚨 Cenários de Erro

### **Token Expirado Durante Cache:**
```
DEBUG: Cache reCAPTCHA expirado após 95 segundos, revalidando...
DEBUG ReCaptchaV3Util - Google response: {"success": false, "error-codes": ["timeout-or-duplicate"]}
Por favor, complete a verificação reCAPTCHA
```

**Solução:** JavaScript automaticamente gera novo token

### **Múltiplas Abas/Janelas:**
Cada aba tem sua própria instância da página, então cada uma tem seu próprio cache.

### **Sessão Expirada:**
Cache é resetado quando usuário recarrega a página.

## 🎯 Melhores Práticas

### **1. Duração do Cache:**
- **Recomendado:** 90 segundos
- **Nunca maior que:** 110 segundos
- **Nunca menor que:** 30 segundos

### **2. Monitoramento:**
```java
// Log detalhado para análise
System.out.println("DEBUG: Cache status - Válido: " + cacheValido + 
                   ", Tempo restante: " + tempoRestante + "s");
```

### **3. Fallback:**
```java
// Se validação falhar, limpar cache
if (!result.isSuccess()) {
    recaptchaValidado = false;
    recaptchaValidadoTimestamp = null;
    throw new ValidacaoException("reCAPTCHA inválido");
}
```

### **4. Configuração Flexível:**
```java
// Permitir configuração via parâmetro
private long getCacheDuration() {
    try {
        String duration = BOFactory.getBO(CommomFacade.class)
            .getParametro("ReCaptchaCacheDurationSeconds");
        return Long.parseLong(duration) * 1000;
    } catch (Exception e) {
        return 90000; // Padrão 90s
    }
}
```

## ✅ Resultado Final

### **Segurança Aprimorada:**
- ✅ Cache expira antes do token
- ✅ Revalidação automática quando necessário
- ✅ Proteção contra tokens expirados
- ✅ Logs detalhados para monitoramento

### **Performance Otimizada:**
- ✅ Evita validações desnecessárias
- ✅ Reduz latência em consultas rápidas
- ✅ Minimiza requisições ao Google
- ✅ Experiência fluida para usuário

### **Flexibilidade:**
- ✅ Duração configurável
- ✅ Logs detalhados
- ✅ Fallback automático
- ✅ Monitoramento em tempo real

**A implementação agora respeita corretamente a validade do token reCAPTCHA v3!** 🛡️
