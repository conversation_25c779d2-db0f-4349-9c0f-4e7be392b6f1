package br.com.ksisolucoes.report.patrimonio;

import br.com.ksisolucoes.bo.BOGenericImpl;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.patrimonio.interfaces.dto.RelacaoPatrimoniosDTOParam;
import br.com.ksisolucoes.report.patrimonio.interfaces.facade._PatrimonioReportFacadeLocal;
import br.com.ksisolucoes.report.patrimonio.interfaces.facade._PatrimonioReportFacadeRemote;

import javax.ejb.Stateless;
import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;

@Stateless
@TransactionAttribute(TransactionAttributeType.REQUIRED)
public class PatrimonioReportBO extends BOGenericImpl implements _PatrimonioReportFacadeLocal, _PatrimonioReportFacadeRemote {

    @Override
    public DataReport relatorioDetalhamentoLicitacoes(RelacaoPatrimoniosDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelacaoPatrimonios(param));
    }

}
