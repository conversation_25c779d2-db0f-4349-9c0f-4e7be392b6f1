package br.com.ksisolucoes.report.entrada.recebimento;

import br.com.ksisolucoes.bo.BOGenericImpl;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.recebimento.consulta.query.QueryEntradaRegistroItemNotaFiscal;
import br.com.ksisolucoes.report.entrada.recebimento.interfaces.dto.QueryEntradaRegistroItemNotaFiscalDTO;
import br.com.ksisolucoes.report.entrada.recebimento.interfaces.dto.QueryEntradaRegistroItemNotaFiscalDTOParam;
import br.com.ksisolucoes.report.entrada.recebimento.interfaces.dto.RelatorioRecebimentoResumidoParam;
import br.com.ksisolucoes.report.entrada.recebimento.interfaces.dto.RelatorioRegistroItemNotaFiscalParam;
import br.com.ksisolucoes.report.entrada.recebimento.interfaces.facade._RegistroNotaFiscalReportFacadeLocal;
import br.com.ksisolucoes.report.entrada.recebimento.interfaces.facade._RegistroNotaFiscalReportFacadeRemote;
import br.com.ksisolucoes.report.entrada.recebimento.relatorio.RelatorioRecebimentoResumido;
import br.com.ksisolucoes.report.entrada.recebimento.relatorio.RelatorioRegistroItemNotaFiscal;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

import javax.ejb.Stateless;
import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import java.util.List;



/**
 * <AUTHOR> Barbosa Schmichtemberg
 */

@Stateless  
@TransactionAttribute(TransactionAttributeType.REQUIRED)
public class RegistroNotaFiscalReportBO extends BOGenericImpl implements  _RegistroNotaFiscalReportFacadeLocal, _RegistroNotaFiscalReportFacadeRemote {

    public List<QueryEntradaRegistroItemNotaFiscalDTO> getEntradaRegistroItemNotaFiscals(QueryEntradaRegistroItemNotaFiscalDTOParam param) throws DAOException, ValidacaoException {
            return ((QueryEntradaRegistroItemNotaFiscal) this.executor.executeQuery(new QueryEntradaRegistroItemNotaFiscal(param))).getDtoList();
    }

    public DataReport getRelatorioRegistroItemNotaFiscal(RelatorioRegistroItemNotaFiscalParam param) throws ReportException{
        return this.executorAsync.executeDataReport(new RelatorioRegistroItemNotaFiscal(param));
    }

    public DataReport getRelatorioRegistroItemNotaFiscalQWE(RelatorioRegistroItemNotaFiscalParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioRegistroItemNotaFiscal(param));
    }

    public DataReport getRelatorioRecebimentoResumido(RelatorioRecebimentoResumidoParam param) throws ReportException{
        return this.executorAsync.executeDataReport(new RelatorioRecebimentoResumido(param));
    }


}
