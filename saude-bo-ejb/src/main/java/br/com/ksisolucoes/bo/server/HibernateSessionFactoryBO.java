package br.com.ksisolucoes.bo.server;

import br.com.ksisolucoes.bo.server.interfaces.facade._HibernateSessionFactoryFacadeLocal;
import br.com.ksisolucoes.bo.server.interfaces.facade._HibernateSessionFactoryFacadeRemote;
import org.hibernate.Session;

import javax.ejb.Stateless;
import javax.persistence.PersistenceContext;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class HibernateSessionFactoryBO implements _HibernateSessionFactoryFacadeRemote, _HibernateSessionFactoryFacadeLocal{

    @PersistenceContext(unitName="SaudeDefaultPU")
    private Session session;

    @PersistenceContext(unitName="SaudeLeituraPU")
    private Session sessionLeitura;

    @Override
    public Session getSession(){
        return session;
    }

    @Override
    public Session getSessionLeitura(){
        return sessionLeitura;
    }

}
