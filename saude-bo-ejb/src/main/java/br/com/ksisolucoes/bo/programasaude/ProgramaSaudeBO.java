/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.programasaude;

import br.com.ksisolucoes.bo.BOGenericImpl;
import br.com.ksisolucoes.bo.programasaude.ExcluirGrupoProgramaSaude;
import br.com.ksisolucoes.bo.programasaude.SalvarGrupoProgramaSaude;
import br.com.ksisolucoes.bo.programasaude.hiperdia.CadastrarHiperdia;
import br.com.ksisolucoes.bo.programasaude.hiperdia.RemoverHiperdia;
import br.com.ksisolucoes.bo.programasaude.interfaces.dto.GrupoProgramaSaudeDTO;
import br.com.ksisolucoes.bo.programasaude.interfaces.facade._ProgramaSaudeFacadeLocal;
import br.com.ksisolucoes.bo.programasaude.interfaces.facade._ProgramaSaudeFacadeRemote;
import br.com.ksisolucoes.bo.programasaude.programasaudeusuario.CadastrarProgramaSaude;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.programasaude.Hiperdia;
import br.com.ksisolucoes.vo.programasaude.HiperdiaMedicamento;
import br.com.ksisolucoes.vo.programasaude.ProgramaSaude;
import br.com.ksisolucoes.vo.programasaude.ProgramaSaudeUsuario;
import br.com.ksisolucoes.vo.programasaude.ProgramaSaudeUsuario.TipoProgramaSaude;
import java.util.List;
import javax.ejb.Stateless;

/**
 *
 * <AUTHOR>
 */
@Stateless 
public class ProgramaSaudeBO extends BOGenericImpl implements _ProgramaSaudeFacadeLocal, _ProgramaSaudeFacadeRemote {

    @Override
    public Hiperdia cadastrarHiperdia(UsuarioCadsus usuarioCadsus,Hiperdia hiperdia, List<HiperdiaMedicamento> medicamentos) throws DAOException, ValidacaoException {
        CadastrarHiperdia cadastrarHiperdia = (CadastrarHiperdia) this.executor.executeReturn(new CadastrarHiperdia(usuarioCadsus,hiperdia, medicamentos));
        return cadastrarHiperdia.getHiperdia();
    }

    @Override
    public void removerHiperdia(Hiperdia hiperdia) throws DAOException, ValidacaoException {
        this.executor.execute(new RemoverHiperdia(hiperdia));
    }

    @Override
    public ProgramaSaudeUsuario cadastrarProgramaSaude( UsuarioCadsus usuarioCadsus, TipoProgramaSaude tipo) throws DAOException, ValidacaoException {
        CadastrarProgramaSaude cadastrarProgramaSaude = (CadastrarProgramaSaude) this.executor.executeReturn(new CadastrarProgramaSaude(usuarioCadsus, tipo));
        return cadastrarProgramaSaude.getProgramaSaudeUsuario();
    }

    @Override
    public void salvarGrupoProgramaSaude(GrupoProgramaSaudeDTO dto) throws DAOException, ValidacaoException {
        this.executor.execute(new SalvarGrupoProgramaSaude(dto));
    }

    @Override
    public void excluirGrupoProgramaSaude(GrupoProgramaSaudeDTO dto) throws DAOException, ValidacaoException {
        this.executor.execute(new ExcluirGrupoProgramaSaude(dto));
    }

    @Override
    public Class getReferenceClass() {
        return ProgramaSaude.class;
    }
}
