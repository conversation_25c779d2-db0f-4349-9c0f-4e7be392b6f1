/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.solicitacaoagendamento;

import br.com.ksisolucoes.bo.BO;
import br.com.ksisolucoes.bo.agendamento.exame.solicitacaoAgendamento.ConsultarSolicitacaoAgendamento;
import br.com.ksisolucoes.bo.agendamento.exame.solicitacaoAgendamento.ValidarSolicitacaoAgendada;
import br.com.ksisolucoes.bo.prontuario.basico.solicitacaoagendamento.UpdateDataProcessamentoSolicitacaoAgendamento;
import br.com.ksisolucoes.bo.prontuario.basico.solicitacaoagendamento.UpdateSolicitacaoAgendamentoPosicaoFila;
import br.com.ksisolucoes.bo.prontuario.basico.solicitacaoagendamento.UpdateUltimaAlteracaoAgendamento;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;

import javax.ejb.Stateless;

@Stateless
public class SolicitacaoAgendamentoBO extends BO implements _SolicitacaoAgendamentoFacadeLocal, _SolicitacaoAgendamentoFacadeRemote {

    @Override
    public SolicitacaoAgendamento consultar(Long codigoSolicitacao) throws DAOException, ValidacaoException {
        return ((ConsultarSolicitacaoAgendamento) this.executor.executeReturn(new ConsultarSolicitacaoAgendamento(codigoSolicitacao))).getSolicitacaoAgendamento();
    }

    @Override
    public void validarSolicitacaoAgendada(SolicitacaoAgendamento solicitacaoAgendamento) throws DAOException, ValidacaoException {
        if (solicitacaoAgendamento != null) {
            this.executor.execute(new ValidarSolicitacaoAgendada(solicitacaoAgendamento.getCodigo()));
        }
    }

    @Override
    public void updateSolicitacaoAgendamentoPosicaoFila(SolicitacaoAgendamento solicitacaoAgendamento, Long posicaoFilaEspera) throws DAOException, ValidacaoException {
        this.executor.execute(new UpdateSolicitacaoAgendamentoPosicaoFila(solicitacaoAgendamento, posicaoFilaEspera));
    }

    @Override
    public void updateSolicitacaoAgendamentoPosicaoFila(SolicitacaoAgendamento solicitacaoAgendamento, Long posicaoFilaEspera,boolean posicaoFilaEsperaManual) throws DAOException, ValidacaoException {
        this.executor.execute(new UpdateSolicitacaoAgendamentoPosicaoFila(solicitacaoAgendamento, posicaoFilaEspera,posicaoFilaEsperaManual));
    }

    @Override
    public void updateUltimaAlteracaoAgendamento() throws DAOException, ValidacaoException {
        this.executor.execute(new UpdateUltimaAlteracaoAgendamento());
    }

    @Override
    public void updateUltimaAlteracaoAgendamento(Long codigoTipoPRocedimento) throws DAOException, ValidacaoException {
        this.executor.execute(new UpdateUltimaAlteracaoAgendamento(codigoTipoPRocedimento));
    }

    @Override
    public void updateDataProcessamento(Long codigoSolicitacao) throws DAOException, ValidacaoException {
        this.executor.execute(new UpdateDataProcessamentoSolicitacaoAgendamento(codigoSolicitacao));
    }
}
