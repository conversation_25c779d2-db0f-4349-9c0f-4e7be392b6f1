package br.com.ksisolucoes.bo.server;

import br.com.ksisolucoes.system.sessao.TenantContext;
import org.hibernate.context.spi.CurrentTenantIdentifierResolver;

/**
 *
 * <AUTHOR>
 */
public class TenantResolver implements CurrentTenantIdentifierResolver{
    
    @Override
    public String resolveCurrentTenantIdentifier() {
        String ident = TenantContext.getContext();
        
        return ident + "-ds";
    }
    
    @Override
    public boolean validateExistingCurrentSessions() {
        return true;
    }


}
