package br.com.ksisolucoes.bo.entradas.estoque;

import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade._ProdutoProcedimentoFacadeRemote;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade._ProdutoProcedimentoFacadeLocal;
import java.io.Serializable;

import br.com.ksisolucoes.bo.BOGenericImpl;
import br.com.ksisolucoes.bo.entradas.estoque.produtoprocedimento.DeleteProdutoProcedimento;
import br.com.ksisolucoes.bo.entradas.estoque.produtoprocedimento.SaveProdutoProcedimento;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.*;
import br.com.ksisolucoes.bo.entradas.estoque.produtoprocedimento.CancelarListProdutoProcedimento;
import br.com.ksisolucoes.bo.entradas.estoque.produtoprocedimento.SaveListProdutoProcedimento;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProdutoProcedimento;

/**
 * <AUTHOR> Giordani
 *
 */
import java.util.ArrayList;
import java.util.List;
import javax.ejb.Stateless;

@Stateless 
public class ProdutoProcedimentoBO extends BOGenericImpl implements _ProdutoProcedimentoFacadeLocal, _ProdutoProcedimentoFacadeRemote {

	/**
	 * {@inheritDoc}
	 */
    @Override
    public void delete(Object vo) throws DAOException, ValidacaoException {
        List<ProdutoProcedimento> list = new ArrayList<ProdutoProcedimento>();
        list.add((ProdutoProcedimento) vo);
        this.executor.execute( new CancelarListProdutoProcedimento(list));
    }

	/**
	 * {@inheritDoc}
	 */
    
	/**
	 * {@inheritDoc}
	 */
    @Override    
    public Serializable save(Object vo) throws DAOException, ValidacaoException {
        this.executor.execute( new SaveProdutoProcedimento( (ProdutoProcedimento) vo) );
        return null;
    }

    public Serializable saveListProdutoProcedimento(List<ProdutoProcedimento> list) throws DAOException, ValidacaoException {
        this.executor.execute( new SaveListProdutoProcedimento(list));
        return null;
    }

    public Serializable cancelarListProdutoProcedimento(List<ProdutoProcedimento> list) throws DAOException, ValidacaoException {
        this.executor.execute( new CancelarListProdutoProcedimento(list));
        return null;
    }

	/**
	 * {@inheritDoc}
	 */
	@Override
    public final Class getReferenceClass() {
        return ProdutoProcedimento.class;
    }

	/**
	 * {@inheritDoc}
	 */
        
}
