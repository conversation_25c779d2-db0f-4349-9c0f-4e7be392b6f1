package br.com.celk.bo.service.async;

import br.com.celk.amazon.sqs.javamessaging.ProviderConfiguration;
import br.com.celk.amazon.sqs.javamessaging.SQSConnection;
import br.com.celk.amazon.sqs.javamessaging.SQSConnectionFactory;
import br.com.celk.amazon.sqs.javamessaging.SQSMessageConsumer;
import br.com.celk.bo.service.message.BnafarSqsMessageReceiver;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.util.AwsUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.ejb.DependsOn;
import javax.ejb.Singleton;
import javax.ejb.Startup;
import javax.jms.JMSException;
import javax.jms.Queue;
import javax.jms.Session;

/**
 * <AUTHOR>
 */
@Startup
@Singleton
@DependsOn(value = {"LoadTenantRegistry", "CacheHelper"})
public class BnafarAssincronoWorker {

    public SQSConnection connectionDefault = null;

    @PreDestroy
    public void finalizandoSingleton() {
        if (connectionDefault != null) {
            try {
                Loggable.log.info("BNAFAR - Fechando conexao SQS Default");
                connectionDefault.close();
                Loggable.log.info("BNAFAR - Conexao SQS Default fechada com sucesso!");
            } catch (JMSException ex) {
                Loggable.log.info(ex.getMessage(), ex);
            }
        }
    }

    @PostConstruct
    public void init() {
        if ("true".equals(RepositoryComponentDefault.SystemProperty.UTILIZA_BNAFAR.value())) {
            Loggable.log.info("BNAFAR - PROCESSO ASSINCRONO Iniciando conexao com SQS");
            try {
                SQSConnectionFactory connectionFactory = new SQSConnectionFactory(new ProviderConfiguration().withNumberOfMessagesToPrefetch(10), AwsUtils.getSQSClient());
                connectionDefault = createSQSConnectionAndWorkers(connectionFactory, getNameQueueCluster(), Coalesce.asInteger(Integer.valueOf(RepositoryComponentDefault.SystemProperty.PROCESSO_BNFAR_WORKERS.value()), 1));
                connectionDefault.start();
                Loggable.log.info("BNAFAR - PROCESSO ASSINCRONO Conexao com SQS concluida com exito.");
            } catch (JMSException e) {
                throw new RuntimeException(e.getMessage(), e.getCause());
            }
        }
    }

    private SQSConnection createSQSConnectionAndWorkers(SQSConnectionFactory connectionFactory, String SQSName, int numberOfWorkers) throws JMSException {
        SQSConnection connection = connectionFactory.createConnection();
        Queue queue = connection.createSession(false, Session.AUTO_ACKNOWLEDGE).createQueue(SQSName);

        for (int i = 0; i < numberOfWorkers; i++) {
            SQSMessageConsumer consumer = (SQSMessageConsumer) connection.createSession(false, Session.AUTO_ACKNOWLEDGE).createConsumer(queue);
            consumer.setMessageListener(new BnafarSqsMessageReceiver());
        }

        return connection;
    }


    private String getNameQueueCluster() {
        return "bnafar_cluster_".concat(RepositoryComponentDefault.SystemProperty.CLUSTER.value());
    }

}
