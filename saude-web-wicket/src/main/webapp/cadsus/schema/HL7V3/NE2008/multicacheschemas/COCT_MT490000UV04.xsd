<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="urn:hl7-org:v3" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:ex="urn:hl7-org/v3-example"
           targetNamespace="urn:hl7-org:v3"
           elementFormDefault="qualified"><!--
*****************************************************************************************************************
* XML schema for message type COCT_MT490000UV04.
* Source information:
*     Rendered by: Visio to MIF transform
*     Rendered on: 
* 
*
* Generated by XMLITS version 3.1.6
*   MIF to XSD Transform $Id: StaticMifToXsd.xsl,v 1.30 2007/12/06 05:50:08 wbeeler Exp $
*     Package Id Conversion: $Id: TransformPackageIds.xsl,v 1.6 2007/03/20 02:48:49 wbeeler Exp $
*
* Copyright (c) 2002, 2003, 2004, 2005, 2006, 2007 Health Level Seven. All rights reserved.
*  Redistribution and use in source and binary forms, with or without
*  modification, are permitted provided that the following conditions
*  are met:
*  1. Redistributions of source code must retain the above copyright
*     notice, this list of conditions and the following disclaimer.
*  2. Redistributions in binary form must reproduce the above copyright
*     notice, this list of conditions and the following disclaimer in the
*     documentation and/or other materials provided with the distribution.
*  3. All advertising materials mentioning features or use of this software
*     must display the following acknowledgement:
*       This product includes software developed by Health Level Seven.
*  THIS SOFTWARE IS PROVIDED BY HEALTH LEVEL SEVEN, INC. AND CONTRIBUTORS "AS IS" AND
*  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
*  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
*  ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
*  FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
*  DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
*  OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
*  HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
*  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
*  OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
*  SUCH DAMAGE.
*
********************************************************************************************************************
	  --><xs:annotation>
      <xs:documentation>Generated using schema builder version 3.1.6. Stylesheets:

StaticMifToXsd.xsl version 2.0</xs:documentation>
   </xs:annotation>
   <xs:include schemaLocation="../coreschemas/infrastructureRoot.xsd"/>
   <xs:include schemaLocation="COCT_MT240003UV02.xsd"/>
   <xs:complexType name="COCT_MT490000UV04.BillableClinicalProduct">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="1"/>
         <xs:element name="code" type="CS" minOccurs="1" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="1" maxOccurs="1"/>
         <xs:element name="product" type="COCT_MT490000UV04.Product" minOccurs="1" maxOccurs="1"/>
         <xs:element name="referrer" type="COCT_MT490000UV04.Referrer" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="consultant" type="COCT_MT490000UV04.Consultant" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="origin" type="COCT_MT490000UV04.Origin" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="destination" type="COCT_MT490000UV04.Destination" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="location" type="COCT_MT490000UV04.Location" minOccurs="1" maxOccurs="1"/>
         <xs:element name="pertinentInformation" type="COCT_MT490000UV04.PertinentInformation"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassSupply" use="required"/>
      <xs:attribute name="moodCode" type="x_ActMoodIntentEvent" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT490000UV04.Consultant">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="healthCareProvider" type="COCT_MT490000UV04.HealthCareProvider"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="CON"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT490000UV04.ContentPackagedProduct">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="quantity" type="RTO_PQ_PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="contained" type="COCT_MT490000UV04.ManufacturedMaterial" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="CONT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT490000UV04.Destination">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="serviceDeliveryLocation" type="COCT_MT240003UV02.ServiceDeliveryLocation"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="DST"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT490000UV04.Diagnosis">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CE" minOccurs="1" maxOccurs="1"/>
         <xs:element name="text" type="ST" minOccurs="0" maxOccurs="1"/>
         <xs:element name="value" type="CE" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassObservation" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT490000UV04.HealthCareProvider">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="healthCareProviderPerson" type="COCT_MT490000UV04.ProviderPerson"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="PROV"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT490000UV04.Location">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="serviceDeliveryLocation" type="COCT_MT240003UV02.ServiceDeliveryLocation"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetLocation" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT490000UV04.ManufacturedMaterial">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="desc" type="ST" minOccurs="0" maxOccurs="1"/>
         <xs:element name="asWarrantor" type="COCT_MT490000UV04.Warrantor" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="contentPackagedProduct" type="COCT_MT490000UV04.ContentPackagedProduct"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClass" use="required" fixed="MMAT "/>
      <xs:attribute name="determinerCode" type="cs" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT490000UV04.ManufacturedProduct">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="1"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="manufacturedMaterial" type="COCT_MT490000UV04.ManufacturedMaterial"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
         <xs:element name="manufacturerManufacturedProductOrganization"
                     type="COCT_MT490000UV04.ManufacturedProductOrganization"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassManufacturedProduct" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT490000UV04.ManufacturedProductOrganization">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="name" type="ON" minOccurs="1" maxOccurs="1"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassOrganization" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT490000UV04.Origin">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="serviceDeliveryLocation" type="COCT_MT240003UV02.ServiceDeliveryLocation"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="ORG"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT490000UV04.PertinentInformation">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="sequenceNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="diagnosis" type="COCT_MT490000UV04.Diagnosis" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipPertains" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT490000UV04.Product">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="manufacturedProduct" type="COCT_MT490000UV04.ManufacturedProduct"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="PRD"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT490000UV04.ProviderPerson">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="name" type="PN" minOccurs="1" maxOccurs="1"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="addr" type="AD" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClass" use="required" fixed="PSN"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT490000UV04.Referrer">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="healthCareProvider" type="COCT_MT490000UV04.HealthCareProvider"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="REF"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT490000UV04.Warrantor">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="1" maxOccurs="1"/>
         <xs:element name="warrantingWarrantorOrganization"
                     type="COCT_MT490000UV04.WarrantorOrganization"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="WRTE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT490000UV04.WarrantorOrganization">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="name" type="ON" minOccurs="1" maxOccurs="1"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassOrganization" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
</xs:schema>