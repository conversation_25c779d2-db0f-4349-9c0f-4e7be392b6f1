package br.com.celk.component.messaging.autocomplete;

import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.tokenautocomplete.behavior.OnAddBehavior;
import br.com.celk.component.tokenautocomplete.behavior.OnDeleteBehavior;
import br.com.celk.resources.Resources;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.ComponentTag;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.form.FormComponentPanel;
import org.apache.wicket.markup.html.link.ResourceLink;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.request.resource.AbstractResource;
import org.apache.wicket.util.string.StringValue;
import org.apache.wicket.util.time.Duration;
import org.odlabs.wiquery.core.javascript.JsQuery;
import org.odlabs.wiquery.core.options.Options;

import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.util.*;

public abstract class MessagingAutoComplete<T extends PesquisaObjectInterface> extends FormComponentPanel<List<T>> {

    private String theme = "messaging";
    private InputField<String> textField;
    private Options options;
    private IConsultaConfigurator<T> configurator;

    private Map<String, T> currentValues = null;
    private List<ConsultaListener> listeners = new ArrayList<ConsultaListener>();
    private List<RemoveListener> removeListeners = new ArrayList<RemoveListener>();

    private String onAddAction;
    private String onDeleteAction;

    public MessagingAutoComplete(String wicketId) {
        super(wicketId);
        init();
    }

    public MessagingAutoComplete(String wicketId, IModel model) {
        super(wicketId, model);
        init();
    }

    public MessagingAutoComplete(String id, boolean required) {
        super(id);
        setRequired(required);
        init();
    }

    private void init() {
        options = new Options(this);

        setTheme(theme);
        setMinChars(3);
//        setTokenLimit(1);
        setPreventDuplicates(true);
        setHintText(BundleManager.getString("digiteParaBuscar"));
        setNoResultsText(BundleManager.getString("nenhumResultadoEncontrado"));
        setSearchingText(BundleManager.getString("procurando"));
        setResultsLimit(10);
        setSearchDelay(600);
        setZIndex(20002);

        if (getConfigurator().getAutoCompleteSettings().getResultsFormatter() != null) {
            setResultsFormatter(getConfigurator().getAutoCompleteSettings().getResultsFormatter());
        }

        if (getConfigurator().getAutoCompleteSettings().getTokenFormatter() != null) {
            setResultsFormatter(getConfigurator().getAutoCompleteSettings().getTokenFormatter());
        }

        setOutputMarkupId(true);

        ResourceLink<?> serverSideDataSourceUrl = new ResourceLink<Object>("serverSideDataSourceUrl", new DataProviderJsonResource());
        serverSideDataSourceUrl.add(new AttributeModifier("id", new Model<String>("serverSideDataSourceUrl" + getMarkupId())));
        add(serverSideDataSourceUrl);

        textField = new InputField<String>("textField", new Model<String>());
        textField.setConvertEmptyInputStringToNull(false);
        textField.setOutputMarkupId(true);
        add(textField);

        add(new OnAddBehavior() {

            @Override
            protected void onEvent(AjaxRequestTarget target) {
                try {
                    String id = getRequest().getRequestParameters().getParameterValue("id").toString();
                    addVO(target, id, (T) loadVO(id));
                } catch (DAOException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                } catch (ValidacaoException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
            }
        });

        add(new OnDeleteBehavior() {

            @Override
            protected void onEvent(AjaxRequestTarget target) {
                try {
                    String id = getRequest().getRequestParameters().getParameterValue("id").toString();
                    removeVO(target, id, (T) loadVO(id));
                } catch (DAOException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                } catch (ValidacaoException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
            }
        });
    }

    public void limpar(AjaxRequestTarget target) {
        textField.limpar(target);
        currentValues = null;
        setModelObject(null);
        setConvertedInput(null);
        target.add(this);
    }

    public class DataProviderJsonResource extends AbstractResource {

        @Override
        protected ResourceResponse newResourceResponse(Attributes attributes) {
            ResourceResponse resourceResponse = new ResourceResponse();
            resourceResponse.setWriteCallback(new WriteCallback() {
                @Override
                public void writeData(Attributes attributes) {
                    StringValue userEnteredPartialText = attributes.getRequest().getQueryParameters().getParameterValue("q");
                    try {
                        List search = getConfigurator().getDataProvider().search(userEnteredPartialText.toString(), options.getInt("resultsLimit"));
                        String jsonResult = jsonForOptions(search);
                        jsonResult = new String(jsonResult.getBytes("UTF-8"), Charset.defaultCharset());
                        attributes.getResponse().write(jsonResult.getBytes("UTF-8"));
                    } catch (DAOException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                    } catch (ValidacaoException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                    } catch (UnsupportedEncodingException e) {
                        throw new RuntimeException(e);
                    }
                }
            });

            resourceResponse.setCacheDuration(Duration.NONE);
            return resourceResponse;
        }
    }

    @Override
    protected void onComponentTag(ComponentTag tag) {
        super.onComponentTag(tag);
        tag.setName("span");
    }

    protected String jsonForOptions(List options) {
        return getConfigurator().getAutoCompleteSettings().getJsonForOptions(options);
    }

    private void addVO(AjaxRequestTarget target, String id, T vo) {
        currentValues.put(id, vo);
        callListener(target, vo);
    }

    private void removeVO(AjaxRequestTarget target, String id, T vo) {
        currentValues.remove(id);
        callRemoveListener(target, vo);
    }

    private T loadVO(String id) throws DAOException, ValidacaoException {
        return (T) getConfigurator().loadVO(id, getPropertiesLoad());
    }

    @Override
    protected void onBeforeRender() {
        if (isValid()) {
            if (currentValues == null) {
                currentValues = new HashMap<String, T>();
                if (getModelObject() != null) {
                    for (T t : getModelObject()) {
                        currentValues.put(t.getIdentificador(), t);
                    }
                }
            }

            setPrePopulate(new ArrayList<T>(currentValues.values()));
        }

        super.onBeforeRender();
    }

    @Override
    protected void convertInput() {
        setConvertedInput(new ArrayList<T>(currentValues.values()));
    }

    @Override
    public boolean checkRequired() {
        if (!isRequired()) {
            return true;
        } else {
            return !currentValues.isEmpty();
        }
    }

    private IConsultaConfigurator getConfigurator() {
        if (this.configurator == null) {
            this.configurator = getConsultaConfiguratorInstance();
        }

        return this.configurator;
    }

    public abstract IConsultaConfigurator getConsultaConfiguratorInstance();

//    public void add(ConsultaListener<T> listener){
//        listeners.add(listener);
//    }
//    
//    public void remove(ConsultaListener<T> listener){
//        listeners.remove(listener);
//    }
//    
//    public void add(RemoveListener<T> listener){
//        removeListeners.add(listener);
//    }
//    
//    public void remove(RemoveListener<T> listener){
//        removeListeners.remove(listener);
//    }

    private void callListener(AjaxRequestTarget target, T vo) {
        for (ConsultaListener consultaListener : listeners) {
            consultaListener.valueObjectLoaded(target, vo);
        }
    }

    private void callRemoveListener(AjaxRequestTarget target, T vo) {
        for (RemoveListener removeListener : removeListeners) {
            removeListener.valueObjectUnLoaded(target, vo);
        }
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_MESSAGING_TOKEN_INPUT));

        String source = "document.getElementById('serverSideDataSourceUrl" + getMarkupId() + "').getAttribute('href')";
        setOnAdd(resolveOnAdd());
        setOnDelete(resolveOnDelete());
        setDisabled(!isEnabledInHierarchy());

        response.render(OnDomReadyHeaderItem.forScript(new JsQuery(textField).$().chain("tokenInput", source, options.getJavaScriptOptions()).render()));
    }

    public void setPreventDuplicates(boolean preventDuplicates) {
        options.put("preventDuplicates", preventDuplicates);
    }

    public void setPrePopulate(List<T> values) {
        options.put("prePopulate", jsonForOptions(values));
    }

    public void setTheme(String theme) {
        options.putLiteral("theme", theme);
    }

    public void setSearchDelay(int searchDelay) {
        options.put("searchDelay", searchDelay);
    }

    public void setMinChars(int minChars) {
        options.put("minChars", minChars);
    }

    public void setTokenLimit(int tokenLimit) {
        options.put("tokenLimit", tokenLimit);
    }

    public void setResultsLimit(int resultsLimit) {
        options.put("resultsLimit", resultsLimit);
    }

    public void setHintText(String hintText) {
        options.putLiteral("hintText", hintText);
    }

    public void setNoResultsText(String noResultsText) {
        options.putLiteral("noResultsText", noResultsText);
    }

    public void setSearchingText(String searchingText) {
        options.putLiteral("searchingText", searchingText);
    }

    public void setDisabled(boolean disabled) {
        options.put("disabled", disabled);
    }

    public void setZIndex(int zindex) {
        options.put("zindex", zindex);
    }

    public void setTokenFormatter(String tokenFormatter) {
        options.put("tokenFormatter", "function(item){ return '" + tokenFormatter + "' }");
    }

    public void setResultsFormatter(String resultsFormatter) {
        options.put("resultsFormatter", "function(item){ return '" + resultsFormatter + "' }");
    }

    private void setOnAdd(String onAdd) {
        options.put("onAdd", onAdd);
    }

    private void setOnDelete(String onDelete) {
        options.put("onDelete", onDelete);
    }

    public void setOnAddAction(String onAddAction) {
        this.onAddAction = onAddAction;
    }

    public void setOnDeleteAction(String onDeleteAction) {
        this.onDeleteAction = onDeleteAction;
    }

    private String resolveOnAdd() {
        String addUrl = "document.getElementById('" + getMarkupId() + "').getAttribute('onadd')";

        String finalOnAdd = "function(item){ Wicket.Ajax.post({\"u\":" + addUrl + "+'&id='+item.id}); " + Coalesce.asString(onAddAction) + " }";

        return finalOnAdd;
    }

    private String resolveOnDelete() {
        String deleteUrl = "document.getElementById('" + getMarkupId() + "').getAttribute('ondelete')";

        String finalOnDelete = "function(item){ Wicket.Ajax.post({\"u\":" + deleteUrl + "+'&id='+item.id}); " + Coalesce.asString(onDeleteAction) + " }";

        return finalOnDelete;
    }

    public InputField<String> getTextField() {
        return textField;
    }

    public String[] getPropertiesLoad() {
        return new HQLProperties(getConfigurator().getReferenceClass()).getProperties();
    }

    public void add(AjaxRequestTarget target, T vo) {
        try {
            String jsonResult = jsonForOptions(Arrays.asList(vo));
            jsonResult = new String(jsonResult.getBytes("UTF-8"), Charset.defaultCharset());
            jsonResult = jsonResult.replace("[", "");
            jsonResult = jsonResult.replace("]", "");
            target.appendJavaScript("$('#" + textField.getMarkupId() + "').tokenInput('add', " + jsonResult + ")");
        } catch (UnsupportedEncodingException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }
}
