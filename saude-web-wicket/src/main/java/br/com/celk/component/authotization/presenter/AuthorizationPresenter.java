package br.com.celk.component.authotization.presenter;

import br.com.celk.component.authotization.view.IAuthorizationView;
import br.com.celk.system.authorization.interfaces.action.IAuthorizationAction;
import br.com.celk.system.authorization.interfaces.action.IAuthorizationClose;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.methods.WicketMethods;
import br.com.celk.system.session.ApplicationSession;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoServidor;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.ajax.markup.html.modal.ModalWindow;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.protocol.http.servlet.ServletWebRequest;
import org.apache.wicket.request.cycle.RequestCycle;

/**
 *
 * <AUTHOR> felipe
 */
public class AuthorizationPresenter implements IAuthorizationPresenter {

    private ModalWindow modalWindow;
    private FormComponent componentRequestFocus;
    
    private List<IAuthorizationAction> actions = new LinkedList<IAuthorizationAction>();
    private IAuthorizationClose onClose;
    private IAuthorizationView view;

    public AuthorizationPresenter(ModalWindow modalWindow) {
        this.modalWindow = modalWindow;
        
        this.modalWindow.setCloseButtonCallback(new ModalWindow.CloseButtonCallback() {

            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                view.limpar(target);
                if (onClose!=null) {
                    try {
                        onClose.onClose(target);
                    } catch (ValidacaoException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                    } catch (DAOException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                    }
                }
                return true;
            }
        });
    }
    
    @Override
    public void autorizar(AjaxRequestTarget target, String identificador) throws ValidacaoException, DAOException {
        if (actions.isEmpty()) {
            throw new UnsupportedOperationException(BundleManager.getString("acaoNaoImplementada"));
        }

        Usuario usuario = LoadManager.getInstance(Usuario.class)
                .addParameter(new QueryCustom.QueryCustomParameter(Usuario.PROP_IDENTIFICACAO, Util.criptografarSenha(identificador)))
                .start().getVO();
        if (usuario == null) {
            view.limpar(target);
            throw new ValidacaoException(Bundle.getStringApplication("msg_identificacao_invalida"));
        }
        
        modalWindow.close(target);

        Empresa empresa = br.com.celk.system.session.ApplicationSession.get().getSession().getEmpresa();

        SessaoAplicacaoServidor authorizedSession = SessaoAplicacaoServidor.getNewInstance(usuario, empresa, Bundle.getLocale());
        authorizedSession.setIpClient(WicketMethods.getIpClient());

        ApplicationSession.get().setAuthorizedSession(authorizedSession);

        for (IAuthorizationAction authorizationAction : actions) {
            authorizationAction.action(target);
        }
        view.limpar(target);
    }

    @Override
    public void fechar(AjaxRequestTarget target) {
        modalWindow.close(target);
        view.limpar(target);
        if (onClose!=null) {
            try {
                onClose.onClose(target);
            } catch (ValidacaoException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            } catch (DAOException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }
    }
    
    @Override
    public void setAuthorizationAction(IAuthorizationAction... actions) {
        this.actions = Arrays.asList(actions);
    }
    
    @Override
    public void setCloseAction(IAuthorizationClose onClose) {
        this.onClose = onClose;
    }

    @Override
    public void setView(IAuthorizationView view) {
        this.view = view;
    }

    @Override
    public void setComponentRequestFocus(FormComponent component) {
        componentRequestFocus = component;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return componentRequestFocus;
    }
}
