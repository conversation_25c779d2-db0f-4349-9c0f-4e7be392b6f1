package br.com.celk.view.atendimento.recepcao.panel.marcacao.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.agendamento.dto.DadosAgendamentoDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ConsultaUsuarioCadsusDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.PacienteSemCadastroDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgDadosAgendamentoMarcacao extends Window {

    private PnlDadosAgendamentoMarcacao pnlDadosAgendamentoMarcacao;

    public DlgDadosAgendamentoMarcacao(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("dados_agendamento");
            }
        });

        setInitialWidth(700);
        setInitialHeight(100);
        setResizable(true);

        setContent(pnlDadosAgendamentoMarcacao = new PnlDadosAgendamentoMarcacao(getContentId()) {

            @Override
            public void onAvancar(AjaxRequestTarget target, PacienteSemCadastroDTO dto, DadosAgendamentoDTO dadosAgendamentoDTO, ConsultaUsuarioCadsusDTO consultaUsuarioCadsusDTO) throws ValidacaoException, DAOException {
                close(target);
                DlgDadosAgendamentoMarcacao.this.onAvancar(target, dto, dadosAgendamentoDTO, consultaUsuarioCadsusDTO);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }

    public abstract void onAvancar(AjaxRequestTarget target, PacienteSemCadastroDTO dto, DadosAgendamentoDTO dadosAgendamentoDTO, ConsultaUsuarioCadsusDTO consultaUsuarioCadsusDTO) throws ValidacaoException, DAOException;

    public void show(AjaxRequestTarget target, ConsultaUsuarioCadsusDTO dto) {
        pnlDadosAgendamentoMarcacao.setDTO(dto, target);
        show(target);
    }

}
