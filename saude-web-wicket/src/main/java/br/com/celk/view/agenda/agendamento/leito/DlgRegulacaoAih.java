package br.com.celk.view.agenda.agendamento.leito;

import br.com.celk.component.window.Window;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.FormComponent;

public abstract class DlgRegulacaoAih extends Window {

    private PnlRegulacaoAih pnlRegulacaoAih;
    private final String titulo;

    public DlgRegulacaoAih(String id, String titulo) {
        super(id);
        this.titulo = titulo;
        init();
    }

    private void init() {
        setTitle(titulo);

        setInitialWidth(550);
        setInitialHeight(250);

        setResizable(false);

        setContent(pnlRegulacaoAih = new PnlRegulacaoAih(getContentId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, <PERSON>h aih, String motivo) throws ValidacaoException, DAOException {
                onFechar(target);
                DlgRegulacaoAih.this.onConfirmar(target, aih, motivo);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                fechar(target);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                fechar(target);
                return true;
            }
        });
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return pnlRegulacaoAih.getTxaMotivo();
    }

    public abstract void onConfirmar(AjaxRequestTarget target, Aih aih, String motivo) throws ValidacaoException, DAOException;

    public void show(AjaxRequestTarget target, Aih aih) {
        pnlRegulacaoAih.setAih(target, aih);
        show(target);
    }

    private void fechar(AjaxRequestTarget target) {
        close(target);
    }
}
