package br.com.celk.view.vigilancia.registroagravo;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.celk.view.vigilancia.registroagravo.enums.SimNaoEnum;
import br.com.celk.view.vigilancia.registroagravo.panel.FichaInvestigacaoAgravoBasePage;
import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoAcidenteAnimalPeconhentoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoAcidenteAnimalPeconhento;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.resource.CssResourceReference;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.Date;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class FichaInvestigacaoAgravoAcidenteAnimalPeconhentoPage extends FichaInvestigacaoAgravoBasePage {
    private final String CSSFILE = "FichaInvestigacaoAgravoAcidenteAnimalPeconhento.css";

    private InvestigacaoAgravoAcidenteAnimalPeconhento investigacaoAgravoAcidenteAnimalPeconhento;

    private WebMarkupContainer containerInvestigacao;
    private DateChooser dataInvestigacao;
    private DisabledInputField ocupacaoCbo;

    private WebMarkupContainer containerAntecedentesEpidemiologicos;
    private DateChooser dataAcidente;
    private AutoCompleteConsultaCidade autoCompleteConsultaCidadeOcorrencia;
    private DisabledInputField codMunicipioOcorrencia;
    private AutoCompleteConsultaCidade autoCompleteConsultaCidadeAcidente;
    private DisabledInputField ufOcorrencia;
    private InputField localidadeOcorrencia;
    private DropDown dropDownZonaOcorrencia;
    private DropDown dropDownTempoDecorridoPicadaAtendimento;
    private DropDown dropDownLocalPicadaCorpo;

    private WebMarkupContainer containerDadosClinicos;
    private DropDown dropDownManifestacoesLocais;
    private WebMarkupContainer containerTiposManifestacoesLocais;
    private DropDown dropDownTiposManifestacoesLocaisDor;
    private DropDown dropDownTiposManifestacoesLocaisEdema;
    private DropDown dropDownTiposManifestacoesLocaisEquimose;
    private DropDown dropDownTiposManifestacoesLocaisNecrose;
    private DropDown dropDownTiposManifestacoesLocaisOutros;
    private InputField tiposManifestacoesLocaisOutros;
    private DropDown dropDownManifestacoesSistemicas;
    private WebMarkupContainer containerTiposManifestacoesSistemicas;
    private DropDown dropDownTiposManifestacoesSistemicasNeuroparaliticas;
    private DropDown dropDownTiposManifestacoesSistemicasHemorragicas;
    private DropDown dropDownTiposManifestacoesSistemicasVagais;
    private DropDown dropDownTiposManifestacoesSistemicasMioliticasHemoliticas;
    private DropDown dropDownTiposManifestacoesSistemicasRenais;
    private DropDown dropDownTiposManifestacoesSistemicasOutros;
    private InputField tiposManifestacoesSistemicasOutros;
    private DropDown dropDownTempoCoagulacao;

    private WebMarkupContainer containerDadosAcidente;
    private DropDown dropDownTipoAcidente;
    private InputField tipoAcidenteOutros;
    private DropDown dropDownTipoAcidenteSerpente;
    private DropDown dropDownTipoAcidenteAranha;
    private DropDown dropDownTipoAcidenteLagarta;

    private WebMarkupContainer containerTratamento;
    private DropDown dropDownClassificacaoCaso;
    private DropDown dropDownSoroterapia;
    private WebMarkupContainer containerNumeroAmpolasSoro;
    private InputField numeroAmpolasSoroAntibotropico;
    private InputField numeroAmpolasSoroAntibotropicoLaquetico;
    private InputField numeroAmpolasSoroAntibotropicoCrolatico;
    private InputField numeroAmpolasSoroAnticrotalico;
    private InputField numeroAmpolasSoroAntielapidico;
    private InputField numeroAmpolasSoroAntiescorpionico;
    private InputField numeroAmpolasSoroAntiaracnidico;
    private InputField numeroAmpolasSoroAntiloxoscelico;
    private InputField numeroAmpolasSoroAntilonomico;
    private DropDown dropDownComplicacoesLocais;
    private WebMarkupContainer containerEspecificacaoComplicacoesLocais;
    private DropDown dropDownComplicacoesLocaisInfeccaoSecundaria;
    private DropDown dropDownComplicacoesLocaisNecroseExtensa;
    private DropDown dropDownComplicacoesLocaisSindromeCompartimental;
    private DropDown dropDownComplicacoesLocaisDeficitFuncional;
    private DropDown dropDownComplicacoesLocaisAmputacao;
    private DropDown dropDownComplicacoesSistemicas;
    private WebMarkupContainer containerEspecificacaoComplicacoesSistemicas;
    private DropDown dropDownComplicacoesSistemicasInsuficienciaRenal;
    private DropDown dropDownComplicacoesSistemicasInsuficienciaRespiratoria;
    private DropDown dropDownComplicacoesSistemicasSepticemia;
    private DropDown dropDownComplicacoesSistemicasChoque;

    private WebMarkupContainer containerConclusao;
    private DropDown dropDownAcidenteRelacionadoTrabalho;
    private DropDown dropDownEvolucaoCaso;
    private DateChooser dataObito;

    private WebMarkupContainer containerObservacoes;
    private InputField observacoes;

    private WebMarkupContainer containerEncerramento;
    private DisabledInputField usuarioEncerramento;
    private DisabledInputField dataEncerramento;

    public FichaInvestigacaoAgravoAcidenteAnimalPeconhentoPage(Long idRegistroAgravo, boolean modoLeitura) {
        super(idRegistroAgravo, modoLeitura);
    }

    @Override
    public void carregarFicha() {
        investigacaoAgravoAcidenteAnimalPeconhento = InvestigacaoAgravoAcidenteAnimalPeconhento.buscaPorRegistroAgravo(getAgravo());
    }

    @Override
    public void inicializarForm() {
        if (investigacaoAgravoAcidenteAnimalPeconhento == null) {
            investigacaoAgravoAcidenteAnimalPeconhento = new InvestigacaoAgravoAcidenteAnimalPeconhento();
            investigacaoAgravoAcidenteAnimalPeconhento.setFlagInformacoesComplementares(RepositoryComponentDefault.SIM);
            investigacaoAgravoAcidenteAnimalPeconhento.setRegistroAgravo(getAgravo());
        }

        TabelaCbo tabelaCbo = FichaInvestigacaoAgravoHelper.getTabelaCboByCodUser(investigacaoAgravoAcidenteAnimalPeconhento.getRegistroAgravo().getUsuarioCadsus().getCodigo());
        if (tabelaCbo != null) {
            investigacaoAgravoAcidenteAnimalPeconhento.getRegistroAgravo().getUsuarioCadsus().setTabelaCbo(tabelaCbo);
            investigacaoAgravoAcidenteAnimalPeconhento.setOcupacaoCbo(tabelaCbo);
        }

        setForm(new Form("form", new CompoundPropertyModel(investigacaoAgravoAcidenteAnimalPeconhento)));
    }

    @Override
    public Object getFichaDTO() {
        FichaInvestigacaoAgravoAcidenteAnimalPeconhentoDTO fichaDTO = new FichaInvestigacaoAgravoAcidenteAnimalPeconhentoDTO();
        fichaDTO.setRegistroAgravo(getAgravo());
        fichaDTO.setInvestigacaoAgravoAcidenteAnimalPeconhento(investigacaoAgravoAcidenteAnimalPeconhento);
        return fichaDTO;
    }

    @Override
    public String carregarInformacoesComplementares() {
        return investigacaoAgravoAcidenteAnimalPeconhento.getFlagInformacoesComplementares() == null
                ? "S" : investigacaoAgravoAcidenteAnimalPeconhento.getFlagInformacoesComplementares();
    }

    @Override
    public void inicializarFicha() {
        InvestigacaoAgravoAcidenteAnimalPeconhento proxy = on(InvestigacaoAgravoAcidenteAnimalPeconhento.class);

        criarInvestigacao(proxy);
        criarAntecedentesEpidemiologicos(proxy);
        criarDadosClinicos(proxy);
        criarDadosAcidente(proxy);
        criarTratamento(proxy);
        criarConclusao(proxy);
        criarObservacoes(proxy);
        criarUsuarioDataEncerramento(proxy);
    }

    @Override
    public void inicializarRegrasComponentes(AjaxRequestTarget target) {
        carregarAntecedentesEpidemiologicos();
        carregarDadosClinicos();
        carregarDadosAcidente();
        carregarTratamento();
        carregarConclusao();
        carregarObservacoes();
    }

    @Override
    public void validarFicha() throws ValidacaoException {

    }

    @Override
    public void salvarFicha(Object fichaDTO) throws ValidacaoException, DAOException {
        FichaInvestigacaoAgravoAcidenteAnimalPeconhentoDTO dto = (FichaInvestigacaoAgravoAcidenteAnimalPeconhentoDTO) fichaDTO;

        validarFicha();

        BOFactoryWicket.getBO(VigilanciaFacade.class).salvarFichaInvestigacaoAgravoAcidenteAnimalPeconhento(dto);

        Page page = new ConsultaRegistroAgravoPage();
        setResponsePage(page);
    }

    @Override
    public Object getEncerrarFichaDTO() {
        FichaInvestigacaoAgravoAcidenteAnimalPeconhentoDTO fichaDTO = (FichaInvestigacaoAgravoAcidenteAnimalPeconhentoDTO) getFichaDTO();

        if (investigacaoAgravoAcidenteAnimalPeconhento.getUsuarioEncerramento() == null) {
            Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
            investigacaoAgravoAcidenteAnimalPeconhento.setUsuarioEncerramento(usuarioLogado);

            investigacaoAgravoAcidenteAnimalPeconhento.setDataEncerramento(DataUtil.getDataAtual());
        }

        fichaDTO.setEncerrarFicha(true);
        return fichaDTO;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(
            CssHeaderItem.forReference(new CssResourceReference(FichaInvestigacaoAgravoAcidenteAnimalPeconhentoPage.class, CSSFILE))
        );
    }

    private void criarInvestigacao(InvestigacaoAgravoAcidenteAnimalPeconhento proxy) {
        containerInvestigacao = new WebMarkupContainer("containerInvestigacao");
        containerInvestigacao.setOutputMarkupId(true);

        dataInvestigacao = new DateChooser(path(proxy.getDataInvestigacao()));
        dataInvestigacao.setRequired(true).setEnabled(!isModoLeitura());

        if (getAgravo().getAtendimento() != null) {
            dataInvestigacao.getData().setMinDate(new DateOption(getAgravo().getAtendimento().getDataAtendimento()));
        }

        ocupacaoCbo = new DisabledInputField(path(proxy.getOcupacaoCbo().getDescricao()));

        containerInvestigacao.add(dataInvestigacao, ocupacaoCbo);
        getContainerInformacoesComplementares().add(containerInvestigacao);
    }

    private void criarAntecedentesEpidemiologicos(InvestigacaoAgravoAcidenteAnimalPeconhento proxy) {
        containerAntecedentesEpidemiologicos = new WebMarkupContainer("containerAntecedentesEpidemiologicos");
        containerAntecedentesEpidemiologicos.setOutputMarkupId(true);

        dataAcidente = new DateChooser(path(proxy.getDataAcidente()));
        dataAcidente.setRequired(true);

        if (getAgravo().getAtendimento() != null) {
            Long timeDataMinimaAcidente = getAgravo().getAtendimento().getDataAtendimento().getTime() - 63072000000L;
            Date dataMinimaAcidente = new Date(timeDataMinimaAcidente);

            dataAcidente.getData().setMinDate(new DateOption(dataMinimaAcidente));
        }

        autoCompleteConsultaCidadeOcorrencia = new AutoCompleteConsultaCidade(path(proxy.getCidadeLocalOcorrencia()));
        autoCompleteConsultaCidadeOcorrencia.setRequired(true);

        autoCompleteConsultaCidadeAcidente = new AutoCompleteConsultaCidade(path(proxy.getCidadeLocalAcidente()));
        autoCompleteConsultaCidadeAcidente.setRequired(true);

        codMunicipioOcorrencia = new DisabledInputField(path(proxy.getCidadeLocalOcorrencia().getCodigo()));
        ufOcorrencia = new DisabledInputField(path(proxy.getCidadeLocalOcorrencia().getEstado().getSigla()));

        localidadeOcorrencia = new InputField(path(proxy.getLocalidadeOcorrencia()));
        dropDownZonaOcorrencia = DropDownUtil.getIEnumDropDown(path(proxy.getZonaOcorrencia()), InvestigacaoAgravoAcidenteAnimalPeconhento.ZonaOcorrencia.values());
        dropDownTempoDecorridoPicadaAtendimento = DropDownUtil.getIEnumDropDown(path(proxy.getTempoDecorridoPicadaAtendimento()), InvestigacaoAgravoAcidenteAnimalPeconhento.TempoDecorridoPicadaAtendimento.values());

        dropDownLocalPicadaCorpo = DropDownUtil.getIEnumDropDown(path(proxy.getLocalPicadaCorpo()), InvestigacaoAgravoAcidenteAnimalPeconhento.LocalPicadaCorpo.values());
        dropDownLocalPicadaCorpo.setRequired(true);

        containerAntecedentesEpidemiologicos.add(dataAcidente, autoCompleteConsultaCidadeOcorrencia, codMunicipioOcorrencia,
                ufOcorrencia, localidadeOcorrencia, dropDownZonaOcorrencia, dropDownTempoDecorridoPicadaAtendimento, dropDownLocalPicadaCorpo, autoCompleteConsultaCidadeAcidente);

        getContainerInformacoesComplementares().add(containerAntecedentesEpidemiologicos);

    }

    private void carregarAntecedentesEpidemiologicos() {
        dataAcidente.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (dataAcidente.getComponentValue() != null) {
                    dataObito.getData().setMinDate(new DateOption(dataAcidente.getComponentValue()));
                }
                target.add(dataObito);
            }
        });

        autoCompleteConsultaCidadeOcorrencia.add(new ConsultaListener<Cidade>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Cidade cidade) {
                codMunicipioOcorrencia.setComponentValue(cidade.getCodigo());

                Cidade cidadeTemp = cidade;
                if (cidadeTemp.getEstado() == null) {
                    cidadeTemp = FichaInvestigacaoAgravoHelper.getCidadeByCodigo(cidade.getCodigo());
                }

                ufOcorrencia.setComponentValue(cidadeTemp.getEstado().getSigla());
                target.add(codMunicipioOcorrencia, ufOcorrencia);
            }
        });

        autoCompleteConsultaCidadeOcorrencia.add(new RemoveListener<Cidade>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Cidade object) {
                codMunicipioOcorrencia.limpar(target);
                ufOcorrencia.limpar(target);
            }
        });

        FichaInvestigacaoAgravoHelper.enableDisableDates(dataAcidente, !isModoLeitura(), true, null);
        FichaInvestigacaoAgravoHelper.enableDisableInput(localidadeOcorrencia, !isModoLeitura(), false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownZonaOcorrencia, !isModoLeitura(), false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTempoDecorridoPicadaAtendimento, !isModoLeitura(), false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownLocalPicadaCorpo, !isModoLeitura(), true, null);
    }

    private void criarDadosClinicos(InvestigacaoAgravoAcidenteAnimalPeconhento proxy) {
        containerDadosClinicos = new WebMarkupContainer("containerDadosClinicos");
        containerDadosClinicos.setOutputMarkupId(true);

        dropDownManifestacoesLocais = DropDownUtil.getIEnumDropDown(path(proxy.getManifestacaoLocal()), SimNaoEnum.getSimNaoIgnorado());

        containerTiposManifestacoesLocais = new WebMarkupContainer("containerTiposManifestacoesLocais");
        containerTiposManifestacoesLocais.setOutputMarkupId(true);

        dropDownTiposManifestacoesLocaisDor = DropDownUtil.getIEnumDropDown(path(proxy.getTipoManifestacaoLocalDor()), SimNaoEnum.getSimNaoIgnoradoDefault());
        dropDownTiposManifestacoesLocaisEdema = DropDownUtil.getIEnumDropDown(path(proxy.getTipoManifestacaoLocalEdema()), SimNaoEnum.getSimNaoIgnoradoDefault());
        dropDownTiposManifestacoesLocaisEquimose = DropDownUtil.getIEnumDropDown(path(proxy.getTipoManifestacaoLocalEquimose()), SimNaoEnum.getSimNaoIgnoradoDefault());
        dropDownTiposManifestacoesLocaisNecrose = DropDownUtil.getIEnumDropDown(path(proxy.getTipoManifestacaoLocalNecrose()), SimNaoEnum.getSimNaoIgnoradoDefault());
        dropDownTiposManifestacoesLocaisOutros = DropDownUtil.getIEnumDropDown(path(proxy.getTipoManifestacaoLocalOutros()), SimNaoEnum.getSimNaoIgnoradoDefault());

        tiposManifestacoesLocaisOutros = new InputField(path(proxy.getTipoManifestacaoLocalOutrosStr()));

        containerTiposManifestacoesLocais.add(dropDownTiposManifestacoesLocaisDor, dropDownTiposManifestacoesLocaisEdema, dropDownTiposManifestacoesLocaisEquimose,
                dropDownTiposManifestacoesLocaisNecrose, dropDownTiposManifestacoesLocaisOutros, tiposManifestacoesLocaisOutros);

        dropDownManifestacoesSistemicas = DropDownUtil.getIEnumDropDown(path(proxy.getManifestacaoSistemica()), SimNaoEnum.getSimNaoIgnorado());

        containerTiposManifestacoesSistemicas = new WebMarkupContainer("containerTiposManifestacoesSistemicas");
        containerTiposManifestacoesSistemicas.setOutputMarkupId(true);

        dropDownTiposManifestacoesSistemicasNeuroparaliticas = DropDownUtil.getIEnumDropDown(path(proxy.getTipoManifestacaoSistemicaNeuroparalitica()), SimNaoEnum.getSimNaoIgnoradoDefault());
        dropDownTiposManifestacoesSistemicasHemorragicas = DropDownUtil.getIEnumDropDown(path(proxy.getTipoManifestacaoSistemicaHemorragica()), SimNaoEnum.getSimNaoIgnoradoDefault());
        dropDownTiposManifestacoesSistemicasVagais= DropDownUtil.getIEnumDropDown(path(proxy.getTipoManifestacaoSistemicaVagais()), SimNaoEnum.getSimNaoIgnoradoDefault());
        dropDownTiposManifestacoesSistemicasMioliticasHemoliticas = DropDownUtil.getIEnumDropDown(path(proxy.getTipoManifestacaoSistemicaHemolitica()), SimNaoEnum.getSimNaoIgnoradoDefault());
        dropDownTiposManifestacoesSistemicasRenais = DropDownUtil.getIEnumDropDown(path(proxy.getTipoManifestacaoSistemicaRenais()), SimNaoEnum.getSimNaoIgnoradoDefault());
        dropDownTiposManifestacoesSistemicasOutros = DropDownUtil.getIEnumDropDown(path(proxy.getTipoManifestacaoSistemicaOutros()), SimNaoEnum.getSimNaoIgnoradoDefault());

        tiposManifestacoesSistemicasOutros = new InputField(path(proxy.getTipoManifestacaoSistemicaOutrosStr()));

        containerTiposManifestacoesSistemicas.add(dropDownTiposManifestacoesSistemicasNeuroparaliticas, dropDownTiposManifestacoesSistemicasHemorragicas,
                dropDownTiposManifestacoesSistemicasVagais, dropDownTiposManifestacoesSistemicasMioliticasHemoliticas,
                dropDownTiposManifestacoesSistemicasRenais, dropDownTiposManifestacoesSistemicasOutros, tiposManifestacoesSistemicasOutros);

        dropDownTempoCoagulacao = DropDownUtil.getIEnumDropDown(path(proxy.getTempoCoagulacao()), InvestigacaoAgravoAcidenteAnimalPeconhento.TempoCoagulacao.values());

        containerDadosClinicos.add(dropDownManifestacoesLocais, containerTiposManifestacoesLocais,
                dropDownManifestacoesSistemicas, containerTiposManifestacoesSistemicas,
                dropDownTempoCoagulacao);

        getContainerInformacoesComplementares().add(containerDadosClinicos);
    }

    private void carregarDadosClinicos() {
        boolean erManifestacoesLocais = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(dropDownManifestacoesLocais, SimNaoEnum.SIM.value());
        boolean erManifestacoesSistemicas = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(dropDownManifestacoesSistemicas, SimNaoEnum.SIM.value());

        containerTiposManifestacoesLocais.setEnabled(erManifestacoesLocais);
        containerTiposManifestacoesSistemicas.setEnabled(erManifestacoesSistemicas);

        dropDownManifestacoesLocais.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                boolean erManifestacoesLocais = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(dropDownManifestacoesLocais, SimNaoEnum.SIM.value());

                containerTiposManifestacoesLocais.setEnabled(erManifestacoesLocais);

                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTiposManifestacoesLocaisDor, erManifestacoesLocais, erManifestacoesLocais, target);
                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTiposManifestacoesLocaisEdema, erManifestacoesLocais, erManifestacoesLocais, target);
                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTiposManifestacoesLocaisEquimose, erManifestacoesLocais, erManifestacoesLocais, target);
                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTiposManifestacoesLocaisNecrose, erManifestacoesLocais, erManifestacoesLocais, target);
                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTiposManifestacoesLocaisOutros, erManifestacoesLocais, erManifestacoesLocais, target);

                target.add(containerTiposManifestacoesLocais, dropDownTiposManifestacoesLocaisDor, dropDownTiposManifestacoesLocaisEdema,
                        dropDownTiposManifestacoesLocaisEquimose, dropDownTiposManifestacoesLocaisNecrose, dropDownTiposManifestacoesLocaisOutros);
            }
        });

        dropDownManifestacoesSistemicas.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                boolean erManifestacoesSistemicas = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(dropDownManifestacoesSistemicas, SimNaoEnum.SIM.value());

                containerTiposManifestacoesSistemicas.setEnabled(erManifestacoesSistemicas);

                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTiposManifestacoesSistemicasNeuroparaliticas, erManifestacoesSistemicas, erManifestacoesSistemicas, target);
                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTiposManifestacoesSistemicasHemorragicas, erManifestacoesSistemicas, erManifestacoesSistemicas, target);
                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTiposManifestacoesSistemicasVagais, erManifestacoesSistemicas, erManifestacoesSistemicas, target);
                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTiposManifestacoesSistemicasMioliticasHemoliticas, erManifestacoesSistemicas, erManifestacoesSistemicas, target);
                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTiposManifestacoesSistemicasRenais, erManifestacoesSistemicas, erManifestacoesSistemicas, target);
                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTiposManifestacoesSistemicasOutros, erManifestacoesSistemicas, erManifestacoesSistemicas, target);

                target.add(containerTiposManifestacoesSistemicas, dropDownTiposManifestacoesSistemicasNeuroparaliticas, dropDownTiposManifestacoesSistemicasHemorragicas,
                        dropDownTiposManifestacoesSistemicasVagais, dropDownTiposManifestacoesSistemicasMioliticasHemoliticas, dropDownTiposManifestacoesSistemicasRenais,
                        dropDownTiposManifestacoesSistemicasOutros);
            }
        });


        boolean erTiposManifestacoesLocaisOutros = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(dropDownTiposManifestacoesLocaisOutros, SimNaoEnum.SIM.value());
        boolean erTiposManifestacoesSistemicasOutros = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(dropDownTiposManifestacoesSistemicasOutros, SimNaoEnum.SIM.value());

        dropDownTiposManifestacoesLocaisOutros.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                boolean erTiposManifestacoesLocaisOutros = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(dropDownTiposManifestacoesLocaisOutros, SimNaoEnum.SIM.value());

                FichaInvestigacaoAgravoHelper.enableDisableInput(tiposManifestacoesLocaisOutros, erTiposManifestacoesLocaisOutros, erTiposManifestacoesLocaisOutros, target);

                target.add(tiposManifestacoesLocaisOutros);
            }
        });

        dropDownTiposManifestacoesSistemicasOutros.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                boolean erTiposManifestacoesSistemicasOutros = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(dropDownTiposManifestacoesSistemicasOutros, SimNaoEnum.SIM.value());

                FichaInvestigacaoAgravoHelper.enableDisableInput(tiposManifestacoesSistemicasOutros, erTiposManifestacoesSistemicasOutros, erTiposManifestacoesSistemicasOutros, target);

                target.add(tiposManifestacoesSistemicasOutros);
            }
        });

        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownManifestacoesLocais, !isModoLeitura(), true, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownManifestacoesSistemicas, !isModoLeitura(), true, null);

        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTiposManifestacoesLocaisDor, erManifestacoesLocais, erManifestacoesLocais, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTiposManifestacoesLocaisEdema, erManifestacoesLocais, erManifestacoesLocais, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTiposManifestacoesLocaisEquimose, erManifestacoesLocais, erManifestacoesLocais, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTiposManifestacoesLocaisNecrose, erManifestacoesLocais, erManifestacoesLocais, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTiposManifestacoesLocaisOutros, erManifestacoesLocais, erManifestacoesLocais, null);

        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTiposManifestacoesSistemicasNeuroparaliticas, erManifestacoesSistemicas, erManifestacoesSistemicas, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTiposManifestacoesSistemicasHemorragicas, erManifestacoesSistemicas, erManifestacoesSistemicas, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTiposManifestacoesSistemicasVagais, erManifestacoesSistemicas, erManifestacoesSistemicas, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTiposManifestacoesSistemicasMioliticasHemoliticas, erManifestacoesSistemicas, erManifestacoesSistemicas, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTiposManifestacoesSistemicasRenais, erManifestacoesSistemicas, erManifestacoesSistemicas, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTiposManifestacoesSistemicasOutros, erManifestacoesSistemicas, erManifestacoesSistemicas, null);

        FichaInvestigacaoAgravoHelper.enableDisableInput(tiposManifestacoesLocaisOutros, erTiposManifestacoesLocaisOutros, erTiposManifestacoesLocaisOutros, null);
        FichaInvestigacaoAgravoHelper.enableDisableInput(tiposManifestacoesSistemicasOutros, erTiposManifestacoesSistemicasOutros, erTiposManifestacoesSistemicasOutros, null);
    }

    private void criarDadosAcidente(InvestigacaoAgravoAcidenteAnimalPeconhento proxy) {
        containerDadosAcidente = new WebMarkupContainer("containerDadosAcidente");
        containerDadosAcidente.setOutputMarkupId(true);

        dropDownTipoAcidente = DropDownUtil.getIEnumDropDown(path(proxy.getTipoAcidente()), InvestigacaoAgravoAcidenteAnimalPeconhento.TipoAcidente.values());
        dropDownTipoAcidente.setRequired(true);

        tipoAcidenteOutros = new InputField(path(proxy.getTipoAcidenteOutrosStr()));

        dropDownTipoAcidenteSerpente = DropDownUtil.getIEnumDropDown(path(proxy.getTipoAcidenteSerpente()), InvestigacaoAgravoAcidenteAnimalPeconhento.TipoAcidenteSerpente.values());
        dropDownTipoAcidenteAranha = DropDownUtil.getIEnumDropDown(path(proxy.getTipoAcidenteAranha()), InvestigacaoAgravoAcidenteAnimalPeconhento.TipoAcidenteAranha.values());
        dropDownTipoAcidenteLagarta = DropDownUtil.getIEnumDropDown(path(proxy.getTipoAcidenteLagarta()), InvestigacaoAgravoAcidenteAnimalPeconhento.TipoAcidenteLagarta.values());

        containerDadosAcidente.add(dropDownTipoAcidente, tipoAcidenteOutros, dropDownTipoAcidenteSerpente, dropDownTipoAcidenteAranha, dropDownTipoAcidenteLagarta);
        getContainerInformacoesComplementares().add(containerDadosAcidente);
    }

    private void carregarDadosAcidente() {
        boolean erTipoAcidenteSerpente = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(dropDownTipoAcidente, InvestigacaoAgravoAcidenteAnimalPeconhento.TipoAcidente.SERPENTE.value());
        boolean erTipoAcidenteAranha = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(dropDownTipoAcidente, InvestigacaoAgravoAcidenteAnimalPeconhento.TipoAcidente.ARANHA.value());
        boolean erTipoAcidenteLagarta = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(dropDownTipoAcidente, InvestigacaoAgravoAcidenteAnimalPeconhento.TipoAcidente.LAGARTA.value());
        boolean erTipoAcidenteOutros = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(dropDownTipoAcidente, InvestigacaoAgravoAcidenteAnimalPeconhento.TipoAcidente.OUTROS.value());

        dropDownTipoAcidente.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                boolean erTipoAcidenteSerpente = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(dropDownTipoAcidente, InvestigacaoAgravoAcidenteAnimalPeconhento.TipoAcidente.SERPENTE.value());
                boolean erTipoAcidenteAranha = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(dropDownTipoAcidente, InvestigacaoAgravoAcidenteAnimalPeconhento.TipoAcidente.ARANHA.value());
                boolean erTipoAcidenteLagarta = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(dropDownTipoAcidente, InvestigacaoAgravoAcidenteAnimalPeconhento.TipoAcidente.LAGARTA.value());
                boolean erTipoAcidenteOutros = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(dropDownTipoAcidente, InvestigacaoAgravoAcidenteAnimalPeconhento.TipoAcidente.OUTROS.value());

                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTipoAcidenteSerpente, erTipoAcidenteSerpente, erTipoAcidenteSerpente, target);
                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTipoAcidenteAranha, erTipoAcidenteAranha, erTipoAcidenteAranha, target);
                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTipoAcidenteLagarta, erTipoAcidenteLagarta, erTipoAcidenteLagarta, target);
                FichaInvestigacaoAgravoHelper.enableDisableInput(tipoAcidenteOutros, erTipoAcidenteOutros, erTipoAcidenteOutros, target);

                target.add(dropDownTipoAcidenteSerpente, dropDownTipoAcidenteAranha, dropDownTipoAcidenteLagarta, tipoAcidenteOutros);
            }
        });

        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTipoAcidente, !isModoLeitura(), !isModoLeitura(), null);

        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTipoAcidenteSerpente, erTipoAcidenteSerpente, erTipoAcidenteSerpente, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTipoAcidenteAranha, erTipoAcidenteAranha, erTipoAcidenteAranha, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownTipoAcidenteLagarta, erTipoAcidenteLagarta, erTipoAcidenteLagarta, null);
        FichaInvestigacaoAgravoHelper.enableDisableInput(tipoAcidenteOutros, erTipoAcidenteOutros, erTipoAcidenteOutros, null);
    }

    private void criarTratamento(InvestigacaoAgravoAcidenteAnimalPeconhento proxy) {
        containerTratamento = new WebMarkupContainer("containerTratamento");
        containerTratamento.setOutputMarkupId(true);

        dropDownClassificacaoCaso = DropDownUtil.getIEnumDropDown(path(proxy.getClassificacaoCaso()), InvestigacaoAgravoAcidenteAnimalPeconhento.ClassificacaoCaso.values());

        dropDownSoroterapia = DropDownUtil.getIEnumDropDown(path(proxy.getSoroterapia()), SimNaoEnum.getSimNaoIgnorado());
        dropDownSoroterapia.setRequired(true);

        containerNumeroAmpolasSoro = new WebMarkupContainer("containerNumeroAmpolasSoro");
        containerNumeroAmpolasSoro.setOutputMarkupId(true);

        numeroAmpolasSoroAntibotropico = new InputField(path(proxy.getNrAmpolasAntibotropico()));
        numeroAmpolasSoroAntibotropicoLaquetico = new InputField(path(proxy.getNrAmpolasAntibotropicoLaquetico()));
        numeroAmpolasSoroAntibotropicoCrolatico = new InputField(path(proxy.getNrAmpolasAntibotropicoCrolatico()));
        numeroAmpolasSoroAnticrotalico = new InputField(path(proxy.getNrAmpolasAnticrolatico()));
        numeroAmpolasSoroAntielapidico = new InputField(path(proxy.getNrAmpolasAntielapidico()));
        numeroAmpolasSoroAntiescorpionico = new InputField(path(proxy.getNrAmpolasAntiescorpianico()));
        numeroAmpolasSoroAntiaracnidico = new InputField(path(proxy.getNrAmpolasAntiaracnidico()));
        numeroAmpolasSoroAntiloxoscelico = new InputField(path(proxy.getNrAmpolasAntilixiscelico()));
        numeroAmpolasSoroAntilonomico = new InputField(path(proxy.getNrAmpolasAntilonomico()));

        containerNumeroAmpolasSoro.add(numeroAmpolasSoroAntibotropico, numeroAmpolasSoroAntibotropicoLaquetico, numeroAmpolasSoroAntibotropicoCrolatico,
                numeroAmpolasSoroAnticrotalico, numeroAmpolasSoroAntielapidico, numeroAmpolasSoroAntiescorpionico, numeroAmpolasSoroAntiaracnidico,
                numeroAmpolasSoroAntiloxoscelico, numeroAmpolasSoroAntilonomico);

        dropDownComplicacoesLocais = DropDownUtil.getIEnumDropDown(path(proxy.getComplicoesLocais()), SimNaoEnum.getSimNaoIgnorado());

        containerEspecificacaoComplicacoesLocais = new WebMarkupContainer("containerEspecificacaoComplicacoesLocais");
        containerEspecificacaoComplicacoesLocais.setOutputMarkupId(true);

        dropDownComplicacoesLocaisInfeccaoSecundaria = DropDownUtil.getIEnumDropDown(path(proxy.getTipoComplicacaoLocalInfeccaoSecundaria()), SimNaoEnum.getSimNaoIgnoradoDefault());
        dropDownComplicacoesLocaisNecroseExtensa = DropDownUtil.getIEnumDropDown(path(proxy.getTipoComplicacaoLocalNecroseExtensa()), SimNaoEnum.getSimNaoIgnoradoDefault());
        dropDownComplicacoesLocaisSindromeCompartimental = DropDownUtil.getIEnumDropDown(path(proxy.getTipoComplicacaoLocalSindromeCompartimental()), SimNaoEnum.getSimNaoIgnoradoDefault());
        dropDownComplicacoesLocaisDeficitFuncional = DropDownUtil.getIEnumDropDown(path(proxy.getTipoComplicacaoLocalDeficitFuncional()), SimNaoEnum.getSimNaoIgnoradoDefault());
        dropDownComplicacoesLocaisAmputacao = DropDownUtil.getIEnumDropDown(path(proxy.getTipoComplicacaoLocalAmputacao()), SimNaoEnum.getSimNaoIgnoradoDefault());

        containerEspecificacaoComplicacoesLocais.add(dropDownComplicacoesLocaisInfeccaoSecundaria, dropDownComplicacoesLocaisNecroseExtensa,
                dropDownComplicacoesLocaisSindromeCompartimental, dropDownComplicacoesLocaisDeficitFuncional, dropDownComplicacoesLocaisAmputacao);

        dropDownComplicacoesSistemicas = DropDownUtil.getIEnumDropDown(path(proxy.getComplicacoesSistemicas()), SimNaoEnum.getSimNaoIgnorado());

        containerEspecificacaoComplicacoesSistemicas = new WebMarkupContainer("containerEspecificacaoComplicacoesSistemicas");
        containerEspecificacaoComplicacoesSistemicas.setOutputMarkupId(true);

        dropDownComplicacoesSistemicasInsuficienciaRenal = DropDownUtil.getIEnumDropDown(path(proxy.getTipoComplicacaoSistemicaInsuficienciaRenal()), SimNaoEnum.getSimNaoIgnoradoDefault());
        dropDownComplicacoesSistemicasInsuficienciaRespiratoria = DropDownUtil.getIEnumDropDown(path(proxy.getTipoComplicacaoSistemicaInsuficienciaRespiratoria()), SimNaoEnum.getSimNaoIgnoradoDefault());
        dropDownComplicacoesSistemicasSepticemia = DropDownUtil.getIEnumDropDown(path(proxy.getTipoComplicacaoSistemicaSepticemia()), SimNaoEnum.getSimNaoIgnoradoDefault());
        dropDownComplicacoesSistemicasChoque = DropDownUtil.getIEnumDropDown(path(proxy.getTipoComplicacaoSistemicaChoque()), SimNaoEnum.getSimNaoIgnoradoDefault());

        containerEspecificacaoComplicacoesSistemicas.add(dropDownComplicacoesSistemicasInsuficienciaRenal, dropDownComplicacoesSistemicasInsuficienciaRespiratoria,
                dropDownComplicacoesSistemicasSepticemia, dropDownComplicacoesSistemicasChoque);

        containerTratamento.add(dropDownClassificacaoCaso, dropDownSoroterapia, containerNumeroAmpolasSoro, dropDownComplicacoesLocais,
                containerEspecificacaoComplicacoesLocais, dropDownComplicacoesSistemicas, containerEspecificacaoComplicacoesSistemicas);

        getContainerInformacoesComplementares().add(containerTratamento);
    }

    private void carregarTratamento() {
        boolean erSoroterapia = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(dropDownSoroterapia, SimNaoEnum.SIM.value());
        boolean erComplicacoesLocais = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(dropDownComplicacoesLocais, SimNaoEnum.SIM.value());
        boolean erComplicacoesSistemicas = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(dropDownComplicacoesSistemicas, SimNaoEnum.SIM.value());

        dropDownSoroterapia.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                boolean erSoroterapia = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(dropDownSoroterapia, SimNaoEnum.SIM.value());

                FichaInvestigacaoAgravoHelper.enableDisableInput(numeroAmpolasSoroAntibotropico, erSoroterapia, erSoroterapia, target);
                FichaInvestigacaoAgravoHelper.enableDisableInput(numeroAmpolasSoroAntibotropicoLaquetico, erSoroterapia, erSoroterapia, target);
                FichaInvestigacaoAgravoHelper.enableDisableInput(numeroAmpolasSoroAntibotropicoCrolatico, erSoroterapia, erSoroterapia, target);
                FichaInvestigacaoAgravoHelper.enableDisableInput(numeroAmpolasSoroAnticrotalico, erSoroterapia, erSoroterapia, target);
                FichaInvestigacaoAgravoHelper.enableDisableInput(numeroAmpolasSoroAntielapidico, erSoroterapia, erSoroterapia, target);
                FichaInvestigacaoAgravoHelper.enableDisableInput(numeroAmpolasSoroAntiescorpionico, erSoroterapia, erSoroterapia, target);
                FichaInvestigacaoAgravoHelper.enableDisableInput(numeroAmpolasSoroAntiaracnidico, erSoroterapia, erSoroterapia, target);
                FichaInvestigacaoAgravoHelper.enableDisableInput(numeroAmpolasSoroAntiloxoscelico, erSoroterapia, erSoroterapia, target);
                FichaInvestigacaoAgravoHelper.enableDisableInput(numeroAmpolasSoroAntilonomico, erSoroterapia, erSoroterapia, target);

                target.add(numeroAmpolasSoroAntibotropico, numeroAmpolasSoroAntibotropicoLaquetico, numeroAmpolasSoroAntibotropicoCrolatico,
                        numeroAmpolasSoroAnticrotalico, numeroAmpolasSoroAntielapidico, numeroAmpolasSoroAntiescorpionico,
                        numeroAmpolasSoroAntiaracnidico, numeroAmpolasSoroAntiloxoscelico, numeroAmpolasSoroAntilonomico);
            }
        });

        dropDownComplicacoesLocais.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                boolean erComplicacoesLocais = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(dropDownComplicacoesLocais, SimNaoEnum.SIM.value());

                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownComplicacoesLocaisInfeccaoSecundaria, erComplicacoesLocais, erComplicacoesLocais, target);
                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownComplicacoesLocaisNecroseExtensa, erComplicacoesLocais, erComplicacoesLocais, target);
                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownComplicacoesLocaisSindromeCompartimental, erComplicacoesLocais, erComplicacoesLocais, target);
                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownComplicacoesLocaisDeficitFuncional, erComplicacoesLocais, erComplicacoesLocais, target);
                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownComplicacoesLocaisAmputacao, erComplicacoesLocais, erComplicacoesLocais, target);

                target.add(dropDownComplicacoesLocaisInfeccaoSecundaria, dropDownComplicacoesLocaisNecroseExtensa, dropDownComplicacoesLocaisSindromeCompartimental,
                        dropDownComplicacoesLocaisDeficitFuncional, dropDownComplicacoesLocaisAmputacao);
            }
        });

        dropDownComplicacoesSistemicas.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                boolean erComplicacoesSistemicas = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(dropDownComplicacoesSistemicas, SimNaoEnum.SIM.value());

                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownComplicacoesSistemicasInsuficienciaRenal, erComplicacoesSistemicas, erComplicacoesSistemicas, target);
                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownComplicacoesSistemicasInsuficienciaRespiratoria, erComplicacoesSistemicas, erComplicacoesSistemicas, target);
                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownComplicacoesSistemicasSepticemia, erComplicacoesSistemicas, erComplicacoesSistemicas, target);
                FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownComplicacoesSistemicasChoque, erComplicacoesSistemicas, erComplicacoesSistemicas, target);

                target.add(dropDownComplicacoesSistemicasInsuficienciaRenal, dropDownComplicacoesSistemicasInsuficienciaRespiratoria, dropDownComplicacoesSistemicasSepticemia,
                        dropDownComplicacoesSistemicasChoque);
            }
        });

        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownClassificacaoCaso, !isModoLeitura(), !isModoLeitura(), null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownSoroterapia, !isModoLeitura(), !isModoLeitura(), null);

        FichaInvestigacaoAgravoHelper.enableDisableInput(numeroAmpolasSoroAntibotropico, erSoroterapia, erSoroterapia, null);
        FichaInvestigacaoAgravoHelper.enableDisableInput(numeroAmpolasSoroAntibotropicoLaquetico, erSoroterapia, erSoroterapia, null);
        FichaInvestigacaoAgravoHelper.enableDisableInput(numeroAmpolasSoroAntibotropicoCrolatico, erSoroterapia, erSoroterapia, null);
        FichaInvestigacaoAgravoHelper.enableDisableInput(numeroAmpolasSoroAnticrotalico, erSoroterapia, erSoroterapia, null);
        FichaInvestigacaoAgravoHelper.enableDisableInput(numeroAmpolasSoroAntielapidico, erSoroterapia, erSoroterapia, null);
        FichaInvestigacaoAgravoHelper.enableDisableInput(numeroAmpolasSoroAntiescorpionico, erSoroterapia, erSoroterapia, null);
        FichaInvestigacaoAgravoHelper.enableDisableInput(numeroAmpolasSoroAntiaracnidico, erSoroterapia, erSoroterapia, null);
        FichaInvestigacaoAgravoHelper.enableDisableInput(numeroAmpolasSoroAntiloxoscelico, erSoroterapia, erSoroterapia, null);
        FichaInvestigacaoAgravoHelper.enableDisableInput(numeroAmpolasSoroAntilonomico, erSoroterapia, erSoroterapia, null);

        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownComplicacoesLocais, !isModoLeitura(), !isModoLeitura(), null);

        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownComplicacoesLocaisInfeccaoSecundaria, erComplicacoesLocais, erComplicacoesLocais, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownComplicacoesLocaisNecroseExtensa, erComplicacoesLocais, erComplicacoesLocais, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownComplicacoesLocaisSindromeCompartimental, erComplicacoesLocais, erComplicacoesLocais, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownComplicacoesLocaisDeficitFuncional, erComplicacoesLocais, erComplicacoesLocais, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownComplicacoesLocaisAmputacao, erComplicacoesLocais, erComplicacoesLocais, null);

        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownComplicacoesSistemicas, !isModoLeitura(), !isModoLeitura(), null);

        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownComplicacoesSistemicasInsuficienciaRenal, erComplicacoesSistemicas, erComplicacoesSistemicas, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownComplicacoesSistemicasInsuficienciaRespiratoria, erComplicacoesSistemicas, erComplicacoesSistemicas, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownComplicacoesSistemicasSepticemia, erComplicacoesSistemicas, erComplicacoesSistemicas, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownComplicacoesSistemicasChoque, erComplicacoesSistemicas, erComplicacoesSistemicas, null);
    }

    private void criarConclusao(InvestigacaoAgravoAcidenteAnimalPeconhento proxy) {
        containerConclusao = new WebMarkupContainer("containerConclusao");
        containerConclusao.setOutputMarkupId(true);

        dropDownAcidenteRelacionadoTrabalho = DropDownUtil.getIEnumDropDown(path(proxy.getAcidenteRelacionadoTrabalho()), SimNaoEnum.getSimNaoIgnorado());
        dropDownEvolucaoCaso = DropDownUtil.getIEnumDropDown(path(proxy.getEvolucaoCaso()), InvestigacaoAgravoAcidenteAnimalPeconhento.EvolucaoCaso.values());

        dataObito = new DateChooser(path(proxy.getDataObito()));

        if (dataAcidente.getComponentValue() != null) {
            dataObito.getData().setMinDate(new DateOption(dataAcidente.getComponentValue()));
        }

        dataObito.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        containerConclusao.add(dropDownAcidenteRelacionadoTrabalho, dropDownEvolucaoCaso, dataObito);
        getContainerInformacoesComplementares().add(containerConclusao);
    }

    private void carregarConclusao() {
        boolean erEvolucaoCasoObito = !isModoLeitura() && (
                FichaInvestigacaoAgravoHelper.isLongTrue(dropDownEvolucaoCaso, InvestigacaoAgravoAcidenteAnimalPeconhento.EvolucaoCaso.OBITO_ACIDENTE_ANIMAL_PECONHENTO.value()) ||
                FichaInvestigacaoAgravoHelper.isLongTrue(dropDownEvolucaoCaso, InvestigacaoAgravoAcidenteAnimalPeconhento.EvolucaoCaso.OBITO_OUTRAS_CAUSAS.value()));

        dropDownEvolucaoCaso.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                boolean erEvolucaoCasoObito = !isModoLeitura() && (
                        FichaInvestigacaoAgravoHelper.isLongTrue(dropDownEvolucaoCaso, InvestigacaoAgravoAcidenteAnimalPeconhento.EvolucaoCaso.OBITO_ACIDENTE_ANIMAL_PECONHENTO.value()) ||
                        FichaInvestigacaoAgravoHelper.isLongTrue(dropDownEvolucaoCaso, InvestigacaoAgravoAcidenteAnimalPeconhento.EvolucaoCaso.OBITO_OUTRAS_CAUSAS.value()));

                FichaInvestigacaoAgravoHelper.enableDisableDates(dataObito, erEvolucaoCasoObito, erEvolucaoCasoObito, target);

                target.add(dataObito);
            }
        });

        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownAcidenteRelacionadoTrabalho, !isModoLeitura(), false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(dropDownEvolucaoCaso, !isModoLeitura(), false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDates(dataObito, erEvolucaoCasoObito, erEvolucaoCasoObito, null);
    }

    private void criarObservacoes(InvestigacaoAgravoAcidenteAnimalPeconhento proxy) {
        containerObservacoes = new WebMarkupContainer("containerObservacoes");
        containerObservacoes.setOutputMarkupId(true);

        observacoes = new InputField(path(proxy.getObservacao()));

        containerObservacoes.add(observacoes);
        getContainerInformacoesComplementares().add(containerObservacoes);
    }

    private void carregarObservacoes() {
        FichaInvestigacaoAgravoHelper.enableDisableInput(observacoes, !isModoLeitura(), false, null);
    }

    private void criarUsuarioDataEncerramento(InvestigacaoAgravoAcidenteAnimalPeconhento proxy) {
        containerEncerramento = new WebMarkupContainer("containerEncerramento");
        containerEncerramento.setOutputMarkupId(true);

        usuarioEncerramento = new DisabledInputField(path(proxy.getUsuarioEncerramento()));
        dataEncerramento = new DisabledInputField(path(proxy.getDataEncerramento()));

        containerEncerramento.add(usuarioEncerramento, dataEncerramento);
        getContainerInformacoesComplementares().add(containerEncerramento);
    }
}
