package br.com.celk.view.materiais.relatorios.judicial;

import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.facade.DispensacaoMedicamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.materiais.judicial.interfaces.dto.RelatorioRelacaoProdutoSolicitadoDispensadoDTOParam;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.vo.basico.TipoSolicitacaoProduto;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.Model;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoProdutoSolicitadoDispensadoPage extends RelatorioPage<RelatorioRelacaoProdutoSolicitadoDispensadoDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresaDispensacao;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresaSolicitante;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;
    private DropDown dropDownTipoSolicitacaoProduto;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private DropDown dropDownFormaApresentacao;

    @Override
    public void init(Form form) {
        RelatorioRelacaoProdutoSolicitadoDispensadoDTOParam proxy = on(RelatorioRelacaoProdutoSolicitadoDispensadoDTOParam.class);

        form.add(autoCompleteConsultaEmpresaDispensacao = new AutoCompleteConsultaEmpresa(path(proxy.getEmpresaDispensacao())));
        form.add(autoCompleteConsultaEmpresaSolicitante = new AutoCompleteConsultaEmpresa(path(proxy.getEmpresaSolicitante())));
        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        form.add(autoCompleteConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus(path(proxy.getUsuarioCadsus())));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto(path(proxy.getProduto())));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(getDropDownTipoSolicitacaoProduto(path(proxy.getTipoSolicitacaoProduto())));
        form.add(dropDownFormaApresentacao = DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelatorioRelacaoProdutoSolicitadoDispensadoDTOParam.FormaApresentacao.values()));
        form.add(new RequiredPnlDatePeriod(path(proxy.getPeriodo())));
    }
    
    private DropDown getDropDownTipoSolicitacaoProduto(String id) {
        if(dropDownTipoSolicitacaoProduto == null){
            dropDownTipoSolicitacaoProduto = (DropDown) new DropDown(id).setLabel(new Model<String>(bundle("tipoSolicitacao")));

            List<TipoSolicitacaoProduto> list = LoadManager.getInstance(TipoSolicitacaoProduto.class)
                    .addProperties(new HQLProperties(TipoSolicitacaoProduto.class).getProperties())
                    .addSorter(new QueryCustom.QueryCustomSorter(TipoSolicitacaoProduto.PROP_DESCRICAO, BuilderQueryCustom.QuerySorter.CRESCENTE))
                    .start().getList();

            dropDownTipoSolicitacaoProduto.addChoice(null, "");
            if(CollectionUtils.isNotNullEmpty(list)){
                for (TipoSolicitacaoProduto tsp : list) {
                    dropDownTipoSolicitacaoProduto.addChoice(tsp, tsp.getDescricao());
                }
            }
        }

        return dropDownTipoSolicitacaoProduto;
    }


    @Override
    public Class getDTOParamClass() {
        return RelatorioRelacaoProdutoSolicitadoDispensadoDTOParam.class;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresaDispensacao.getTxtDescricao().getTextField();
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoProdutoSolicitadoDispensadoDTOParam param) throws ReportException {
        return BOFactory.getBO(DispensacaoMedicamentoReportFacade.class).relatorioRelacaoMedicamentoSolicitadoDispensado(param);
    }

    @Override
    public String getTituloPrograma() {
        return bundle("relacaoProdutosSolicitadosDispensados");
    }
}
