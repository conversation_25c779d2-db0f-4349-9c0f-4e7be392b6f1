package br.com.celk.view.agenda.agendamento.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public class DlgMotivoBloqueioFilaEspera extends Window {

    private PnlMotivoBloqueioFilaEspera pnlMotivoBloqueioFilaEspera;

    public DlgMotivoBloqueioFilaEspera(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
            @Override
            protected String load(){
                return BundleManager.getString("motivoBloqueio");
            }
        });
        setInitialWidth(500);
        setInitialHeight(150);

        setResizable(false);

        setContent(pnlMotivoBloqueioFilaEspera = new PnlMotivoBloqueioFilaEspera(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                close(target);
            }
        });
    }

    public void show(AjaxRequestTarget target, SolicitacaoAgendamento solicitacaoAgendamento) {
        show(target);
        this.pnlMotivoBloqueioFilaEspera.update(target, solicitacaoAgendamento);
    }
}
