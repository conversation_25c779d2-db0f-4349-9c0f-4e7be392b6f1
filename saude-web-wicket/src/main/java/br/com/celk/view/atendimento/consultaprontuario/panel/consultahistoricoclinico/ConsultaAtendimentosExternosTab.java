package br.com.celk.view.atendimento.consultaprontuario.panel.consultahistoricoclinico;

import br.com.celk.atendimento.prontuario.interfaces.dto.AtendimentoProntuarioDTO;
import br.com.celk.atendimento.prontuario.interfaces.dto.ConsultaProntuariosDemmandPaggingDTOParam;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.atendimento.prontuario.panel.PanelHistoricoClinicoAtendimentoExterno;
import br.com.celk.view.atendimento.prontuario.tabbedpanel.AtendimentosExternoTab;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NoHistoricoClinicoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoProntuario;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.ajax.markup.html.form.AjaxCheckBox;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.repeater.RepeatingView;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 */
public class ConsultaAtendimentosExternosTab extends TabPanel<NoHistoricoClinicoDTO> {

    private ConsultaProntuariosDemmandPaggingDTOParam dtoParam;
    private WebMarkupContainer containerRepeater;
    private RepeatingView repeaterHistoricoClinicoAtendimentos;

    public ConsultaAtendimentosExternosTab(String id, NoHistoricoClinicoDTO object) {
        super(id, object);
        this.dtoParam = new ConsultaProntuariosDemmandPaggingDTOParam(object.getAtendimento().getUsuarioCadsus());
        init();
    }

    private void init() {
        setDefaultModel(new CompoundPropertyModel(dtoParam));

        add(getDropDownTipoRegistro());

        InputField txtDescricao = new InputField("descricao");
        txtDescricao.addAjaxUpdateValue();
        add(txtDescricao);

        txtDescricao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                procurar(target);
            }
        });

        add(new AjaxCheckBox("meusAtendimentos") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                procurar(target);
            }
        });

        add(containerRepeater = new WebMarkupContainer("containerRepeater"));
        containerRepeater.setOutputMarkupId(true);
        containerRepeater.add(repeaterHistoricoClinicoAtendimentos = new RepeatingView("repeaterHistoricoClinicoAtendimentos"));
        add(new AbstractAjaxButton("btnCarregarMais") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                procurar(target, true);
            }
        });

        procurar(null);
    }

    private void addRepeater(List<AtendimentoProntuarioDTO> prontuarios, boolean deveExibirHorarioImpressao) {
        PanelHistoricoClinicoAtendimentoExterno painel = new PanelHistoricoClinicoAtendimentoExterno(
            repeaterHistoricoClinicoAtendimentos.newChildId(),
            prontuarios,
            deveExibirHorarioImpressao
        );
        repeaterHistoricoClinicoAtendimentos.add(painel);
    }

    private DropDown getDropDownTipoRegistro() {
        DropDown<Long> cbxTipoRegistro = DropDownUtil.getIEnumDropDown("tipoRegistroProntuario", AtendimentoProntuario.TipoRegistro.values(), true, bundle("todos"), false, false, true);
        cbxTipoRegistro.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                procurar(target);
            }
        });

        return cbxTipoRegistro;
    }

    private void procurar(AjaxRequestTarget target) {
        procurar(target, false);
    }

    private void procurar(AjaxRequestTarget target, boolean carregarMais) {
        if (carregarMais) {
            dtoParam.moreResults();
        } else {
            dtoParam.resetControlPagging();
            repeaterHistoricoClinicoAtendimentos.removeAll();
        }

        try {
            List<AtendimentoProntuarioDTO> atendimentoProntuarioList = BOFactoryWicket.getBO(AtendimentoFacade.class).consultaProntuariosDemmandPaggingExterno(dtoParam);
            if (CollectionUtils.isNotNullEmpty(atendimentoProntuarioList)) {
                boolean deveExibirHorarioImpressao = obterConfiguracaoImprimeHorarioImpressao();
                Group<AtendimentoProntuarioDTO> byCodigo = Lambda.group(atendimentoProntuarioList, Lambda.by(Lambda.on(AtendimentoProntuarioDTO.class).getCodigoAtendimentoPrincipalExterno()));
                for (Group<AtendimentoProntuarioDTO> subgroup : byCodigo.subgroups()) {
                    addRepeater(subgroup.findAll(), deveExibirHorarioImpressao);
                }
            }
            if (target != null) {
                target.add(containerRepeater);
            }

        } catch (DAOException | ValidacaoException ex) {
            Logger.getLogger(AtendimentosExternoTab.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    @Override
    public String getTitle() {
        return bundle("atendimentosExternos");
    }

    private boolean obterConfiguracaoImprimeHorarioImpressao() throws DAOException {
        String configuracao = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("ImprimeHorarioImpressao");
        return RepositoryComponentDefault.SIM.equals(configuracao);
    }

}
