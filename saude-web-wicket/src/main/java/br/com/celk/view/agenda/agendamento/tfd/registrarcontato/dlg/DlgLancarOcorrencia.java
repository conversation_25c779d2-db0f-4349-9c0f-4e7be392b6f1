package br.com.celk.view.agenda.agendamento.tfd.registrarcontato.dlg;

import br.com.celk.component.window.Window;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgLancarOcorrencia extends Window {
    
    private LaudoTfd object;
    private PnlLancarOcorrencia pnlLancarOcorrencia;
    public DlgLancarOcorrencia(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        setInitialWidth(600);
        setInitialHeight(150);

        setResizable(false);

        setTitle(bundle("ocorrencia"));

        setContent(pnlLancarOcorrencia = new PnlLancarOcorrencia(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, String motivo) throws ValidacaoException, DAOException {
                close(target);
                DlgLancarOcorrencia.this.onConfirmar(target,motivo,DlgLancarOcorrencia.this.object);
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target,String motivo, LaudoTfd object) throws ValidacaoException, DAOException;

    public void setObject(LaudoTfd object) {
        this.object = object;
    }

    @Override
    public void show(AjaxRequestTarget target) {
        pnlLancarOcorrencia.limpar(target);
        super.show(target);
    }
}