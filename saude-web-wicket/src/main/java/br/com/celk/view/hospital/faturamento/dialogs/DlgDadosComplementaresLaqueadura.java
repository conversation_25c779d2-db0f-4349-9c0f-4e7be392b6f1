package br.com.celk.view.hospital.faturamento.dialogs;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgDadosComplementaresLaqueadura extends Window {

    private PnlDadosComplementaresLaqueadura pnlDadosComplementaresLaqueadura;
    private ItemContaPaciente itemContaPaciente;

    public DlgDadosComplementaresLaqueadura(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(BundleManager.getString("dadosComplementaresLaqueadura"));
        setInitialWidth(650);
        setInitialHeight(215);
        setResizable(false);

        setContent(pnlDadosComplementaresLaqueadura = new PnlDadosComplementaresLaqueadura(getContentId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException {
                close(target);
                DlgDadosComplementaresLaqueadura.this.onFechar(target, itemContaPaciente);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                close(target);
                DlgDadosComplementaresLaqueadura.this.onFechar(target, itemContaPaciente);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                close(target);
                onFechar(target, itemContaPaciente);
                return false;
            }
        });
    }

    public void show(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) {
        super.show(target);
        this.itemContaPaciente = itemContaPaciente;
        pnlDadosComplementaresLaqueadura.limpar(target);
        pnlDadosComplementaresLaqueadura.setItemContaPaciente(target, itemContaPaciente);
    }

    public abstract void onFechar(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente);
}
