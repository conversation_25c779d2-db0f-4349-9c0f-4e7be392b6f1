package br.com.celk.view.agenda.cadastro;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.agendamento.dto.AgendaGradeAtendimentoHorariosDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgAlterarVagas extends Window {

    private PnlAlterarVagas pnlAlterarVagas;

    public DlgAlterarVagas(String id) {
        super(id);
        init();
    }

    private void init() {
        setInitialHeight(130);
        setInitialWidth(600);

        setResizable(true);

        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("alterarVagas");
            }
        });

        setContent(pnlAlterarVagas = new PnlAlterarVagas(getContentId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO dto) throws DAOException, ValidacaoException {
                DlgAlterarVagas.this.onConfirmar(target, dto);
                close(target);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                close(target);
            }
        });
    }


    public void show(AjaxRequestTarget target, Agenda agenda, AgendaGradeAtendimentoHorariosDTO dto, List<AgendaGradeAtendimentoHorariosDTO> agendaGradeAtendimentoHorariosAtuaisDTOList) {
        //pnlNovoLancamento.limpar(target); 
        pnlAlterarVagas.limpar(target);
        pnlAlterarVagas.setItemContaPaciente(target, agenda, dto, agendaGradeAtendimentoHorariosAtuaisDTOList);
        show(target);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO dto) throws DAOException, ValidacaoException;
}
