package br.com.celk.view.vigilancia.pessoa;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IFileAction;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.cpffield.CpfField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.StringUtil;
import br.com.celk.view.vigilancia.endereco.autocomplete.AutoCompleteConsultaVigilanciaEndereco;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaVigilanciaPessoaPage extends ConsultaPage<VigilanciaPessoa, List<BuilderQueryCustom.QueryParameter>> {

    private String nome;
    private String cpf;
    private AutoCompleteConsultaVigilanciaEndereco autoCompleteConsultaVigilanciaEndereco;
    private VigilanciaEndereco vigilanciaEndereco;

    public ConsultaVigilanciaPessoaPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("nome"));
        form.add(new CpfField("cpf"));
        form.add(autoCompleteConsultaVigilanciaEndereco = new AutoCompleteConsultaVigilanciaEndereco("vigilanciaEndereco"));
        add(form);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        VigilanciaPessoa proxy = on(VigilanciaPessoa.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("nome"), proxy.getNome()));
        columns.add(createColumn(bundle("cpf"), proxy.getCpfFormatado()));
        columns.add(createColumn(bundle("rg"), proxy.getRg()));
        columns.add(createColumn(bundle("celular"), proxy.getCelularFormatado()));
        columns.add(createColumn(bundle("email"), proxy.getEmail()));
        columns.add(createColumn(bundle("dataNascimento"), proxy.getDataNascimento()));
        columns.add(createColumn(bundle("endereco"), proxy.getVigilanciaEndereco().getEnderecoFormatadoComCidade()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<VigilanciaPessoa>() {
            @Override
            public void customizeColumn(VigilanciaPessoa rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<VigilanciaPessoa>() {
                    @Override
                    public void action(AjaxRequestTarget target, VigilanciaPessoa modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroVigilanciaPessoaPage(modelObject));
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<VigilanciaPessoa>() {
                    @Override
                    public void action(AjaxRequestTarget target, VigilanciaPessoa modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(VigilanciaFacade.class).excluirPessoaVigilancia(modelObject);
                        getPageableTable().populate(target);
                    }
                });
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<VigilanciaPessoa>() {
                    @Override
                    public void action(AjaxRequestTarget target, VigilanciaPessoa modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroVigilanciaPessoaPage(modelObject, true));
                    }
                });
                addAction(ActionType.HISTORICO, rowObject, new IFileAction<VigilanciaPessoa>() {
                    @Override
                    public File action(VigilanciaPessoa vigilanciaPessoa) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).historico(vigilanciaPessoa);
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(
                        new HQLProperties(VigilanciaPessoa.class).getProperties(),
                        new HQLProperties(VigilanciaEndereco.class, VigilanciaPessoa.PROP_VIGILANCIA_ENDERECO).getProperties(),
                        new HQLProperties(Usuario.class, VigilanciaPessoa.PROP_USUARIO_RESPONSAVEL).getProperties()
                );
            }

            @Override
            public Class getClassConsulta() {
                return VigilanciaPessoa.class;
            }
        }) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VigilanciaPessoa.PROP_NOME, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(VigilanciaPessoa.PROP_NOME), BuilderQueryCustom.QueryParameter.ILIKE, nome));
        if (!"".equals(StringUtil.getDigits(cpf))) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(VigilanciaPessoa.PROP_CPF), StringUtil.getDigits(cpf)));
        }

        if (vigilanciaEndereco != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(VigilanciaPessoa.PROP_VIGILANCIA_ENDERECO), vigilanciaEndereco));
        }

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroVigilanciaPessoaPage.class;
    }


    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaVigilanciaPessoaPage");
    }

}
