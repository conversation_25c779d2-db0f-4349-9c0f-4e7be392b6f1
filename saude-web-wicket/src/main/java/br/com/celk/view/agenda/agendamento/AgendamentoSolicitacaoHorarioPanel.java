package br.com.celk.view.agenda.agendamento;

import br.com.celk.agendamento.AgendamentoHelper;
import br.com.celk.agendamento.ValidacoesAgendamentoBehavior;
import br.com.celk.agendamento.agendamentofilaespera.RegraDiasLimiteAgendamentoListaEspera;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.bo.service.rest.integracao.aquarela.prescritor.AquarelaPrescritorDto;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionLinkPanel;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dialog.DlgImpressao;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.notification.INotificationPanel;
import br.com.celk.component.table.SelectionTable;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.column.TimeColumn;
import br.com.celk.component.table.selection.deprecated.MultiSelectionTableOld;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CalcularProcedimentoPorVaga;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.agenda.agendamento.agendamentoListaEspera.FiltrarAgendaGradePorPrioridade;
import br.com.celk.view.agenda.agendamento.containerProfissional.BuildWebContainerProfissional;
import br.com.celk.view.agenda.agendamento.containerProfissional.BuildWebContainerUnidade;
import br.com.celk.view.agenda.agendamento.recebimentosolicitacoesagendamento.ConfirmacaoRecebimentoSolicitacoesPage;
import br.com.celk.view.agenda.agendamento.util.ValidaTipoProcedimentoUtil;
import br.com.celk.view.agenda.solicitacao.DetalhesSolicitacaoPage;
import br.com.celk.view.atendimento.recepcao.panel.marcacao.agendamento.*;
import br.com.celk.view.atendimento.recepcao.panel.marcacao.dialog.DlgRecomendacoesAgenda;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.agendamento.dto.DadosAgendamentoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.*;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.AtendimentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioImprimirComprovanteAgendamentoDTOParam;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.*;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import br.com.ksisolucoes.vo.prontuario.exame.SolicitacaoAgendamentoExame;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.form.AjaxButton;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.link.BookmarkablePageLink;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;
import org.hamcrest.Matchers;

import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.*;

/**
 *
 * <AUTHOR>
 */
@Private
public class AgendamentoSolicitacaoHorarioPanel extends BasePage {

    private List<AgendaGradeAtendimentoDTO> horariosDisponiveis;
    private Form<AgendaGradeAtendimentoDTOParam> form;
    private Table<AgendaGradeAtendimentoDTO> tblAgendamentoSolicitacao;
    private final AgendaGradeAtendimentoDTOParam param = new AgendaGradeAtendimentoDTOParam();
    private Class classeVoltar;
    private String unidadeAgenda;
    private String profissionalAgenda;
    private PageParameters parameters;
    private InputField inputUnidadeAgenda;
    private InputField inputProfissionalAgenda;
    private LoteSolicitacaoAgendamentoItem loteSolicitacaoAgendamentoItem;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;

    private SelectionTable<AgendaGradeAtendimentoDTO> tblAgenda;
    private List<AgendaGradeAtendimentoDTO> agendaList;

    private List<AgendaGradeAtendimentoPacienteDTO> agendaHorarioList;
    private MultiSelectionTableOld<AgendaGradeAtendimentoPacienteDTO> tblAgendaHorarios;

    private DlgImpressao dlgImpressao;
    private DlgAgendarAtendimento dlgAgendarAtendimento;
    private final List<AgendaGradeAtendimentoReservadosDTO> horariosReservados = new ArrayList<>();
    private DlgRecomendacoesAgenda dlgRecomendacoesAgenda;
    private Empresa empresaAgenda;
    private AgendamentoListaEsperaDTOParam paramLista;

    private List<SolicitacaoAgendamentoExame> lstSolicitacaoAgendamentoExame;

    private boolean mostrarDialogConfirmacao = false;
    private DlgConfirmacaoSimNao<AgendaGradeAtendimentoPacienteDTO> dlgConfirmacaoSimNao;
    private DlgPacienteHorarioSugerido dlgPacienteHorarioSugerido;
    private AjaxButton btnConsultarDatasSugeridas;
    private AgendaGradeAtendimentoDTO agendaGradeSelecionada;

    public AgendamentoSolicitacaoHorarioPanel(SolicitacaoAgendamento solicitacaoAgendamento, PageParameters parameters, List<AgendaGradeAtendimentoDTO> horarios, Empresa empresaAgenda) {
        super(parameters);
        this.parameters = parameters;
        this.empresaAgenda = empresaAgenda;
        this.horariosDisponiveis = horarios;
        inicializarParam(solicitacaoAgendamento);
        init();
    }

    public AgendamentoSolicitacaoHorarioPanel(SolicitacaoAgendamento solicitacaoAgendamento, PageParameters parameters, Empresa empresaAgenda,  List<SolicitacaoAgendamentoExame> lstSolicitacaoAgendamentoExame) {
        super(parameters);
        this.parameters = parameters;
        this.empresaAgenda = empresaAgenda;
        this.lstSolicitacaoAgendamentoExame = lstSolicitacaoAgendamentoExame;
        inicializarParam(solicitacaoAgendamento);
        init();
    }

    public AgendamentoSolicitacaoHorarioPanel(SolicitacaoAgendamento solicitacaoAgendamento, PageParameters parameters, Empresa empresaAgenda,  List<AgendaGradeAtendimentoDTO> horarios, List<SolicitacaoAgendamentoExame> lstSolicitacaoAgendamentoExame) {
        super(parameters);
        this.parameters = parameters;
        this.empresaAgenda = empresaAgenda;
        this.lstSolicitacaoAgendamentoExame = lstSolicitacaoAgendamentoExame;
        this.horariosDisponiveis = horarios;
        this.param.setListaDiasDisponiveisAux(horarios);
        inicializarParam(solicitacaoAgendamento);
        init();
    }

    public AgendamentoSolicitacaoHorarioPanel(SolicitacaoAgendamento solicitacaoAgendamento, AgendamentoListaEsperaDTOParam paramList, Empresa empresaAgenda, List<SolicitacaoAgendamentoExame> lstSolicitacaoAgendamentoExame) {
        this.paramLista = paramList;
        this.empresaAgenda = empresaAgenda;
        this.lstSolicitacaoAgendamentoExame = lstSolicitacaoAgendamentoExame;
        inicializarParam(solicitacaoAgendamento);
        init();
    }

    public AgendamentoSolicitacaoHorarioPanel(SolicitacaoAgendamento solicitacaoAgendamento, LoteSolicitacaoAgendamentoItem loteSolicitacaoAgendamentoItem, PageParameters parameters) {
        super(parameters);
        this.parameters = parameters;
        inicializarParam(solicitacaoAgendamento);
        this.loteSolicitacaoAgendamentoItem = loteSolicitacaoAgendamentoItem;
        init();
    }

    public AgendamentoSolicitacaoHorarioPanel(SolicitacaoAgendamento solicitacaoAgendamento, Class classeVoltar, PageParameters parameters, Empresa empresaAgenda) {
        super(parameters);
        this.parameters = parameters;
        this.empresaAgenda = empresaAgenda;
        inicializarParam(solicitacaoAgendamento);
        this.classeVoltar = classeVoltar;
        init();
    }

    public void setHorarios(List<AgendaGradeAtendimentoDTO> horariosDisponiveis) {
        this.horariosDisponiveis = horariosDisponiveis;
    }

    public void setExames(List<SolicitacaoAgendamentoExame> exames) {
        this.lstSolicitacaoAgendamentoExame = exames;
    }

    private List<AgendaGradeAtendimentoDTO> filtraPorProfissional(Profissional profissional) {
        if (profissional == null) {
            return horariosDisponiveis;
        }
        List<AgendaGradeAtendimentoDTO> horariosFiltrados = new ArrayList<>();
        for (AgendaGradeAtendimentoDTO horario: horariosDisponiveis) {
            Profissional profissionalHorario = horario.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getProfissional();
            if (profissionalHorario != null && profissional.getCodigo().equals(profissionalHorario.getCodigo())) {
                horariosFiltrados.add(horario);
            }
        }
        return horariosFiltrados;
    }

    private void init() {
        AgendaGradeAtendimentoDTOParam proxy = on(AgendaGradeAtendimentoDTOParam.class);

        getForm().add(new DisabledInputField(path(proxy.getTipoProcedimento().getDescricao())));
        getForm().add(new DisabledInputField(path(proxy.getEmpresaOrigem().getDescricao())));
        getForm().add(new DisabledInputField(path(proxy.getUsuarioCadsus().getNomeSocial())));

        autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional(path(proxy.getProfissional()));

        autoCompleteConsultaProfissional.add(new ConsultaListener<Profissional>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Profissional object) {
                inputProfissionalAgenda.limpar(target);
                inputUnidadeAgenda.limpar(target);
                tblAgenda.populate(target);
                tblAgendaHorarios.limpar(target);
            }
        });

        autoCompleteConsultaProfissional.add(new RemoveListener<Profissional>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Profissional object) {
                inputProfissionalAgenda.limpar(target);
                inputUnidadeAgenda.limpar(target);
                tblAgenda.populate(target);
                tblAgendaHorarios.limpar(target);
            }
        });

        inputProfissionalAgenda = new DisabledInputField("profissionalAgenda", new PropertyModel(this, "profissionalAgenda"));
        inputUnidadeAgenda = new DisabledInputField("unidadeAgenda", new PropertyModel(this, "unidadeAgenda"));

        WebMarkupContainer containerProfissional = new BuildWebContainerProfissional().setAutoCompleteConsultaProfissional(autoCompleteConsultaProfissional)
            .setInputProfissionalAgenda(inputProfissionalAgenda)
            .ocultarContainer(param.getSolicitacaoAgendamento())
            .build();

        WebMarkupContainer containerUnidade = new BuildWebContainerUnidade().setInputUnidadeAgenda(inputUnidadeAgenda)
            .ocultarContainer()
            .build();

        getForm().add(containerProfissional);
        getForm().add(containerUnidade);

        getForm().add(btnConsultarDatasSugeridas = getBtnConsultarDatasSugeridas());
        btnConsultarDatasSugeridas.setVisible(ExistIntegracaoAquarela.exists());

        getForm().add(tblAgenda = new SelectionTable("tblAgenda", getColumnsAgenda(), getCollectionProviderAgenda()));
        tblAgenda.populate();
        tblAgenda.addSelectionAction(new ISelectionAction<AgendaGradeAtendimentoDTO>() {
            @Override
            public void onSelection(AjaxRequestTarget target, AgendaGradeAtendimentoDTO object) {
                if (object.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getProfissional() != null) {
                    profissionalAgenda = object.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getProfissional().getNome();
                    target.add(inputProfissionalAgenda);
                }

                unidadeAgenda = object.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getEmpresa().getDescricao();
                target.add(inputUnidadeAgenda);

                try {
                    selecionarAgendaGradeHorario(target, object);
                } catch (ValidacaoException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                } catch (DAOException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }

                carregarRecomendacoes(target, object.getAgendaGradeAtendimento().getAgendaGrade().getAgenda());
            }
        });

        tblAgendaHorarios = new MultiSelectionTableOld("tblAgendaHorarios", getColumnsHorarios(), getCollectionProviderHorarios()) {
            @Override
            public List customizeColumns(List columns) {
                columns = super.customizeColumns(columns);
                columns.remove(0);
                return columns;
            }
        };
        getForm().add(tblAgendaHorarios);
        tblAgendaHorarios.populate();

        tblAgendaHorarios.addSelectionAction(new ISelectionAction<AgendaGradeAtendimentoPacienteDTO>() {
            @Override
            public void onSelection(AjaxRequestTarget target, AgendaGradeAtendimentoPacienteDTO object) {
                tblAgendaHorarios.update(target);
                target.add(tblAgendaHorarios);
            }
        });

        tblAgendaHorarios.addUnselectionAction(new ISelectionAction<AgendaGradeAtendimentoPacienteDTO>() {
            @Override
            public void onSelection(AjaxRequestTarget target, AgendaGradeAtendimentoPacienteDTO object) {
                tblAgendaHorarios.update(target);
                target.add(tblAgendaHorarios);
            }
        });

        if (getClasseVoltar() != null) {
            getForm().add(new BookmarkablePageLink("btnVoltar", getClasseVoltar(), parameters));
        } else {
            getForm().add(new VoltarButton("btnVoltar"));
        }

        addBtnDetalhes();

        add(getForm());
        if (!br.com.celk.util.CollectionUtils.isNotNullEmpty(param.getListaDiasDisponiveisAux())) {
            verificarComparecimento();
        }
        carregarOrientacaoDocumentoRetido();
    }

    private void addBtnDetalhes() {
        getForm().add(new AbstractAjaxLink("btnDetalhes") {
            @Override
            public void onAction(AjaxRequestTarget target) {
                Page detalhes = new DetalhesSolicitacaoPage(param.getSolicitacaoAgendamento().getCodigo(),
                        parameters,
                        empresaAgenda,
                        horariosDisponiveis,
                        lstSolicitacaoAgendamentoExame,
                        true);
                setResponsePage(detalhes);
            }
        });
    }

    private void verificarComparecimento() {
        if (CollectionUtils.isNotNullEmpty(param.getListaDiasDisponiveisAux())) {
            return;
        }
        ValidacoesAgendamentoDTO validacoesAgendamentoDTO = new ValidacoesAgendamentoDTO();
        validacoesAgendamentoDTO.setTipoProcedimento(param.getTipoProcedimento());
        validacoesAgendamentoDTO.setUsuarioCadsus(param.getUsuarioCadsus());
        validacoesAgendamentoDTO.setCidadeEmpresaLogada(SessaoAplicacaoImp.getInstance().getEmpresa().getCidade());
        ValidacoesAgendamentoBehavior validacoesAgendamentoBehavior = new ValidacoesAgendamentoBehavior(validacoesAgendamentoDTO);
        String msg = validacoesAgendamentoBehavior.validacoes();
        if (msg != null && !"".equals(msg) && !validacoesAgendamentoBehavior.isAgendamentoLiberado()) {
            warn(msg);
        }
    }

    private void carregarOrientacaoDocumentoRetido() {
        TipoProcedimento tp = LoadManager.getInstance(TipoProcedimento.class)
            .addProperty(TipoProcedimento.PROP_CODIGO)
            .addProperty(TipoProcedimento.PROP_DESCRICAO)
            .addProperty(TipoProcedimento.PROP_FLAG_DOCUMENTO_RETIDO)
            .addParameter(new QueryCustom.QueryCustomParameter(TipoProcedimento.PROP_CODIGO, getForm().getModel().getObject().getTipoProcedimento().getCodigo()))
            .start().getVO();
        if (RepositoryComponentDefault.SIM_LONG.equals(tp.getFlagDocumentoRetido())) {
            getSession().getFeedbackMessages().warn(this, bundle("msgDocumentoDeveFicarRetidoEstabelecimento"));
        }
    }

    public List<IColumn> getColumnsAgenda() {
        List<IColumn> columns = new ArrayList<>();
        AgendaGradeAtendimentoDTO proxy = on(AgendaGradeAtendimentoDTO.class);
        columns.add(new DateColumn(bundle("dia"), path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getData())).setPattern("dd/MM"));
        columns.add(createColumn(bundle("semana"), proxy.getDiaSemanaAbv()));
        columns.add(createColumn(bundle("tipo"), proxy.getAgendaGradeAtendimento().getTipoAtendimentoAgenda().getDescricao()));
        columns.add(createColumn(bundle("vagas"), proxy.getVagasDisponiveis()));
        return columns;
    }

    public ICollectionProvider getCollectionProviderAgenda() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object o) throws DAOException, ValidacaoException {
                AgendaGradeAtendimentoDTOParam dto = form.getModel().getObject();
                agendaList = new ArrayList<>();
                if (dto.getSolicitacaoAgendamento() != null) {
                    param.setProfissional(dto.getProfissional());

                    //Se possui cache da lista
                    if (CollectionUtils.isNotNullEmpty(horariosDisponiveis)) {
                        agendaList = filtraPorProfissional(param.getProfissional());
                    } else {
                        if (SolicitacaoAgendamento.TIPO_FILA_REGULACAO.equals(dto.getSolicitacaoAgendamento().getTipoFila())) {
                            param.setTipoAtendimentoAgendaList(Arrays.asList(TipoAtendimentoAgenda.TIPO_REGULACAO));
                        } else {
                            param.setTipoAtendimentoAgendaList(Arrays.asList(TipoAtendimentoAgenda.TIPO_CONSULTA, TipoAtendimentoAgenda.TIPO_RETORNO));
                        }
                        param.setApenasAgendasComVagas(true);
                        param.setValidarUnidadeInformatizada(true);
                        param.setValidarEmpresaOrigem(true);
                        param.setExameProcedimentoList(AgendamentoHelper.examesSolicitacaoAgendamento(param.getSolicitacaoAgendamento()));
                        param.setDiasLimiteAgendamentoListasEspera(new RegraDiasLimiteAgendamentoListaEspera(param.getSolicitacaoAgendamento().getEmpresa()));

                        agendaList = BOFactoryWicket.getBO(AgendamentoFacade.class).consultarVagasDisponiveisAgendaExame(param);
                        if (!AgendamentoHelper.isCadastrarNaRecepcao(param.getTipoProcedimento())){
                            agendaList = new FiltrarAgendaGradePorPrioridade().filtrar(agendaList, param.getSolicitacaoAgendamento());
                        }
                        //Seta o cache da lista
                        setHorarios(agendaList);
                    }
                }
                return agendaList;
            }
        };
    }

    public List<IColumn> getColumnsHorarios() {
        List<IColumn> columns = new ArrayList<>();
        AgendaGradeAtendimentoPacienteDTO proxy = on(AgendaGradeAtendimentoPacienteDTO.class);
        columns.add(getActionColumnHorarios());
        columns.add(new TimeColumn<AgendaGradeHorario>(bundle("horario"), path(proxy.getAgendaGradeHorario().getHora())).setPattern("HH:mm"));
        columns.add(createColumn(bundle("paciente"), proxy.getNomePaciente()));
        return columns;
    }

    private IColumn getActionColumnHorarios() {
        return new MultipleActionCustomColumn<AgendaGradeAtendimentoPacienteDTO>() {
            @Override
            public void customizeColumn(final AgendaGradeAtendimentoPacienteDTO rowObject) {
                boolean enabledActionAgendar = AgendaGradeHorario.Status.PENDENTE.value().equals(rowObject.getAgendaGradeHorario().getStatus())
                    && !isHorarioAgendado(rowObject.getAgendaGradeHorario());
                ActionLinkPanel alp = addAction(ActionType.AGENDAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        permiteAgendamentoRetroativo(rowObject,target);
                        viewDlgAgendarAtendimento(target, rowObject);
                    }
                });
                alp.setEnabled(enabledActionAgendar);
            }
        };
    }

    private void permiteAgendamentoRetroativo(AgendaGradeAtendimentoPacienteDTO rowObject, AjaxRequestTarget target) throws DAOException, ValidacaoException {
        Long permitirAgendamentoRetroativo = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("permitirAgendamentoRetroativo");
        if (RepositoryComponentDefault.NAO_LONG.equals(permitirAgendamentoRetroativo) &&
            DataUtil.getDataAtual().compareTo(rowObject.getAgendaGradeHorario().getHora()) > 0 &&
            DataUtil.getHora(DataUtil.getDataAtual()) >= DataUtil.getHora(rowObject.getAgendaGradeHorario().getHora())) {
            throw new ValidacaoException(bundle("msgNaoPossivelRealizarAgendamentoParaUmaDataHoraAnteriorAtual"));
        }
    }

    public ICollectionProvider getCollectionProviderHorarios() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object o) throws DAOException, ValidacaoException {
                return agendaHorarioList;
            }
        };
    }

    private boolean isHorarioAgendado(AgendaGradeHorario agendaGradeHorario) {
        for (AgendaGradeAtendimentoReservadosDTO horarioReservado : horariosReservados) {
            for (AgendaGradeHorario item : horarioReservado.getAgendaHorarioList()) {
                if (item.equals(agendaGradeHorario)) {
                    return true;
                }
            }
        }
        return false;
    }

    private void inicializarParam(SolicitacaoAgendamento solicitacaoAgendamento) {
        param.setSolicitacaoAgendamento(solicitacaoAgendamento);
        TipoProcedimento tp = LoadManager.getInstance(TipoProcedimento.class)
            .setId(solicitacaoAgendamento.getTipoProcedimento().getCodigo())
            .start().getVO();
        param.setTipoProcedimento(tp);
        mostrarDialogConfirmacao = tp.habilitaAgendamentoGrupo();
        param.setEmpresaOrigem(solicitacaoAgendamento.getEmpresa());
        if (empresaAgenda != null) {
            param.setEmpresaAgenda(empresaAgenda);
            param.setEmpresa(empresaAgenda);
        }
        param.setUsuarioCadsus(solicitacaoAgendamento.getUsuarioCadsus());
    }

    private void viewDlgAgendarAtendimento(AjaxRequestTarget target, AgendaGradeAtendimentoPacienteDTO rowObject) {
        if (dlgAgendarAtendimento == null) {
            addModal(target, dlgAgendarAtendimento = new DlgAgendarAtendimento(newModalId(), true) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, AgendaGradeAtendimentoPacienteDTO dto) throws ValidacaoException, DAOException {
                    ValidaTipoProcedimentoUtil.validaProcedimentoRegulado(param.getTipoProcedimento(), param.getUsuarioCadsus());

                    if (mostrarDialogConfirmacao) {
                        if (dlgConfirmacaoSimNao == null) {
                            addModal(target, dlgConfirmacaoSimNao = new DlgConfirmacaoSimNao<AgendaGradeAtendimentoPacienteDTO>(newModalId(), bundle("msgDesejaConfirmar")) {
                                @Override
                                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                                    reservarHorario(target, dlgConfirmacaoSimNao.getObject().getAgendaGradeHorario());
                                    agendarHorarios(target);
                                    atualizaNomePacienteReservado();
                                    tblAgendaHorarios.update(target);
                                }
                            });
                        }
                        dlgConfirmacaoSimNao.setObject(dto);
                        dlgConfirmacaoSimNao.show(target);
                    } else {
                        reservarHorario(target, dto.getAgendaGradeHorario());
                        agendarHorarios(target);
                        atualizaNomePacienteReservado();
                        tblAgendaHorarios.update(target);
                    }
                }

                @Override
                public void onContinuar(AjaxRequestTarget target, AgendaGradeAtendimentoPacienteDTO dto) throws ValidacaoException, DAOException {
                    reservarHorario(target, dto.getAgendaGradeHorario());
                    atualizaNomePacienteReservado();
                    tblAgendaHorarios.update(target);
                }
            });
        }

        if (br.com.celk.util.CollectionUtils.isNotNullEmpty(lstSolicitacaoAgendamentoExame)) {
            dlgAgendarAtendimento.setDisableContinuar(true);
        }
        dlgAgendarAtendimento.show(target, rowObject, horariosReservados, tblAgenda.getSelectedObject());
    }

    private void reservarHorario(AjaxRequestTarget target, AgendaGradeHorario agendaGradeHorario) throws ValidacaoException {
        if (br.com.celk.util.CollectionUtils.isNotNullEmpty(lstSolicitacaoAgendamentoExame)) {
            reservarMultiplosHorarios(agendaGradeHorario, calcularVagasExames());
        } else {
            boolean isAgendaAdicionadaLista = false;
            AgendaGradeAtendimentoDTO agendaSelecionada = tblAgenda.getSelectedObject();
            for (AgendaGradeAtendimentoReservadosDTO item : horariosReservados) {
                if (agendaSelecionada.equals(item.getAgendaGradeAtendimentoDTO())) {
                    item.addHorario(agendaGradeHorario);
                    isAgendaAdicionadaLista = true;
                    break;
                }
            }
            if (!isAgendaAdicionadaLista) {
                AgendaGradeAtendimentoReservadosDTO dto = new AgendaGradeAtendimentoReservadosDTO();
                dto.setAgendaGradeAtendimentoDTO(agendaSelecionada);
                dto.addHorario(agendaGradeHorario);
                horariosReservados.add(dto);
            }
        }
    }

    private Long calcularVagasExames() {
        return (long) CalcularProcedimentoPorVaga.calcular(lstSolicitacaoAgendamentoExame.size(), param.getTipoProcedimento().getProcedimentoVaga());
    }

    private void reservarMultiplosHorarios(AgendaGradeHorario agendaGradeHorario, Long numeroVagasSolicitadas) throws ValidacaoException {
        List<AgendaGradeHorario> horariosParaReservar = new ArrayList<AgendaGradeHorario>();

        AgendaGradeAtendimentoReservadosDTO dto;
        AgendaGradeAtendimentoDTO agendaSelecionada = tblAgenda.getSelectedObject();

        // Verifica se existe horários disponíveis após o selecionado, que complete o número de vagas solicitadas
        List<AgendaGradeAtendimentoPacienteDTO> agendasDisponiveisList = new ArrayList<>();

        if (numeroVagasSolicitadas > 1) {
            //Se o numero de vagas necessário for maior que uma, os horarios devem ser em sequencia
            ListIterator<AgendaGradeAtendimentoPacienteDTO> listIterator = agendaHorarioList.listIterator();
            while (listIterator.hasNext()) {
                AgendaGradeAtendimentoPacienteDTO next = listIterator.next();
                if (AgendaGradeHorario.Status.PENDENTE.value().equals(next.getAgendaGradeHorario().getStatus())) {
                    if (listIterator.hasNext()) {
                        ListIterator<AgendaGradeAtendimentoPacienteDTO> listIteratorAfter = agendaHorarioList.listIterator(listIterator.nextIndex());
                        //Já começa contando o inicial
                        int vagasDisponiveis = 1;
                        while (listIteratorAfter.hasNext()) {
                            AgendaGradeAtendimentoPacienteDTO nextAfter = listIteratorAfter.next();
                            if (AgendaGradeHorario.Status.PENDENTE.value().equals(nextAfter.getAgendaGradeHorario().getStatus())) {
                                vagasDisponiveis++;
                                if (vagasDisponiveis >= numeroVagasSolicitadas) {
                                    agendasDisponiveisList.add(next);
                                    break;
                                }
                            } else {
                                break;
                            }
                        }
                    }
                }
            }
        } else {
            for (AgendaGradeAtendimentoPacienteDTO itemDTO : agendaHorarioList) {
                if (AgendaGradeHorario.Status.PENDENTE.value().equals(itemDTO.getAgendaGradeHorario().getStatus())) {
                    agendasDisponiveisList.add(itemDTO);
                }
            }
        }

        AgendaGradeAtendimentoPacienteDTO horarioPrincipalDisponivel;
        List<AgendaGradeAtendimentoPacienteDTO> selectHorarioDisponivel = select(agendasDisponiveisList, having(on(AgendaGradeAtendimentoPacienteDTO.class).getAgendaGradeHorario(), Matchers.equalTo(agendaGradeHorario)));
        if (!br.com.celk.util.CollectionUtils.isNotNullEmpty(selectHorarioDisponivel)) {
            throw new ValidacaoException(bundle("msgNumeroVagasDisponiveisAposDataSelecionadaDeveSerMaiorIgualNumeroVagasSolicitadasX", numeroVagasSolicitadas.toString()));
        } else {
            horarioPrincipalDisponivel = selectHorarioDisponivel.get(0);
        }

        if (numeroVagasSolicitadas > 1) {
            ListIterator<AgendaGradeAtendimentoPacienteDTO> listIterator = agendaHorarioList.listIterator(agendaHorarioList.indexOf(horarioPrincipalDisponivel));
            int vagasReservadas = 0;
            while (vagasReservadas < numeroVagasSolicitadas) {
                if (listIterator.hasNext()) {
                    AgendaGradeAtendimentoPacienteDTO next = listIterator.next();
                    horariosParaReservar.add(next.getAgendaGradeHorario());
                }
                vagasReservadas++;
            }
        } else {
            horariosParaReservar.add(agendaGradeHorario);
        }

        List<AgendaGradeAtendimentoReservadosDTO> selectAgendaReservadaExistente = select(horariosReservados, having(on(AgendaGradeAtendimentoReservadosDTO.class).getAgendaGradeAtendimentoDTO(), Matchers.equalTo(agendaSelecionada)));
        if (br.com.celk.util.CollectionUtils.isNotNullEmpty(selectAgendaReservadaExistente)) {
            AgendaGradeAtendimentoReservadosDTO agendaReservadaExistente = selectAgendaReservadaExistente.get(0);
            for (AgendaGradeHorario gradeHorario : horariosParaReservar) {
                agendaReservadaExistente.addHorario(gradeHorario);
            }
        } else {
            dto = new AgendaGradeAtendimentoReservadosDTO();
            dto.setAgendaGradeAtendimentoDTO(agendaSelecionada);
            for (AgendaGradeHorario gradeHorario : horariosParaReservar) {
                dto.addHorario(gradeHorario);
            }
            horariosReservados.add(dto);
        }
    }

    private void agendarHorarios(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        AgendaGradeAtendimentoDTOParam object = getForm().getModel().getObject();
        if (CollectionUtils.isNotNullEmpty(horariosReservados)) {
            List<AgendaGradeAtendimentoDTO> agaDTOList = new ArrayList<AgendaGradeAtendimentoDTO>();
            for (AgendaGradeAtendimentoReservadosDTO item : horariosReservados) {
                for (AgendaGradeHorario horario : item.getAgendaHorarioList()) {
                    AgendaGradeAtendimentoDTO agaDTO = (AgendaGradeAtendimentoDTO) SerializationUtils.clone(item.getAgendaGradeAtendimentoDTO());
                    agaDTO.setUsuarioCadsus(object.getUsuarioCadsus());
                    agaDTO.setNomePaciente(object.getUsuarioCadsus().getNomeSocial());
                    agaDTO.setProcedimento(object.getSolicitacaoAgendamento().getProcedimento());
                    agaDTO.setSolicitacaoAgendamento(object.getSolicitacaoAgendamento());
                    agaDTO.setAgendaGradeHorario(horario);
                    agaDTO.setEmpresaAgenda(empresaAgenda);
                    if (loteSolicitacaoAgendamentoItem != null) {
                        agaDTO.setLoteSolicitacaoAgendamentoItem(loteSolicitacaoAgendamentoItem);
                    }
                    if (br.com.celk.util.CollectionUtils.isNotNullEmpty(lstSolicitacaoAgendamentoExame)) {
                        List<ExameProcedimento> extractExameProcedimentos = extract(lstSolicitacaoAgendamentoExame, on(SolicitacaoAgendamentoExame.class).getExameProcedimento());
                        agaDTO.setExameProcedimentoList(extractExameProcedimentos);
                        agaDTO.setValidarAgendamentoExistente(false);
                        agaDTO.getSolicitacaoAgendamento().setValidarExisteOutraSolicitacao(false);
                    }
                    agaDTOList.add(agaDTO);
                }
            }
            if (CollectionUtils.isNotNullEmpty(agaDTOList)) {
                List<AgendaGradeAtendimentoHorario> agahList = BOFactoryWicket.getBO(AgendamentoFacade.class).registrarAgendamentosSelecionados(
                    agaDTOList, form.getModelObject().getEmpresaOrigem(),
                    object.getUsuarioCadsus(),
                    object.getSolicitacaoAgendamento().getProcedimento(), null, null);

                viewDlgImpressao(target, agahList);
            }
        }
    }

    private void viewDlgImpressao(AjaxRequestTarget target, final List<AgendaGradeAtendimentoHorario> agahList) {
        if (dlgRecomendacoesAgenda == null) {
            addModal(target, dlgRecomendacoesAgenda = new DlgRecomendacoesAgenda(newModalId()) {
                @Override
                public DataReport onImprimir() throws ReportException {
                    return imprimir(agahList);
                }

                @Override
                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    closeDlgRedirectPage();
                }
            });
        }
        dlgRecomendacoesAgenda.show(target, agahList, true);
    }

    private DataReport imprimir(List<AgendaGradeAtendimentoHorario> agahList) throws ReportException{
        RelatorioImprimirComprovanteAgendamentoDTOParam paramRelatorio = new RelatorioImprimirComprovanteAgendamentoDTOParam();
        paramRelatorio.setAgendaGradeAtendimentoHorarioList(agahList);
        return BOFactoryWicket.getBO(AtendimentoReportFacade.class).relatorioImprimirComprovanteAgendamentoSemSolicitacao(paramRelatorio);
    }

    public void closeDlgRedirectPage() {
        Class returnClass = getClasseVoltar();
        if (returnClass != null) {
            setResponsePage(returnClass, parameters);
        } else {
            Page page;
            if (loteSolicitacaoAgendamentoItem != null) {
                page = new ConfirmacaoRecebimentoSolicitacoesPage(loteSolicitacaoAgendamentoItem.getLoteSolicitacaoAgendamento().getCodigo(), parameters);
            } else {
                if (parameters != null) {
                    page = new AgendamentoListaEsperaPage(parameters, true);
                } else {
                    page = new AgendamentoListaEsperaResumoContatoPage(paramLista, true);
                }
            }
            setResponsePage(page);
        }
    }

    private void selecionarAgendaGradeHorario(AjaxRequestTarget target, AgendaGradeAtendimentoDTO dto) throws ValidacaoException, DAOException {
        this.agendaGradeSelecionada = dto;
        btnConsultarDatasSugeridas.setEnabled(dto.getVagasDisponiveis() > 0 );
        target.add(btnConsultarDatasSugeridas);
        carregarAgendaHorarios(dto);
        if (target != null) {
            tblAgendaHorarios.populate(target);
            tblAgendaHorarios.update(target);
        } else {
            tblAgendaHorarios.populate();
        }
    }

    private void carregarAgendaHorarios(AgendaGradeAtendimentoDTO dto) throws ValidacaoException, DAOException {
        agendaHorarioList = BOFactoryWicket.getBO(AgendamentoFacade.class).consultarAgendamentoHorarioPaciente(dto.getAgendaGradeAtendimento());
        atualizaNomePacienteReservado();
    }

    private void atualizaNomePacienteReservado() {
        for (AgendaGradeAtendimentoPacienteDTO agendaGradeAtendimentoPacienteDTO : agendaHorarioList) {
            if (isHorarioAgendado(agendaGradeAtendimentoPacienteDTO.getAgendaGradeHorario())) {
                agendaGradeAtendimentoPacienteDTO.setNomePaciente(param.getUsuarioCadsus().getNomeSocial());
            }
        }
    }

    private AjaxButton getBtnConsultarDatasSugeridas() {
        return (AjaxButton) new AbstractAjaxButton("btnConsultarDatasSugeridas") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                ValidaCboProfissional.validaCboProfissional(agendaGradeSelecionada);
                AgendaGradeAtendimentoDTOParam agendaGradeAtendimento = (AgendaGradeAtendimentoDTOParam) getForm().getModel().getObject();
                DadosAgendamentoDTO dadosAgendamentoDTO = new DadosAgendamentoDTO();
                dadosAgendamentoDTO.setUnidadeAgenda(empresaAgenda);
                AquarelaPrescritorDto aquarelaPrescritorDto = new BuildAquarelaPrescritorDto(agendaGradeAtendimento.getUsuarioCadsus(), dadosAgendamentoDTO, agendaGradeSelecionada).buildAquarelaPrescritor();
                addModal(target, dlgPacienteHorarioSugerido = new DlgPacienteHorarioSugerido(newModalId(), aquarelaPrescritorDto) {});
                dlgPacienteHorarioSugerido.show(target);
            }
        }.setEnabled(false);
    }
    private Form<AgendaGradeAtendimentoDTOParam> getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel<AgendaGradeAtendimentoDTOParam>(param));
        }
        return this.form;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("agendamentoSolicitacao");
    }

    public Class getClasseVoltar() {
        return classeVoltar;
    }

    private void carregarRecomendacoes(AjaxRequestTarget target, Agenda agenda){
        if(AgendamentoHelper.exibirRecomendacoes(agenda)){
            info(target, bundle("recomendacoesX", agenda.getRecomendacoes()));
            updateNotificationPanel(target);
        } else {
            INotificationPanel findNotificationPanel = MessageUtil.findNotificationPanel(AgendamentoSolicitacaoHorarioPanel.this);
            if (findNotificationPanel != null) {
                getSession().getFeedbackMessages().clear();
                if (target != null) {
                    findNotificationPanel.updateNotificationPanel(target);
                }
            }
            getSession().getFeedbackMessages().clear();
            updateNotificationPanel(target);
        }
    }
}