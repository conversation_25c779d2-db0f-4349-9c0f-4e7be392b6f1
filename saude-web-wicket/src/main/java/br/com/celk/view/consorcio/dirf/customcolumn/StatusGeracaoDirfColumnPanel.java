package br.com.celk.view.consorcio.dirf.customcolumn;

import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.GeracaoDirfDTO;
import br.com.ksisolucoes.vo.consorcio.GeracaoDirf;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;

import java.util.Arrays;

/**
 * Created by <PERSON>
 */
public class StatusGeracaoDirfColumnPanel extends Panel {

    private GeracaoDirfDTO geracaoDirfDTO;

    private WebMarkupContainer img;

    public StatusGeracaoDirfColumnPanel(String id, GeracaoDirfDTO geracaoDirfDTO) {
        super(id);
        this.geracaoDirfDTO = geracaoDirfDTO;
        init();
    }

    private void init() {
        StatusGeracaoDirf statusGeracaoDirf = null;
        if (geracaoDirfDTO.getGeracaoDirf().getStatus() != null) {
            if (Arrays.asList(GeracaoDirf.Status.PROCESSANDO.value()).contains(geracaoDirfDTO.getGeracaoDirf().getStatus())) {
                statusGeracaoDirf = StatusGeracaoDirf.PROCESSANDO;
            } else if (Arrays.asList(GeracaoDirf.Status.ERRO.value()).contains(geracaoDirfDTO.getGeracaoDirf().getStatus())) {
                statusGeracaoDirf = StatusGeracaoDirf.ERRO;
            } else if (Arrays.asList(GeracaoDirf.Status.PROCESSADO.value()).contains(geracaoDirfDTO.getGeracaoDirf().getStatus())) {
                statusGeracaoDirf = StatusGeracaoDirf.PROCESSADO;
            } else if (Arrays.asList(GeracaoDirf.Status.CANCELADO.value()).contains(geracaoDirfDTO.getGeracaoDirf().getStatus())) {
                statusGeracaoDirf = StatusGeracaoDirf.CANCELADO;
            }
        }

        add(img = new WebMarkupContainer("img"));

        if (statusGeracaoDirf != null) {
            img.add(new AttributeModifier("class", statusGeracaoDirf.icon()));
            img.add(new AttributeModifier("title", statusGeracaoDirf.title()));
        }
    }

    public enum StatusGeracaoDirf {

        PROCESSADO("hudson green", BundleManager.getString("processado")),
        ERRO("hudson red", BundleManager.getString("erro")),
        PROCESSANDO("celk-icon loading", BundleManager.getString("processando")),
        CANCELADO("hudson grey", BundleManager.getString("cancelado")),
        ;

        private String icon;
        private String title;

        private StatusGeracaoDirf(String icon, String title) {
            this.icon = icon;
            this.title = title;
        }

        public String icon() {
            return icon;
        }

        public String title() {
            return title;
        }
    }

}