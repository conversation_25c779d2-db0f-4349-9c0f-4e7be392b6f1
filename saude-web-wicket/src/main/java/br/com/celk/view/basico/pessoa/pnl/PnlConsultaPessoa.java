package br.com.celk.view.basico.pessoa.pnl;

import br.com.celk.component.consulta.PnlConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.basico.pessoa.customize.CustomizeConsultaPessoa;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.vo.basico.Pessoa;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class PnlConsultaPessoa extends PnlConsulta<Pessoa> {

    public PnlConsultaPessoa(String id, IModel<Pessoa> model, boolean required) {
        super(id, model, required);
    }

    public PnlConsultaPessoa(String id, IModel<Pessoa> model) {
        super(id, model);
    }

    public PnlConsultaPessoa(String id, boolean required) {
        super(id, required);
    }

    public PnlConsultaPessoa(String id) {
        super(id);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {

            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaPessoa();
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("pessoas");
    }

}
