package br.com.celk.view.frota.tipooperacao;

import br.com.celk.component.checkbox.CheckBoxSimNao;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.view.frota.planocontas.pnl.PnlConsultaPlanoContas;
import br.com.ksisolucoes.vo.frota.TipoOperacao;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroTipoOperacaoPage extends CadastroPage<TipoOperacao> {

    private RequiredInputField txtDescricao;
    private InputField txtReferencia;
    
    public CadastroTipoOperacaoPage(TipoOperacao object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroTipoOperacaoPage(TipoOperacao object) {
        this(object, false);
    }

    public CadastroTipoOperacaoPage() {
        this(null);
    }

    @Override
    public void init(Form form) {
        form.add(txtReferencia = new UpperField("referencia"));
        form.add(txtDescricao = new RequiredInputField<String>("descricao"));
        form.add(new PnlConsultaPlanoContas("planoContas", true));
        form.add(new CheckBoxSimNao("flagDataGarantia"));
        form.add(new CheckBoxSimNao("flagKmAtual"));
        form.add(new CheckBoxSimNao("flagKmProximaTrocaOleo"));
        form.add(new CheckBoxSimNao("flagKmProximaRevisao"));
        form.add(new CheckBoxSimNao("flagDataProximaRevisao"));
        form.add(new CheckBoxSimNao("flagMotorista"));
        form.add(new CheckBoxSimNao("flagCombustivel"));
        form.add(new CheckBoxSimNao("flagAtualizaDadoVeiculo"));
        form.add(new CheckBoxSimNao(TipoOperacao.PROP_DATA_PROXIMO_LICENCIAMENTO));
        form.add(new CheckBoxSimNao(TipoOperacao.PROP_DATA_PROXIMO_SEGURO_OBRIGATORIO));
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtReferencia;
    }

    @Override
    public Class<TipoOperacao> getReferenceClass() {
        return TipoOperacao.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaTipoOperacaoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroTipoOperacao");
    }

}
