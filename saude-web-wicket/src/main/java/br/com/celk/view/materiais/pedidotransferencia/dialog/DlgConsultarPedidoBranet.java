package br.com.celk.view.materiais.pedidotransferencia.dialog;

import br.com.celk.component.window.Window;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

import java.io.Serializable;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgConsultarPedidoBranet<T extends Serializable> extends Window {

    private T object;
    private PnlConsultarPedidoBranet pnlConsultarPedidoBranet;

    public DlgConsultarPedidoBranet(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        setInitialWidth(600);
        setInitialHeight(60);

        setResizable(false);

        setTitle(bundle("consultarPedidosBranet"));

        setContent(pnlConsultarPedidoBranet = new PnlConsultarPedidoBranet(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, Long idSolicitacao) throws ValidacaoException, DAOException {
                close(target);
                target.getLastFocusedElementId();
                DlgConsultarPedidoBranet.this.onConfirmar(target, idSolicitacao, DlgConsultarPedidoBranet.this.object);
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target, Long idSolicitacao, T object) throws ValidacaoException, DAOException;

    public void setObject(T object) {
        this.object = object;
    }

    @Override
    public void show(AjaxRequestTarget target) {
        pnlConsultarPedidoBranet.limpar(target);
        super.show(target);
    }
}
