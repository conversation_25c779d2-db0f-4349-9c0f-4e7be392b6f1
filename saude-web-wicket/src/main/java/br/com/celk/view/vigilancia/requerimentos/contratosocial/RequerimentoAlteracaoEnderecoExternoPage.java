package br.com.celk.view.vigilancia.requerimentos.contratosocial;

import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.dialog.DlgImpressaoObjectMulti;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.RequiredUpperField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.vigilanciaendereco.RequiredPnlVigilanciaEndereco;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.view.vigilancia.RequerimentoVigilanciaFiscaisPanel;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.externo.template.base.RequerimentosVigilanciaPage;
import br.com.celk.view.vigilancia.helper.VigilanciaPageHelper;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoVigilanciaOcorrenciaPanel;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoVigilanciaSolicitantePanel;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlDadosComumRequerimentoVigilancia;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlRequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.IReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAlteracaoEndereco;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Created by Roger on 18/01/2017.
 */
public class RequerimentoAlteracaoEnderecoExternoPage extends RequerimentosVigilanciaPage {

    private Form<RequerimentoAlteracaoEnderecoDTO> form;
    private TipoSolicitacao tipoSolicitacao;
    private RequerimentoVigilancia requerimentoVigilancia;
    private RequerimentoAlteracaoEndereco requerimentoAlteracaoEndereco;
    private AutoCompleteConsultaEstabelecimento autoCompleteConsultaEstabelecimento;
    private DisabledInputField<String> txtEnderecoEstabelecimento;
    private DisabledInputField<String> txtCnpjCpfFormatado;
    private DisabledInputField<String> txtFantasia;
    private DisabledInputField<String> txtProtocolo;
    private DisabledInputField<String> txtDescricaoAtividadeEstabelecimento;
    private DisabledInputField<String> txtDescricaoRepresentanteAtual;
    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList;
    private InputField<String> txtNomeRepresentante;
    private DlgImpressaoObjectMulti<RequerimentoVigilancia> dlgConfirmacaoImpressao;
    private PnlRequerimentoVigilanciaAnexo pnlRequerimentoVigilanciaAnexo;

    private boolean enabled;
    private PnlDadosComumRequerimentoVigilancia pnlDadosComumRequerimentoVigilancia;
    private Class classReturn;

    public RequerimentoAlteracaoEnderecoExternoPage(TipoSolicitacao tipoSolicitacao, Class clazz) {
        this.tipoSolicitacao = tipoSolicitacao;
        init(true);
        this.classReturn = clazz;
    }

    public RequerimentoAlteracaoEnderecoExternoPage(RequerimentoVigilancia requerimentoVigilancia, boolean viewOnly, Class clazz) {
        this.requerimentoVigilancia = requerimentoVigilancia;
        carregarRequerimentoRepresentanteLegal(requerimentoVigilancia);
        if (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(requerimentoVigilancia.getSituacao()) || RequerimentoVigilancia.Situacao.ANALISE.value().equals(requerimentoVigilancia.getSituacao())) {
            init(viewOnly);
        } else {
            init(false);
        }
        this.classReturn = clazz;
    }

    private void init(boolean viewOnly) {
        this.enabled = viewOnly;
        if (this.requerimentoVigilancia != null) {
            info(VigilanciaHelper.mensagemSituacaoDataAlteracaoRequerimento(requerimentoVigilancia));
        }

        RequerimentoAlteracaoEnderecoDTO proxy = on(RequerimentoAlteracaoEnderecoDTO.class);

        Long codigoRequerimentoRepresentanteLegal = getForm().getModel().getObject().getRequerimentoAlteracaoEndereco().getCodigo();

        getForm().add(autoCompleteConsultaEstabelecimento = new AutoCompleteConsultaEstabelecimento(path(proxy.getRequerimentoAlteracaoEndereco().getRequerimentoVigilancia().getEstabelecimento()), true));
        autoCompleteConsultaEstabelecimento.setEnabled(enabled && codigoRequerimentoRepresentanteLegal == null);
        autoCompleteConsultaEstabelecimento.setLabel(new Model(bundle("estabelecimento")));
        autoCompleteConsultaEstabelecimento.setExibirNaoAutorizados(true);
        autoCompleteConsultaEstabelecimento.setExibirProvisorios(true);
        try {
            autoCompleteConsultaEstabelecimento.setFiltrarUsuarioLogado(RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.VIGILANCIA_SANITARIA).getParametro("restricaoEstabelecimentoRequerimentoExterno")));
        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        getForm().add(txtEnderecoEstabelecimento = new DisabledInputField<String>(path(proxy.getRequerimentoAlteracaoEndereco().getRequerimentoVigilancia().getEnderecoFormatado())));
        getForm().add(txtCnpjCpfFormatado = new DisabledInputField<>(path(proxy.getRequerimentoAlteracaoEndereco().getRequerimentoVigilancia().getEstabelecimento().getCnpjCpfFormatado())));
        getForm().add(txtFantasia = new DisabledInputField<>(path(proxy.getRequerimentoAlteracaoEndereco().getRequerimentoVigilancia().getEstabelecimento().getFantasia())));
        getForm().add(txtProtocolo = new DisabledInputField<>(path(proxy.getRequerimentoAlteracaoEndereco().getRequerimentoVigilancia().getProtocoloFormatado())));
        getForm().add(txtDescricaoAtividadeEstabelecimento = new DisabledInputField<>(path(proxy.getRequerimentoAlteracaoEndereco().getRequerimentoVigilancia().getEstabelecimento().getAtividadeEstabelecimento().getDescricao())));
        getForm().add(txtDescricaoRepresentanteAtual = new DisabledInputField<>(path(proxy.getRequerimentoAlteracaoEndereco().getRequerimentoVigilancia().getEstabelecimento().getRepresentanteNome())));

        getForm().add(new RequerimentoVigilanciaSolicitantePanel("solicitantePanel", form.getModel().getObject().getRequerimentoAlteracaoEndereco().getRequerimentoVigilancia(), enabled));

        {// Endereco Estabelecimento
            getForm().add(new RequiredPnlVigilanciaEndereco(path(proxy.getRequerimentoAlteracaoEndereco().getVigilanciaEndereco())).setLabel(new Model<String>(bundle("endereco"))).setEnabled(enabled));
            getForm().add(new RequiredUpperField(path(proxy.getRequerimentoAlteracaoEndereco().getNumeroLogradouro())).setLabel(new Model<String>(bundle("numero"))).setEnabled(enabled));
            getForm().add(new InputField<String>(path(proxy.getRequerimentoAlteracaoEndereco().getComplemento())).setEnabled(enabled));
            getForm().add(new InputField<String>(path(proxy.getRequerimentoAlteracaoEndereco().getPontoReferencia())).setLabel(new Model<String>(bundle("pontoReferencia"))).setEnabled(enabled));
        }

        DadosComumRequerimentoVigilanciaDTOParam dadosComumParam = VigilanciaPageHelper
                .createDadosComumRequerimentoVigilanciaDTOParam(getForm().getModel().getObject().getRequerimentoAlteracaoEndereco().getRequerimentoVigilancia());
        dadosComumParam.setDesabilitaFiscais(true);
        getForm().add(pnlDadosComumRequerimentoVigilancia = new PnlDadosComumRequerimentoVigilancia("dadosComumRequerimentoVigilancia", dadosComumParam, enabled));
        pnlDadosComumRequerimentoVigilancia.getContainerSetorVigilancia().setVisible(false);

        {//Inicio Anexos
            PnlRequerimentoVigilanciaAnexoDTO dtoPnlAnexo = new PnlRequerimentoVigilanciaAnexoDTO();
            dtoPnlAnexo.setRequerimentoVigilanciaAnexoDTOList(requerimentoVigilanciaAnexoDTOList);
            dtoPnlAnexo.setTipoSolicitacao(tipoSolicitacao);
            getForm().add(pnlRequerimentoVigilanciaAnexo = new PnlRequerimentoVigilanciaAnexo(dtoPnlAnexo, enabled));
            pnlRequerimentoVigilanciaAnexo.setOutputMarkupId(true);
        }

        getForm().add(new RequerimentoVigilanciaFiscaisPanel("fiscaisRequerimento", requerimentoVigilancia));

        getForm().add(new VoltarButton("btnVoltar"));
        getForm().add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }).setEnabled(enabled));

        consultaListenerEstab();
        removeListnerEstab();

        getForm().add(new RequerimentoVigilanciaOcorrenciaPanel("ocorrencias", getForm().getModel().getObject().getRequerimentoAlteracaoEndereco().getRequerimentoVigilancia().getCodigo(), true).setVisible(!enabled));

        add(getForm());
    }

    private void consultaListenerEstab() {
        autoCompleteConsultaEstabelecimento.add(new ConsultaListener<Estabelecimento>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Estabelecimento object) {
                carregarAtividade(object);
                atualizarDadosEstabelecimento(target, object);
                pnlRequerimentoVigilanciaAnexo.filtrarAtividadeEstabelecimento(target, object);
            }
        });
    }

    private void removeListnerEstab() {
        autoCompleteConsultaEstabelecimento.add(new RemoveListener<Estabelecimento>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Estabelecimento object) {
                getForm().getModel().getObject().getRequerimentoAlteracaoEndereco().getRequerimentoVigilancia().setVigilanciaEndereco(null);
                atualizarDadosEstabelecimento(target, object);
                pnlRequerimentoVigilanciaAnexo.filtrarAtividadeEstabelecimento(target, null);
            }
        });
    }

    private Form<RequerimentoAlteracaoEnderecoDTO> getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel(new RequerimentoAlteracaoEnderecoDTO()));
            if (requerimentoAlteracaoEndereco != null) {
                form.getModel().getObject().setRequerimentoAlteracaoEndereco(requerimentoAlteracaoEndereco);
            } else {
                form.getModel().getObject().setRequerimentoAlteracaoEndereco(new RequerimentoAlteracaoEndereco());
                form.getModel().getObject().getRequerimentoAlteracaoEndereco().setRequerimentoVigilancia(new RequerimentoVigilancia());
            }
        }
        return form;
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
//        if (getForm().getModel().getObject().getRequerimentoAlteracaoEndereco().getRepresentanteTelefone() == null
//                && getForm().getModel().getObject().getRequerimentoAlteracaoEndereco().getRepresentanteCelular() == null) {
//            throw new ValidacaoException(bundle("msgInformeTelefoneCelularNovoRepresentanteLegal"));
//        }
        if(getForm().getModel().getObject().getRequerimentoAlteracaoEndereco().getRequerimentoVigilancia().getCpfSolicitante() == null
                && form.getModel().getObject().getRequerimentoAlteracaoEndereco().getRequerimentoVigilancia().getRgSolicitante() == null){
            throw new ValidacaoException(bundle("msgInformeCpfEOURgSolicitante"));
        }
        getForm().getModel().getObject().getRequerimentoAlteracaoEndereco().getRequerimentoVigilancia().setOrigem(RequerimentoVigilancia.Origem.EXTERNO.value());
        getForm().getModel().getObject().setRequerimentoVigilanciaAnexoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoDTOList());
        getForm().getModel().getObject().setRequerimentoVigilanciaAnexoExcluidoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoExcluidoDTOList());
        getForm().getModel().getObject().setTipoSolicitacao(tipoSolicitacao);
        getForm().getModel().getObject().getRequerimentoAlteracaoEndereco().getRequerimentoVigilancia().setTipoSolicitacao(tipoSolicitacao);
        atualizarDadosComuns();
        RequerimentoVigilancia rv = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarRequerimentoAlteracaoEndereco(getForm().getModel().getObject());

        String mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocolo");
        final ConfiguracaoVigilancia configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
            mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocoloTermoSolicitacaoServico");
        }

        addModal(target, dlgConfirmacaoImpressao = new DlgImpressaoObjectMulti<RequerimentoVigilancia>(newModalId(), mensagemImpressao) {
            @Override
            public List<IReport> getDataReports(RequerimentoVigilancia object) throws ReportException {
                RelatorioRequerimentoVigilanciaComprovanteDTOParam param = new RelatorioRequerimentoVigilanciaComprovanteDTOParam();
                param.setRequerimentoVigilancia(object);
                param.setOrigem(RequerimentoVigilancia.Origem.EXTERNO.value());

                QRCodeGenerateDTOParam qrCodeParam = new QRCodeGenerateDTOParam(VigilanciaHelper.getURLQRCodePageRequerimento(), object.getChaveQRcode());
                param.setQRCodeParam(qrCodeParam);

                DataReport comprovanteRequerimento = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRequerimentoVigilanciaComprovante(param);

                List<IReport> lstDataReport = new ArrayList();
                lstDataReport.add(comprovanteRequerimento);

                if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
                    DataReport termoSolicitacaoServico = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoTermoSolicitacaoServico(object.getCodigo());
                    lstDataReport.add(termoSolicitacaoServico);
                }

                return lstDataReport;
            }

            @Override
            public void onFechar(AjaxRequestTarget target, RequerimentoVigilancia object) throws ValidacaoException, DAOException {
                try {
                    Page pageReturn = (Page) classReturn.newInstance();
                    getSession().getFeedbackMessages().info(pageReturn, BundleManager.getString("registro_salvo_sucesso_protocolo_x", object.getProtocoloFormatado()));
                    setResponsePage(pageReturn);
                } catch (InstantiationException | IllegalAccessException e) {
                    Loggable.log.error(e.getMessage(), e);
                }
            }
        });

        dlgConfirmacaoImpressao.show(target, rv);
    }

    private void atualizarDadosEstabelecimento(AjaxRequestTarget target, Estabelecimento estabelecimento) {
        target.add(txtEnderecoEstabelecimento);
        target.add(txtCnpjCpfFormatado);
        target.add(txtFantasia);
        target.add(txtProtocolo);
        target.add(txtDescricaoAtividadeEstabelecimento);
        target.add(txtDescricaoRepresentanteAtual);
    }

    private void carregarRequerimentoRepresentanteLegal(RequerimentoVigilancia requerimentoVigilancia) {
        if (requerimentoVigilancia != null) {
            tipoSolicitacao = requerimentoVigilancia.getTipoSolicitacao();

            requerimentoAlteracaoEndereco = LoadManager.getInstance(RequerimentoAlteracaoEndereco.class)
                    .addProperties(new HQLProperties(RequerimentoAlteracaoEndereco.class).getProperties())
                    .addProperties(new HQLProperties(RequerimentoVigilancia.class, RequerimentoAlteracaoEndereco.PROP_REQUERIMENTO_VIGILANCIA).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, VOUtils.montarPath(RequerimentoAlteracaoEndereco.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_ESTABELECIMENTO)).getProperties())
                    .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO)).getProperties())
                    .addProperties(new HQLProperties(Cidade.class, VOUtils.montarPath(RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE)).getProperties())
                    .addProperties(new HQLProperties(Estado.class, VOUtils.montarPath(RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE, Cidade.PROP_ESTADO)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoAlteracaoEndereco.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                    .start().getVO();
            requerimentoAlteracaoEndereco.setRequerimentoVigilancia(requerimentoVigilancia);

            if (requerimentoAlteracaoEndereco != null) {
                carregarAtividade(requerimentoAlteracaoEndereco.getRequerimentoVigilancia().getEstabelecimento());
            }
            carregarAnexos(requerimentoVigilancia);
        }
    }

    private void carregarAnexos(RequerimentoVigilancia rv) {
        List<RequerimentoVigilanciaAnexo> list = VigilanciaHelper.carregarAnexosVigilancia(rv);

        requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
        RequerimentoVigilanciaAnexoDTO anexoDTO;
        for (RequerimentoVigilanciaAnexo rva : list) {
            anexoDTO = new RequerimentoVigilanciaAnexoDTO();
            anexoDTO.setDescricaoAnexo(rva.getDescricao());
            anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
            anexoDTO.setRequerimentoVigilanciaAnexo(rva);

            requerimentoVigilanciaAnexoDTOList.add(anexoDTO);
        }
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("alteracaoEndereco");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(autoCompleteConsultaEstabelecimento.getTxtDescricao().getTextField())));
    }

    private void carregarAtividade(Estabelecimento estabelecimento) {
        if (getForm().getModel().getObject().getRequerimentoAlteracaoEndereco().getRequerimentoVigilancia().getEstabelecimento().getCnpjCpf() == null || getForm().getModel().getObject().getRequerimentoAlteracaoEndereco().getRequerimentoVigilancia().getEstabelecimento().getCnpjCpf().isEmpty()) {
            Estabelecimento estabPrincipal = LoadManager.getInstance(Estabelecimento.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(Estabelecimento.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, estabelecimento.getEstabelecimentoPrincipal().getCodigo()))
                    .start().getVO();

            getForm().getModel().getObject().getRequerimentoAlteracaoEndereco().getRequerimentoVigilancia().getEstabelecimento().setCnpjCpf(estabPrincipal.getCnpjCpfFormatado());
        }
        if (pnlDadosComumRequerimentoVigilancia != null) {
            pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList().clear();
        }
        EstabelecimentoAtividade estabelecimentoAtividade = LoadManager.getInstance(EstabelecimentoAtividade.class)
                .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_ESTABELECIMENTO, QueryCustom.QueryCustomParameter.IGUAL, estabelecimento))
                .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_FLAG_PRINCIPAL, QueryCustom.QueryCustomParameter.IGUAL, RepositoryComponentDefault.SIM_LONG))
                .start().getVO();
        if (estabelecimentoAtividade != null && estabelecimentoAtividade.getAtividadeEstabelecimento() != null) {
            getForm().getModel().getObject().getRequerimentoAlteracaoEndereco().getRequerimentoVigilancia().getEstabelecimento().setAtividadeEstabelecimento(estabelecimentoAtividade.getAtividadeEstabelecimento());
        }

        if (estabelecimento.getVigilanciaEndereco() != null && estabelecimento.getVigilanciaEndereco().getCodigo() != null) {
            VigilanciaEndereco ve = LoadManager.getInstance(VigilanciaEndereco.class).setId(estabelecimento.getVigilanciaEndereco().getCodigo()).start().getVO();
            getForm().getModel().getObject().getRequerimentoAlteracaoEndereco().getRequerimentoVigilancia().setVigilanciaEndereco(ve);
        }
    }

    @Override
    public Permissions getAction() {
        return Permissions.DECLARACAO_CARTORIO;
    }
    
    private void atualizarDadosComuns(){
        getForm().getModel().getObject().getRequerimentoAlteracaoEndereco().getRequerimentoVigilancia().setObservacaoRequerimento(pnlDadosComumRequerimentoVigilancia.getParam().getObservacaoRequerimento());
        getForm().getModel().getObject().setEloRequerimentoVigilanciaSetorVigilanciaList(pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList());
        getForm().getModel().getObject().setEloRequerimentoVigilanciaSetorVigilanciaExcluirList(pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaExcluirList());
    }
}
