package br.com.celk.view.consorcio.licitacao.customize;

import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.vo.consorcio.Licitacao;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaLicitacao extends CustomizeConsultaAdapter{

    @Override
    public Class getClassConsulta() {
        return Licitacao.class;
    }

    @Override
    public String[] getProperties() {
        return new HQLProperties(Licitacao.class).getProperties();
    }

}
