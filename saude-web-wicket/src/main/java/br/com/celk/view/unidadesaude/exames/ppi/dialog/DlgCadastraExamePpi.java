package br.com.celk.view.unidadesaude.exames.ppi.dialog;

import br.com.celk.agendamento.ppi.dto.PpiOcorrenciaDTO;
import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.agendamento.ppi.dto.PpiTipoExameDTO;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

import java.util.List;

public abstract class DlgCadastraExamePpi extends Window {

    private final PpiTipoExameDTO ppiTipoExameDTO;
    private PnlCadastraExamePpi pnlCadastraExamePpi;
    private boolean somenteLeitura = false;
    private List<PpiOcorrenciaDTO> transferencias;
    private boolean permiteTransferencia;

    public DlgCadastraExamePpi(String id, boolean somenteLeitura, PpiTipoExameDTO ppiTipoExameDTO, List<PpiOcorrenciaDTO> transferencias, boolean permiteTransferencia) {
        super(id);
        this.somenteLeitura = somenteLeitura;
        this.ppiTipoExameDTO = ppiTipoExameDTO;
        this.transferencias = transferencias;
        this.permiteTransferencia = permiteTransferencia;
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("examesProcedimentos");
            }
        });

        setContent (pnlCadastraExamePpi = new PnlCadastraExamePpi(getContentId(), somenteLeitura, ppiTipoExameDTO, transferencias, permiteTransferencia) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, PpiTipoExameDTO dto, List<PpiOcorrenciaDTO> transferencias) {
                close(target);
                DlgCadastraExamePpi.this.onConfirmar(target, dto, transferencias);
            }

            @Override
            public void onFechar(AjaxRequestTarget target, PpiTipoExameDTO dto, List<PpiOcorrenciaDTO> transferencias) {
                close(target);
                verificaEdicaoPendente(target);
                DlgCadastraExamePpi.this.onFechar(target, dto, transferencias);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                pnlCadastraExamePpi.onFechar(target, pnlCadastraExamePpi.getPpiTipoExameDTO(), pnlCadastraExamePpi.getTransferencias());
                return true;
            }
        });

        setResizable(true);

        setInitialWidth(1000);
        setInitialHeight(600);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, PpiTipoExameDTO dto, List<PpiOcorrenciaDTO> transferencias);

    public abstract void onFechar(AjaxRequestTarget target, PpiTipoExameDTO dto, List<PpiOcorrenciaDTO> transferencias);

    public void show(AjaxRequestTarget target, PpiTipoExameDTO dto) {
        show(target);
        pnlCadastraExamePpi.setDTO(target, dto);
    }
}