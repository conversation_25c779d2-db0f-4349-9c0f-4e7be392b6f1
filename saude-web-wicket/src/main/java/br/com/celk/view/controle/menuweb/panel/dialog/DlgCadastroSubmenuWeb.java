package br.com.celk.view.controle.menuweb.panel.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.web.MenuWeb;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgCadastroSubmenuWeb extends Window{
    
    private PnlCadastroSubmenuWeb pnlCadastroSubmenuWeb;
    
    public DlgCadastroSubmenuWeb(String id){
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("cadastroSubmenu");
            }
        });
                
        setInitialWidth(650);
        setInitialHeight(140);
        setResizable(true);
        
        setContent(pnlCadastroSubmenuWeb = new PnlCadastroSubmenuWeb(getContentId()) {

            @Override
            public void onSalvar(AjaxRequestTarget target, MenuWeb submenu) throws ValidacaoException, DAOException {
                close(target);
                DlgCadastroSubmenuWeb.this.onSalvar(target, submenu);
            }
            
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }
    
    public abstract void onSalvar(AjaxRequestTarget target, MenuWeb submenu) throws ValidacaoException, DAOException;
    
    public void showDlg(AjaxRequestTarget target, MenuWeb modulo, MenuWeb menu){
        show(target);
        pnlCadastroSubmenuWeb.limpar(target, modulo, menu);
    }    
}