package br.com.celk.view.patrimonio.bempatrimonio.dlg;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.vo.patrimonio.BemPatrimonio;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgAlterarBemPatrimonio extends Window {

    private PnlAlterarBemPatrimonio pnlAlterarBemPatrimonio;

    public DlgAlterarBemPatrimonio(String id) {
        super(id);
        init();
    }

    private void init() {

        setInitialWidth(600);
        setInitialHeight(90);

        setTitle(BundleManager.getString("alterarCodigoBemPatrimonio"));

        setContent(pnlAlterarBemPatrimonio = new PnlAlterarBemPatrimonio(getContentId()) {

            @Override
            public void onFechar(AjaxRequestTarget target) {
                close(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, BemPatrimonio bemPatrimonio, Long novaReferencia) {
                close(target);
                DlgAlterarBemPatrimonio.this.onConfirmar(target, bemPatrimonio, novaReferencia);
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target, BemPatrimonio bemPatrimonio, Long novaReferencia);

    public void show(AjaxRequestTarget target, BemPatrimonio bemPatrimonio) {
        super.show(target);
        pnlAlterarBemPatrimonio.setDados(target, bemPatrimonio);
    }

}
