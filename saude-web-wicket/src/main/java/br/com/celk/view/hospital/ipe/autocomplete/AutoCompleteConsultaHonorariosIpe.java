package br.com.celk.view.hospital.ipe.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.hospital.autocomplete.honorarioipe.RestricaoContainerHonorarioIpe;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.QueryConsultaHonorariosIpeDTOParam;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.bo.prontuario.procedimento.interfaces.dto.QueryConsultaProcedimentoDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.hospital.ipe.HonorariosIpe;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaHonorariosIpe extends AutoCompleteConsulta<HonorariosIpe> {
    
    private Long tipoHonorario;

    public AutoCompleteConsultaHonorariosIpe(String id) {
        super(id);
    }

    public AutoCompleteConsultaHonorariosIpe(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaHonorariosIpe(String id, IModel<HonorariosIpe> model) {
        super(id, model);
    }

    public AutoCompleteConsultaHonorariosIpe(String id, IModel<HonorariosIpe> model, boolean required) {
        super(id, model, required);
    }


    @Override
    public String getTitle() {
        return BundleManager.getString("honorarioIpe");
    }

        @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {

            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(HonorariosIpe.class);
                
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), VOUtils.montarPath(HonorariosIpe.PROP_PROCEDIMENTO, Procedimento.PROP_REFERENCIA)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(HonorariosIpe.PROP_PROCEDIMENTO, Procedimento.PROP_DESCRICAO)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerHonorarioIpe(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<HonorariosIpe, QueryConsultaHonorariosIpeDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaHonorariosIpeDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(HospitalFacade.class).queryConsultaHonorariosIpe(dataPaging);
                    }

                    @Override
                    public QueryConsultaHonorariosIpeDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaHonorariosIpeDTOParam param = new QueryConsultaHonorariosIpeDTOParam();
                        param.setKeyword(searchCriteria);
                        return param;
                    }
                    
                    @Override
                    public void customizeParam(QueryConsultaHonorariosIpeDTOParam param) {
                        param.setTipoHonorario(tipoHonorario);
                    }
                    
                    @Override
                    public SortParam getDefaultSort() {
                        return new SortParam(VOUtils.montarPath(HonorariosIpe.PROP_PROCEDIMENTO, Procedimento.PROP_DESCRICAO), true);
                    }
                };
            }

            @Override
            public Class getReferenceClass() {
                return HonorariosIpe.class;
            }

        };
    }

    public void setTipoHonorario(Long tipoHonorario) {
        this.tipoHonorario = tipoHonorario;
    }
}
