package br.com.celk.view.agenda.cadastro;

import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.table.CustomColorTableRow;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.TableColorEnum;
import br.com.ksisolucoes.agendamento.dto.AgendaGradeAtendimentoHorariosDTO;
import br.com.ksisolucoes.agendamento.dto.CadastroAgendaGradeAtendimentoHorarioDTO;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class CadastroAgendaTableColor extends Table{
    private LinkedHashMap<Date, TableColorEnum> map;

    public CadastroAgendaTableColor(String id, List<IColumn> columns, ICollectionProvider collectionProvider) {
        super(id, columns, collectionProvider);
        map = new LinkedHashMap<Date, TableColorEnum>();
    }

    @Override
    public void update(AjaxRequestTarget target) {
        map = new LinkedHashMap<Date, TableColorEnum>();
        super.update(target); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    protected Item newRowItem(String id, int index, IModel model) {
        return new CustomColorTableRow(id, index, model) {
            @Override
            public TableColorEnum getColor() {
                AgendaGradeAtendimentoHorariosDTO aga = (AgendaGradeAtendimentoHorariosDTO) getRowObject();
                Date data = aga.getAgendaGradeAtendimento().getAgendaGrade().getData();
                if(map.isEmpty()){
                    map.put(data, TableColorEnum.POSITIVA);
                    return TableColorEnum.POSITIVA;
                }
                if(map.containsKey(data)){
                    return map.get(data);
                }
                TableColorEnum classe = (TableColorEnum) map.values().toArray()[map.values().size()-1];
                if(classe.equals(TableColorEnum.POSITIVA)){
                    classe = TableColorEnum.NEGATIVA;
                }else{
                    classe = TableColorEnum.POSITIVA;
                }

                map.put(data, classe);
                return classe;
            }
        };
    }

}