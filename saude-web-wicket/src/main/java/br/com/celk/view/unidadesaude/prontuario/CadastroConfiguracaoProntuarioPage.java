package br.com.celk.view.unidadesaude.prontuario;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.atendimento.prontuario.NodesConsultaProntuarioRef;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.table.SelectionRow;
import br.com.celk.component.table.SelectionTable;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.util.AnnotationUtils;
import br.com.celk.view.atendimento.consultaprontuario.nodes.annotations.ConsultaProntuarioNode;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NodoConsultaProntuarioDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.NodoConsultaProntuario;
import br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.odlabs.wiquery.ui.sortable.SortableBehavior;


/**
 *
 * <AUTHOR>
 */
@Private

public class CadastroConfiguracaoProntuarioPage extends CadastroPage<GrupoAtendimentoCbo>{
    
    private WebMarkupContainer containerNodos;    
    private DropDown<NodesConsultaProntuarioRef> dropDownNodo;
    private SelectionTable<NodoConsultaProntuarioDTO> tblNodos;
    private List<NodoConsultaProntuarioDTO> dtoList = new ArrayList<NodoConsultaProntuarioDTO>();
    private NodesConsultaProntuarioRef nodo;
    private Map<String, NodesConsultaProntuarioRef> mapNodos = new HashMap<String,NodesConsultaProntuarioRef>();

    public CadastroConfiguracaoProntuarioPage(GrupoAtendimentoCbo grupoAtendimentoCbo) {
        super(grupoAtendimentoCbo);
        relacionarNodosDisponiveis(); // atualiza com registros selecionados no dropbox
        carregarNodos();//carrega registros na tabela
    }

    @Override
    public void init(Form<GrupoAtendimentoCbo> form) {
        GrupoAtendimentoCbo proxy = on(GrupoAtendimentoCbo.class);
        form.add(new DisabledInputField(path(proxy.getDescricao())));
        
        containerNodos = new WebMarkupContainer("containerNodos", new CompoundPropertyModel(this));
        containerNodos.add(dropDownNodo = DropDownUtil.getEnumDropDown("nodo", NodesConsultaProntuarioRef.values(), ""));
        containerNodos.add(new AbstractAjaxButton("btnAdicionarNodo") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarNodo(target);
            }
        });
        containerNodos.add(tblNodos = new SelectionTable("tblNodos", getColumnNodos(), getCollectionProviderNodos()));
        
        
         SortableBehavior sortableBehavior = new SortableBehavior();
         sortableBehavior.setUpdateEvent(new SortableBehavior.AjaxUpdateCallback() {
            @Override
            public void update(AjaxRequestTarget art, Component cmpnt, int i, Component sortedComponent) {
                SelectionRow<NodoConsultaProntuarioDTO> sr = (SelectionRow<NodoConsultaProntuarioDTO>) sortedComponent;
                dtoList.remove(dtoList.indexOf(sr.getModelObject()));
                dtoList.add(i,sr.getModelObject());
            }
        });
        sortableBehavior.setConnectWith(".connectedSortable");
        sortableBehavior.setAxis(SortableBehavior.AxisEnum.Y);
        tblNodos.getBody().add(sortableBehavior);
        
        tblNodos.getBody().add(new AttributeModifier("class", "connectedSortable"));        
        
        form.add(containerNodos);
        
        tblNodos.populate();
    }
    
    private void adicionarNodo(AjaxRequestTarget target) throws ValidacaoException {
        if(nodo == null){
            throw new ValidacaoException(BundleManager.getString("informeNo"));
        }
        for (NodoConsultaProntuarioDTO nodoConsultaProntuarioDTO : dtoList) {
            if(nodoConsultaProntuarioDTO.getNodo().equals(nodo)){
                throw new ValidacaoException(BundleManager.getString("itemJaAdicionado"));
            }
        }
        
        String className = null;
        for (Map.Entry<String, NodesConsultaProntuarioRef> entry : mapNodos.entrySet()) {
            if(entry.getValue().equals(nodo)){
                className = entry.getKey();
                break;
            }
        }
        
        NodoConsultaProntuarioDTO dto = new NodoConsultaProntuarioDTO();
        dto.setClassName(className);
        dto.setNodo(nodo);
        
        dtoList.add(0, dto);
        
        dropDownNodo.limpar(target);
        target.add(tblNodos);
    }
    
    private ICollectionProvider getCollectionProviderNodos() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return dtoList;
            }
        };
    }

    @Override
    public Class<GrupoAtendimentoCbo> getReferenceClass() {
        return GrupoAtendimentoCbo.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaConfiguracaoProntuarioPage.class;
    }
    
    private List<IColumn> getColumnNodos() {
        List<IColumn> columns = new ArrayList<IColumn>();
        NodoConsultaProntuarioDTO proxy = on(NodoConsultaProntuarioDTO.class);
        
        columns.add(getCustomColumnNode());
        columns.add(createColumn(bundle("descricao"), proxy.getNodo()));
        return columns;
    }

    private IColumn getCustomColumnNode() {
        return new MultipleActionCustomColumn<NodoConsultaProntuarioDTO>() {
            @Override
            public void customizeColumn(final NodoConsultaProntuarioDTO rowObject) {
                addAction(ActionType.REMOVER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        dtoList.remove(rowObject);
                        target.add(tblNodos);
                    }
                });
            };
        };
    }

    @Override
    public String getTituloPrograma() {
        return bundle("cadastroConfiguracaoConsultaProntuario");
    }
    
    private void relacionarNodosDisponiveis() {
        List<Class> classList = AnnotationUtils.findClasses(ConsultaProntuarioNode.class, "br/com/celk/view/atendimento/consultaprontuario/nodes");
        for (Class clazz : classList) {
            ConsultaProntuarioNode pn = (ConsultaProntuarioNode) clazz.getAnnotation(ConsultaProntuarioNode.class);
            mapNodos.put(clazz.getName(),pn.value());
        }
    }
    
    private void carregarNodos(){
        List<NodoConsultaProntuario> nodos = LoadManager.getInstance(NodoConsultaProntuario.class)
            .addParameter(new QueryCustom.QueryCustomParameter(NodoConsultaProntuario.PROP_GRUPO_ATENDIMENTO_CBO, getForm().getModel().getObject()))
            .addSorter(new QueryCustom.QueryCustomSorter(NodoConsultaProntuario.PROP_ORDEM))
            .start().getList();
        for (NodoConsultaProntuario nodoConsultaProntuario : nodos) {
            NodoConsultaProntuarioDTO dto = new NodoConsultaProntuarioDTO();
            dto.setClassName(nodoConsultaProntuario.getClasseNodo());
            dto.setNodo(mapNodos.get(nodoConsultaProntuario.getClasseNodo()));
            
            dtoList.add(dto);
        }
        
    }
    
    @Override
    public Object salvar(GrupoAtendimentoCbo object) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(AtendimentoFacade.class).configurarConsultaProntuario(object, dtoList);
        return null;
    }
    
}
