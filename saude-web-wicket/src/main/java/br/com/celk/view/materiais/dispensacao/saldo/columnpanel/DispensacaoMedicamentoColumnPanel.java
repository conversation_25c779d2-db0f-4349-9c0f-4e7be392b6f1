package br.com.celk.view.materiais.dispensacao.saldo.columnpanel;

import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public abstract class DispensacaoMedicamentoColumnPanel extends Panel {

    public AbstractAjaxLink btnSelecionar;
    public AbstractAjaxLink btnRemover;

    private DispensacaoMedicamento dispensacaoMedicamento;
    
    public DispensacaoMedicamentoColumnPanel(String id, DispensacaoMedicamento dispensacaoMedicamento) {
        super(id);
        this.dispensacaoMedicamento = dispensacaoMedicamento;
        init();
    }

    private void init() {
        add(btnSelecionar = new AbstractAjaxLink("btnSelecionar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onSelecionar(target, dispensacaoMedicamento);
            }
        });
        
        btnSelecionar.add(new AttributeModifier("title", BundleManager.getString("selecionar")));
    }
    
    public abstract void onSelecionar(AjaxRequestTarget target, DispensacaoMedicamento dispensacaoMedicamento) throws ValidacaoException, DAOException;
    
}
