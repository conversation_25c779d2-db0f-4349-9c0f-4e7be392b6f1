package br.com.celk.view.unidadesaude.processos.regulacaosolicitacao.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.JustificativaPriorizacao;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgConfirmarEnvioFilaRegulacaoSolicitacaoAgendamento extends Window {

    private PnlConfirmarEnvioFilaRegulacaoSolicitacaoAgendamento pnlEnvioFila;

    public DlgConfirmarEnvioFilaRegulacaoSolicitacaoAgendamento(String id) {
        super(id);
        init();
    }

    private void init() {
        setInitialHeight(500);
        setInitialWidth(650);

        setResizable(true);

        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("confirmacaoEnvioFila");
            }
        });

        setContent(pnlEnvioFila = new PnlConfirmarEnvioFilaRegulacaoSolicitacaoAgendamento(getContentId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, Long codigoSolicitacao, Long prioridade, String observacaoAutorizador, JustificativaPriorizacao justificativaPriorizacao) throws DAOException, ValidacaoException {
                DlgConfirmarEnvioFilaRegulacaoSolicitacaoAgendamento.this.onConfirmar(target, codigoSolicitacao, prioridade, observacaoAutorizador, justificativaPriorizacao);
                close(target);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                close(target);
            }
        });
    }

    public void limpar(AjaxRequestTarget target) {
        pnlEnvioFila.limpar(target);
    }

    public void show(AjaxRequestTarget target, SolicitacaoAgendamento solicitacaoAgendamento) {
        show(target);
        limpar(target);
        pnlEnvioFila.setSolicitacaoAgendamento(target, solicitacaoAgendamento);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, Long codigoSolicitacao, Long prioridade, String observacaoAutorizador, JustificativaPriorizacao justificativaPriorizacao) throws DAOException, ValidacaoException;
    
}
