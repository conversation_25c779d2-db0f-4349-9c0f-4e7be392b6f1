package br.com.celk.view.hospital.financeiro.adiantamento.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.hospital.financeiro.Adiantamentos;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgMotivoCancelamentoAdiantamento extends Window{
    
    private PnlMotivoCancelamentoAdiantamento pnlMotivoCancelamentoAdiantamento;
    
    public DlgMotivoCancelamentoAdiantamento(String id){
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("motivoCancelamento");
            }
        });
                
        setInitialWidth(500);
        setInitialHeight(210);
        setResizable(true);
        
        setContent(pnlMotivoCancelamentoAdiantamento = new PnlMotivoCancelamentoAdiantamento(getContentId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, Adiantamentos adiantamento) throws ValidacaoException, DAOException {
                close(target);
                DlgMotivoCancelamentoAdiantamento.this.onConfirmar(target, adiantamento);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target, Adiantamentos adiantamento) throws ValidacaoException, DAOException;
    
    public void show(AjaxRequestTarget target, Adiantamentos adiantamento){
        show(target);
        pnlMotivoCancelamentoAdiantamento.setObject(target, adiantamento);
    }    
}