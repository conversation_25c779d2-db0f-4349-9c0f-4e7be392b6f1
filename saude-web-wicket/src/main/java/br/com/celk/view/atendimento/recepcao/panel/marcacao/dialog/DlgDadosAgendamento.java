package br.com.celk.view.atendimento.recepcao.panel.marcacao.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.util.ImagemAvatarHelper;
import br.com.ksisolucoes.agendamento.dto.DadosAgendamentoDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ConsultaUsuarioCadsusDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgDadosAgendamento extends Window {

    private PnlDadosAgendamento pnlDadosAgendamento;

    public DlgDadosAgendamento(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("dados_agendamento");
            }
        });

        setInitialWidth(800);
        setInitialHeight(270);
        setResizable(true);

        setContent(pnlDadosAgendamento = new PnlDadosAgendamento(getContentId()) {

            @Override
            public void onAvancar(AjaxRequestTarget target, ConsultaUsuarioCadsusDTO dto, DadosAgendamentoDTO dadosAgendamentoDTO) throws ValidacaoException, DAOException {
                close(target);
                DlgDadosAgendamento.this.onAvancar(target, dto, dadosAgendamentoDTO);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }

    public abstract void onAvancar(AjaxRequestTarget target, ConsultaUsuarioCadsusDTO dto, DadosAgendamentoDTO dadosAgendamentoDTO) throws ValidacaoException, DAOException;

    public void show(AjaxRequestTarget target, ConsultaUsuarioCadsusDTO dto) throws DAOException {
        pnlDadosAgendamento.setDTO(dto, target);
        pnlDadosAgendamento.setResourceImage(target, ImagemAvatarHelper.carregarAvatarResource(dto.getUsuarioCadsus()));
        show(target);
    }
}
