package br.com.celk.view.atendimento.prontuario.dialog;

import br.com.celk.component.appletbiometria.IAppletAction;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.link.AjaxPageNewTabLink;
import br.com.celk.component.passwordfield.PasswordField;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.resources.Resources;
import br.com.celk.service.jms.JMSUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.consultaprontuario.ProntuarioPage;
import br.com.celk.view.atendimento.painel.PainelDTO;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.ProfissionalFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.AtendimentoHelper;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.ActionAtendimentoWebDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtendimentoWebDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.temp.v2.TempConverterV2;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.atendimento.painel.Painel;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.basico.SalaUnidade;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.ProfissionalCargaHoraria;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.EmpresaNaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.image.Image;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.LoadableDetachableModel;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.resource.CssResourceReference;
import org.apache.wicket.request.resource.IResource;
import org.apache.wicket.request.resource.ResourceReference;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 * <AUTHOR>
 */
public abstract class PnlAcoesAtendimento extends Panel implements IAppletAction {

    private static final String CSS_FILE = "PnlAcoesAtendimento.css";
    private Form form;
    private ActionAtendimentoWebDTO dto = new ActionAtendimentoWebDTO();
    private WebMarkupContainer containerUsuarioNaoIdentificavel;
    private WebMarkupContainer containerSenha;
    private WebMarkupContainer containerSala;
    private WebMarkupContainer containerEquipeProfissional;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private DisabledInputField disabledInputFieldUsuario;
    private DlgCancelarAtendimento dlgCancelarAtendimento;
    private DlgConfirmacaoSimNao dlgConfirmacaoSimNao;
    private DlgConfirmacaoSimNao dlgConfirmacaoSimNaoPainel;
    private DropDown<SalaUnidade> dropDownSalaUnidade;
    private DropDown<EquipeProfissional> dropDownEquipeProfissional;
    private EmpresaNaturezaProcuraTipoAtendimento enpta;
    private AbstractAjaxLink linkAtender;
    private AbstractAjaxLink linkEvoluir;
    private AbstractAjaxLink linkCancelarAtendimento;
    private AbstractAjaxLink linkLiberarAtendimento;
    private AbstractAjaxLink linkPainel;
    private AbstractAjaxLink linkReverterSituacao;
    private AbstractAjaxLink linkVoltar;
    private AjaxPageNewTabLink linkConsultarProntuario;
    private PasswordField passwordField;
    private InputField txtNome;
    private String senha;
    private SalaUnidade salaUnidade;
    private EquipeProfissional equipeProfissional;
    private String nomeUsuarioCadsus;
    private Profissional profissional;
    private Usuario usuarioLogado;
    private Usuario usuario;
    private boolean profissionalBiometria;
    private boolean existePainelCadastrado = false;
    private Image imagem;
    private boolean reverterSituacaoAgendamento;
    private boolean isValidarUsuarioTemporario;
    private String paramChamarUtilizando;
    private DropDown<TabelaCbo> dropDownCboProfissional;
    private TabelaCbo tabelaCbo;

    public PnlAcoesAtendimento(String id) {
        super(id);
        init();
    }

    private void init() {
        form = new Form("form", new CompoundPropertyModel(this));
        setOutputMarkupId(true);

        isValidarUsuarioTemporario = AtendimentoHelper.isValidarUsuarioTemporario(getUsuarioLogado().getFlagUsuarioTemporario(), getUsuarioLogado().getFlagIdentificavel());

        form.add(txtNome = new DisabledInputField("nomeUsuarioCadsus"));
        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("profissional"));
        form.add(dropDownCboProfissional =  new DropDown<>("dropDownCboProfissional", new PropertyModel<>(this, "tabelaCbo")));
        dropDownCboProfissional.setEnabled(false);
        dropDownCboProfissional.setOutputMarkupId(true);
        dropDownCboProfissional.add(new Tooltip().setMessage("Campo disponivel para profissionais que possuem mais de um CBO e desejam escolher qual utilizar"));

        autoCompleteConsultaProfissional.add(new ConsultaListener<Profissional>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Profissional object) {
                populateDropDownCbo(target);
            }
        });
        autoCompleteConsultaProfissional.add(new RemoveListener<Profissional>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Profissional object) {
                dropDownCboProfissional.limpar(target);
                dropDownCboProfissional.removeAllChoices();
                dropDownCboProfissional.setEnabled(false);
                target.add(dropDownCboProfissional);
            }
        });

        containerUsuarioNaoIdentificavel = new WebMarkupContainer("containerUsuarioNaoIdentificavel");
        containerUsuarioNaoIdentificavel.setOutputMarkupId(true);
        containerUsuarioNaoIdentificavel.setVisible(false);
        form.add(containerUsuarioNaoIdentificavel);

        autoCompleteConsultaProfissional.add(new ConsultaListener<Profissional>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Profissional profissional) {
                if (usuarioNaoIdentificavel()) {
                    buscarUsuario(target);
                }
                carregarEquipeProfissional(target);
            }
        });
        autoCompleteConsultaProfissional.add(new RemoveListener<Profissional>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Profissional object) {
                if (usuarioNaoIdentificavel()) {
                    limparDadosProfissional(target);
                }
                carregarEquipeProfissional(target);
            }
        });
        dropDownCboProfissional.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {
                if (dropDownCboProfissional.getComponentValue() != null) {
                    tabelaCbo = dropDownCboProfissional.getComponentValue();
                }
            }
        });

        if (usuarioNaoIdentificavel()) {
            containerUsuarioNaoIdentificavel.add(disabledInputFieldUsuario = new DisabledInputField("usuario.login", new PropertyModel<String>(this, "usuario.login")));
            containerUsuarioNaoIdentificavel.add(containerSenha = new WebMarkupContainer("containerSenha"));
            containerSenha.add(passwordField = new PasswordField("senha", new PropertyModel<String>(this, "senha")));
            passwordField.setResetPassword(true);
            passwordField.addAjaxUpdateValue();
            containerSenha.setVisible(!isValidarUsuarioTemporario);

            containerUsuarioNaoIdentificavel.setVisible(true);
        }

        form.add(containerSala = new WebMarkupContainer("containerSala"));
        containerSala.setOutputMarkupId(true);
        containerSala.add(dropDownSalaUnidade = new DropDown("salaUnidade"));
        dropDownSalaUnidade.addAjaxUpdateValue();
        dropDownSalaUnidade.setOutputMarkupId(true);

        containerEquipeProfissional = new WebMarkupContainer("containerEquipe");
        containerEquipeProfissional.setOutputMarkupPlaceholderTag(true);
        dropDownEquipeProfissional = new DropDown("equipeProfissional");
        dropDownEquipeProfissional.addAjaxUpdateValue();
        dropDownEquipeProfissional.setOutputMarkupId(true);
        carregarEquipeProfissional(null);

        dropDownEquipeProfissional.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                carregarSession();
            }
        });

        containerEquipeProfissional.add(dropDownEquipeProfissional);
        form.add(containerEquipeProfissional);

        // Atender
        form.add(linkAtender = new AbstractAjaxLink("linkAtender") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                target.appendJavaScript(JScript.removeAutoCompleteDrop());

                if (dropDownCboProfissional.isEnabled() && dropDownCboProfissional.getModelObject() == null) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_necesessario_informar_cbo"));
                }

                if (validarAtenderEvoluir(target, false)) {
                    dto.setUsuarioNaoIdentificavel(usuarioNaoIdentificavel());
                    dto.setProfissional(profissional);
                    if (tabelaCbo != null) {
                        dto.getAtendimentoWebDTO().getAtendimento().setTabelaCbo(tabelaCbo);
                        if (getSession().getAttribute("tabelaCbo") == null) {
                            getSession().setAttribute("tabelaCbo", tabelaCbo);
                            getSession().setAttribute("profissional", profissional);
                        }
                        dropDownCboProfissional.removeAllChoices();
                        dropDownCboProfissional.limpar(target);
                    }
                    onAtender(target, dto);
                }
            }
        });

        linkAtender.add(new Image("linkAtender", new LoadableDetachableModel<ResourceReference>() {
            @Override
            protected ResourceReference load() {
                return Resources.Images.CHECKMARK.resourceReference();
            }
        }));
        linkAtender.add(new AttributeModifier("title", BundleManager.getString("atender")));
        linkAtender.add(new Label("labelAtender", BundleManager.getString("atender")));

        // Evoluir
        form.add(linkEvoluir = new AbstractAjaxLink("linkEvoluir") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
                if (validarAtenderEvoluir(target, true)) {
                    ActionAtendimentoWebDTO actionAtendimentoWebDTO = (ActionAtendimentoWebDTO) SerializationUtils.clone(dto);

                    actionAtendimentoWebDTO.getAtendimentoWebDTO().getAtendimento().setParalelo(RepositoryComponentDefault.SIM_LONG);
                    actionAtendimentoWebDTO.setUsuarioNaoIdentificavel(usuarioNaoIdentificavel());
                    actionAtendimentoWebDTO.setProfissional(profissional);

                    onEvoluir(target, actionAtendimentoWebDTO);
                }
            }
        });

        linkEvoluir.add(new Image("linkEvoluir", new LoadableDetachableModel<ResourceReference>() {
            @Override
            protected ResourceReference load() {
                return Resources.Images.CLIPBOARD.resourceReference();
            }
        }));
        linkEvoluir.add(new AttributeModifier("title", BundleManager.getString("evoluir")));
        linkEvoluir.add(new Label("labelEvoluir", BundleManager.getString("evoluir")));

        // Cancelar Atendimento
        form.add(linkCancelarAtendimento = new AbstractAjaxLink("linkCancelarAtendimento") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
                initDlgCancelarAtendimento(target);
            }
        });

        AbstractAjaxButton btnBiometria = new AbstractAjaxButton("btnBiometria") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                target.appendJavaScript("bioApplet.consultarUsuario();");
            }
        };
        form.add(btnBiometria);

//        String parametroBiometria = null;
//        try {
//            parametroBiometria = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("usaBiometria");
//        } catch (DAOException ex) {
//            Loggable.log.error(ex.getMessage(), ex);
//        }
        //O sistema não permite pesquisa por biometria
//        if (!RepositoryComponentDefault.SIM.equals(parametroBiometria)) {
        btnBiometria.setVisible(false);
//        }

        linkCancelarAtendimento.add(new Image("linkCancelarAtendimento", new LoadableDetachableModel<ResourceReference>() {
            @Override
            protected ResourceReference load() {
                return Resources.Images.DELETE.resourceReference();
            }
        }));
        linkCancelarAtendimento.add(new AttributeModifier("title", BundleManager.getString("cancelarAtendimento")));
        linkCancelarAtendimento.add(new Label("labelCancelarAtendimento", BundleManager.getString("cancelarAtendimento")));

        // Liberar Atendimento
        form.add(linkLiberarAtendimento = new AbstractAjaxLink("linkLiberarAtendimento") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
                initDlgLiberarAtendimento(target);
            }
        });

        linkLiberarAtendimento.add(new Image("linkLiberarAtendimento", new LoadableDetachableModel<ResourceReference>() {
            @Override
            protected ResourceReference load() {
                return Resources.Images.UNLOCK.resourceReference();
            }
        }));
        linkLiberarAtendimento.add(new AttributeModifier("title", BundleManager.getString("liberarAtendimento")));
        linkLiberarAtendimento.add(new Label("labelLiberarAtendimento", BundleManager.getString("liberarAtendimento")));

        // Painel
        form.add(linkPainel = new AbstractAjaxLink("linkPainel") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
                validarChamarPainel(target);
            }
        });

        linkPainel.add(new Image("linkPainel", new LoadableDetachableModel<ResourceReference>() {
            @Override
            protected ResourceReference load() {
                return Resources.Images.PANEL.resourceReference();
            }
        }));
        linkPainel.add(new AttributeModifier("title", BundleManager.getString("painel")));
        linkPainel.add(new Label("labelPainel", BundleManager.getString("painel")));

        // Reverter Situação
        form.add(linkReverterSituacao = new AbstractAjaxLink("linkReverterSituacao") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
                onReverterAgendamento(target, dto);
            }
        });

        linkReverterSituacao.add(new Image("linkReverterSituacao", new LoadableDetachableModel<ResourceReference>() {
            @Override
            protected ResourceReference load() {
                return Resources.Images.UNDO.resourceReference();
            }
        }));
        linkReverterSituacao.add(new AttributeModifier("title", BundleManager.getString("reverterSituacao")));
        linkReverterSituacao.add(new Label("labelReverterSituacao", BundleManager.getString("reverterSituacao")));

        // Voltar
        form.add(linkVoltar = new AbstractAjaxLink("linkVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
                onVoltar(target, dto);
            }
        });

        linkVoltar.add(new Image("linkVoltar", new LoadableDetachableModel<ResourceReference>() {
            @Override
            protected ResourceReference load() {
                return Resources.Images.ARROW_LEFT.resourceReference();
            }
        }));
        linkVoltar.add(new AttributeModifier("title", BundleManager.getString("voltar")));
        linkVoltar.add(new Label("labelVoltar", BundleManager.getString("voltar")));

        // Consultar Prontuario
        form.add(linkConsultarProntuario = new AjaxPageNewTabLink("linkConsultarProntuario") {
            @Override
            public Page getPageToOpen() {
                return new ProntuarioPage(dto, usuarioLogado);
            }

            @Override
            public boolean isVisible() {
                return new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().getUsuario(), ProntuarioPage.class.getName());
            }
        });

        linkConsultarProntuario.add(new Image("linkConsultarProntuario", new LoadableDetachableModel<ResourceReference>() {
            @Override
            protected ResourceReference load() {
                return Resources.Images.CONSULTA_PRONTUARIO.resourceReference();
            }
        }));
        linkConsultarProntuario.add(new AttributeModifier("title", BundleManager.getString("consultarProntuario")));
        linkConsultarProntuario.add(new Label("labelConsultarProntuario", BundleManager.getString("consultarProntuario")));

        form.add(imagem = new Image("imgAvatar", ""));
        imagem.setOutputMarkupId(true);

        add(form);
    }

    private void populateDropDownCbo(AjaxRequestTarget target) {
        dropDownCboProfissional.removeAllChoices();
        dropDownCboProfissional.setModelObject(null);
        dropDownCboProfissional.setEnabled(false);

        tabelaCbo = null;

        // Verifica se o profissional é o mesmo da sessão e já há um cbo definido
        if (profissional.equals(getSession().getAttribute("profissional")) && getSession().getAttribute("tabelaCbo") != null) {
            tabelaCbo = ((TabelaCbo) getSession().getAttribute("tabelaCbo"));
            dropDownCboProfissional.addChoice(tabelaCbo, tabelaCbo.getDescricaoFormatado());
            dropDownCboProfissional.setModelObject(tabelaCbo);
            dropDownCboProfissional.setEnabled(false);
            target.add(dropDownCboProfissional);
            return;
        } else {
            // Se mudou de profissional, limpa os atributos salvos
            getSession().removeAttribute("profissional");
            getSession().removeAttribute("tabelaCbo");
        }

        if (profissional != null) {
            List<ProfissionalCargaHoraria> lstProfissionais = LoadManager.getInstance(ProfissionalCargaHoraria.class)
                    .addProperty(VOUtils.montarPath(ProfissionalCargaHoraria.PROP_TABELA_CBO, TabelaCbo.PROP_CBO))
                    .addProperty(VOUtils.montarPath(ProfissionalCargaHoraria.PROP_TABELA_CBO, TabelaCbo.PROP_DESCRICAO))
                    .addParameter(new QueryCustom.QueryCustomParameter(ProfissionalCargaHoraria.PROP_PROFISSIONAL, profissional))
                    .addParameter(new QueryCustom.QueryCustomParameter(ProfissionalCargaHoraria.PROP_EMPRESA, ApplicationSession.get().getSessaoAplicacao().getEmpresa()))
                    .start().getList();

            if (!lstProfissionais.isEmpty() && lstProfissionais.size() >= 2) {
                dropDownCboProfissional.setEnabled(true);
                dropDownCboProfissional.addChoice(null, ""); // Placeholder
                for (ProfissionalCargaHoraria profissionalCargaHoraria : lstProfissionais) {
                    dropDownCboProfissional.addChoice(
                            profissionalCargaHoraria.getTabelaCbo(),
                            profissionalCargaHoraria.getTabelaCbo().getDescricaoFormatado()
                    );
                }
            }
        }

        target.add(dropDownCboProfissional);
        dropDownCboProfissional.addAjaxUpdateValue();
    }


    private void carregarSession() {
        EquipeProfissional equipeProfissionalSession = (EquipeProfissional) getSession().getAttribute("equipeProfissional");
        if (equipeProfissional == null || equipeProfissionalSession == null || !equipeProfissionalSession.getCodigo().equals(equipeProfissional.getCodigo())) {
            getSession().setAttribute("equipeProfissional", equipeProfissional);
        }
    }

    public abstract void onAtender(AjaxRequestTarget target, ActionAtendimentoWebDTO dto) throws ValidacaoException, DAOException;

    public abstract void onEvoluir(AjaxRequestTarget target, ActionAtendimentoWebDTO dto) throws ValidacaoException, DAOException;

    public abstract void onCancelarAtendimento(AjaxRequestTarget target, ActionAtendimentoWebDTO dto) throws ValidacaoException, DAOException;

    public abstract void onLiberarAtendimento(AjaxRequestTarget target, ActionAtendimentoWebDTO dto) throws ValidacaoException, DAOException;

    public abstract void onVoltar(AjaxRequestTarget target, ActionAtendimentoWebDTO dto) throws ValidacaoException, DAOException;

    public abstract void onReverterAgendamento(AjaxRequestTarget target, ActionAtendimentoWebDTO dto) throws ValidacaoException, DAOException;

    public abstract void onConsultarProntuario(AjaxRequestTarget target, ActionAtendimentoWebDTO dto) throws ValidacaoException, DAOException;

    public void limparDadosProfissional(AjaxRequestTarget target) {
        autoCompleteConsultaProfissional.limpar(target);
        disabledInputFieldUsuario.limpar(target);
        passwordField.limpar(target);
        target.appendJavaScript(JScript.removeAutoCompleteDrop());
        target.focusComponent(autoCompleteConsultaProfissional.getTxtDescricao().getTextField());
        profissionalBiometria = false;
        dropDownEquipeProfissional.removeAllChoices();
        dropDownEquipeProfissional.limpar(target);
        containerEquipeProfissional.setVisible(false);
        target.add(dropDownEquipeProfissional);
    }

    public void setDTO(AjaxRequestTarget target, ActionAtendimentoWebDTO dto) {
        this.dto = dto;
        this.usuarioLogado = null;
        txtNome.limpar(target);
        profissionalBiometria = false;
        autoCompleteConsultaProfissional.limpar(target);
        if (usuarioNaoIdentificavel()) {
            disabledInputFieldUsuario.limpar(target);
            passwordField.limpar(target);
        }

        if (dto.getAtendimentoWebDTO().getAtendimento().getUsuarioCadsus() != null) {
            this.nomeUsuarioCadsus = dto.getAtendimentoWebDTO().getAtendimento().getUsuarioCadsus().getNomeSocial();
        }
        target.add(txtNome);

        autoCompleteConsultaProfissional.setCodigoEmpresa(dto.getAtendimentoWebDTO().getAtendimento().getEmpresa().getCodigo());
        autoCompleteConsultaProfissional.setPeriodoEmpresa(true);
        autoCompleteConsultaProfissional.setEnabled(true);

        if (!usuarioNaoIdentificavel() && getUsuarioLogado().getProfissional() != null) {
            autoCompleteConsultaProfissional.setComponentValue(getUsuarioLogado().getProfissional());
            autoCompleteConsultaProfissional.setEnabled(false);
        }

        target.add(autoCompleteConsultaProfissional);
        target.focusComponent(autoCompleteConsultaProfissional.getTxtDescricao().getTextField());

        enableLinks(target);
    }

    private void enableLinks(AjaxRequestTarget target) {
        linkAtender.setVisible(false);
        linkEvoluir.setVisible(false);
        linkLiberarAtendimento.setVisible(false);
        linkConsultarProntuario.setVisible(false);

        Atendimento atendimento = dto.getAtendimentoWebDTO().getAtendimento();

        if (CollectionUtils.isNotNullEmpty(dto.getPermissionsList())) {
            boolean reclassificacao = dto.isReclassificacao();
            if (dto.getPermissionsList().contains(Permissions.ATENDER.value())) {
                linkAtender.setVisible(true);
            }
            if (dto.getPermissionsList().contains(Permissions.CONSULTAR.value())) {
                linkConsultarProntuario.setVisible(true);
            }
            if (dto.getPermissionsList().contains(Permissions.EVOLUIR.value()) && !Atendimento.STATUS_FINALIZADO.equals(atendimento.getStatus()) && !RepositoryComponentDefault.SIM_LONG.equals(atendimento.getCorrecao()) && !reclassificacao) {
                linkEvoluir.setVisible(true);
            }
            if (dto.getPermissionsList().contains(Permissions.RETORNO.value()) && Atendimento.STATUS_ATENDENDO.equals(atendimento.getStatus()) && !reclassificacao) {
                linkLiberarAtendimento.setVisible(true);
            }

            linkCancelarAtendimento.setVisible(!reclassificacao);
        }

        existePainelCadastrado = existePainelCadastrado();
        if (!existePainelCadastrado) {
            linkPainel.setVisible(false);
            containerSala.setVisible(false);
        } else {
            linkPainel.setVisible(true);
            containerSala.setVisible(true);
            carregarSalaUnidade(target);
        }

        carregarEquipeProfissional(target);

        linkReverterSituacao.setVisible(false);
        if (dto.getAtendimentoWebDTO() != null && dto.getAtendimentoWebDTO().getAtendimento() != null) {
            boolean existeAgendamento = LoadManager.getInstance(AgendaGradeAtendimentoHorario.class).addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_ATENDIMENTO_GERADO, dto.getAtendimentoWebDTO().getAtendimento())).exists();

            if (existeAgendamento) {
                linkReverterSituacao.setVisible(true);
            }
        }

        target.add(linkAtender);
        target.add(linkEvoluir);
        target.add(linkLiberarAtendimento);
        target.add(linkPainel);
        target.add(linkReverterSituacao);
        target.add(linkConsultarProntuario);
    }

    private boolean existePainelCadastrado() {
        enpta = LoadManager.getInstance(EmpresaNaturezaProcuraTipoAtendimento.class).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EmpresaNaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO, NaturezaProcuraTipoAtendimento.PROP_CODIGO), dto.getAtendimentoWebDTO().getAtendimento().getNaturezaProcuraTipoAtendimento().getCodigo())).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EmpresaNaturezaProcuraTipoAtendimento.PROP_EMPRESA, Empresa.PROP_CODIGO), dto.getAtendimentoWebDTO().getAtendimento().getEmpresa().getCodigo())).addProperties(VOUtils.montarPath(EmpresaNaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO, NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_DESCRICAO)).addProperties(VOUtils.montarPath(EmpresaNaturezaProcuraTipoAtendimento.PROP_PAINEL, Painel.PROP_CHAVE)).start().getVO();
        return enpta != null && enpta.getPainel() != null && enpta.getPainel().getChave() != null;
    }

    private DropDown carregarSalaUnidade(AjaxRequestTarget target) {
        dropDownSalaUnidade.removeAllChoices(target);
        dropDownSalaUnidade.limpar(target);

        List<SalaUnidade> salaList = LoadManager.getInstance(SalaUnidade.class).addProperties(new HQLProperties(SalaUnidade.class).getProperties()).addProperties(new HQLProperties(Empresa.class, SalaUnidade.PROP_EMPRESA).getProperties()).addParameter(new QueryCustom.QueryCustomParameter(SalaUnidade.PROP_EMPRESA, dto.getAtendimentoWebDTO().getAtendimento().getEmpresa())).addSorter(new QueryCustom.QueryCustomSorter(SalaUnidade.PROP_DESCRICAO)).start().getList();

        dropDownSalaUnidade.addChoice(null, "");
        for (SalaUnidade sala : salaList) {
            dropDownSalaUnidade.addChoice(sala, sala.getDescricao());
        }

        SalaUnidade salaUnidadeAtendimento = (SalaUnidade) getSession().getAttribute("salaUnidadeAtendimento");
        dropDownSalaUnidade.setComponentValue(salaUnidadeAtendimento);

        target.add(dropDownSalaUnidade);

        return dropDownSalaUnidade;
    }

    private void carregarEquipeProfissional(AjaxRequestTarget target) {
        dropDownEquipeProfissional.removeAllChoices();
        containerEquipeProfissional.setVisible(false);
        if (profissional != null) {
            List<EquipeProfissional> equipeProfissionalList = LoadManager.getInstance(EquipeProfissional.class).addProperty(EquipeProfissional.PROP_CODIGO).addProperty(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_CODIGO)).addProperty(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_REFERENCIA)).addProperty(VOUtils.montarPath(EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_CODIGO)).addProperty(VOUtils.montarPath(EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_NOME)).addProperty(VOUtils.montarPath(EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_CODIGO_CNS)).addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_STATUS, EquipeProfissional.STATUS_ATIVO)).addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_PROFISSIONAL, profissional)).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA), dto.getAtendimentoWebDTO().getAtendimento().getEmpresa())).start().getList();

            if (!equipeProfissionalList.isEmpty()) {
                if (equipeProfissionalList.size() > 1) {
                    containerEquipeProfissional.setVisible(true);
                    dropDownEquipeProfissional.addChoice(null, "");
                } else {
                    getSession().setAttribute("equipeProfissional", equipeProfissionalList.get(0));
                }

                for (EquipeProfissional equipeProfiss : equipeProfissionalList) {
                    dropDownEquipeProfissional.addChoice(equipeProfiss, equipeProfiss.getEquipe().getReferencia());
                }
            } else {
                getSession().removeAttribute("equipeProfissional");
            }

            EquipeProfissional equipeProfissionalSession = (EquipeProfissional) getSession().getAttribute("equipeProfissional");
            dropDownEquipeProfissional.setComponentValue(equipeProfissionalSession);
            getSession().setAttribute("equipeProfissional", equipeProfissionalSession);
        }

        if (target != null) {
            target.add(dropDownEquipeProfissional);
            target.add(containerEquipeProfissional);
        }
    }

    public boolean validarAtenderEvoluir(AjaxRequestTarget target, boolean apenasEvolucao) {
        try {
            Atendimento atendimentoAtualizado = AtendimentoHelper.getSituacaoAndProfissionalAtendimento(dto.getAtendimentoWebDTO().getAtendimento());
            if (profissional == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_profissional"));
            }

            if (containerEquipeProfissional.isVisible() && !dropDownEquipeProfissional.getValues().isEmpty() && equipeProfissional == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_equipe_profissional"));
            }

            if (apenasEvolucao) {
                if (Atendimento.STATUS_ATENDENDO.equals(atendimentoAtualizado.getStatus())
                        && (atendimentoAtualizado.getProfissional().getCodigo().equals(profissional.getCodigo()))) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_paciente_ja_esta_atendimento_profissional_X", atendimentoAtualizado.getProfissional().getDescricaoFormatado()));
                }
            } else {
                if (Atendimento.STATUS_ATENDENDO.equals(atendimentoAtualizado.getStatus())
                        && !(atendimentoAtualizado.getProfissional().getCodigo().equals(profissional.getCodigo()))) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_paciente_ja_esta_atendimento_profissional_X", atendimentoAtualizado.getProfissional().getDescricaoFormatado()));
                }
            }

            if (dto.getAtendimentoWebDTO().getAtendimento().getUsuarioCadsus() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_paciente_sem_cadastro"));
            }

            if (usuarioNaoIdentificavel()) {
                if (usuario == null || usuario.getLogin() == null) {
                    throw new ValidacaoException(BundleManager.getString("msgNaoFoiEncontradoUsuarioProfissionalInformado"));
                }
                if (!isValidarUsuarioTemporario && !profissionalBiometria && senha == null) {
                    throw new ValidacaoException(BundleManager.getString("informe_senha"));
                }

                if (!isValidarUsuarioTemporario && !profissionalBiometria && !usuario.getSenha().equals(Util.criptografarSenha(senha))) {
                    throw new ValidacaoException(BundleManager.getString("msgSenhaInvalida"));
                } else if (Usuario.STATUS_INATIVO.equals(usuario.getStatus())) {
                    throw new ValidacaoException(BundleManager.getString("msgUsuarioProfissionalInativo"));
                }
            }
        } catch (ValidacaoException e) {
            MessageUtil.modalWarn(target, this, e);
            return false;
        }

        return true;
    }

    public void validarChamarPainel(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (profissional == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_profissional"));
        }
        if (salaUnidade == null) {
            throw new ValidacaoException(bundle("selecioneSala"));
        }

        registrarSalaUnidadeSessao();

        AtendimentoWebDTO atendimentoWebDTO = BOFactoryWicket.getBO(AtendimentoFacade.class).registrarChamadaAtendimento(dto.getAtendimentoWebDTO().getAtendimento(), profissional, true, true, false);
        if (atendimentoWebDTO.isPacienteJaChamado()) {
            if (dlgConfirmacaoSimNaoPainel == null) {
                WindowUtil.addModal(target, this, dlgConfirmacaoSimNaoPainel = new DlgConfirmacaoSimNao(WindowUtil.newModalId(this)) {
                    @Override
                    public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        chamarNovamentePainel();
                    }
                });
            }

            dlgConfirmacaoSimNaoPainel.setMessage(target, bundle("pacienteXFoiChamadoPorXEmXDesejaChamarNovamente", dto.getAtendimentoWebDTO().getAtendimento().getUsuarioCadsus().getDescricaoSocialFormatado(), atendimentoWebDTO.getAtendimento().getProfissionalChamada().getNome(), Data.formatarDataHora(atendimentoWebDTO.getAtendimento().getDataChamada())));
            dlgConfirmacaoSimNaoPainel.show(target);
        } else {
            enviarNotificacaoPainel(atendimentoWebDTO);
        }
    }

    private void registrarSalaUnidadeSessao() {
        SalaUnidade salaUnidadeSessao = (SalaUnidade) getSession().getAttribute("salaUnidadeAtendimento");
        if (salaUnidadeSessao == null || !salaUnidadeSessao.getCodigo().equals(salaUnidade.getCodigo())) {
            getSession().setAttribute("salaUnidadeAtendimento", salaUnidade);
        }
    }

    private void enviarNotificacaoPainel(AtendimentoWebDTO atendimentoWebDTO) {
        dto.setAtendimentoWebDTO(atendimentoWebDTO);

        if (atendimentoWebDTO.getAtendimento().getUsuarioCadsus() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(atendimentoWebDTO.getAtendimento().getUsuarioCadsus().getUtilizaNomeSocial())) {
                this.nomeUsuarioCadsus = atendimentoWebDTO.getAtendimento().getUsuarioCadsus().getApelido();
            } else {
                this.nomeUsuarioCadsus = atendimentoWebDTO.getAtendimento().getUsuarioCadsus().getNome();
            }
        }


        try {
            paramChamarUtilizando = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("chamarUtilizando");
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage(), e);
        }

        if(Atendimento.ParamGemAtedimento.SENHA.descricao().equals(paramChamarUtilizando)) {
            if (validaSenhaATendimento(atendimentoWebDTO.getAtendimento())) {
                nomeUsuarioCadsus = "SENHA " + atendimentoWebDTO.getAtendimento().getSenhaFormatada();
            } else {
                if (validaSenhaAtendimentoPrincipal(atendimentoWebDTO.getAtendimento())) {
                    nomeUsuarioCadsus = "SENHA " + atendimentoWebDTO.getAtendimento().getAtendimentoPrincipal().getSenhaFormatada();
                }
            }
        }

        PainelDTO painelDTO = new PainelDTO();
        painelDTO.setPaciente(nomeUsuarioCadsus);
        painelDTO.setProfissional(Atendimento.ParamGemAtedimento.SENHA.descricao().equals(paramChamarUtilizando) ?"":profissional.getNome());
        painelDTO.setTipoAtendimento(Atendimento.ParamGemAtedimento.SENHA.descricao().equals(paramChamarUtilizando) ?"":enpta.getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricao());
        painelDTO.setChave(enpta.getPainel().getChave());
        painelDTO.setDescricaoSala(salaUnidade.getDescricao());
        painelDTO.setClassificacaoRisco(atendimentoWebDTO.getAtendimento().getClassificacaoRisco());
        painelDTO.setDataChamado(DataUtil.getDataAtual());

        String mensagem = new TempConverterV2().toJson(painelDTO);

        JMSUtil.enviarNotificacao(mensagem, JMSUtil.DestinoJms.PAINEL, enpta.getPainel().getChave());
    }

    private boolean validaSenhaATendimento(Atendimento atendimento) {
        return atendimento.getTipoSenha() != null && atendimento.getTipoSenha() !=null;
    }

    private boolean validaSenhaAtendimentoPrincipal(Atendimento atendimento) {
        return atendimento.getAtendimentoPrincipal() != null && atendimento.getAtendimentoPrincipal().getTipoSenha() != null;
    }

    public void chamarNovamentePainel() throws ValidacaoException, DAOException {
        AtendimentoWebDTO atendimentoWebDTO = BOFactoryWicket.getBO(AtendimentoFacade.class).registrarChamadaAtendimento(dto.getAtendimentoWebDTO().getAtendimento(), profissional, false, true, false);
        enviarNotificacaoPainel(atendimentoWebDTO);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(CssHeaderItem.forReference(new CssResourceReference(this.getClass(), CSS_FILE)));
    }

    private Usuario getUsuarioLogado() {
        if (usuarioLogado == null) {
            usuarioLogado = ApplicationSession.get().getSession().getUsuario();
        }
        return usuarioLogado;
    }

    private boolean usuarioNaoIdentificavel() {
        return RepositoryComponentDefault.NAO.equals(getUsuarioLogado().getFlagIdentificavel());
    }

    private void initDlgCancelarAtendimento(AjaxRequestTarget target) {
        if (dlgCancelarAtendimento == null) {
            WindowUtil.addModal(target, this, dlgCancelarAtendimento = new DlgCancelarAtendimento(WindowUtil.newModalId(this)) {
                @Override
                public void onOk(AjaxRequestTarget target, Atendimento atendimento) throws ValidacaoException, DAOException {
                    cancelarAtendimento(target, atendimento);
                }
            });
        }
        dlgCancelarAtendimento.show(target, dto.getAtendimentoWebDTO().getAtendimento());
    }

    private void cancelarAtendimento(AjaxRequestTarget target, Atendimento atendimento) throws ValidacaoException, DAOException {
        dto.getAtendimentoWebDTO().setAtendimento(atendimento);
        onCancelarAtendimento(target, dto);
    }

    private void initDlgLiberarAtendimento(AjaxRequestTarget target) {
        if (dlgConfirmacaoSimNao == null) {
            WindowUtil.addModal(target, this, dlgConfirmacaoSimNao = new DlgConfirmacaoSimNao(WindowUtil.newModalId(this), bundle("seProfissionaoXAindaEstiverProntuarioAbertoPerderaInformacoes", (dto.getAtendimentoWebDTO().getAtendimento().getProfissional() == null ? "" : dto.getAtendimentoWebDTO().getAtendimento().getProfissional().getNome()))) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    liberarAtendimento(target);
                }
            });
        }
        dlgConfirmacaoSimNao.show(target);
    }

    private void liberarAtendimento(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        onLiberarAtendimento(target, dto);
    }

    @Override
    public void search(AjaxRequestTarget target, String key) {
        try {
            profissionalBiometria = false;
            Long codigoRetorno = BOFactoryWicket.getBO(ProfissionalFacade.class).verificarBiometria(key);

            if (codigoRetorno != null) {
                autoCompleteConsultaProfissional.limpar(target);
                passwordField.limpar(target);

                profissional = LoadManager.getInstance(Profissional.class).addProperties(new HQLProperties(Profissional.class).getProperties()).addParameter(new QueryCustom.QueryCustomParameter(Profissional.PROP_CODIGO, codigoRetorno)).start().getVO();

                if (profissional != null) {
                    profissionalBiometria = true;
                    if (usuarioNaoIdentificavel()) {
                        buscarUsuario(target);
                        if (usuario != null) {
                            passwordField.setEnabled(false);
                            target.add(passwordField);
                        } else {
                            throw new ValidacaoException(BundleManager.getString("msgNenhumUsuarioEncontrado"));
                        }
                    }
                    autoCompleteConsultaProfissional.setComponentValue(profissional);
                    autoCompleteConsultaProfissional.setEnabled(false);
                    target.add(autoCompleteConsultaProfissional);

                }
            } else {
                throw new ValidacaoException(BundleManager.getString("msgNenhumProfissionalEncontrado"));
            }
        } catch (DAOException | ValidacaoException ex) {
            MessageUtil.modalWarn(target, PnlAcoesAtendimento.this, ex);
        }
    }

    @Override
    public void register(AjaxRequestTarget target, String key) {
    }

    private void buscarUsuario(AjaxRequestTarget target) {
        try {
            List<Usuario> usuarioList = LoadManager.getInstance(Usuario.class).addProperty(Usuario.PROP_CODIGO).addProperty(Usuario.PROP_LOGIN).addProperty(Usuario.PROP_SENHA).addParameter(new QueryCustom.QueryCustomParameter(Usuario.PROP_PROFISSIONAL, profissional)).addParameter(new QueryCustom.QueryCustomParameter(Usuario.PROP_FLAG_USUARIO_TEMPORARIO, RepositoryComponentDefault.NAO_LONG)).addParameter(new QueryCustom.QueryCustomParameter(Usuario.PROP_STATUS, Usuario.STATUS_ATIVO)).start().getList();

            if (usuarioList.isEmpty()) {
                throw new ValidacaoException(BundleManager.getString("msgNaoFoiEncontradoUsuarioProfissionalInformado"));
            } else if (usuarioList.size() > 1) {
                throw new ValidacaoException(BundleManager.getString("msgExisteMaisUmUsuarioRelacionadoEsteProfissional"));
            }

            usuario = usuarioList.get(0);
            target.add(disabledInputFieldUsuario);
        } catch (ValidacaoException ex) {
            MessageUtil.modalWarn(target, PnlAcoesAtendimento.this, ex);
        }
    }

    public boolean isExistePainelCadastrado() {
        return existePainelCadastrado;
    }

    public ActionAtendimentoWebDTO getDto() {
        return dto;
    }

    public void setResourceImage(AjaxRequestTarget target, IResource resource) {
        imagem.setImageResource(resource);
        target.add(imagem);
    }

    public void setReverterSituacaoAgendamento(boolean reverterSituacaoAgendamento) {
        this.reverterSituacaoAgendamento = reverterSituacaoAgendamento;
    }
}