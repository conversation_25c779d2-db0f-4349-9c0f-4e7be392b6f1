package br.com.celk.view.controle.parametrogem;

import br.com.ksisolucoes.util.parametrogem.ParametroGemHelper;
import br.com.ksisolucoes.vo.basico.ParametroGem;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ParametroDTO implements Serializable{

    private List<ParametroDTO> filhos;
    private String descricao;
    private ParametroGem parametro;
    private List<ParametroGem> parametroEmpresas;
    private List<ParametroGem> parametroUsuarios;

    public List<ParametroDTO> getFilhos() {
        return filhos;
    }

    public List<ParametroGem> getParametroEmpresas() {
        return parametroEmpresas;
    }

    public List<ParametroGem> getParametroUsuarios() {
        return parametroUsuarios;
    }

    public void addParametroUsuarios(ParametroGem parametro) {
        if(parametroUsuarios == null){
            parametroUsuarios = new ArrayList<ParametroGem>();
        }
        parametroUsuarios.add(parametro);
    }

    public void addParametroEmpresa(ParametroGem parametro) {
        if(parametroEmpresas == null){
            parametroEmpresas = new ArrayList<ParametroGem>();
        }
        parametroEmpresas.add(parametro);
    }

    public void addFilho(ParametroDTO filho) {
        if(filhos == null){
            filhos = new ArrayList<ParametroDTO>();
        }
        filhos.add(filho);
    }

    public String getDescricao() {
        return descricao;
    }

    public void setValor(Object valor) {
        if(parametro != null){
            parametro.setValueCache(valor);
            if(valor != null){
                if(valor instanceof CodigoManager){
                    parametro.setValor(((CodigoManager)valor).getCodigoManager().toString());
                }else{
                    parametro.setValor(valor.toString());
                }
            }else{
                parametro.setValor(null);
            }
        }
    }
    
    public Object getValor() {
        if(parametro != null && parametro.getValor()!= null){
            return ParametroGemHelper.getValueTyped(parametro.getType(), parametro.getValor());
//            return parametro.getValueDescription();
        }
        return null;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public ParametroGem getParametro() {
        return parametro;
    }

    public void setParametro(ParametroGem parametro) {
        this.parametro = parametro;
    }

    @Override
    public String toString() {
        return descricao;
    }
    
}
