package br.com.celk.view.materiais.materiais;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.RequiredDropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.methods.WicketMethods;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.unidade.pnl.PnlConsultaUnidade;
import br.com.celk.view.hospital.convenio.autocomplete.AutoCompleteConsultaConvenio;
import br.com.celk.view.materiais.classificacaocontabil.autocomplete.AutoCompleteConsultaClassificacaoContabil;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaMedicamentoCatmat;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProdutoBrasindice;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.CadastroMaterialDTO;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.ProdutoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import com.amazonaws.util.StringUtils;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroMateriaisPage extends BasePage {

    private CadastroMaterialDTO dto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown dropDownPermiteDispensarMais;

    private WebMarkupContainer containerHorus;
    private WebMarkupContainer containerConvenio;
    private WebMarkupContainer containerIntegracaoSistemaTerceiros;

    private EloProdutoBrasindice elo;
    private AutoCompleteConsultaProdutoBrasindice autoCompleteConsultaProdutoBrasindice;
    private AutoCompleteConsultaMedicamentoCatmat autoCompleteConsultaMedicamentoCatmat;
    private RadioButtonGroup radioGroupTipoFatorConversao;
    private DoubleField txtFatorConversao;
    private DropDown<Long> dropDownFlagListaPublica;

    private AutoCompleteConsultaConvenio autoCompleteConsultaConvenio;
    private InputField txtReferencia;
    private InputField txtItemConvenio;
    private InputField<Long> txtIdIntegracaoTerceiro;
    private InputField<String> txtNomeIntegracaoTerceiro;
    private IModel<ProdutoConvenio> modelProdutoConvenio;
    private Table<ProdutoConvenio> tblProdutoConvenio;
    private Form formProdutoConvenio;

    private DropDown<String> tipoProdutoCatmat;
    private InputField txtVidaUtil;
    private Produto produtoIntegracao;

    public CadastroMateriaisPage(Produto produto, boolean viewOnly, boolean clone) {
        init(viewOnly);
        loadDTO(produto, clone);
        initiateSubGrupoEditView();
    }

    public CadastroMateriaisPage(Produto produto, boolean viewOnly) {
        this(produto, viewOnly, false);
    }

    public CadastroMateriaisPage(Produto produto) {
        this(produto, false);
    }

    public CadastroMateriaisPage() {
        this(null);
    }

    private void init(boolean viewOnly) {
        Form form = new Form("form");
        WebMarkupContainer container = new WebMarkupContainer("container", new CompoundPropertyModel(dto = new CadastroMaterialDTO()));
        container.setEnabled(!viewOnly);

        CadastroMaterialDTO proxy = on(CadastroMaterialDTO.class);

        container.add(txtReferencia = new UpperField(path(proxy.getProduto().getReferencia())));
        container.add(new RequiredInputField(path(proxy.getProduto().getDescricao())));
        container.add(DropDownUtil.getNaoSimDropDown(path(proxy.getProduto().getFlagControleMinimo())));
        container.add(DropDownUtil.getCurvaProdutoDropDown(path(proxy.getProduto().getCurva())));
        container.add(DropDownUtil.getCriticidadeProdutoDropDown(path(proxy.getProduto().getCriticidade())));
        container.add(DropDownUtil.getNaoSimDropDown(path(proxy.getProduto().getFlagBaixaEstoqueProcessoEnfermagem())));
        container.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getProduto().getFlagEmprestimo())));
        container.add(getDropDownListaPublica());
        container.add(dropDownPermiteDispensarMais = DropDownUtil.getNaoSimDropDown(path(proxy.getProduto().getFlagPermiteDispensarMais())));
        dropDownPermiteDispensarMais.setEnabled(isActionPermitted(Permissions.PERMITIR_DISPENSAR_MAIS));
        container.add(new PnlConsultaUnidade(path(proxy.getProduto().getUnidade()), true));
        container.add(getDropDownGrupo(path(proxy.getProduto().getSubGrupo().getRoGrupoProduto())));
        container.add(getDropDownSubGrupo(path(proxy.getProduto().getSubGrupo())));
        container.add(new AutoCompleteConsultaClassificacaoContabil(path(proxy.getProduto().getClassificacaoContabil())));
        container.add(txtVidaUtil = new InputField(path(proxy.getProduto().getVidaUtil())));
        container.add(getDropDownCoeficienteVidaUtil(path(proxy.getProduto().getCoeficienteVidaUtil())));
        container.add(new InputField(path(proxy.getProduto().getQtdadeMgMl())).setLabel(new Model<String>(BundleManager.getString("quantidadeUnidade"))));

        container.add(containerConvenio = new WebMarkupContainer("containerConvenio"));
        containerConvenio.setOutputMarkupId(true);
        containerConvenio.add(autoCompleteConsultaProdutoBrasindice = new AutoCompleteConsultaProdutoBrasindice(path(proxy.getEloProdutoBrasindice().getProdutoBrasindice())));
        containerConvenio.add(radioGroupTipoFatorConversao = new RadioButtonGroup(path(proxy.getEloProdutoBrasindice().getTipoFatorConversao())));
        radioGroupTipoFatorConversao.setOutputMarkupId(true);
        radioGroupTipoFatorConversao.setRenderBodyOnly(false);

        radioGroupTipoFatorConversao.add(new AjaxRadio("semFator", new Model(ProdutoBrasindice.TipoFatorConversao.SEM_FATOR.value())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                txtFatorConversao.setComponentValue(null);
                txtFatorConversao.setEnabled(false);
                target.add(txtFatorConversao);
            }
        });

        radioGroupTipoFatorConversao.add(new AjaxRadio("multiplicacao", new Model(ProdutoBrasindice.TipoFatorConversao.MULTIPLICACAO.value())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                txtFatorConversao.setEnabled(true);
                target.add(txtFatorConversao);
            }
        });

        radioGroupTipoFatorConversao.add(new AjaxRadio("divisao", new Model(ProdutoBrasindice.TipoFatorConversao.DIVISAO.value())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                txtFatorConversao.setEnabled(true);
                target.add(txtFatorConversao);
            }
        });

        containerConvenio.add(txtFatorConversao = new DoubleField(path(proxy.getEloProdutoBrasindice().getFatorConversao())));
        txtFatorConversao.setEnabled(false);

        formProdutoConvenio = new Form("formProdutoConvenio", modelProdutoConvenio = new CompoundPropertyModel(new ProdutoConvenio()));
        ProdutoConvenio proxyProdutoConvenio = on(ProdutoConvenio.class);

        formProdutoConvenio.add(autoCompleteConsultaConvenio = new AutoCompleteConsultaConvenio(path(proxyProdutoConvenio.getConvenio())));
        formProdutoConvenio.add(txtItemConvenio = new InputField(path(proxyProdutoConvenio.getItemConvenio())));

        formProdutoConvenio.add(new AbstractAjaxButton("btnAdicionarProdutoConvenio") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarProdutoConvenio(target);
            }

        });
        formProdutoConvenio.add(tblProdutoConvenio = new Table("tblProdutoConvenio", getColumnsProdutoConvenio(), getCollectionProviderProdutoConvenio()));
        tblProdutoConvenio.populate();

        containerConvenio.add(formProdutoConvenio);

        initContainerHorus(container, proxy);

        form.add(container);

        containerIntegracaoSistemaTerceiros = new WebMarkupContainer("containerIntegracaoSistemaTerceiros");
        containerIntegracaoSistemaTerceiros.setOutputMarkupId(true);

        containerIntegracaoSistemaTerceiros.add(txtIdIntegracaoTerceiro = new InputField<Long>(path(proxy.getProduto().getIdIntegracaoTerceiro())));
        containerIntegracaoSistemaTerceiros.add(txtNomeIntegracaoTerceiro = new InputField<String>(path(proxy.getProduto().getNomeIntegracaoTerceiro())));
        txtIdIntegracaoTerceiro.add(new Tooltip().setText("msgTooltipIdUnidadeIntegracao"));
        txtNomeIntegracaoTerceiro.add(new Tooltip().setText("msgTooltipNomeUnidadeIntegracao"));

        container.add(containerIntegracaoSistemaTerceiros);

        form.add(new VoltarButton("btnVoltar"));
        form.add(new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(target);
            }
        }.setVisible(!viewOnly));

        add(form);
    }

    private DropDown getDropDownListaPublica() {
        if (dropDownFlagListaPublica != null) {
            return dropDownFlagListaPublica;
        }
        dropDownFlagListaPublica = new DropDown<Long>("produto.flagListaMedicamentoPublico");
        dropDownFlagListaPublica.addChoice(null, "");
        dropDownFlagListaPublica.addChoice(RepositoryComponentDefault.NAO_LONG, BundleManager.getString("nao"));
        dropDownFlagListaPublica.addChoice(RepositoryComponentDefault.SIM_LONG, BundleManager.getString("sim"));

        return dropDownFlagListaPublica;
    }

    private DropDown<SubGrupo> getDropDownSubGrupo(String id) {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new RequiredDropDown<SubGrupo>(id);
            this.dropDownSubGrupo.addChoice(null, bundle("selecioneGrupo"));
        }

        return this.dropDownSubGrupo;
    }

    public DropDown<Produto.CoeficienteVidaUtil> getDropDownCoeficienteVidaUtil(String id) {

        DropDown dropDownCoeficienteVidaUtil = new DropDown(id);

        dropDownCoeficienteVidaUtil.addChoice(Produto.HORAS, Bundle.getStringApplication("rotulo_horas"));
        dropDownCoeficienteVidaUtil.addChoice(Produto.DIAS, Bundle.getStringApplication("rotulo_dias"));
        dropDownCoeficienteVidaUtil.addChoice(Produto.SEMANAS, Bundle.getStringApplication("rotulo_semanas"));
        dropDownCoeficienteVidaUtil.addChoice(Produto.MESES, Bundle.getStringApplication("rotulo_meses"));
        dropDownCoeficienteVidaUtil.addChoice(Produto.ANOS, Bundle.getStringApplication("rotulo_anos"));

        return dropDownCoeficienteVidaUtil;
    }

    private void initContainerHorus(WebMarkupContainer container, CadastroMaterialDTO proxy) {
        container.add(containerHorus = new WebMarkupContainer("containerHorus"));
        containerHorus.setOutputMarkupId(true);
        containerHorus.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getProduto().getFlagExportaHorus()), false, true));
        containerHorus.add(autoCompleteConsultaMedicamentoCatmat = new AutoCompleteConsultaMedicamentoCatmat(path(proxy.getProduto().getMedicamentoCatmat())));
        containerHorus.add(tipoProdutoCatmat = getDropDownTipoProdutoCatmat(path(proxy.getProduto().getTipoProdutoCatmat())));
        tipoProdutoCatmat.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                Long tipoProduto = null;

                if ("B".equals(tipoProdutoCatmat.getComponentValue())) {
                    tipoProduto = 0L;
                } else if ("S".equals(tipoProdutoCatmat.getComponentValue())) {
                    tipoProduto = 1L;
                } else if ("E".equals(tipoProdutoCatmat.getComponentValue())) {
                    tipoProduto = 2L;
                }
                autoCompleteConsultaMedicamentoCatmat.setTipoCatmat(tipoProduto);
                autoCompleteConsultaMedicamentoCatmat.limpar(target);
            }
        });

    }

    private DropDown<String> getDropDownTipoProdutoCatmat(String id) {

        final DropDown dropDown = new DropDown(id);

        dropDown.addChoice(null, "");
        dropDown.addChoice(Produto.TipoProdutoCatmat.BASICO.value(), Produto.TipoProdutoCatmat.BASICO.descricao());
        dropDown.addChoice(Produto.TipoProdutoCatmat.ESPECIALIZADO.value(), Produto.TipoProdutoCatmat.ESPECIALIZADO.descricao());
        dropDown.addChoice(Produto.TipoProdutoCatmat.ESTRATEGICO.value(), Produto.TipoProdutoCatmat.ESTRATEGICO.descricao());

        return dropDown;
    }

    private DropDown<GrupoProduto> getDropDownGrupo(String id) {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new RequiredDropDown<GrupoProduto>(id);

            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();

                    dropDownSubGrupo.addChoice(null, bundle("selecioneGrupo"));

                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();

                    if (grupoProduto != null) {

                        List<SubGrupo> subGrupos = carregaSubGrupos(grupoProduto.getCodigo());

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, "");
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                                if (RepositoryComponentDefault.SIM_LONG.equals(subGrupo.getFlagVidaUtilEstimada())) {
                                    txtVidaUtil.setRequired(true);
                                    txtVidaUtil.addRequiredClass();
                                }
                            }
                        }
                        dto.getProduto().setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                    target.add(txtVidaUtil);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(GrupoProduto.PROP_CODIGO)
                    .addProperty(GrupoProduto.PROP_DESCRICAO)
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, "");

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }

        return this.dropDownGrupoProduto;
    }


    private List<SubGrupo> carregaSubGrupos(Long codigo) {
        SubGrupo proxy = on(SubGrupo.class);
        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                .addProperty(path(proxy.getId().getCodigo()))
                .addProperty(path(proxy.getId().getCodigoGrupoProduto()))
                .addProperty(path(proxy.getDescricao()))
                .addProperty(path(proxy.getFlagMedicamento()))
                .addProperty(path(proxy.getFlagValidadeMaterial()))
                .addProperty(path(proxy.getFlagVidaUtilEstimada()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getId().getCodigoGrupoProduto()), codigo))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getFlagMedicamento()), SubGrupo.MEDICAMENTO_NAO))
                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getDescricao())))
                .start().getList();

        return subGrupos;
    }

    private void initiateSubGrupoEditView() {
        dropDownSubGrupo.removeAllChoices();
        dropDownSubGrupo.addChoice(null, bundle("todos"));

        GrupoProduto grupoProduto = null;
        if (dto.getProduto() != null) {
            grupoProduto = dto.getProduto().getSubGrupo().getRoGrupoProduto();
        }

        if (grupoProduto != null) {
            SubGrupo proxy = on(SubGrupo.class);
            List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                    .addProperty(path(proxy.getId().getCodigo()))
                    .addProperty(path(proxy.getId().getCodigoGrupoProduto()))
                    .addProperty(path(proxy.getDescricao()))
                    .addProperty(path(proxy.getFlagMedicamento()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getId().getCodigoGrupoProduto()), grupoProduto.getCodigo()))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                for (SubGrupo subGrupo : subGrupos) {
                    dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                }
            }
        }
    }

    private void loadDTO(Produto produto, boolean clone) {
        dto.setProduto(produto);
        if (clone) {
            Produto newProduto = VOUtils.cloneObject(produto);
            newProduto.setCodigo(null);
            newProduto.setReferencia(null);
            dto.setProduto(newProduto);
        }

        if (produto != null && produto.getCodigo() != null) {
            EloProdutoBrasindice proxy = on(EloProdutoBrasindice.class);
            EloProdutoBrasindice elo = LoadManager.getInstance(EloProdutoBrasindice.class)
                    .addProperties(new HQLProperties(EloProdutoBrasindice.class).getProperties())
                    .addProperties(new HQLProperties(ProdutoBrasindice.class, path(proxy.getProdutoBrasindice())).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getProduto()), produto))
                    .start().getVO();

            if (elo != null) {
                dto.setEloProdutoBrasindice(elo);
                if (clone) {
                    EloProdutoBrasindice newElo = VOUtils.cloneObject(elo);
                    newElo.setCodigo(null);
                    dto.setEloProdutoBrasindice(newElo);
                }
                txtFatorConversao.setEnabled(elo.getTipoFatorConversao() != null);
            }

            if (clone) {
                dto.setProdutoConvenioList(new ArrayList<ProdutoConvenio>());
            } else {
                List<ProdutoConvenio> produtoConvenioList = LoadManager.getInstance(ProdutoConvenio.class)
                        .addProperties(new HQLProperties(ProdutoConvenio.class).getProperties())
                        .addProperties(new HQLProperties(Convenio.class, ProdutoConvenio.PROP_CONVENIO).getProperties())
                        .addProperties(new HQLProperties(Produto.class, ProdutoConvenio.PROP_PRODUTO).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(ProdutoConvenio.PROP_PRODUTO, produto))
                        .start().getList();

                if (CollectionUtils.isNotNullEmpty(produtoConvenioList)) {
                    dto.setProdutoConvenioList(produtoConvenioList);
                }
            }
        }
    }

    public void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {

        if (!SubGrupo.MEDICAMENTO_NAO.equals(dto.getProduto().getSubGrupo().getFlagMedicamento())) {
            throw new ValidacaoException(bundle("msgNaoPermitidoSalvarMaterialSubgrupoConfigurado"));
        }

        if (dto.getProduto().getIdIntegracaoTerceiro() != null) {
            boolean idIntegracaoTerceiroExists = existsIdIntegracaoTerceiro(dto.getProduto().getIdIntegracaoTerceiro(),dto.getProduto());

            if (isIntegracaoBranetHabilitada() && idIntegracaoTerceiroExists) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_id_ja_cadastrado", produtoIntegracao));
            }
        }

        if (isIntegracaoBranetHabilitada() && isValidarIdIntegracaoBranet() && (dto.getProduto().getIdIntegracaoTerceiro() == null || StringUtils.isNullOrEmpty(dto.getProduto().getNomeIntegracaoTerceiro()))) {
            throw new ValidacaoException("Deverá ser preenchido o ID e o nome do material para que haja integração.");
        }

        Produto produto = BOFactoryWicket.getBO(ProdutoFacade.class).saveMateriais(dto);

        Page page = new ConsultaMateriaisPage();
        setResponsePage(page);
        getSession().getFeedbackMessages().info(page, WicketMethods.getMessageResgistroSalvo(Produto.class, produto));
    }

    private boolean isIntegracaoBranetHabilitada() throws DAOException {
        return RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("habilitarIntegracaoBranet"));
    }

    private boolean isValidarIdIntegracaoBranet() throws DAOException {
        return RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("validarIdNomeIntegracaoBranet"));
    }

    private boolean existsIdIntegracaoTerceiro(Long idIntegracaoTerceiro, Produto object) {
        Produto produto = LoadManager.getInstance(Produto.class)
                .addProperty(br.com.ksisolucoes.vo.entradas.estoque.Produto.PROP_CODIGO)
                .addProperty(br.com.ksisolucoes.vo.entradas.estoque.Produto.PROP_ID_INTEGRACAO_TERCEIRO)
                .addParameter(new QueryCustom.QueryCustomParameter(br.com.ksisolucoes.vo.entradas.estoque.Produto.PROP_ID_INTEGRACAO_TERCEIRO, idIntegracaoTerceiro))
                .setMaxResults(1).start().getVO();

        if (produto == null) {
            return false;
        }

        produtoIntegracao = produto;

        return !(produto.getCodigo().equals(object.getCodigo()) && produto.getIdIntegracaoTerceiro().equals(object.getIdIntegracaoTerceiro()));
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        if (dto.getEloProdutoBrasindice() != null) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerConvenio)));
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(formProdutoConvenio)));
        }
        if (dto.getProduto() != null && dto.getProduto().getMedicamentoCatmat() != null && (dto.getProduto().getMedicamentoCatmat().getCatmat() != null || dto.getProduto().getTipoProdutoCatmat() != null)) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerHorus)));
        }
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtReferencia;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("cadastroMateriais");
    }

    private void adicionarProdutoConvenio(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        ProdutoConvenio produtoConvenio = modelProdutoConvenio.getObject();
        if (produtoConvenio.getConvenio() == null) {
            throw new ValidacaoException(bundle("informeConvenio"));
        }
        if (produtoConvenio.getItemConvenio() == null) {
            throw new ValidacaoException(bundle("informeCodigoProprio"));
        }

        for (ProdutoConvenio item : dto.getProdutoConvenioList()) {
            if (item.getConvenio().equals(produtoConvenio.getConvenio())) {
                throw new ValidacaoException(bundle("convenioJaAdicionado"));
            }
        }

        dto.getProdutoConvenioList().add(produtoConvenio);
        tblProdutoConvenio.update(target);
        modelProdutoConvenio.setObject(new ProdutoConvenio());
        autoCompleteConsultaConvenio.limpar(target);
        txtItemConvenio.limpar(target);
        target.focusComponent(autoCompleteConsultaConvenio);
    }

    private List<IColumn> getColumnsProdutoConvenio() {
        List<IColumn> columns = new ArrayList<IColumn>();

        ProdutoConvenio on = on(ProdutoConvenio.class);

        columns.add(getActionColumnProdutoConvenio());
        columns.add(createColumn(bundle("codigoProprio"), on.getItemConvenio()));
        columns.add(createColumn(bundle("convenio"), on.getConvenio().getDescricao()));

        return columns;
    }

    private IColumn getActionColumnProdutoConvenio() {
        return new MultipleActionCustomColumn<ProdutoConvenio>() {

            @Override
            public void customizeColumn(ProdutoConvenio rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<ProdutoConvenio>() {
                    @Override
                    public void action(AjaxRequestTarget target, ProdutoConvenio modelObject) throws ValidacaoException, DAOException {
                        removerProdutoConvenio(target, modelObject);
                    }
                });
            }
        };
    }

    private void removerProdutoConvenio(AjaxRequestTarget target, ProdutoConvenio produtoConvenio) {
        for (int i = 0; i < dto.getProdutoConvenioList().size(); i++) {
            if (dto.getProdutoConvenioList().get(i) == produtoConvenio) {
                dto.getProdutoConvenioList().remove(i);
                break;
            }
        }
        tblProdutoConvenio.update(target);
    }

    private ICollectionProvider getCollectionProviderProdutoConvenio() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return dto.getProdutoConvenioList();
            }

        };
    }
}
