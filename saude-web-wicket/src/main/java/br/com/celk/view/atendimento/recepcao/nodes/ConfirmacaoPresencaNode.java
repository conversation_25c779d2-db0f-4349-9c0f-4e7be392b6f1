package br.com.celk.view.atendimento.recepcao.nodes;

import br.com.celk.atendimento.recepcao.NodesRecepcaoRef;
import br.com.celk.resources.Icon32;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.recepcao.nodes.annotations.RecepcaoNode;
import br.com.celk.view.atendimento.recepcao.nodes.base.RecepcaoNodeImp;
import br.com.celk.view.atendimento.recepcao.panel.confirmacaopresenca.ConfirmacaoPresencaoPanel;
import br.com.celk.view.atendimento.recepcao.panel.template.RecepcaoCadastroPanel;

/**
 * <AUTHOR>
 */
@RecepcaoNode(NodesRecepcaoRef.CONFIRMACAO_PRESENCA)
public class ConfirmacaoPresencaNode extends RecepcaoNodeImp{

    @Override
    public RecepcaoCadastroPanel getPanel(String id) {
        return new ConfirmacaoPresencaoPanel(id);
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("confirmacaoPresenca");
    }

    @Override
    public Icon32 getIcone() {
        return Icon32.CALENDAR_EDIT;
    }
}
