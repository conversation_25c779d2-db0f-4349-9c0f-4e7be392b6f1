package br.com.celk.view.materiais.medicamento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.TipoInfusao;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static ch.lambdaj.Lambda.on;


/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaTipoInfusaoPage extends ConsultaPage<TipoInfusao, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;
    private Long codigo;
     @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new UpperField("descricao"));
        form.add(new InputField("codigo"));
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        TipoInfusao proxy = on(TipoInfusao.class);
        ColumnFactory columnFactory = new ColumnFactory(TipoInfusao.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(TipoInfusao.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), VOUtils.montarPath(TipoInfusao.PROP_CODIGO)));
        return columns;
    }

    private CustomColumn<TipoInfusao> getCustomColumn() {
        return new CustomColumn<TipoInfusao>() {

            @Override
            public Component getComponent(String componentId, final TipoInfusao rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target)  {
                        setResponsePage(new CadastroTipoInfusaoPage(rowObject,false));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target){
                        setResponsePage(new CadastroTipoInfusaoPage(rowObject, true));
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter(){
            @Override
            public Class getClassConsulta() {
                return TipoInfusao.class;
            }
        }) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(TipoInfusao.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoInfusao.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoInfusao.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IGUAL, codigo));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroTipoInfusaoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consulta_TipoInfusao");
    }
}
