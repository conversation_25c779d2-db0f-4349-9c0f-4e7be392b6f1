package br.com.celk.view.frota.veiculocombustivel;

import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.frota.VeiculoCombustivel;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaVeiculoCombustivel extends CustomizeConsultaAdapter{

    @Override
    public void consultaCustomizeFilterProperties(Map<String, QueryParameter> filterProperties) {
        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(VOUtils.montarPath(VeiculoCombustivel.PROP_DESCRICAO), QueryParameter.ILIKE));
    }

    @Override
    public void consultaCustomizeViewProperties(Map<String, String> properties) {
        properties.put(BundleManager.getString("referencia"), VOUtils.montarPath(VeiculoCombustivel.PROP_REFERENCIA));
        properties.put(BundleManager.getString("descricao"), VOUtils.montarPath(VeiculoCombustivel.PROP_DESCRICAO));
    }

    @Override
    public Class getClassConsulta() {
        return VeiculoCombustivel.class;
    }

    @Override
    public String[] getProperties() {
        return new HQLProperties(VeiculoCombustivel.class).getProperties();
    }

}
