package br.com.celk.view.vigilancia.estabelecimento.autocomplete.restricaocontainer;

import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.inputfield.InputField;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QueryConsultaEstabelecimentoDTOParam;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class RestricaoContainerEstabelecimento extends Panel implements IRestricaoContainer<QueryConsultaEstabelecimentoDTOParam> {

    private InputField<String> txtDescricao;
    private InputField<String> txtFantasia;
    private InputField txtCpfCnpj;
    
    private QueryConsultaEstabelecimentoDTOParam param = new QueryConsultaEstabelecimentoDTOParam();
    
    public RestricaoContainerEstabelecimento(String id) {
        super(id);
        
        WebMarkupContainer root = new WebMarkupContainer("root", new CompoundPropertyModel(param));
        
        root.add(txtDescricao = new InputField<String>("razaoSocial"));
        root.add(txtFantasia = new InputField<String>("fantasia"));
        root.add(txtCpfCnpj = new InputField<String>("cpfCnpj"));
        
        add(root);
    }

    @Override
    public QueryConsultaEstabelecimentoDTOParam getRestricoes() {
        return param;
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        txtDescricao.limpar(target);
    }

    @Override
    public Component getComponentRequestFocus() {
        return txtDescricao;
    }

}
