package br.com.celk.view.materiais.gruposubgrupo;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.materiais.grupoproduto.customize.CustomizeConsultaGrupoProduto;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaGrupoSubGrupoPage extends ConsultaPage<GrupoProduto, List<BuilderQueryCustom.QueryParameter>> {

    private Long codigo;
    private String descricao;
    
    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(new InputField<Long>("codigo"));
        form.add(new UpperField("descricao"));
    }
    
    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(GrupoProduto.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), VOUtils.montarPath(GrupoProduto.PROP_CODIGO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO)));
        
        return columns;
    }
    
     private CustomColumn<GrupoProduto> getCustomColumn() {
        return new CustomColumn<GrupoProduto>() {

            @Override
            public Component getComponent(String componentId, final GrupoProduto rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroGrupoSubGrupoPage(rowObject));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(BasicoFacade.class).deletarGrupoProduto(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroGrupoSubGrupoPage(rowObject, true));
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaGrupoProduto()){

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(GrupoProduto.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<QueryParameter> getParameters() {
        List<QueryParameter> parameters = new ArrayList<QueryParameter>();
        
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO), QueryParameter.ILIKE, descricao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(GrupoProduto.PROP_CODIGO), QueryParameter.IGUAL, codigo));
        
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroGrupoSubGrupoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaGrupoProduto");
    }

}
