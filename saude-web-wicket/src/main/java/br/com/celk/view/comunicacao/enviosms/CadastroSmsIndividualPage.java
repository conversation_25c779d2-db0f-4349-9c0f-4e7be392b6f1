package br.com.celk.view.comunicacao.enviosms;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.bo.service.sms.interfaces.facade.SmsFacade;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.inputarea.RequiredInputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.telefonefield.TelefoneField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.service.sms.SmsCadastro;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroSmsIndividualPage extends BasePage {
    
    private Form<SmsCadastro> form;
    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;
    private String celular;
    private InputField txtCelular;
    private SmsCadastro smsCadastro;
    
    public CadastroSmsIndividualPage(){
        init();
    }
    
    public CadastroSmsIndividualPage(SmsCadastro smsCadastro){
        this.smsCadastro = smsCadastro;
        this.celular = smsCadastro.getUsuarioCadsus().getCelular();
        init();
    }
    
    private void init() {
        form = new Form("form", new CompoundPropertyModel(smsCadastro != null ? smsCadastro : new SmsCadastro()));
        
        SmsCadastro proxy = on(SmsCadastro.class);
        
        form.add(autoCompleteConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus(path(proxy.getUsuarioCadsus()), true) {

            @Override
            public AutoCompleteConsultaUsuarioCadsus.Configuration getConfigurationInstance() {
                return AutoCompleteConsultaUsuarioCadsus.Configuration.ATIVO_PROVISORIO;
            }
        });
        
        autoCompleteConsultaUsuarioCadsus.add(new ConsultaListener<UsuarioCadsus>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, UsuarioCadsus object) {
                celular = object.getCelular();
                target.add(txtCelular);
                target.appendJavaScript(JScript.initMasks());
            }
        });
        autoCompleteConsultaUsuarioCadsus.add(new RemoveListener<UsuarioCadsus>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, UsuarioCadsus object) {
                celular = null;
                txtCelular.limpar(target);
            }
        });
        
        form.add(txtCelular = (InputField) new TelefoneField("celular", new PropertyModel<String>(this, "celular")).setEnabled(false));
        form.add(new RequiredInputArea<String>(path(proxy.getMensagem())));
        
        form.add(new VoltarButton("btnVoltar"));
        
        form.add(new AbstractAjaxButton("btnSalvar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(false);
            }
        });
        
        form.add(new AbstractAjaxButton("btnSalvarEnviar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(true);
            }
        });
        
        add(form);
    }
    
    private void salvar(boolean enviarSms) throws DAOException, ValidacaoException{
        if(celular == null){
            throw new ValidacaoException(BundleManager.getString("msgPacienteInformadoDevePossuirCelularCadastrado"));
        }
        
        smsCadastro = form.getModel().getObject();
        smsCadastro.setTipoDestino(SmsCadastro.TipoDestino.INDIVIDUAL.value());
        smsCadastro.setTotalSms(1L);
        smsCadastro.setDestino(smsCadastro.getUsuarioCadsus().getNome() + " - " + Util.getTelefoneFormatado(smsCadastro.getUsuarioCadsus().getCelular()));
        
        BOFactoryWicket.getBO(SmsFacade.class).salvarEnviarSmsCadastro(smsCadastro,null, enviarSms);
        
        Page page = new ConsultaEnvioSmsPage();
        getSession().getFeedbackMessages().info(page, BundleManager.getString("registro_salvo_sucesso"));
        setResponsePage(page);
    }
    
    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaUsuarioCadsus.getTxtDescricao().getTextField();
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroSmsIndividual");
    }   
    
}
