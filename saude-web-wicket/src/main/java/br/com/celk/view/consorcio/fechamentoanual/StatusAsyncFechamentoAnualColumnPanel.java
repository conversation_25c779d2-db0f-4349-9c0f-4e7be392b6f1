package br.com.celk.view.consorcio.fechamentoanual;

import br.com.celk.component.template.Panel;
import br.com.celk.system.asyncprocess.AsyncProcessHelper;
import br.com.ksisolucoes.vo.consorcio.FechamentoAnual;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.markup.html.WebMarkupContainer;

public class StatusAsyncFechamentoAnualColumnPanel extends Panel {
    private FechamentoAnual fechamentoAnual;

    public StatusAsyncFechamentoAnualColumnPanel(String id, FechamentoAnual fechamentoAnual) {
        super(id);
        this.fechamentoAnual = fechamentoAnual;
        init();
    }

    private void init() {
        WebMarkupContainer img;

        AsyncProcessHelper.StatusAsyncProcess sap = AsyncProcessHelper.resolveStatus(fechamentoAnual.getAsyncProcess());
        add(img = new WebMarkupContainer("img"));
        if (sap != null) {
            img.add(new AttributeModifier("class", sap.icon()));
            img.add(new AttributeModifier("title", sap.title()));
        }
    }
}
