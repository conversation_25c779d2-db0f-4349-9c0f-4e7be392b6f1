package br.com.celk.view.atendimento.consultaprontuario.panel.consultacurvacrescimento;

import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.longfield.LongField;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.atendimento.consultaprontuario.panel.template.ConsultaProntuarioCadastroPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.chart.curvacrescimento.*;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.GraficoCurvaCrescimentoDTO;
import br.com.ksisolucoes.bo.prontuario.web.chart.dto.CurvaCrescimentoPacienteDTO;
import br.com.ksisolucoes.bo.prontuario.web.chart.dto.CurvaCrescimentoSeriesDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Antecedentes;
import br.com.ksisolucoes.vo.prontuario.basico.GraficoDesenvolvimento;
import br.com.ksisolucoes.vo.prontuario.basico.Puerperio;
import com.googlecode.wickedcharts.highcharts.options.Options;
import com.googlecode.wickedcharts.wicket6.highcharts.Chart;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class ConsultaCurvaCrescimentoPanel extends ConsultaProntuarioCadastroPanel {

    private Form<GraficoCurvaCrescimentoDTO> form;
    private DropDown<Long> dropDownPadraoCurva;
    private DropDown<Long> dropDownTipoGrafico;
    private Chart chartCurvaCrescimento;

    private WebMarkupContainer containerPreNatal;

    public ConsultaCurvaCrescimentoPanel(String id) {
        super(id, bundle("crescimentoDesenvolvimento"));
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        GraficoCurvaCrescimentoDTO proxy = on(GraficoCurvaCrescimentoDTO.class);

        getForm().add(containerPreNatal = new WebMarkupContainer("containerPreNatal"));
        containerPreNatal.add(DropDownUtil.getIEnumDropDown(path(proxy.getAntecedentes().getTipoGravidez()), UsuarioCadsus.TipoGravidez.values(), true));
        containerPreNatal.add(DropDownUtil.getIEnumDropDown(path(proxy.getAntecedentes().getTipoParto()), Puerperio.TipoParto.values(), true));
        containerPreNatal.add(new LongField(path(proxy.getAntecedentes().getIdadeGestacional())));
        containerPreNatal.add(new LongField(path(proxy.getAntecedentes().getApgarPrimeiroMinuto())));
        containerPreNatal.add(new LongField(path(proxy.getAntecedentes().getApgarQuintoMinuto())));
        containerPreNatal.add(new LongField(path(proxy.getAntecedentes().getApgarDecimoMinuto())));
        containerPreNatal.add(new DoubleField(path(proxy.getAntecedentes().getPesoNascer())));
        containerPreNatal.add(new LongField(path(proxy.getAntecedentes().getComprimentoNascer())));
        containerPreNatal.add(new LongField(path(proxy.getAntecedentes().getPerimetroCefalicoNascer())));
        containerPreNatal.setEnabled(false);

        getForm().add(dropDownPadraoCurva = DropDownUtil.getIEnumDropDown(path(proxy.getPadraoCurva()), GraficoCurvaCrescimentoDTO.PadraoCurva.values(), false, false));
        getForm().getModel().getObject().setPadraoCurva(GraficoCurvaCrescimentoDTO.PadraoCurva.OMS_2006.value());

        dropDownPadraoCurva.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                carregarTipoGrafico(target);
                target.add(dropDownTipoGrafico);
                atualizarGrafico(target);
            }
        });

        getForm().add(dropDownTipoGrafico = getDropDownTipoGrafico(path(proxy.getTipoGrafico())));

        carregarDadosUsuarioCadsus();
        getForm().add(chartCurvaCrescimento = new Chart("chart", gerarGrafico()));

        add(getForm());
        carregaDadosAntecedentes();
    }

    private Options gerarGrafico() {
        GraficoCurvaCrescimentoDTO grafico = getForm().getModel().getObject();

        List<CurvaCrescimentoSeriesDTO> dtoSeries = gerarCurvaPadrao(grafico);
        CurvaCrescimentoSeriesDTO dtoPaciente = gerarSeriePaciente(grafico);

        if (dtoPaciente != null) {
            dtoSeries.add(dtoPaciente);
        }

        if (GraficoCurvaCrescimentoDTO.PadraoCurva.OMS_2006.value().equals(grafico.getPadraoCurva())) {
            if (GraficoDesenvolvimento.TipoGrafico.PESO_POR_IDADE.value().equals(grafico.getTipoGrafico())) {
                PesoIdadeChart options = new PesoIdadeChart();
                options.setLineSeries(dtoSeries);

                return options.startChart();
            } else if (GraficoDesenvolvimento.TipoGrafico.PESO_POR_COMPRIMENTO.value().equals(grafico.getTipoGrafico())) {
                PesoComprimentoChart options = new PesoComprimentoChart();
                options.setLineSeries(dtoSeries);

                return options.startChart();
            } else if (GraficoDesenvolvimento.TipoGrafico.PESO_POR_ESTATURA.value().equals(grafico.getTipoGrafico())) {
                PesoEstaturaChart options = new PesoEstaturaChart();
                options.setLineSeries(dtoSeries);

                return options.startChart();
            } else if (GraficoDesenvolvimento.TipoGrafico.COMPRIMENTO_ESTATURA_POR_IDADE.value().equals(grafico.getTipoGrafico())) {
                ComprimentoEstaturaIdadeChart options = new ComprimentoEstaturaIdadeChart();
                options.setLineSeries(dtoSeries);

                return options.startChart();
            } else if (GraficoDesenvolvimento.TipoGrafico.PERIMETRO_CEFALICO_POR_IDADE.value().equals(grafico.getTipoGrafico())) {
                PerimetroCefalicoIdadeChart options = new PerimetroCefalicoIdadeChart();
                options.setLineSeries(dtoSeries);

                return options.startChart();
            } else {
                ImcIdadeChart options = new ImcIdadeChart();
                options.setLineSeries(dtoSeries);

                return options.startChart();
            }
        } else if (GraficoCurvaCrescimentoDTO.PadraoCurva.OMS_2007.value().equals(grafico.getPadraoCurva())) {
            if (GraficoDesenvolvimento.TipoGrafico.ESTATURA_POR_IDADE_CINCO_A_DEZENOVE_ANOS.value().equals(grafico.getTipoGrafico())) {
                EstaturaIdadeCincoDezenoveAnosChart options = new EstaturaIdadeCincoDezenoveAnosChart();
                options.setLineSeries(dtoSeries);

                return options.startChart();
            } else {
                ImcIdadeCincoDezenoveAnosChart options = new ImcIdadeCincoDezenoveAnosChart();
                options.setLineSeries(dtoSeries);

                return options.startChart();
            }
        } else {//GraficoCurvaCrescimentoDTO.PadraoCurva.OMS_2007_5_10
            //GraficoDesenvolvimento.TipoGrafico.PESO_POR_IDADE_CINCO_A_DEZ_ANOS
            PesoIdadeCincoDezAnosChart options = new PesoIdadeCincoDezAnosChart();
            options.setLineSeries(dtoSeries);

            return options.startChart();
        }
    }

    private DropDown getDropDownTipoGrafico(String id) {
        dropDownTipoGrafico = new DropDown<Long>(id);

        carregarTipoGrafico(null);

        dropDownTipoGrafico.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                atualizarGrafico(target);
            }
        });

        return dropDownTipoGrafico;
    }

    private Form<GraficoCurvaCrescimentoDTO> getForm() {
        if (this.form == null) {
            this.form = new Form<GraficoCurvaCrescimentoDTO>("form", new CompoundPropertyModel<GraficoCurvaCrescimentoDTO>(new GraficoCurvaCrescimentoDTO()));
        }
        return this.form;
    }

    private void carregarTipoGrafico(AjaxRequestTarget target) {

        if (target != null) {
            dropDownTipoGrafico.removeAllChoices(target);
        }

        if (GraficoCurvaCrescimentoDTO.PadraoCurva.OMS_2006.value().equals(getForm().getModel().getObject().getPadraoCurva())) {
            dropDownTipoGrafico.addChoice(GraficoDesenvolvimento.TipoGrafico.PESO_POR_IDADE.value(), bundle("pesoPorIdade", this));
            dropDownTipoGrafico.addChoice(GraficoDesenvolvimento.TipoGrafico.PESO_POR_COMPRIMENTO.value(), bundle("pesoPorComprimento", this));
            dropDownTipoGrafico.addChoice(GraficoDesenvolvimento.TipoGrafico.PESO_POR_ESTATURA.value(), bundle("pesoPorEstatura", this));
            dropDownTipoGrafico.addChoice(GraficoDesenvolvimento.TipoGrafico.COMPRIMENTO_ESTATURA_POR_IDADE.value(), bundle("comprimentoEstaturaPorIdade", this));
            dropDownTipoGrafico.addChoice(GraficoDesenvolvimento.TipoGrafico.IMC_POR_IDADE.value(), bundle("imcPorIdade", this));
            dropDownTipoGrafico.addChoice(GraficoDesenvolvimento.TipoGrafico.PERIMETRO_CEFALICO_POR_IDADE.value(), bundle("perimetroCefalicoPorIdade"));
            getForm().getModel().getObject().setTipoGrafico(GraficoDesenvolvimento.TipoGrafico.PESO_POR_IDADE.value());
        } else if (GraficoCurvaCrescimentoDTO.PadraoCurva.OMS_2007.value().equals(getForm().getModel().getObject().getPadraoCurva())) {
            dropDownTipoGrafico.addChoice(GraficoDesenvolvimento.TipoGrafico.ESTATURA_POR_IDADE_CINCO_A_DEZENOVE_ANOS.value(), bundle("estaturaPorIdade", this));
            dropDownTipoGrafico.addChoice(GraficoDesenvolvimento.TipoGrafico.IMC_POR_IDADE_CINCO_A_DEZENOVE_ANOS.value(), bundle("imcPorIdade", this));
            getForm().getModel().getObject().setTipoGrafico(GraficoDesenvolvimento.TipoGrafico.ESTATURA_POR_IDADE_CINCO_A_DEZENOVE_ANOS.value());
        } else if (GraficoCurvaCrescimentoDTO.PadraoCurva.OMS_2007_5_10.value().equals(getForm().getModel().getObject().getPadraoCurva())) {
            dropDownTipoGrafico.addChoice(GraficoDesenvolvimento.TipoGrafico.PESO_POR_IDADE_CINCO_A_DEZ_ANOS.value(), bundle("pesoPorIdade", this));
            dropDownTipoGrafico.addChoice(GraficoDesenvolvimento.TipoGrafico.ESTATURA_POR_IDADE_CINCO_A_DEZENOVE_ANOS.value(), bundle("estaturaPorIdade", this));
            dropDownTipoGrafico.addChoice(GraficoDesenvolvimento.TipoGrafico.IMC_POR_IDADE_CINCO_A_DEZENOVE_ANOS.value(), bundle("imcPorIdade", this));
            getForm().getModel().getObject().setTipoGrafico(GraficoDesenvolvimento.TipoGrafico.PESO_POR_IDADE_CINCO_A_DEZ_ANOS.value());
        }

    }

    private void carregarDadosUsuarioCadsus() {
        getForm().getModel().getObject().setIdade(getUsuarioCadsus().getIdade());
        if (RepositoryComponentDefault.SEXO_MASCULINO.equals(getUsuarioCadsus().getSexo())) {
            getForm().getModel().getObject().setSexo(GraficoDesenvolvimento.Sexo.MASCULINO.value());
        } else if (RepositoryComponentDefault.SEXO_FEMININO.equals(getUsuarioCadsus().getSexo())) {
            getForm().getModel().getObject().setSexo(GraficoDesenvolvimento.Sexo.FEMININO.value());
        }
    }

    private List<CurvaCrescimentoSeriesDTO> gerarCurvaPadrao(GraficoCurvaCrescimentoDTO grafico) {
        List<CurvaCrescimentoSeriesDTO> dtoSeries = new ArrayList<CurvaCrescimentoSeriesDTO>();

        Map<Integer, List<Number>> map = null;
        try {
            map = BOFactoryWicket.getBO(BasicoFacade.class).consultarCurvaDesenvolvimento(grafico);
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        CurvaCrescimentoSeriesDTO dtoN3 = new CurvaCrescimentoSeriesDTO();
        dtoN3.setName(bundle("threeNegativeDP", this));
        dtoN3.setMarker(Boolean.FALSE);
        dtoN3.setSerie(map.get(-3));
        dtoN3.setSerieCategoria(map.get(7));

        CurvaCrescimentoSeriesDTO dtoN2 = new CurvaCrescimentoSeriesDTO();
        dtoN2.setName(bundle("twoNegativeDP", this));
        dtoN2.setMarker(Boolean.FALSE);
        dtoN2.setSerie(map.get(-2));
        dtoN2.setSerieCategoria(map.get(7));

        CurvaCrescimentoSeriesDTO dtoN1 = new CurvaCrescimentoSeriesDTO();
        dtoN1.setName(bundle("oneNegativeDP", this));
        dtoN1.setMarker(Boolean.FALSE);
        dtoN1.setSerie(map.get(-1));
        dtoN1.setSerieCategoria(map.get(7));

        CurvaCrescimentoSeriesDTO dtoM = new CurvaCrescimentoSeriesDTO();
        dtoM.setName(bundle("mediana", this));
        dtoM.setMarker(Boolean.FALSE);
        dtoM.setSerie(map.get(0));
        dtoM.setSerieCategoria(map.get(7));

        CurvaCrescimentoSeriesDTO dtoP1 = new CurvaCrescimentoSeriesDTO();
        dtoP1.setName(bundle("oneDP", this));
        dtoP1.setMarker(Boolean.FALSE);
        dtoP1.setSerie(map.get(1));
        dtoP1.setSerieCategoria(map.get(7));

        CurvaCrescimentoSeriesDTO dtoP2 = new CurvaCrescimentoSeriesDTO();
        dtoP2.setName(bundle("twoDP", this));
        dtoP2.setMarker(Boolean.FALSE);
        dtoP2.setSerie(map.get(2));
        dtoP2.setSerieCategoria(map.get(7));

        CurvaCrescimentoSeriesDTO dtoP3 = new CurvaCrescimentoSeriesDTO();
        dtoP3.setName(bundle("threeDP", this));
        dtoP3.setMarker(Boolean.FALSE);
        dtoP3.setSerie(map.get(3));
        dtoP3.setSerieCategoria(map.get(7));

        dtoSeries.add(dtoP3);
        dtoSeries.add(dtoP2);
        dtoSeries.add(dtoP1);
        dtoSeries.add(dtoM);
        dtoSeries.add(dtoN1);
        dtoSeries.add(dtoN2);
        dtoSeries.add(dtoN3);

        return dtoSeries;
    }

    private CurvaCrescimentoSeriesDTO gerarSeriePaciente(GraficoCurvaCrescimentoDTO grafico) {
        CurvaCrescimentoSeriesDTO dto = null;
        CurvaCrescimentoPacienteDTO atendimentoPrimarioList = null;

        try {
            atendimentoPrimarioList = BOFactoryWicket.getBO(BasicoFacade.class).consultarCurvaDesenvolvimentoPaciente(getUsuarioCadsus(), grafico.getTipoGrafico());
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        if (atendimentoPrimarioList != null) {
            dto = new CurvaCrescimentoSeriesDTO();

            dto.setName(bundle("paciente"));
            dto.setMarker(Boolean.TRUE);
            if (GraficoDesenvolvimento.TipoGrafico.PESO_POR_IDADE.value().equals(grafico.getTipoGrafico())
                    || GraficoDesenvolvimento.TipoGrafico.PESO_POR_IDADE_CINCO_A_DEZ_ANOS.value().equals(grafico.getTipoGrafico())) {
                dto.setSerie(atendimentoPrimarioList.getPeso());
            } else if (GraficoDesenvolvimento.TipoGrafico.PESO_POR_COMPRIMENTO.value().equals(grafico.getTipoGrafico())
                    || GraficoDesenvolvimento.TipoGrafico.PESO_POR_ESTATURA.value().equals(grafico.getTipoGrafico())) {
                dto.setSerie(atendimentoPrimarioList.getPeso());
                dto.setSerieSecundaria(atendimentoPrimarioList.getAltura());
            } else if (GraficoDesenvolvimento.TipoGrafico.COMPRIMENTO_ESTATURA_POR_IDADE.value().equals(grafico.getTipoGrafico())
                    || GraficoDesenvolvimento.TipoGrafico.ESTATURA_POR_IDADE_CINCO_A_DEZENOVE_ANOS.value().equals(grafico.getTipoGrafico())) {
                dto.setSerie(atendimentoPrimarioList.getAltura());
            } else if (GraficoDesenvolvimento.TipoGrafico.IMC_POR_IDADE.value().equals(grafico.getTipoGrafico())
                    || GraficoDesenvolvimento.TipoGrafico.IMC_POR_IDADE_CINCO_A_DEZENOVE_ANOS.value().equals(grafico.getTipoGrafico())) {
                dto.setSerie(atendimentoPrimarioList.getImc());
            } else if (GraficoDesenvolvimento.TipoGrafico.PERIMETRO_CEFALICO_POR_IDADE.value().equals(grafico.getTipoGrafico())) {
                dto.setSerie(atendimentoPrimarioList.getPerimetroCefalico());
            }
            dto.setDataAtendimento(atendimentoPrimarioList.getDataAvaliacao());
            dto.setPaciente(Boolean.TRUE);
            dto.setDataNascimentoPaciente(getUsuarioCadsus().getDataNascimento());
        }
        return dto;
    }

    private void atualizarGrafico(AjaxRequestTarget target) {
        chartCurvaCrescimento.setOptions(gerarGrafico());
        target.add(chartCurvaCrescimento);
    }

    private void carregaDadosAntecedentes() {
        Antecedentes antecedentes = LoadManager.getInstance(Antecedentes.class)
                .addProperties(new HQLProperties(Antecedentes.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Antecedentes.PROP_USUARIO_CAD_SUS, UsuarioCadsus.PROP_CODIGO), getUsuarioCadsus().getCodigo()))
                .addSorter(new QueryCustom.QueryCustomSorter(Antecedentes.PROP_CODIGO, QueryCustom.QueryCustomSorter.DECRESCENTE))
                .setMaxResults(1)
                .start().getVO();

        getForm().getModel().getObject().setAntecedentes(antecedentes);
    }
}