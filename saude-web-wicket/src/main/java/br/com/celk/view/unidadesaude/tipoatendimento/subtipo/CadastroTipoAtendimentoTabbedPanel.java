package br.com.celk.view.unidadesaude.tipoatendimento.subtipo;

import br.com.celk.component.tabbedpanel.cadastro.CadastroTabbedPanel;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.unidadesaude.tipoatendimento.dto.TipoAtendimentoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.util.List;
import org.apache.wicket.extensions.markup.html.tabs.ITab;

/**
 *
 * <AUTHOR>
 */
public class CadastroTipoAtendimentoTabbedPanel extends CadastroTabbedPanel<TipoAtendimentoDTO> {

    public CadastroTipoAtendimentoTabbedPanel(String id, TipoAtendimentoDTO object, boolean viewOnly, List<ITab> tabs, boolean buttonBackVisible) {
        super(id, object, viewOnly, tabs, buttonBackVisible);
    }

    public CadastroTipoAtendimentoTabbedPanel(String id, TipoAtendimentoDTO object, List<ITab> tabs) {
        super(id, object, tabs);
    }

    public CadastroTipoAtendimentoTabbedPanel(String id, List<ITab> tabs) {
        super(id, tabs);
    }

    @Override
    public Class<TipoAtendimentoDTO> getReferenceClass() {
        return TipoAtendimentoDTO.class;
    }

    @Override
    public Object salvar(TipoAtendimentoDTO object) throws DAOException, ValidacaoException {
        if (CollectionUtils.isAllEmpty(object.getLstTipoAtendimento())) {
            throw new ValidacaoException(bundle("msgObrigatorioMenosUmTipoAtendimento"));
        }
        if (CollectionUtils.isAllEmpty(object.getLstNaturezaProcuraTipoAtendimentoDTO())) {
            throw new ValidacaoException(bundle("msgObrigatorioMenosUmNaturezaTipo"));
        }
        return BOFactoryWicket.getBO(AtendimentoFacade.class).salvarTipoAtendimento(object);
    }

    @Override
    public Class getResponsePage() {
        return ConsultarTipoAtendimentoSubtipoPage.class;
    }
}
