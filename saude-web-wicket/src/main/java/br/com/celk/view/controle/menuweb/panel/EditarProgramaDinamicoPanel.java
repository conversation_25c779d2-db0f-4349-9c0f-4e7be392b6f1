package br.com.celk.view.controle.menuweb.panel;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgConfirmacaoOk;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.menu.MenuCache;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.controlemenu.dto.EdicaoMenuDinamicoDTO;
import br.com.celk.controlemenu.dto.MenuDinamicoDTO;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import br.com.celk.template.annotation.PainelControle;
import br.com.celk.util.Coalesce;
import br.com.celk.view.controle.menuweb.CadastroMenuWebDinamico;
import br.com.celk.view.controle.menuweb.panel.dialog.DlgCadastroMenuWeb;
import br.com.celk.view.controle.menuweb.panel.dialog.DlgCadastroModuloWeb;
import br.com.celk.view.controle.menuweb.panel.dialog.DlgCadastroSubmenuWeb;
import br.com.celk.view.controle.menuweb.panel.template.MenuDinamicoCadastroPanel;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.web.MenuWeb;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import static ch.lambdaj.Lambda.on;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
@PainelControle
public class EditarProgramaDinamicoPanel extends MenuDinamicoCadastroPanel {
    
    private Form<EdicaoMenuDinamicoDTO> formEdicao;
    private MenuDinamicoDTO menuDinamicoDTO;
    private WebMarkupContainer containerProgramas;
    private WebMarkupContainer containerCaminhosProgramas;
    private InputField txtCodigoPrograma;
    private InputField txtDescricao;
    private DropDown<MenuWeb> dropDownModulo;
    private DropDown<MenuWeb> dropDownMenu;
    private DropDown<MenuWeb> dropDownSubmenu;
    private DropDown dropDownLayout;
    private Table tblCaminhosProgramas;
    private AbstractAjaxLink btnCadadastroSubmenu;
    private List<MenuWeb> caminhosProgramasList = new ArrayList<MenuWeb>();
    private List<MenuWeb> caminhosProgramasRemovidosList;
    
    private DlgCadastroModuloWeb dlgCadastroModuloWeb;
    private DlgCadastroMenuWeb dlgCadastroMenuWeb;
    private DlgCadastroSubmenuWeb dlgCadastroSubmenuWeb;
    private DlgConfirmacaoOk dlgConfirmacaoOk;
    
    private MenuCache menuCache;
    private MenuWeb caminhoEdicao;

    public EditarProgramaDinamicoPanel(String id) {
        super(id);
        this.caminhosProgramasRemovidosList = new ArrayList<MenuWeb>();
        init();
    }

    private void init() {
        EdicaoMenuDinamicoDTO proxy = on(EdicaoMenuDinamicoDTO.class);
        setOutputMarkupId(true);

        getForm().add(txtDescricao = (InputField) new InputField(path(proxy.getDescricao())).setEnabled(true));
        
        getForm().add(dropDownModulo = (DropDown<MenuWeb>) new DropDown(path(proxy.getModulo())).setEnabled(true));
        dropDownModulo.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                MenuWeb modulo = (MenuWeb) SerializationUtils.clone(dropDownModulo.getComponentValue());

                limparDropDown(target, dropDownMenu);
                limparDropDown(target, dropDownSubmenu);

                getDropDownMenus(modulo);
                target.add(dropDownMenu);

                MenuWeb menu = (MenuWeb) SerializationUtils.clone(dropDownMenu.getComponentValue());

                getDropDownSubmenus(menu);
                target.add(dropDownSubmenu);
            }
        });
        getForm().add(new AbstractAjaxLink("btnCadadastroModulo") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                getDlgCadastrarModuloWeb(target).showDlg(target);
            }
        }).setEnabled(true);
        
        getForm().add(dropDownMenu = (DropDown<MenuWeb>) new DropDown(path(proxy.getMenu())).setEnabled(true));
        dropDownMenu.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if(MenuDinamicoDTO.TipoMenu.PROGRAMA.value().equals(menuDinamicoDTO.getTipoMenu())) {
                    MenuWeb menu = (MenuWeb) SerializationUtils.clone(dropDownMenu.getComponentValue());

                    limparDropDown(target, dropDownSubmenu);

                    getDropDownSubmenus(menu);
                    target.add(dropDownSubmenu);
                }
            }
        });
        getForm().add(new AbstractAjaxLink("btnCadadastroMenu") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                getDlgCadastrarMenuWeb(target).showDlg(target, dropDownModulo.getComponentValue());
            }
        }).setEnabled(true);
        
        getForm().add(dropDownSubmenu = (DropDown<MenuWeb>) new DropDown(path(proxy.getSubmenu())).setEnabled(true));
        getForm().add(btnCadadastroSubmenu = new AbstractAjaxLink("btnCadadastroSubmenu") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                getDlgCadastrarSubmenuWeb(target).showDlg(target, dropDownModulo.getComponentValue(), dropDownMenu.getComponentValue());
            }
        }).setEnabled(true);
        
        getForm().add(dropDownLayout = getDropDownLayoutMenu(path(proxy.getLayoutMenu())));
        
        containerProgramas = new WebMarkupContainer("containerProgramas");
        containerProgramas.setOutputMarkupId(true);
        
        containerProgramas.add(txtCodigoPrograma = new DisabledInputField(path(proxy.getCodigoPrograma())));
        
        containerProgramas.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                EdicaoMenuDinamicoDTO edicaoMenuDinamicoDTO = (EdicaoMenuDinamicoDTO) SerializationUtils.clone((Serializable) form.getModel().getObject());
                adicionar(target, edicaoMenuDinamicoDTO);
            }
        });
        
        containerProgramas.add(new AbstractAjaxButton("btnLimpar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                EdicaoMenuDinamicoDTO edicaoMenuDinamicoDTO = (EdicaoMenuDinamicoDTO) SerializationUtils.clone((Serializable) form.getModel().getObject());
                btnLimpar(target, edicaoMenuDinamicoDTO);
                target.focusComponent(txtDescricao);
            }
        });
        
        getForm().add(containerProgramas);
        
        containerCaminhosProgramas = new WebMarkupContainer("containerCaminhosProgramas");
        containerCaminhosProgramas.setOutputMarkupId(true);
        
        containerCaminhosProgramas.add(tblCaminhosProgramas = new Table("tblCaminhosProgramas", getColumns(), getCollectionProvider()));
        tblCaminhosProgramas.populate();
        
        getForm().add(containerCaminhosProgramas);
        
        getForm().add(new AbstractAjaxButton("btnSalvar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar();
            }
        });
        
        add(getForm());
    }
    
    private DropDown getDropDownLayoutMenu(String id){
        DropDown dropDown = new DropDown(id);
        
        dropDown.addChoice(MenuWeb.LayoutMenu.HOSPITAL.value(), bundle("hospital"));
        dropDown.addChoice(MenuWeb.LayoutMenu.UNIDADE_SAUDE.value(), bundle("unidadeSaude"));
        
        return dropDown;
    }
    
    private void btnLimpar(AjaxRequestTarget target, EdicaoMenuDinamicoDTO dto){
        limpar(target);        
        
        MenuWeb menuWeb = clonarMenuWeb(dto);
        menuWeb.setCodigo(null);
        menuWeb.setVersion(null);
        menuWeb.setDescricao(null);
        formEdicao = null;
        
        getForm().getModelObject().setModulo(menuWeb.getMenuWebPai().getMenuWebPai().getMenuWebPai());
        getForm().getModelObject().setMenu(menuWeb.getMenuWebPai().getMenuWebPai());
        getForm().getModelObject().setSubmenu(menuWeb.getMenuWebPai());
        getForm().getModelObject().setPrograma(menuWeb);
        getForm().getModelObject().setLayoutMenu(menuWeb.getLayoutMenu());
        recarregarObjetos(target);
        limpar(target);
        target.focusComponent(txtDescricao);
        caminhoEdicao = menuWeb;
    }
    
    private MenuWeb clonarMenuWeb(EdicaoMenuDinamicoDTO dto){
        MenuWeb menuWeb = new MenuWeb();
        menuWeb.setDescricao(dto.getDescricao());
        menuWeb.setMenuWebPai(dto.getSubmenu());
        menuWeb.getMenuWebPai().setMenuWebPai(dto.getMenu());
        menuWeb.getMenuWebPai().getMenuWebPai().setMenuWebPai(dto.getModulo());
        menuWeb.setLayoutMenu(dto.getLayoutMenu());
        
        return menuWeb;
    }
    
    private void adicionar(AjaxRequestTarget target, EdicaoMenuDinamicoDTO dto) throws ValidacaoException, DAOException {
        if(dto.getDescricao() == null){
            target.focusComponent(txtDescricao);
            throw new ValidacaoException(bundle("msgInformeDescricao"));
        }
        if(dto.getSubmenu() == null){
            throw new ValidacaoException(bundle("msgSelecioneSubmenu"));
        }
        
        MenuWeb menuWebAdicionar = clonarMenuWeb(dto);
        
        if(getForm().getModel().getObject().getPrograma() != null && getForm().getModel().getObject().getPrograma().getCodigo() != null){
            menuWebAdicionar.setCodigo(getForm().getModel().getObject().getPrograma().getCodigo());
            menuWebAdicionar.setVersion(getForm().getModel().getObject().getPrograma().getVersion());
        }
        
        Integer idx = null;
        for (int i = 0; i < caminhosProgramasList.size(); i++) {
            MenuWeb menuWeb = caminhosProgramasList.get(i);
            
            if (caminhoEdicao != null && (caminhoEdicao.equals(menuWeb) || caminhoEdicao == menuWeb)) {
                idx = i;
            } else if(menuWeb.getDescricao().equals(menuWebAdicionar.getDescricao()) && menuWeb.getLayoutMenu().equals(menuWebAdicionar.getLayoutMenu())){
                if(menuWeb.getMenuWebPai().getMenuWebPai().equals(menuWebAdicionar.getMenuWebPai().getMenuWebPai())){
                    if(menuWeb.getMenuWebPai().getMenuWebPai().getMenuWebPai().equals(menuWebAdicionar.getMenuWebPai().getMenuWebPai().getMenuWebPai())){
                        if(menuWeb.getMenuWebPai().equals(menuWebAdicionar.getMenuWebPai())){
                            throw new ValidacaoException(bundle("msgJaExisteCaminhoDisponivelDescricaoModuloMenuSubmenuLayout"));
                        }   
                    }                    
                }
            } else if(menuWeb.getLayoutMenu().equals(menuWebAdicionar.getLayoutMenu())){
                throw new ValidacaoException(bundle("msgJaExisteCaminhoDisponivelParaEsseLayout"));
            }
        }

        if (caminhoEdicao != null && idx != null) {
            caminhosProgramasList.remove(idx.intValue());
            caminhosProgramasList.add(idx, menuWebAdicionar);
        } else {
            caminhosProgramasList.add(0, menuWebAdicionar);
        }
        
        MenuWeb menuWeb = (MenuWeb) VOUtils.cloneObject(menuWebAdicionar);
        menuWeb.setDescricao(null);
        formEdicao = null;
        
        getForm().getModelObject().setModulo(menuWeb.getMenuWebPai().getMenuWebPai().getMenuWebPai());
        getForm().getModelObject().setMenu(menuWeb.getMenuWebPai().getMenuWebPai());
        getForm().getModelObject().setSubmenu(menuWeb.getMenuWebPai());
        getForm().getModelObject().setPrograma(menuWeb);
        getForm().getModelObject().setLayoutMenu(menuWeb.getLayoutMenu());
        recarregarObjetos(target);
        limpar(target);
        target.focusComponent(txtDescricao);
        tblCaminhosProgramas.update(target);
        caminhoEdicao = menuWeb;
    }
    
    private List<IColumn> getColumns(){
        List<IColumn> columns = new ArrayList<IColumn>();
        
        MenuWeb proxy = on(MenuWeb.class);
        
        columns.add(getActionColumn());
        columns.add(createColumn(bundle("descricao"), proxy.getDescricao()));
        columns.add(createColumn(bundle("modulo"), proxy.getMenuWebPai().getMenuWebPai().getMenuWebPai().getDescricao()));
        columns.add(createColumn(bundle("menu"), proxy.getMenuWebPai().getMenuWebPai().getDescricao()));
        columns.add(createColumn(bundle("submenu"), proxy.getMenuWebPai().getDescricao()));
        columns.add(createColumn(bundle("layout"), proxy.getDescricaoLayoutMenu()));
        
        return columns;
    }
    
    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<MenuWeb>() {
            @Override
            public void customizeColumn(MenuWeb rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<MenuWeb>() {
                    @Override
                    public void action(AjaxRequestTarget target, MenuWeb modelObject) throws ValidacaoException, DAOException {
                        editarCaminho(target, modelObject);                        
                    }
                }).setEnabled(!MenuWeb.LayoutMenu.PADRAO.value().equals(rowObject.getLayoutMenu()));
                addAction(ActionType.REMOVER, rowObject, new IModelAction<MenuWeb>() {
                    @Override
                    public void action(AjaxRequestTarget target, MenuWeb modelObject) throws ValidacaoException, DAOException {
                        removerCaminho(target, modelObject);
                    }
                }).setEnabled(caminhosProgramasList.size() > 1 && !MenuWeb.LayoutMenu.PADRAO.value().equals(rowObject.getLayoutMenu()));
            }
        };
    }
    
    private void editarCaminho(AjaxRequestTarget target, MenuWeb modelObject) {
        MenuWeb menuWeb = (MenuWeb) SerializationUtils.clone(modelObject);
        caminhoEdicao = modelObject;
        formEdicao = null;        
        
        getForm().getModelObject().setDescricao(menuWeb.getDescricao());
        getForm().getModelObject().setModulo(menuWeb.getMenuWebPai().getMenuWebPai().getMenuWebPai());
        getForm().getModelObject().setMenu(menuWeb.getMenuWebPai().getMenuWebPai());
        getForm().getModelObject().setSubmenu(menuWeb.getMenuWebPai());
        getForm().getModelObject().setPrograma(menuWeb);
        getForm().getModelObject().setLayoutMenu(menuWeb.getLayoutMenu());
        recarregarObjetos(target);
    }
    
    private void removerCaminho(AjaxRequestTarget target, MenuWeb modelObject) throws ValidacaoException {        
        caminhoEdicao = null;
        limpar(target);
        
        if(modelObject.getCodigo() != null){
            MenuWeb menuWebRemover = (MenuWeb) SerializationUtils.clone(modelObject);
            caminhosProgramasRemovidosList.add(menuWebRemover);
        }
        
        for (int i = 0; i < caminhosProgramasList.size(); i++) {
            if (caminhosProgramasList.get(i) == modelObject) {
                caminhosProgramasList.remove(i);
                break;
            }
        }
        tblCaminhosProgramas.update(target);
    }
    
    private void limpar(AjaxRequestTarget target){
        txtDescricao.limpar(target);
    }
    
    private ICollectionProvider getCollectionProvider(){
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return caminhosProgramasList;
            }
        };
    }
    
    private DlgCadastroSubmenuWeb getDlgCadastrarSubmenuWeb(AjaxRequestTarget target) {
        if (this.dlgCadastroSubmenuWeb == null) {
            WindowUtil.addModal(target, this, dlgCadastroSubmenuWeb = new DlgCadastroSubmenuWeb(WindowUtil.newModalId(this)) {

                @Override
                public void onSalvar(AjaxRequestTarget target, MenuWeb modulo) throws ValidacaoException, DAOException {
                    salvarNovoMenu(target, modulo, MenuDinamicoDTO.TipoMenu.SUB_MENU.value());
                }
            });
            
        }
        return dlgCadastroSubmenuWeb;
    }
    
    private DlgCadastroMenuWeb getDlgCadastrarMenuWeb(AjaxRequestTarget target) {
        if (this.dlgCadastroMenuWeb == null) {
            WindowUtil.addModal(target, this, dlgCadastroMenuWeb = new DlgCadastroMenuWeb(WindowUtil.newModalId(this)) {

                @Override
                public void onSalvar(AjaxRequestTarget target, MenuWeb modulo) throws ValidacaoException, DAOException {
                    salvarNovoMenu(target, modulo, MenuDinamicoDTO.TipoMenu.MENU.value());
                }
            });
            
        }
        return dlgCadastroMenuWeb;
    }
    
    private DlgCadastroModuloWeb getDlgCadastrarModuloWeb(AjaxRequestTarget target) {
        if (this.dlgCadastroModuloWeb == null) {
            WindowUtil.addModal(target, this, dlgCadastroModuloWeb = new DlgCadastroModuloWeb(WindowUtil.newModalId(this)) {

                @Override
                public void onSalvar(AjaxRequestTarget target, MenuWeb modulo) throws ValidacaoException, DAOException {
                    salvarNovoMenu(target, modulo, MenuDinamicoDTO.TipoMenu.MODULO.value());
                }
            });
            
        }
        return dlgCadastroModuloWeb;
    }
    
    private DropDown getDropDownModulos(AjaxRequestTarget target) {

        for (MenuWeb menuModulo : menuCache.getModulos()) {
            dropDownModulo.addChoice(menuModulo, menuModulo.getDescricao());
        }
        
        if(target != null){
            target.add(dropDownModulo);
        }
        return dropDownModulo;
    }
    
    private DropDown getDropDownMenus(MenuWeb modulo) {
        List<MenuWeb> menuList = menuCache.getChilds(modulo);
        
        if (CollectionUtils.isNotNullEmpty(menuList)) {
            for (MenuWeb menu : menuList) {
                if(dropDownMenu.getComponentValue() == null){
                    dropDownMenu.setComponentValue(menu);                    
                }
                dropDownMenu.addChoice(menu, menu.getDescricao());
            }
        }
        
        return dropDownMenu;
    }
    
    private DropDown getDropDownSubmenus(MenuWeb menu) {
        List<MenuWeb> submenuList = menuCache.getChilds(menu);

        if (CollectionUtils.isNotNullEmpty(submenuList)) {
            for (MenuWeb submenu : submenuList) {
                if(dropDownSubmenu.getComponentValue() == null){
                    dropDownSubmenu.setComponentValue(menu);                    
                }
                dropDownSubmenu.addChoice(submenu, submenu.getDescricao());
            }
        }
        return dropDownSubmenu;
    }
    
    private void limparDropDown(AjaxRequestTarget target, DropDown dropDown){
        if(dropDown != null){
            dropDown.removeAllChoices();
            if(target != null){
                dropDown.limpar(target);
            }
        }
    }
    
    private Form<EdicaoMenuDinamicoDTO> getForm(){
        if(this.formEdicao == null){
            formEdicao = new Form("formEdicao", new CompoundPropertyModel(new EdicaoMenuDinamicoDTO()));
        }
        return this.formEdicao;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDescricao;
    }
    
    private void salvarNovoMenu(AjaxRequestTarget target, MenuWeb menuWeb, Long tipoMenuSalvar) throws DAOException, ValidacaoException{
        if(menuWeb.getDescricaoBundle() == null){
            menuWeb.setDescricaoBundle("");
        }
        if(MenuDinamicoDTO.TipoMenu.MENU.value().equals(tipoMenuSalvar)){
            menuWeb.setMenuWebPai(dropDownModulo.getComponentValue());
        } else if(MenuDinamicoDTO.TipoMenu.SUB_MENU.value().equals(tipoMenuSalvar)){
            menuWeb.setMenuWebPai(dropDownMenu.getComponentValue());
        }
            
        MenuWeb menuWebSave = BOFactoryWicket.save(menuWeb);
        
        menuCache.recarregarMenus();
        
        if(MenuDinamicoDTO.TipoMenu.MODULO.value().equals(tipoMenuSalvar)){
            limparDropDown(target, dropDownModulo);
            getDropDownModulos(target);
            dropDownModulo.setComponentValue(getForm().getModel().getObject().getModulo());
            dropDownModulo.setEnabled(false);
            target.add(dropDownModulo);
        } else if(MenuDinamicoDTO.TipoMenu.MENU.value().equals(tipoMenuSalvar)){
            limparDropDown(target, dropDownMenu);
            getDropDownMenus(dropDownModulo.getComponentValue());
            dropDownMenu.setComponentValue(menuWebSave);
            target.add(dropDownMenu);
        } else if(MenuDinamicoDTO.TipoMenu.SUB_MENU.value().equals(tipoMenuSalvar)){
            limparDropDown(target, dropDownSubmenu);
            getDropDownSubmenus(dropDownMenu.getComponentValue());
            dropDownSubmenu.setComponentValue(menuWebSave);
            target.add(dropDownSubmenu);
        }
        
        initConfirmarNovoMenu(target);
    }
    
    private void initConfirmarNovoMenu(AjaxRequestTarget target){
        if (dlgConfirmacaoOk == null) {
            WindowUtil.addModal(target, this, dlgConfirmacaoOk = new DlgConfirmacaoOk(WindowUtil.newModalId(this), bundle("registro_salvo_sucesso"), "img-info") {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                }
            });
        }
        
        dlgConfirmacaoOk.show(target);
    }
    
    public void setDTO(AjaxRequestTarget target, MenuDinamicoDTO menuDinamicoDTO){
        this.menuDinamicoDTO = menuDinamicoDTO;
        MenuWeb menuWeb = carregarMenuWeb(this.menuDinamicoDTO.getMenuWeb());
        
        formEdicao = null;
        txtDescricao.limpar(target);
        dropDownModulo.limpar(target);
        dropDownMenu.limpar(target);
        dropDownSubmenu.limpar(target);
        dropDownLayout.limpar(target);
        txtCodigoPrograma.limpar(target);
        
        iniciarMenuWeb(menuWeb);
        
        caminhoEdicao = null;
        caminhosProgramasList = new ArrayList<MenuWeb>();
        
        recarregarObjetos(target);
        target.focusComponent(txtDescricao);
    }
    
    private void iniciarMenuWeb(MenuWeb menuWeb){
        getForm().getModel().getObject().setModulo(menuWeb.getMenuWebPai().getMenuWebPai().getMenuWebPai());
        getForm().getModel().getObject().setMenu(menuWeb.getMenuWebPai().getMenuWebPai());
        getForm().getModel().getObject().setSubmenu(menuWeb.getMenuWebPai());
        getForm().getModel().getObject().setLayoutMenu(menuWeb.getLayoutMenu());
        getForm().getModel().getObject().setPrograma(menuWeb);
        getForm().getModel().getObject().setCodigoPrograma(menuWeb.getProgramaWeb().getCodigo());
    }
    
    private MenuWeb carregarMenuWeb(MenuWeb menuWeb){
        return LoadManager.getInstance(MenuWeb.class)
            .addProperties(new HQLProperties(MenuWeb.class).getProperties())
            .addProperties(new HQLProperties(MenuWeb.class, MenuWeb.PROP_MENU_MODULO).getProperties())
            .addProperties(new HQLProperties(MenuWeb.class, MenuWeb.PROP_MENU_WEB_PAI).getProperties())
            .addProperties(new HQLProperties(MenuWeb.class, VOUtils.montarPath(MenuWeb.PROP_MENU_WEB_PAI, MenuWeb.PROP_MENU_WEB_PAI)).getProperties())
            .addProperties(new HQLProperties(MenuWeb.class, VOUtils.montarPath(MenuWeb.PROP_MENU_WEB_PAI, MenuWeb.PROP_MENU_WEB_PAI, MenuWeb.PROP_MENU_WEB_PAI)).getProperties())
            .addProperties(new HQLProperties(ProgramaWeb.class, MenuWeb.PROP_PROGRAMA_WEB).getProperties())
            .addParameter(new QueryCustom.QueryCustomParameter(MenuWeb.PROP_CODIGO, menuWeb.getCodigo()))
            .start().getVO();
    }
    
    private void recarregarObjetos(AjaxRequestTarget target){
        if(this.menuDinamicoDTO != null){
            menuCache = MenuCache.get();
            menuCache.recarregarMenus();
            
            EdicaoMenuDinamicoDTO edicaoDTO = getForm().getModel().getObject();

            limparDropDown(target, dropDownModulo);
            limparDropDown(target, dropDownMenu);
            limparDropDown(target, dropDownSubmenu);
            
            btnCadadastroSubmenu.setEnabled(true);
            containerProgramas.setVisible(MenuDinamicoDTO.TipoMenu.PROGRAMA.value().equals(menuDinamicoDTO.getTipoMenu()));
            containerCaminhosProgramas.setVisible(MenuDinamicoDTO.TipoMenu.PROGRAMA.value().equals(menuDinamicoDTO.getTipoMenu()));
            
            if(!MenuWeb.LayoutMenu.PADRAO.value().equals(edicaoDTO.getLayoutMenu())){
                txtDescricao.setComponentValue(edicaoDTO.getDescricao());
            }
            dropDownLayout.setComponentValue(edicaoDTO.getLayoutMenu());
            
            if(MenuDinamicoDTO.TipoMenu.PROGRAMA.value().equals(menuDinamicoDTO.getTipoMenu())) {
                dropDownModulo.setComponentValue(edicaoDTO.getModulo());
                dropDownMenu.setComponentValue(edicaoDTO.getMenu());
                dropDownSubmenu.setComponentValue(edicaoDTO.getSubmenu());

                getDropDownMenus(dropDownModulo.getComponentValue());
                getDropDownSubmenus(dropDownMenu.getComponentValue());

                if(CollectionUtils.isEmpty(caminhosProgramasList)){
                    carregarCaminhosDisponiveis(target, edicaoDTO.getPrograma());
                    formEdicao = null;
                    if(!MenuWeb.LayoutMenu.PADRAO.value().equals(edicaoDTO.getLayoutMenu())){
                        getForm().getModel().setObject((EdicaoMenuDinamicoDTO) SerializationUtils.clone(edicaoDTO));
                        caminhoEdicao = edicaoDTO.getPrograma();
                    }
                }
            }

            getDropDownModulos(null);
            if(edicaoDTO.getPrograma() != null && edicaoDTO.getPrograma().getProgramaWeb() != null){
                txtCodigoPrograma.setComponentValue(edicaoDTO.getPrograma().getProgramaWeb().getCodigo());                    
            }

            target.add(txtDescricao);
            target.add(dropDownLayout);
            target.add(txtCodigoPrograma);
        }
    }
    
    private void carregarCaminhosDisponiveis(AjaxRequestTarget target, MenuWeb menuWeb){
        caminhosProgramasList = LoadManager.getInstance(MenuWeb.class)
            .addProperties(new HQLProperties(MenuWeb.class).getProperties())
            .addProperties(new HQLProperties(MenuWeb.class, MenuWeb.PROP_MENU_MODULO).getProperties())
            .addProperties(new HQLProperties(MenuWeb.class, MenuWeb.PROP_MENU_WEB_PAI).getProperties())
            .addProperties(new HQLProperties(MenuWeb.class, VOUtils.montarPath(MenuWeb.PROP_MENU_WEB_PAI, MenuWeb.PROP_MENU_WEB_PAI)).getProperties())
            .addProperties(new HQLProperties(MenuWeb.class, VOUtils.montarPath(MenuWeb.PROP_MENU_WEB_PAI, MenuWeb.PROP_MENU_WEB_PAI, MenuWeb.PROP_MENU_WEB_PAI)).getProperties())
            .addProperties(new HQLProperties(ProgramaWeb.class, MenuWeb.PROP_PROGRAMA_WEB).getProperties())
            .addParameter(new QueryCustom.QueryCustomParameter(MenuWeb.PROP_PROGRAMA_WEB, menuWeb.getProgramaWeb()))
            .start().getList();
        
        if(target != null){
            tblCaminhosProgramas.update(target);
        }
    }
    
    private void salvar() throws ValidacaoException, DAOException{
        EdicaoMenuDinamicoDTO dto = getForm().getModel().getObject();
        
        if(CollectionUtils.isEmpty(caminhosProgramasList)){
            throw new ValidacaoException(bundle("msgNaoPossivelRemoverPoisNecessarioPeloMenosUmCaminhoDisponivelProgramaX", txtCodigoPrograma.getComponentValue()));
        }
            
        dto.setCaminhosProgramasList(caminhosProgramasList);
        dto.setCaminhosProgramasRemovidosList(caminhosProgramasRemovidosList);
        dto.setTipoMenu(menuDinamicoDTO.getTipoMenu());
        dto.setCodigoPrograma(Coalesce.asLong((Long) txtCodigoPrograma.getComponentValue()));
        
        BOFactoryWicket.getBO(BasicoFacade.class).salvarEdicaoMenuDinamico(dto);
        
        CadastroMenuWebDinamico page = new CadastroMenuWebDinamico();
        page.info(bundle("menuSalvoComSucesso"));
        setResponsePage(page);
    }
    
}