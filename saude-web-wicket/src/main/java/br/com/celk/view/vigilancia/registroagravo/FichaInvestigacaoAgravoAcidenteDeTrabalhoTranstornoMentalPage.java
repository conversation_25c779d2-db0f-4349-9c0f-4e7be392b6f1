package br.com.celk.view.vigilancia.registroagravo;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.celk.view.vigilancia.registroagravo.enums.*;
import br.com.celk.view.vigilancia.registroagravo.panel.FichaInvestigacaoAgravoBasePage;
import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoAcidenteTrabalhoTranstornoMentalDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoAcidenteTrabalhoTranstornoMental;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDermatoseOcupacionalEnum;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR> Lucas
 */

public class FichaInvestigacaoAgravoAcidenteDeTrabalhoTranstornoMentalPage extends FichaInvestigacaoAgravoBasePage {

    private InvestigacaoAgravoAcidenteTrabalhoTranstornoMental investigacaoAgravo;

    //CONTAINER INVESTIGAÇÃO
    private WebMarkupContainer containerInvestigacao;
    private DateChooser dataInvestigacao;
    private DisabledInputField ocupacaoCbo;

    //CONTAINER ANTECEDENTES EPIDEMIOLÓGICOS
    private WebMarkupContainer containerAntecedentesEpidemiologicos;
    private DropDown ddSituacaoMercadoTrabalho;
    private InputField txtTempoTrabalhado;
    private DropDown ddUnidadeDeTempo;
    private AutoCompleteConsultaEmpresa acEmpresa;
    private DisabledInputField empresaCpfOUcnpj;
    private DisabledInputField empresaatividadeEconomicaCNAE;
    private DisabledInputField empresaMunicipio;
    private DisabledInputField empresaUF;
    private DisabledInputField empresaCodigoIBGE;
    private DisabledInputField empresaDistrito;
    private DisabledInputField empresaBairro;
    private DisabledInputField empresaEndereco;
    private DisabledInputField empresaNumero;
    private DisabledInputField empresaPontoDeReferencia;
    private DisabledInputField empresaTelefone;
    private DropDown ddEmpregadorTerceirizada;

    //CONTAINER TRANSTORNOS MENTAIS
    private WebMarkupContainer containerTranstornosMentais;
    private DropDown ddTempoDeExposicaoAgenteRisco;
    private InputField txtTempoDeExposicaoAgenteRIsco;
    private DropDown ddRegimeTratamento;
    private AutoCompleteConsultaCid acDiagnosticoEspecifico;
    private DropDown ddHabitoAlcool;
    private DropDown ddHabitoDrogasPsicoativas;
    private DropDown ddHabitoPsicofarmacos;
    private DropDown ddHabitoFumar;
    private DropDown ddTempoExposicaoTabaco;
    private InputField txtTempoExposicaoTabaco;

    //CONTAINER CONCLUSÃO
    private WebMarkupContainer containerConclusao;
    private DropDown ddAfastamentoSituacaoDesgasteMental;
    private DropDown ddAdocaoProtecaoIndividual;
    private DropDown ddAdocaoMudancaOrganizacaoTrabalho;
    private DropDown ddNenhuma;
    private DropDown ddAdocaoPotecaoColetiva;
    private DropDown ddAfastamenteLocalTrabalho;
    private InputField txtOutros;
    private DropDown ddHaOutrosComMesmaDoenca;
    private DropDown ddPacienteCAPES;
    private DropDown ddEvolucaoDocaso;
    private DateChooser dataObito;
    private DropDown ddComunicacaoAcidenteTrabalho;


    //CONTAINER OBSERVAÇÃO
    private WebMarkupContainer containerObservacao;
    private InputArea txtObservacao;

    //CONTAINER ENCERRAMENTO
    private WebMarkupContainer containerEncerramento;
    private DisabledInputField usuarioEncerramento;
    private DisabledInputField dataEncerramento;


    public FichaInvestigacaoAgravoAcidenteDeTrabalhoTranstornoMentalPage(Long idRegistroAgravo, boolean modoLeitura) {
        super(idRegistroAgravo, modoLeitura);
    }


    @Override
    public void inicializarFicha() {
        InvestigacaoAgravoAcidenteTrabalhoTranstornoMental proxy = on(InvestigacaoAgravoAcidenteTrabalhoTranstornoMental.class);

        criarContainerInvestigacao(proxy);
        criarContainerAntecedentesEpidemiologicos(proxy);
        criarContainerTranstornosMentais(proxy);
        criarContainerConclusão(proxy);
        criarContainerObservacao(proxy);
        criarContainerEncerramento(proxy);
    }

    @Override
    public void inicializarRegrasComponentes(AjaxRequestTarget target) {
        carregarAntecedentesEpidemiologicos();
        carregarConclusao();
    }



    @Override
    public void inicializarForm() {
        if (investigacaoAgravo == null) {
            investigacaoAgravo = new InvestigacaoAgravoAcidenteTrabalhoTranstornoMental();
            investigacaoAgravo.setRegistroAgravo(getAgravo());
            investigacaoAgravo.setFlagInformacoesComplementares(RepositoryComponentDefault.SIM);
        }

        TabelaCbo tabelaCbo = FichaInvestigacaoAgravoHelper.getTabelaCboByCodUser(investigacaoAgravo.getRegistroAgravo().getUsuarioCadsus().getCodigo());
        if (tabelaCbo != null) {
            investigacaoAgravo.getRegistroAgravo().getUsuarioCadsus().setTabelaCbo(tabelaCbo);
            investigacaoAgravo.setOcupacaoCbo(tabelaCbo);
        }


        setForm(new Form("form", new CompoundPropertyModel(investigacaoAgravo)));
    }

    @Override
    public void carregarFicha() {
        investigacaoAgravo = InvestigacaoAgravoAcidenteTrabalhoTranstornoMental.buscaPorRegistroAgravo(getAgravo());
    }

    @Override
    public Object getFichaDTO() {
        FichaInvestigacaoAgravoAcidenteTrabalhoTranstornoMentalDTO fichaDTO = new FichaInvestigacaoAgravoAcidenteTrabalhoTranstornoMentalDTO();
        fichaDTO.setRegistroAgravo(getAgravo());
        fichaDTO.setInvestigacaoAgravoAcidenteTrabalhoTranstornoMental(investigacaoAgravo);
        return fichaDTO;
    }

    @Override
    public String carregarInformacoesComplementares() {
        return investigacaoAgravo.getFlagInformacoesComplementares() == null
                ? "S"
                : investigacaoAgravo.getFlagInformacoesComplementares();
    }

    @Override
    public void validarFicha() throws ValidacaoException {

    }

    @Override
    public void salvarFicha(Object fichaDTO) throws ValidacaoException, DAOException {
        FichaInvestigacaoAgravoAcidenteTrabalhoTranstornoMentalDTO dto = (FichaInvestigacaoAgravoAcidenteTrabalhoTranstornoMentalDTO) fichaDTO;
        validarFicha();
        BOFactoryWicket.getBO(VigilanciaFacade.class).salvarFichaInvestigacaoAgravoAcidenteTrabalhoTranstornoMental(dto);
        Page page = new ConsultaRegistroAgravoPage();
        setResponsePage(page);
    }

    @Override
    public Object getEncerrarFichaDTO() {
        FichaInvestigacaoAgravoAcidenteTrabalhoTranstornoMentalDTO fichaDTO = (FichaInvestigacaoAgravoAcidenteTrabalhoTranstornoMentalDTO) getFichaDTO();

        if (investigacaoAgravo.getUsuarioEncerramento() == null) {
            Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
            investigacaoAgravo.setUsuarioEncerramento(usuarioLogado);
            investigacaoAgravo.setDataEncerramento(DataUtil.getDataAtual());
        }

        fichaDTO.setEncerrarFicha(true);
        return fichaDTO;
    }

    @Override
    public void atualizarCampoInformacoesComplementares(String flag) {
        investigacaoAgravo.setFlagInformacoesComplementares(flag);
    }


    //CRIAÇÃO DOS CONTAINERS
    private void criarContainerInvestigacao(InvestigacaoAgravoAcidenteTrabalhoTranstornoMental proxy) {
        containerInvestigacao = new WebMarkupContainer("containerInvestigacao");
        containerInvestigacao.setOutputMarkupId(true);

        dataInvestigacao = new DateChooser(path(proxy.getDataInvestigacao()));
        dataInvestigacao.setRequired(true);
        dataInvestigacao.addRequiredClass();

        dataInvestigacao.getData().setMinDate(new DateOption(investigacaoAgravo.getRegistroAgravo().getDataRegistro()));
        dataInvestigacao.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        ocupacaoCbo = new DisabledInputField(path(proxy.getOcupacaoCbo().getDescricao()));

        containerInvestigacao.add(dataInvestigacao, ocupacaoCbo);
        getContainerInformacoesComplementares().add(containerInvestigacao);
    }

    private void criarContainerAntecedentesEpidemiologicos(InvestigacaoAgravoAcidenteTrabalhoTranstornoMental proxy) {
        containerAntecedentesEpidemiologicos = new WebMarkupContainer("containerAntecedentesEpidemiologicos");
        containerAntecedentesEpidemiologicos.setOutputMarkupId(true);

        ddSituacaoMercadoTrabalho = DropDownUtil.getIEnumDropDown(path(proxy.getSituacaoMercadoTrabalho()), SituacaoMercadoTrabalhoEnum.values());
        txtTempoTrabalhado = new InputField(path(proxy.getTempoMercadoTrabalho()));
        ddUnidadeDeTempo = DropDownUtil.getIEnumDropDown(path(proxy.getTempoMercadoTrabalhoUm()), MedidaTempoTrabalhoEnum.values());

        //DADOS EMPRESA
        acEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEmpresaContratante()));
        empresaCpfOUcnpj = new DisabledInputField(path(proxy.getEmpresaContratante().getCnpjFormatado()));
        empresaatividadeEconomicaCNAE = new DisabledInputField(path(proxy.getEmpresaContratante().getAtividade().getDescricao()));
        empresaMunicipio = new DisabledInputField(path(proxy.getEmpresaContratante().getCidade().getDescricao()));
        empresaUF = new DisabledInputField(path(proxy.getEmpresaContratante().getCidade().getEstado().getSigla()));
        empresaCodigoIBGE = new DisabledInputField(path(proxy.getEmpresaContratante().getCidade().getCodigo()));
        empresaDistrito = new DisabledInputField(path(proxy.getEmpresaContratante().getDistritoSanitario()));
        empresaBairro = new DisabledInputField(path(proxy.getEmpresaContratante().getBairro()));
        empresaEndereco = new DisabledInputField(path(proxy.getEmpresaContratante().getEnderecoFormatado()));
        empresaNumero = new DisabledInputField(path(proxy.getEmpresaContratante().getNumero()));
        empresaPontoDeReferencia = new DisabledInputField(path(proxy.getEmpresaContratante().getReferencia()));
        empresaTelefone = new DisabledInputField(path(proxy.getEmpresaContratante().getTelefoneFormatado()));
        ddEmpregadorTerceirizada = DropDownUtil.getIEnumDropDown(path(proxy.getEmpregadorEmpresaTerceirizada()), EmpregadorTercerizadoEnum.values());

        containerAntecedentesEpidemiologicos.add(
                ddSituacaoMercadoTrabalho, txtTempoTrabalhado, ddUnidadeDeTempo,
                acEmpresa, empresaCpfOUcnpj, empresaatividadeEconomicaCNAE,
                empresaMunicipio, empresaUF, empresaCodigoIBGE,
                empresaDistrito, empresaBairro, empresaNumero,
                empresaPontoDeReferencia, empresaTelefone, ddEmpregadorTerceirizada,
                empresaEndereco
        );
        getContainerInformacoesComplementares().add(containerAntecedentesEpidemiologicos);
    }

    private void criarContainerTranstornosMentais(InvestigacaoAgravoAcidenteTrabalhoTranstornoMental proxy) {
        containerTranstornosMentais = new WebMarkupContainer("containerTranstornosMentais");
        containerTranstornosMentais.setOutputMarkupId(true);

        ddTempoDeExposicaoAgenteRisco = DropDownUtil.getIEnumDropDown(path(proxy.getTempoExposicaoAgenteRiscoUnidadeMedida()), MedidaTempoTrabalhoEnum.values());
        txtTempoDeExposicaoAgenteRIsco = new InputField(path(proxy.getTempoExposicaoAgenteRisco()));
        ddRegimeTratamento = DropDownUtil.getIEnumDropDown(path(proxy.getRegimeTratamento()), RegimeTratamentoEnum.values());
        acDiagnosticoEspecifico = new AutoCompleteConsultaCid(path(proxy.getCidDiagnosticoEspecifico()));
        ddHabitoAlcool = DropDownUtil.getIEnumDropDown(path(proxy.getHabitosAlcool()), SimNaoIgnoradoEnum.values());
        ddHabitoDrogasPsicoativas = DropDownUtil.getIEnumDropDown(path(proxy.getHabitosDrogasPsicoativas()), SimNaoIgnoradoEnum.values());
        ddHabitoPsicofarmacos = DropDownUtil.getIEnumDropDown(path(proxy.getHabitosPsicofarmacos()), SimNaoIgnoradoEnum.values());
        ddHabitoFumar = DropDownUtil.getIEnumDropDown(path(proxy.getHabitosFumar()), ExFumanteEnum.values());
        ddTempoExposicaoTabaco = DropDownUtil.getIEnumDropDown(path(proxy.getTempoExposicaoTabacoUnidadeMedida()), MedidaTempoTrabalhoEnum.values());
        txtTempoExposicaoTabaco = new InputField(path(proxy.getTempoExposicaoTabaco()));

        acDiagnosticoEspecifico.setRequired(true);

        containerTranstornosMentais.add(
                ddTempoDeExposicaoAgenteRisco,
                txtTempoDeExposicaoAgenteRIsco,
                ddRegimeTratamento,
                acDiagnosticoEspecifico,
                ddHabitoAlcool,
                ddHabitoDrogasPsicoativas,
                ddHabitoPsicofarmacos,
                ddHabitoFumar,
                ddTempoExposicaoTabaco,
                txtTempoExposicaoTabaco
        );
        getContainerInformacoesComplementares().add(containerTranstornosMentais);
    }

    private void criarContainerConclusão(InvestigacaoAgravoAcidenteTrabalhoTranstornoMental proxy) {
        containerConclusao = new WebMarkupContainer("containerConclusao");
        containerConclusao.setOutputMarkupId(true);

        ddAfastamentoSituacaoDesgasteMental = DropDownUtil.getIEnumDropDown(path(proxy.getCondutaGeralAfastamentoSituacaoDesgasteMental()), SimOuNaoEnum.values());
        ddAdocaoProtecaoIndividual = DropDownUtil.getIEnumDropDown(path(proxy.getCondutaGeralProtecaoIndividual()), SimOuNaoEnum.values());
        ddAdocaoMudancaOrganizacaoTrabalho = DropDownUtil.getIEnumDropDown(path(proxy.getCondutaGeralMudancaOrganizacaoTrabalho()), SimOuNaoEnum.values());
        ddNenhuma = DropDownUtil.getIEnumDropDown(path(proxy.getCondutaGeralNenhum()), SimOuNaoEnum.values());
        ddAdocaoPotecaoColetiva = DropDownUtil.getIEnumDropDown(path(proxy.getCondutaGeralProtecaoColetiva()), SimOuNaoEnum.values());
        ddAfastamenteLocalTrabalho = DropDownUtil.getIEnumDropDown(path(proxy.getCondutaGeralAfastamentoLocalTrabalho()), SimOuNaoEnum.values());
        txtOutros = new InputField(path(proxy.getCondutaGeralOutros()));

        ddHaOutrosComMesmaDoenca = DropDownUtil.getIEnumDropDown(path(proxy.getOutrosTrabalhadoresMesmaDoencaLocalTrabalho()), SimNaoIgnoradoEnum.values());
        ddPacienteCAPES = DropDownUtil.getIEnumDropDown(path(proxy.getPacienteEncaminhadoCapes()), SimNaoIgnoradoEnum.values());
        ddEvolucaoDocaso = DropDownUtil.getIEnumDropDown(path(proxy.getEvolucaoCaso()), InvestigacaoAgravoDermatoseOcupacionalEnum.EvolucaoCasoEnum.values());

        dataObito = new DateChooser(path(proxy.getDataObito()));
        dataObito.setEnabled(false);
        dataObito.getData().setMinDate(new DateOption(investigacaoAgravo.getRegistroAgravo().getDataRegistro()));
        dataObito.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        ddComunicacaoAcidenteTrabalho = DropDownUtil.getIEnumDropDown(path(proxy.getEmitidaCAT()), SimNaoSeAplicaEnum.values());
        ddComunicacaoAcidenteTrabalho.setRequired(true);
        ddComunicacaoAcidenteTrabalho.addRequiredClass();

        containerConclusao.add(
                ddAfastamentoSituacaoDesgasteMental, ddAdocaoProtecaoIndividual, ddAdocaoMudancaOrganizacaoTrabalho,
                ddNenhuma, ddAdocaoPotecaoColetiva, ddAfastamenteLocalTrabalho,
                txtOutros, ddHaOutrosComMesmaDoenca, ddPacienteCAPES,
                ddEvolucaoDocaso, dataObito, ddComunicacaoAcidenteTrabalho
        );
        getContainerInformacoesComplementares().add(containerConclusao);
    }

    private void criarContainerObservacao(InvestigacaoAgravoAcidenteTrabalhoTranstornoMental proxy) {
        containerObservacao = new WebMarkupContainer("containerObservacao");
        containerObservacao.setOutputMarkupId(true);

        txtObservacao = new InputArea(path(proxy.getObservacao()));


        containerObservacao.add(txtObservacao);
        getContainerInformacoesComplementares().add(containerObservacao);
    }

    private void criarContainerEncerramento(InvestigacaoAgravoAcidenteTrabalhoTranstornoMental proxy) {
        containerEncerramento = new WebMarkupContainer("containerEncerramento");
        containerEncerramento.setOutputMarkupId(true);

        usuarioEncerramento = new DisabledInputField(path(proxy.getUsuarioEncerramento()));
        dataEncerramento = new DisabledInputField(path(proxy.getDataEncerramento()));

        containerEncerramento.add(usuarioEncerramento, dataEncerramento);
        getContainerInformacoesComplementares().add(containerEncerramento);
    }

    //CARREGAR REGRAS
    private void carregarAntecedentesEpidemiologicos() {
        acEmpresa.add(new ConsultaListener<Empresa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa empresa) {
                if (empresa != null) {
                    empresaCpfOUcnpj.setComponentValue(empresa.getCnpjFormatado());
                    empresaatividadeEconomicaCNAE.setComponentValue(empresa.getAtividade().getDescricao());
                    empresaMunicipio.setComponentValue(empresa.getCidade().getDescricao());
                    empresaCodigoIBGE.setComponentValue(empresa.getCidade().getCodigo());
                    empresaBairro.setComponentValue(empresa.getBairro());
                    empresaNumero.setComponentValue(empresa.getNumero());
                    empresaTelefone.setComponentValue(empresa.getTelefoneFormatado());
                    empresaEndereco.setComponentValue(empresa.getEnderecoFormatado());
                    empresaPontoDeReferencia.setComponentValue(null);

                    if (empresa.getEnderecoEstruturadoDistrito() != null && empresa.getEnderecoEstruturadoDistrito().getDescricao() != null) {
                        empresaDistrito.setComponentValue(empresa.getEnderecoEstruturadoDistrito().getDescricao());
                    }

                    Cidade cidadeTemp = FichaInvestigacaoAgravoHelper.getCidadeByCodigo(empresa.getCidade().getCodigo());
                    if (cidadeTemp != null && cidadeTemp.getEstado() != null && cidadeTemp.getEstado().getSigla() != null) {
                        empresaUF.setComponentValue(cidadeTemp.getEstado().getSigla());
                    }
                }

                target.add(empresaCpfOUcnpj, empresaatividadeEconomicaCNAE, empresaMunicipio, empresaUF, empresaCodigoIBGE,
                        empresaDistrito, empresaBairro, empresaNumero, empresaTelefone, empresaEndereco, empresaPontoDeReferencia);
            }
        });

        acEmpresa.add(new RemoveListener<Empresa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa empresa) {
                empresaCpfOUcnpj.limpar(target);
                empresaatividadeEconomicaCNAE.limpar(target);
                empresaMunicipio.limpar(target);
                empresaUF.limpar(target);
                empresaCodigoIBGE.limpar(target);
                empresaDistrito.limpar(target);
                empresaBairro.limpar(target);
                empresaNumero.limpar(target);
                empresaTelefone.limpar(target);
                empresaPontoDeReferencia.limpar(target);
                empresaEndereco.limpar(target);
            }
        });
    }

    private void carregarConclusao() {
        ddEvolucaoDocaso.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {
                boolean erObito = !isModoLeitura() && (
                        FichaInvestigacaoAgravoHelper.isLongTrue(ddEvolucaoDocaso, InvestigacaoAgravoDermatoseOcupacionalEnum.EvolucaoCasoEnum.OBITO_DOENCA.value()) ||
                                FichaInvestigacaoAgravoHelper.isLongTrue(ddEvolucaoDocaso, InvestigacaoAgravoDermatoseOcupacionalEnum.EvolucaoCasoEnum.OBITO_OUTRA_CAUSA.value()));

                FichaInvestigacaoAgravoHelper.enableDisableDates(dataObito, erObito, erObito, ajaxRequestTarget);
                ajaxRequestTarget.add(dataObito);
            }
        });
    }

}
