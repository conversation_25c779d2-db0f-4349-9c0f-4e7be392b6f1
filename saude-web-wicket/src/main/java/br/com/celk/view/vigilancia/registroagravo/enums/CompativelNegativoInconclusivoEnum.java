package br.com.celk.view.vigilancia.registroagravo.enums;

import br.com.ksisolucoes.enums.IEnum;

public enum CompativelNegativoInconclusivoEnum implements IEnum<SimNaoEnum> {
    COMPATIVEL(1L, "Compatível"),
    NEGATIVO(2L, "Negativo"),
    INCONCLUSIVO(3L, "Inconclusivo"),
    NAO_REALIZADO(4L, "Não Realizado");

    private Long value;
    private String descricao;

    CompativelNegativoInconclusivoEnum(Long value, String descricao) {
        this.value = value;
        this.descricao = descricao;
    }

    @Override
    public Long value() {
        return value;
    }

    @Override
    public String descricao() {
        return descricao;
    }

    public static CompativelNegativoInconclusivoEnum valueOf(Long value) {
        for (CompativelNegativoInconclusivoEnum v : CompativelNegativoInconclusivoEnum.values()) {
            if (v.value().equals(value)) {
                return v;
            }
        }
        return null;
    }
}
