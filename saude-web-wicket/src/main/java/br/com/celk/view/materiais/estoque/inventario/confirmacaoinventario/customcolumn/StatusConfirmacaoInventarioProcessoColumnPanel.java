package br.com.celk.view.materiais.estoque.inventario.confirmacaoinventario.customcolumn;

import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.vo.entradas.estoque.ConfirmacaoInventarioProcesso;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public class StatusConfirmacaoInventarioProcessoColumnPanel extends Panel{

    private final ConfirmacaoInventarioProcesso confirmacaoInventarioProcesso;
    
    private WebMarkupContainer img;
    
    public StatusConfirmacaoInventarioProcessoColumnPanel(String id, ConfirmacaoInventarioProcesso confirmacaoInventarioProcesso) {
        super(id);
        this.confirmacaoInventarioProcesso = confirmacaoInventarioProcesso;
        init();
    }

    private void init() {
        StatusConfirmacaoInventario statusConfirmacaoInventario = null ;
        if (confirmacaoInventarioProcesso.getAsyncProcess().getStatus().equals(AsyncProcess.STATUS_PROCESSANDO)) {
            statusConfirmacaoInventario = StatusConfirmacaoInventario.PROCESSANDO;
        } else if (confirmacaoInventarioProcesso.getAsyncProcess().getStatus().equals(AsyncProcess.STATUS_CONCLUIDO_ERRO)) {
            statusConfirmacaoInventario = StatusConfirmacaoInventario.CONCLUIDO_ERRO;
        } else if (confirmacaoInventarioProcesso.getAsyncProcess().getStatus().equals(AsyncProcess.STATUS_CONCLUIDO_EXITO)) {
            statusConfirmacaoInventario = StatusConfirmacaoInventario.CONCLUIDO_EXITO;
        } else if (confirmacaoInventarioProcesso.getAsyncProcess().getStatus().equals(AsyncProcess.STATUS_AGUARDANDO_PROCESSAMENTO)) {
            statusConfirmacaoInventario = StatusConfirmacaoInventario.AGUARDANDO_PROCESSAMENTO;
        }
        
        add(img = new WebMarkupContainer("img"));
        
        if (statusConfirmacaoInventario!=null) {
            img.add(new AttributeModifier("class", statusConfirmacaoInventario.icon()));
            img.add(new AttributeModifier("title", statusConfirmacaoInventario.title()));
        }
    }
    
    public enum StatusConfirmacaoInventario {

    CONCLUIDO_EXITO("hudson blue", BundleManager.getString("concluido")),
    AGUARDANDO_PROCESSAMENTO("celk-icon loading", BundleManager.getString("aguardandoProcessamento")),
    CONCLUIDO_ERRO("hudson red", BundleManager.getString("erro")),
    PROCESSANDO("celk-icon loading", BundleManager.getString("processando"));
    private final String icon;
    private final String title;

    private StatusConfirmacaoInventario(String icon, String title) {
        this.icon = icon;
        this.title = title;
    }

    public String icon() {
        return icon;
    }

    public String title() {
        return title;
    }
}
    
}
