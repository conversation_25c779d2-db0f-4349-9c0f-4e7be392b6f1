package br.com.celk.view.vigilancia.rotinas;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.PnlRequerimentoVigilanciaAnexoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaAnexoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class DlgRequerimentoVigilanciaAnexo<T extends Serializable> extends Window {

    private T object;
    private PnlRequerimentoVigilanciaAnexoDTO pnlRequerimentoVigilanciaAnexoDTO;
    private PnlRequerimentoVigilanciaAnexoDialog pnlRequerimentoVigilanciaAnexoDialog;

    public DlgRequerimentoVigilanciaAnexo(String id, PnlRequerimentoVigilanciaAnexoDTO pnlRequerimentoVigilanciaAnexoDTO, boolean descricaoObrigatoria) {
        super(id);
        this.pnlRequerimentoVigilanciaAnexoDTO = pnlRequerimentoVigilanciaAnexoDTO;
        init(descricaoObrigatoria);
    }

    private void init(boolean descricaoObrigatoria) {
        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("anexos");
            }
        });

        setInitialWidth(1024);
        setInitialHeight(450);
        setResizable(true);

        setContent(pnlRequerimentoVigilanciaAnexoDialog = new PnlRequerimentoVigilanciaAnexoDialog(getContentId(), pnlRequerimentoVigilanciaAnexoDTO, true, false, true, descricaoObrigatoria) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOExcluidoList, String descricaoOcorrencia) throws ValidacaoException, DAOException {
                close(target);
                DlgRequerimentoVigilanciaAnexo.this.onConfirmar(target, requerimentoVigilanciaAnexoDTOList, requerimentoVigilanciaAnexoDTOExcluidoList, descricaoOcorrencia);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
                DlgRequerimentoVigilanciaAnexo.this.onFechar(target);
            }
        });
    }

    public PnlRequerimentoVigilanciaAnexoDTO getDto() {
        return this.pnlRequerimentoVigilanciaAnexoDTO;
    }

    public void show(AjaxRequestTarget target, T object) {
        super.show(target);
        this.object = object;
    }

    public T getObject() {
        return object;
    }

    public abstract void onConfirmar(AjaxRequestTarget target, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOExcluidoList, String descricaoOcorrencia) throws ValidacaoException, DAOException;

    public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {}

}