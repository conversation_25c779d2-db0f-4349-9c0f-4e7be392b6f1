package br.com.celk.view.agenda.agendamento.tfd.encaminhamentopedido;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.AbstractActionLinkPanel;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.treetable.pageable.PageableTreeTable;
import br.com.celk.component.treetable.pageable.customdatatable.CustomTreeColumn;
import br.com.celk.component.treetable.pageable.customdatatable.ICustomColumnTreeTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.AutoCompleteConsultaTipoProcedimento;
import br.com.celk.view.unidadesaude.esus.planejamentovisitadomicilio.dlg.DlgConfirmarImpressaoSimNao;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.tfd.interfaces.facade.TfdFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.agendamento.interfaces.facade.AgendamentoReportFacade;
import br.com.ksisolucoes.report.encaminhamento.dto.RelatorioEncaminhadosRegionalDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.EloPedidoTfdLoteEncaminhamentoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.grid.ICellPopulator;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.tree.table.NodeBorder;
import org.apache.wicket.extensions.markup.html.repeater.tree.table.NodeModel;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaEncaminhamentoPedidoPage extends BasePage {

    private Form form;

    private String nomePaciente;
    private TipoProcedimento tipoProcedimento;
    private String numeroPedido;
    private Long lote;
    private DatePeriod periodo;
    private PnlChoicePeriod pnlChoicePeriod;
    private DropDown dropDownStatus;
    private Long status;
    private PageableTreeTable tableTree;
    private final EncaminhamentoPedidoTreeProvider provider = new EncaminhamentoPedidoTreeProvider();
    private DlgConfirmarImpressaoSimNao dlgConfirmarImpressaoSimNao;

    private WebMarkupContainer containerTree;

    private DlgMotivoCancelamentoEncaminhamentoTfd dlgMotivoCancelamentoEncaminhamentoTfd;

    public ConsultaEncaminhamentoPedidoPage() {
    }

    @Override
    protected void postConstruct() {
        form = new Form("form");

        form.add(new InputField<String>("nomePaciente", new PropertyModel(this, "nomePaciente")));
        form.add(new AutoCompleteConsultaTipoProcedimento("tipoProcedimento", new PropertyModel(this, "tipoProcedimento")).setIncluirInativos(true).setTfd(true));
        form.add(new InputField("numeroPedido", new PropertyModel(this, "numeroPedido")));
        form.add(new LongField("lote", new PropertyModel(this, "lote")));
        form.add(dropDownStatus = getDropDownStatus("status", new PropertyModel(this, "status")));
        dropDownStatus.setComponentValue(LaudoTfd.StatusLaudoTfd.ENCAMINHADO_REGIONAL.value());
        form.add(pnlChoicePeriod = new PnlChoicePeriod("periodo", new PropertyModel(this, "periodo")));
        pnlChoicePeriod.setDefaultOutro();

        containerTree = new WebMarkupContainer("containerTree");
        EncaminhamentoPedidoDTOparam param = new EncaminhamentoPedidoDTOparam();
        param.setStatus(status);
        provider.recarregarProvider(param);
        tableTree = new PageableTreeTable("treeTable", getColumns(), provider, 10);
        tableTree.setOutputMarkupId(true);
        containerTree.add(tableTree);
        containerTree.setOutputMarkupId(true);
        form.add(containerTree);

        form.add(new AbstractAjaxButton("btnProcurar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                procurar(target);
            }
        });
        form.add(new AbstractAjaxButton("btnNovo") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new CadastroEncaminhamentoPedidoPage());
            }
        });
        add(form);
    }

    private DropDown getDropDownStatus(String id, PropertyModel model) {
        DropDown dropDown = new DropDown(id, model);

        dropDown.addChoice("", BundleManager.getString("todos"));
        dropDown.addChoice(LaudoTfd.StatusLaudoTfd.AUTORIZADO.value(), LaudoTfd.StatusLaudoTfd.AUTORIZADO.toString());
        dropDown.addChoice(LaudoTfd.StatusLaudoTfd.CANCELADO.value(), LaudoTfd.StatusLaudoTfd.CANCELADO.toString());
        dropDown.addChoice(LaudoTfd.StatusLaudoTfd.ENCAMINHADO_REGIONAL.value(), LaudoTfd.StatusLaudoTfd.ENCAMINHADO_REGIONAL.toString());
        dropDown.addChoice(LaudoTfd.StatusLaudoTfd.INCONCLUSIVO.value(), LaudoTfd.StatusLaudoTfd.INCONCLUSIVO.toString());
        dropDown.addChoice(LaudoTfd.StatusLaudoTfd.NEGADO.value(), LaudoTfd.StatusLaudoTfd.NEGADO.toString());

        return dropDown;
    }

    private void procurar(AjaxRequestTarget target) {
        EncaminhamentoPedidoDTOparam param = new EncaminhamentoPedidoDTOparam();
        param.setLote(lote);
        param.setNumeroPedido(numeroPedido);
        param.setTipoProcedimento(tipoProcedimento);
        param.setNomePaciente(nomePaciente);
        param.setPeriodoEncaminhamento(periodo);
        param.setStatus(status);

        provider.recarregarProvider(param);

        target.add(containerTree);
    }

    private List getColumns() {
        List<IColumn<EncaminhamentoPedidoDTO, String>> result = new LinkedList();
        EncaminhamentoPedidoDTO proxy = on(EncaminhamentoPedidoDTO.class);
        int headerIndex = 0;

        result.add(getCustomColumn());
        result.add(new CustomTreeColumn<EncaminhamentoPedidoDTO, String>(Model.of(BundleManager.getString("lote"))) {
            @Override
            public void populateItem(Item<ICellPopulator<EncaminhamentoPedidoDTO>> cellItem, String componentId, IModel<EncaminhamentoPedidoDTO> rowModel) {
                if (!rowModel.getObject().isHeader()) {
                    super.populateItem(cellItem, componentId, rowModel);
                } else {
                    Component component = new Label(componentId, new Model(BundleManager.getString("lote")));
                    NodeModel<EncaminhamentoPedidoDTO> nodeModel = (NodeModel<EncaminhamentoPedidoDTO>) rowModel;
                    component.add(new NodeBorder(nodeModel.getBranches()));
                    component.add(new AttributeModifier("class", "header-pedido"));
                    cellItem.add(component);
                }
            }

        });
        result.add(new ICustomColumnTreeTable(path(proxy.getEloPedidoTfdLoteEncaminhamentoTfd().getLoteEncaminhamentoTfd().getDataEncaminhamentoFormatado()), path(proxy.getPedidoTfd().getNumeroPedido()), headerIndex++, Model.of(BundleManager.getString("dataEncaminhamento"))));
        result.add(new ICustomColumnTreeTable(path(proxy.getEloPedidoTfdLoteEncaminhamentoTfd().getLoteEncaminhamentoTfd().getResponsavel()), path(proxy.getPedidoTfd().getLaudoTfd().getUsuarioCadsus().getNomeSocial()), headerIndex++, Model.of(BundleManager.getString("responsavel"))));
        result.add(new ICustomColumnTreeTable(path(proxy.getEloPedidoTfdLoteEncaminhamentoTfd().getLoteEncaminhamentoTfd().getRegionalSaude().getDescricao()), path(proxy.getPedidoTfd().getLaudoTfd().getTipoProcedimento().getDescricao()), headerIndex++, Model.of(BundleManager.getString("regionalSaude"))));
        result.add(new ICustomColumnTreeTable(null, path(proxy.getPedidoTfd().getLaudoTfd().getProfissional().getNome()), headerIndex++, Model.of("")));
        result.add(new ICustomColumnTreeTable(null, path(proxy.getPedidoTfd().getDataCadastroFormatado()), headerIndex++, Model.of("")));
        result.add(new ICustomColumnTreeTable(null, path(proxy.getPedidoTfd().getLaudoTfd().getDescricaoStatus()), headerIndex++, Model.of("")));

        return result;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<EncaminhamentoPedidoDTO>() {
            @Override
            public void customizeColumn(final EncaminhamentoPedidoDTO rowObject) {
                AbstractActionLinkPanel actionEditar = addAction(ActionType.EDITAR, rowObject, new IModelAction<EncaminhamentoPedidoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, EncaminhamentoPedidoDTO modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroEncaminhamentoPedidoPage(modelObject.getEloPedidoTfdLoteEncaminhamentoTfd().getLoteEncaminhamentoTfd().getCodigo()));
                    }
                }).setTitleBundleKey("editarEncaminhamento");
                actionEditar.setEnabled(rowObject.getEloPedidoTfdLoteEncaminhamentoTfd() == null || (rowObject.getEloPedidoTfdLoteEncaminhamentoTfd() != null && EloPedidoTfdLoteEncaminhamentoTfd.Status.PENTENDE.value().equals(rowObject.getEloPedidoTfdLoteEncaminhamentoTfd().getStatus())));
                configuraEditar(rowObject, actionEditar);

                AbstractActionLinkPanel actionImprimirFecharLote = addAction(ActionType.IMPRIMIR, rowObject, new IModelAction<EncaminhamentoPedidoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, EncaminhamentoPedidoDTO modelObject) throws ValidacaoException, DAOException {
                        if (EloPedidoTfdLoteEncaminhamentoTfd.Status.PENTENDE.value().equals(modelObject.getEloPedidoTfdLoteEncaminhamentoTfd().getStatus())) {
                            addModal(target, dlgConfirmarImpressaoSimNao = new DlgConfirmarImpressaoSimNao(newModalId(), "Deseja fechar o lote? Após o fechamento do lote, não poderá ser inserido novos pedidos.") {
                                @Override
                                public DataReport getDataReport(AjaxRequestTarget target, boolean sim, Object object) throws ValidacaoException, DAOException, ReportException {
                                    if (sim) {
                                        EloPedidoTfdLoteEncaminhamentoTfd eloPedidoTfdLoteEncaminhamentoTfd = LoadManager.getInstance(EloPedidoTfdLoteEncaminhamentoTfd.class)
                                                .addParameter(new QueryCustom.QueryCustomParameter(EloPedidoTfdLoteEncaminhamentoTfd.PROP_CODIGO, ((EncaminhamentoPedidoDTO) object).getEloPedidoTfdLoteEncaminhamentoTfd().getCodigo()))
                                                .setMaxResults(1).start().getVO();
                                        if (eloPedidoTfdLoteEncaminhamentoTfd != null && !EloPedidoTfdLoteEncaminhamentoTfd.Status.FECHADO.value().equals(eloPedidoTfdLoteEncaminhamentoTfd.getStatus())) {
                                            eloPedidoTfdLoteEncaminhamentoTfd.setStatus(EloPedidoTfdLoteEncaminhamentoTfd.Status.FECHADO.value());
                                            BOFactoryWicket.save(eloPedidoTfdLoteEncaminhamentoTfd);
                                            procurar(target);
                                        }
                                    }
                                    RelatorioEncaminhadosRegionalDTOParam param = new RelatorioEncaminhadosRegionalDTOParam();
                                    param.setCodigosLoteEncaminhamento(Arrays.asList(((EncaminhamentoPedidoDTO) object).getEloPedidoTfdLoteEncaminhamentoTfd().getLoteEncaminhamentoTfd().getCodigo()));
                                    return BOFactoryWicket.getBO(AgendamentoReportFacade.class).relatorioEncaminhadosRegional(param);
                                }
                            });
                            dlgConfirmarImpressaoSimNao.show(target, modelObject);
                        }
                    }
                }).setTitleBundleKey("impressaoLote");
                configuraAcaoImprimirFechar(rowObject, actionImprimirFecharLote);

                AbstractActionLinkPanel actionImprimir = addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<EncaminhamentoPedidoDTO>() {
                    @Override
                    public DataReport action(EncaminhamentoPedidoDTO modelObject) throws ReportException {
                        RelatorioEncaminhadosRegionalDTOParam param = new RelatorioEncaminhadosRegionalDTOParam();
                        param.setCodigosLoteEncaminhamento(Arrays.asList(modelObject.getEloPedidoTfdLoteEncaminhamentoTfd().getLoteEncaminhamentoTfd().getCodigo()));
                        return BOFactoryWicket.getBO(AgendamentoReportFacade.class).relatorioEncaminhadosRegional(param);
                    }
                }).setTitleBundleKey("impressaoLote");
                configuraAcaoImprimir(rowObject, actionImprimir);

                AbstractActionLinkPanel actionCancelar = addAction(ActionType.CANCELAR, rowObject, new IModelAction<EncaminhamentoPedidoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, EncaminhamentoPedidoDTO modelObject) throws ValidacaoException, DAOException {
                        onCancelar(target, modelObject);
                    }
                }).setTitleBundleKey("cancelarEncaminhamento")
                        .setQuestionDialogBundleKey(null);
                configuraAcaoCancelar(rowObject, actionCancelar);
            }
        };
    }

    private void configuraAcaoImprimir(EncaminhamentoPedidoDTO rowObject, AbstractActionLinkPanel actionImprimir) {
        if (rowObject.isHeader() || rowObject.getPedidoTfd() != null || EloPedidoTfdLoteEncaminhamentoTfd.Status.PENTENDE.value().equals(rowObject.getEloPedidoTfdLoteEncaminhamentoTfd().getStatus())) {
            actionImprimir.setVisible(false);
        }
    }

    private void configuraEditar(EncaminhamentoPedidoDTO rowObject, AbstractActionLinkPanel actionImprimir) {
        if (rowObject.isHeader() || rowObject.getPedidoTfd() != null) {
            actionImprimir.setVisible(false);
        }
    }

    private void configuraAcaoImprimirFechar(EncaminhamentoPedidoDTO rowObject, AbstractActionLinkPanel actionImprimir) {
        if (rowObject.isHeader() || rowObject.getPedidoTfd() != null || !EloPedidoTfdLoteEncaminhamentoTfd.Status.PENTENDE.value().equals(rowObject.getEloPedidoTfdLoteEncaminhamentoTfd().getStatus())) {
            actionImprimir.setVisible(false);
        }
    }

    private void configuraAcaoCancelar(EncaminhamentoPedidoDTO rowObject, AbstractActionLinkPanel actionCancelar) {
        if (rowObject.getPedidoTfd() != null) {
            PedidoTfd pedido = LoadManager.getInstance(PedidoTfd.class)
                    .addProperties(new HQLProperties(PedidoTfd.class).getProperties())
                    .addProperties(new HQLProperties(LaudoTfd.class, PedidoTfd.PROP_LAUDO_TFD).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTfd.PROP_LAUDO_TFD, LaudoTfd.PROP_STATUS), BuilderQueryCustom.QueryParameter.DIFERENTE, LaudoTfd.StatusLaudoTfd.ENCAMINHADO_REGIONAL.value()))
                    .addParameter(new QueryCustom.QueryCustomParameter(PedidoTfd.PROP_CODIGO, rowObject.getPedidoTfd().getCodigo()))
                    .start().getVO();
            if (pedido != null) {
                actionCancelar.setVisible(false);
            }
        }
        if (rowObject.isHeader() || rowObject.getPedidoTfd() == null) {
            actionCancelar.setVisible(false);
        }
    }

    private void onCancelar(AjaxRequestTarget target, EncaminhamentoPedidoDTO modelObject) {
        if (dlgMotivoCancelamentoEncaminhamentoTfd == null) {
            addModal(target, dlgMotivoCancelamentoEncaminhamentoTfd = new DlgMotivoCancelamentoEncaminhamentoTfd(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, String motivo, EncaminhamentoPedidoDTO modelObject) throws ValidacaoException, DAOException {
                    PedidoTfd pedido = LoadManager.getInstance(PedidoTfd.class)
                            .addProperties(new HQLProperties(PedidoTfd.class).getProperties())
                            .addProperties(new HQLProperties(LaudoTfd.class, PedidoTfd.PROP_LAUDO_TFD).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(PedidoTfd.PROP_CODIGO, modelObject.getPedidoTfd().getCodigo()))
                            .start().getVO();
                    if (pedido != null) {
                        BOFactoryWicket.getBO(TfdFacade.class).cancelarEncaminhamentoTfdWeb(modelObject.getEloPedidoTfdLoteEncaminhamentoTfd(), pedido, motivo);
                        EncaminhamentoPedidoTreeProvider provider = (EncaminhamentoPedidoTreeProvider) tableTree.getProvider();
                        provider.removeRoot(modelObject);
                        target.add(containerTree);
                    } else {
                        throw new ValidacaoException(BundleManager.getString("pedidoNaoEncontrado"));
                    }
                }
            });
        }
        dlgMotivoCancelamentoEncaminhamentoTfd.show(target, modelObject);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaEncaminhamentosPedido");
    }
}
