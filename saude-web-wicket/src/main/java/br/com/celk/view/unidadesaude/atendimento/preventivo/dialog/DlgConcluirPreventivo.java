package br.com.celk.view.unidadesaude.atendimento.preventivo.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.programasaude.Preventivo;
import org.apache.wicket.ajax.AjaxRequestTarget;

import java.util.Date;

/**
 * <AUTHOR>
 */
public abstract class DlgConcluirPreventivo extends Window {

    private PnlConcluirPreventivo pnlConcluirPreventivo;

    public DlgConcluirPreventivo(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(BundleManager.getString("dataEntregaPaciente"));
        setInitialWidth(400);
        setInitialHeight(80);
        setResizable(true);

        setContent(pnlConcluirPreventivo = new PnlConcluirPreventivo(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) {
                close(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, Preventivo preventivo, Date dataConclusao) throws DAOException, ValidacaoException {
                DlgConcluirPreventivo.this.onConfirmar(target, preventivo, dataConclusao);
                close(target);
            }
        });
    }

    public void show(AjaxRequestTarget target, Preventivo preventivo) {
        super.show(target);
        pnlConcluirPreventivo.setPreventivo(target, preventivo);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, Preventivo preventivo, Date dataConclusao) throws DAOException, ValidacaoException;

}
