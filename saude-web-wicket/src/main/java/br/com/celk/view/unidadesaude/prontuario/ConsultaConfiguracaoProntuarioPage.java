package br.com.celk.view.unidadesaude.prontuario;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaConfiguracaoProntuarioPage extends ConsultaPage<GrupoAtendimentoCbo, List<BuilderQueryCustom.QueryParameter>>{

    private String descricao;
    
    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("descricao"));
        
        getLinkNovo().setVisible(false);
        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        GrupoAtendimentoCbo proxy = on(GrupoAtendimentoCbo.class);
        
        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("descricao"), proxy.getDescricao()));
        return columns;
    }
    
    private IColumn getCustomColumn(){
        return new MultipleActionCustomColumn<GrupoAtendimentoCbo>() {
            @Override
            public void customizeColumn(GrupoAtendimentoCbo rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<GrupoAtendimentoCbo>() {

                    @Override
                    public void action(AjaxRequestTarget target, GrupoAtendimentoCbo grupoAtendimentoCbo) throws ValidacaoException, DAOException {
                        editarItem(grupoAtendimentoCbo);
                    }
                });
            }
        };
    }
    
    private void editarItem(GrupoAtendimentoCbo grupoAtendimentoCbo){
        setResponsePage(new CadastroConfiguracaoProntuarioPage(grupoAtendimentoCbo));
    }
    
    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return GrupoAtendimentoCbo.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(GrupoAtendimentoCbo.class).getProperties());
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(GrupoAtendimentoCbo.PROP_DESCRICAO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        
        parameters.add(new QueryCustom.QueryCustomParameter(GrupoAtendimentoCbo.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return ConsultaConfiguracaoProntuarioPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("configuracaoConsultaProntuario");
    }
    
}
