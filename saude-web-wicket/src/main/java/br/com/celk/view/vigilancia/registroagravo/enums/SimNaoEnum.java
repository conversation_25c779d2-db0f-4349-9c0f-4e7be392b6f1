package br.com.celk.view.vigilancia.registroagravo.enums;

import br.com.ksisolucoes.enums.IEnum;

public enum SimNaoEnum implements IEnum<SimNaoEnum> {
    DEFAULT(null, ""),
    SIM(1L, "Sim"),
    NAO(2L, "Não"),
    INDETERMINADO(3L, "Indeterminado"),
    IGNORADO(9L, "Ignorado");

    private Long value;
    private String descricao;

    SimNaoEnum(Long value, String descricao) {
        this.value = value;
        this.descricao = descricao;
    }

    @Override
    public Long value() {
        return value;
    }

    @Override
    public String descricao() {
        return descricao;
    }

    public static SimNaoEnum valueOf(Long value) {
        for (SimNaoEnum v : SimNaoEnum.values()) {
            if (v.value().equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static IEnum[] getSimNaoIndeterminado() {
        IEnum[] arr = {SimNaoEnum.SIM, SimNaoEnum.NAO, SimNaoEnum.INDETERMINADO};
        return arr;
    }

    public static IEnum[] getSimNaoIgnorado() {
        IEnum[] arr = {SimNaoEnum.SIM, SimNaoEnum.NAO, SimNaoEnum.IGNORADO};
        return arr;
    }

    public static IEnum[] getSimNaoIgnoradoDefault() {
        IEnum[] arr = {SimNaoEnum.DEFAULT, SimNaoEnum.SIM, SimNaoEnum.NAO, SimNaoEnum.IGNORADO};
        return arr;
    }
}
