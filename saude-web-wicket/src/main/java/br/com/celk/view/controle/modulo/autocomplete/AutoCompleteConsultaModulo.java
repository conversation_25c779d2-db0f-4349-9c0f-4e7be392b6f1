package br.com.celk.view.controle.modulo.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.controle.Modulo;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 17.11.29
 */
public class AutoCompleteConsultaModulo extends AutoCompleteConsulta<Modulo> {

    public AutoCompleteConsultaModulo(String id) {
        super(id);
    }

    public AutoCompleteConsultaModulo(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaModulo(String id, IModel<Modulo> model) {
        super(id, model);
    }

    public AutoCompleteConsultaModulo(String id, IModel<Modulo> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {
            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaAdapter() {

                    @Override
                    public void consultaCustomizeViewProperties(Map<String, String> properties) {
                        properties.put(BundleManager.getString("codigo"), Modulo.PROP_CODIGO);
                        properties.put(BundleManager.getString("nome"), Modulo.PROP_NOME);
                    }

                    @Override
                    public void consultaCustomizeFilterProperties(Map<String, BuilderQueryCustom.QueryParameter> filterProperties) {
                        filterProperties.put(BundleManager.getString("nome"), new QueryCustom.QueryCustomParameter(Modulo.PROP_NOME, BuilderQueryCustom.QueryParameter.CONSULTA_LIKED));
                        filterProperties.put(BundleManager.getString("codigo"), new QueryCustom.QueryCustomParameter(Modulo.PROP_CODIGO));
                    }

                    @Override
                    public Class getClassConsulta() {
                        return Modulo.class;
                    }
                };
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(Modulo.PROP_CODIGO), true);
            }

            @Override
            public List getSearchParam(String searchCriteria) {
                List<BuilderQueryCustom.QueryParameter> list = new ArrayList<BuilderQueryCustom.QueryParameter>();
                list.add(new QueryCustom.QueryCustomParameter(Modulo.PROP_NOME, BuilderQueryCustom.QueryParameter.ILIKE, (searchCriteria != null ? searchCriteria.trim() : null)));
                return list;
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("modulos");
    }
}