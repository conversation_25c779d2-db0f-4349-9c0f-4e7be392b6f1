package br.com.celk.view.atendimento.programas.autocompleteintercorrencia;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.vo.prontuario.avaliacao.SisvanIntercorrencia;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaIntercorrencia extends AutoCompleteConsulta<SisvanIntercorrencia> {

    public AutoCompleteConsultaIntercorrencia(String id) {
        super(id);
    }

    public AutoCompleteConsultaIntercorrencia(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaIntercorrencia(String id, IModel<SisvanIntercorrencia> model) {
        super(id, model);
    }

    public AutoCompleteConsultaIntercorrencia(String id, IModel<SisvanIntercorrencia> model, boolean required) {
        super(id, model, required);
    }


    @Override
    public String getTitle() {
        return BundleManager.getString("intercorrencia");
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {

           @Override
            public Class getReferenceClass() {
                return SisvanIntercorrencia.class;
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(SisvanIntercorrencia.PROP_DESCRICAO_INTERCORRENCIA, true);
            }

            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaAdapter(){

                    @Override
                    public void consultaCustomizeViewProperties(Map<String, String> properties) {
                        properties.put(BundleManager.getString("codigo"), SisvanIntercorrencia.PROP_CODIGO);
                        properties.put(BundleManager.getString("descricao"), SisvanIntercorrencia.PROP_DESCRICAO_INTERCORRENCIA);
                    }

                    @Override
                    public void consultaCustomizeFilterProperties(Map<String, BuilderQueryCustom.QueryParameter> filterProperties) {
                        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(SisvanIntercorrencia.PROP_DESCRICAO_INTERCORRENCIA, (String) BuilderQueryCustom.QueryParameter.CONSULTA_LIKED));
                        filterProperties.put(BundleManager.getString("codigo"), new QueryCustom.QueryCustomParameter(SisvanIntercorrencia.PROP_CODIGO));
                    }

                    @Override
                    public Class getClassConsulta() {
                        return SisvanIntercorrencia.class;
                    }
                    
                };
            }

            @Override
            public List<BuilderQueryCustom.QueryParameter> getSearchParam(String searchCriteria) {
                return Arrays.<BuilderQueryCustom.QueryParameter>asList(new QueryCustom.QueryCustomParameter(SisvanIntercorrencia.PROP_DESCRICAO_INTERCORRENCIA, BuilderQueryCustom.QueryParameter.CONSULTA_LIKED, (searchCriteria != null ? searchCriteria.trim() : null)));
            }
            
        };
    }
    
}
