package br.com.celk.view.vigilancia.tipoprojetovigilancia;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.checkbox.CheckBoxUtil;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.doublefield.RequiredDoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.RequiredDropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputarea.RequiredInputArea;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.FinanceiroVigilanciaHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.TipoProjetoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.taxa.Taxa;
import br.com.ksisolucoes.vo.vigilancia.taxa.TaxaIndice;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.resource.CssResourceReference;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroTipoProjetoVigilanciaPage extends CadastroPage<TipoProjetoVigilancia> {
    
    private InputArea txaDescricao;
    private DropDown<Taxa> dropDownTaxa;
    private DoubleField qtdTaxa;
    private DoubleField txtMetragemMaxima;
    private String valorTaxa;
    private Label lbValorTaxa;
    private TaxaIndice taxaVigente;
    private List<CheckBoxLongValue> lstCheckBoxTipo;
    private CheckBoxLongValue checkBoxPba;
    private CheckBoxLongValue checkBoxVistoriaPba;
    private CheckBoxLongValue checkBoxPhs;
    private CheckBoxLongValue checkBoxPhsDeclaratorio;
    private CheckBoxLongValue checkBoxVistoriaPhs;
    private CheckBoxLongValue checkBoxVistoriaHabitiseDeclaratorio;
    private CheckBoxLongValue checkBoxProjetoArquitetonicoSanitario;

    private String CSS_FILE = "CadastroTipoProjetoVigilanciaPage.css";

    public CadastroTipoProjetoVigilanciaPage(TipoProjetoVigilancia object,boolean viewOnly, boolean editar) {
        this(object, viewOnly);
    }

    public CadastroTipoProjetoVigilanciaPage(TipoProjetoVigilancia object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroTipoProjetoVigilanciaPage(TipoProjetoVigilancia object) {
        this(object, false);
    }

    public CadastroTipoProjetoVigilanciaPage() {
        this(null);
    }

    @Override
    public void init(Form form) {
        carregarTaxaVigente(getForm().getModel().getObject().getTaxa());
        txaDescricao = new RequiredInputArea<>(TipoProjetoVigilancia.PROP_DESCRICAO);
        txtMetragemMaxima = new RequiredDoubleField(TipoProjetoVigilancia.PROP_METRAGEM_MAXIMA);
        txtMetragemMaxima.setMDec(4).addAjaxUpdateValue();
        txtMetragemMaxima.add(new Tooltip().setText("msgTooltipMetragemMaximaTipoProjeto"));
        DropDown ddTaxa = getDropDownTaxa(TipoProjetoVigilancia.PROP_TAXA);
        DropDown ddTipoCobranca = DropDownUtil.getIEnumDropDown(TipoProjetoVigilancia.PROP_TIPO_COBRANCA, TipoProjetoVigilancia.TipoCobranca.values());
        qtdTaxa = new RequiredDoubleField(TipoProjetoVigilancia.PROP_VALOR);
        qtdTaxa.setMDec(4).addAjaxUpdateValue();
        qtdTaxa.add(new AjaxFormComponentUpdatingBehavior("onBlur") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                montaSequencial(target);
            }
        });

        lbValorTaxa = new Label("valorTaxa", new PropertyModel<String>(this, "valorTaxa"));
        lbValorTaxa.setOutputMarkupId(true);

        checkBoxPba = new CheckBoxLongValue("pba", TipoProjetoVigilancia.Tipo.PBA.value(), new Model<>());
        checkBoxVistoriaPba = new CheckBoxLongValue("vistoriaPba", TipoProjetoVigilancia.Tipo.VISTORIA_PBA.value(), new Model<>());
        checkBoxPhs = new CheckBoxLongValue("phs", TipoProjetoVigilancia.Tipo.PHS.value(), new Model<>());
        checkBoxPhsDeclaratorio = new CheckBoxLongValue("phsDeclaratorio", TipoProjetoVigilancia.Tipo.PHSDeclaratorio.value(), new Model<>());
        checkBoxVistoriaPhs = new CheckBoxLongValue("vistoriaPhs", TipoProjetoVigilancia.Tipo.VISTORIA_HABITE_SE.value(), new Model<>());
        checkBoxVistoriaHabitiseDeclaratorio = new CheckBoxLongValue("vistoriaHabitiseDeclaratorio", TipoProjetoVigilancia.Tipo.VISTORIA_HABITE_SE_DECLARATORIO.value(), new Model<>());
        checkBoxProjetoArquitetonicoSanitario = new CheckBoxLongValue("projetoArquitetonicoSanitario", TipoProjetoVigilancia.Tipo.PROJETO_ARQUITETONICO_SANITARIO.value(), new Model<>());

        form.add(txaDescricao, txtMetragemMaxima, ddTaxa, ddTipoCobranca, qtdTaxa, lbValorTaxa,
                checkBoxPba, checkBoxVistoriaPba, checkBoxPhs, checkBoxPhsDeclaratorio, checkBoxVistoriaPhs, checkBoxVistoriaHabitiseDeclaratorio,
                checkBoxProjetoArquitetonicoSanitario
        );

        montaSequencial(null);

        lstCheckBoxTipo = Arrays.asList(checkBoxPba, checkBoxVistoriaPba, checkBoxPhs, checkBoxPhsDeclaratorio, checkBoxVistoriaPhs, checkBoxVistoriaHabitiseDeclaratorio, checkBoxProjetoArquitetonicoSanitario);

        if(getForm().getModel().getObject().getCodigo() == null){
            getForm().getModel().getObject().setTipo(TipoProjetoVigilancia.Tipo.PBA.value());
        }
        CheckBoxUtil.selecionarSomatorio(lstCheckBoxTipo, getForm().getModel().getObject().getTipo());
    }

    private void montaSequencial(AjaxRequestTarget target) {
        StringBuilder sb = new StringBuilder();
        BigDecimal totalTaxa = new BigDecimal(0);
        if (taxaVigente != null && qtdTaxa.getComponentValue() != null) {
            totalTaxa = new Dinheiro(qtdTaxa.getComponentValue(), 4).multiplicar(taxaVigente.getValorIndice(), 4).bigDecimalValue();
        }
        NumberFormat nf = NumberFormat.getCurrencyInstance();
        sb.append(nf.format(totalTaxa));
        valorTaxa = sb.toString();
        if (target != null) {
            target.add(lbValorTaxa);
        }
    }
    
    @Override
    public FormComponent getComponentRequestFocus() {
        return txaDescricao;
    }

    @Override
    public Class<TipoProjetoVigilancia> getReferenceClass() {
        return TipoProjetoVigilancia.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaTipoProjetoVigilanciaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroTipoProjeto");
    }
    
    @Override
    public Object salvar(TipoProjetoVigilancia object) throws DAOException, ValidacaoException {
        return super.salvar(object);
    }

    private DropDown<Taxa> getDropDownTaxa(String id) {
        dropDownTaxa = new RequiredDropDown(id);

        dropDownTaxa.addChoice(null, "");
        dropDownTaxa.addAjaxUpdateValue();

        List<Taxa> listTaxa = LoadManager.getInstance(Taxa.class)
                .addProperties(new HQLProperties(Taxa.class).getProperties())
                .addSorter(new QueryCustom.QueryCustomSorter(Taxa.PROP_DESCRICAO))
                .start().getList();

        for (Taxa taxa : listTaxa) {
            dropDownTaxa.addChoice(taxa, taxa.getDescricao());
        }

        dropDownTaxa.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                carregarTaxaVigente(dropDownTaxa.getComponentValue());
                montaSequencial(target);
            }
        });

        return dropDownTaxa;
    }

    private void carregarTaxaVigente(Taxa taxa) {
        try {
            taxaVigente = FinanceiroVigilanciaHelper.getTaxaIndiceVigente(taxa);
        } catch (Exception e) {
            Loggable.log.info(e.getMessage());
        }
    }

    @Override
    public void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        getForm().getModel().getObject().setTipo(CheckBoxUtil.getSomatorio(lstCheckBoxTipo));

        if(Coalesce.asLong(getForm().getModel().getObject().getTipo()) <= 0L){
            throw new ValidacaoException(bundle("msgSelecionePeloMenosUmTipo"));
        }

        super.salvar(target);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(CssHeaderItem.forReference(new CssResourceReference(this.getClass(), CSS_FILE)));
    }
}
