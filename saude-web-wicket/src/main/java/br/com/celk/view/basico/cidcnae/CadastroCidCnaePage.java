package br.com.celk.view.basico.cidcnae;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.RequiredDropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.column.panel.RemoverActionColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.estabelecimentocerest.ConsultaEstabelecimentoCerestPage;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.celk.view.vigilancia.estabelecimento.tabbedpanel.DadosAtividadeEconomicaEstabelecimentoTab;
import br.com.celk.view.vigilancia.tabelacnae.autocomplete.AutoCompleteConsultaTabelaCnae;
import br.com.ksisolucoes.bo.basico.interfaces.dto.CadastroCidCnaeDTO;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.CidCerest;
import br.com.ksisolucoes.vo.basico.CidCerestCnae;
import br.com.ksisolucoes.vo.basico.EstabelecimentoCerestCnae;
import br.com.ksisolucoes.vo.vigilancia.EstabelecimentoCnae;
import br.com.ksisolucoes.vo.vigilancia.TabelaCnae;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Created by jonas on 21/03/18.
 */
public class CadastroCidCnaePage extends BasePage {


    private Form<CadastroCidCnaeDTO> form;
    private CidCerest cidCerest;
    private CidCerestCnae cidCerestCnae;
    private Table<CidCerestCnae> table;

    private AutoCompleteConsultaCid autoCompleteConsultaCid;
    private AutoCompleteConsultaTabelaCnae autoCompleteConsultaTabelaCnae;
    private ArrayList<IColumn> columns;
    private CollectionProvider collectionProvider;
    private TabelaCnae tabelaCnae;
    private boolean editar = true;


    public CadastroCidCnaePage() {
        init(true);
    }

    public CadastroCidCnaePage(CidCerest cidCerest) {
        this.cidCerest = cidCerest;
        init(true);
    }

    public CadastroCidCnaePage(CidCerest cidCerest, boolean viewOnly) {
        this.cidCerest = cidCerest;
        carregaCnae();
        this.editar = viewOnly;
        init(viewOnly);
    }

    private void init(boolean viewOnly) {
        CadastroCidCnaeDTO proxy  = on(CadastroCidCnaeDTO.class);

        getForm().add(autoCompleteConsultaCid = new AutoCompleteConsultaCid(path(proxy.getCidCerest().getCodigoCid())));
        autoCompleteConsultaCid.getTxtDescricao().addRequiredClass();
        autoCompleteConsultaCid.setRequired(true);
        autoCompleteConsultaCid.setEnabled(cidCerest == null);
        autoCompleteConsultaCid.setLabel(new Model<String>(BundleManager.getString("cid")));

        getForm().add(autoCompleteConsultaTabelaCnae = new AutoCompleteConsultaTabelaCnae("tabelaCnae", new PropertyModel<TabelaCnae>(this, "tabelaCnae")));
        autoCompleteConsultaTabelaCnae.setEnabled(editar);
        getForm().add(table = new Table("table", getColumns(), getCollectionProvider()));
        table.populate();
        getForm().add(new AbstractAjaxButton("btnAdicionar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        }.setEnabled(editar));

        getForm().add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
               salvar();

            }
        }).setVisible(editar));

        getForm().add(new VoltarButton("btnVoltar"));

        add(getForm());

    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaCid.getTxtDescricao().getTextField();
    }

    private void salvar() throws DAOException, ValidacaoException {
        BOFactory.getBO(BasicoFacade.class).salvarCidCerestCnae(getForm().getModel().getObject());

        ConsultaCidCnaePage page = new ConsultaCidCnaePage();
        getSession().getFeedbackMessages().info(page, BundleManager.getString("registro_salvo_sucesso"));
        setResponsePage(page);
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException {
        if (tabelaCnae != null) {
            //Primeiro checa de já existe o livro na tabela.
            if (!jaExiste()) {
                //Não existindo, instancia uma novo objeto
                CidCerestCnae cidCerestCnae = new CidCerestCnae();
                //seta o grupo do cnae dentro do grupo do cidCerestCnae
                cidCerestCnae.setGrupo(tabelaCnae.getGrupo());
                this.getForm().getModel().getObject().getCidCerestCnaeList().add(cidCerestCnae);
                target.add(table);
                this.table.update(target);
            }
        }
        autoCompleteConsultaTabelaCnae.limpar(target);
    }

    private boolean jaExiste() throws ValidacaoException {
            //Percorre dentro da lista, analisando se já tem o codigo do grupo adicionado
            // se sim, retorna true, se não retorna false
            for (CidCerestCnae cidCerestCnae : getForm().getModel().getObject().getCidCerestCnaeList()) {
                if (cidCerestCnae.getGrupo().equals(this.tabelaCnae.getGrupo())) {
                    throw new ValidacaoException(Bundle.getStringApplication("rotulo_cnae_cid"));
                }
            }
        return false;
    }

    private void remover(AjaxRequestTarget target, CidCerestCnae _cidCidCerestCnae) {

        for (int i = 0; i < getForm().getModel().getObject().getCidCerestCnaeList().size(); i++) {
            if (getForm().getModel().getObject().getCidCerestCnaeList().get(i).getGrupo() == _cidCidCerestCnae.getGrupo()) {
                getForm().getModel().getObject().getCidCerestCnaeList().remove(i);
                break;
            }
        }
        table.update(target);
    }


    private List<IColumn> getColumns() {
        if (this.columns == null) {
            this.columns = new ArrayList<IColumn>();

            ColumnFactory columnFactory = new ColumnFactory(CidCerestCnae.class);

            this.columns.add(getCustomColumn());
            this.columns.add(columnFactory.createSortableColumn(BundleManager.getString("grupoCnae"), CidCerestCnae.PROP_GRUPO));
        }
        return this.columns;
    }

    private CustomColumn<CidCerestCnae> getCustomColumn() {
        return new CustomColumn<CidCerestCnae>() {

            @Override
            public Component getComponent(String componentId, final CidCerestCnae rowObject) {
                return new RemoverActionColumnPanel(componentId) {

                    @Override
                    public void onRemover(AjaxRequestTarget target) {
                       remover(target, rowObject);
                    }
                }.setEnabled(editar);
            }
        };
    }

    private CollectionProvider getCollectionProvider() {
        if (this.collectionProvider == null) {
            this.collectionProvider = new CollectionProvider() {
                @Override
                public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                    return CadastroCidCnaePage.this.getForm().getModel().getObject().getCidCerestCnaeList();


                }
            };
        }
        return this.collectionProvider;
    }

    private Form<CadastroCidCnaeDTO> getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel(new CadastroCidCnaeDTO()));
            form.getModel().getObject().setCidCerest(cidCerest);
        }
        return form;
    }


    private void carregaCnae(){
        List <CidCerestCnae> cidCerestList = LoadManager.getInstance(CidCerestCnae.class)
                .addProperties(new HQLProperties(CidCerestCnae.class).getProperties())
                .addProperties(new HQLProperties(CidCerest.class, CidCerestCnae.PROP_CODIGO_CID_CEREST).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(CidCerestCnae.PROP_CODIGO_CID_CEREST, cidCerest))
                .start().getList();
        CadastroCidCnaePage.this.getForm().getModel().getObject().setCidCerestCnaeList(cidCerestList);
    }


    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroCidCnae");
    }



}
