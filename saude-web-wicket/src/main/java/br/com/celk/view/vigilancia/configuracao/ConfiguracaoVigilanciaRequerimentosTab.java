package br.com.celk.view.vigilancia.configuracao;

import br.com.celk.component.tabbedpanel.cadastro.CadastroTab;
import br.com.celk.component.tabbedpanel.cadastro.ITabPanel;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.CadastroConfiguracaoVigilanciaDTO;
import org.apache.wicket.extensions.markup.html.tabs.ITab;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 * <AUTHOR>
 */
public class ConfiguracaoVigilanciaRequerimentosTab extends TabPanel<CadastroConfiguracaoVigilanciaDTO> {

    private CadastroConfiguracaoVigilanciaDTO cadastroConfiguracaoVigilanciaDTO;

    public ConfiguracaoVigilanciaRequerimentosTab(String id, CadastroConfiguracaoVigilanciaDTO cadastroConfiguracaoVigilanciaDTO) {
        super(id, cadastroConfiguracaoVigilanciaDTO);
        this.cadastroConfiguracaoVigilanciaDTO = cadastroConfiguracaoVigilanciaDTO;
        init();
    }

    private void init() {
        List<ITab> tabs = new ArrayList<ITab>();

        tabs.add(new CadastroTab<CadastroConfiguracaoVigilanciaDTO>(cadastroConfiguracaoVigilanciaDTO) {
            @Override
            public ITabPanel<CadastroConfiguracaoVigilanciaDTO> newTabPanel(String panelId, CadastroConfiguracaoVigilanciaDTO dto) {
                return new ConfiguracaoVigilanciaAlvarasTab(panelId, dto);
            }
        });

        tabs.add(new CadastroTab<CadastroConfiguracaoVigilanciaDTO>(cadastroConfiguracaoVigilanciaDTO) {
            @Override
            public ITabPanel<CadastroConfiguracaoVigilanciaDTO> newTabPanel(String id, CadastroConfiguracaoVigilanciaDTO dto) {
                return new ConfiguracaoVigilanciaReceituarioTab(id, dto);
            }
        });

        tabs.add(new CadastroTab<CadastroConfiguracaoVigilanciaDTO>(cadastroConfiguracaoVigilanciaDTO) {
            @Override
            public ITabPanel<CadastroConfiguracaoVigilanciaDTO> newTabPanel(String id, CadastroConfiguracaoVigilanciaDTO dto) {
                return new ConfiguracaoVigilanciaResponsabilidadeTecnicaTab(id, dto);
            }
        });

        tabs.add(new CadastroTab<CadastroConfiguracaoVigilanciaDTO>(cadastroConfiguracaoVigilanciaDTO) {
            @Override
            public ITabPanel<CadastroConfiguracaoVigilanciaDTO> newTabPanel(String panelId, CadastroConfiguracaoVigilanciaDTO dto) {
                return new ConfiguracaoVigilanciaLicencaTransporteTab(panelId, dto);
            }
        });

        tabs.add(new CadastroTab<CadastroConfiguracaoVigilanciaDTO>(cadastroConfiguracaoVigilanciaDTO) {
            @Override
            public ITabPanel<CadastroConfiguracaoVigilanciaDTO> newTabPanel(String panelId, CadastroConfiguracaoVigilanciaDTO dto) {
                return new ConfiguracaoVigilanciaDenunciaTab(panelId, dto);
            }
        });

        tabs.add(new CadastroTab<CadastroConfiguracaoVigilanciaDTO>(cadastroConfiguracaoVigilanciaDTO) {
            @Override
            public ITabPanel<CadastroConfiguracaoVigilanciaDTO> newTabPanel(String panelId, CadastroConfiguracaoVigilanciaDTO dto) {
                return new ConfiguracaoVigilanciaAnaliseProjetoTab(panelId, dto);
            }
        });

        add(new TabRequerimentosTabbedPanel("wizard", cadastroConfiguracaoVigilanciaDTO, false, tabs, false));
    }

    @Override
    public String getTitle() {
        return bundle("requerimentos");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnLoadHeaderItem.forScript("$('div[class=tab-panel]').find('fieldset')[0].style = 'margin-top: initial';"));
    }
}
