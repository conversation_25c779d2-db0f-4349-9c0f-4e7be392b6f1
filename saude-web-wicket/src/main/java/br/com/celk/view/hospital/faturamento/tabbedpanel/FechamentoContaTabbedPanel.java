package br.com.celk.view.hospital.faturamento.tabbedpanel;

import br.com.celk.component.tabbedpanel.cadastro.CadastroTabbedPanel;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.hospital.faturamento.ConsultaFechamentoConta;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.FechamentoContaDTO;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.util.List;
import org.apache.wicket.Page;
import org.apache.wicket.extensions.markup.html.tabs.ITab;

public class FechamentoContaTabbedPanel extends CadastroTabbedPanel<FechamentoContaDTO> {

    public FechamentoContaTabbedPanel(String id, FechamentoContaDTO dto, boolean viewOnly, List<ITab> tabs) {
        super(id, dto, viewOnly, tabs, true, true);
    }

    @Override
    public Class<FechamentoContaDTO> getReferenceClass() {
        return FechamentoContaDTO.class;
    }

    @Override
    public Object salvar(FechamentoContaDTO object) throws DAOException, ValidacaoException {

//        if (object.getListaItensContaPaciente().isEmpty() && object.getListaItensContaPacienteTransferidos().isEmpty()) {
//            throw new ValidacaoException(BundleManager.getString("naoHaItensAseremSalvos"));
//        }
        
        BOFactoryWicket.getBO(HospitalFacade.class).salvarItensContaPaciente(object);
        return null;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaFechamentoConta.class;
    }

    @Override
    public void customResponsePage(Page page) {
        ((ConsultaFechamentoConta) page).setCodigoRegistroSalvo(getObject().getContaPaciente().getCodigo());
    }
}
