package br.com.celk.view.hospital.faturamento.ipe.tabbedpanel;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.view.hospital.faturamento.dialogs.ipe.DlgLancamentosConfirmadosExamesIpe;
import br.com.celk.view.hospital.faturamento.dialogs.ipe.DlgNovoLancamentoExameIpe;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.FechamentoContaDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import static ch.lambdaj.Lambda.on;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.upload.FileUpload;

/**
 *
 * <AUTHOR>
 */
public class ExamesIpeTab extends TabPanel<FechamentoContaDTO> {

    private String produto;
    private Table tabela;
    private Form<FechamentoContaDTO> form;
    private DlgNovoLancamentoExameIpe dlgNovoLancamentoIpe;
    private DlgLancamentosConfirmadosExamesIpe dlgLancamentosConfirmadosExamesIpe;
    private AbstractAjaxButton btnLancamentosConfirmados;
    private FechamentoContaDTO fechamentoContaDTO;
    private List<ItemContaPaciente> examesList;
    private List<ItemContaPaciente> examesConfirmadosList;
    private boolean btnConfirmadoOK;
    private AbstractAjaxButton btnConfirmaLancamentos;
    private ItemContaPaciente itemEdicao;

    public ExamesIpeTab(String id, FechamentoContaDTO object) {
        super(id, object);
        this.fechamentoContaDTO = object;
        init();
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("exames");
    }

    private void init() {

        add(tabela = new Table("tabela", getColumns(), getCollectionProvider()));
        tabela.populate();

        add(new AbstractAjaxButton("btnNovoLancamento") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                abreDialog(target, new ItemContaPaciente());
            }
        });

        add(getBtnLanctosConfirmados());
        add(getBtnConfirmaLancamentos());
    }

    private void abreDialog(AjaxRequestTarget target, ItemContaPaciente item) throws ValidacaoException {

        WindowUtil.addModal(target, this, dlgNovoLancamentoIpe = new DlgNovoLancamentoExameIpe(WindowUtil.newModalId(this)) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente, FileUpload file) throws DAOException, ValidacaoException {
                adicionaNovoItem(target, itemContaPaciente, file);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
            }
        });

        dlgNovoLancamentoIpe.show(target, item, fechamentoContaDTO.getContaPaciente().getAtendimentoInformacao().getAtendimentoPrincipal().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getTipoContaIpe(), fechamentoContaDTO.getContaPaciente().getAtendimentoInformacao());
    }

    private AbstractAjaxButton getBtnConfirmaLancamentos() {
        if (btnConfirmaLancamentos == null) {
            btnConfirmaLancamentos = new AbstractAjaxButton("btnConfirmaLancamentos") {
                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    confirmaTodosLancamentos(target);
                }
            };
        }
        return btnConfirmaLancamentos;
    }

    private void confirmaTodosLancamentos(AjaxRequestTarget target) {

        for (ItemContaPaciente item : fechamentoContaDTO.getListaItensContaPaciente()) {
            if (item.getTipo().equals(ItemContaPaciente.Tipo.EXAME.value()) && item.getStatus().equals(ItemContaPaciente.Status.ABERTO.value())) {
                item.setStatus(ItemContaPaciente.Status.CONFIRMADO.value());
            }
        }

        btnLancamentosConfirmados.setEnabled(true);
        target.add(btnLancamentosConfirmados);
        tabela.populate();
        target.add(tabela);
    }

    private void adicionaNovoItem(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente, FileUpload file) {
        fechamentoContaDTO.getContaPaciente().setConvenio(fechamentoContaDTO.getContaPaciente().getConvenio());
        itemContaPaciente.setContaPaciente(fechamentoContaDTO.getContaPaciente());
        itemContaPaciente.setTipo(ItemContaPaciente.Tipo.EXAME.value());
        itemContaPaciente.setStatus(ItemContaPaciente.Status.ABERTO.value());
        itemContaPaciente.setOrigemLancamento(ItemContaPaciente.OrigemLancamento.FECHAMENTO_CONTA.value());
        itemContaPaciente.setQuantidade(0D);

        if (file != null) {
            itemContaPaciente.setCaminhoAnexo(getCaminhoArquivo(file));
        }

        if (itemEdicao != null) {
            for (int i = 0; i < fechamentoContaDTO.getListaItensContaPaciente().size(); i++) {

                if (fechamentoContaDTO.getListaItensContaPaciente().get(i) == itemEdicao) {
                    fechamentoContaDTO.getListaItensContaPaciente().remove(i);
                    break;
                }
            }
        }

        itemEdicao = null;
        fechamentoContaDTO.getListaItensContaPaciente().add(itemContaPaciente);
        tabela.populate();
        target.add(tabela);
    }

    private String getCaminhoArquivo(FileUpload file) {
        try {
            if (file != null) {
                File newFile = file.writeToTempFile();
                return newFile.getAbsolutePath();
            }
        } catch (IOException ex) {
            Loggable.log.error(ex.getMessage());
        }
        return null;
    }

    public List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ItemContaPaciente proxy = on(ItemContaPaciente.class);
        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("exame"), proxy.getProcedimento().getDescricao()));
        columns.add(createSortableColumn(bundle("dataOcorrencia"), proxy.getDataLancamento()));
        columns.add(createSortableColumn(bundle("quantidadeDias"), proxy.getQuantidadeDias()));
        columns.add(createSortableColumn(bundle("ocorrenciasDia"), proxy.getQuantidadePorDia()));
        columns.add(createSortableColumn(bundle("valor"), proxy.getPrecoUnitario()));
        columns.add(createSortableColumn(bundle("total"), proxy.getValorTotalIpe()));
        columns.add(createSortableColumn(bundle("prestadorServico"), proxy.getEmpresaPrestador().getDescricao()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<ItemContaPaciente>() {
            @Override
            public void customizeColumn(ItemContaPaciente rowObject) {
                addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<ItemContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ItemContaPaciente modelObject) throws ValidacaoException, DAOException {
                        changeStatus(target, modelObject, ItemContaPaciente.Status.CONFIRMADO.value());
                        btnLancamentosConfirmados.setEnabled(true);
                        target.add(btnLancamentosConfirmados);
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<ItemContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ItemContaPaciente modelObject) throws ValidacaoException, DAOException {
                        changeStatus(target, modelObject, ItemContaPaciente.Status.CANCELADO.value());
                        btnLancamentosConfirmados.setEnabled(true);
                        target.add(btnLancamentosConfirmados);
                    }
                });

                addAction(ActionType.EDITAR, rowObject, new IModelAction<ItemContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ItemContaPaciente modelObject) throws ValidacaoException, DAOException {
                        itemEdicao = modelObject;
                        ItemContaPaciente itemClone = (ItemContaPaciente) SerializationUtils.clone(modelObject);
                        abreDialog(target, itemClone);
                    }
                });
            }
        };
    }

    private void changeStatus(AjaxRequestTarget target, ItemContaPaciente icp, Long status) {
        for (int i = 0; i < fechamentoContaDTO.getListaItensContaPaciente().size(); i++) {
            if (fechamentoContaDTO.getListaItensContaPaciente().get(i) == icp) {
                fechamentoContaDTO.getListaItensContaPaciente().get(i).setStatus(status);
                break;
            }
        }
        tabela.populate();
        target.add(tabela);
    }

    private AbstractAjaxButton getBtnLanctosConfirmados() {
        if (btnLancamentosConfirmados == null) {
            btnLancamentosConfirmados = new AbstractAjaxButton("btnLancamentosConfirmados") {
                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    enviaConfirmados(target);
                    dlgLancamentosConfirmadosExamesIpe.show(target);
                }
            };
        }
        btnLancamentosConfirmados.setEnabled(dlgLancamentosConfirmadosExamesIpe != null && !dlgLancamentosConfirmadosExamesIpe.getListaItens().isEmpty() || btnConfirmadoOK);

        return btnLancamentosConfirmados;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {

                examesList = new ArrayList<ItemContaPaciente>();
                for (ItemContaPaciente itemContaPaciente : fechamentoContaDTO.getListaItensContaPaciente()) {
                    if (itemContaPaciente.getTipo().equals(ItemContaPaciente.Tipo.EXAME.value())) {

                        if (itemContaPaciente.getStatus().equals(ItemContaPaciente.Status.ABERTO.value())) {
                            examesList.add(itemContaPaciente);
                        }
                        if (itemContaPaciente.getStatus().equals(ItemContaPaciente.Status.CONFIRMADO.value())) {
                            btnConfirmadoOK = true;
                            getBtnLanctosConfirmados();
                        }
                    }
                }

                return examesList;
            }
        };
    }

    private List<ItemContaPaciente> getListConfirmados() {

        examesConfirmadosList = new ArrayList<ItemContaPaciente>();
        for (ItemContaPaciente itemContaPaciente : fechamentoContaDTO.getListaItensContaPaciente()) {
            if (itemContaPaciente.getTipo().equals(ItemContaPaciente.Tipo.EXAME.value()) && itemContaPaciente.getStatus().equals(ItemContaPaciente.Status.CONFIRMADO.value())) {
                examesConfirmadosList.add(itemContaPaciente);
            }
        }
        return examesConfirmadosList;
    }

    private void enviaConfirmados(AjaxRequestTarget target) {
        if (dlgLancamentosConfirmadosExamesIpe == null) {
            WindowUtil.addModal(target, this, dlgLancamentosConfirmadosExamesIpe = new DlgLancamentosConfirmadosExamesIpe(WindowUtil.newModalId(this)) {
                @Override
                public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                    if (dlgLancamentosConfirmadosExamesIpe.getListaItens().isEmpty()) {
                        btnConfirmadoOK = false;
                    }
                    getBtnLanctosConfirmados();
                    target.add(getBtnLanctosConfirmados());
                }

                @Override
                public void onReverter(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException {
                    changeStatus(target, itemContaPaciente, ItemContaPaciente.Status.ABERTO.value());
                }
            });
        }
        if (CollectionUtils.isNotNullEmpty(getListConfirmados())) {
            dlgLancamentosConfirmadosExamesIpe.setListItem(target, getListConfirmados());
        }
        target.add(getBtnLanctosConfirmados());
    }
}
