package br.com.celk.view.exame.integracao;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;


/**
 *
 * <AUTHOR>
 */
@Private

public class IntegracaoExamePage extends BasePage {
    
    private Form form;
    
    public IntegracaoExamePage() {
        super();
        initForm();
    }

    public void initForm() {
        form = new Form("form", new CompoundPropertyModel(this));
        
        form.add(new AbstractAjaxButton("btnGerar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                BOFactoryWicket.getBO(ExameFacade.class).integrarExamesLaboratorio();
            }
        });
        form.add(new AbstractAjaxButton("btnResultado") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                BOFactoryWicket.getBO(ExameFacade.class).integrarResultadoExameIntegracao();
            }
        });
        
        add(form);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("integracaoExames");
    }


}
