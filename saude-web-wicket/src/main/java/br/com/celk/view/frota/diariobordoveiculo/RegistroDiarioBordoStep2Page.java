package br.com.celk.view.frota.diariobordoveiculo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.duracaofield.HoraMinutoField;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.frota.interfaces.dto.DiarioBordoVeiculoDTO;
import br.com.ksisolucoes.bo.frota.interfaces.facade.FrotaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.frota.DiarioBordoVeiculo;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class RegistroDiarioBordoStep2Page extends BasePage {

    private WebMarkupContainer containerDiario;
    private CompoundPropertyModel<DiarioBordoVeiculoDTO> model;
    private Table<DiarioBordoVeiculo> tblDiarioBordoVeiculoRecemCadastrados;
    private List<DiarioBordoVeiculo> diarioBordoVeiculoRecemCadastradoList = new ArrayList();
    private DateChooser dtChooserDtSaida;
    private DateChooser dtChooserDtChegada;

    public RegistroDiarioBordoStep2Page(DiarioBordoVeiculoDTO dto) {
        model = new CompoundPropertyModel(dto);
    }

    @Override
    protected void postConstruct() {
        Form form = new Form("form", model);

        DiarioBordoVeiculoDTO proxy = on(DiarioBordoVeiculoDTO.class);

        form.add(new DisabledInputField(path(proxy.getDiarioBordoVeiculo().getVeiculo().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getDiarioBordoVeiculo().getMotorista().getNome())));
        

        form.add(containerDiario = new WebMarkupContainer("containerDiario"));
        containerDiario.setOutputMarkupId(true);

        dtChooserDtSaida = new DateChooser(path(proxy.getDataSaida()));
        dtChooserDtSaida.addAjaxUpdateValue();
        dtChooserDtSaida.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                Date data = dtChooserDtSaida.getComponentValue();
                if (data != null) {
                    dtChooserDtChegada.limpar(target);
                    dtChooserDtChegada.setModelObject(data);                    
                    target.add(dtChooserDtChegada);
                }
            }
        });

        containerDiario.add(dtChooserDtSaida);
        containerDiario.add(new HoraMinutoField(path(proxy.getHoraSaida())));

        dtChooserDtChegada = new DateChooser(path(proxy.getDataChegada()));
        dtChooserDtChegada.addAjaxUpdateValue();

        containerDiario.add(dtChooserDtChegada);
        containerDiario.add(new HoraMinutoField(path(proxy.getHoraChegada())));

        containerDiario.add(new LongField(path(proxy.getDiarioBordoVeiculo().getKmInicial())));
        containerDiario.add(new LongField(path(proxy.getDiarioBordoVeiculo().getKmFinal())));

        containerDiario.add(new InputField(path(proxy.getDiarioBordoVeiculo().getDestino())));

        containerDiario.add(new DoubleField(path(proxy.getDiarioBordoVeiculo().getValorDespesaViagem())).setVMax(999999999D));
        containerDiario.add(new DoubleField(path(proxy.getDiarioBordoVeiculo().getValorDespesaVeiculo())).setVMax(999999999D));
        
        containerDiario.add(new InputArea(path(proxy.getDiarioBordoVeiculo().getObservacao())));

        containerDiario.add(new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(target);
            }
        });

        form.add(tblDiarioBordoVeiculoRecemCadastrados = new Table("tblDiarioBordoVeiculoRecemCadastrados", getColumns(), getCollectionProvider()));
        tblDiarioBordoVeiculoRecemCadastrados.populate();

        AbstractAjaxButton btnVoltar;
        form.add(btnVoltar = new AbstractAjaxButton("btnVoltar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new ConsultaDiarioBordoVeiculoPage());
            }

        });
        btnVoltar.setDefaultFormProcessing(false);

        add(form);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();

        DiarioBordoVeiculo proxy = on(DiarioBordoVeiculo.class);

        columns.add(getCustomColumn());
        columns.add(new DateTimeColumn(bundle("saida"), path(proxy.getDataSaida())));
        columns.add(new DateTimeColumn(bundle("chegada"), path(proxy.getDataChegada())));
        columns.add(createColumn(bundle("kmInicial"), proxy.getKmInicial()));
        columns.add(createColumn(bundle("kmFinal"), proxy.getKmFinal()));
        columns.add(createColumn(bundle("kmPercorrido"), proxy.getKmPercorrido()));
        columns.add(createColumn(bundle("destino"), proxy.getDestino()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<DiarioBordoVeiculo>() {
            @Override
            public void customizeColumn(DiarioBordoVeiculo rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<DiarioBordoVeiculo>() {
                    @Override
                    public void action(AjaxRequestTarget target, DiarioBordoVeiculo diarioBordoVeiculo) throws ValidacaoException, DAOException {
                        editar(target, diarioBordoVeiculo);
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<DiarioBordoVeiculo>() {
                    @Override
                    public void action(AjaxRequestTarget target, DiarioBordoVeiculo diarioBordoVeiculo) throws ValidacaoException, DAOException {
                        excluir(target, diarioBordoVeiculo);
                    }
                });
            }
        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return diarioBordoVeiculoRecemCadastradoList;
            }
        };
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        DiarioBordoVeiculo diarioBordoVeiculo = BOFactoryWicket.getBO(FrotaFacade.class).cadastrarDiarioBordoVeiculo(model.getObject());
        adicionar(target, diarioBordoVeiculo);

        DiarioBordoVeiculo newDiarioBordo = new DiarioBordoVeiculo();
        newDiarioBordo.setMotorista(diarioBordoVeiculo.getMotorista());
        newDiarioBordo.setVeiculo(diarioBordoVeiculo.getVeiculo());

        DiarioBordoVeiculoDTO dto = new DiarioBordoVeiculoDTO();
        dto.setDiarioBordoVeiculo(newDiarioBordo);
        dto.setDataSaida(diarioBordoVeiculo.getDataSaida());

        model.setObject(dto);

        target.add(containerDiario);
    }

    private void adicionar(AjaxRequestTarget target, DiarioBordoVeiculo diarioBordoVeiculo) {
        if (diarioBordoVeiculoRecemCadastradoList.contains(diarioBordoVeiculo)) {
            for (int i = 0; i < diarioBordoVeiculoRecemCadastradoList.size(); i++) {
                DiarioBordoVeiculo item = diarioBordoVeiculoRecemCadastradoList.get(i);
                if (item.getCodigo().equals(diarioBordoVeiculo.getCodigo())) {
                    diarioBordoVeiculoRecemCadastradoList.set(i, diarioBordoVeiculo);
                    break;
                }
            }
        } else {
            diarioBordoVeiculoRecemCadastradoList.add(diarioBordoVeiculo);
            Collections.sort(diarioBordoVeiculoRecemCadastradoList, new ComparatorDiarioBordoVeiculo());
        }

        tblDiarioBordoVeiculoRecemCadastrados.update(target);
    }

    private void editar(AjaxRequestTarget target, DiarioBordoVeiculo diarioBordoVeiculo) {
        DiarioBordoVeiculoDTO dto = new DiarioBordoVeiculoDTO();

        dto.setDiarioBordoVeiculo(diarioBordoVeiculo);
        dto.setDataSaida(diarioBordoVeiculo.getDataSaida());
        dto.setHoraSaida(diarioBordoVeiculo.getDataSaida());
        dto.setDataChegada(diarioBordoVeiculo.getDataChegada());
        dto.setHoraChegada(diarioBordoVeiculo.getDataChegada());

        model.setObject(dto);
        target.add(containerDiario);
    }

    private void excluir(AjaxRequestTarget target, DiarioBordoVeiculo diarioBordoVeiculo) throws DAOException, ValidacaoException {
        BOFactoryWicket.delete(diarioBordoVeiculo);
        diarioBordoVeiculoRecemCadastradoList.remove(diarioBordoVeiculo);

        Collections.sort(diarioBordoVeiculoRecemCadastradoList, new ComparatorDiarioBordoVeiculo());
        tblDiarioBordoVeiculoRecemCadastrados.update(target);
    }

    private static class ComparatorDiarioBordoVeiculo implements Comparator<DiarioBordoVeiculo> {

        @Override
        public int compare(DiarioBordoVeiculo d1, DiarioBordoVeiculo d2) {
            int compareTo = d2.getCodigo().compareTo(d1.getCodigo());
            return compareTo;
        }
    };

    @Override
    public String getTituloPrograma() {
        return bundle("registroDiarioBordo");
    }
}
