package br.com.celk.view.vigilancia.rotinas.autoinfracao.dlg;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.table.SelectionTable;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlConsultarRegistroInspecao extends Panel {

    private Long codigo;
    private List<RegistroInspecao> lstRegistroInspecao;
    private SelectionTable tblRegistroInspecao;
    private AbstractAjaxButton btnConsultar;

    public PnlConsultarRegistroInspecao(String id) {
        super(id);
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(new LongField("codigo", new PropertyModel<Long>(this, "codigo")));
        form.add(btnConsultar = new AbstractAjaxButton("btnProcurar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                consultarRegistroInspecao(target);
            }
        });

        form.add(tblRegistroInspecao = new SelectionTable("tblRegistroInspecao", getColumns(), getCollectionProvider()));
        tblRegistroInspecao.addSelectionAction(new ISelectionAction<RegistroInspecao>() {
            @Override
            public void onSelection(AjaxRequestTarget target, RegistroInspecao object) {
                PnlConsultarRegistroInspecao.this.onClick(target, object);
            }
        });
        tblRegistroInspecao.populate();

        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                PnlConsultarRegistroInspecao.this.onFechar(target);
            }
        }.setDefaultFormProcessing(false));
        add(form);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (lstRegistroInspecao == null) {
                    lstRegistroInspecao = new ArrayList<RegistroInspecao>();
                }
                return lstRegistroInspecao;
            }
        };
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        RegistroInspecao proxy = on(RegistroInspecao.class);

        columns.add(createColumn(bundle("codigo"), proxy.getCodigo()));
        columns.add(createColumn(bundle("inspecionado"), proxy.getInspecionado()));

        return columns;
    }

    public abstract void onClick(AjaxRequestTarget target, RegistroInspecao registroInspecao);

    public abstract void onFechar(AjaxRequestTarget target);

    public void consultarRegistroInspecao(AjaxRequestTarget target) {
        LoadManager loadManager = LoadManager.getInstance(RegistroInspecao.class);
        loadManager.addProperties(new HQLProperties(RegistroInspecao.class).getProperties());
        loadManager.addProperties(new HQLProperties(Estabelecimento.class, RegistroInspecao.PROP_ESTABELECIMENTO).getProperties());
        loadManager.addProperties(new HQLProperties(VigilanciaPessoa.class, RegistroInspecao.PROP_VIGILANCIA_PESSOA).getProperties());
        loadManager.addParameter(new QueryCustom.QueryCustomParameter(RegistroInspecao.PROP_CODIGO, codigo));
        loadManager.addSorter(new QueryCustom.QueryCustomSorter(RegistroInspecao.PROP_CODIGO, "desc"));

        lstRegistroInspecao = loadManager.start().getList();

        tblRegistroInspecao.populate();
        tblRegistroInspecao.update(target);
    }
}
