package br.com.celk.view.controle.horarios;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.view.controle.horarios.customize.CustomizeConsultaUsuarioEmpresa;
import br.com.celk.view.controle.usuario.pnl.PnlConsultaUsuario;
import br.com.celk.view.basico.empresa.pnl.PnlConsultaEmpresa;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.HorarioDiaSemana;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.UsuarioEmpresa;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaControleHorariosPage extends ConsultaPage<HorarioDiaSemana, List<BuilderQueryCustom.QueryParameter>> {

    private Usuario usuario;
    private Empresa empresa;

    public ConsultaControleHorariosPage() {
        super();
    }

    public ConsultaControleHorariosPage(PageParameters parameters) {
        super(parameters);
    }
    
    @Override
    public void initForm(Form form) {
        form.setModel(new CompoundPropertyModel(this));
        form.add(new PnlConsultaUsuario("usuario"));
        form.add(new PnlConsultaEmpresa("empresa"));
        getLinkNovo().setVisible(false);
        setExibeExpandir(true);
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(UsuarioEmpresa.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("usuario"), VOUtils.montarPath(UsuarioEmpresa.PROP_USUARIO, Usuario.PROP_NOME)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("empresa"), VOUtils.montarPath(UsuarioEmpresa.PROP_EMPRESA, Empresa.PROP_DESCRICAO)));
        
        return columns;
    }
    
    private CustomColumn getCustomColumn(){
        return new CustomColumn<UsuarioEmpresa>() {

            @Override
            public Component getComponent(String componentId, final UsuarioEmpresa rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroControleHorariosPage(rowObject, getPageParameters()));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroControleHorariosPage(rowObject, true, getPageParameters()));
                    }

                    @Override
                    public boolean isExcluirVisible() {
                        return false;
                    }
                    
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaUsuarioEmpresa());
    }

    @Override
    public List<QueryParameter> getParameters() {
        List<QueryParameter> parameters = new ArrayList<QueryParameter>();
        
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioEmpresa.PROP_USUARIO), usuario));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioEmpresa.PROP_EMPRESA), empresa));
        
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroControleHorariosPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaHorarios");
    }

}
