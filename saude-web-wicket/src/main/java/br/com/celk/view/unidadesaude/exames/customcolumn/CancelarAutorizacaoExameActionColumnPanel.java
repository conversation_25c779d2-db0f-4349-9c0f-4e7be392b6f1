package br.com.celk.view.unidadesaude.exames.customcolumn;

import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public abstract class CancelarAutorizacaoExameActionColumnPanel extends Panel {

    public AbstractAjaxLink btnCancelar;

    public CancelarAutorizacaoExameActionColumnPanel(String id) {
        super(id);
        init();
    }

    private void init() {
        add(btnCancelar = new AbstractAjaxLink("btnCancelar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onCancelar(target);
            }

        });
        
        btnCancelar.add(new AttributeModifier("title", BundleManager.getString("cancelar")));
    }
    
    public abstract void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
}
