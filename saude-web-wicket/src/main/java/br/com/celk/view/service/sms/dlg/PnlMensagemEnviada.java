package br.com.celk.view.service.sms.dlg;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlMensagemEnviada extends Panel {

    private String mensagem;

    public PnlMensagemEnviada(String id) {
        super(id);
        init();
    }

    private void init() {
        Form form = new Form("form");

        form.add(new InputArea("mensagem", new PropertyModel(this, "mensagem")).setEnabled(false));

        AbstractAjaxButton btnFechar;
        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                fechar(target);
            }
        });
        btnFechar.setDefaultFormProcessing(false);

        add(form);
    }

    public void setModelObject(String mensagem) {
        this.mensagem = mensagem;
    }

    public abstract void fechar(AjaxRequestTarget target);
}
