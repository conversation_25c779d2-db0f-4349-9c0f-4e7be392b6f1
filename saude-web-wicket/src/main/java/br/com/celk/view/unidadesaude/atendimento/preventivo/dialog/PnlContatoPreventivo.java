package br.com.celk.view.unidadesaude.atendimento.preventivo.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.unidadesaude.atendimento.preventivo.dto.PreventivoDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.programasaude.Preventivo;
import br.com.ksisolucoes.vo.programasaude.PreventivoOcorrencia;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public abstract class PnlContatoPreventivo extends Panel {

    private CompoundPropertyModel<PreventivoDTO> model;
    private Form form;
    private Table table;
    private Preventivo preventivo;
    private DlgLancarPreventivoOcorrenciaContato dlgLancarOcorrencia;
    private List<PreventivoOcorrencia> lstOcorrencias = new ArrayList<>();

    public PnlContatoPreventivo(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        model = new CompoundPropertyModel(new PreventivoDTO());

        form = new Form("form", model);

        form.setOutputMarkupId(true);

        PreventivoDTO proxy = on(PreventivoDTO.class);
        //TODO - conferir campos
        form.add(new DisabledInputField(path(proxy.getPreventivo().getUsuarioCadsus().getDescricaoSocialFormatado())));
        form.add(new DisabledInputField(path(proxy.getPreventivo().getDataAtendimento())));
        form.add(new DisabledInputField(path(proxy.getPreventivo().getExameRequisicao().getExame().getEmpresaSolicitante().getDescricaoFormatado())));
        form.add(new DisabledInputField(path(proxy.getPreventivo().getProfissional().getDescricaoFormatado())));

        form.add(new DisabledInputField(path(proxy.getPreventivo().getUsuarioCadsus().getTelefone())));
        form.add(new DisabledInputField(path(proxy.getPreventivo().getUsuarioCadsus().getTelefone2())));
        form.add(new DisabledInputField(path(proxy.getPreventivo().getUsuarioCadsus().getTelefone3())));
        form.add(new DisabledInputField(path(proxy.getPreventivo().getUsuarioCadsus().getTelefone4())));
        form.add(new DisabledInputField(path(proxy.getPreventivo().getUsuarioCadsus().getCelular())));
        form.add(new DisabledInputField(path(proxy.getPreventivo().getUsuarioCadsus().getEmail())));

        form.add(table = new Table("tableOcorrencias", getColumns(), getCollectionProvider()));
        table.setScrollY("200px");

        form.add(new AbstractAjaxButton("btnConfirmarContato") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                confirmarContato(target, preventivo);
            }
        });

        form.add(new AbstractAjaxButton("btnLancarOcorrencia") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                viewDlgLancarOcorrencia(target);
            }
        });

        add(form);
    }

    public void setModelObject(AjaxRequestTarget target, PreventivoDTO preventivoDTO) {
        model.setObject(preventivoDTO);
        preventivo = preventivoDTO.getPreventivo();
        update(target);
    }

    private void update(AjaxRequestTarget target) {
        target.add(form);
        table.populate(target);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();

        PreventivoOcorrencia proxy = on(PreventivoOcorrencia.class);
        columns.add(createColumn(bundle("data_hora"), proxy.getDataOcorrencia()));
        columns.add(createColumn(bundle("usuario"), proxy.getUsuarioCadastro().getNome()));
        columns.add(createColumn(bundle("ocorrencia"), proxy.getDescricaoOcorrencia()));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                PreventivoOcorrencia proxy = on(PreventivoOcorrencia.class);
                lstOcorrencias = LoadManager.getInstance(PreventivoOcorrencia.class)
                        .addProperty(path(proxy.getDataOcorrencia()))
                        .addProperty(path(proxy.getUsuarioCadastro().getNome()))
                        .addProperty(path(proxy.getDescricaoOcorrencia()))
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getPreventivo()), preventivo))
                        .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getDataOcorrencia()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                        .start().getList();
                return lstOcorrencias;
            }
        };
    }

    private void viewDlgLancarOcorrencia(AjaxRequestTarget target) {
        if (dlgLancarOcorrencia == null) {
            WindowUtil.addModal(target, this, dlgLancarOcorrencia = new DlgLancarPreventivoOcorrenciaContato(WindowUtil.newModalId(this)) {
                @Override
                public void onSalvar(AjaxRequestTarget target, String ocorrencia) throws DAOException, ValidacaoException {
                    salvarOcorrencia(target, ocorrencia);
                }
            });
        }

        dlgLancarOcorrencia.show(target);
    }

    private void salvarOcorrencia(AjaxRequestTarget target, String ocorrencia) throws DAOException, ValidacaoException {
        PreventivoOcorrencia preventivoOcorrencia = new PreventivoOcorrencia();
        preventivoOcorrencia.setPreventivo(model.getObject().getPreventivo());
        preventivoOcorrencia.setDescricaoOcorrencia(ocorrencia);
        BOFactoryWicket.save(preventivoOcorrencia);
        ocorrencia = null;
        depoisSalvarOcorrencia(target);
    }

    public abstract void confirmarContato(AjaxRequestTarget target, Preventivo preventivo) throws ValidacaoException, DAOException;

    public void depoisSalvarOcorrencia(AjaxRequestTarget target) {
        table.update(target);
    }
}
