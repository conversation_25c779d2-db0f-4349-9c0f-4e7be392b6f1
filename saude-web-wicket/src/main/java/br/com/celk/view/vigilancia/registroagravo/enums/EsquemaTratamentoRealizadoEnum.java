package br.com.celk.view.vigilancia.registroagravo.enums;

import br.com.ksisolucoes.enums.IEnum;

public enum EsquemaTratamentoRealizadoEnum implements IEnum<SimNaoEnum> {

    PENICILINA_G_BEZANTINA_2400(1L, "Penicilina G Bezantina 2.400.000 UI"),
    PENICILINA_G_BEZANTINA_4800(2L, "Penicilina G Bezantina 4.800.000 UI"),
    PENICILINA_G_BEZANTINA_7200(3L, "Penicilina G Bezantina 7.200.000 UI"),
    OUTRO_ESQUEMA(4L, "Outro Esquema"),
    NAO_REALIZADO(4L, "Não Realizado"),
    IGNORADO(9L, "Ignorado");

    private Long value;
    private String descricao;

    EsquemaTratamentoRealizadoEnum(Long value, String descricao) {
        this.value = value;
        this.descricao = descricao;
    }

    @Override
    public Long value() {
        return value;
    }

    @Override
    public String descricao() {
        return descricao;
    }

    public static EsquemaTratamentoRealizadoEnum valueOf(Long value) {
        for (EsquemaTratamentoRealizadoEnum v : EsquemaTratamentoRealizadoEnum.values()) {
            if (v.value().equals(value)) {
                return v;
            }
        }
        return null;
    }
}
