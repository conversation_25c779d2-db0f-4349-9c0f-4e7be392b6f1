package br.com.celk.view.siab.arquivo;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.siab.interfaces.dto.GerarArquivoSiabDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.text.ParseException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlGerarArquivoSiab extends Panel{

    private AbstractAjaxButton btnGerar;
    private AbstractAjaxButton btnFechar;
    private DropDown cbxMes;
    private DropDown cbxAno;
    
    private GerarArquivoSiabDTOParam param;
    
    public PnlGerarArquivoSiab(String id) {
        super(id);
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(param = new GerarArquivoSiabDTOParam()));
        
        form.add(cbxMes = DropDownUtil.getMesesDropDown("mes", true, false));
        form.add(cbxAno = DropDownUtil.getAnoDropDown("ano", true, false));
        
        form.add(btnGerar = new AbstractAjaxButton("btnGerar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                gerar(target);
            }

        });
        
        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                fechar(target);
                sugerirMesAnoAtual(target);
            }

        });
        
        btnFechar.setDefaultFormProcessing(false);
        
        add(form);
        
        try {
            cbxMes.setModelObject(new Integer(Data.getMes(Data.getDataAtual())).longValue());
            cbxAno.setModelObject(new Integer(Data.getAno(Data.getDataAtual())).longValue());
        } catch (ParseException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        
    }
    
    private void sugerirMesAnoAtual(AjaxRequestTarget target){
        try {
            cbxMes.limpar(target);
            cbxAno.limpar(target);
            cbxMes.setModelObject(new Integer(Data.getMes(Data.getDataAtual())).longValue());
            cbxAno.setModelObject(new Integer(Data.getAno(Data.getDataAtual())).longValue());
        } catch (ParseException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }
    
    public void gerar(AjaxRequestTarget target) throws DAOException, ValidacaoException{
        BOFactoryWicket.getBO(BasicoFacade.class).enviarArquivosSiabFila(param);        
        fechar(target);
        sugerirMesAnoAtual(target);
        atualizarTabela(target);
    }
    
    public abstract void atualizarTabela(AjaxRequestTarget target);
    
    public abstract void fechar(AjaxRequestTarget target);

}
