package br.com.celk.view.unidadesaude.receituario;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgCancelarEmissaoReceituario extends Window{

    private PnlCancelarEmissaoReceituario pnlCancelarEmissaoReceituario;
    
    public DlgCancelarEmissaoReceituario(String id) {
        super(id);
        init();
    }
    
    private void init(){
        setOutputMarkupId(true);
        
        setInitialWidth(700);
        setInitialHeight(250);
        
        setResizable(false);
        
        setTitle(BundleManager.getString("motivoCancelamento"));
        
        setContent(pnlCancelarEmissaoReceituario = new PnlCancelarEmissaoReceituario(getContentId()) {

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                limpar(target);
                close(target);
                onClose(target);
            }
        });
    }
    
    public void setModelObject(Atendimento atendimento) {
        pnlCancelarEmissaoReceituario.setModelObject(atendimento);
    }

    public void update(AjaxRequestTarget target){
        pnlCancelarEmissaoReceituario.update(target);
    }
    
    public abstract void onClose(AjaxRequestTarget target) throws ValidacaoException, DAOException;
}
