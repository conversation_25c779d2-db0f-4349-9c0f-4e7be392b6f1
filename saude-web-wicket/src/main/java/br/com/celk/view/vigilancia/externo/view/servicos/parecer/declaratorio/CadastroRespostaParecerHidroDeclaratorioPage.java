package br.com.celk.view.vigilancia.externo.view.servicos.parecer.declaratorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputarea.RequiredInputArea;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.tinymce.EditorBehavior;
import br.com.celk.component.tinymce.SimpleEditorSettings;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.vigilancia.externo.template.base.BasePageVigilancia;
import br.com.celk.view.vigilancia.externo.view.consulta.ConsultaRequerimentoVigilanciaExternoPage;
import br.com.celk.view.vigilancia.helper.VigilanciaPageHelper;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoExumacaoPage;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlConsultaRequerimentoVigilanciaAnexo;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlRequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.AnexoPranchaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ImpressaoParecerProjetoHidrossanitarioDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.PnlRequerimentoVigilanciaAnexoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaAnexoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.StringUtilKsi;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioDeclaratorio;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioDeclaratorioParecer;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecerResposta;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.enums.RequerimentosProjetosEnums;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperExportManager;
import org.apache.commons.lang.SerializationUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.upload.FileUpload;
import org.apache.wicket.markup.html.form.upload.FileUploadField;
import org.apache.wicket.markup.html.panel.EmptyPanel;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;
import org.apache.wicket.util.resource.FileResourceStream;
import org.apache.wicket.util.resource.IResourceStream;
import org.joda.time.DateTime;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;


@Private
public class CadastroRespostaParecerHidroDeclaratorioPage extends BasePageVigilancia {
    private RequerimentoVigilancia requerimentoVigilancia;
    private Class returnClass;
    private Form<RequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta> form;
    private CompoundPropertyModel<RequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta> model = new CompoundPropertyModel<>(new RequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta());
    private PageParameters parameters;
    private RequiredInputArea txaResposta;
    private PnlRequerimentoVigilanciaAnexo pnlRequerimentoVigilanciaAnexo;
    private boolean enableForm = true;
    private AjaxPreviewBlank ajaxPreviewBlank;
    private AbstractAjaxButton btnSalvar;
    private AbstractAjaxButton btnSalvarEnviar;

    private CompoundPropertyModel<AnexoPranchaDTO> modelAnexoPrancha;
    private WebMarkupContainer containerAnexosPrancha;
    private FileUpload upload;
    private FileUploadField fileUploadField;
    private List<FileUpload> lstUpload;
    private Table tblAnexoPrancha;
    private List<AnexoPranchaDTO> anexosPranchaList = new ArrayList<>();
    private List<AnexoPranchaDTO> anexosPranchaExcluirList = new ArrayList<>();

    public CadastroRespostaParecerHidroDeclaratorioPage(RequerimentoVigilancia requerimentoVigilancia, RequerimentoProjetoHidrossanitarioDeclaratorioParecer parecer, PageParameters parameters, Class returnClass) {
        super(parameters);
        this.parameters = parameters;
        this.returnClass = returnClass;
        this.requerimentoVigilancia = requerimentoVigilancia;
        carregarResposta(parecer);
        init();
    }

    private void init() {
        RequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta proxy = on(RequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta.class);
        getForm().add(new MultiLineLabel(path(proxy.getRequerimentoProjetoHidrossanitarioDeclaratorioParecer().getDescricaoParecer())).setEscapeModelStrings(false));

        getForm().add(txaResposta = new RequiredInputArea(path(proxy.getDescricaoResposta())));
        txaResposta.add(new EditorBehavior(new SimpleEditorSettings(910, 500, true)));
        txaResposta.setEnabled(enableForm);
        txaResposta.setLabel(new Model(bundle("resposta")));

        addCamposAnexos();

        getForm().add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                retornarPaginaConsulta();
            }
        }.setDefaultFormProcessing(false));

        getForm().add(btnSalvar = new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(target, false, false);
            }
        });

        getForm().add(btnSalvarEnviar = new AbstractAjaxButton("btnSalvarEnviar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(target, true, true);
            }
        });

        addPanelAnexosParecer();

        getForm().add(getContainerAnexoPrancha());

        getForm().add(ajaxPreviewBlank = new AjaxPreviewBlank());
        add(getForm());
    }

    private Form getForm() {
        if (this.form == null) {
            this.form = new Form<>("form", model);
        }
        return this.form;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("projetoHidrossanitario") + " - " + BundleManager.getString("parecerTecnico") + " - " + BundleManager.getString("resposta");
    }

    private void salvar(AjaxRequestTarget target, boolean enviar, boolean imprimir) throws ValidacaoException, DAOException {
        if (validarCampoRespostaPreenchido(model.getObject())) {
            throw new ValidacaoException(bundle("msgInformeResposta"));
        }
        if (enviar) {
            model.getObject().setSituacao(RequerimentosProjetosEnums.SituacaoParecer.ENVIADO.value());
            model.getObject().getRequerimentoProjetoHidrossanitarioDeclaratorioParecer().setDataRetorno(DataUtil.getDataAtual());
        } else {
            model.getObject().setSituacao(RequerimentoProjetoHidrossanitarioParecerResposta.Situacao.CADASTRADO.value());
        }
        RequerimentoVigilancia requerimentoVigilancia = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarRequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta(model.getObject(), pnlRequerimentoVigilanciaAnexo.getPnlRequerimentoVigilanciaAnexoDTO(), anexosPranchaList, anexosPranchaExcluirList);

        if (imprimir) {
            imprimirParecer(target, requerimentoVigilancia);
            pnlRequerimentoVigilanciaAnexo.setEnabled(false);
            txaResposta.setEnabled(false);
            btnSalvarEnviar.setVisible(false);
            btnSalvar.setVisible(false);
            target.add(btnSalvarEnviar);
            target.add(btnSalvar);
        } else {
            retornarPaginaConsulta();
        }
    }

    private boolean validarCampoRespostaPreenchido(RequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta requerimentoProjetoHidrossanitarioParecerResposta) {
        return StringUtils.trimToNull(StringUtilKsi.removeHtmlString(requerimentoProjetoHidrossanitarioParecerResposta.getDescricaoResposta())) == null;
    }

    private void retornarPaginaConsulta() {
        setResponsePage(returnClass);
    }

    private void imprimirParecer(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia) {
        try {
            ImpressaoParecerProjetoHidrossanitarioDTOParam param = new ImpressaoParecerProjetoHidrossanitarioDTOParam();
            param.setRequerimentoVigilancia(requerimentoVigilancia);

            param.setUrlQRcode(VigilanciaHelper.getURLQRCodePageRequerimento());
            param.setChaveQrcode(requerimentoVigilancia.getChaveQRcode());

            DataReport dataReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoParecerProjetoHidrossanitarioDeclaratorio(param);

            File file = File.createTempFile("parecer", ".pdf");
            JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), file.getAbsolutePath());

            IResourceStream resourceStream = new FileResourceStream(new org.apache.wicket.util.file.File(file));
            ajaxPreviewBlank.initiate(target, "Parecer.pdf", resourceStream);
        } catch (ReportException | IOException | JRException ex) {
            Logger.getLogger(ConsultaRequerimentoVigilanciaExternoPage.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private void addCamposAnexos() {
        {//Inicio Anexos
            PnlRequerimentoVigilanciaAnexoDTO dtoPnlAnexo = new PnlRequerimentoVigilanciaAnexoDTO();
            dtoPnlAnexo.setRequerimentoVigilanciaAnexoDTOList(carregarAnexos(model.getObject()));
            requerimentoVigilancia.getTipoSolicitacao().setTipoDocumento(TipoSolicitacao.TipoDocumento.ANALISE_PROJETO_HIDROSSANITARIO_DECLARATORIO.value());
            dtoPnlAnexo.setTipoSolicitacao(requerimentoVigilancia.getTipoSolicitacao());
            getForm().add(pnlRequerimentoVigilanciaAnexo = new PnlRequerimentoVigilanciaAnexo(dtoPnlAnexo, enableForm));
            pnlRequerimentoVigilanciaAnexo.setOutputMarkupId(true);
        }
    }

    private List<RequerimentoVigilanciaAnexoDTO> carregarAnexos(RequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta requerimentoProjetoHidrossanitarioParecerResposta) {
        List<RequerimentoVigilanciaAnexo> list = VigilanciaHelper.carregarAnexosVigilancia(requerimentoProjetoHidrossanitarioParecerResposta);

        List<RequerimentoVigilanciaAnexoDTO> listRequerimentoVigilanciaAnexoDTOList = new ArrayList<>();
        RequerimentoVigilanciaAnexoDTO anexoDTO;
        for (RequerimentoVigilanciaAnexo rva : list) {
            anexoDTO = new RequerimentoVigilanciaAnexoDTO();
            anexoDTO.setDescricaoAnexo(rva.getDescricao());
            anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
            anexoDTO.setRequerimentoVigilanciaAnexo(rva);

            listRequerimentoVigilanciaAnexoDTOList.add(anexoDTO);
        }
        return listRequerimentoVigilanciaAnexoDTOList;
    }

    private void carregarResposta(RequerimentoProjetoHidrossanitarioDeclaratorioParecer parecer) {
        RequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta resposta = LoadManager.getInstance(RequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta.class)
                .addProperties(new HQLProperties(RequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta.class).getProperties())
                .addProperties(new HQLProperties(RequerimentoProjetoHidrossanitarioDeclaratorioParecer.class, RequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta.PROP_REQUERIMENTO_PROJETO_HIDROSSANITARIO_DECLARATORIO_PARECER).getProperties())
                .addProperties(new HQLProperties(RequerimentoProjetoHidrossanitarioDeclaratorio.class, VOUtils.montarPath(RequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta.PROP_REQUERIMENTO_PROJETO_HIDROSSANITARIO_DECLARATORIO_PARECER, RequerimentoProjetoHidrossanitarioDeclaratorioParecer.PROP_REQUERIMENTO_PROJETO_HIDROSSANITARIO_DECLARATORIO)).getProperties())
                .addProperties(new HQLProperties(RequerimentoVigilancia.class, VOUtils.montarPath(RequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta.PROP_REQUERIMENTO_PROJETO_HIDROSSANITARIO_DECLARATORIO_PARECER, RequerimentoProjetoHidrossanitarioDeclaratorioParecer.PROP_REQUERIMENTO_PROJETO_HIDROSSANITARIO_DECLARATORIO, RequerimentoProjetoHidrossanitarioDeclaratorio.PROP_REQUERIMENTO_VIGILANCIA)).getProperties())
                .addProperties(new HQLProperties(TipoSolicitacao.class, VOUtils.montarPath(RequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta.PROP_REQUERIMENTO_PROJETO_HIDROSSANITARIO_DECLARATORIO_PARECER, RequerimentoProjetoHidrossanitarioDeclaratorioParecer.PROP_REQUERIMENTO_PROJETO_HIDROSSANITARIO_DECLARATORIO, RequerimentoProjetoHidrossanitarioDeclaratorio.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_TIPO_SOLICITACAO)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta.PROP_REQUERIMENTO_PROJETO_HIDROSSANITARIO_DECLARATORIO_PARECER, parecer))
                .start().getVO();

        if (resposta != null && resposta.getCodigo() != null) {
            if (RequerimentosProjetosEnums.SituacaoParecer.CADASTRADO.value().equals(resposta.getSituacao())) {
                model.setObject(resposta);
            } else {
                getForm().setEnabled(false);
            }
            anexosPranchaList = VigilanciaPageHelper.carregarAnexosPranchasDeclaratorio(resposta);
        } else {
            model.setObject(new RequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta());
            model.getObject().setRequerimentoProjetoHidrossanitarioDeclaratorioParecer(parecer);
        }
    }

    private void addPanelAnexosParecer() {
        Panel panel;
        PnlRequerimentoVigilanciaAnexoDTO dtoAnexos = getDtoAnexos();
        if (CollectionUtils.isNotNullEmpty(dtoAnexos.getRequerimentoVigilanciaAnexoDTOList())) {
            panel = new PnlConsultaRequerimentoVigilanciaAnexo("requerimentoVigilanciaAnexoParecer", dtoAnexos);
        } else {
            panel = new EmptyPanel("requerimentoVigilanciaAnexoParecer");
            panel.setVisible(false);
        }
        getForm().add(panel);
    }

    private PnlRequerimentoVigilanciaAnexoDTO getDtoAnexos() {
        List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
        List<RequerimentoVigilanciaAnexo> listObjects = VigilanciaHelper.carregarAnexosVigilancia(model.getObject().getRequerimentoProjetoHidrossanitarioDeclaratorioParecer());
        RequerimentoVigilanciaAnexoDTO anexoDTO;
        for (RequerimentoVigilanciaAnexo rva : listObjects) {
            anexoDTO = new RequerimentoVigilanciaAnexoDTO();
            anexoDTO.setDescricaoAnexo(rva.getDescricao());
            anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
            anexoDTO.setRequerimentoVigilanciaAnexo(rva);
            requerimentoVigilanciaAnexoDTOList.add(anexoDTO);
        }
        PnlRequerimentoVigilanciaAnexoDTO dtoPnlAnexo = new PnlRequerimentoVigilanciaAnexoDTO();
        dtoPnlAnexo.setRequerimentoVigilanciaAnexoDTOList(requerimentoVigilanciaAnexoDTOList);
        return dtoPnlAnexo;
    }

    private WebMarkupContainer getContainerAnexoPrancha() {
        containerAnexosPrancha = new WebMarkupContainer("containerAnexosPrancha", modelAnexoPrancha = new CompoundPropertyModel<>(new AnexoPranchaDTO()));
        containerAnexosPrancha.setOutputMarkupPlaceholderTag(true);

        fileUploadField = new FileUploadField("upload", new PropertyModel<List<FileUpload>>(this, "lstUpload"));
        containerAnexosPrancha.add(fileUploadField);

        AbstractAjaxButton btnAdicionar;
        this.containerAnexosPrancha.add(btnAdicionar = new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarPrancha(target);
            }
        });
        btnAdicionar.setDefaultFormProcessing(false);
        tblAnexoPrancha = new Table("tblAnexoPrancha", getColumnsAnexoPrancha(), getCollectionProviderAnexoPrancha());
        tblAnexoPrancha.setScrollXInner("930px");
        tblAnexoPrancha.populate();
        containerAnexosPrancha.add(tblAnexoPrancha);

        return containerAnexosPrancha;
    }

    private List<IColumn> getColumnsAnexoPrancha() {
        List<IColumn> columns = new ArrayList<>();
        AnexoPranchaDTO proxy = on(AnexoPranchaDTO.class);

        columns.add(getActionColumnAnexo());
        columns.add(createColumn(bundle("status"), proxy.getHidrossanitarioDeclaratorioAnexo().getDescricaoStatus()));
        columns.add(createColumn(bundle("anexo"), proxy.getDescricaoAnexo()));
        columns.add(new DateColumn(bundle("data"), path(proxy.getDataCadastro())).setPattern("dd/MM/yyyy - HH:mm"));

        return columns;
    }

    private ICollectionProvider getCollectionProviderAnexoPrancha() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return anexosPranchaList;
            }
        };
    }

    private IColumn getActionColumnAnexo() {
        return new MultipleActionCustomColumn<AnexoPranchaDTO>() {
            @Override
            public void customizeColumn(final AnexoPranchaDTO rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<AnexoPranchaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AnexoPranchaDTO modelObject) throws ValidacaoException, DAOException {
                        for (int i = 0; i < anexosPranchaList.size(); i++) {
                            AnexoPranchaDTO item = anexosPranchaList.get(i);
                            if (item == rowObject) {
                                if (item.getHidrossanitarioDeclaratorioAnexo() != null && item.getHidrossanitarioDeclaratorioAnexo().getCodigo() != null) {
                                    anexosPranchaExcluirList.add(item);
                                }
                                anexosPranchaList.remove(i);
                            }
                        }
                        tblAnexoPrancha.populate();
                        tblAnexoPrancha.update(target);
                    }
                });
            }
        };
    }

    private void adicionarPrancha(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        AnexoPranchaDTO anexo = (AnexoPranchaDTO) SerializationUtils.clone(modelAnexoPrancha.getObject());

        upload = fileUploadField.getFileUpload();
        if (upload == null) {
            throw new ValidacaoException(bundle("selecioneAnexo"));
        }
        try {
            Long tamanhoAnexoPranchas = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.VIGILANCIA_SANITARIA).getParametro("tamanhoAnexoPranchas");
            if (tamanhoAnexoPranchas != null) {
                if ((upload.getSize() / 1024) > tamanhoAnexoPranchas) {
                    throw new ValidacaoException(BundleManager.getString("msgTamanhoMaximoAnexoXMB", tamanhoAnexoPranchas));
                }
            } else if (upload.getSize() > 1048576L) {
                throw new ValidacaoException(BundleManager.getString("msgTamanhoMaximoAnexo1MB"));
            }

            if (upload.getClientFileName().endsWith(".pdf")) {
                File file = File.createTempFile("anexo", upload.getClientFileName());

                upload.writeTo(file);
                anexo.setFile(file);
                anexo.setDescricaoAnexo(upload.getClientFileName());
                anexo.setDataCadastro(DateTime.now().toDate());
                anexo.getHidrossanitarioDeclaratorioAnexo().setSituacao(RequerimentosProjetosEnums.Status.PENDENTE.value());
                anexo.setOrigemArquivo(GerenciadorArquivo.OrigemArquivo.PRANCHA_PROJ_HIDRO_DECLARATORIO);

                anexosPranchaList.add(anexo);

                tblAnexoPrancha.populate();
                tblAnexoPrancha.update(target);

                modelAnexoPrancha.setObject(new AnexoPranchaDTO());
                upload = null;
                target.add(containerAnexosPrancha);
            } else {
                throw new ValidacaoException(bundle("somentePossivelAnexarPdf"));
            }
        } catch (IOException ex) {
            Logger.getLogger(RequerimentoExumacaoPage.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
}
