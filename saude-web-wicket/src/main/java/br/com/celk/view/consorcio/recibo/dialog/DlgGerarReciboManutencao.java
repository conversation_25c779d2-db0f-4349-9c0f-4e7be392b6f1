package br.com.celk.view.consorcio.recibo.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.consorcio.pedidotransferencialicitacao.dialog.PnlImprimirRecibo;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacaoEntrega;
import br.com.ksisolucoes.vo.consorcio.ReciboConsorcio;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR> <PERSON>
 */
public class DlgGerarReciboManutencao extends Window{

    private PnlGerarReciboManutencao pnlGerarReciboManutencao;

    public DlgGerarReciboManutencao(String id) {
        super(id);

        setTitle(BundleManager.getString("gerarReciboManutencao"));
      
        setInitialWidth(800);
        setInitialHeight(600);
        
        setResizable(false);
        
        setContent(pnlGerarReciboManutencao = new PnlGerarReciboManutencao(getContentId()) {

            @Override
            protected void onFechar(AjaxRequestTarget target) {
                close(target);
            }

        });
    }
}
