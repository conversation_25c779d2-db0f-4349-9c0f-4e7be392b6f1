package br.com.celk.view.vigilancia.rotinas.autopenalidade;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.duracaofield.HoraMinutoField;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.ComponentUtils;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.autodepenalidade.autocomplete.AutoCompleteConsultaTipoPenalidade;
import br.com.celk.view.vigilancia.endereco.DlgCadastroVigilanciaEndereco;
import br.com.celk.view.vigilancia.endereco.autocomplete.AutoCompleteConsultaVigilanciaEndereco;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.pessoa.DlgCadastroVigilanciaPessoa;
import br.com.celk.view.vigilancia.pessoa.autocomplete.AutoCompleteConsultaVigilanciaPessoa;
import br.com.celk.view.vigilancia.rotinas.autoIntimacao.autocomplete.AutoCompleteConsultaMotivoRetorno;
import br.com.celk.view.vigilancia.rotinas.autoIntimacao.dialog.DlgCadastroMotivoRetorno;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.*;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracaoProvidencia;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.MotivoRetorno;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaEnum;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import ch.lambdaj.Lambda;
import org.apache.commons.lang.NotImplementedException;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.TextArea;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.hamcrest.Matchers;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class CadastroAutoPenalidadePage extends CadastroPage<AutoPenalidade> {

    private InputField txtDenuncia;
    private InputField txtAutoInfracao;
    private InputField txtAutoIntimacao;
    private InputField txtRoteiroInspecao;
    private InputField txtProcessoAdministrativo;
    private RequiredDateChooser dchPenalidade;

    //campos Autuado
    private DropDown dropDownTipoPessoa;
    private AutoCompleteConsultaVigilanciaPessoa autoCompleteConsultaVigilanciaPessoa;
    private AutoCompleteConsultaEstabelecimento autoCompleteConsultaEstabelecimento;

    //campos do endereço
    private AutoCompleteConsultaVigilanciaEndereco autoCompleteConsultaVigilanciaEndereco;
    private DlgCadastroVigilanciaEndereco dlgCadastroVigilanciaEndereco;
    private LongField txtNumeroEndereco;

    //campos responsavel
    private InputField txtNomeResponsavel;
    private Date hora;
    private CheckBoxLongValue chkRecusouAssinar;
    private InputField txtTestemunha;

    //campos fiscal
    private CompoundPropertyModel<AutoPenalidadeFiscal> modelFiscal;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private Table tblFiscal;
    private List<AutoPenalidadeFiscal> lstFiscal = new ArrayList<>();
    // campos dados da penalidade
    private WebMarkupContainer containerDadosPenalidade;
    private CompoundPropertyModel<AutoPenalidadeItem> modelPenalidade;
    private AutoCompleteConsultaTipoPenalidade autoCompleteConsultaTipoPenalidade;
    private Table tblPenalidades;
    private List<AutoPenalidadeItem> lstPenalidades = new ArrayList<>();
    private InputArea txaObservacaoPenalidade;
    private DoubleField doubleValorMulta;
    // campos dados da infracao
    private WebMarkupContainer containerDadosInfracao;
    private CompoundPropertyModel<AutoPenalidadeInfracao> modelInfracao;
    private Table tblInfracoes;
    private List<AutoPenalidadeInfracao> lstInfracoes = new ArrayList<>();
    private InputArea txaInfracao;
    private InputArea txaObservacaoInfracao;
    private InputArea txaLegislacao;

    private WebMarkupContainer containerDadosEdicao;
    private WebMarkupContainer containerDadosGeral;
    private WebMarkupContainer containerAutuado;
    private WebMarkupContainer containerAutuadoEstabelecimento;
    private WebMarkupContainer containerAutuadoPessoa;
    private WebMarkupContainer containerEnquadramento;
    private WebMarkupContainer containerResponsavel;
    private WebMarkupContainer containerFiscal;

    private ConfiguracaoVigilancia configuracaoVigilancia;
    private DlgCadastroVigilanciaPessoa dlgCadastroVigilanciaPessoa;
    private WebMarkupContainer containerNumeroEnderecoPessoa;
    private WebMarkupContainer containerEnvio;
    private DateChooser dchRecebimento;
    private AutoCompleteConsultaMotivoRetorno autoCompleteConsultaMotivoRetorno;
    private AbstractAjaxLink btnCadastrarMotivo;
    private DlgCadastroMotivoRetorno dlgCadastroMotivoRetorno;
    private DlgImpressaoObject<AutoPenalidade> dlgConfirmacaoImpressao;
    private Integer idxInfracao;
    private Integer idxPenalidade;

    public CadastroAutoPenalidadePage() {
    }

    public CadastroAutoPenalidadePage(AutoPenalidade object) {
        super(object);
    }

    public CadastroAutoPenalidadePage(AutoPenalidade object, boolean viewOnly) {
        super(object, viewOnly);
    }

    @Override
    public void init(Form<AutoPenalidade> form) {
        AutoPenalidade proxy = on(AutoPenalidade.class);

        addContainerDadosEdicao(form, proxy);

        addContainerDadosGeral(form, proxy);

        addContainerAutuado(form, proxy);

        addContainerEnquadramentoLegal(form, proxy);

        addContainerDadosInfracao(form);

        addContainerDadosPenalidade(form);

        addContainerResponsavel(form, proxy);

        addContainerFiscal(form);

        addContainerEnvio(form, proxy);

        configForm();

        this.getBtnSalvar().setVisible(false);
        this.getControls().add(getSalvarButton());
    }

    private SubmitButton getSalvarButton() {
        SubmitButton btnSalvar = new SubmitButton(this.getControls().newChildId(), new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvarAction(target, (AutoPenalidade) form.getModel().getObject());
            }
        });
        btnSalvar.add(new AttributeModifier("class", "save"));
        btnSalvar.add(new AttributeModifier("value", bundle("salvar")));
        btnSalvar.setVisible(!isViewOnly());
        return btnSalvar;
    }

    private void addContainerEnquadramentoLegal(Form<AutoPenalidade> form, AutoPenalidade proxy) {
        form.add(containerEnquadramento = new WebMarkupContainer("containerEnquadramento"));
        containerEnquadramento.add(new TextArea(path(proxy.getEnquadramentoLegal())));
        containerEnquadramento.setOutputMarkupId(true);
    }

    private void configForm() {
        if (configuracaoVigilancia == null) {
            try {
                configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
            } catch (ValidacaoException e) {
                Loggable.log.error(e.getMessage(), e);
            }
        }
        if (isViewOnly()) {
            containerDadosEdicao.setVisible(true);
        } else {
            containerDadosEdicao.setVisible(false);
        }
        txtAutoInfracao.setEnabled(false);
        txtDenuncia.setEnabled(false);
        txtAutoIntimacao.setEnabled(false);
        txtRoteiroInspecao.setEnabled(false);
        txtProcessoAdministrativo.setEnabled(false);

        if (getForm().getModelObject().getTipoAutuado() == null) {
            getForm().getModelObject().setTipoAutuado(AutoPenalidade.TipoAutuado.ESTABELECIMENTO.value());
        }
        if (AutoIntimacao.TipoDenunciado.ESTABELECIMENTO.value().equals(getForm().getModel().getObject().getTipoAutuado())) {
            containerNumeroEnderecoPessoa.setVisible(false);
        }

        if (getForm().getModelObject().getDataPenalidade() == null) {
            getForm().getModelObject().setDataPenalidade(DataUtil.getDataAtual());
        }

        enableCamposAutuado(null, false);

        if (getForm().getModel().getObject().getCodigo() != null) {
            containerAutuado.setEnabled(false);
        }
    }

    private void addContainerDadosEdicao(Form<AutoPenalidade> form, AutoPenalidade proxy) {
        form.add(containerDadosEdicao = new WebMarkupContainer("containerDadosEdicao"));
        containerDadosEdicao.add(new DisabledInputField(path(proxy.getUsuario().getNome())));
        containerDadosEdicao.add(new DateChooser(path(proxy.getDataCadastro())).setEnabled(false));
        containerDadosEdicao.add(new DisabledInputField(path(proxy.getUsuarioEdicao().getNome())));
        containerDadosEdicao.add(new DateChooser(path(proxy.getDataUsuario())).setEnabled(false));
    }

    private void addContainerDadosGeral(Form<AutoPenalidade> form, AutoPenalidade proxy) {
        form.add(containerDadosGeral = new WebMarkupContainer("containerDadosGeral"));

        containerDadosGeral.add(new DisabledInputField(path(proxy.getNumeroFormatado())));

        containerDadosGeral.add(new InputField(path(proxy.getNumeroFormulario())));

        containerDadosGeral.add(new DisabledInputField(path(proxy.getRequerimentoVigilancia().getProtocoloFormatado())));

        containerDadosGeral.add(dchPenalidade = new RequiredDateChooser(path(proxy.getDataPenalidade())));
        dchPenalidade.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        if (form.getModelObject().getDataPenalidade() == null) {
            form.getModelObject().setDataPenalidade(DataUtil.getDataAtual());
        }

        addCampoDenuncia(form, proxy);

        addCampoAutoIntimacao(form, proxy);

        addCampoAutoInfracao(form, proxy);

        addCampoRoteiroInspecao(form, proxy);

        addCampoProcessoAdministrativo(form, proxy);

        containerDadosGeral.setOutputMarkupId(true);
    }

    private void addCampoAutoInfracao(Form<AutoPenalidade> form, AutoPenalidade proxy) {
        containerDadosGeral.add(txtAutoInfracao = new InputField(path(proxy.getAutoInfracao().getNumeroFormatado())));
    }

    private void addCampoDenuncia(Form<AutoPenalidade> form, AutoPenalidade proxy) {
        containerDadosGeral.add(txtDenuncia = new InputField(path(proxy.getDenuncia().getCodigo())));
    }

    private void addCampoAutoIntimacao(Form<AutoPenalidade> form, AutoPenalidade proxy) {
        containerDadosGeral.add(txtAutoIntimacao = new InputField(path(proxy.getAutoIntimacao().getNumeroFormatado())));
    }

    private void addCampoRoteiroInspecao(Form<AutoPenalidade> form, AutoPenalidade proxy) {
        containerDadosGeral.add(txtRoteiroInspecao = new InputField(path(proxy.getRegistroInspecao().getCodigo())));
    }

    private void addCampoProcessoAdministrativo(Form<AutoPenalidade> form, AutoPenalidade proxy) {
        containerDadosGeral.add(txtProcessoAdministrativo = new InputField(path(proxy.getProcessoAdministrativo().getNumeroProcessoFormatado())));
    }

    private void addContainerAutuado(Form<AutoPenalidade> form, AutoPenalidade proxy) {

        form.add(containerAutuado = new WebMarkupContainer("containerAutuado"));
        containerAutuado.setOutputMarkupId(true);

        containerAutuado.add(getDropDownTipoPessoa(proxy));

        containerAutuado.add(getContainerDenunciadoPessoa(proxy));

        containerAutuado.add(getContainerDenunciadoEstabelecimento(proxy));

        addCamposEndereco(proxy);

    }

    private DropDown getDropDownTipoPessoa(AutoPenalidade proxy) {
        dropDownTipoPessoa = DropDownUtil.getIEnumDropDown(path(proxy.getTipoAutuado()), AutoPenalidade.TipoAutuado.values(), false, true);
        dropDownTipoPessoa.addAjaxUpdateValue();
        dropDownTipoPessoa.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                enableCamposAutuado(target, true);
            }
        });
        return dropDownTipoPessoa;
    }

    private WebMarkupContainer getContainerDenunciadoPessoa(AutoPenalidade proxy) {
        containerAutuadoPessoa = new WebMarkupContainer("containerAutuadoPessoa");
        containerAutuadoPessoa.setOutputMarkupPlaceholderTag(true);

        containerAutuadoPessoa.add(autoCompleteConsultaVigilanciaPessoa = new AutoCompleteConsultaVigilanciaPessoa(path(proxy.getVigilanciaPessoa()), true));
        autoCompleteConsultaVigilanciaPessoa.setOutputMarkupPlaceholderTag(true);
        autoCompleteConsultaVigilanciaPessoa.add(new ConsultaListener<VigilanciaPessoa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, VigilanciaPessoa object) {
                atualizarEnderecoPessoa(target, object);
            }
        });

        autoCompleteConsultaVigilanciaPessoa.add(new RemoveListener<VigilanciaPessoa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, VigilanciaPessoa object) {
                atualizarEnderecoPessoa(target, null);
            }
        });

        return containerAutuadoPessoa;
    }

    private void atualizarEnderecoEstabelecimento(AjaxRequestTarget target, Estabelecimento estabelecimento) {
        autoCompleteConsultaVigilanciaEndereco.limpar(target);

        if (estabelecimento != null) {
            VigilanciaEndereco ve = VigilanciaHelper.carregarVigilanciaEnderecoEstabelecimento(estabelecimento);
            getForm().getModelObject().setVigilanciaEndereco(ve);
            autoCompleteConsultaVigilanciaEndereco.setComponentValue(target, ve);

            adicionarFromEstabelecimento(target, estabelecimento.getRepresentanteNome());
        }
    }

    private void atualizarEnderecoPessoa(AjaxRequestTarget target, VigilanciaPessoa vigilanciaPessoa) {
        autoCompleteConsultaVigilanciaEndereco.limpar(target);
        txtNumeroEndereco.limpar(target);

        if (vigilanciaPessoa != null) {
            VigilanciaPessoa vo = LoadManager.getInstance(VigilanciaPessoa.class)
                    .addProperty(VigilanciaPessoa.PROP_NUMERO_LOGRADOURO)
                    .setId(vigilanciaPessoa.getCodigo())
                    .start().getVO();
            VigilanciaEndereco ve = VigilanciaHelper.carregarVigilanciaEnderecoPessoa(vigilanciaPessoa);
            getForm().getModelObject().setVigilanciaEndereco(ve);
            txtNumeroEndereco.setComponentValue(vo.getNumeroLogradouro());
            autoCompleteConsultaVigilanciaEndereco.setComponentValue(target, ve);
            target.add(txtNumeroEndereco);
        }
    }

    private WebMarkupContainer getContainerDenunciadoEstabelecimento(AutoPenalidade proxy) {
        containerAutuadoEstabelecimento = new WebMarkupContainer("containerAutuadoEstabelecimento");
        containerAutuadoEstabelecimento.setOutputMarkupPlaceholderTag(true);
        containerAutuadoEstabelecimento.add(autoCompleteConsultaEstabelecimento = new AutoCompleteConsultaEstabelecimento(path(proxy.getEstabelecimentoAutuado()), true));
        autoCompleteConsultaEstabelecimento.setOutputMarkupPlaceholderTag(true);

        autoCompleteConsultaEstabelecimento.add(new ConsultaListener<Estabelecimento>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Estabelecimento object) {
                atualizarEnderecoEstabelecimento(target, object);
            }
        });

        autoCompleteConsultaEstabelecimento.add(new RemoveListener<Estabelecimento>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Estabelecimento object) {
                atualizarEnderecoEstabelecimento(target, null);
            }
        });

        return containerAutuadoEstabelecimento;
    }

    private void addCamposEndereco(AutoPenalidade proxy) {
        containerNumeroEnderecoPessoa = new WebMarkupContainer("containerNumeroEnderecoPessoa");


        containerAutuado.add(autoCompleteConsultaVigilanciaEndereco = new AutoCompleteConsultaVigilanciaEndereco(path(proxy.getVigilanciaEndereco()), true));
        autoCompleteConsultaVigilanciaEndereco.setLabel(new Model(bundle("endereco")));

        containerNumeroEnderecoPessoa.add(txtNumeroEndereco = new LongField(path(proxy.getVigilanciaPessoa().getNumeroLogradouro())));
        containerNumeroEnderecoPessoa.setOutputMarkupPlaceholderTag(true);
        txtNumeroEndereco.addAjaxUpdateValue();
        txtNumeroEndereco.setConvertZeroToNull(true);
        txtNumeroEndereco.setVMin(0L);
        txtNumeroEndereco.setASep("");
        containerAutuado.add(containerNumeroEnderecoPessoa);
    }

    private void addContainerResponsavel(Form<AutoPenalidade> form, AutoPenalidade proxy) {
        form.add(containerResponsavel = new WebMarkupContainer("containerResponsavel"));
        containerResponsavel.setOutputMarkupId(true);
        containerResponsavel.add(txtNomeResponsavel = new InputField(path(proxy.getNomeResponsavel())));
        txtNomeResponsavel.setOutputMarkupId(true);
        containerResponsavel.add(chkRecusouAssinar = new CheckBoxLongValue(path(proxy.getRecusouAssinar())));
        chkRecusouAssinar.add(new AjaxFormComponentUpdatingBehavior("onblur") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (getForm().getModelObject().getRecusouAssinar().equals(RepositoryComponentDefault.SIM_LONG)) {
                    txtTestemunha.setEnabled(true);
                    txtTestemunha.setRequired(true);
                    txtTestemunha.addRequiredClass();
                } else {
                    txtTestemunha.setEnabled(false);
                    txtTestemunha.setRequired(false);
                    txtTestemunha.removeRequiredClass();
                }
                target.add(txtTestemunha);
            }
        });
        chkRecusouAssinar.addAjaxUpdateValue();
        containerResponsavel.add(txtTestemunha = new InputField(path(proxy.getTestemunha())));
        if (RepositoryComponentDefault.SIM_LONG.equals(getForm().getModelObject().getRecusouAssinar())) {
            txtTestemunha.setEnabled(true);
        } else {
            txtTestemunha.setEnabled(false);
        }
    }

    private void addContainerFiscal(Form<AutoPenalidade> form) {
        AutoPenalidadeFiscal proxyFiscal = on(AutoPenalidadeFiscal.class);
        form.add(containerFiscal = new WebMarkupContainer("containerFiscal", modelFiscal = new CompoundPropertyModel<AutoPenalidadeFiscal>(new AutoPenalidadeFiscal())));
        containerFiscal.setOutputMarkupId(true);
        containerFiscal.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional(path(proxyFiscal.getProfissional())));
        autoCompleteConsultaProfissional.addAjaxUpdateValue();
        containerFiscal.add(new AbstractAjaxButton("btnAdicionarFiscal") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarFiscal(target);
            }
        }.setDefaultFormProcessing(false));
        containerFiscal.add(tblFiscal = new Table("tblFiscal", getColumnsFiscal(), getCollectionProviderFiscal()));
        tblFiscal.populate();
    }

    private void addContainerDadosInfracao(Form<AutoPenalidade> form) {
        AutoPenalidadeInfracao proxyInfracao = on(AutoPenalidadeInfracao.class);
        form.add(containerDadosInfracao = new WebMarkupContainer("containerDadosInfracao", modelInfracao = new CompoundPropertyModel<AutoPenalidadeInfracao>(new AutoPenalidadeInfracao())));
        containerDadosInfracao.setOutputMarkupId(true);
        containerDadosInfracao.add(txaInfracao = new InputArea(path(proxyInfracao.getInfracao())));
        txaInfracao.addAjaxUpdateValue();
        containerDadosInfracao.add(txaObservacaoInfracao = new InputArea(path(proxyInfracao.getObservacao())));
        txaObservacaoInfracao.addAjaxUpdateValue();
        containerDadosInfracao.add(txaLegislacao = new InputArea(path(proxyInfracao.getLegislacao())));
        txaLegislacao.addAjaxUpdateValue();
        containerDadosInfracao.add(new AbstractAjaxButton("btnAdicionarInfracao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarInfracao(target);
            }
        }.setDefaultFormProcessing(false));
        containerDadosInfracao.add(tblInfracoes = new Table("tblInfracoes", getColumnsInfracao(), getCollectionProviderInfracao()));
        tblInfracoes.populate();
    }

    private void addContainerDadosPenalidade(Form<AutoPenalidade> form) {
        AutoPenalidadeItem proxyPenalidadeItem = on(AutoPenalidadeItem.class);
        form.add(containerDadosPenalidade = new WebMarkupContainer("containerDadosPenalidade", modelPenalidade = new CompoundPropertyModel<AutoPenalidadeItem>(new AutoPenalidadeItem())));
        containerDadosPenalidade.setOutputMarkupId(true);
        containerDadosPenalidade.add(autoCompleteConsultaTipoPenalidade = new AutoCompleteConsultaTipoPenalidade(path(proxyPenalidadeItem.getTipoPenalidade())));
        autoCompleteConsultaTipoPenalidade.addAjaxUpdateValue();
        containerDadosPenalidade.add(txaObservacaoPenalidade = new InputArea(path(proxyPenalidadeItem.getObservacao())));
        containerDadosPenalidade.add(doubleValorMulta = new DoubleField(path(proxyPenalidadeItem.getValorMulta())));
        doubleValorMulta.setLabel(Model.of(bundle("valorMulta")));
        doubleValorMulta.addAjaxUpdateValue();
        txaObservacaoPenalidade.addAjaxUpdateValue();
        containerDadosPenalidade.add(new AbstractAjaxButton("btnAdicionarPenalidade") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarPenalidades(target);
            }
        }.setDefaultFormProcessing(false));
        containerDadosPenalidade.add(tblPenalidades = new Table("tblPenalidades", getColumnsTipoPenalidade(), getCollectionProviderPenalidade()));
        tblPenalidades.populate();

        if (getForm().getModel().getObject().getCodigo() != null) {
            containerDadosPenalidade.setEnabled(VigilanciaHelper.getVigilanciaFinanceiro(getForm().getModel().getObject()) == null);
        }
    }

    private void addContainerEnvio(Form<AutoPenalidade> form, AutoPenalidade proxy) {
        containerEnvio = new WebMarkupContainer("containerEnvio");
        containerEnvio.setOutputMarkupId(true);
        containerEnvio.add(dchRecebimento = new DateChooser(path(proxy.getDataRecebimento())));
        containerEnvio.add(new HoraMinutoField("hora", new PropertyModel<Date>(this, "hora")));
        if (getForm().getModelObject().getDataRecebimento() != null) {
            hora = getForm().getModelObject().getDataRecebimento();
        }
        dchRecebimento.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        dchRecebimento.addAjaxUpdateValue();
        containerEnvio.add(DropDownUtil.getEnviadosDropDown(path(proxy.getEnviado()), true, false));

        containerEnvio.add(autoCompleteConsultaMotivoRetorno = new AutoCompleteConsultaMotivoRetorno(path(proxy.getMotivoRetorno())));
        autoCompleteConsultaMotivoRetorno.addAjaxUpdateValue();
        containerEnvio.add(btnCadastrarMotivo = new AbstractAjaxLink("btnCadastrarMotivo") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                showDlgCadastrarMotivoRetorno(target);
            }
        });

        form.add(containerEnvio);
    }

    private void showDlgCadastrarMotivoRetorno(AjaxRequestTarget target) {
        if (dlgCadastroMotivoRetorno == null) {
            addModal(target, dlgCadastroMotivoRetorno = new DlgCadastroMotivoRetorno(newModalId()) {
                @Override
                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    target.add(autoCompleteConsultaMotivoRetorno);
                    autoCompleteConsultaMotivoRetorno.focus(target);
                }

                @Override
                public void onExcluir(AjaxRequestTarget target, MotivoRetorno motivoRetorno) throws ValidacaoException, DAOException {
                    if (getForm().getModelObject().getMotivoRetorno() != null && motivoRetorno.equals(getForm().getModelObject().getMotivoRetorno())) {
                        autoCompleteConsultaMotivoRetorno.limpar(target);
                    }
                }
            });
        }
        dlgCadastroMotivoRetorno.show(target);
    }

    private void enableCamposAutuado(AjaxRequestTarget target, boolean limparCampos) {
        if (AutoIntimacao.TipoDenunciado.ESTABELECIMENTO.value().equals(getForm().getModelObject().getTipoAutuado())) {
            getForm().getModelObject().setVigilanciaPessoa(null);
            containerAutuadoEstabelecimento.setVisible(true);
            containerAutuadoPessoa.setVisible(false);
        } else {
            getForm().getModelObject().setEstabelecimentoAutuado(null);
            containerAutuadoEstabelecimento.setVisible(false);
            containerAutuadoPessoa.setVisible(true);
        }

        if (target != null) {
            if (AutoPenalidade.TipoAutuado.PESSOA.value().equals(getForm().getModelObject().getTipoAutuado())) {
                containerNumeroEnderecoPessoa.setVisible(true);
            } else {
                containerNumeroEnderecoPessoa.setVisible(true);
            }
            if (limparCampos) {
                autoCompleteConsultaEstabelecimento.limpar(target);
                autoCompleteConsultaVigilanciaPessoa.limpar(target);
                autoCompleteConsultaVigilanciaEndereco.limpar(target);
            }
            target.add(containerAutuado);
            target.appendJavaScript(JScript.initMasks());
        }
    }

    private void adicionarFiscal(AjaxRequestTarget target) throws ValidacaoException {
        AutoPenalidadeFiscal autoPenalidadeFiscal = modelFiscal.getObject();
        if (autoPenalidadeFiscal.getProfissional() == null) {
            throw new ValidacaoException(bundle("obrigatorioSelecionarFiscal"));
        }
        boolean profissionalJaInserido = Lambda.exists(lstFiscal, Lambda.having(on(AutoPenalidadeFiscal.class).getProfissional().getCodigo(), Matchers.equalTo(autoPenalidadeFiscal.getProfissional().getCodigo())));
        if (profissionalJaInserido) {
            throw new ValidacaoException(bundle("fiscalJaInserido"));
        }
        lstFiscal.add(autoPenalidadeFiscal);

        tblFiscal.populate();
        tblFiscal.update(target);
        modelFiscal.setObject(new AutoPenalidadeFiscal());
        autoCompleteConsultaProfissional.limpar(target);
    }

    private void adicionarPenalidades(AjaxRequestTarget target) throws ValidacaoException {
        AutoPenalidadeItem autoPenalidadeItem = modelPenalidade.getObject();
        if (autoPenalidadeItem.getTipoPenalidade() == null) {
            throw new ValidacaoException(bundle("obrigatorioSelecionarPenalidade"));
        }

        autoPenalidadeItem.setObservacao(StringUtil.removeBarraString(autoPenalidadeItem.getObservacao()));

        if (idxPenalidade != null) {
            lstPenalidades.set(idxPenalidade, autoPenalidadeItem);
            idxPenalidade = null;
            autoCompleteConsultaTipoPenalidade.setEnabled(true);
        } else {
            AutoPenalidadeItem proxy = on(AutoPenalidadeItem.class);
            boolean penalidadeJaInserida = Lambda.exists(lstPenalidades, Lambda.having(proxy.getTipoPenalidade().getCodigo(), Matchers.equalTo(autoPenalidadeItem.getTipoPenalidade().getCodigo())));
            if (penalidadeJaInserida) {
                throw new ValidacaoException(bundle("penalidadeJaAdicionada"));
            }
            lstPenalidades.add(autoPenalidadeItem);
        }

        limparDadosPenalidade(target);
    }

    private void adicionarInfracao(AjaxRequestTarget target) throws ValidacaoException {
        AutoPenalidadeInfracao autoPenalidadeinfracao = modelInfracao.getObject();
        if (autoPenalidadeinfracao.getInfracao() == null) {
            throw new ValidacaoException(bundle("msgInformeEspecificacaoDetalhadaAtoFatoConstitutivoInfracao"));
        }

        autoPenalidadeinfracao.setInfracao(StringUtil.removeBarraString(autoPenalidadeinfracao.getInfracao()));
        autoPenalidadeinfracao.setObservacao(StringUtil.removeBarraString(autoPenalidadeinfracao.getObservacao()));
        autoPenalidadeinfracao.setLegislacao(StringUtil.removeBarraString(autoPenalidadeinfracao.getLegislacao()));

        if (idxInfracao != null) {
            lstInfracoes.set(idxInfracao, autoPenalidadeinfracao);
            idxInfracao = null;
        } else {
            lstInfracoes.add(autoPenalidadeinfracao);
        }

        limparDadosInfracao(target);
    }

    private void limparDadosPenalidade(AjaxRequestTarget target) {
        modelPenalidade.setObject(new AutoPenalidadeItem());
        tblPenalidades.update(target);
        autoCompleteConsultaTipoPenalidade.limpar(target);
        txaObservacaoPenalidade.limpar(target);
        doubleValorMulta.limpar(target);
        target.add(containerDadosPenalidade);
    }

    private void limparDadosInfracao(AjaxRequestTarget target) {
        modelInfracao.setObject(new AutoPenalidadeInfracao());
        tblInfracoes.update(target);
        txaObservacaoInfracao.limpar(target);
        txaLegislacao.limpar(target);
        txaInfracao.limpar(target);
    }

    private void adicionarFromEstabelecimento(AjaxRequestTarget target, String nomeResponsavel) {
        getForm().getModelObject().setNomeResponsavel(nomeResponsavel);
        txtNomeResponsavel.setComponentValue(nomeResponsavel);
        target.add(txtNomeResponsavel);
    }

    private ICollectionProvider getCollectionProviderFiscal() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (CollectionUtils.isEmpty(lstFiscal)) {
                    carregarFiscais();
                }
                return lstFiscal;
            }
        };
    }

    private ICollectionProvider getCollectionProviderPenalidade() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (CollectionUtils.isEmpty(lstPenalidades)) {
                    carregarPenalidades();
                }
                return lstPenalidades;
            }
        };
    }

    private ICollectionProvider getCollectionProviderInfracao() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (CollectionUtils.isEmpty(lstInfracoes)) {
                    carregarInfracoes();
                }
                return lstInfracoes;
            }
        };
    }

    private List<IColumn> getColumnsFiscal() {
        List<IColumn> columns = new ArrayList<>();
        AutoPenalidadeFiscal proxy = on(AutoPenalidadeFiscal.class);

        columns.add(getActionColumnFiscal());
        columns.add(createColumn(bundle("nome"), proxy.getProfissional().getNome()));

        return columns;
    }

    private List<IColumn> getColumnsTipoPenalidade() {
        List<IColumn> columns = new ArrayList<>();
        AutoPenalidadeItem proxy = on(AutoPenalidadeItem.class);

        if (!isViewOnly()) {
            columns.add(getActionColumnPenalidade());
        }
        columns.add(createColumn(bundle("tipoPenalidade"), proxy.getTipoPenalidade().getDescricao()));
        columns.add(createColumn(bundle("legislacao"), proxy.getTipoPenalidade().getLegislacao()));
        columns.add(new DoubleColumn(bundle("valorMulta"), path(proxy.getValorMulta())).setCasasDecimais(2));
        columns.add(createColumn(bundle("observacao"), proxy.getObservacao()));

        return columns;
    }

    private List<IColumn> getColumnsInfracao() {
        List<IColumn> columns = new ArrayList<>();
        AutoPenalidadeInfracao proxy = on(AutoPenalidadeInfracao.class);

        if (!isViewOnly()) {
            columns.add(getActionColumnInfracao());
        }
        columns.add(createColumn(bundle("atoFatoInfracao"), proxy.getInfracao()));
        columns.add(createColumn(bundle("observacao"), proxy.getObservacao()));
        columns.add(createColumn(bundle("legislacao"), proxy.getLegislacao()));

        return columns;
    }

    private IColumn getActionColumnFiscal() {
        return new MultipleActionCustomColumn<AutoPenalidadeFiscal>() {
            @Override
            public void customizeColumn(final AutoPenalidadeFiscal rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<AutoPenalidadeFiscal>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoPenalidadeFiscal modelObject) throws ValidacaoException, DAOException {
                        for (int i = 0; i < lstFiscal.size(); i++) {
                            AutoPenalidadeFiscal item = lstFiscal.get(i);
                            if (item == rowObject) {
                                lstFiscal.remove(i);
                            }
                        }
                        tblFiscal.populate();
                        tblFiscal.update(target);
                    }
                });
            }
        };
    }

    private IColumn getActionColumnPenalidade() {
        return new MultipleActionCustomColumn<AutoPenalidadeItem>() {
            @Override
            public void customizeColumn(final AutoPenalidadeItem rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<AutoPenalidadeItem>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoPenalidadeItem modelObject) throws ValidacaoException, DAOException {
                        for (int i = 0; i < lstPenalidades.size(); i++) {
                            AutoPenalidadeItem item = lstPenalidades.get(i);
                            if (item == modelObject) {
                                lstPenalidades.remove(i);
                            }
                        }
                        tblPenalidades.populate();
                        tblPenalidades.update(target);
                    }
                });
                addAction(ActionType.EDITAR, rowObject, new IModelAction<AutoPenalidadeItem>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoPenalidadeItem modelObject) throws ValidacaoException, DAOException {
                        editPenalidade(target, modelObject);
                    }
                });
            }
        };
    }

    private void editPenalidade(AjaxRequestTarget target, AutoPenalidadeItem penalidade) {
        modelPenalidade.setObject(new AutoPenalidadeItem());
        ComponentUtils.limparContainer(containerDadosPenalidade, target);
        for (int i = 0; i < lstPenalidades.size(); i++) {
            if (penalidade == lstPenalidades.get(i)) {
                idxPenalidade = i;
                modelPenalidade.setObject(lstPenalidades.get(i));
                break;
            }
        }
        autoCompleteConsultaTipoPenalidade.setEnabled(false);
        ComponentUtils.updateComponentsContainer(containerDadosPenalidade, target);
    }

    private IColumn getActionColumnInfracao() {
        return new MultipleActionCustomColumn<AutoPenalidadeInfracao>() {
            @Override
            public void customizeColumn(final AutoPenalidadeInfracao rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<AutoPenalidadeInfracao>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoPenalidadeInfracao modelObject) throws ValidacaoException, DAOException {
                        CrudUtils.removerItem(target, tblInfracoes, lstInfracoes, modelObject);
                    }

                });
                addAction(ActionType.EDITAR, rowObject, new IModelAction<AutoPenalidadeInfracao>() {
                    @Override
                    public void action(AjaxRequestTarget target, AutoPenalidadeInfracao modelObject) throws ValidacaoException, DAOException {
                        editInfracao(target, modelObject);
                    }
                });
            }
        };
    }

    private void editInfracao(AjaxRequestTarget target, AutoPenalidadeInfracao infracao) {
        modelInfracao.setObject(new AutoPenalidadeInfracao());
        ComponentUtils.limparContainer(containerDadosInfracao, target);
        for (int i = 0; i < lstInfracoes.size(); i++) {
            if (infracao == lstInfracoes.get(i)) {
                idxInfracao = i;
                modelInfracao.setObject(lstInfracoes.get(i));
                break;
            }
        }
        ComponentUtils.updateComponentsContainer(containerDadosInfracao, target);
    }

    private void carregarFiscais() {
        if (getForm().getModelObject().getCodigo() != null) {
            Long codigoAutoPenalidade = getForm().getModelObject().getCodigo();
            lstFiscal = LoadManager.getInstance(AutoPenalidadeFiscal.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AutoPenalidadeFiscal.PROP_AUTO_PENALIDADE, AutoPenalidade.PROP_CODIGO), codigoAutoPenalidade))
                    .addProperties(new HQLProperties(AutoPenalidadeFiscal.class).getProperties())
                    .addProperties(new HQLProperties(AutoPenalidade.class, AutoPenalidadeFiscal.PROP_AUTO_PENALIDADE).getProperties())
                    .addProperties(new HQLProperties(Profissional.class, AutoPenalidadeFiscal.PROP_PROFISSIONAL).getProperties())
                    .start().getList();
        }
    }

    private void carregarPenalidades() {
        if (getForm().getModelObject().getCodigo() != null) {
            Long codigoAutoPenalidade = getForm().getModelObject().getCodigo();
            lstPenalidades = LoadManager.getInstance(AutoPenalidadeItem.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AutoPenalidadeItem.PROP_AUTO_PENALIDADE, AutoPenalidade.PROP_CODIGO), codigoAutoPenalidade))
                    .addProperties(new HQLProperties(AutoPenalidadeItem.class).getProperties())
                    .addProperties(new HQLProperties(AutoPenalidade.class, AutoPenalidadeItem.PROP_AUTO_PENALIDADE).getProperties())
                    .addProperties(new HQLProperties(TipoPenalidade.class, AutoPenalidadeItem.PROP_TIPO_PENALIDADE).getProperties())
                    .start().getList();
        }
    }


    private void carregarInfracoes() {
        if (getForm().getModelObject().getCodigo() != null) {
            Long codigoAutoPenalidade = getForm().getModelObject().getCodigo();
            lstInfracoes = LoadManager.getInstance(AutoPenalidadeInfracao.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AutoPenalidadeInfracao.PROP_AUTO_PENALIDADE, AutoPenalidade.PROP_CODIGO), codigoAutoPenalidade))
                    .addProperties(new HQLProperties(AutoPenalidadeInfracao.class).getProperties())
                    .addProperties(new HQLProperties(AutoPenalidade.class, AutoPenalidadeItem.PROP_AUTO_PENALIDADE).getProperties())
                    .start().getList();
        }
    }

    @Deprecated
    public void instanceFromRequerimento(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia) {
        if (true)
            throw new NotImplementedException();

        AutoPenalidade autoPenalidade = new AutoPenalidade();
        autoPenalidade.setEstabelecimentoAutuado(requerimentoVigilancia.getEstabelecimento());
        autoPenalidade.setTipoAutuado(AutoPenalidade.TipoAutuado.ESTABELECIMENTO.value());
        autoPenalidade.setAutuado(requerimentoVigilancia.getNome());
        autoPenalidade.setRequerimentoVigilancia(requerimentoVigilancia);

        getForm().getModel().setObject(autoPenalidade);
        containerAutuado.setEnabled(false);

        target.add(containerDadosGeral);
        target.add(containerAutuado);
        target.add(containerEnquadramento);
        tblInfracoes.populate(target);

        configForm();
    }

    public void instanceFromProcessoAdministrativo(AjaxRequestTarget target, ProcessoAdministrativo processoAdministrativo) {
        if (processoAdministrativo != null && processoAdministrativo.getAutoInfracao() != null) {
            processoAdministrativo = LoadManager.getInstance(ProcessoAdministrativo.class)
                    .addProperties(new HQLProperties(ProcessoAdministrativo.class).getProperties())
                    .addProperties(new HQLProperties(AutoInfracao.class, ProcessoAdministrativo.PROP_AUTO_INFRACAO).getProperties())
                    .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(ProcessoAdministrativo.PROP_AUTO_INFRACAO, AutoInfracao.PROP_VIGILANCIA_ENDERECO)).getProperties())
                    .addProperties(new HQLProperties(Cidade.class, VOUtils.montarPath(ProcessoAdministrativo.PROP_AUTO_INFRACAO, AutoInfracao.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE)).getProperties())
                    .addProperties(new HQLProperties(Estado.class, VOUtils.montarPath(ProcessoAdministrativo.PROP_AUTO_INFRACAO, AutoInfracao.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE, Cidade.PROP_ESTADO)).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, ProcessoAdministrativo.PROP_ESTABELECIMENTO).getProperties())
                    .addProperties(new HQLProperties(VigilanciaPessoa.class, ProcessoAdministrativo.PROP_VIGILANCIA_PESSOA).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(ProcessoAdministrativo.PROP_CODIGO, processoAdministrativo.getCodigo()))
                    .start().getVO();
            List<AutoIntimacao> autoIntimacaoSubsistenteList = LoadManager.getInstance(AutoIntimacao.class)
                    .addProperties(new HQLProperties(AutoIntimacao.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(AutoIntimacao.PROP_AUTO_INFRACAO, processoAdministrativo.getAutoInfracao()))
                    .addSorter(new QueryCustom.QueryCustomSorter(AutoIntimacao.PROP_DATA_CADASTRO, "desc"))
                    .start().getList();
            AutoIntimacao autoIntimacaoSubsistente = null;
            if (CollectionUtils.isNotNullEmpty(autoIntimacaoSubsistenteList)) {
                autoIntimacaoSubsistente = autoIntimacaoSubsistenteList.get(0);
            }
            AutoPenalidade autoPenalidade = new AutoPenalidade();
            autoPenalidade.setAutoInfracao(processoAdministrativo.getAutoInfracao());
            autoPenalidade.setRequerimentoVigilancia(processoAdministrativo.getAutoInfracao().getRequerimentoVigilancia());
            autoPenalidade.setProcessoAdministrativo(processoAdministrativo);
            autoPenalidade.setAutoIntimacao(autoIntimacaoSubsistente);
            autoPenalidade.setRegistroInspecao(processoAdministrativo.getAutoInfracao().getRegistroInspecao());
            autoPenalidade.setDenuncia(processoAdministrativo.getAutoInfracao().getDenuncia());

            autoPenalidade.setEstabelecimentoAutuado(processoAdministrativo.getEstabelecimento());
            autoPenalidade.setTipoAutuado(processoAdministrativo.getTipoAutuado());
            autoPenalidade.setVigilanciaPessoa(processoAdministrativo.getVigilanciaPessoa());
            autoPenalidade.setAutuado(processoAdministrativo.getAutuado());
            autoPenalidade.setEnquadramentoLegal(getEnquadramentoLegalProcessoAdm(processoAdministrativo));
            autoPenalidade.setVigilanciaEndereco(getVigilanciaEnderecoProcessoAdm(processoAdministrativo));

            instanciarInfracoes(processoAdministrativo.getAutoInfracao());

            getForm().getModel().setObject(autoPenalidade);
            containerAutuado.setEnabled(false);

            target.add(containerDadosGeral);
            target.add(containerAutuado);
            target.add(containerEnquadramento);
            tblInfracoes.populate(target);

            configForm();
        }
    }

    private VigilanciaEndereco getVigilanciaEnderecoProcessoAdm(ProcessoAdministrativo processoAdministrativo) {
        if (processoAdministrativo.getAutoInfracao() != null) {
            return processoAdministrativo.getAutoInfracao().getVigilanciaEndereco();
        }
        return null;
    }

    private String getEnquadramentoLegalProcessoAdm(ProcessoAdministrativo processoAdministrativo) {
        if (processoAdministrativo.getAutoInfracao() != null) {
            return processoAdministrativo.getAutoInfracao().getEnquadramentoLegal();
        }
        return null;
    }

    private void instanciarInfracoes(AutoInfracao autoInfracao) {
        lstInfracoes = new ArrayList<>();
        List<AutoInfracaoProvidencia> autoInfracaoProvidenciaList = LoadManager.getInstance(AutoInfracaoProvidencia.class)
                .addProperties(new HQLProperties(AutoInfracaoProvidencia.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(AutoInfracaoProvidencia.PROP_AUTO_INFRACAO, autoInfracao))
                .addSorter(new QueryCustom.QueryCustomSorter(AutoInfracaoProvidencia.PROP_DATA_CADASTRO, "asc"))
                .start().getList();
        if (CollectionUtils.isNotNullEmpty(autoInfracaoProvidenciaList)) {
            for (AutoInfracaoProvidencia autoInfracaoProvidencia : autoInfracaoProvidenciaList) {
                AutoPenalidadeInfracao autoPenalidadeInfracao = new AutoPenalidadeInfracao();
                autoPenalidadeInfracao.setInfracao(autoInfracaoProvidencia.getInfracao());
                autoPenalidadeInfracao.setObservacao(autoInfracaoProvidencia.getObservacao());
                autoPenalidadeInfracao.setLegislacao(autoInfracaoProvidencia.getLegislacao());
                autoPenalidadeInfracao.setAutoInfracaoProvidencia(autoInfracaoProvidencia);
                lstInfracoes.add(autoPenalidadeInfracao);
            }
        }
    }

    public void salvarAction(AjaxRequestTarget target, AutoPenalidade object) throws DAOException, ValidacaoException {
        if (CollectionUtils.isAllEmpty(lstFiscal)) {
            throw new ValidacaoException(bundle("msgObrigatorioAddAutoridadeSaude"));
        }
        if (CollectionUtils.isAllEmpty(lstPenalidades)) {
            throw new ValidacaoException(bundle("msgObrigatorioAoMenosUmaPenalidade"));
        }
        if (CollectionUtils.isAllEmpty(lstInfracoes)) {
            throw new ValidacaoException(bundle("msgObrigatorioAoMenosUmaInfracao"));
        }
        if (hora == null) {
            hora = DataUtil.getDataAtual();
        }
        if (object.getDataRecebimento() != null && hora != null) {
            object.setDataRecebimento(Data.juntarDataHora(object.getDataRecebimento(), hora));
        }

        if (!AutoPenalidade.Situacao.CONCLUIDO.value().equals(object.getSituacao())) {
            if (ConfiguracaoVigilanciaEnum.TipoDatabaseCalculoRecurso.RECEBIMENTO.value().equals(configuracaoVigilancia.getTipoDatabaseCalculoPrazoRecurso())) {
                if (object.getDataRecebimento() == null) {
                    object.setSituacao(AutoPenalidade.Situacao.AGUARDANDO_RECEBIMENTO.value());
                } else {
                    object.setSituacao(AutoPenalidade.Situacao.AGUARDANDO_RECURSO.value());
                }
            } else {
                object.setSituacao(AutoPenalidade.Situacao.AGUARDANDO_RECURSO.value());
            }
        }
        AutoPenalidade autoSalvo = (AutoPenalidade) BOFactoryWicket.getBO(VigilanciaFacade.class).salvarAutoPenalidade(object, lstFiscal, lstPenalidades, lstInfracoes);
        impressaoAction(target, autoSalvo);
    }

    private void impressaoAction(AjaxRequestTarget target, AutoPenalidade autoPenalidade) {
        StringBuilder mensagemImpressao = new StringBuilder(bundle("autoPenalidadeNumeroXSalvoSucesso", autoPenalidade.getNumeroFormatado()));
        mensagemImpressao.append("\n");
        mensagemImpressao.append(bundle("msgImprimirAutoPenalidade"));
        addModal(target, dlgConfirmacaoImpressao = new DlgImpressaoObject<AutoPenalidade>(newModalId(), mensagemImpressao.toString()) {
            @Override
            public DataReport getDataReport(AutoPenalidade object) throws ReportException {
                return BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoComprovanteAutoPenalidade(object.getCodigo(), object.getNumeroFormatado());
            }

            @Override
            public void onFechar(AjaxRequestTarget target, AutoPenalidade object) throws ValidacaoException, DAOException {
                returnPage(null);
            }
        });
        dlgConfirmacaoImpressao.show(target, autoPenalidade);
    }

    @Override
    public Class<AutoPenalidade> getReferenceClass() {
        return AutoPenalidade.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaAutoPenalidadePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("cadastroAutoPenalidade");
    }
}
