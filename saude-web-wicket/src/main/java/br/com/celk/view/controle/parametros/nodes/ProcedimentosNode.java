package br.com.celk.view.controle.parametros.nodes;

import br.com.celk.resources.Icon32;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.controle.parametros.NodesParametrosRef;
import br.com.celk.view.controle.parametros.nodes.annotations.ParametrosNode;
import br.com.celk.view.controle.parametros.nodes.base.ParametrosNodeImp;
import br.com.celk.view.controle.parametros.panel.procedimentos.ProcedimentosPanel;
import br.com.celk.view.controle.parametros.panel.template.ParametrosCadastroPanel;

/**
 *
 * <AUTHOR>
 */
@ParametrosNode(NodesParametrosRef.PROCEDIMENTOS)
public class ProcedimentosNode extends ParametrosNodeImp {

    private ProcedimentosPanel procedimentosPanel;

    @Override
    public ParametrosCadastroPanel getPanel(String id) {
        if (procedimentosPanel == null) {
            procedimentosPanel = new ProcedimentosPanel(id);
        }
        return procedimentosPanel;
    }
    
    public ProcedimentosPanel getProcedimentosPanel(){
        return procedimentosPanel;
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("procedimentos");
    }

    @Override
    public Icon32 getIcone() {
        return Icon32.REPORT;
    }

}
