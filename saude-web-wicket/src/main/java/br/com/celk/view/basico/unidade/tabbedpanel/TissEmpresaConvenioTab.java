package br.com.celk.view.basico.unidade.tabbedpanel;

import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import br.com.celk.view.hospital.convenio.autocomplete.AutoCompleteConsultaConvenio;
import br.com.ksisolucoes.bo.basico.dto.CadastroEmpresaDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.hospital.tiss.EloTissEmpresaConvenio;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public class TissEmpresaConvenioTab extends TabPanel<CadastroEmpresaDTO> {

    private InputField txtCodigoContratadoExecutanteOrigem;
    private InputField txtLogin;
    private InputField txtSenha;
    private AutoCompleteConsultaConvenio autoCompleteConsultaConvenio;
    private Table<EloTissEmpresaConvenio> table;
    private AbstractAjaxButton btnLimpar;
    private EloTissEmpresaConvenio elo = new EloTissEmpresaConvenio();

    public TissEmpresaConvenioTab(String id, CadastroEmpresaDTO cadastroEmpresaDTO) {
        super(id, cadastroEmpresaDTO);
        init();
    }

    public void init() {
        add(txtCodigoContratadoExecutanteOrigem = new InputField("codigoContratadoExecutanteOrigem", new PropertyModel(this, "elo.codigoContratadoExecutanteOrigem")));
        add(txtLogin = new InputField("login", new PropertyModel(this, "elo.login")));
        add(txtSenha = new InputField("senha", new PropertyModel(this, "elo.senha")));
        add(autoCompleteConsultaConvenio = new AutoCompleteConsultaConvenio("convenio", new PropertyModel<Convenio>(this, "elo.convenio")));
        add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
                target.focusComponent(autoCompleteConsultaConvenio);
            }
        });

        add(btnLimpar = new AbstractAjaxButton("btnLimpar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                limparElo(target);
            }
        });

        add(table = new Table("tblElos", getColumns(), getCollectionProvider()));
        table.populate();
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtCodigoContratadoExecutanteOrigem;
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        validarAdicionar();

        boolean adicionar = true;
        for (EloTissEmpresaConvenio eloItem : object.getElosTiss()) {
            if (eloItem.getConvenio().equals(elo.getConvenio())) {
                if (eloItem != elo) {
                    throw new ValidacaoException(bundle("msgJaExisteContratadoEsteCovenio"));
                } else {
                    adicionar = false;
                    break;
                }
            }
        }

        if (adicionar) {
            object.getElosTiss().add(elo);
        }

        table.update(target);
        limparElo(target);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return object.getElosTiss();
            }
        };
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();
        EloTissEmpresaConvenio proxy = on(EloTissEmpresaConvenio.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("codigoContratadoExecutanteOrigem"), proxy.getCodigoContratadoExecutanteOrigem()));
        columns.add(createColumn(bundle("convenio"), proxy.getConvenio().getDescricao()));
        columns.add(createColumn(bundle("login"), proxy.getLogin()));
        columns.add(createColumn(bundle("senha"), proxy.getSenha()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<EloTissEmpresaConvenio>() {
            @Override
            public void customizeColumn(final EloTissEmpresaConvenio rowObject) {
                addAction(ActionType.EDITAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        limparElo(target);
                        atualizaForm(rowObject, target);
                    }
                });
                addAction(ActionType.REMOVER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerVinculo(target, rowObject);
                    }
                });
            }
        };
    }

    private void removerVinculo(AjaxRequestTarget target, EloTissEmpresaConvenio elo) {
        int i;
        for (i = 0; i < object.getElosTiss().size(); i++) {
            if (object.getElosTiss().get(i) == elo) {
                object.getElosTiss().remove(i);
                break;
            }
        }

        table.update(target);
    }

    private void atualizaForm(EloTissEmpresaConvenio elo, AjaxRequestTarget target) {
        this.elo = elo;

        target.add(txtCodigoContratadoExecutanteOrigem);
        target.add(txtLogin);
        target.add(txtSenha);
        target.add(autoCompleteConsultaConvenio);
    }

    @Override
    public String getTitle() {
        return bundle("tiss");
    }

    private void validarAdicionar() throws ValidacaoException {
        if (elo.getCodigoContratadoExecutanteOrigem() == null) {
            throw new ValidacaoException(bundle("msgInformeCodigoContratadoExecutanteOrigem"));
        }

        if (elo.getConvenio() == null) {
            throw new ValidacaoException(bundle("informeConvenio"));
        }
    }

    private void limparElo(AjaxRequestTarget target) {
        this.elo = new EloTissEmpresaConvenio();
        autoCompleteConsultaConvenio.limpar(target);
        target.add(txtCodigoContratadoExecutanteOrigem);
        target.add(txtLogin);
        target.add(txtSenha);
    }
}
