package br.com.celk.view.materiais.devolucao.item;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.devolucao.interfaces.dto.DevolucaoMedicamentoItemDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.html.panel.EmptyPanel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgAdicionarDevolucaoItem extends Window{

    private IAdicionarDevolucaoItemPanel adicionarItemPanel;
    
    public DlgAdicionarDevolucaoItem(String id) {
        super(id);
        init();
    }

    private void init() {
        setContent(new EmptyPanel(getContentId()));
        
        setTitle(BundleManager.getString("itemParaDevolver"));
    }

    private void setPanel(DevolucaoMedicamentoItemDTO devolucaoMedicamentoItemDTO){
            SubGrupo subGrupo = LoadManager.getInstance(SubGrupo.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO), devolucaoMedicamentoItemDTO.getDevolucaoMedicamentoItem().getProduto().getSubGrupo().getId().getCodigo()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), devolucaoMedicamentoItemDTO.getDevolucaoMedicamentoItem().getProduto().getSubGrupo().getId().getCodigoGrupoProduto()))
                        .start().getVO();
            if (subGrupo.getFlagControlaGrupoEstoque().equals(RepositoryComponentDefault.SIM)) {
                adicionarItemPanel = new PnlAdicionarDevolucaoItemControlaLote(getContentId()) {

                    @Override
                    public void adicionar(AjaxRequestTarget target, DevolucaoMedicamentoItemDTO itemOrigem, DevolucaoMedicamentoItemDTO itemDestino) throws ValidacaoException, DAOException{
                        DlgAdicionarDevolucaoItem.this.adicionar(target, itemOrigem, itemDestino);
                        close(target);
                    }

                    @Override
                    public void fechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        close(target);
                    }
                };

                setInitialWidth(700);
                setInitialHeight(100);
            } else {
                adicionarItemPanel = new PnlAdicionarDevolucaoItem(getContentId()) {

                    @Override
                    public void adicionar(AjaxRequestTarget target, DevolucaoMedicamentoItemDTO itemOrigem, DevolucaoMedicamentoItemDTO itemDestino) throws ValidacaoException, DAOException{
                        DlgAdicionarDevolucaoItem.this.adicionar(target, itemOrigem, itemDestino);
                        close(target);
                    }

                    @Override
                    public void fechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        close(target);
                    }
                };

                setInitialWidth(700);
                setInitialHeight(100);
            }

            setResizable(false);
            
            setContent((Component)adicionarItemPanel);
    }
    
    public void setObject(AjaxRequestTarget target, DevolucaoMedicamentoItemDTO devolucaoMedicamentoItemDTO){
        setPanel(devolucaoMedicamentoItemDTO);
        adicionarItemPanel.setObject(target, devolucaoMedicamentoItemDTO);
    }

    public abstract void adicionar(AjaxRequestTarget target, DevolucaoMedicamentoItemDTO itemOrigem, DevolucaoMedicamentoItemDTO itemDestino) throws ValidacaoException, DAOException;

    @Override
    public FormComponent getComponentRequestFocus() {
        return adicionarItemPanel.getComponentRequestFocus();
    }
    
    
}
