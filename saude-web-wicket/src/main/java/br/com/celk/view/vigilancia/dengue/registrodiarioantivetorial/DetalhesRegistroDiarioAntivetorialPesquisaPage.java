package br.com.celk.view.vigilancia.dengue.registrodiarioantivetorial;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.duracaofield.HoraMinutoField;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.markup.html.form.Form;
import br.com.ksisolucoes.vo.vigilancia.dengue.RegistroDiarioAntivetorialPesquisa;
import br.com.ksisolucoes.vo.vigilancia.dengue.RegistroDiarioAntivetorialResultadoLaboratorial;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.resource.CssResourceReference;

/**
 *
 * <AUTHOR>
 */
@Private
public class DetalhesRegistroDiarioAntivetorialPesquisaPage extends BasePage {

    private static final String CUSTOM_CSS = "DetalhesRegistroDiarioAntivetorialPesquisaPage.css";
    private RegistroDiarioAntivetorialPesquisa pesquisa;

    public DetalhesRegistroDiarioAntivetorialPesquisaPage(RegistroDiarioAntivetorialPesquisa pesquisa) {
        this.pesquisa = pesquisa;
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(pesquisa));
        RegistroDiarioAntivetorialPesquisa proxy = on(RegistroDiarioAntivetorialPesquisa.class);

        form.add(new DisabledInputField(path(proxy.getRegistroDiarioAntivetorial().getProfissional().getNome())));
        form.add(new DisabledInputField(path(proxy.getRegistroDiarioAntivetorial().getCiclo().getDescricaoPeriodoCiclo())));
        form.add(new DisabledInputField(path(proxy.getRegistroDiarioAntivetorial().getDataAtividade())));

        form.add(new DisabledInputField(path(proxy.getDataCadastro())));
        form.add(new DisabledInputField(path(proxy.getUsuario().getNome())));

        form.add(new DisabledInputField(path(proxy.getEndereco().getEnderecoFormatado())));
        form.add(new DisabledInputField(path(proxy.getNumeroLogradouro())));
        form.add(new DisabledInputField(path(proxy.getSequenciaLogradouro())));

        form.add(new DisabledInputField(path(proxy.getNumeroQuarteirao())));
        form.add(new DisabledInputField(path(proxy.getSequenciaQuarteirao())));
        form.add(new DisabledInputField(path(proxy.getLadoQuarteirao())));
        form.add(new DisabledInputField(path(proxy.getTipoImovel().getDescricao())));
        form.add(new HoraMinutoField(path(proxy.getHoraEntrada())).setEnabled(false));
        form.add(new DisabledInputField(path(proxy.getDescricaoPendencia())));

        // Depósitos Inspecionados
        form.add(new DisabledInputField(path(proxy.getDepositoInspecionadoA1())));
        form.add(new DisabledInputField(path(proxy.getDepositoInspecionadoA2())));
        form.add(new DisabledInputField(path(proxy.getDepositoInspecionadoB())));
        form.add(new DisabledInputField(path(proxy.getDepositoInspecionadoC())));
        form.add(new DisabledInputField(path(proxy.getDepositoInspecionadoD1())));
        form.add(new DisabledInputField(path(proxy.getDepositoInspecionadoD2())));
        form.add(new DisabledInputField(path(proxy.getDepositoInspecionadoE())));

        // Coleta Amostra
        form.add(new DisabledInputField(path(proxy.getDescricaoColetaAmostra())));
        form.add(new DisabledInputField(path(proxy.getNumeroAmostraInicial())));
        form.add(new DisabledInputField(path(proxy.getNumeroAmostraFinal())));
        form.add(new DisabledInputField(path(proxy.getQuantidadeTubitos())));

        // Tratamento
        form.add(new DisabledInputField(path(proxy.getDepositosEliminados())));
        form.add(new DisabledInputField(path(proxy.getDescricaoImovelTratado())));

        // Focal - Coleta Amostra
        form.add(new DisabledInputField(path(proxy.getTipoInseticidaAmostra().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getQuantidadeAmostra())));
        form.add(new DisabledInputField(path(proxy.getQuantidadeDepTratamentoAmostra())));

        // Focal - Larvicida
        form.add(new DisabledInputField(path(proxy.getTipoInseticidaLarvicida().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getQuantidadeLarvicida())));
        form.add(new DisabledInputField(path(proxy.getQuantidadeDepTratamentoLarvicida())));

        // Adulticida
        form.add(new DisabledInputField(path(proxy.getTipoInseticidaAdulticida().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getQuantidadeCargasAdulticida())));

        form.add(new VoltarButton("btnVoltar"));
        form.add(new AbstractAjaxButton("btnResultadoLaboratorio") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new ResultadoPesquisaRegistroDiarioAntivetorialPage(pesquisa, true));
            }

            @Override
            public boolean isVisible() {
                return LoadManager.getInstance(RegistroDiarioAntivetorialResultadoLaboratorial.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(RegistroDiarioAntivetorialResultadoLaboratorial.PROP_REGISTRO_DIARIO_ANTIVETORIAL_PESQUISA, pesquisa))
                        .exists();
            }
        });

        add(form);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("detalhesPesquisaRegistroDiarioAntivetorial");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(CssHeaderItem.forReference(new CssResourceReference(DetalhesRegistroDiarioAntivetorialPesquisaPage.class, CUSTOM_CSS)));
    }
}
