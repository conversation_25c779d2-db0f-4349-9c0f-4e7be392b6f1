package br.com.celk.view.agenda.agendamento.manutencaoagendamento.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.agendamento.exame.dto.CadastroNovaAgendaDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgCadastroNovaAgenda extends Window {
    
    private PnlCadastroNovaAgenda pnlCadastroNovaAgenda;
    
    public DlgCadastroNovaAgenda(String id){
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("remanejamentoAgendamento");
            }
        });
                
        setInitialWidth(600);
        setInitialHeight(440);
        setResizable(true);
        
        setContent(pnlCadastroNovaAgenda = new PnlCadastroNovaAgenda(getContentId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, CadastroNovaAgendaDTO dto) throws ValidacaoException, DAOException {
                DlgCadastroNovaAgenda.this.onConfirmar(target, dto);
//                close(target);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target, CadastroNovaAgendaDTO dto) throws ValidacaoException, DAOException;
    
    public void show(AjaxRequestTarget target, List<AgendaGradeAtendimentoHorario> list){
        show(target);
        pnlCadastroNovaAgenda.setAgendaGradeAtendimentoHorarioList(target, list);
    }    
}