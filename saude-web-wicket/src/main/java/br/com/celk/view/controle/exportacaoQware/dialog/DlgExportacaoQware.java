package br.com.celk.view.controle.exportacaoQware.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 * Created by sulivan on 14/06/17.
 */
public abstract class DlgExportacaoQware extends Window {

    private PnlExportacaoQware pnlExportacaoQWare;

    public DlgExportacaoQware(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(BundleManager.getString("dados"));

        setInitialWidth(500);
        setInitialHeight(200);
        setResizable(true);

        setContent(pnlExportacaoQWare = new PnlExportacaoQware(getContentId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
                DlgExportacaoQware.this.onConfirmar(target);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                fechar(target);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                fechar(target);
                return true;
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    @Override
    public void show(AjaxRequestTarget target) {
        super.show(target);
    }

    public void onFechar(AjaxRequestTarget target) {
    }

    private void fechar(AjaxRequestTarget target) {
        close(target);
        DlgExportacaoQware.this.onFechar(target);
    }
}

