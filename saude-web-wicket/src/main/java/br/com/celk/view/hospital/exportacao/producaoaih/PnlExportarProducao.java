package br.com.celk.view.hospital.exportacao.producaoaih;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.util.Date;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlExportarProducao extends Panel {

    private CompoundPropertyModel<Date> model;
    private DateChooser dchDataCompetencia;
    private AbstractAjaxButton btnConfirmar;
    private AbstractAjaxButton btnFechar;
    private String data;
    private String dataApresentacao;
    private InputField txtCompetencia;
    private InputField txtDataApresentacao;

    public PnlExportarProducao(String id) {
        super(id);
        init();
    }

    private void init() {
        Form<Date> form = new Form<Date>("form", model = new CompoundPropertyModel(this));

        form.add(txtCompetencia = new InputField("data", new PropertyModel(this, "data")));
        form.add(txtDataApresentacao = new InputField("dataApresentacao", new PropertyModel(this, "dataApresentacao")));

        form.add(btnConfirmar = new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validaData(data, Bundle.getStringApplication("msg_infome_competencia"));
                validaData(dataApresentacao, Bundle.getStringApplication("msg_informe_data_apresentacao"));
                onConfirmar(target, data, dataApresentacao);
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        btnFechar.setDefaultFormProcessing(
                false);

        setOutputMarkupId(
                true);

        add(form);
    }

    private void validaData(String data, String msgErroData) throws ValidacaoException {
        if (data == null) {
            throw new ValidacaoException(msgErroData);
        }
        Integer mes = Integer.parseInt(data.substring(0, 2));
        Integer ano = Integer.parseInt(data.substring(3, 7));
        if (!Data.isValidoMes(mes)) {
            throw new ValidacaoException(BundleManager.getString("mesInvalido"));
        }
        if (!Data.isValidoAno(ano)) {
            throw new ValidacaoException(BundleManager.getString("anoInvalido"));
        }
    }

    public void limpar(AjaxRequestTarget target) {
        txtCompetencia.limpar(target);
        txtDataApresentacao.limpar(target);
        target.add(txtCompetencia);
        target.add(txtDataApresentacao);
        target.focusComponent(txtCompetencia);
    }

    private void update(AjaxRequestTarget target) {
        target.add(this);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, String data, String dataApresentacao) throws DAOException, ValidacaoException;

    public abstract void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException;
}
