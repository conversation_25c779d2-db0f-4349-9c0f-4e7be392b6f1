package br.com.celk.view.basico.orgaoemissor;

import br.com.celk.component.inputfield.InputField;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.OrgaoEmissor;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaOrgaoEmissorPage extends ConsultaPage<OrgaoEmissor, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;

    public ConsultaOrgaoEmissorPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("descricao"));
        setExibeExpandir(true);

        getLinkNovo().setVisible(false);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        OrgaoEmissor proxy = on(OrgaoEmissor.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("descricao"), proxy.getDescricao()));
        columns.add(createSortableColumn(bundle("sigla"), proxy.getSigla()));
        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<OrgaoEmissor>() {
            @Override
            public void customizeColumn(final OrgaoEmissor rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<OrgaoEmissor>() {
                    @Override
                    public void action(AjaxRequestTarget target, OrgaoEmissor modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroOrgaoEmissorPage(modelObject));
                    }
                });
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<OrgaoEmissor>() {
                    @Override
                    public void action(AjaxRequestTarget target, OrgaoEmissor modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroOrgaoEmissorPage(modelObject, true));
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return OrgaoEmissor.class;
            }

            @Override
            public String[] getProperties() {
                return new HQLProperties(OrgaoEmissor.class).getProperties();
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(OrgaoEmissor.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(OrgaoEmissor.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.ILIKE, descricao));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroOrgaoEmissorPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaOrgaoEmissor");
    }
}
