package br.com.celk.view.materiais.dispensacao.prescricaoatendimento.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.doublefield.DisabledDoubleField;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.lote.saida.PnlSaidaLote;
import br.com.celk.component.notification.INotificationPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.RemoverActionColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import static br.com.ksisolucoes.system.methods.CoreMethods.*;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.Valor;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresaHelper;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoGrupoEstoqueItemDTO;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import static ch.lambdaj.Lambda.*;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.commons.lang.SerializationUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AbstractDefaultAjaxBehavior;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.CallbackParameter;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.cycle.RequestCycle;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlAdicionarDispensacaoPrescricaoItemControlaLote extends Panel implements IAdicionarDispensacaoPrescricaoItemPanel, INotificationPanel {

    private CompoundPropertyModel<DispensacaoMedicamentoItem> model;
    private InputField txtQuantidadeLote;
    private Form form;
    private AbstractAjaxButton btnFechar;
    private PnlSaidaLote pnlSaidaLote;
    private Table tblLotes;
    private String lote;
    private Double total;
    private List<MovimentoGrupoEstoqueItemDTO> lotes = new ArrayList<MovimentoGrupoEstoqueItemDTO>();
    private DispensacaoMedicamentoItem _dispensacaoMedicamentoItem;
    private DoubleField txtPrecoUnitario;
    private DisabledDoubleField txtTotal;
    private boolean validaPrecoUnitario = true;
    private boolean isMedicamento;
    private WebMarkupContainer containerMedicamento;

    public PnlAdicionarDispensacaoPrescricaoItemControlaLote(String id, boolean isMedicamento) {
        super(id);
        this.isMedicamento = isMedicamento;
        init();
    }

    private void init() {
        form = new Form("form", model = new CompoundPropertyModel<DispensacaoMedicamentoItem>(new DispensacaoMedicamentoItem()));

        form.setOutputMarkupId(true);

        DispensacaoMedicamentoItem proxy = on(DispensacaoMedicamentoItem.class);

        form.add(new DisabledInputField(path(proxy.getProduto().getDescricao())));

        containerMedicamento = new WebMarkupContainer("containerMedicamento");
        containerMedicamento.setOutputMarkupId(true);
        containerMedicamento.add(new DisabledInputField(path(proxy.getReceituarioItem().getPosologia())));
        containerMedicamento.add(new DisabledInputField(path(proxy.getReceituarioItem().getTipoViaMedicamento().getDescricao())));
        form.add(containerMedicamento);
        containerMedicamento.setVisible(isMedicamento);

        form.add(txtQuantidadeLote = new InputField(path(proxy.getQuantidadeDispensar())));
        form.add(pnlSaidaLote = new PnlSaidaLote("pnlSaidaLote", new PropertyModel(this, "lote")));
        pnlSaidaLote.setTxtQuantidade(txtQuantidadeLote);
        pnlSaidaLote.setValidarLotesVencidos(true);
        pnlSaidaLote.registerEvents();
        pnlSaidaLote.setValidateRequired(false);

        form.add(new AbstractAjaxButton("btnAdicionarLote") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarLote(target);
            }
        });

        form.add(tblLotes = new Table("tblLotes", getColumnsLotes(), getCollectionProvider()));
        tblLotes.populate();

        form.add(new DisabledDoubleField(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_QUANTIDADE_DISPENSADA)));
        form.add(txtPrecoUnitario = new DoubleField(DispensacaoMedicamentoItem.PROP_PRECO_UNITARIO));
        form.add(txtTotal = new DisabledDoubleField("total", new PropertyModel<Double>(this, "total")));

        txtPrecoUnitario.add(new AjaxFormComponentUpdatingBehavior("onblur") {
            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                if (txtPrecoUnitario.getComponentValue() != null) {
                    total = Coalesce.asDouble(model.getObject().getQuantidadeDispensada(), 0D) * Coalesce.asDouble(model.getObject().getPrecoUnitario(), 0D);
                    art.add(txtTotal);
                }
            }
        });

        form.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                fechar(target);
            }
        });

        btnFechar.setDefaultFormProcessing(false);

        add(form);
        //atalhosTeclado();
    }

    private List<IColumn> getColumnsLotes() {
        List<IColumn> columns = new ArrayList<IColumn>();

        ColumnFactory columnFactory = new ColumnFactory(MovimentoGrupoEstoqueItemDTO.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("lote"), VOUtils.montarPath(MovimentoGrupoEstoqueItemDTO.PROP_GRUPO_ESTOQUE)));
        columns.add(columnFactory.createColumn(BundleManager.getString("quantidade"), VOUtils.montarPath(MovimentoGrupoEstoqueItemDTO.PROP_QUANTIDADE)));

        return columns;
    }

    private CustomColumn getCustomColumn() {
        return new CustomColumn<MovimentoGrupoEstoqueItemDTO>() {
            @Override
            public Component getComponent(String componentId, final MovimentoGrupoEstoqueItemDTO rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerLote(target, rowObject);
                    }
                };
            }
        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lotes;
            }
        };
    }

    @Override
    public void setObject(AjaxRequestTarget target, DispensacaoMedicamentoItem dispensacaoMedicamentoItem, Empresa empresaBaixa) {
        lotes.clear();

        _dispensacaoMedicamentoItem = dispensacaoMedicamentoItem;
        DispensacaoMedicamentoItem dmi = (DispensacaoMedicamentoItem) SerializationUtils.clone(dispensacaoMedicamentoItem);
        model.setObject(dmi);

        pnlSaidaLote.setEmpresa(empresaBaixa);
        pnlSaidaLote.setProduto(target, dmi.getProduto());
        lotes = new ArrayList<MovimentoGrupoEstoqueItemDTO>();
        if (dmi.getMovimentoGrupoEstoqueItemDTOList() != null && !dmi.getMovimentoGrupoEstoqueItemDTOList().isEmpty()) {
            lotes.addAll(dmi.getMovimentoGrupoEstoqueItemDTOList());
        }

        try {
            Atendimento atendimento = LoadManager.getInstance(Atendimento.class)
                    .addProperties(new HQLProperties(Atendimento.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(Atendimento.PROP_CODIGO, dmi.getDispensacaoMedicamento().getAtendimento().getCodigo()))
                    .start().getVO();

            if (Coalesce.asDouble(model.getObject().getPrecoUnitario()) == 0D) {
                Double precoProduto = BOFactory.getBO(HospitalFacade.class).getPrecoProduto(dmi.getProduto(), atendimento.getConvenio(), DataUtil.getDataAtual());

                if (precoProduto != null) {
                    model.getObject().setPrecoUnitario(precoProduto);
                }
            }

            Convenio convenio = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("convenioSUS");
            if (convenio.equals(atendimento.getConvenio())) {
                validaPrecoUnitario = false;
            } else {
                validaPrecoUnitario = true;
            }
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Logger.getLogger(PnlAdicionarDispensacaoPrescricaoItemControlaLote.class.getName()).log(Level.SEVERE, null, ex);
        }

        total = Coalesce.asDouble(model.getObject().getQuantidadeDispensada(), 0D) * Coalesce.asDouble(model.getObject().getPrecoUnitario(), 0D);
        target.add(txtTotal);
        target.add(form);
    }

    private void adicionarLote(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (StringUtils.trimToNull(lote) == null) {
            throw new ValidacaoException(BundleManager.getString("informeLote"));
        }

        Double quantidadeDispensar = (Double) txtQuantidadeLote.getComponentValue();
        if (quantidadeDispensar == null) {
            throw new ValidacaoException(BundleManager.getString("informeQuantidadeDispensar"));
        }

        MovimentoGrupoEstoqueItemDTO dto = getLote(pnlSaidaLote.getLoteSelecionado());
        if (dto != null) {
            dto.setQuantidade(dto.getQuantidade() + quantidadeDispensar);
        } else {
            if (StringUtils.trimToNull(lote) != null && Coalesce.asDouble(quantidadeDispensar) > 0D) {
                dto = pnlSaidaLote.getLoteSelecionado();
                dto.setGrupoEstoque(lote);
                dto.setQuantidade(quantidadeDispensar);
                lotes.add(dto);
            }
        }
        realizarCalculos(target);
        lote = null;
        quantidadeDispensar = null;
        tblLotes.update(target);
        pnlSaidaLote.limparLote(target);
        txtQuantidadeLote.limpar(target);
        target.add(form);
        target.focusComponent(pnlSaidaLote.getTxtLote());
        model.getObject().setMovimentoGrupoEstoqueItemDTOList(lotes);
    }

    private MovimentoGrupoEstoqueItemDTO getLote(MovimentoGrupoEstoqueItemDTO dto) {
        for (MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO : lotes) {
            if (movimentoGrupoEstoqueItemDTO.equals(pnlSaidaLote.getLoteSelecionado())) {
                return movimentoGrupoEstoqueItemDTO;
            }
        }
        return null;
    }

    private void removerLote(AjaxRequestTarget target, MovimentoGrupoEstoqueItemDTO dto) {
        for (int i = 0; i < lotes.size(); i++) {
            if (lotes.get(i) == dto) {
                lotes.remove(i);
            }
        }
        tblLotes.update(target);

        realizarCalculos(target);
    }

    private void realizarCalculos(AjaxRequestTarget target) {
        Double quantidadeTotal = 0D;

        for (MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO : lotes) {
            quantidadeTotal += movimentoGrupoEstoqueItemDTO.getQuantidade();
        }

        model.getObject().setQuantidadeDispensada(quantidadeTotal);

        this.total = quantidadeTotal * Coalesce.asDouble(model.getObject().getPrecoUnitario(), 0D);

        target.add(form);
    }

    @Override
    public void update(AjaxRequestTarget target) {
        DispensacaoMedicamentoItem dmi = model.getObject();
        model.setObject(new DispensacaoMedicamentoItem());
        model.setObject(dmi);
        target.add(form);
        target.focusComponent(pnlSaidaLote.getTxtLote());
    }

    public void adicionar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (validaPrecoUnitario) {
            if (this.model.getObject().getPrecoUnitario() == null || this.model.getObject().getPrecoUnitario().equals(0D)) {
                throw new ValidacaoException(BundleManager.getString("informePrecoUnitario"));
            }
        } else {
            if (this.model.getObject().getPrecoUnitario() == null || this.model.getObject().getPrecoUnitario().equals(0D)) {
                this.model.getObject().setPrecoUnitario(Valor.round(Coalesce.asDouble(EstoqueEmpresaHelper.getPrecoCusto(ApplicationSession.get().getSession().getEmpresa(), model.getObject().getProduto())), 2));
            }
        }
        adicionar(target, model.getObject(), _dispensacaoMedicamentoItem);
    }

    public abstract void adicionar(AjaxRequestTarget target, DispensacaoMedicamentoItem itemOrigem, DispensacaoMedicamentoItem itemDestino) throws ValidacaoException, DAOException;

    public abstract void fechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    @Override
    public void clearNotifications(AjaxRequestTarget target) {
    }

    @Override
    public void error(AjaxRequestTarget target, String message) {
    }

    @Override
    public void info(AjaxRequestTarget target, String message) {
    }

    @Override
    public void message(AjaxRequestTarget target, String message, int lvl) {
    }

    @Override
    public void updateNotificationPanel(AjaxRequestTarget target) {
    }

    @Override
    public void updateNotificationPanel(AjaxRequestTarget target, boolean scrollToTop) {
    }

    @Override
    public void warn(AjaxRequestTarget target, String message) {
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return pnlSaidaLote.getTxtLote();
    }

    private void atalhosTeclado() {
        add(new AbstractDefaultAjaxBehavior() {
            @Override
            protected void respond(AjaxRequestTarget target) {
                String action = RequestCycle.get().getRequest().getRequestParameters().getParameterValue("action").toString();
                if ("esc".equals(action)) {
                    try {
                        fechar(target);
                    } catch (ValidacaoException ex) {
                        Logger.getLogger(PnlAdicionarDispensacaoPrescricaoItemControlaLote.class.getName()).log(Level.SEVERE, null, ex);
                    } catch (DAOException ex) {
                        Logger.getLogger(PnlAdicionarDispensacaoPrescricaoItemControlaLote.class.getName()).log(Level.SEVERE, null, ex);
                    }
                } else if ("adicionar".equals(action)) {
                    try {
                        System.err.println("adicionadooooo");
                        adicionar(target);
                    } catch (ValidacaoException ex) {
                        info(target, ex.getMessage());
                    } catch (DAOException ex) {
                        Logger.getLogger(PnlAdicionarDispensacaoPrescricaoItemControlaLote.class.getName()).log(Level.SEVERE, null, ex);
                    }
                }
            }

            @Override
            public void renderHead(Component component, IHeaderResponse response) {
                super.renderHead(component, response);
                String list = getMarkupId() + "lote";
                String script = "$.Shortcuts.add({"
                        + "type: 'down',"
                        + "mask: 'Alt+a',"
                        + "enableInInput: true ,"
                        + "list: '" + list + "',"
                        + "handler: function() {"
                        + getCallbackFunctionBody(CallbackParameter.resolved("action", "'adicionar'"))
                        + "}"
                        + "});"
                        + "$.Shortcuts.add({"
                        + "type: 'down',"
                        + "mask: 'Esc',"
                        + "enableInInput: true ,"
                        + "list: '" + list + "',"
                        + "handler: function() {"
                        + getCallbackFunctionBody(CallbackParameter.resolved("action", "'esc'"))
                        + "}"
                        + "});"
                        + "$.Shortcuts.start('" + list + "');";

                response.render(JavaScriptHeaderItem.forScript(script, getMarkupId() + "atalho"));
            }
        });

    }
}
