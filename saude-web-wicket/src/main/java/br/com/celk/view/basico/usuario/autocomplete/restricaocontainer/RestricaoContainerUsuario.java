package br.com.celk.view.basico.usuario.autocomplete.restricaocontainer;

import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.inputfield.InputField;
import br.com.ksisolucoes.bo.basico.interfaces.dto.QueryConsultaUsuarioDTOParam;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class RestricaoContainerUsuario extends Panel implements IRestricaoContainer<QueryConsultaUsuarioDTOParam> {

    private InputField<String> txtNome;
    
    private QueryConsultaUsuarioDTOParam param = new QueryConsultaUsuarioDTOParam();
    
    public RestricaoContainerUsuario(String id) {
        super(id);
        
        WebMarkupContainer root = new WebMarkupContainer("root", new CompoundPropertyModel(param));
        
        root.add(txtNome = new InputField<String>("nome"));
        
        add(root);
    }

    @Override
    public QueryConsultaUsuarioDTOParam getRestricoes() {
        return param;
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        txtNome.limpar(target);
    }

    @Override
    public Component getComponentRequestFocus() {
        return txtNome;
    }

}
