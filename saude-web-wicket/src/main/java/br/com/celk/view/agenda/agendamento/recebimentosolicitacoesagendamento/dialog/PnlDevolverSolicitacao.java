package br.com.celk.view.agenda.agendamento.recebimentosolicitacoesagendamento.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.system.util.MessageUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.LoteSolicitacaoAgendamentoItem;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlDevolverSolicitacao extends Panel{
    
    private CompoundPropertyModel<LoteSolicitacaoAgendamentoItem> model;
    private InputArea motivo;
    
    public PnlDevolverSolicitacao(String id){
        super(id);
        init();
    }

    private void init() {
        Form form = new Form<LoteSolicitacaoAgendamentoItem>("form", model = new CompoundPropertyModel(new LoteSolicitacaoAgendamentoItem()));
        
        LoteSolicitacaoAgendamentoItem proxy = on(LoteSolicitacaoAgendamentoItem.class);
        
        form.add(new DisabledInputField(path(proxy.getLoteSolicitacaoAgendamento().getEmpresa().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getNomeSocial())));
        form.add(new DisabledInputField(path(proxy.getSolicitacaoAgendamento().getTipoProcedimento().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getSolicitacaoAgendamento().getCodigo())));
        form.add(motivo = new InputArea(path(proxy.getMotivo())));
        
        form.add(new AbstractAjaxButton("btnConfirmar") {
            
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if(validarMotivo(target)){
                    onConfirmar(target, model.getObject());
                }
            }
        });
        
        form.add(new AbstractAjaxButton("btnCancelar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onCancelar(target);
            }
        }.setDefaultFormProcessing(false));
        
        add(form);
    }
    
    public boolean validarMotivo(AjaxRequestTarget target) {
        try {
            if(motivo.getComponentValue() == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_infome_motivo"));
            }
        } catch (ValidacaoException e) {              
            MessageUtil.modalWarn(target, this, e);
            return false;
        }
        
        return true;
    }    
   
    public abstract void onConfirmar(AjaxRequestTarget target, LoteSolicitacaoAgendamentoItem loteSolicitacaoAgendamentoItem) throws ValidacaoException, DAOException;
    
    public abstract void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public void limpar(AjaxRequestTarget target){
        motivo.limpar(target);
    }
    
    public void setLoteSolicitacaoAgendamentoItem(LoteSolicitacaoAgendamentoItem loteSolicitacaoAgendamentoItem){
        this.model.setObject(loteSolicitacaoAgendamentoItem);
    }
}