package br.com.celk.view.agenda.cadastro.dialog;

import br.com.celk.agendamento.AgendamentoHelper;
import br.com.celk.agendamento.CadastroAgendaBehavior;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.duracaofield.HoraMinutoField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.agendamento.dto.AgendaGradeAtendimentoHorariosDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeHorario;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.PropertyModel;

import java.text.ParseException;
import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlAgendaGradeHorario extends Panel{
    
    private Form form;
    private AgendaGradeAtendimentoHorariosDTO model;
    private Date horario;
    private Date data;
    private InputField txtHorario;
    private Table tblAgendaGradeHorario;
    private List<AgendaGradeHorario> agendaGradeHorarioList = new ArrayList<AgendaGradeHorario>();
    private DlgConfirmacaoSimNao dlgConfirmacaoSimNao;
    private Agenda agenda;
    private List<AgendaGradeAtendimentoHorariosDTO> agendaGradeAtendimentoHorariosAtuaisDTOList;
    private CadastroAgendaBehavior behavior = null;
    private WebMarkupContainer containerDados;
    private AbstractAjaxButton btnSalvar;
    private final boolean somenteLeitura;

    public PnlAgendaGradeHorario(String id, boolean somenteLeitura ) {
        super(id);
        this.somenteLeitura = somenteLeitura;
        init();
    }

    private void init() {
        form = new Form("form");
        setOutputMarkupId(true);

        form.add(containerDados = new WebMarkupContainer("containerDados"));
        containerDados.setVisible(!somenteLeitura);
        containerDados.add(txtHorario = new HoraMinutoField("horario", new PropertyModel<Date>(this, "horario")));
        containerDados.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarAdicionar(target);
            }
        });

        form.add(tblAgendaGradeHorario = new Table("tblAgendaGradeHorario", getColumns(), getCollectionProvider()));
        tblAgendaGradeHorario.setScrollY("180px");
        tblAgendaGradeHorario.populate();

        form.add(btnSalvar = new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarCadastro();
                model.setAgendaGradeHorarioList(agendaGradeHorarioList);
                Integer quantidadeAtendimento = 0;
                for (AgendaGradeHorario agendaGradeHorario : agendaGradeHorarioList) {
                    if (agendaGradeHorario.getStatus().equals(AgendaGradeHorario.Status.PENDENTE.value())
                            || agendaGradeHorario.getStatus().equals(AgendaGradeHorario.Status.AGENDADO.value())) {
                        quantidadeAtendimento++;
                    }
                }
                model.getAgendaGradeAtendimento().setQuantidadeAtendimento(Long.valueOf(quantidadeAtendimento));
                AgendamentoHelper.validarQuantidadeAtendimentoAgendaGradeAtendimento(model.getAgendaGradeAtendimento());
                onSalvar(target, model);
            }
        });
        btnSalvar.setVisible(!somenteLeitura);

        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        add(form);
    }
    
    private Date getDataFormatada(){
        try {
            return Data.getDateTime(data, horario);
        } catch (ParseException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return null;
    }

    private void getHoraList(AgendaGradeAtendimento agendaGradeAtendimento) {
        try {
            Date horaInicial = Data.getDateTime(data, agendaGradeHorarioList.get(0).getHora());
            agendaGradeAtendimento.getAgendaGrade().setHoraInicial(horaInicial);

            Date horaFinal = Data.getDateTime(data, agendaGradeHorarioList.get(agendaGradeHorarioList.size()-1).getHora());
            agendaGradeAtendimento.getAgendaGrade().setHoraFinal(horaFinal);
        } catch (ParseException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }
    
    private void getHora(AgendaGradeAtendimento agendaGradeAtendimento) {
        Date horaInicial = agendaGradeAtendimento.getAgendaGrade().getHoraInicial();
        Date horaFim = agendaGradeAtendimento.getAgendaGrade().getHoraFinal();
        
        if(horaInicial != null && horaFim != null){
            if(horaInicial.after(getDataFormatada())){
                horaInicial = getDataFormatada();
                agendaGradeAtendimento.getAgendaGrade().setHoraInicial(horaInicial);
            } else if(horaFim.before(getDataFormatada())){
                Long tempoMedio = agendaGradeAtendimento.getTempoMedio();
                horaFim = getDataFormatada();

                horaFim = Data.addMinutos(horaFim, tempoMedio.intValue());
                agendaGradeAtendimento.getAgendaGrade().setHoraFinal(horaFim);
            }
        }
    }
    
    private void validarAdicionar(AjaxRequestTarget target) throws ValidacaoException, DAOException{
        if (horario == null) {
            throw new ValidacaoException(BundleManager.getString("informeHorario"));
        }
        
        AgendaGradeHorario agendaGradeHorario = new AgendaGradeHorario();
        agendaGradeHorario.setStatus(AgendaGradeHorario.Status.PENDENTE.value());
        agendaGradeHorario.setAgendaGradeAtendimento(model.getAgendaGradeAtendimento());        
        try{
            agendaGradeHorario.setHora(Data.getDateTime(model.getAgendaGradeAtendimento().getAgendaGrade().getData(), horario));
        
        
            AgendaGradeAtendimento agendaGradeAtendimento = model.getAgendaGradeAtendimento();

            if(agendaGradeAtendimento.getQuantidadeAtendimentoOriginal() == null){
                agendaGradeAtendimento.setQuantidadeAtendimentoOriginal(agendaGradeAtendimento.getQuantidadeAtendimento());            
            }
            
            AgendaGradeAtendimento aga = (AgendaGradeAtendimento) SerializationUtils.clone(agendaGradeAtendimento);
            aga.setAgendaGrade(model.getAgendaGradeAtendimento().getAgendaGrade());
            
            behavior.validarConflitosAgendaGradeHorarioNaoSalvas(agendaGradeAtendimentoHorariosAtuaisDTOList, agendaGradeHorarioList, aga, agendaGradeHorario);

            if (CollectionUtils.isEmpty(agendaGradeHorarioList)) {
                agendaGradeAtendimento.getAgendaGrade().setHoraInicial(Data.getDateTime(model.getAgendaGradeAtendimento().getAgendaGrade().getData(), horario));
                agendaGradeAtendimento.getAgendaGrade().setHoraFinal(Data.getDateTime(model.getAgendaGradeAtendimento().getAgendaGrade().getData(), 
                        Data.addMinutos(horario, model.getAgendaGradeAtendimento().getTempoMedio().intValue())));
            }

            
            validarConflitos(target, aga, agendaGradeHorario);
        } catch (ParseException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }
    
    private void validarConflitos(AjaxRequestTarget target, final AgendaGradeAtendimento agendaGradeAtendimentoClone, final AgendaGradeHorario agendaGradeHorario) throws ValidacaoException, DAOException {
        if (agenda != null) {

            Date horaInicial = agendaGradeAtendimentoClone.getAgendaGrade().getHoraInicial();
            Date horaFinal = agendaGradeAtendimentoClone.getAgendaGrade().getHoraFinal();

            if (horario.before(horaInicial)) {
                agendaGradeAtendimentoClone.getAgendaGrade().setHoraInicial(horario);
            } else if (horario.after(horaFinal) || horario.equals(horaFinal)) {
                agendaGradeAtendimentoClone.getAgendaGrade().setHoraFinal(Data.addMinutos(horario, agendaGradeAtendimentoClone.getTempoMedio().intValue()));
            }

            String mensagemHorarioConflitante = behavior.validarConflitosAgendasSalvas(agendaGradeAtendimentoClone);
            
            if(mensagemHorarioConflitante != null){
               WindowUtil.addModal(target, this, dlgConfirmacaoSimNao = new DlgConfirmacaoSimNao(WindowUtil.newModalId(this), mensagemHorarioConflitante) {
                    @Override
                    public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        adicionarItem(target, agendaGradeHorario);
                    }
                });
                dlgConfirmacaoSimNao.show(target); 
            } else {
                adicionarItem(target, agendaGradeHorario);
            }
        }
    }
    
    private void validarCadastro() throws ValidacaoException {
        if (CollectionUtils.isEmpty(agendaGradeHorarioList)) {
            throw new ValidacaoException(bundle("alertaExclusãoGrade"));
        }
    }
    
    private void adicionarItem(AjaxRequestTarget target, AgendaGradeHorario agendaGradeHorario) throws ValidacaoException {
        getHora(model.getAgendaGradeAtendimento());
        agendaGradeHorarioList.add(agendaGradeHorario);
        ordenarLista();
        
        tblAgendaGradeHorario.update(target);
        txtHorario.limpar(target);
        if (model.getAgendaGradeHorarioToSaveList() == null) {
            model.setAgendaGradeHorarioToSaveList(new ArrayList<AgendaGradeHorario>());
        }
        model.getAgendaGradeHorarioToSaveList().add(agendaGradeHorario);
    }
    
    private void ordenarLista() {
        Collections.sort(agendaGradeHorarioList, new Comparator<AgendaGradeHorario>() {
            @Override
            public int compare(AgendaGradeHorario o1, AgendaGradeHorario o2) {
                if (o1.getHora().before(o2.getHora())) {
                    return -1;
                } else {
                    return 1;
                }
            }
        });
    }

    private void remover(AjaxRequestTarget target, AgendaGradeHorario rowObject) throws ValidacaoException, DAOException {
        Date agendaHoraInicial = null;
        if(agendaGradeHorarioList.size() == 1 && agendaGradeHorarioList.get(0).equals(rowObject)) {
            agendaHoraInicial = model.getAgendaGradeAtendimento().getAgendaGrade().getHoraInicial();
        }
        CrudUtils.removerItem(target,tblAgendaGradeHorario,agendaGradeHorarioList,rowObject);
//        for (int i = 0; i < agendaGradeHorarioList.size(); i++) {
//            if (agendaGradeHorarioList.get(i) == rowObject) {
//                agendaGradeHorarioList.remove(i);
//            }
//        }
//        tblAgendaGradeHorario.update(target);

        if(CollectionUtils.isNotNullEmpty(agendaGradeHorarioList)){
            getHoraList(model.getAgendaGradeAtendimento());
        } else {
            model.getAgendaGradeAtendimento().getAgendaGrade().setHoraInicial(agendaHoraInicial);
            model.getAgendaGradeAtendimento().getAgendaGrade().setHoraFinal(null);
        }
        if (model.getAgendaGradeHorarioToRemoveList() == null) {
            model.setAgendaGradeHorarioToRemoveList(new ArrayList<AgendaGradeHorario>());
        }
        model.getAgendaGradeHorarioToRemoveList().add(rowObject);
    }
    
    private List<IColumn> getColumns() {

        ColumnFactory columnFactory = new ColumnFactory(AgendaGradeHorario.class);
        List<IColumn> columns = new ArrayList<IColumn>();
        AgendaGradeHorario proxy = on(AgendaGradeHorario.class);

        columns.add(getCustomColumn());
        columns.add(new DateTimeColumn<AtendimentoInformacao>(bundle("data"), path(proxy.getHora()), path(proxy.getHora())).setPattern("dd/MM/yyyy"));
        columns.add(new DateTimeColumn<AtendimentoInformacao>(bundle("hora"), path(proxy.getHora()), path(proxy.getHora())).setPattern("HH:mm"));
        columns.add(columnFactory.createColumn(BundleManager.getString("situacao"), path(proxy.getDescricaoStatus())));

        return columns;
    }
    
    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<AgendaGradeHorario>() {
            @Override
            public void customizeColumn(final AgendaGradeHorario rowObject) {
                addAction(ActionType.REMOVER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        remover(target, rowObject);
                    }
                }).setEnabled(AgendaGradeHorario.Status.PENDENTE.value().equals(rowObject.getStatus())).setVisible(!somenteLeitura);
            }
        };
    }
    
    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return agendaGradeHorarioList;
            }
        };
    }
   
    public abstract void onSalvar(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO dto) throws ValidacaoException, DAOException;
    
    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public void setDTO(AgendaGradeAtendimentoHorariosDTO dto, AjaxRequestTarget target, Agenda agenda, List<AgendaGradeAtendimentoHorariosDTO> agendaGradeAtendimentoHorariosAtuaisDTOList){
        this.model = dto;
        this.agendaGradeHorarioList = new ArrayList<AgendaGradeHorario>(dto.getAgendaGradeHorarioList());
        txtHorario.limpar(target);
        this.data = dto.getAgendaGradeAtendimento().getAgendaGrade().getHoraInicial();
        this.agenda = agenda;
        this.agendaGradeAtendimentoHorariosAtuaisDTOList = agendaGradeAtendimentoHorariosAtuaisDTOList;
        this.behavior = new CadastroAgendaBehavior(agenda);
        
        target.add(txtHorario);
    }
}