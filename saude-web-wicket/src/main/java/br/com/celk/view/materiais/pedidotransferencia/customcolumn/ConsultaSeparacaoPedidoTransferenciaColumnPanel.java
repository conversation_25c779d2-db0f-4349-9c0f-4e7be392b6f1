package br.com.celk.view.materiais.pedidotransferencia.customcolumn;

import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.link.ReportLink;
import br.com.celk.system.authorization.annotation.PermissionContainer;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public abstract class ConsultaSeparacaoPedidoTransferenciaColumnPanel extends Panel implements PermissionContainer{

    private AjaxLink btnSeparar;
    
    private ReportLink btnImprimir;
    
    public ConsultaSeparacaoPedidoTransferenciaColumnPanel(String id) {
        super(id);
        init();
    }

    private void init() {
        add(btnSeparar = new AbstractAjaxLink("btnSeparar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onSeparar(target);
            }

        });

        add(btnImprimir = new ReportLink("btnImprimir") {

            @Override
            public DataReport getDataReport() throws ReportException {
                return onImprimir();
            }

        });
        
        btnSeparar.add(new AttributeModifier("title", BundleManager.getString("separarPedido")));
        btnImprimir.add(new AttributeModifier("title", BundleManager.getString("imprimir")));
    }
    
    public abstract void onSeparar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public abstract DataReport onImprimir() throws ReportException;

}
