package br.com.celk.view.materiais.dispensacao.item;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.ProfissionalCargaHoraria;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoTreinamento;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlAdicionarItemSemEstoque extends Panel{
    
    private Form form;
    private DispensacaoMedicamentoItem dispensacaoMedicamentoItem;
    private String descricaoProduto;
    private WebMarkupContainer image; 
    private InputField txtDescricaoProduto;
    private InputField txtQuantidadePrescrita;
    private final String IMG = "img-warn";
    private MultiLineLabel messageLabel;
    private Long quantidadePrescrita;
    
    public PnlAdicionarItemSemEstoque(String id){
        super(id);
        init();
    }

    private void init() {
        form = new Form("form");
        setOutputMarkupId(true);
        
        form.add(image = new WebMarkupContainer("img"));
        form.add(messageLabel = new MultiLineLabel("messageLabel"));
        form.add(txtDescricaoProduto = new DisabledInputField("descricaoProduto", new PropertyModel<String>(this, "descricaoProduto")));
        form.add(txtQuantidadePrescrita = new InputField("quantidadePrescrita", new PropertyModel<Long>(this, "quantidadePrescrita")));
        
        form.add(new AbstractAjaxButton("btnOk") {
            
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if(validarCadastro(target)){
                    dispensacaoMedicamentoItem.setQuantidadePrescrita(quantidadePrescrita.doubleValue());
                    dispensacaoMedicamentoItem.setQuantidadeDispensada(0D);
                    dispensacaoMedicamentoItem.setStatus(DispensacaoMedicamentoItem.STATUS_SEM_ESTOQUE);
                    onOk(target, dispensacaoMedicamentoItem);
                }
            }
        });
        
        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));
        
        add(form);
        image.add(new AttributeModifier("class", IMG));
    }
    
    public boolean validarCadastro(AjaxRequestTarget target) {
        try {            
            if(txtQuantidadePrescrita.getComponentValue() == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_quantidade_prescrita"));
            }
        } catch (ValidacaoException e) {              
            MessageUtil.modalWarn(target, this, e);
            return false;
        }
        
        return true;
    }
   
    public abstract void onOk(AjaxRequestTarget target, DispensacaoMedicamentoItem dispensacaoMedicamentoItem) throws ValidacaoException, DAOException;
    
    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public void setDTO(AjaxRequestTarget target, DispensacaoMedicamentoItem dispensacaoMedicamentoItem){
        this.dispensacaoMedicamentoItem = dispensacaoMedicamentoItem;
        txtDescricaoProduto.limpar(target);
        txtQuantidadePrescrita.limpar(target);
        Empresa empresa = ApplicationSession.get().getSessaoAplicacao().getEmpresa();
        MovimentoGrupoEstoqueItemDTO infoEstoqueDisponivel = carregarMovimentoGrupoEstoque(dispensacaoMedicamentoItem.getProduto(),empresa);
        if(infoEstoqueDisponivel != null) {
            double saldoFisico = infoEstoqueDisponivel.getEstoqueFisico() + infoEstoqueDisponivel.getEstoqueEncomendado();
            if (saldoFisico <= 0D && infoEstoqueDisponivel.getEstoqueReservado() <= 0D) {
                messageLabel.setDefaultModel(new Model<String>(BundleManager.getString("msgProdutoInformadoNaoPossuiEstoqueOuVencido")));
            } else {
                messageLabel.setDefaultModel(new Model<String>(BundleManager.getString("msgProdutoInformadoPossuiEstoqueVencido")));
            }
        }
        descricaoProduto = dispensacaoMedicamentoItem.getProduto().getDescricao();
        txtDescricaoProduto.setComponentValue(descricaoProduto);
        target.add(txtDescricaoProduto);
        
        if(Coalesce.asDouble(dispensacaoMedicamentoItem.getQuantidadePrescrita()) > 0D){
            quantidadePrescrita = dispensacaoMedicamentoItem.getQuantidadePrescrita().longValue();
            txtQuantidadePrescrita.setComponentValue(quantidadePrescrita);
            target.add(txtQuantidadePrescrita);
        }
        
        target.focusComponent(txtQuantidadePrescrita);
    }

    private MovimentoGrupoEstoqueItemDTO carregarMovimentoGrupoEstoque(Produto produto, Empresa empresa){
        Date dataAtual = DataUtil.getDataAtual();
        MovimentoGrupoEstoqueItemDTO dto = new MovimentoGrupoEstoqueItemDTO();
        dto.setEstoqueReservado(0D);
        dto.setEstoqueEncomendado(0D);
        dto.setEstoqueFisico(0D);
        dto.setEmpresa(empresa);
        dto.setProduto(produto);
        List<GrupoEstoque> grupoEstoques =  LoadManager.getInstance(GrupoEstoque.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_CODIGO_DEPOSITO), 0))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_ESTOQUE_EMPRESA, EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA, Empresa.PROP_CODIGO), empresa.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_ESTOQUE_EMPRESA, EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO), produto))
                //.addParameter(new QueryCustom.QueryCustomParameter(GrupoEstoque.PROP_DATA_VALIDADE, BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, dataAtual, HQLHelper.NOT_RESOLVE_TYPE, dataAtual))
                .addParameter(new QueryCustom.QueryCustomParameter(GrupoEstoque.PROP_DATA_VALIDADE, BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, DataUtil.getDataAtual()))
                .addParameter(new QueryCustom.QueryCustomParameter(GrupoEstoque.PROP_ESTOQUE_FISICO, BuilderQueryCustom.QueryParameter.MAIOR, 0D))
                .start().getList();
        //datavencimento >= datatual
        //estoquefisico > 0
        for(GrupoEstoque ge : grupoEstoques){
            dto.setEstoqueFisico(dto.getEstoqueFisico()+ge.getEstoqueFisico());
            dto.setEstoqueEncomendado(dto.getEstoqueEncomendado()+ge.getEstoqueEncomendado());
            dto.setEstoqueReservado(dto.getEstoqueReservado()+ge.getEstoqueReservado());
        }
        return dto;
    }
}