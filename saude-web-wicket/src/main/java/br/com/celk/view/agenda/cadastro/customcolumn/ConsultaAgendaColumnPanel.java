package br.com.celk.view.agenda.cadastro.customcolumn;

import br.com.celk.agendamento.AgendamentoHelper;
import br.com.celk.component.dialog.DlgConfirmacaoObject;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.authorization.annotation.ActionsEnum;
import br.com.celk.system.authorization.annotation.Permission;
import br.com.celk.system.authorization.annotation.PermissionContainer;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.Coalesce;
import br.com.celk.view.agenda.cadastro.CadastroAgendaPageStep1;
import br.com.celk.view.agenda.cadastro.ConsultaDetalhadaAgendaPage;
import br.com.celk.view.agenda.cadastro.DlgConsultaPacientesAgendados;
import br.com.celk.view.agenda.manutencaoagenda.ManutencaoAgendaDiarioPage;
import br.com.celk.view.agenda.manutencaoagenda.ManutencaoAgendaHorarioPage;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.markup.html.panel.Panel;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 */
public abstract class ConsultaAgendaColumnPanel extends Panel implements PermissionContainer{

    private AbstractAjaxLink btnEditar;
    //    private AbstractAjaxLink btnExcluir;
    private AbstractAjaxLink btnInativar;
    private AbstractAjaxLink btnAtivar;
    private AbstractAjaxLink btnVisualizarAgendados;
    private AbstractAjaxLink btnConsultar;
    
    @Permission(type = Permissions.MANUTENCAO, action=ActionsEnum.RENDER)
    private AbstractAjaxLink btnManutencaoAgenda;
    
    private Agenda agenda;
    private DlgConfirmacaoObject<Agenda> dlgConfirmacaoExclusaoAgenda;
    private DlgConfirmacaoObject<Agenda> dlgConfirmacaoAtivarAgenda;
    private DlgConfirmacaoObject dlgConfirmacaoExclusaoAgendaSemAgendamento;
    private DlgConsultaPacientesAgendados dlgConsultaPacientesAgendados;

    public ConsultaAgendaColumnPanel(String id, Agenda agenda) {
        super(id);
        this.agenda = agenda;
        init();
    }

    private void init() {
        add(btnEditar = new AbstractAjaxLink("btnEditar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                editar();
            }

            @Override
            public boolean isEnabled() {
                return !agenda.getStatus().equals(Agenda.STATUS_INATIVA);
            }
        });

        add(btnInativar = new AbstractAjaxLink("btnInativar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                inativar(target);
            }

            @Override
            public boolean isVisible() {
                return !agenda.getStatus().equals(Agenda.STATUS_INATIVA);

            }
        });

        add(btnAtivar = new AbstractAjaxLink("btnAtivar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                ativar(target);
            }

            @Override
            public boolean isVisible() {
                return agenda.getStatus().equals(Agenda.STATUS_INATIVA);
            }
        });

        add(btnVisualizarAgendados = new AbstractAjaxLink("btnVisualizarAgendados") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                viewDlgConsultaPacientesAgendados(target);
            }

            @Override
            public boolean isEnabled() {
                return existsAgendamentos();
            }
        });

        add(btnConsultar = new AbstractAjaxLink("btnConsultar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                setResponsePage(new ConsultaDetalhadaAgendaPage(agenda.getCodigo()));
            }
        });
        
        add(btnManutencaoAgenda = new AbstractAjaxLink("btnManutencaoAgenda") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                if(TipoProcedimento.TipoAgenda.DIARIO.value().equals(AgendamentoHelper.getTipoAgenda(agenda))){
                    setResponsePage(new ManutencaoAgendaDiarioPage(agenda.getCodigo()));
                } else if(TipoProcedimento.TipoAgenda.HORARIO.value().equals(AgendamentoHelper.getTipoAgenda(agenda))){
                    setResponsePage(new ManutencaoAgendaHorarioPage(agenda.getCodigo()));                    
                }
            }
            
            @Override
            public boolean isEnabled() {
                try {
                    return !TipoProcedimento.TipoAgenda.PERSONALIZADA.value().equals(AgendamentoHelper.getTipoAgenda(agenda)) &&
                            (new Long(Agenda.STATUS_ABERTO).equals(Coalesce.asLong(agenda.getStatus()))
                                    || new Long(Agenda.STATUS_CONFIRMADO).equals(Coalesce.asLong(agenda.getStatus()))
                                    || new Long(Agenda.STATUS_PENDENTE).equals(Coalesce.asLong(agenda.getStatus()))
                                    || new Long(Agenda.STATUS_NAO_APROVADA).equals(Coalesce.asLong(agenda.getStatus())));
                } catch (ValidacaoException ex) {
                    Logger.getLogger(ConsultaAgendaColumnPanel.class.getName()).log(Level.SEVERE, null, ex);
                }
                return false;
            }
        });

        btnEditar.add(new AttributeModifier("title", BundleManager.getString("editar")));
        btnInativar.add(new AttributeModifier("title", BundleManager.getString("inativar")));
        btnVisualizarAgendados.add(new AttributeModifier("title", BundleManager.getString("visualizarAgendados")));
        btnConsultar.add(new AttributeModifier("title", BundleManager.getString("consultar")));
        btnManutencaoAgenda.add(new AttributeModifier("title", BundleManager.getString("manutencaoAgenda")));
    }

    private void editar() throws DAOException, ValidacaoException {
        validaEdicaoAgenda();
        setResponsePage(new CadastroAgendaPageStep1(agenda));
    }

    private void validaEdicaoAgenda() throws DAOException, ValidacaoException {
        String isAprovarAgenda;
        isAprovarAgenda = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("aprovaAgendaCadastro");

        List<Long> status = new ArrayList();
        status.add(Agenda.STATUS_ABERTO);
        status.add(Agenda.STATUS_NAO_APROVADA);
        status.add(Agenda.STATUS_PENDENTE);
        if (RepositoryComponentDefault.SIM.equals(isAprovarAgenda)) {
            status.add(Agenda.STATUS_CONFIRMADO);
        }

        if (!status.contains(Coalesce.asLong(agenda.getStatus()))) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_somente_agenda_aberto_modificadas"));
        }
        
        Long tipoAgendaAtual = AgendamentoHelper.getTipoAgenda(agenda.getTipoProcedimento(), agenda.getEmpresa());
        if(agenda.getTipoAgendaOrigem() != null && !agenda.getTipoAgendaOrigem().equals(tipoAgendaAtual)){
            throw new ValidacaoException(bundle("msgNaoPossivelEditarAgendaPoisTipoAgendaAlteradoParaXParaPoderEditarFavorAlterarTipoAgendaTipoProcedimentoParaX",
                    AgendamentoHelper.getDescricaoTipoAgenda(tipoAgendaAtual), AgendamentoHelper.getDescricaoTipoAgenda(agenda.getTipoAgendaOrigem())));
        };
    }

    private void inativar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        viewDlgConfirmacaoInativacaoAgenda(target);
    }

    private void ativar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        viewDlgConfirmacaoAtivacaoAgenda(target);
    }

    private void viewDlgConfirmacaoInativacaoAgenda(AjaxRequestTarget target) {
        if (dlgConfirmacaoExclusaoAgenda == null) {
            WindowUtil.addModal(target, this, dlgConfirmacaoExclusaoAgenda = new DlgConfirmacaoObject<Agenda>(WindowUtil.newModalId(this), bundle("msgDesejaRealmenteInativarAgenda")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, Agenda agenda) throws ValidacaoException, DAOException {
                    inativarAgenda(target);
                }
            });
        }
        dlgConfirmacaoExclusaoAgenda.show(target, agenda);
    }

    private void viewDlgConfirmacaoAtivacaoAgenda(AjaxRequestTarget target) {
        if (dlgConfirmacaoAtivarAgenda == null) {
            WindowUtil.addModal(target, this, dlgConfirmacaoAtivarAgenda = new DlgConfirmacaoObject<Agenda>(WindowUtil.newModalId(this), bundle("msgDesejaRealmenteAtivarAgenda")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, Agenda agenda) throws ValidacaoException, DAOException {
                    ativarAgenda(target);
                }
            });
        }
        dlgConfirmacaoAtivarAgenda.show(target, agenda);
    }

    private void inativarAgenda(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(AgendamentoFacade.class).inativarAgenda(agenda);
        updateTable(target);
    }

    private void ativarAgenda(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(AgendamentoFacade.class).ativarAgenda(agenda);
        updateTable(target);
    }

    private void viewDlgConsultaPacientesAgendados(AjaxRequestTarget target) {
        if (dlgConsultaPacientesAgendados == null) {
            WindowUtil.addModal(target, this, dlgConsultaPacientesAgendados = new DlgConsultaPacientesAgendados(WindowUtil.newModalId(this)));
        }
        dlgConsultaPacientesAgendados.show(target, agenda);
    }

    private boolean existsAgendamentos() {
        return AgendamentoHelper.existsAgendamento(agenda);
    }

    public abstract void updateTable(AjaxRequestTarget target);
    
    public AjaxLink getBtnManutencaoAgenda() {
        return btnManutencaoAgenda;
    }
}
