package br.com.celk.view.basico.integracao.prontuario;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.vo.prontuario.integracao.ConfiguracaoEmpresaIntegracao;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.markup.html.form.Form;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.basico.restservice.dto.GravarAtendimentoProntuarioExternoDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public class CadastroConfiguracaoEmpresaIntegracaoPage extends CadastroPage<ConfiguracaoEmpresaIntegracao> {


    public CadastroConfiguracaoEmpresaIntegracaoPage() {
        super();
    }

    public CadastroConfiguracaoEmpresaIntegracaoPage(ConfiguracaoEmpresaIntegracao object) {
        super(object);
    }

    public CadastroConfiguracaoEmpresaIntegracaoPage(ConfiguracaoEmpresaIntegracao object, boolean viewOnly) {
        super(object, viewOnly);
    }

    @Override
    public void init(Form<ConfiguracaoEmpresaIntegracao> form) {

        ConfiguracaoEmpresaIntegracao proxy = on(ConfiguracaoEmpresaIntegracao.class);

        form.add(new AutoCompleteConsultaEmpresa(path(proxy.getEmpresa()), true));
        form.add(new RequiredInputField(path(proxy.getUrl())));
        form.add(new RequiredInputField(path(proxy.getId())));
        form.add(new RequiredInputField(path(proxy.getChave())));
    }

    @Override
    public Class<ConfiguracaoEmpresaIntegracao> getReferenceClass() {
        return ConfiguracaoEmpresaIntegracao.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaConfiguracaoEmpresaIntegracaoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("cadastroEmpresaConfiguracaoIntegracao");
    }

}
