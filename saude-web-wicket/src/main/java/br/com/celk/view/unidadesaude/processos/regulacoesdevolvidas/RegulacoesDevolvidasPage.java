package br.com.celk.view.unidadesaude.processos.regulacoesdevolvidas;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dirtyforms.button.SubmitLink;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.inputarea.DisabledInputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.solicitacao.AplicarValidacaoStatusSolicitacao;
import br.com.celk.solicitacao.ValidarReenvioSolicitacao;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.agenda.agendamento.DlgMotivoCancelamentoSolicitacaoAgendamento;
import br.com.celk.view.agenda.agendamento.DlgReenviarSolicitacaoAgendamento;
import br.com.celk.view.agenda.agendamento.dialog.DlgLancarOcorrenciaContatoListaEspera;
import br.com.celk.view.agenda.solicitacao.DetalhesSolicitacaoPage;
import br.com.celk.view.atendimento.consultaprontuario.ProntuarioPage;
import br.com.celk.view.atendimento.recepcao.panel.marcacao.dialog.DlgRecomendacoesAgenda;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.solicitacaoagendamento.SolicitacaoAgendamentoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.AtendimentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioImprimirComprovanteAgendamentoDTOParam;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.agendamento.AgendaGrade;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamentoOcorrencia;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import br.com.ksisolucoes.vo.service.sms.SmsControleIntegracao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class RegulacoesDevolvidasPage extends BasePage {

    private Form<RegulacoesDevolvidasDTO> form;
    private DlgConfirmacaoSimNao dlgConfirmacaoContato;
    private DlgRecomendacoesAgenda dlgRecomendacoesAgenda;
    private Table tableOcorrencias;
    private Table tableSolicitacoes;
    private ICollectionProvider<SolicitacaoAgendamentoOcorrencia, SolicitacaoAgendamento> collectionProvider;
    private ICollectionProvider<SolicitacaoAgendamento, SolicitacaoAgendamento> collectionProviderSolicitacao;
    private ICollectionProvider<AgendaGradeAtendimentoHorario, SolicitacaoAgendamento> collectionProviderAgendamento;
    private DlgLancarOcorrenciaContatoListaEspera dlgLancarOcorrencia;
    private RegulacoesDevolvidasDTO dto;
    private SolicitacaoAgendamento solicitacaoAgendamento;
    private WebMarkupContainer containerTableOcorrencias;

    private List<SolicitacaoAgendamentoOcorrencia> solicitacaoAgendamentoOcorrenciaList;
    private List<SolicitacaoAgendamento> solicitacaoAgendamentoList;
    private List<AgendaGradeAtendimentoHorario> agendaGradeAtendimentoHorarioList;
    private DlgMotivoCancelamentoSolicitacaoAgendamento dlgCancelamento;
    private DlgReenviarSolicitacaoAgendamento dlgReenviar;

    public RegulacoesDevolvidasPage() {
        super();
    }

    public RegulacoesDevolvidasPage(RegulacoesDevolvidasDTO dto) {
        super();
        this.dto = dto;
        solicitacaoAgendamento = dto.getSolicitacaoAgendamento();
        carregarSolicitacaoAgendamentro();
        init();
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("regulacoesDevolvidas");
    }

    private void init() {
        setOutputMarkupId(true);
        form = new Form("form", new CompoundPropertyModel(dto.getSolicitacaoAgendamento()));
        getAgendaGradeAtendimentoHorarioList();
        getSolicitacaoAgendamentoList();
        getSolicitacaoAgendamentoOcorrenciaList();

        SolicitacaoAgendamento proxy = on(SolicitacaoAgendamento.class);
        form.add(new Label(path(proxy.getUsuarioCadsus().getNomeSocial())));
        form.add(new DisabledInputField(path(proxy.getDataSolicitacao())));
        form.add(new DisabledInputField(path(proxy.getTipoProcedimento().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getEmpresa().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getNomeProfissionalOrigem())));

        form.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getTelefoneFormatado())));
        form.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getTelefone2Formatado())));
        form.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getTelefone3())));
        form.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getTelefone4())));
        form.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getCelularFormatado())));
        form.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getEmail())));

        form.add(new DisabledInputField(path(proxy.getUsuarioAutorizador())));
        form.add(new DisabledInputField(path(proxy.getDataAutorizador())));
        form.add(new DisabledInputArea(path(proxy.getObservacaoAutorizador())));

        form.add(containerTableOcorrencias = new WebMarkupContainer("containerTableOcorrencias"));
        containerTableOcorrencias.setOutputMarkupId(true).setVisible(true);

        containerTableOcorrencias.add(tableOcorrencias = new Table("tableOcorrencias", getColumnsOcorrencias(), getCollectionProviderOcorrencias()));
        tableOcorrencias.populate();

        form.add(containerTableOcorrencias);

        addModal(dlgCancelamento = new DlgMotivoCancelamentoSolicitacaoAgendamento(newModalId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, String motivo, SolicitacaoAgendamento solicitacaoAgendamento) throws DAOException, ValidacaoException {
                validarReenvioSolicitacao(solicitacaoAgendamento);
                BOFactoryWicket.getBO(AgendamentoFacade.class).cancelarSolicitacaoAgendamento(solicitacaoAgendamento.getCodigo(), motivo, false);
                voltarAction(target);
            }
        });
        addModal(dlgReenviar = new DlgReenviarSolicitacaoAgendamento(newModalId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, String observacao, SolicitacaoAgendamento solicitacaoAgendamento) throws DAOException, ValidacaoException {
                validarReenvioSolicitacao(solicitacaoAgendamento);
                BOFactoryWicket.getBO(AgendamentoFacade.class).reenviarSolicitacaoDevolvida(solicitacaoAgendamento.getCodigo(), observacao);
                voltarAction(target);
            }
        });
        form.add(new SubmitLink("btnVoltar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                voltarAction(target);
            }
        }));
        form.add(new AbstractAjaxButton("btnReenviarSolicitacao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                solicitacaoAgendamento.setObservacaoDevolucao(null);
                dlgReenviar.show(target, solicitacaoAgendamento);
            }
        });
        form.add(new AbstractAjaxButton("btnLancarOcorrencia") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                viewDlgLancarOcorrencia(target);
            }
        });
        form.add(new AbstractAjaxLink("btnDetalhes") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                setResponsePage(new DetalhesSolicitacaoPage(solicitacaoAgendamento.getCodigo()));
            }
        });
        form.add(new AbstractAjaxLink("btnCancelar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                dlgCancelamento.show(target, solicitacaoAgendamento);
            }
        });
        add(form);
        form.add(new AbstractAjaxLink("btnProntuario") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
                if (!usuarioLogado.isNivelAdminOrMaster() && usuarioLogado.getProfissional() == null) {
                    warn(bundle("msgDeveSerConfiguradoProfissionalParaUsuario"));
                } else {
                    setResponsePage(new ProntuarioPage(usuarioLogado, solicitacaoAgendamento, RegulacoesDevolvidasPage.class));
                }
            }
        });
        add(form);

        configuraContainers();
    }

    private void voltarAction(AjaxRequestTarget target) {
        setResponsePage(new ConsultaRegulacoesDevolvidasPage());
    }

    private void configuraContainers() {
        if (CollectionUtils.isEmpty(solicitacaoAgendamentoOcorrenciaList)) {
            containerTableOcorrencias.setVisible(false);
        } else {
            containerTableOcorrencias.setVisible(true);
        }
    }

    private void carregarSolicitacaoAgendamentro() {
        SolicitacaoAgendamento vo = LoadManager.getInstance(SolicitacaoAgendamento.class)
                .addProperties(new HQLProperties(SolicitacaoAgendamento.class).getProperties())
                .addProperties(new HQLProperties(UsuarioCadsus.class, SolicitacaoAgendamento.PROP_USUARIO_CADSUS).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamento.PROP_CODIGO, dto.getSolicitacaoAgendamento().getCodigo()))
                .start().getVO();
        solicitacaoAgendamento = vo;
        dto.setSolicitacaoAgendamento(vo);
    }

    private List<IColumn> getColumnsOcorrencias() {
        List<IColumn> columns = new ArrayList();

        SolicitacaoAgendamentoOcorrencia proxy = on(SolicitacaoAgendamentoOcorrencia.class);
        columns.add(createColumn(bundle("data_hora"), proxy.getDataHora()));
        columns.add(createColumn(bundle("usuario"), proxy.getUsuario().getNome()));
        columns.add(createColumn(bundle("tipo"), proxy.getDescricaoTipoOcorrencia()));
        columns.add(createColumn(bundle("ocorrencia"), proxy.getDescricao()));

        return columns;
    }

    private List<IColumn> getColumnsAgendamento() {
        List<IColumn> columns = new ArrayList();

//        columns.add(getCustomColumnAgendamentos());
        AgendaGradeAtendimentoHorario proxy = on(AgendaGradeAtendimentoHorario.class);
        columns.add(createColumn(bundle("dataAgendamento"), proxy.getDataHoraAgendamentoFormatado()));
        columns.add(createColumn(bundle("local"), proxy.getLocalAgendamento()));
        columns.add(createColumn(bundle("unidade_solicitante"), proxy.getEmpresaOrigem().getDescricao()));
        columns.add(createColumn(bundle("registro"), proxy.getDataCadastro()));


        return columns;
    }

    private List<IColumn> getColumnsSolicitacoes() {
        List<IColumn> columns = new ArrayList();

        SolicitacaoAgendamento proxy = on(SolicitacaoAgendamento.class);

        columns.add(getCustomColumnSolicitacoes());
        columns.add(createColumn(bundle("tipo_procedimento"), proxy.getTipoProcedimento().getDescricao()));
        columns.add(createColumn(bundle("data"), proxy.getDataSolicitacao()));
        columns.add(createColumn(bundle("situacao"), proxy.getDescricaoSituacao()));
        columns.add(createColumn(bundle("prioridade"), proxy.getDescricaoPrioridade()));

        return columns;
    }

    private List<IColumn> getColumnsSms() {
        List<IColumn> columns = new ArrayList();

        SmsControleIntegracao proxy = on(SmsControleIntegracao.class);
        columns.add(createColumn(bundle("dataEnvio"), proxy.getSmsMensagem().getDataCadastro()));
        columns.add(createColumn(bundle("mensagem"), proxy.getSmsMensagem().getMensagem()));
        columns.add(createColumn(bundle("tipoMensagem"), proxy.getDescricaoTipoMensagem()));
        columns.add(createColumn(bundle("situacao"), proxy.getSmsMensagem().getMensagemOperadora()));
        columns.add(createColumn(bundle("resposta"), proxy.getSmsMensagem().getUltimaResposta()));
        columns.add(createColumn(bundle("dataResposta"), proxy.getSmsMensagem().getDataUltimaResposta()));
        columns.add(createColumn(bundle("situacao"), proxy.getSmsMensagem().getDescricaoStatus()));


        return columns;
    }

    private ICollectionProvider<SolicitacaoAgendamentoOcorrencia, SolicitacaoAgendamento> getCollectionProviderOcorrencias() {
        if (this.collectionProvider == null) {
            this.collectionProvider = new CollectionProvider<SolicitacaoAgendamentoOcorrencia, SolicitacaoAgendamento>() {
                @Override
                public Collection getCollection(SolicitacaoAgendamento sa) throws DAOException, ValidacaoException {
                    return solicitacaoAgendamentoOcorrenciaList;
                }
            };
        }

        return this.collectionProvider;
    }

    private ICollectionProvider<AgendaGradeAtendimentoHorario, SolicitacaoAgendamento> getCollectionProviderAgendamento() {
        if (this.collectionProviderAgendamento == null) {
            this.collectionProviderAgendamento = new CollectionProvider<AgendaGradeAtendimentoHorario, SolicitacaoAgendamento>() {
                @Override
                public Collection getCollection(SolicitacaoAgendamento sa) throws DAOException, ValidacaoException {
                    return agendaGradeAtendimentoHorarioList;
                }
            };
        }

        return this.collectionProviderAgendamento;
    }


    private ICollectionProvider<SolicitacaoAgendamento, SolicitacaoAgendamento> getCollectionProviderSolicitacoes() {
        if (this.collectionProviderSolicitacao == null) {
            this.collectionProviderSolicitacao = new CollectionProvider<SolicitacaoAgendamento, SolicitacaoAgendamento>() {
                @Override
                public Collection getCollection(SolicitacaoAgendamento sa) throws DAOException, ValidacaoException {
                    return solicitacaoAgendamentoList;
                }
            };
        }

        return this.collectionProviderSolicitacao;
    }

    public List<AgendaGradeAtendimentoHorario> getAgendaGradeAtendimentoHorarioList() {
        agendaGradeAtendimentoHorarioList = LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
                .addProperties(new HQLProperties(AgendaGradeAtendimentoHorario.class).getProperties())
                .addProperties(new HQLProperties(GerenciadorArquivo.class, AgendaGradeAtendimentoHorario.PROP_GERENCIADOR_ARQUIVO).getProperties())
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO, AgendaGradeAtendimento.PROP_AGENDA_GRADE, AgendaGrade.PROP_AGENDA, Agenda.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_STATUS, BuilderQueryCustom.QueryParameter.NOT_IN, Arrays.asList(AgendaGradeAtendimentoHorario.STATUS_CANCELADO, AgendaGradeAtendimentoHorario.STATUS_REMANEJADO)))
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_SOLICITACAO_AGENDAMENTO, solicitacaoAgendamento))
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO_PRINCIPAL, BuilderQueryCustom.QueryParameter.IS_NULL))
                .addSorter(new QueryCustom.QueryCustomSorter(AgendaGradeAtendimentoHorario.PROP_DATA_AGENDAMENTO))
                .start().getList();
        return agendaGradeAtendimentoHorarioList;
    }

    private List<SolicitacaoAgendamentoOcorrencia> getSolicitacaoAgendamentoOcorrenciaList() {
        SolicitacaoAgendamentoOcorrencia proxy = on(SolicitacaoAgendamentoOcorrencia.class);
        solicitacaoAgendamentoOcorrenciaList = LoadManager.getInstance(SolicitacaoAgendamentoOcorrencia.class)
                .addProperty(path(proxy.getDataOcorrencia()))
                .addProperty(path(proxy.getUsuario().getNome()))
                .addProperty(path(proxy.getDescricao()))
                .addProperty(path(proxy.getTipoOcorrencia()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getSolicitacaoAgendamento()), solicitacaoAgendamento))
                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getDataOcorrencia()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .start().getList();
        return solicitacaoAgendamentoOcorrenciaList;
    }

    public List<SolicitacaoAgendamento> getSolicitacaoAgendamentoList() {
        solicitacaoAgendamentoList = LoadManager.getInstance(SolicitacaoAgendamento.class)
                .addProperties(new HQLProperties(SolicitacaoAgendamento.class).getProperties())
                .addProperties(new HQLProperties(TipoProcedimento.class, SolicitacaoAgendamento.PROP_TIPO_PROCEDIMENTO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamento.PROP_USUARIO_CADSUS, dto.getSolicitacaoAgendamento().getUsuarioCadsus()))
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamento.PROP_CODIGO, BuilderQueryCustom.QueryParameter.DIFERENTE, dto.getSolicitacaoAgendamento().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamento.PROP_STATUS, BuilderQueryCustom.QueryParameter.IN, SolicitacaoAgendamento.STATUS_PENDENTES))
                .addSorter(new QueryCustom.QueryCustomSorter(SolicitacaoAgendamento.PROP_DATA_AGENDAMENTO, BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();
        return solicitacaoAgendamentoList;
    }

    private void viewDlgLancarOcorrencia(AjaxRequestTarget target) {
        if (dlgLancarOcorrencia == null) {
            addModal(target, dlgLancarOcorrencia = new DlgLancarOcorrenciaContatoListaEspera(newModalId()) {
                @Override
                public void onSalvar(AjaxRequestTarget target, String ocorrencia, SolicitacaoAgendamento solicitacaoAgendamento) throws DAOException, ValidacaoException {
                    salvarOcorrencia(target, ocorrencia);
                }
            });
        }

        dlgLancarOcorrencia.show(target);
    }

    private void salvarOcorrencia(AjaxRequestTarget target, String ocorrencia) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(AgendamentoFacade.class).gerarOcorrenciaSolicitacaoAgendamento(SolicitacaoAgendamentoOcorrencia.TipoOcorrencia.SOLICITACAO, Bundle.getStringApplication("analise_devolucao_x", ocorrencia),
                solicitacaoAgendamento);
        solicitacaoAgendamento.setDataContatoDevolucao(DataUtil.getDataAtual());
        
        getSolicitacaoAgendamentoOcorrenciaList();
        target.add(tableOcorrencias);

        saveSolicitacaoAgendamento();
//        setResponsePage(new ConsultaRegulacoesDevolvidasPage());
    }

    private IColumn getCustomColumnSolicitacoes() {
        return new MultipleActionCustomColumn<SolicitacaoAgendamento>() {
            @Override
            public void customizeColumn(SolicitacaoAgendamento rowObject) {
                addAction(ActionType.CONSULTAR, rowObject,
                        new IModelAction<SolicitacaoAgendamento>() {
                            @Override
                            public void action(AjaxRequestTarget target, SolicitacaoAgendamento modelObject) throws ValidacaoException, DAOException {
                                setResponsePage(new DetalhesSolicitacaoPage(modelObject.getCodigo()));
                            }
                        }
                ).setVisible(!solicitacaoAgendamento.getCodigo().equals(rowObject.getCodigo()));
            }
        };
    }

    private void initDlgRecomendacoesAgenda(AjaxRequestTarget target) {
        if (dlgRecomendacoesAgenda == null) {
            addModal(target, dlgRecomendacoesAgenda = new DlgRecomendacoesAgenda(newModalId()) {
                @Override
                public DataReport onImprimir() throws ReportException {
                    return imprimirComprovanteAgendamento();
                }

                @Override
                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                }
            });
        }

        dlgRecomendacoesAgenda.show(target, agendaGradeAtendimentoHorarioList, true);
    }

    private DataReport imprimirComprovanteAgendamento() throws ReportException {
        if (CollectionUtils.isNotNullEmpty(agendaGradeAtendimentoHorarioList)) {
            RelatorioImprimirComprovanteAgendamentoDTOParam reportParam = new RelatorioImprimirComprovanteAgendamentoDTOParam();
            reportParam.setAgendaGradeAtendimentoHorarioList(agendaGradeAtendimentoHorarioList);

            return BOFactoryWicket.getBO(AtendimentoReportFacade.class).relatorioImprimirComprovanteAgendamentoSemSolicitacao(reportParam);
        }
        return null;
    }

    private void saveSolicitacaoAgendamento() throws ValidacaoException, DAOException {
        solicitacaoAgendamento = BOFactoryWicket.save(solicitacaoAgendamento);
    }

    private void validarReenvioSolicitacao(SolicitacaoAgendamento solicitacaoAgendamento) throws ValidacaoException, DAOException {
        SolicitacaoAgendamento solicitacao = BOFactory.getBO(SolicitacaoAgendamentoFacade.class).consultar(solicitacaoAgendamento.getCodigo());
        new AplicarValidacaoStatusSolicitacao(new ValidarReenvioSolicitacao()).validar(solicitacao);
    }
}
