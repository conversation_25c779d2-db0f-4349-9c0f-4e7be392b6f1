package br.com.celk.view.agenda.tipoatendimentoagenda.customize;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.agendamento.TipoAtendimentoAgenda;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaTipoAtendimentoAgenda extends CustomizeConsultaAdapter {

    @Override
    public void consultaCustomizeFilterProperties(Map<String, BuilderQueryCustom.QueryParameter> filterProperties) {
        filterProperties.put(Bundle.getStringApplication("rotulo_descricao"), new QueryCustom.QueryCustomParameter(TipoAtendimentoAgenda.PROP_DESCRICAO));
        filterProperties.put(Bundle.getStringApplication("rotulo_codigo"), new QueryCustom.QueryCustomParameter(TipoAtendimentoAgenda.PROP_CODIGO));
    }

    @Override
    public void consultaCustomizeViewProperties(Map<String, String> properties) {
        properties.put(Bundle.getStringApplication("rotulo_codigo"), TipoAtendimentoAgenda.PROP_CODIGO);
        properties.put(Bundle.getStringApplication("rotulo_descricao"), TipoAtendimentoAgenda.PROP_DESCRICAO);
    }

    @Override
    public Class getClassConsulta() {
        return TipoAtendimentoAgenda.class;
    }

    @Override
    public String[] getProperties() {
        return new HQLProperties(TipoAtendimentoAgenda.class).getProperties();
    }
}
