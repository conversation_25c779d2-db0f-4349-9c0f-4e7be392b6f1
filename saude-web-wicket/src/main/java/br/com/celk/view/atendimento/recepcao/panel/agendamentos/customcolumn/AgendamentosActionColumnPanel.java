package br.com.celk.view.atendimento.recepcao.panel.agendamentos.customcolumn;

import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoHorarioDTO;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.panel.Panel;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public abstract class AgendamentosActionColumnPanel extends Panel {

    private AbstractAjaxLink btnCancelar;
    private AbstractAjaxLink btnRemanejar;
    private List<Long> empresasUsuarioList;

    public AgendamentosActionColumnPanel(String id, AgendaGradeAtendimentoHorarioDTO rowObject) {
        super(id);
        init(rowObject);
    }

    private void init(final AgendaGradeAtendimentoHorarioDTO rowObject) {
        final boolean isAgendado = rowObject.getAgendaGradeAtendimentoHorario().getStatus().equals(AgendaGradeAtendimentoHorario.STATUS_AGENDADO);

        add(btnCancelar = new AbstractAjaxLink("btnCancelar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onCancelar(target, rowObject);
            }

            @Override
            public boolean isEnabled() {
                return isAgendado;
            }
        });
        
        add(btnRemanejar = new AbstractAjaxLink("btnRemanejar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onRemanejar(target, rowObject);
            }

            @Override
            public boolean isEnabled() {
                return isAgendado && rowObject.getAgendaGradeAtendimentoHorario().getAgendaGradeAtendimento() != null
                        && validarEstabelecimentoExecutante(rowObject.getAgendaGradeAtendimentoHorario().getLocalAgendamento())
                        && Data.adjustRangeHour(rowObject.getAgendaGradeAtendimentoHorario().getDataAgendamento()).getDataInicial()
                                .compareTo(Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial()) >= 0;
            }
        });
        
        btnCancelar.add(new AttributeModifier("title", BundleManager.getString("cancelar")));
        btnRemanejar.add(new AttributeModifier("title", BundleManager.getString("remanejar")));
    }

    public abstract void onCancelar(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO rowObject) throws ValidacaoException, DAOException;
    public abstract void onRemanejar(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO rowObject) throws ValidacaoException, DAOException;

    public List<Long> getEmpresasUsuarioList() {
        if(CollectionUtils.isEmpty(empresasUsuarioList)){
            try {
                empresasUsuarioList = BOFactoryWicket.getBO(UsuarioFacade.class).getEmpresasUsuario(SessaoAplicacaoImp.getInstance().<Usuario>getUsuario());
            } catch (DAOException | ValidacaoException ex) {
                Logger.getLogger(AgendamentosActionColumnPanel.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return CollectionUtils.isNotNullEmpty(empresasUsuarioList) ? empresasUsuarioList : new ArrayList<Long>();
    }
    
    private boolean validarEstabelecimentoExecutante(Empresa estabelecimentoExecutante) {
        return !(!SessaoAplicacaoImp.getInstance().<Usuario>getUsuario().isNivelAdminOrMaster() 
                && (estabelecimentoExecutante == null || !getEmpresasUsuarioList().contains(estabelecimentoExecutante.getCodigo())));
    }
}
