package br.com.celk.view.basico.ppigrupo.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.basico.ppigrupo.autocomplete.restricaocontainer.RestricaoContainerPpiGrupo;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.ppigrupo.QueryConsultaPpiGrupoDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.geral.PpiGrupo;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaPpiGrupo extends AutoCompleteConsulta<PpiGrupo> { 

    public AutoCompleteConsultaPpiGrupo(String id) {
        super(id);
    }

    public AutoCompleteConsultaPpiGrupo(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaPpiGrupo(String id, IModel<PpiGrupo> model) {
        super(id, model);
    }

    public AutoCompleteConsultaPpiGrupo(String id, IModel<PpiGrupo> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {

            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(PpiGrupo.class);
                
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), VOUtils.montarPath(PpiGrupo.PROP_CODIGO)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(PpiGrupo.PROP_DESCRICAO)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerPpiGrupo(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<PpiGrupo, QueryConsultaPpiGrupoDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaPpiGrupoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(BasicoFacade.class).consultarPpiGrupo(dataPaging);
                    }

                    @Override
                    public QueryConsultaPpiGrupoDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaPpiGrupoDTOParam param = new QueryConsultaPpiGrupoDTOParam();
                        param.setKeyword(searchCriteria);
                        return param;
                    }
                    
                    @Override
                    public void customizeParam(QueryConsultaPpiGrupoDTOParam param) {
                        param.setPropSort(getSort().getProperty());
                        param.setAscending(getSort().isAscending());
                    }
                    
                    @Override
                    public SortParam getDefaultSort() {
                        return new SortParam(VOUtils.montarPath(PpiGrupo.PROP_DESCRICAO), true);
                    }
                };
            }
            
            @Override
            public Class getReferenceClass() {
                return PpiGrupo.class;
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("grupoPpi");
    }
}
