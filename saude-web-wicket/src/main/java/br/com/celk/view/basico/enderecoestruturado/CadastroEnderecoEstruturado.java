package br.com.celk.view.basico.enderecoestruturado;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.cepField.CepWsField;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.Util;
import br.com.celk.view.basico.bairro.autocomplete.AutoCompleteConsultaBairro;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.enderecoestruturadodistrito.autocomplete.AutoCompleteConsultaEnderecoEstruturadoDistrito;
import br.com.celk.view.basico.enderecoestruturadologradouro.autocomplete.AutoCompleteConsultaEnderecoEstruturadoLogradouro;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.basico.dto.CepWSDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.enderecoestruturado.EnderecoEstruturado;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 * <AUTHOR>
 */
@Private
public class CadastroEnderecoEstruturado extends CadastroPage<EnderecoEstruturado> {

    private Form<EnderecoEstruturado> form;
    private AutoCompleteConsultaCidade autoCompleteConsultaCidade;
    private AutoCompleteConsultaBairro autoCompleteConsultaBairro;
    private CepWsField cepWsField;
    private RequiredInputField txtLogradouro;
    private InputField txtNumero;
    private InputField txtComplemento;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaEnderecoEstruturadoLogradouro autoCompleteConsultaEnderecoEstruturadoLogradouro;
    private AutoCompleteConsultaEnderecoEstruturadoDistrito autoCompleteConsultaEnderecoEstruturadoDistrito;
    private Cidade cidade;
    private Cidade cidadeCep;
    private Long limpaCepInvalido;
    private DropDown<EquipeArea> cbxEquipeArea;
    private DropDown<EquipeMicroArea> cbxEquipeMicroArea;
    private EquipeArea equipeArea;
    //    private InputField txtAgenteComunitario;
    private InputField txtUnidadeArea;
    private EnderecoEstruturado enderecoEstruturado;
    private CheckBoxLongValue cbxFlagEnderecoSN;
    private Long enderecoSN;
    private boolean utilizaEnderecoEstruturado;

    public CadastroEnderecoEstruturado(EnderecoEstruturado object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroEnderecoEstruturado(EnderecoEstruturado object) {
        super(object);
    }

    public CadastroEnderecoEstruturado() {
    }

    @Override
    public void init(Form form) {
        this.enderecoEstruturado = this.getForm().getModel().getObject();
        try {
            utilizaEnderecoEstruturado = RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("utilizaEnderecoEstruturado"));
            cidade = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("MunicipioComEnderecoEstruturado");
        } catch (ValidacaoRuntimeException | DAOException e) {
//            Loggable.log.error(e.getMessage(), e);
            warn(e.getMessage());
        }


        form.add(cepWsField = new CepWsField(VOUtils.montarPath(EnderecoEstruturado.PROP_CEP), new PropertyModel(form.getModel(), VOUtils.montarPath(EnderecoEstruturado.PROP_CEP))) {
            @Override
            public void load(AjaxRequestTarget target, CepWSDTO cepWSDTO) {
                cidadeCep = cepWSDTO.getCidade();
                if (cepWSDTO.getCidade() != null && !cepWSDTO.getCidade().getCodigo().equals(cidade.getCodigo())) {
                    CadastroEnderecoEstruturado.this.warn(target, BundleManager.getString("msgCepXNaoPerteceCidadeX", cepWSDTO.getCep(), cidade.getDescricao()));
                }
            }

            @Override
            public void unload(AjaxRequestTarget target) {
            }
        });
        cepWsField.setRequired(true);


        form.add(autoCompleteConsultaCidade = new AutoCompleteConsultaCidade("cidade", new PropertyModel(this, "cidade")));
        form.add(autoCompleteConsultaEnderecoEstruturadoDistrito = new AutoCompleteConsultaEnderecoEstruturadoDistrito(VOUtils.montarPath(EnderecoEstruturado.PROP_ENDERECO_ESTRUTURADO_DISTRITO), true));
        autoCompleteConsultaEnderecoEstruturadoDistrito.setLabel(new Model<String>(bundle("distrito")));
        form.add(autoCompleteConsultaBairro = new AutoCompleteConsultaBairro(VOUtils.montarPath(EnderecoEstruturado.PROP_BAIRRO), true));
        autoCompleteConsultaBairro.setCidade(cidade);
        form.add(autoCompleteConsultaEnderecoEstruturadoLogradouro = new AutoCompleteConsultaEnderecoEstruturadoLogradouro(VOUtils.montarPath(EnderecoEstruturado.PROP_ENDERECO_ESTRUTURADO_LOGRADOURO), true));
        autoCompleteConsultaEnderecoEstruturadoLogradouro.setLabel(new Model<String>(bundle("logradouro")));
        form.add(txtNumero = new InputField(VOUtils.montarPath(EnderecoEstruturado.PROP_NUMERO)));
        txtNumero.setLabel(new Model<String>(bundle("numero"))).setRequired(true);
        txtNumero.addRequiredClass();
        txtNumero.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONKEYUP) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                String novaString = Util.mantemSomenteNumeros(txtNumero.getValue());
                if (RepositoryComponentDefault.NAO_LONG.equals(enderecoSN)
                    && !txtNumero.getValue().equals(novaString)){
                        txtNumero.setComponentValue(novaString);
                        target.add(txtNumero);
                }
            }
        });

        form.add(cbxFlagEnderecoSN = new CheckBoxLongValue("enderecoSN", RepositoryComponentDefault.SIM_LONG, new PropertyModel(this, "enderecoSN")));

        cbxFlagEnderecoSN.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                if (RepositoryComponentDefault.SIM_LONG.equals(enderecoSN)) {
                    txtNumero.setEnabled(false);
                    txtNumero.limpar(art);
                    txtNumero.setComponentValue("S/N");

                } else {
                    txtNumero.limpar(art);
                    txtNumero.setEnabled(true);
                    txtNumero.setComponentValue("");
                }
                art.add(txtNumero);
            }
        });
        enderecoSN = RepositoryComponentDefault.NAO_LONG;

        form.add(cbxEquipeArea = new DropDown("equipeArea", new PropertyModel(this, "equipeArea")));
        form.add(cbxEquipeMicroArea = new DropDown(VOUtils.montarPath(EnderecoEstruturado.PROP_EQUIPE_MICRO_AREA)));
        cbxEquipeMicroArea.setLabel(new Model<String>(bundle("equipeMicroArea")));
        form.add(txtUnidadeArea = new InputField("unidadeArea", new Model()));
        txtUnidadeArea.setEnabled(false);

        autoCompleteConsultaCidade.setEnabled(false);
        autoCompleteConsultaCidade.add(new ConsultaListener<Cidade>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Cidade cidade) {
                autoCompleteConsultaBairro.setCidade(cidade);
                target.add(autoCompleteConsultaBairro);
            }
        });
        autoCompleteConsultaCidade.add(new RemoveListener<Cidade>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Cidade cidade) {
                autoCompleteConsultaBairro.setCidade(null);
                target.add(autoCompleteConsultaBairro);
                autoCompleteConsultaBairro.limpar(target);
            }
        });

        cbxEquipeArea.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                cbxEquipeMicroArea.limpar(target);
                atualizarMicroAreas(target, cbxEquipeArea.getComponentValue());
                if (cbxEquipeArea.getModelObject() != null
                        && cbxEquipeMicroArea.getModelObject() != null) {
                    procurarAgenteComunitario(target, cbxEquipeArea.getModelObject(), cbxEquipeMicroArea.getModelObject());
                } else {
                    txtUnidadeArea.limpar(target);
                }
            }
        });

        cbxEquipeMicroArea.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (cbxEquipeArea.getModelObject() != null
                        && cbxEquipeMicroArea.getModelObject() != null) {
                    procurarAgenteComunitario(target, cbxEquipeArea.getModelObject(), cbxEquipeMicroArea.getModelObject());
                }
            }
        });

        if (cidade != null) {
            loadAreas(cidade);
        }
        if (enderecoEstruturado != null && enderecoEstruturado.getCodigo() != null) {
            carregarMicroAreaSalva(enderecoEstruturado);
        }

        if (enderecoEstruturado.getCodigo() == null) {
            enderecoSN = RepositoryComponentDefault.NAO_LONG;
            txtNumero.setRequired(true);
            txtNumero.addRequiredClass();
            txtNumero.setEnabled(true);
            this.getForm().getModel().getObject().setNumero(null);
        } else if (enderecoEstruturado.getNumero() == null || "0".equals(enderecoEstruturado.getNumero()) || "S/N".equals(enderecoEstruturado.getNumero()) ){
            enderecoSN = RepositoryComponentDefault.SIM_LONG;
            txtNumero.setRequired(false);
            txtNumero.removeRequiredClass();
            txtNumero.setEnabled(false);
        }

        if (utilizaEnderecoEstruturado && cidade == null) {
            warn("Defina o parametro MunicipioComEnderecoEstruturado");
            cepWsField.setEnabled(false);
            autoCompleteConsultaEnderecoEstruturadoDistrito.setEnabled(false);
            autoCompleteConsultaBairro.setEnabled(false);
            autoCompleteConsultaEnderecoEstruturadoLogradouro.setEnabled(false);
            txtNumero.setEnabled(false);
            cbxEquipeArea.setEnabled(false);
            cbxFlagEnderecoSN.setEnabled(false);
            cbxEquipeMicroArea.setEnabled(false);
            getBtnSalvar().setEnabled(false);
        }
    }

    @Override
    public Class<EnderecoEstruturado> getReferenceClass() {
        return EnderecoEstruturado.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaEnderecoEstruturado.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("cadastroEnderecoEstruturado");
    }

    @Override
    public Object salvar(EnderecoEstruturado object) throws DAOException, ValidacaoException {
        if (cidadeCep != null && !cidadeCep.getCodigo().equals(cidade.getCodigo())) {
            throw new ValidacaoException(BundleManager.getString("msgCepXNaoPerteceCidadeX", object.getCepFormatado(), cidade.getDescricao()));
        }
        return super.salvar(object);
    }

    private void loadAreas(Cidade object) {
        cbxEquipeArea.removeAllChoices();
        if (object != null) {
            List<EquipeArea> equipeAreas = LoadManager.getInstance(EquipeArea.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeArea.PROP_CIDADE), object))
                    .addSorter(new QueryCustom.QueryCustomSorter(EquipeArea.PROP_DESCRICAO))
                    .start().getList();

            cbxEquipeArea.addChoice(null, "");
            if (CollectionUtils.isNotNullEmpty(equipeAreas)) {
                for (EquipeArea equipeArea : equipeAreas) {
                    cbxEquipeArea.addChoice(equipeArea, equipeArea.getDescricao());
                }
            }
        }
    }

    private void atualizarMicroAreas(AjaxRequestTarget target, EquipeArea equipeArea) {
        cbxEquipeMicroArea.removeAllChoices();
        cbxEquipeMicroArea.addChoice(null, "");
        if (equipeArea != null) {
            List<EquipeMicroArea> microAreas = LoadManager.getInstance(EquipeMicroArea.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(EquipeMicroArea.PROP_EQUIPE_AREA, equipeArea))
                    .addParameter(new QueryCustom.QueryCustomParameter(EquipeMicroArea.PROP_STATUS, RepositoryComponentDefault.SIM_LONG))
                    .addSorter(new QueryCustom.QueryCustomSorter(EquipeMicroArea.PROP_MICRO_AREA))
                    .start().getList();
            for (EquipeMicroArea equipeMicroArea : microAreas) {
                cbxEquipeMicroArea.addChoice(equipeMicroArea, Coalesce.asString(equipeMicroArea.getMicroArea()));
            }
        }
        if (target != null) {
            target.add(cbxEquipeMicroArea);
        }
    }

    private void procurarAgenteComunitario(AjaxRequestTarget target, EquipeArea equipeArea, EquipeMicroArea
            equipeMicroArea) {
        List<EquipeProfissional> lista = LoadManager.getInstance(EquipeProfissional.class)
                .addProperties(new HQLProperties(Profissional.class, VOUtils.montarPath(EquipeProfissional.PROP_PROFISSIONAL)).getProperties())
                .addProperties(new HQLProperties(Empresa.class, VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA), equipeArea))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE_MICRO_AREA), equipeMicroArea))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_ATIVO), RepositoryComponentDefault.SIM))
                .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_STATUS, EquipeProfissional.STATUS_ATIVO))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(lista) && lista.size() == 1) {
            txtUnidadeArea.setModelObject(lista.get(0).getEquipe().getEmpresa().getDescricao());

            if (target != null) {
                target.add(txtUnidadeArea);
            }
        } else {
            if (target != null) {
                txtUnidadeArea.limpar(target);
            }
        }
    }

    private void carregarMicroAreaSalva(EnderecoEstruturado enderecoEstruturado) {
        if (enderecoEstruturado.getEquipeMicroArea() != null) {
            equipeArea = enderecoEstruturado.getEquipeMicroArea().getEquipeArea();
            atualizarMicroAreas(null, enderecoEstruturado.getEquipeMicroArea().getEquipeArea());
            procurarAgenteComunitario(null, enderecoEstruturado.getEquipeMicroArea().getEquipeArea(), enderecoEstruturado.getEquipeMicroArea());
        }
    }
}
