package br.com.celk.view.vigilancia.taxas;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.util.ImagemAvatarHelper;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.vo.vigilancia.cva.proprietarioresponsavel.CvaProprietarioResponsavel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.TextArea;
import org.apache.wicket.model.CompoundPropertyModel;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

@Private
public class DetalhesTaxaPage extends BasePage {
    
    private CvaProprietarioResponsavel cvaProprietarioResponsavel;

    public DetalhesTaxaPage(CvaProprietarioResponsavel cvaProprietarioResponsavel) {
        this.cvaProprietarioResponsavel = cvaProprietarioResponsavel;
        init();
    }

    public void init() {
        CvaProprietarioResponsavel proxy = on(CvaProprietarioResponsavel.class);
        Form form = new Form("form", new CompoundPropertyModel(cvaProprietarioResponsavel));

        form.add(new DisabledInputField(path(proxy.getUsuarioCadastro())));
        form.add(new DisabledInputField(path(proxy.getDataCadastro())));
        
        form.add(new DisabledInputField(path(proxy.getProprietarioResponsavel())));
        form.add(new DisabledInputField(path(proxy.getDataNascimento())));
        form.add(new DisabledInputField(path(proxy.getSexoFormatado())));
        form.add(new DisabledInputField(path(proxy.getCpf())));
        form.add(new DisabledInputField(path(proxy.getRg())));
        form.add(new DisabledInputField(path(proxy.getProfissao())));
        
        form.add(new DisabledInputField(path(proxy.getEnderecoFormatadoEstrangeiro())));
        form.add(new DisabledInputField(path(proxy.getComplementoLogradouro())));
        form.add(new CheckBoxLongValue(path(proxy.getFlagEstrangeiro())).setEnabled(false));
        
        form.add(new DisabledInputField(path(proxy.getTelefone1Formatado())));
        form.add(new DisabledInputField(path(proxy.getTelefone2Formatado())));
        form.add(new DisabledInputField(path(proxy.getTelefone3Formatado())));
        form.add(new DisabledInputField(path(proxy.getEmail())));
        form.add(new TextArea(path(proxy.getObservacao())).setEnabled(false));

        form.add(ImagemAvatarHelper.carregarAvatarByCaminho(cvaProprietarioResponsavel.getFoto() == null ? "" : cvaProprietarioResponsavel.getFoto().getCaminho(), false, cvaProprietarioResponsavel.getSexo()));
        
        form.add(new VoltarButton("btnVoltar"));
        add(form);

    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("detalhesDaTaxa");
    }

}
