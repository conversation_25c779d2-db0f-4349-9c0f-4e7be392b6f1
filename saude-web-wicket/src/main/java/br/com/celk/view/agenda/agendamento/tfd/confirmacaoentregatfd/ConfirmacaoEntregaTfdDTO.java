package br.com.celk.view.agenda.agendamento.tfd.confirmacaoentregatfd;

import br.com.ksisolucoes.tfd.dto.SalvarPedidoTfdPassageiroDTO;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.frota.viagem.TipoTransporteViagem;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ConfirmacaoEntregaTfdDTO implements Serializable {

    private LaudoTfd laudoTfd;
    private List<SalvarPedidoTfdPassageiroDTO> salvarPedidoTfdPassageiroDTOs;
    private TipoTransporteViagem tipoTransporteViagem;

    public LaudoTfd getLaudoTfd() {
        return laudoTfd;
    }

    public void setLaudoTfd(LaudoTfd laudoTfd) {
        this.laudoTfd = laudoTfd;
    }

    public List<SalvarPedidoTfdPassageiroDTO> getSalvarPedidoTfdPassageiroDTOs() {
        return salvarPedidoTfdPassageiroDTOs;
    }

    public void setSalvarPedidoTfdPassageiroDTOs(List<SalvarPedidoTfdPassageiroDTO> salvarPedidoTfdPassageiroDTOs) {
        this.salvarPedidoTfdPassageiroDTOs = salvarPedidoTfdPassageiroDTOs;
    }

    public TipoTransporteViagem getTipoTransporteViagem() {
        return tipoTransporteViagem;
    }

    public void setTipoTransporteViagem(TipoTransporteViagem tipoTransporteViagem) {
        this.tipoTransporteViagem = tipoTransporteViagem;
    }

}
