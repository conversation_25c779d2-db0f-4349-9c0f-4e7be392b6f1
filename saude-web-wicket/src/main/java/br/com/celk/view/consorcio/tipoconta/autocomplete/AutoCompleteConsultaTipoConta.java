package br.com.celk.view.consorcio.tipoconta.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.consorcio.tipoconta.autocomplete.restricaocontainer.RestricaoContainerDominioTipoConta;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.QueryConsultaDominioTipoContaDTOParam;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.DominioTipoConta;
import br.com.ksisolucoes.vo.consorcio.TipoConta;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaTipoConta extends AutoCompleteConsulta<TipoConta> {

    private boolean validarVisivelConsorciado = false;
    
    public AutoCompleteConsultaTipoConta(String id) {
        super(id);
    }

    public AutoCompleteConsultaTipoConta(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaTipoConta(String id, IModel<TipoConta> model) {
        super(id, model);
    }

    public AutoCompleteConsultaTipoConta(String id, IModel<TipoConta> model, boolean required) {
        super(id, model, required);
    }
    
    @Override
    public int getMinDialogHeight() {
        return 400;
    }

    @Override
    public int getMinDialogWidth() {
        return 600;
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {

            @Override
            public Class<TipoConta> getReferenceClass() {
                return TipoConta.class;
            }

            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(DominioTipoConta.class);
                
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("referencia"), VOUtils.montarPath(DominioTipoConta.PROP_REFERENCIA)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(DominioTipoConta.PROP_DESCRICAO_TIPO_CONTA)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerDominioTipoConta(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<TipoConta, QueryConsultaDominioTipoContaDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaDominioTipoContaDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(ConsorcioFacade.class).consultarDominioTipoConta(dataPaging);
                    }

                    @Override
                    public QueryConsultaDominioTipoContaDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaDominioTipoContaDTOParam param = new QueryConsultaDominioTipoContaDTOParam();
                        param.setKeyword(searchCriteria);
                        return param;
                    }

                    @Override
                    public SortParam getDefaultSort() {
                        return new SortParam(DominioTipoConta.PROP_DESCRICAO_TIPO_CONTA, true);
                    }

                    @Override
                    public void customizeParam(QueryConsultaDominioTipoContaDTOParam param) {
                        param.setPropSort(getSort().getProperty());
                        param.setAscending(getSort().isAscending());
                        param.setValidarVisivelConsorciado(validarVisivelConsorciado);
                    }
                };
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("tiposConta");
    }

    public AutoCompleteConsultaTipoConta setValidarVisivelConsorciado(boolean validarVisivelConsorciado) {
        this.validarVisivelConsorciado = validarVisivelConsorciado;
        return this;
    }

}
