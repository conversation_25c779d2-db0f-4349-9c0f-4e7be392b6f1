package br.com.celk.view.unidadesaude.processos.regulacaosolicitacao.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.JustificativaPriorizacao;
import br.com.ksisolucoes.vo.prontuario.basico.ClassificacaoRisco;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.SubClassificacaoFilaEspera;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 * <AUTHOR>
 */
public abstract class DlgClassificarEnviarFilaRegulacao extends Window {

    private PnlClassificarEnviarFilaRegulacao pnlClassificarEnviarFilaRegulacao;

    public DlgClassificarEnviarFilaRegulacao(String id) {
        super(id);
        init();
    }

    private void init() {
        setInitialHeight(450);
        setInitialWidth(600);

        setResizable(true);

        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("classificarEnviarFila");
            }
        });

        setContent(pnlClassificarEnviarFilaRegulacao = new PnlClassificarEnviarFilaRegulacao(getContentId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, Long codigoSolicitacao, ClassificacaoRisco classificacaoRisco, String observacaoAutorizador, JustificativaPriorizacao justificativaPriorizacao, SubClassificacaoFilaEspera subClassificacaoFilaEspera) throws DAOException, ValidacaoException {
                DlgClassificarEnviarFilaRegulacao.this.onConfirmar(target, codigoSolicitacao, classificacaoRisco, observacaoAutorizador, justificativaPriorizacao, subClassificacaoFilaEspera);
                close(target);
            }

            @Override
            public void onConfirmarAgendar(AjaxRequestTarget target, Long codigoSolicitacao, ClassificacaoRisco classificacaoRisco, String observacaoAutorizador, SubClassificacaoFilaEspera subClassificacaoFilaEspera) throws DAOException, ValidacaoException {
                DlgClassificarEnviarFilaRegulacao.this.onConfirmarAgendar(target, codigoSolicitacao, classificacaoRisco, observacaoAutorizador, subClassificacaoFilaEspera);
                close(target);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                close(target);
            }
        });
    }

    public void limpar(AjaxRequestTarget target) {
        pnlClassificarEnviarFilaRegulacao.limpar(target);
    }

    public void show(AjaxRequestTarget target, SolicitacaoAgendamento solicitacaoAgendamento, boolean exibirbtnSalvarAgendar) {
        show(target);
        limpar(target);
        pnlClassificarEnviarFilaRegulacao.setSolicitacaoAgendamento(target, solicitacaoAgendamento, exibirbtnSalvarAgendar);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, Long codigoSolicitacao, ClassificacaoRisco classificacaoRisco, String observacaoAutorizador, JustificativaPriorizacao justificativaPriorizacao, SubClassificacaoFilaEspera subClassificacaoFilaEspera) throws DAOException, ValidacaoException;
    public abstract void onConfirmarAgendar(AjaxRequestTarget target, Long codigoSolicitacao, ClassificacaoRisco classificacaoRisco, String observacaoAutorizador, SubClassificacaoFilaEspera subClassificacaoFilaEspera) throws DAOException, ValidacaoException;

}
