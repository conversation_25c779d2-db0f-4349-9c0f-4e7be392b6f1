package br.com.celk.view.consorcio.licitacao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.longfield.DisabledLongField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.component.table.column.panel.RemoverActionColumnPanel;
import br.com.celk.component.validator.DataMaiorAtualValidator;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.fabricante.autocomplete.AutoCompleteConsultaFabricante;
import br.com.celk.view.basico.fabricante.dialog.DlgCadastroFabricante;
import br.com.celk.view.basico.pessoa.autocomplete.AutoCompleteConsultaPessoa;
import br.com.celk.view.consorcio.licitacao.columnpanel.PregaoLicitacaoItemColumnPanel;
import br.com.celk.view.consorcio.licitacaoitem.autocomplete.AutoCompleteConsultaProdutoLicitacaoItem;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.consorcio.Licitacao;
import br.com.ksisolucoes.vo.consorcio.LicitacaoItem;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import org.apache.wicket.Component;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;


/**
 *
 * <AUTHOR>
 */
@Private

public class LancamentoPregaoPage extends BasePage {

    private AutoCompleteConsultaPessoa autoCompleteConsultaPessoa;
    private AutoCompleteConsultaProdutoLicitacaoItem autoCompleteConsultaProduto;
    private InputField txtNumeroPregao;
    private InputField txtQuantidade;
    private InputField txtNrItemPregao;
    private InputField txtPrecoTotal;
    private Table<LicitacaoItem> tblProdutos;
    private Table<LicitacaoItem> tblProdutosLicitados;
    private CompoundPropertyModel<LicitacaoItem> modelItem;
    private DlgJustificarNaoCotacao dlgJustificarNaoCotacao;
    private DlgJustificarNaoCotacao dlgJustificarNaoCotacaoTodos;

    private Licitacao licitacao;
    private List<LicitacaoItem> produtos = new ArrayList<LicitacaoItem>();
    private List<LicitacaoItem> produtosLicitados = new ArrayList<LicitacaoItem>();

    private AutoCompleteConsultaFabricante autoCompleteConsultaFabricante;
    private DlgCadastroFabricante dlgCadastroFabricante;
    private AbstractAjaxLink btnCadFabricante;
    
    public LancamentoPregaoPage(Licitacao licitacao) {
        this.licitacao = licitacao;
        init();
    }

    private void init() {
        carregarItens();
        Form form = new Form("form", new CompoundPropertyModel(licitacao));
        form.add(txtNumeroPregao = new InputField(Licitacao.PROP_NUMERO_PREGAO));
        form.add(new DateChooser(Licitacao.PROP_DATA_VALIDADE).add(new DataMaiorAtualValidator()));
        
        WebMarkupContainer containerItem = new WebMarkupContainer("containerItem", modelItem = new CompoundPropertyModel<LicitacaoItem>(new LicitacaoItem()));
        
        containerItem.add(tblProdutos = new Table("tblProdutos", getColumnsProdutos(), getCollectionProdutos()));
        tblProdutos.setScrollY("200px");
        tblProdutos.populate();
        
        containerItem.add(autoCompleteConsultaPessoa = new AutoCompleteConsultaPessoa(LicitacaoItem.PROP_PESSOA));
        containerItem.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProdutoLicitacaoItem(LicitacaoItem.PROP_PRODUTO){

            @Override
            public String[] getPropertiesLoad() {
                return VOUtils.mergeProperties(new HQLProperties(Produto.class).getProperties(),
                        new String[]{VOUtils.montarPath(Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE)});
            }
            
        }.setLicitacao(licitacao));
        containerItem.add(txtPrecoTotal = new DoubleField(LicitacaoItem.PROP_PRECO_TOTAL));
        containerItem.add(txtQuantidade = new DisabledLongField(LicitacaoItem.PROP_QUANTIDADE_LICITADA));
        containerItem.add(txtNrItemPregao = new InputField(LicitacaoItem.PROP_NUMERO_ITEM_PREGAO));
        containerItem.add(new AbstractAjaxButton("btnAdicionar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });

        containerItem.add(autoCompleteConsultaFabricante = new AutoCompleteConsultaFabricante(LicitacaoItem.PROP_FABRICANTE));
        containerItem.add(btnCadFabricante = new AbstractAjaxLink("btnCadFabricante") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                initDlgCadastroFabricante(target);
            }
        });

        containerItem.add(new AbstractAjaxButton("btnNaoCotado") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                dlgJustificarNaoCotacao.setLicitacaoItem(modelItem.getObject());
                dlgJustificarNaoCotacao.show(target);
            }
        });
        containerItem.add(new AbstractAjaxButton("btnTodosNaoCotado") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                dlgJustificarNaoCotacaoTodos.show(target);
            }
        });
        
        form.add(containerItem);
        
        form.add(tblProdutosLicitados = new Table("tblProdutosLicitados", getColumnsProdutosLicitados(), getCollectionProdutosLicitados()));
        tblProdutosLicitados.populate();
        
        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {

            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }));
        
        add(form);
        
        addModal(dlgJustificarNaoCotacao = new DlgJustificarNaoCotacao(newModalId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, LicitacaoItem licitacaoItem, String justificativa) throws ValidacaoException, DAOException {
                definirNaoCotado(target, licitacaoItem, justificativa);
            }
        });
        addModal(dlgJustificarNaoCotacaoTodos = new DlgJustificarNaoCotacao(newModalId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, LicitacaoItem licitacaoItem, String justificativa) throws ValidacaoException, DAOException {
                definirTodosNaoCotado(target, justificativa);
            }
        });
        
        autoCompleteConsultaProduto.add(new ConsultaListener<Produto>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Produto object) {
                if (object!=null) {
                        LicitacaoItem itemPersistido = LoadManager.getInstance(LicitacaoItem.class)
                            .addProperties(new HQLProperties(LicitacaoItem.class).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LicitacaoItem.PROP_LICITACAO), licitacao))
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO), object))
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LicitacaoItem.PROP_STATUS), LicitacaoItem.StatusLicitacaoItem.ABERTO.value()))
                            .start().getVO();
                        modelItem.getObject().setQuantidadeLicitada(itemPersistido.getQuantidadeLicitada());
                        target.add(txtQuantidade);
                }
            }
        });
    }

    private void initDlgCadastroFabricante(AjaxRequestTarget target){
        if(dlgCadastroFabricante == null){
            addModal(target, dlgCadastroFabricante = new DlgCadastroFabricante(newModalId()) {
                @Override
                public void onSalvar(AjaxRequestTarget target, Fabricante fabricante) throws ValidacaoException, DAOException {
                    autoCompleteConsultaFabricante.limpar(target);
                    autoCompleteConsultaFabricante.setComponentValue(fabricante);
                    target.add(autoCompleteConsultaFabricante);
                }
            });
        }
        dlgCadastroFabricante.showDlg(target);
    }
    
    private List<IColumn> getColumnsProdutos(){
        List<IColumn> columns = new ArrayList<IColumn>();
        
        ColumnFactory columnFactory = new ColumnFactory(LicitacaoItem.class);
        
        columns.add(getCustomColumnProdutos());
        columns.add(columnFactory.createColumn(BundleManager.getString("descricao"), VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_DESCRICAO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("un"), VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE)));
        columns.add(columnFactory.createColumn(BundleManager.getString("quantidade"), VOUtils.montarPath(LicitacaoItem.PROP_QUANTIDADE_LICITADA)));
        
        return columns;
    }
    
    private CustomColumn getCustomColumnProdutos(){
        return new CustomColumn<LicitacaoItem>() {

            @Override
            public Component getComponent(String componentId, final LicitacaoItem rowObject) {
                return new PregaoLicitacaoItemColumnPanel(componentId) {

                    @Override
                    public void onAdicionar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setarInformacoes(target, rowObject);
                    }

                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        dlgJustificarNaoCotacao.setLicitacaoItem(rowObject);
                        dlgJustificarNaoCotacao.show(target);
                    }

                };
            }
        };
    }

    private ICollectionProvider getCollectionProdutos(){
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return produtos;
            }
        };
    }
    
    private List<IColumn> getColumnsProdutosLicitados(){
        List<IColumn> columns = new ArrayList<IColumn>();
        
        ColumnFactory columnFactory = new ColumnFactory(LicitacaoItem.class);
        
        columns.add(getCustomColumnProdutosLicitados());
        columns.add(columnFactory.createColumn(BundleManager.getString("descricao"), VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_DESCRICAO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("un"), VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE)));
        columns.add(columnFactory.createColumn(BundleManager.getString("fornecedor"), VOUtils.montarPath(LicitacaoItem.PROP_PESSOA, Pessoa.PROP_DESCRICAO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("fabricanteMarca"), VOUtils.montarPath(LicitacaoItem.PROP_FABRICANTE, Fabricante.PROP_DESCRICAO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("quantidade"), VOUtils.montarPath(LicitacaoItem.PROP_QUANTIDADE_LICITADA)));
        columns.add(new DoubleColumn(BundleManager.getString("precoUn"), VOUtils.montarPath(LicitacaoItem.PROP_PRECO_UNITARIO)).setCasasDecimais(4));
        columns.add(columnFactory.createColumn(BundleManager.getString("total"), VOUtils.montarPath(LicitacaoItem.PROP_PRECO_TOTAL)));
        
        return columns;
    }
    
    private CustomColumn getCustomColumnProdutosLicitados(){
        return new CustomColumn<LicitacaoItem>() {

            @Override
            public Component getComponent(String componentId, final LicitacaoItem rowObject) {
                return new RemoverActionColumnPanel(componentId) {

                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerItemLicitado(target, rowObject);
                    }

                };
            }
        };
    }

    private ICollectionProvider getCollectionProdutosLicitados(){
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return produtosLicitados;
            }
        };
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("lancamentoPregao");
    }
    
    private void carregarItens(){
            produtos = LoadManager.getInstance(LicitacaoItem.class)
                    .addProperties(new HQLProperties(LicitacaoItem.class).getProperties())
                    .addProperty(VOUtils.montarPath(LicitacaoItem.PROP_FABRICANTE, Fabricante.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(LicitacaoItem.PROP_FABRICANTE, Fabricante.PROP_DESCRICAO))
                    .addProperty(VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE))
                    .addProperty(VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                    .addProperty(VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_FLAG_CONTROLA_GRUPO_ESTOQUE))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LicitacaoItem.PROP_LICITACAO), licitacao))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LicitacaoItem.PROP_STATUS), LicitacaoItem.StatusLicitacaoItem.ABERTO.value()))
                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_DESCRICAO)))
                    .start().getList();
            produtosLicitados = LoadManager.getInstance(LicitacaoItem.class)
                    .addProperties(new HQLProperties(LicitacaoItem.class).getProperties())
                    .addProperty(VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE))
                    .addProperty(VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                    .addProperty(VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_FLAG_CONTROLA_GRUPO_ESTOQUE))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LicitacaoItem.PROP_LICITACAO), licitacao))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LicitacaoItem.PROP_STATUS), BuilderQueryCustom.QueryParameter.IN, Arrays.asList(LicitacaoItem.StatusLicitacaoItem.NAO_COTADO.value(), LicitacaoItem.StatusLicitacaoItem.LICITADO.value())))
                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_DESCRICAO)))
                    .start().getList();
    }
    
    private void setarInformacoes(AjaxRequestTarget target, LicitacaoItem rowObject) throws DAOException, ValidacaoException{
        autoCompleteConsultaProduto.limpar(target);
        modelItem.setObject(rowObject);
        target.add(txtQuantidade);
        autoCompleteConsultaPessoa.focus(target);
    }
    
    private void adicionar(AjaxRequestTarget target) throws DAOException, ValidacaoException{
        LicitacaoItem licitacaoItem = modelItem.getObject();
        if (licitacaoItem.getProduto()==null) {
            throw new ValidacaoException(BundleManager.getString("informeProduto"));
        }
        if (licitacaoItem.getPessoa()==null) {
            throw new ValidacaoException(BundleManager.getString("informeFornecedor"));
        }
        if (licitacaoItem.getPrecoTotal()==null) {
            throw new ValidacaoException(BundleManager.getString("informePrecoTotal"));
        }
        if (licitacaoItem.getCodigo()==null) {
            LicitacaoItem itemPersistido = LoadManager.getInstance(LicitacaoItem.class)
                    .addProperties(new HQLProperties(LicitacaoItem.class).getProperties())
                    .addProperty(VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LicitacaoItem.PROP_LICITACAO), licitacao))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO), licitacaoItem.getProduto()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LicitacaoItem.PROP_STATUS), LicitacaoItem.StatusLicitacaoItem.ABERTO.value()))
                    .start().getVO();
            itemPersistido.setPessoa(licitacaoItem.getPessoa());
            itemPersistido.setPrecoTotal(licitacaoItem.getPrecoTotal());
            licitacaoItem = itemPersistido;
        }
        produtos.remove(licitacaoItem);
        LicitacaoItem itemLicitado = BOFactoryWicket.getBO(ConsorcioFacade.class).licitarItem(licitacaoItem);
        produtosLicitados.add(0, itemLicitado);
        tblProdutos.update(target);
        tblProdutosLicitados.update(target);
        limpar(target);
    }
    
    private void limpar(AjaxRequestTarget target){
        modelItem.setObject(new LicitacaoItem());
        autoCompleteConsultaProduto.limpar(target);
        txtPrecoTotal.limpar(target);
        txtQuantidade.limpar(target);
        txtNrItemPregao.limpar(target);
        autoCompleteConsultaProduto.focus(target);
    }
    
    private void removerItemLicitado(AjaxRequestTarget target, LicitacaoItem rowObject) throws ValidacaoException, DAOException {
        produtosLicitados.remove(rowObject);
        LicitacaoItem itemReaberto = BOFactoryWicket.getBO(ConsorcioFacade.class).reabrirItem(rowObject);
        produtos.add(0, itemReaberto);
        tblProdutos.update(target);
        tblProdutosLicitados.update(target);
    }
    
    private void definirNaoCotado(AjaxRequestTarget target, LicitacaoItem licitacaoItem, String justificativa) throws ValidacaoException, DAOException{
        if (licitacaoItem.getCodigo()==null) {
            LicitacaoItem itemPersistido = LoadManager.getInstance(LicitacaoItem.class)
                    .addProperties(new HQLProperties(LicitacaoItem.class).getProperties())
                    .addProperty(VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LicitacaoItem.PROP_LICITACAO), licitacao))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO), licitacaoItem.getProduto()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LicitacaoItem.PROP_STATUS), LicitacaoItem.StatusLicitacaoItem.ABERTO.value()))
                    .start().getVO();
            itemPersistido.setPessoa(licitacaoItem.getPessoa());
            itemPersistido.setPrecoTotal(licitacaoItem.getPrecoTotal());
            licitacaoItem = itemPersistido;
        }
        produtos.remove(licitacaoItem);
        LicitacaoItem itemNaoCotado = BOFactoryWicket.getBO(ConsorcioFacade.class).definirNaoCotado(licitacaoItem, justificativa);
        produtosLicitados.add(itemNaoCotado);
        tblProdutos.update(target);
        tblProdutosLicitados.update(target);
        limpar(target);
    }
    
    private void definirTodosNaoCotado(AjaxRequestTarget target, String justificativa) throws ValidacaoException, DAOException{
        List<LicitacaoItem> itensNaoCotados = BOFactoryWicket.getBO(ConsorcioFacade.class).definirTodosNaoCotado(produtos, justificativa);
        produtos.removeAll(itensNaoCotados);
        produtosLicitados.addAll(itensNaoCotados);
        tblProdutos.update(target);
        tblProdutosLicitados.update(target);
        limpar(target);
    }
    
    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (licitacao.getNumeroPregao()==null) {
            throw new ValidacaoException(BundleManager.getString("informeNumeroPregao"));
        }
        if (licitacao.getDataValidade()==null) {
            throw new ValidacaoException(BundleManager.getString("informeValidade"));
        }
        BOFactoryWicket.save(licitacao);
        Page page = new ConsultaLicitacaoPage();
        setResponsePage(page);
            
        getSession().getFeedbackMessages().info(page, BundleManager.getString("pregaoRealizadoComSucesso"));
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtNumeroPregao;
    }

}
