package br.com.celk.view.materiais.localizacao.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.util.MessageUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.Localizacao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Created by sulivan on 27/10/17.
 */
public abstract class PnlCadastroLocalizacao extends Panel {

    private Form<Localizacao> form;
    private InputField txtDescricao;
    private InputField txtSigla;

    public PnlCadastroLocalizacao(String id){
        super(id);
        init();
    }

    private void init() {
        form = new Form("form", new CompoundPropertyModel(new Localizacao()));
        setOutputMarkupId(true);

        Localizacao proxy = on(Localizacao.class);

        form.add(txtDescricao = new InputField(path(proxy.getDescricao())));
        form.add(txtSigla = new InputField(path(proxy.getSigla())));

        form.add(new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if(validarCadastro(target)) {
                    onSalvar(target, salvar());
                }
            }
        });

        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        add(form);
    }

    public boolean validarCadastro(AjaxRequestTarget target) {
        try {
            if(txtDescricao.getComponentValue() == null){
                throw new ValidacaoException(bundle("msgInformeDescricao"));
            }
            if(txtSigla.getComponentValue() == null){
                throw new ValidacaoException(bundle("msgInformeSigla"));
            }
        } catch (ValidacaoException e) {
            MessageUtil.modalWarn(target, this, e);
            return false;
        }

        return true;
    }

    private Localizacao salvar() throws ValidacaoException, DAOException {
        Localizacao localizacao = form.getModel().getObject();

        boolean existeLocalizacao = LoadManager.getInstance(Localizacao.class)
                .addParameter(new QueryCustom.QueryCustomParameter(Localizacao.PROP_SIGLA, localizacao.getSigla()))
                .addParameter(new QueryCustom.QueryCustomParameter(Localizacao.PROP_DESCRICAO, localizacao.getDescricao()))
                .exists();
        if (existeLocalizacao){
            throw new ValidacaoException(bundle("msgLocalizacaoComMesmaSeglaJaExiste"));
        }

        return BOFactoryWicket.save(localizacao);
    }

    public abstract void onSalvar(AjaxRequestTarget target, Localizacao localizacao) throws DAOException, ValidacaoException;

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void limpar(AjaxRequestTarget target) {
        form.setModel(new CompoundPropertyModel(new Localizacao()));
        target.add(form);
        txtDescricao.limpar(target);
        txtSigla.limpar(target);
    }

    public final void onFocus(AjaxRequestTarget target) {
        target.focusComponent(txtDescricao);
    }
}