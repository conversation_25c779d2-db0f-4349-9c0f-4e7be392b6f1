package br.com.celk.view.vigilancia.faturamento.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class AutoCompleteConsultaAtividadesVigilancia extends AutoCompleteConsulta<AtividadesVigilancia> {

    private boolean apenasSolicitacoesDiversas;
    private boolean apenasSolicitacoesJuridicas;

    public AutoCompleteConsultaAtividadesVigilancia(String id) {
        super(id);
    }

    public AutoCompleteConsultaAtividadesVigilancia(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaAtividadesVigilancia(String id, IModel<AtividadesVigilancia> model) {
        super(id, model);
    }

    public AutoCompleteConsultaAtividadesVigilancia(String id, IModel<AtividadesVigilancia> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {
            @Override
            public Class getReferenceClass() {
                return AtividadesVigilancia.class;
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(AtividadesVigilancia.PROP_DESCRICAO, true);
            }

            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaAdapter() {

                    @Override
                    public void consultaCustomizeViewProperties(Map<String, String> properties) {
                        properties.put(BundleManager.getString("descricao"), VOUtils.montarPath(AtividadesVigilancia.PROP_DESCRICAO));
                    }

                    @Override
                    public void consultaCustomizeFilterProperties(Map<String, BuilderQueryCustom.QueryParameter> filterProperties) {
                        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(AtividadesVigilancia.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.CONSULTA_LIKED));
                    }

                    @Override
                    public void consultaCustomizeParameters(List<BuilderQueryCustom.QueryParameter> parameters) {
                        if(apenasSolicitacoesDiversas){
                            parameters.add(new QueryCustom.QueryCustomParameter(AtividadesVigilancia.PROP_SOLICITACOES_DIVERSAS, BuilderQueryCustom.QueryParameter.IGUAL, RepositoryComponentDefault.SIM_LONG));
                        }
                        if(apenasSolicitacoesJuridicas){
                            parameters.add(new QueryCustom.QueryCustomParameter(AtividadesVigilancia.PROP_SOLICITACAO_JURIDICA, BuilderQueryCustom.QueryParameter.IGUAL, RepositoryComponentDefault.SIM_LONG));
                        }
                    }


                    @Override
                    public Class getClassConsulta() {
                        return AtividadesVigilancia.class;
                    }

                };
            }

            @Override
            public List<QueryCustom.QueryCustomParameter> getSearchParam(String searchCriteria) {
                return Arrays.asList(new QueryCustom.QueryCustomParameter(AtividadesVigilancia.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.CONSULTA_LIKED, (searchCriteria != null ? searchCriteria.trim() : null)));
            }
        };
    }

    public void setApenasSolicitacoesDiversas(boolean apenasSolicitacoesDiversas) {
        this.apenasSolicitacoesDiversas = apenasSolicitacoesDiversas;
    }

    public void setApenasSolicitacoesJuridicas(boolean apenasSolicitacoesJuridicas) {
        this.apenasSolicitacoesJuridicas = apenasSolicitacoesJuridicas;
    }

    @Override
    public String[] getPropertiesLoad() {
        return VOUtils.mergeProperties(new HQLProperties(AtividadesVigilancia.class).getProperties(),
                new String[]{
                        VOUtils.montarPath(AtividadesVigilancia.PROP_PROCEDIMENTO, Procedimento.PROP_FLAG_FATURAVEL),
                });
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("atividadesVigilancia");
    }

}
