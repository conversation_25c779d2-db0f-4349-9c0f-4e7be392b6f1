package br.com.celk.view.unidadesaude.receituario;

import static ch.lambdaj.Lambda.*;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.QueryConsultaOcupacaoDiariaDTO;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.QueryConsultaOcupacaoDiariaDTOParam;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.DTOParamConfigureDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaOcupacaoDiariaPage extends BasePage {
    
    private Table table;
    
    public ConsultaOcupacaoDiariaPage(){
        init();
    }
    
    private void init() {
        Form form = new Form("form");
        form.add(table = new Table("table", getColumns(), getCollectionProvider()));
        
        table.setScrollY("500px");
        table.populate();
        
        add(form);
    }
    
    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        QueryConsultaOcupacaoDiariaDTO proxy = on(QueryConsultaOcupacaoDiariaDTO.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("setor"), proxy.getEmpresa().getDescricao()));
        columns.add(createColumn(bundle("leitosAtivos"), proxy.getLeitosAtivos()));
        columns.add(createColumn(bundle("leitosDisponiveis"), proxy.getLeitosDisponiveis()));
        columns.add(createColumn(bundle("leitosOcupados"), proxy.getLeitosOcupados()));
        columns.add(createColumn(bundle("taxaOcupacao"), proxy.descricaoTaxaOcupacao()));
        columns.add(createColumn(bundle("mediaPermanencia"), proxy.descricaoMediaPermanencia()));
        
        return columns;
    }
    
    private IColumn getActionColumn(){
        return new MultipleActionCustomColumn<QueryConsultaOcupacaoDiariaDTO>() {

            @Override
            public void customizeColumn(QueryConsultaOcupacaoDiariaDTO rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<QueryConsultaOcupacaoDiariaDTO>() {

                    @Override
                    public void action(AjaxRequestTarget target, QueryConsultaOcupacaoDiariaDTO modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new ConsultaOcupacaoDiariaPorSetorPage(modelObject));
                    }
                });
            }
        };
    }
    
    private ICollectionProvider getCollectionProvider(){
        return new CollectionProvider<QueryConsultaOcupacaoDiariaDTO, QueryConsultaOcupacaoDiariaDTOParam>() {
            @Override
            public Collection getCollection(QueryConsultaOcupacaoDiariaDTOParam param) throws DAOException, ValidacaoException {
                param = new QueryConsultaOcupacaoDiariaDTOParam();
                if(getSort() != null){                    
                    DTOParamConfigureDefault paramConfigureDefault = new DTOParamConfigureDefault();
                    paramConfigureDefault.addSorter(getSort().getProperty(), getSort().isAscending()?"asc":"desc");
                    param.setConfigureParam(paramConfigureDefault);
                }
                return BOFactoryWicket.getBO(HospitalFacade.class).consultaOcupacaoDiaria(param);
            }

            @Override
            public SortParam<String> getDefaultSort() {
                return null;
            }
        };
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("ocupacaoDiaria");
    }
    
}