package br.com.celk.view.agenda.agendamento;

import br.com.celk.agendamento.AgendamentoHelper;
import br.com.celk.agendamento.agendamentofilaespera.RegraDiasLimiteAgendamentoListaEspera;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionLinkPanel;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dialog.DlgImpressao;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.notification.INotificationPanel;
import br.com.celk.component.table.SelectionTable;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.view.agenda.agendamento.agendamentoListaEspera.FiltrarAgendaGradePorPrioridade;
import br.com.celk.view.agenda.agendamento.containerProfissional.BuildWebContainerProfissional;
import br.com.celk.view.agenda.agendamento.containerProfissional.BuildWebContainerUnidade;
import br.com.celk.view.agenda.agendamento.recebimentosolicitacoesagendamento.ConfirmacaoRecebimentoSolicitacoesPage;
import br.com.celk.view.atendimento.recepcao.panel.marcacao.agendamento.MarcacaoAgendamentoColumnPanel;
import br.com.celk.view.atendimento.recepcao.panel.marcacao.agendamento.MarcacaoAgendamentoHorarioTableColor;
import br.com.celk.view.atendimento.recepcao.panel.marcacao.dialog.DlgRecomendacoesAgenda;
import br.com.celk.view.atendimento.recepcao.panel.marcacao.dialog.DlgRecomendacoesAgendaAntesConfirmar;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTOParam;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoPacienteDTO;
import br.com.ksisolucoes.agendamento.exame.dto.AgendamentoListaEsperaDTOParam;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.solicitacaoagendamento.SolicitacaoAgendamentoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.AtendimentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioImprimirComprovanteAgendamentoDTOParam;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.*;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import ch.lambdaj.Lambda;
import org.apache.wicket.Component;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.link.BookmarkablePageLink;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;
import org.hamcrest.Matchers;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.having;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class AgendamentoSolicitacaoCotaDiarioPage extends BasePage {

    private Form<AgendaGradeAtendimentoDTOParam> form;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private SelectionTable<AgendaGradeAtendimentoDTO> tblAgendamentoSolicitacao;
    private final AgendaGradeAtendimentoDTOParam param = new AgendaGradeAtendimentoDTOParam();
    private List<AgendaGradeAtendimentoDTO> agendaGradeAtendimentoDTOList;
    private DlgImpressao dlgImpressao;
    private Class classeVoltar;
    private LoteSolicitacaoAgendamentoItem loteSolicitacaoAgendamentoItem;
    private PageParameters parameters;
    private final AgendaCota agendaCota;
    private String cotaUtilizada;
    private Long qtdCota;
    private InputField txtCotaUtilizada;
    private InputField txtCotaProfissional;
    private DisabledInputField quantidadeCota;
    private String profissionalAgenda;
    private InputField inputProfissionalAgenda;
    private String unidadeAgenda;
    private InputField txtUnidadeAgenda;
    private Table<AgendaGradeAtendimentoDTO> tblAgendamentos;
    private List<AgendaGradeAtendimentoPacienteDTO> agendamentosList;
    private final List<AgendaGradeAtendimentoDTO> agendarList = new ArrayList<AgendaGradeAtendimentoDTO>();
    private AbstractAjaxButton btnAgendar;
    private DlgConfirmacaoSimNao dlgConfirmacaoSimNao;
    private DlgRecomendacoesAgenda dlgRecomendacoesAgenda;
    private Empresa empresaAgenda;
    private DlgRecomendacoesAgendaAntesConfirmar dlgRecomendacoesAgendaAntesConfirmar;
    private DlgConfirmacaoSimNao dlgConfirmacaoSimNaoRecomendacoes;
    private AgendamentoListaEsperaDTOParam paraLista;

    public AgendamentoSolicitacaoCotaDiarioPage(SolicitacaoAgendamento solicitacaoAgendamento, PageParameters parameters, AgendaCota agendaCota, Empresa empresaAgenda) {
        super(parameters);
        this.parameters = parameters;
        this.agendaCota = agendaCota;
        this.empresaAgenda = empresaAgenda;
        inicializarParam(solicitacaoAgendamento);
        init();
    }

    public AgendamentoSolicitacaoCotaDiarioPage(SolicitacaoAgendamento solicitacaoAgendamento, AgendamentoListaEsperaDTOParam paraLista, AgendaCota agendaCota, Empresa empresaAgenda) {
        this.paraLista = paraLista;
        this.agendaCota = agendaCota;
        this.empresaAgenda = empresaAgenda;
        inicializarParam(solicitacaoAgendamento);
        init();
    }

    public AgendamentoSolicitacaoCotaDiarioPage(SolicitacaoAgendamento solicitacaoAgendamento, LoteSolicitacaoAgendamentoItem loteSolicitacaoAgendamentoItem, PageParameters parameters, AgendaCota agendaCota) {
        super(parameters);
        this.parameters = parameters;
        this.agendaCota = agendaCota;
        inicializarParam(solicitacaoAgendamento);
        this.loteSolicitacaoAgendamentoItem = loteSolicitacaoAgendamentoItem;
        init();
    }

    public AgendamentoSolicitacaoCotaDiarioPage(SolicitacaoAgendamento solicitacaoAgendamento, Class classeVoltar, PageParameters parameters, AgendaCota agendaCota, Empresa empresaAgenda) {
        super(parameters);
        this.parameters = parameters;
        this.agendaCota = agendaCota;
        this.empresaAgenda = empresaAgenda;
        inicializarParam(solicitacaoAgendamento);
        this.classeVoltar = classeVoltar;
        init();
    }

    private void init() {
        AgendaGradeAtendimentoDTOParam proxy = on(AgendaGradeAtendimentoDTOParam.class);

        getForm().add(new DisabledInputField(path(proxy.getTipoProcedimento().getDescricao())));
        getForm().add(new DisabledInputField(path(proxy.getEmpresaOrigem().getDescricao())));
        getForm().add(new DisabledInputField(path(proxy.getUsuarioCadsus().getNomeSocial())));
        getForm().add(new DisabledInputField(path(proxy.getAgendaCota().getDescricaoTipoCota())));
        getForm().add(txtCotaProfissional = new DisabledInputField("qtdCota", new PropertyModel(this, "qtdCota")));
        getForm().add(txtCotaUtilizada = new DisabledInputField("cotaUtilizada", new PropertyModel(this, "cotaUtilizada")));
        qtdCota = agendaCota.getQuantidadeCota();

        autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional(path(proxy.getProfissional()));

        inputProfissionalAgenda = new DisabledInputField("profissionalAgenda", new PropertyModel(this, "profissionalAgenda"));
        txtUnidadeAgenda = new DisabledInputField("unidadeAgenda", new PropertyModel(this, "unidadeAgenda"));

        WebMarkupContainer containerProfissional = new BuildWebContainerProfissional().setAutoCompleteConsultaProfissional(autoCompleteConsultaProfissional)
                                                                                      .setInputProfissionalAgenda(inputProfissionalAgenda)
                                                                                      .ocultarContainer(param.getSolicitacaoAgendamento())
                                                                                      .build();

        WebMarkupContainer containerUnidade = new BuildWebContainerUnidade().setInputUnidadeAgenda(txtUnidadeAgenda)
                                                                            .ocultarContainer()
                                                                            .build();

        getForm().add(containerProfissional);
        getForm().add(containerUnidade);

        autoCompleteConsultaProfissional.add(new ConsultaListener<Profissional>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Profissional object) {
                AgendaCotaProfissional acp = AgendamentoHelper.consultarAgendaCotaProfissional(agendaCota, object);
                if (acp != null) {
                    qtdCota = acp.getQuantidadeCota();
                    target.add(txtCotaProfissional);
                }
                txtCotaUtilizada.limpar(target);
                carregarAgendaGradeAtendimento(target, object);
            }
        });

        autoCompleteConsultaProfissional.add(new RemoveListener<Profissional>() {

            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Profissional object) {
                qtdCota = agendaCota.getQuantidadeCota();
                txtCotaUtilizada.limpar(target);
                target.add(txtCotaProfissional);
                carregarAgendaGradeAtendimento(target, null);
            }
        });

        getForm().add(tblAgendamentoSolicitacao = new MarcacaoAgendamentoHorarioTableColor("tblAgendamentoSolicitacao", getColumns(), getCollectionProvider()));
        tblAgendamentoSolicitacao.populate();
        tblAgendamentoSolicitacao.addSelectionAction(new ISelectionAction<AgendaGradeAtendimentoDTO>() {
            @Override
            public void onSelection(AjaxRequestTarget target, AgendaGradeAtendimentoDTO object) {
                if (object.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getProfissional() != null) {
                    profissionalAgenda = object.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getProfissional().getNome();
                    target.add(inputProfissionalAgenda);
                }
                if (object.getEmpresa() != null) {
                    unidadeAgenda = object.getEmpresa().getDescricao();
                    target.add(txtUnidadeAgenda);
                }

                try {
                    selecionarAgenda(target, object);
                    verificarCotaUtilizada(target, object);
                } catch (ValidacaoException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                } catch (DAOException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
                
                carregarRecomendacoes(target, object.getAgendaGradeAtendimento().getAgendaGrade().getAgenda());
            }
        });
        tblAgendamentoSolicitacao.setScrollY("150px");

        tblAgendamentos = new Table("tblAgendamentos", getColumnsAgenda(), getCollectionProviderAgenda());
        getForm().add(tblAgendamentos);
        tblAgendamentos.populate();
        tblAgendamentos.setScrollY("150px");

        if (getClasseVoltar() != null) {
            getForm().add(new BookmarkablePageLink("btnVoltar", getClasseVoltar(), parameters));
        } else {
            getForm().add(new VoltarButton("btnVoltar"));
        }

        getForm().add(btnAgendar = getBtnAgendar());
        btnAgendar.setOutputMarkupPlaceholderTag(true);
        btnAgendar.setVisible(getForm().getModel().getObject().getTipoProcedimento().habilitaAgendamentoGrupo());
        add(getForm());
        carregarAgendaGradeAtendimento(null, null);
        verificarComparecimento();
        carregarOrientacaoDocumentoRetido();
        tblAgendamentoSolicitacao.populate();
        tblAgendamentos.populate();
    }

    private void carregarOrientacaoDocumentoRetido() {
        TipoProcedimento tp = LoadManager.getInstance(TipoProcedimento.class)
                .addProperty(TipoProcedimento.PROP_CODIGO)
                .addProperty(TipoProcedimento.PROP_DESCRICAO)
                .addProperty(TipoProcedimento.PROP_FLAG_DOCUMENTO_RETIDO)
                .addParameter(new QueryCustom.QueryCustomParameter(TipoProcedimento.PROP_CODIGO, getForm().getModel().getObject().getTipoProcedimento().getCodigo()))
                .start().getVO();
        if (RepositoryComponentDefault.SIM_LONG.equals(tp.getFlagDocumentoRetido())) {
            getSession().getFeedbackMessages().warn(this, bundle("msgDocumentoDeveFicarRetidoEstabelecimento"));
        }
    }

    private AbstractAjaxButton getBtnAgendar() {
        return new AbstractAjaxButton("btnAgendar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (CollectionUtils.isEmpty(agendarList)) {
                    throw new ValidacaoException(BundleManager.getString("msgSelecionePeloMenosUmaAgendaParaAgendar"));
                }

                if (dlgConfirmacaoSimNao == null) {
                    AgendaGradeAtendimentoDTOParam object = (AgendaGradeAtendimentoDTOParam) getForm().getModel().getObject();

                    addModal(target, dlgConfirmacaoSimNao = new DlgConfirmacaoSimNao(newModalId(), Bundle.getStringApplication("msg_confirma_agendamento_itens_selecionados_atendimento_X",
                            object.getTipoProcedimento().getDescricao())) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                            agendarActionSelecionados(target);
                        }
                    });
                }
                dlgConfirmacaoSimNao.show(target);
            }
        };
    }

    private void agendarActionSelecionados(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        AgendaGradeAtendimentoDTOParam object = getForm().getModel().getObject();
        if (CollectionUtils.isNotNullEmpty(agendarList)) {
            for (AgendaGradeAtendimentoDTO aga : agendarList) {
                aga.setUsuarioCadsus(object.getUsuarioCadsus());
                aga.setNomePaciente(object.getUsuarioCadsus().getNomeSocial());
                aga.setProcedimento(object.getSolicitacaoAgendamento().getProcedimento());
                aga.setSolicitacaoAgendamento(object.getSolicitacaoAgendamento());
                aga.setEmpresaAgenda(empresaAgenda);

                if (loteSolicitacaoAgendamentoItem != null) {
                    aga.setLoteSolicitacaoAgendamentoItem(loteSolicitacaoAgendamentoItem);
                }
            }

            List<AgendaGradeAtendimentoHorario> agahList = BOFactoryWicket.getBO(AgendamentoFacade.class).registrarAgendamentosSelecionados(
                    agendarList, empresaAgenda,
                    object.getUsuarioCadsus(),
                    object.getSolicitacaoAgendamento().getProcedimento(), null, null);

            viewDlgImpressao(target, agahList);
        }
    }

    private void viewDlgImpressao(AjaxRequestTarget target, final List<AgendaGradeAtendimentoHorario> agahList) {
        if (dlgRecomendacoesAgenda == null) {
            addModal(target, dlgRecomendacoesAgenda = new DlgRecomendacoesAgenda(newModalId()) {
                @Override
                public DataReport onImprimir() throws ReportException {
                    return imprimir(agahList);
                }

                @Override
                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    fechar();
                }
            });
        }

        dlgRecomendacoesAgenda.show(target, agahList, true);
        
//        Agenda agenda = agahList.get(0).getAgendaGradeAtendimento().getAgendaGrade().getAgenda();
//
//        Agenda agendaAnexo = LoadManager.getInstance(Agenda.class)
//                .addProperty(VOUtils.montarPath(Agenda.PROP_GERENCIADOR_ARQUIVO, GerenciadorArquivo.PROP_CODIGO))
//                .addParameter(new QueryCustom.QueryCustomParameter(Agenda.PROP_CODIGO, agenda.getCodigo()))
//                .start().getVO();
//
//        if (RepositoryComponentDefault.SIM_LONG.equals(agenda.getFlagVisualizaAgendar()) && (agenda.getRecomendacoes() != null
//                || (agendaAnexo != null && agendaAnexo.getGerenciadorArquivo() != null && agendaAnexo.getGerenciadorArquivo().getCodigo() != null))) {
//            if (dlgRecomendacoesAgenda == null) {
//                addModal(target, dlgRecomendacoesAgenda = new DlgRecomendacoesAgenda(newModalId()) {
//                    @Override
//                    public DataReport onImprimir() throws ReportException {
//                        return imprimir(agahList);
//                    }
//
//                    @Override
//                    public void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
//                        fechar();
//                    }
//                });
//            }
//
//            dlgRecomendacoesAgenda.show(target, agahList);
//        } else {
//            if (dlgImpressao == null) {
//                addModal(target, dlgImpressao = new DlgImpressao(newModalId(), bundle("agendamentoRealizadoSucesso")) {
//                    @Override
//                    public DataReport onImprimir() throws ReportException {
//                        return imprimir(agahList);
//                    }
//
//                    @Override
//                    public void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
//                        fechar();
//                    }
//                });
//            }
//
//            dlgImpressao.show(target);
//        }
    }

    private DataReport imprimir(List<AgendaGradeAtendimentoHorario> agahList) throws ReportException {
        RelatorioImprimirComprovanteAgendamentoDTOParam paramRelatorio = new RelatorioImprimirComprovanteAgendamentoDTOParam();
        paramRelatorio.setAgendaGradeAtendimentoHorarioList(agahList);
        return BOFactoryWicket.getBO(AtendimentoReportFacade.class).relatorioImprimirComprovanteAgendamentoSemSolicitacao(paramRelatorio);
    }

    public void fechar() {
        Page page;

        if (loteSolicitacaoAgendamentoItem != null) {
            page = new ConfirmacaoRecebimentoSolicitacoesPage(loteSolicitacaoAgendamentoItem.getLoteSolicitacaoAgendamento().getCodigo(), parameters);
        } else {
            page = new AgendamentoListaEsperaPage(parameters, true);
        }

        setResponsePage(page);
    }

    private void verificarComparecimento() {
        List<AgendaGradeAtendimentoHorario> agahList = LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
                .addProperties(new HQLProperties(AgendaGradeAtendimentoHorario.class).getProperties())
                .addProperties(new HQLProperties(Empresa.class, AgendaGradeAtendimentoHorario.PROP_LOCAL_AGENDAMENTO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_USUARIO_CADSUS, param.getUsuarioCadsus()))
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_TIPO_PROCEDIMENTO, param.getTipoProcedimento()))
                .addSorter(new QueryCustom.QueryCustomSorter(AgendaGradeAtendimentoHorario.PROP_CODIGO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(agahList)) {
            for (AgendaGradeAtendimentoHorario agah : agahList) {
                if (AgendaGradeAtendimentoHorario.STATUS_NAO_COMPARECEU.equals(agah.getStatus())) {
                    warn(bundle("msgPacienteNaoCompareceuUltimoAgendamentoRealizadoParaEsteTipoDataAgendamentoXLocalX",
                            agah.getDataHoraAgendamentoFormatado(), agah.getLocalAgendamento().getDescricao()));
                }
                break;
            }
        }
    }

    private void inicializarParam(SolicitacaoAgendamento solicitacaoAgendamento) {
        param.setSolicitacaoAgendamento(solicitacaoAgendamento);
        param.setTipoProcedimento(solicitacaoAgendamento.getTipoProcedimento());
        if (empresaAgenda != null) {
            param.setEmpresaAgenda(empresaAgenda);
        }
        param.setEmpresaOrigem(solicitacaoAgendamento.getEmpresa());
        param.setUsuarioCadsus(solicitacaoAgendamento.getUsuarioCadsus());
    }

    private Form<AgendaGradeAtendimentoDTOParam> getForm() {
        if (this.form == null) {
            param.setAgendaCota(agendaCota);
            this.form = new Form("form", new CompoundPropertyModel<AgendaGradeAtendimentoDTOParam>(param));
        }
        return this.form;
    }

    public List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        AgendaGradeAtendimentoDTO proxy = on(AgendaGradeAtendimentoDTO.class);
        if (getForm().getModel().getObject().getTipoProcedimento().habilitaAgendamentoGrupo()) {
            columns.add(getSelectionActionColumn());
        }
        columns.add(getActionColumn());
        columns.add(new DateColumn(bundle("dia"), path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getData())).setPattern("dd/MM"));
        columns.add(createColumn(bundle("semana"), proxy.getDiaSemanaAbv()));
        columns.add(createColumn(bundle("inicio"), proxy.getAgendaGradeAtendimento().getAgendaGrade().getDescricaoHoraInicial()));
        columns.add(createColumn(bundle("vagas"), proxy.getVagasDisponiveis()));
        columns.add(createColumn(bundle("tipo"), proxy.getAgendaGradeAtendimento().getTipoAtendimentoAgenda().getDescricao()));

        return columns;
    }

    private IColumn getSelectionActionColumn() {
        return new CustomColumn<AgendaGradeAtendimentoDTO>() {

            @Override
            public Component getComponent(String componentId, final AgendaGradeAtendimentoDTO rowObject) {
                return new MarcacaoAgendamentoColumnPanel(componentId, rowObject.getTipoProcedimento().habilitaAgendamentoGrupo(), rowObject.getVagasDisponiveis()) {
                    @Override
                    public void onSelectionAction(AjaxRequestTarget target, boolean selectionAction) {
                        if (selectionAction) {
                            agendarList.add(rowObject);
                        } else {
                            agendarList.remove(rowObject);
                        }
                        
                        carregarRecomendacoes(target, rowObject.getAgendaGradeAtendimento().getAgendaGrade().getAgenda());
                    }
                };
            }
        };
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<AgendaGradeAtendimentoDTO>() {
            @Override
            public void customizeColumn(final AgendaGradeAtendimentoDTO rowObject) {
                ActionLinkPanel alp = addAction(ActionType.AGENDAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        if(exibirRecomendacoes(rowObject.getAgendaGradeAtendimento().getAgendaGrade().getAgenda())){
                            initDlgRecomendacoesAgendaAntesConfirmar(target, rowObject);
                        } else {
                            agendarAction(target, rowObject);
                        }
                    }
                });
                alp.setTitleBundleKey("agendar");
                alp.setQuestionDialogBundleKey(exibirRecomendacoes(rowObject.getAgendaGradeAtendimento().getAgendaGrade().getAgenda()) ? null : "msgConfirmarAgendamento_X",
                        rowObject.getTipoProcedimento().getDescricao(),
                        rowObject.getAgendaGradeAtendimento().getAgendaGrade().getDescricaoDataHoraInicial(),
                        rowObject.getAgendaGradeAtendimento().getTipoAtendimentoAgenda().getDescricao());
                alp.setEnabled(rowObject.getVagasDisponiveis() > 0L);

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<AgendaGradeAtendimentoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AgendaGradeAtendimentoDTO modelObject) throws ValidacaoException, DAOException {
                        verificarCotaUtilizada(target, modelObject);
                    }
                }).setTitleBundleKey("visualizarCota");
            }
        };
    }

    private void verificarCotaUtilizada(AjaxRequestTarget target, AgendaGradeAtendimentoDTO object) {
        boolean existsAgendaCotaProfissional = AgendamentoHelper.existsAgendaCotaProfissional(agendaCota);
        AgendaCotaProfissional acp = null;
        try {
            Profissional prof = getForm().getModel().getObject().getProfissional();
            if (existsAgendaCotaProfissional && prof != null) {
                acp = AgendamentoHelper.consultarAgendaCotaProfissional(agendaCota, prof);
                if (acp != null) {
                    Long cotaUtilizadaProfissional = BOFactoryWicket.getBO(AgendamentoFacade.class)
                            .countConsultarCotaProfissionalUtilizadaAgendamentos(acp, object.getAgendaGradeAtendimento().getAgendaGrade().getData());
                    cotaUtilizada = cotaUtilizadaProfissional.toString();
                }
            } else {
                Long cota = BOFactoryWicket.getBO(AgendamentoFacade.class)
                        .countConsultarCotaUtilizadaAgendamentos(agendaCota, object.getAgendaGradeAtendimento().getAgendaGrade().getData());
                cotaUtilizada = cota.toString();
            }

        } catch (DAOException | ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        target.add(txtCotaUtilizada);
    }

    private void agendarAction(AjaxRequestTarget target, AgendaGradeAtendimentoDTO dto) throws DAOException, ValidacaoException {
        final AgendaGradeAtendimentoDTOParam object = getForm().getModel().getObject();
        dto.setUsuarioCadsus(object.getUsuarioCadsus());
        dto.setNomePaciente(object.getUsuarioCadsus().getNomeSocial());
        dto.setProcedimento(object.getSolicitacaoAgendamento().getProcedimento());
        dto.setSolicitacaoAgendamento(object.getSolicitacaoAgendamento());

        if (loteSolicitacaoAgendamentoItem != null) {
            dto.setLoteSolicitacaoAgendamentoItem(loteSolicitacaoAgendamentoItem);
        }

        BOFactory.getBO(SolicitacaoAgendamentoFacade.class).validarSolicitacaoAgendada(dto.getSolicitacaoAgendamento());
        final AgendaGradeAtendimentoHorario agah = BOFactoryWicket.getBO(AgendamentoFacade.class).registrarAgendamento(dto, object.getSolicitacaoAgendamento().getEmpresa(), empresaAgenda);

        List<AgendaGradeAtendimentoHorario> agahList = new ArrayList();
        agahList.add(agah);
        viewDlgImpressao(target, agahList);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object object) throws DAOException, ValidacaoException {
                if (CollectionUtils.isEmpty(agendaGradeAtendimentoDTOList)) {
                    if (TipoProcedimento.TIPO_PROCEDIMENTO_DENTRO_DA_REDE.equals(param.getSolicitacaoAgendamento().getTipoProcedimento().getTipoAgendamento())) {
                        Page page = new AgendamentoListaEsperaPage(parameters, true);
                        setResponsePage(page);
                        if (AgendamentoHelper.existsAgendaCotaProfissional(agendaCota)) {
                            getSession().getFeedbackMessages().warn(page, bundle("msgNaoExisteCotaCadastradaParaProfissionalAgenda"));
                        } else {
                            getSession().getFeedbackMessages().warn(page, BundleManager.getString("msgEstabelecimentoSemCotaProcedimento"));
                        }
                    } else {
                        setResponsePage(new AgendamentoExternoSolicitacaoPanel(param.getSolicitacaoAgendamento(), parameters));
                    }
                }
                if(CollectionUtils.isNotNullEmpty(agendaGradeAtendimentoDTOList)){
                    agendaGradeAtendimentoDTOList = Lambda.select(agendaGradeAtendimentoDTOList,
                            having(on(AgendaGradeAtendimentoDTO.class).getVagasDisponiveis(), Matchers.greaterThan(0L)));
                }

                return agendaGradeAtendimentoDTOList;
            }
        };
    }

    private void carregarAgendaGradeAtendimento(AjaxRequestTarget target, Profissional profissional) {
        agendamentosList = new ArrayList<>();

        param.setValidarUnidadeInformatizada(true);
        param.setValidarEmpresaOrigem(true);
        param.setTipoAtendimentoAgendaList(Arrays.asList(TipoAtendimentoAgenda.TIPO_CONSULTA, TipoAtendimentoAgenda.TIPO_RETORNO));
        param.setApenasAgendasComVagas(false);
        param.setProfissional(profissional);
        param.setExameProcedimentoList(AgendamentoHelper.examesSolicitacaoAgendamento(param.getSolicitacaoAgendamento()));
        param.setDiasLimiteAgendamentoListasEspera(new RegraDiasLimiteAgendamentoListaEspera(param.getSolicitacaoAgendamento().getEmpresa()));

        try {
            agendaGradeAtendimentoDTOList = BOFactoryWicket.getBO(AgendamentoFacade.class).consultarVagasDisponiveisAgendaExame(param);
            agendaGradeAtendimentoDTOList = new FiltrarAgendaGradePorPrioridade().filtrar(agendaGradeAtendimentoDTOList, param.getSolicitacaoAgendamento());

            AgendaCotaProfissional acp = null;
            Profissional profissionalAgendaList;
            Long cotaAgenda = Coalesce.asLong(agendaCota.getQuantidadeCota());
            List<AgendaGradeAtendimentoDTO> agendaListAux = new ArrayList<>();
            agendaListAux.addAll(agendaGradeAtendimentoDTOList);

            boolean existsAgendaCotaProfissional = AgendamentoHelper.existsAgendaCotaProfissional(agendaCota);

            for (AgendaGradeAtendimentoDTO agendaDTO : agendaListAux) {
                if (existsAgendaCotaProfissional) {
                    profissionalAgendaList = agendaDTO.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getProfissional();
                    acp = AgendamentoHelper.consultarAgendaCotaProfissional(agendaCota, profissionalAgendaList);
                    if (acp != null) {
                        Long cotaUtilizadaProfissional = BOFactoryWicket.getBO(AgendamentoFacade.class)
                                .countConsultarCotaProfissionalUtilizadaAgendamentos(acp, agendaDTO.getAgendaGradeAtendimento().getAgendaGrade().getData());
                        if (Coalesce.asLong(acp.getQuantidadeCota()) <= cotaUtilizadaProfissional) {
                            agendaGradeAtendimentoDTOList.remove(agendaDTO);
                        }
                    } else {
                        agendaGradeAtendimentoDTOList.remove(agendaDTO);
                    }
                } else {
                    Long cota = BOFactoryWicket.getBO(AgendamentoFacade.class)
                            .countConsultarCotaUtilizadaAgendamentos(agendaCota, agendaDTO.getAgendaGradeAtendimento().getAgendaGrade().getData());
                    if (cotaAgenda <= cota) {
                        agendaGradeAtendimentoDTOList.remove(agendaDTO);
                    }
                }
            }
        } catch (DAOException | ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        if (target != null) {
            tblAgendamentoSolicitacao.update(target);
            tblAgendamentos.update(target);
        }
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("agendamentoSolicitacao");
    }

    public Class getClasseVoltar() {
        return classeVoltar;
    }

    private void selecionarAgenda(AjaxRequestTarget target, AgendaGradeAtendimentoDTO dto) throws ValidacaoException, DAOException {
        carregarAgenda(dto);
        if (target != null) {
            tblAgendamentos.populate(target);
            tblAgendamentos.update(target);
        } else {
            tblAgendamentos.populate();
        }
    }

    private void carregarAgenda(AgendaGradeAtendimentoDTO dto) throws ValidacaoException, DAOException {
        agendamentosList = new ArrayList<AgendaGradeAtendimentoPacienteDTO>();

        List<AgendaGradeAtendimentoHorario> agahList = LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME))
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO))
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL))
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO, dto.getAgendaGradeAtendimento()))
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE, AgendaGradeAtendimentoHorario.STATUS_CANCELADO))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME), BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();

        for (AgendaGradeAtendimentoHorario agah : agahList) {
            AgendaGradeAtendimentoPacienteDTO agendaGradeAtendimentoPacienteDTO = new AgendaGradeAtendimentoPacienteDTO();
            agendaGradeAtendimentoPacienteDTO.setNomePaciente(agah.getUsuarioCadsus().getNomeSocial());

            agendamentosList.add(agendaGradeAtendimentoPacienteDTO);
        }
    }

    public List<IColumn> getColumnsAgenda() {
        List<IColumn> columns = new ArrayList<IColumn>();
        AgendaGradeAtendimentoPacienteDTO proxy = on(AgendaGradeAtendimentoPacienteDTO.class);

        columns.add(createColumn(bundle("paciente"), proxy.getNomePaciente()));

        return columns;
    }

    public ICollectionProvider getCollectionProviderAgenda() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object o) throws DAOException, ValidacaoException {
                return agendamentosList;
            }
        };
    }
    
    private boolean exibirRecomendacoes(Agenda agenda){
        return RepositoryComponentDefault.SIM_LONG.equals(agenda.getFlagVisualizaAgendar()) && agenda.getRecomendacoes() != null;
    }
    
    private void carregarRecomendacoes(AjaxRequestTarget target, Agenda agenda){
        if(exibirRecomendacoes(agenda)){
            info(target, bundle("recomendacoesX", agenda.getRecomendacoes()));
            updateNotificationPanel(target);
        } else {
            INotificationPanel findNotificationPanel = MessageUtil.findNotificationPanel(AgendamentoSolicitacaoCotaDiarioPage.this);
            if (findNotificationPanel != null) {
                getSession().getFeedbackMessages().clear();
                if (target != null) {
                    findNotificationPanel.updateNotificationPanel(target);
                }
            }
            
            getSession().getFeedbackMessages().clear();
            updateNotificationPanel(target);
        }
    }
    
    private void initDlgRecomendacoesAgendaAntesConfirmar(AjaxRequestTarget target, AgendaGradeAtendimentoDTO dto){
        if (dlgRecomendacoesAgendaAntesConfirmar == null) {
                addModal(target, dlgRecomendacoesAgendaAntesConfirmar = new DlgRecomendacoesAgendaAntesConfirmar(newModalId()) {
                    
                    @Override
                    public void onFechar(AjaxRequestTarget target, AgendaGradeAtendimentoDTO dto) throws ValidacaoException, DAOException {
                        initDlgSimNao(target, dto);
                        
                    }
                });
            }

        dlgRecomendacoesAgendaAntesConfirmar.show(target, dto);
    }
    
    public void initDlgSimNao(AjaxRequestTarget target, AgendaGradeAtendimentoDTO dto) {
        if (dlgConfirmacaoSimNaoRecomendacoes == null) {
            addModal(target, dlgConfirmacaoSimNaoRecomendacoes = new DlgConfirmacaoSimNao(newModalId(), 
                    bundle("msgConfirmarAgendamento_X", dto.getTipoProcedimento().getDescricao(),
                        dto.getAgendaGradeAtendimento().getAgendaGrade().getDescricaoDataHoraInicial(),
                        dto.getAgendaGradeAtendimento().getTipoAtendimentoAgenda().getDescricao())) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    agendarAction(target, (AgendaGradeAtendimentoDTO) getObject());
                }

                @Override
                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                }
            });
        }
        dlgConfirmacaoSimNaoRecomendacoes.setObject(dto);
        dlgConfirmacaoSimNaoRecomendacoes.show(target);
    }
}
