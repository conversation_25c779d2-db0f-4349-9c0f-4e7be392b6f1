package br.com.celk.view.hospital.faturamento.dialogs.ipe;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlLancamentosConfirmadosMedicamentosIpe extends Panel {

    private CompoundPropertyModel<ItemContaPaciente> model;
    private AbstractAjaxButton btnConfirmar;
    private AbstractAjaxButton btnFechar;
    private Table tabela;
    private List<ItemContaPaciente> listaItens;

    public PnlLancamentosConfirmadosMedicamentosIpe(String id) {
        super(id);
        init();
    }

    private void init() {

        add(tabela = new Table("tabela", getColumns(), getCollectionProvider()));
        tabela.populate();

        add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        btnFechar.setDefaultFormProcessing(false);

        setOutputMarkupId(true);
    }

    public List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ItemContaPaciente proxy = on(ItemContaPaciente.class);
        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("produto"), proxy.getProduto().getDescricao()));
        columns.add(createSortableColumn(bundle("quantidadeDias"), proxy.getQuantidadeDias()));
        columns.add(createSortableColumn(bundle("ocorrenciasDia"), proxy.getQuantidadePorDia()));
        columns.add(createSortableColumn(bundle("valor"), proxy.getPrecoUnitario()));
        columns.add(createSortableColumn(bundle("total"), proxy.getValorTotalIpe()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<ItemContaPaciente>() {
            @Override
            public void customizeColumn(ItemContaPaciente rowObject) {
                addAction(ActionType.REVERTER, rowObject, new IModelAction<ItemContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ItemContaPaciente modelObject) throws ValidacaoException, DAOException {
                        PnlLancamentosConfirmadosMedicamentosIpe.this.onReverter(target, modelObject);
                        removerItem(target, modelObject);
                        tabela.update(target);
                    }
                });
            }
        };
    }

    private void removerItem(AjaxRequestTarget target, ItemContaPaciente modelObject) {
        for (int i = 0; i < listaItens.size(); i++) {
            if (listaItens.get(i) == modelObject) {
                listaItens.remove(i);
                break;
            }
        }
        tabela.update(target);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return getListaItens();
            }
        };
    }

    public void setItemContaPaciente(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) {
        if (itemContaPaciente != null) {
            getListaItens().add(itemContaPaciente);
        }
        tabela.update(target);
        update(target);
    }

    public List<ItemContaPaciente> getListaItens() {
        if (listaItens == null) {
            listaItens = new ArrayList<ItemContaPaciente>();
        }
        return listaItens;
    }

    public void setListItemContaPaciente(AjaxRequestTarget target, List<ItemContaPaciente> lista) {
        if (CollectionUtils.isNotNullEmpty(lista)) {
            getListaItens().clear();
            getListaItens().addAll(lista);
        }
        tabela.update(target);
        update(target);
    }

    private void update(AjaxRequestTarget target) {
        target.add(this);
    }

    public abstract void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException;

    public abstract void onReverter(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException;
//    public abstract void onSetItem(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException;
}
