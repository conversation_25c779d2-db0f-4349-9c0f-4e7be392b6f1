package br.com.celk.view.vigilancia.registroagravo.enums;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;

/**
 * <AUTHOR>
 */

public enum AgenteAcidenteBiologicoEnum implements IEnum {
    AGULHA_LUMEN(1L, Bundle.getStringApplication("agulhaLumen")),
    AGULHO_SEM_LUMEN(2L, Bundle.getStringApplication("agulhaSemLumen")),
    INTRACATH(3L, Bundle.getStringApplication("intracath")),
    VIDROS(4L, Bundle.getStringApplication("vidros")),
    LAMINA_LACETA(5L, Bundle.getStringApplication("laminaLancete")),
    OUTROS(6L, Bundle.getStringApplication("outros")),
    IGNORADO(7L, Bundle.getStringApplication("ignorado"));

    private Long value;
    private String descricao;

    AgenteAcidenteBiologicoEnum(Long value, String descricao) {
        this.value = value;
        this.descricao = descricao;
    }

    @Override
    public Long value() {
        return value;
    }

    @Override
    public String descricao() {
        return descricao;
    }

    public static AgenteAcidenteBiologicoEnum valueOf(Long value) {
        for (AgenteAcidenteBiologicoEnum v : AgenteAcidenteBiologicoEnum.values()) {
            if (v.value().equals(value)) {
                return v;
            }
        }
        return null;
    }
}
