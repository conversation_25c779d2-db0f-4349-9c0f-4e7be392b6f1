package br.com.celk.view.hospital.faturamento.tiss;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.duracaofield.MesAnoField;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.tabbedpanel.cadastro.CadastroTab;
import br.com.celk.component.tabbedpanel.cadastro.ITabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.hospital.faturamento.dialogs.DlgNovaOcorrencia;
import br.com.celk.view.hospital.faturamento.tiss.tabbedpanel.*;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.FechamentoContaTissDTO;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAlta;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.OcorrenciaContaPaciente;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.tabs.ITab;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class FechamentoContaTissPage extends BasePage {

    private ContaPaciente contaPaciente;
    private InputField txtCompetencia;
    private InputField txtNumeroCarteira;
    private DlgNovaOcorrencia dlgNovaOcorrencia;
    private WebMarkupContainer containerOcorrencias;
    private Table<OcorrenciaContaPaciente> tblOcorrencias;
    private AbstractAjaxLink btnNovaOcorrencia;
    private List<OcorrenciaContaPaciente> ocorrencias = new ArrayList();
    private FechamentoContaTissDTO fechamentoContaTissDTO;
    private AutoCompleteConsultaCid autoCompleteConsultaCid;
    private DateChooser dchDataValidadeConvenio;
    private Date dataValidadeConvenio;
    private InputField txtDataSaida;
    private DropDown cbxMotivo;
    private InputField txtNumeroDN;
    private InputField txtDeclaracaoObito;
    private DropDown cbxIndicadorDecObitoRN;
    private DropDown cbxAtendimentoRN;
    private DropDown cbxAcidente;

    public FechamentoContaTissPage(ContaPaciente contaPaciente) {
        this.contaPaciente = contaPaciente;
        init();
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("fechamentoContaPacienteTiss");
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(contaPaciente));

        ContaPaciente proxy = on(ContaPaciente.class);

        form.add(new DisabledInputField(path(proxy.getAtendimentoInformacao().getEmpresa().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getAtendimentoInformacao().getUsuarioCadsus().getNomeSocial())));
        form.add(new DisabledInputField(path(proxy.getAtendimentoInformacao().getUsuarioCadsus().getIdade())));
        form.add(txtNumeroCarteira = new InputField(path(proxy.getAtendimentoInformacao().getAtendimentoPrincipal().getNumeroRegistroConvenio())));
        form.add(dchDataValidadeConvenio = new DateChooser(path(proxy.getAtendimentoInformacao().getAtendimentoPrincipal().getDataValidadeConvenio())));

        form.add(new DisabledInputField(path(proxy.getAtendimentoInformacao().getUsuarioCadsus().getCns())));
        form.add(new DisabledInputField(path(proxy.getAtendimentoInformacao().getUsuarioCadsus().getCpfFormatado())));
        form.add(new DisabledInputField(path(proxy.getAtendimentoInformacao().getDataChegada())));
        form.add(new DisabledInputField(path(proxy.getAtendimentoInformacao().getTipoAtendimentoFaturamento().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getAtendimentoInformacao().getLeitoQuarto().getQuartoInternacao().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getAtendimentoInformacao().getLeitoQuarto().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getConvenio().getDescricao())));
        form.add(txtCompetencia = new MesAnoField(path(proxy.getCompetenciaAtendimento())));

        form.add(new DisabledInputField(path(proxy.getAtendimentoInformacao().getProfissionalAlta().getNome())));
        form.add(txtDataSaida = new InputField(path(proxy.getAtendimentoInformacao().getDataSaida())));
        form.add(cbxMotivo = DropDownUtil.getIEnumDropDown(path(proxy.getAtendimentoInformacao().getMotivoAlta()), AtendimentoAlta.MotivoAlta.values(), true, false, true));
        form.add(autoCompleteConsultaCid = new AutoCompleteConsultaCid(path(proxy.getCid())));
        form.add(txtNumeroDN = new InputField(path(proxy.getDeclaracaoNascido())));
        form.add(txtDeclaracaoObito = new InputField(path(proxy.getDeclaracaoObito())));
        form.add(cbxIndicadorDecObitoRN = DropDownUtil.getNaoSimLongDropDown(path(proxy.getFlagDeclaracaoObitoRecemNascido()), true));
        form.add(cbxAtendimentoRN = DropDownUtil.getNaoSimLongDropDown(path(proxy.getAtendimentoInformacao().getAtendimentoRN())));
        form.add(getDropDownAcidente(path(proxy.getAtendimentoInformacao().getIndicadorAcidente())));

        form.add(containerOcorrencias = new WebMarkupContainer("containerOcorrencias"));
        containerOcorrencias.setOutputMarkupId(true);

        containerOcorrencias.add(btnNovaOcorrencia = new AbstractAjaxLink("btnNovaOcorrencia") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                viewDlgNovaOcorrencia(target);
            }
        });

        containerOcorrencias.add(tblOcorrencias = new Table("tblOcorrencias", getColumns(), getCollectionProvider()));
        carregarOcorrencias();
        tblOcorrencias.setOutputMarkupId(true);
        tblOcorrencias.populate();

        adicionaTabs(form);

        add(form);
        initControlComponents();
    }

    private void initControlComponents() {
        txtNumeroCarteira.setLabel(new Model<String>(bundle("numeroCarteira")));
        txtNumeroCarteira.addRequiredClass();

        dchDataValidadeConvenio.setLabel(new Model<String>(bundle("validade")));
        dchDataValidadeConvenio.getData().setMinDate(new DateOption(DataUtil.getDataAtual()));

        if (!TipoAtendimento.TipoInternacaoTiss.OBSTETRICA.value().equals(contaPaciente.getAtendimentoInformacao().getTipoAtendimentoFaturamento().getTipoInternacaoTiss())) {
            txtNumeroDN.setEnabled(false);
        } else {
            txtNumeroDN.addRequiredClass();
        }

        if (!AtendimentoAlta.MotivoAlta.OBITO.value().equals(contaPaciente.getAtendimentoInformacao().getMotivoAlta())) {
            txtDeclaracaoObito.setEnabled(false);
            cbxIndicadorDecObitoRN.setEnabled(false);
        } else {
            txtDeclaracaoObito.addRequiredClass();
            cbxIndicadorDecObitoRN.addRequiredClass();
        }

        if (!isActionPermitted(ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario(), Permissions.EDITAR, ConsultaFechamentoContaTissPage.class)) {
            txtCompetencia.setEnabled(false);
        }

        cbxMotivo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                boolean enabled = AtendimentoAlta.MotivoAlta.OBITO.value().equals(contaPaciente.getAtendimentoInformacao().getMotivoAlta());
                txtDeclaracaoObito.setEnabled(enabled);
                cbxIndicadorDecObitoRN.setEnabled(enabled);

                if (enabled) {
                    cbxIndicadorDecObitoRN.addRequiredClass();
                    txtDeclaracaoObito.addRequiredClass();
                } else {
                    cbxIndicadorDecObitoRN.removeRequiredClass();
                    txtDeclaracaoObito.removeRequiredClass();
                }

                txtDeclaracaoObito.limpar(target);
                cbxIndicadorDecObitoRN.limpar(target);
            }
        });

        txtNumeroCarteira.addAjaxUpdateValue();
        dchDataValidadeConvenio.addAjaxUpdateValue();
        autoCompleteConsultaCid.addAjaxUpdateValue();
        txtCompetencia.addAjaxUpdateValue();
        txtDataSaida.addAjaxUpdateValue();
        cbxMotivo.addAjaxUpdateValue();
        txtNumeroDN.addAjaxUpdateValue();
        txtDeclaracaoObito.addAjaxUpdateValue();
        cbxIndicadorDecObitoRN.addAjaxUpdateValue();
        cbxAtendimentoRN.addAjaxUpdateValue();
        cbxAcidente.addAjaxUpdateValue();
    }

    private DropDown getDropDownAcidente(String id) {
        if (cbxAcidente == null) {
            cbxAcidente = new DropDown(id);

            cbxAcidente.addChoice(AtendimentoInformacao.IndicadorAcidente.NAO_ACIDENTE.value(), AtendimentoInformacao.IndicadorAcidente.NAO_ACIDENTE.descricao());
            cbxAcidente.addChoice(AtendimentoInformacao.IndicadorAcidente.TRABALHO.value(), AtendimentoInformacao.IndicadorAcidente.TRABALHO.descricao());
            cbxAcidente.addChoice(AtendimentoInformacao.IndicadorAcidente.TRANSITO.value(), AtendimentoInformacao.IndicadorAcidente.TRANSITO.descricao());
            cbxAcidente.addChoice(AtendimentoInformacao.IndicadorAcidente.OUTROS.value(), AtendimentoInformacao.IndicadorAcidente.OUTROS.descricao());
        }

        return cbxAcidente;
    }

    private void viewDlgNovaOcorrencia(AjaxRequestTarget target) {
        if (dlgNovaOcorrencia == null) {
            addModal(target, dlgNovaOcorrencia = new DlgNovaOcorrencia(newModalId(), contaPaciente) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, OcorrenciaContaPaciente ocorrenciaContaPaciente) throws DAOException, ValidacaoException {
                    ocorrencias.add(0, ocorrenciaContaPaciente);
                    tblOcorrencias.update(target);
                    JScript.showFieldset(target, containerOcorrencias);
                }
            });
        }
        dlgNovaOcorrencia.show(target);
    }

    private void adicionaTabs(Form form) {
        getFechamentoContaTissDTO().setContaPaciente(contaPaciente);

        try {
            getFechamentoContaTissDTO().setLstItens(BOFactoryWicket.getBO(HospitalFacade.class).consultaItensContaTiss(contaPaciente.getCodigo()));
        } catch (DAOException ex) {
            Logger.getLogger(FechamentoContaTissPage.class.getName()).log(Level.SEVERE, null, ex);
        } catch (ValidacaoException ex) {
            Logger.getLogger(FechamentoContaTissPage.class.getName()).log(Level.SEVERE, null, ex);
        }

        List<ITab> tabs = new ArrayList<ITab>();
        tabs.add(new CadastroTab<FechamentoContaTissDTO>(fechamentoContaTissDTO) {
            @Override
            public ITabPanel newTabPanel(String panelId, FechamentoContaTissDTO dto) {
                return new MateriaisMedicamentosTissTab(panelId, dto);
            }
        });

        tabs.add(new CadastroTab<FechamentoContaTissDTO>(fechamentoContaTissDTO) {
            @Override
            public ITabPanel newTabPanel(String panelId, FechamentoContaTissDTO dto) {
                return new ProcedimentosTissTab(panelId, dto);
            }
        });

        tabs.add(new CadastroTab<FechamentoContaTissDTO>(fechamentoContaTissDTO) {
            @Override
            public ITabPanel newTabPanel(String panelId, FechamentoContaTissDTO dto) {
                return new ExamesTissTab(panelId, dto);
            }
        });

        tabs.add(new CadastroTab<FechamentoContaTissDTO>(fechamentoContaTissDTO) {
            @Override
            public ITabPanel newTabPanel(String panelId, FechamentoContaTissDTO dto) {
                return new HonorariosTissTab(panelId, dto);
            }
        });

        tabs.add(new CadastroTab<FechamentoContaTissDTO>(fechamentoContaTissDTO) {
            @Override
            public ITabPanel newTabPanel(String panelId, FechamentoContaTissDTO dto) {
                return new OutrasDespesasTissTab(panelId, dto);
            }
        });

        form.add(new FechamentoContaTissTabbedPanel("abas", getFechamentoContaTissDTO(), false, tabs));
    }

    private FechamentoContaTissDTO getFechamentoContaTissDTO() {
        if (fechamentoContaTissDTO == null) {
            fechamentoContaTissDTO = new FechamentoContaTissDTO();
        }

        return fechamentoContaTissDTO;
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();

        OcorrenciaContaPaciente proxy = on(OcorrenciaContaPaciente.class);

        columns.add(createColumn(bundle("data"), proxy.getData()));
        columns.add(createColumn(bundle("usuario"), proxy.getUsuario().getNome()));
        columns.add(createColumn(bundle("motivo"), proxy.getMotivoOcorrenciaContaPaciente().getDescricao()));
        columns.add(createColumn(bundle("descricao"), proxy.getDescricao()));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return ocorrencias;
            }
        };
    }

    private void carregarOcorrencias() {
        OcorrenciaContaPaciente proxy = on(OcorrenciaContaPaciente.class);

        ocorrencias = LoadManager.getInstance(OcorrenciaContaPaciente.class)
                .addProperties(new HQLProperties(OcorrenciaContaPaciente.class).getProperties())
                .addProperty(path(proxy.getUsuario().getNome()))
                .addProperty(path(proxy.getMotivoOcorrenciaContaPaciente().getDescricao()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getContaPaciente().getContaPacientePrincipal().getCodigo()), contaPaciente.getCodigo()))
                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getData()), QueryCustom.QueryCustomSorter.DECRESCENTE))
                .start().getList();
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);

        if (!ocorrencias.isEmpty()) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerOcorrencias)));
        }
    }
}
