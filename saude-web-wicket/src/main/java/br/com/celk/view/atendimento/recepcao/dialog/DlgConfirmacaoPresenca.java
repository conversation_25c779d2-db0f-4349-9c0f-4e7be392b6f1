package br.com.celk.view.atendimento.recepcao.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.util.ImagemAvatarHelper;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoHorarioDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 * <AUTHOR>
 */
public abstract class DlgConfirmacaoPresenca extends Window {

    private PnlConfirmacaoPresenca pnlConfirmacaoPresenca;

    public DlgConfirmacaoPresenca(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>() {
            @Override
            protected String load() {
                return BundleManager.getString("confirmacaoPresenca");
            }
        });

        setInitialWidth(700);
        setInitialHeight(180);
        setResizable(true);

        setContent(pnlConfirmacaoPresenca = new PnlConfirmacaoPresenca(getContentId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO dto) throws ValidacaoException, DAOException {
                DlgConfirmacaoPresenca.this.onFechar(target);
                DlgConfirmacaoPresenca.this.onConfirmar(target, dto);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                DlgConfirmacaoPresenca.this.onFechar(target);
            }

            @Override
            public void onBiometria(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO dto) throws ValidacaoException, DAOException {
                DlgConfirmacaoPresenca.this.onFechar(target);
                DlgConfirmacaoPresenca.this.onBiometria(target, dto);
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO dto) throws ValidacaoException, DAOException;
    
    public abstract void onBiometria(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO dto) throws ValidacaoException, DAOException;

    public void onFechar(AjaxRequestTarget target) {
        close(target);
    }

    public void show(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO dto) throws DAOException {
        super.show(target);
        pnlConfirmacaoPresenca.setDTO(target, dto);
        pnlConfirmacaoPresenca.setResourceImage(target, ImagemAvatarHelper.carregarAvatarResource(dto.getAgendaGradeAtendimentoHorario().getUsuarioCadsus()));
    }
}