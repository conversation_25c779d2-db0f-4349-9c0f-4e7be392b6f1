package br.com.celk.view.atendimento.consultaprontuario.panel.consultadocumentos;

import br.com.celk.component.action.IHtmlReportAction;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.report.HtmlReport;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.consultaprontuario.panel.template.ConsultaProntuarioCadastroPanel;
import br.com.celk.view.atendimento.prontuario.documentos.DocumentoHtmlQRCodeReport;
import br.com.celk.view.atendimento.prontuario.documentos.DocumentoHtmlReport;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.io.FileUtils;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioDocumentoEncaminhamentoProteseDTOParam;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.integracao.DocumentoAssinado;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.DocumentoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoDocumentoAtendimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.util.resource.FileResourceStream;
import org.apache.wicket.util.resource.IResourceStream;

import java.io.File;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.UUID;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class ConsultaDocumentosPanel extends ConsultaProntuarioCadastroPanel {

    private Form<DocumentoAtendimento> form;

    private WebMarkupContainer containerDocumentosHistorico;
    private Table tblDocumentosHistorico;

    private DatePeriod datePeriod;
    private Integer periodo;
    private AjaxPreviewBlank ajaxPreviewBlank;

    public ConsultaDocumentosPanel(String id) {
        super(id, bundle("documentos"));
    }

    @Override
    public void postConstruct() {
        super.postConstruct();

        form = new Form<DocumentoAtendimento>("form", new CompoundPropertyModel(new DocumentoAtendimento()));
        form.add(ajaxPreviewBlank = new AjaxPreviewBlank());

        form.add(containerDocumentosHistorico = new WebMarkupContainer("containerDocumentosHistorico", new CompoundPropertyModel(this)));
        containerDocumentosHistorico.setOutputMarkupId(true);
        containerDocumentosHistorico.add(getDropDownFiltroHistorico());
        periodo = 999;
        datePeriod = new DatePeriod(Data.removeMeses(DataUtil.getDataAtual(), periodo), DataUtil.getDataAtual());
        containerDocumentosHistorico.add(tblDocumentosHistorico = new Table("tblDocumentosHistorico", getColumnsHistorico(), getCollectionProviderHistorico()));
        tblDocumentosHistorico.populate();

        add(form);
    }

    private DropDown getDropDownFiltroHistorico() {
        DropDown dropDown = new DropDown("filtroHistorico", new PropertyModel(this, "periodo"));
        dropDown.addChoice(999, BundleManager.getString("todos", this));
        dropDown.addChoice(3, BundleManager.getString("ultimos3meses", this));
        dropDown.addChoice(6, BundleManager.getString("ultimos6meses", this));
        dropDown.addChoice(12, BundleManager.getString("ultimoAno", this));

        dropDown.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (periodo != 999) {
                    datePeriod = new DatePeriod(Data.removeMeses(DataUtil.getDataAtual(), periodo), DataUtil.getDataAtual());
                } else {
                    datePeriod = null;
                }
                tblDocumentosHistorico.populate(target);
            }
        });

        return dropDown;
    }

    private ICollectionProvider getCollectionProviderHistorico() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                LoadManager load = LoadManager.getInstance(DocumentoAtendimento.class)
                        .addProperties(new HQLProperties(DocumentoAtendimento.class).getProperties())
                        .addProperties(new HQLProperties(TipoDocumentoAtendimento.class, DocumentoAtendimento.PROP_TIPO_DOCUMENTO_ATENDIMENTO).getProperties())
                        .addProperties(new HQLProperties(DocumentoAssinado.class, DocumentoAtendimento.PROP_DOCUMENTO_ASSINADO).getProperties())
                        .addProperties(new HQLProperties(GerenciadorArquivo.class, VOUtils.montarPath(DocumentoAtendimento.PROP_DOCUMENTO_ASSINADO, DocumentoAssinado.PROP_GERENCIADOR_ARQUIVO_ASSINADO)).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DocumentoAtendimento.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS), getUsuarioCadsus()));
                if (datePeriod != null) {
                    load.addParameter(new QueryCustom.QueryCustomParameter(DocumentoAtendimento.PROP_DATA_CADASTRO, QueryCustom.QueryCustomParameter.MAIOR_IGUAL, datePeriod.getDataInicial()));
                    load.addParameter(new QueryCustom.QueryCustomParameter(DocumentoAtendimento.PROP_DATA_CADASTRO, QueryCustom.QueryCustomParameter.MENOR_IGUAL, datePeriod.getDataFinal()));
                }
                load.addSorter(new QueryCustom.QueryCustomSorter(DocumentoAtendimento.PROP_DATA_CADASTRO, QueryCustom.QueryCustomSorter.DECRESCENTE));

                return load.start().getList();
            }
        };
    }

    private List<IColumn> getColumnsHistorico() {
        List<IColumn> columns = new ArrayList<IColumn>();

        DocumentoAtendimento proxy = on(DocumentoAtendimento.class);

        columns.add(getCustomColumnHistorico());
        columns.add(new DateTimeColumn(bundle("data"), path(proxy.getDataCadastro())));
        columns.add(createColumn(bundle("tipoDocumento"), proxy.getDescricaoTipoDocumentoFormatado()));
        columns.add(createColumn(bundle("profissional"), proxy.getProfissional().getDescricaoFormatado()));

        return columns;
    }

    private IColumn getCustomColumnHistorico() {
        return new MultipleActionCustomColumn<DocumentoAtendimento>() {

            @Override
            public void customizeColumn(DocumentoAtendimento rowObject) {

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<DocumentoAtendimento>() {
                    @Override
                    public void action(AjaxRequestTarget target, DocumentoAtendimento modelObject) throws ValidacaoException, DAOException {
                        DetalhesDocumentoConsultaPanel detalhesDocumentoConsultaPanel = new DetalhesDocumentoConsultaPanel(getConsultaProntuarioController().panelId(), modelObject.getCodigo());
                        getConsultaProntuarioController().changePanel(target, detalhesDocumentoConsultaPanel);
                    }
                });

                if (rowObject.getDocumentoAssinado() != null && RepositoryComponentDefault.SIM_LONG.equals(rowObject.getDocumentoAssinado().getFlagAssinado())) {
                    addAction(ActionType.IMPRIMIR, rowObject, new IHtmlReportAction<DocumentoAtendimento>() {
                        @Override
                        public HtmlReport action(AjaxRequestTarget target, DocumentoAtendimento modelObject) throws DAOException, ValidacaoException {
                            try {
                                File f = File.createTempFile(UUID.randomUUID().toString(), ".pdf");
                                String path = rowObject.getDocumentoAssinado().getGerenciadorArquivoAssinado().getCaminho();
                                FileUtils.buscarArquivoFtp(path, f.getAbsolutePath());
                                IResourceStream resourceStream = new FileResourceStream(new org.apache.wicket.util.file.File(f));
                                ajaxPreviewBlank.initiate(target, rowObject.getDocumentoAssinado().getGerenciadorArquivoAssinado().getNomeArquivo(), resourceStream);
                            } catch (Exception e) {
                                Loggable.log.error(e);
                                throw new ValidacaoException(e);
                            }
                            return null;
                        }
                    });
                }
                else if (TipoDocumentoAtendimento.TipoRelatorio.HTML_REPORT.value().equals(rowObject.getTipoDocumentoAtendimento().getTipoRelatorio())) {
                    addAction(ActionType.IMPRIMIR, rowObject, new IHtmlReportAction<DocumentoAtendimento>() {
                        @Override
                        public HtmlReport action(AjaxRequestTarget target, DocumentoAtendimento documento) throws DAOException, ValidacaoException {
                            String parametroImprimeQrCodeAtestado = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("ImprimeQrCodeAtestado");
                            return (documento.getTipoDocumentoAtendimento().isAtestado() &&
                                    RepositoryComponentDefault.SIM.equals(parametroImprimeQrCodeAtestado)) ?
                                    new DocumentoHtmlQRCodeReport(documento)
                                    :
                                    new DocumentoHtmlReport(documento);
                        }
                    });
                } else {
                    addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<DocumentoAtendimento>() {
                        @Override
                        public DataReport action(DocumentoAtendimento modelObject) throws ReportException {
                            return imprimirDocumento(modelObject);
                        }
                    });
                }
            }
        };
    }

    public DataReport imprimirDocumento(DocumentoAtendimento object) throws ReportException {
        RelatorioDocumentoEncaminhamentoProteseDTOParam param = new RelatorioDocumentoEncaminhamentoProteseDTOParam();
        param.setCodigoDocumentoAtendimento(object.getCodigo());

        return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImprimirDocumentoEncaminhamentoProtese(param);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerDocumentosHistorico)));
    }
}
