package br.com.celk.view.unidadesaude.atividadegrupo.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.link.AjaxMultiReportLink;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.IReport;
import br.com.ksisolucoes.report.atividadegrupo.interfaces.dto.AtividadeDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlImpressaoBoletimAtividadeGrupo extends Panel{
    
    private final String IMG_WARN = "img-question";
    
    private Form<AtividadeDTOParam> form;
    
    public PnlImpressaoBoletimAtividadeGrupo(String id){
        super(id);
        init();
    }
    
    private void init() {
        form = new Form("form", new CompoundPropertyModel(new AtividadeDTOParam()));

        WebMarkupContainer image = new WebMarkupContainer("img");
        form.add(image);
        form.add(getDropDownNumeroPaginas());
        
        AjaxMultiReportLink btnImprimir = new AjaxMultiReportLink("btnImprimir") {
            @Override
            public List<IReport> getDataReports(AjaxRequestTarget target) throws ValidacaoException, DAOException, ReportException {
                return PnlImpressaoBoletimAtividadeGrupo.this.onImprimir(form.getModel().getObject());
            }

        };
        form.add(btnImprimir);
        
        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        Label labelImprimir = new Label("labelImprimir", BundleManager.getString("imprimir"));
        btnImprimir.add(labelImprimir);
        labelImprimir.setOutputMarkupId(true);
        
        add(form);
        image.add(new AttributeModifier("class", IMG_WARN));
    }
    
    private DropDown<Long> getDropDownNumeroPaginas(){
        DropDown dropDown = new DropDown("numeroPaginas");
        
        dropDown.addChoice(1L, "1");
        dropDown.addChoice(2L, "2");
        dropDown.addChoice(3L, "3");
        dropDown.addChoice(4L, "4");
        dropDown.addChoice(5L, "5");
        dropDown.addChoice(6L, "6");
        dropDown.addChoice(7L, "7");
        dropDown.addChoice(8L, "8");
        dropDown.addChoice(9L, "9");
        dropDown.addChoice(10L, "10");
                
        dropDown.addAjaxUpdateValue();
        
        return dropDown;
    }    
    
    public abstract List<IReport> onImprimir(AtividadeDTOParam param) throws ReportException;

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public void limpar(AjaxRequestTarget target){}
    
    public void setAtividadeGrupo(AtividadeGrupo atividadeGrupo) {
        AtividadeDTOParam dto = new AtividadeDTOParam();
        dto.setCodigoAtividade(atividadeGrupo.getCodigo());
        dto.setAtividadeGrupo(atividadeGrupo);

        form.getModel().setObject(dto);
    }
}
