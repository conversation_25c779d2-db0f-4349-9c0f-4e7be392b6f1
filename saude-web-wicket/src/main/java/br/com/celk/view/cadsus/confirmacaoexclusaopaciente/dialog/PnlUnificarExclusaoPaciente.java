package br.com.celk.view.cadsus.confirmacaoexclusaopaciente.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.UnificarCadastroPacienteDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDocumento;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusExclusao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlUnificarExclusaoPaciente extends Panel{
    
    private Form form;
    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;
    private UsuarioCadsus usuarioCadsus;
    private UsuarioCadsus usuarioCadsusDestino;
    private String nomeUsuarioCadsus;
    private InputField txtNome;
    private UnificarCadastroPacienteDTO dto;
    private UsuarioCadsusExclusao usuarioCadsusExclusao;
    
    public PnlUnificarExclusaoPaciente(String id){
        super(id);
        init();
    }

    private void init() {
        form = new Form("form");
        setOutputMarkupId(true);
        
        form.add(txtNome = new DisabledInputField("nomeUsuarioCadsus", new PropertyModel<String>(this, "nomeUsuarioCadsus")));
        form.add(autoCompleteConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus("usuarioCadsusDestino", new PropertyModel(this, "usuarioCadsusDestino")));
        autoCompleteConsultaUsuarioCadsus.add(new ConsultaListener<UsuarioCadsus>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus) {
                carregaDocumentos(usuarioCadsus);
            }
        });
        autoCompleteConsultaUsuarioCadsus.add(new RemoveListener<UsuarioCadsus>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, UsuarioCadsus object) {
                dto.setUsuarioCadsusDestino(usuarioCadsusDestino);
            }
        });
        
        form.add(new AbstractAjaxButton("btnConfirmar") {
            
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if(validarCadastro(target)){
                    onConfirmar(target, dto, usuarioCadsusExclusao);                    
                }
            }
        });
        
        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));
        
        add(form);
    }
    
    public boolean validarCadastro(AjaxRequestTarget target) {
        try {            
            if(autoCompleteConsultaUsuarioCadsus.getComponentValue() == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_paciente_destino"));
            }
            
            if (usuarioCadsus.getCodigo().equals(usuarioCadsusDestino.getCodigo())) {
                throw new ValidacaoException(bundle("msgPacienteOrigemDeveSerDiferentePacientePacienteDestino"));
            }
            
            if(!usuarioCadsus.getDataNascimento().equals(usuarioCadsusDestino.getDataNascimento())){
                throw new ValidacaoException(bundle("dataNascimentoDiferente"));
            }
        } catch (ValidacaoException e) {              
            MessageUtil.modalWarn(target, this, e);
            return false;
        }
        
        return true;
    }
   
    public abstract void onConfirmar(AjaxRequestTarget target, UnificarCadastroPacienteDTO dto, UsuarioCadsusExclusao usuarioCadsusExclusao) throws ValidacaoException, DAOException;
    
    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public void setObject(AjaxRequestTarget target, UsuarioCadsusExclusao usuarioCadsusExclusao){
        dto = new UnificarCadastroPacienteDTO();
        this.usuarioCadsusExclusao = usuarioCadsusExclusao;
        this.usuarioCadsus = usuarioCadsusExclusao.getUsuarioCadsus();
        this.usuarioCadsusDestino = null;
        txtNome.limpar(target);
        autoCompleteConsultaUsuarioCadsus.limpar(target);
        this.nomeUsuarioCadsus = usuarioCadsus.getNomeSocial();
        target.add(txtNome);
        target.focusComponent(autoCompleteConsultaUsuarioCadsus.getTxtDescricao().getTextField());
        target.add(autoCompleteConsultaUsuarioCadsus);
        
        List<UsuarioCadsus> usuarioCadsusOrigemList = new ArrayList<>();
        usuarioCadsusOrigemList.add(usuarioCadsusExclusao.getUsuarioCadsus());
        dto.setUsuarioCadsusOrigemList(usuarioCadsusOrigemList);
    }
    
    private void carregaDocumentos(UsuarioCadsus usuarioCadsus) {
        List<UsuarioCadsusDocumento> usuarioCadsusDocumentosList = LoadManager.getInstance(UsuarioCadsusDocumento.class)
            .addProperties(new HQLProperties(UsuarioCadsusDocumento.class).getProperties())
            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusDocumento.PROP_USUARIO_CADSUS), usuarioCadsus))
            .start().getList();

        dto.setUsuarioCadsusDestino(usuarioCadsusDestino);
        dto.getUsuarioCadsusDestino().setDocumentos(usuarioCadsusDocumentosList);
    }
}