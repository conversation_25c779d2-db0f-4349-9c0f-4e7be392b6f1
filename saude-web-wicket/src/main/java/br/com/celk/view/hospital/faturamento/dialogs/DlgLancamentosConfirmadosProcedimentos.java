package br.com.celk.view.hospital.faturamento.dialogs;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgLancamentosConfirmadosProcedimentos extends Window {

    private PnlLancamentosConfirmadosProcedimentos pnlLancamentosConfirmadosProcedimentos;

    public DlgLancamentosConfirmadosProcedimentos(String id) {
        super(id);
        init();
    }

    private void init() {
        setInitialHeight(400);
        setInitialWidth(1000);

        setResizable(true);

        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("lancamentosConfirmados");
            }
        });

        setContent(pnlLancamentosConfirmadosProcedimentos = new PnlLancamentosConfirmadosProcedimentos(getContentId()) {

            @Override
            public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                DlgLancamentosConfirmadosProcedimentos.this.onFechar(target);
                close(target);
            }

            @Override
            public void onReverter(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException {
                DlgLancamentosConfirmadosProcedimentos.this.onReverter(target, itemContaPaciente);
            }
        });
    }

    public List<ItemContaPaciente> getListaItens() {
        return pnlLancamentosConfirmadosProcedimentos.getListaItens();
    }

    public void setItemLista(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) {
        pnlLancamentosConfirmadosProcedimentos.setItemContaPaciente(target, itemContaPaciente);
    }
    
     public void setListItem(AjaxRequestTarget target, List<ItemContaPaciente> lista) {
        pnlLancamentosConfirmadosProcedimentos.setListItemContaPaciente(target, lista);
    }
     
    public abstract void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException;
    public abstract void onReverter(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException;
}
