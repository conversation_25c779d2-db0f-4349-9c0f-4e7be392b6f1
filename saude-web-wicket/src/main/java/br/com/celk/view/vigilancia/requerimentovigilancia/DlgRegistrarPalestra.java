package br.com.celk.view.vigilancia.requerimentovigilancia;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import java.util.Date;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgRegistrarPalestra extends Window {

    private PnlRegistrarPalestra pnlRegistrarPalestra;

    public DlgRegistrarPalestra(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(BundleManager.getString("registrarPalestra"));

        setInitialWidth(600);
        setInitialHeight(100);
        setResizable(true);

        setContent(pnlRegistrarPalestra = new PnlRegistrarPalestra(getContentId()) {

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, Profissional profissional, Date dataPalestra, RequerimentoVigilancia requerimentoVigilancia) throws ValidacaoException, DAOException {
                DlgRegistrarPalestra.this.onConfirmar(target, profissional, dataPalestra, requerimentoVigilancia);
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target, Profissional profissional, Date dataPalestra, RequerimentoVigilancia requerimentoVigilancia) throws ValidacaoException, DAOException;

    public void show(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia) {
        pnlRegistrarPalestra.setRequerimentoVigilancia(target, requerimentoVigilancia);
        super.show(target);
    }
}
