package br.com.celk.view.controle.parametros.panel.bnafar;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dialog.DlgConfirmacaoOk;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.passwordfield.PasswordField;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.SystemHelper;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.controle.parametros.panel.template.ParametrosCadastroPanel;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.bo.command.dynamodb.UsuariosBnafar;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Button;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import static br.com.celk.dynamodb.bnafar.BnafarDynamoHelper.buscaUsuarioPorTenant;
import static br.com.celk.system.methods.WicketMethods.bundle;

public class BnafarPanel extends ParametrosCadastroPanel {

    private DlgConfirmacaoOk dlgConfirmacaoOk;

    private Form<ParametroBnafarDTO> form;

    public BnafarPanel(String id) {
        super(id, bundle("bnafar"));
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        form = new Form("form", new CompoundPropertyModel(new ParametroBnafarDTO()));

        carregar();


        Button btnValidar = new AbstractAjaxButton("btnValidar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                validar(target);
            }
        };
        btnValidar.setDefaultFormProcessing(false);


        Button btnSalvar = new AbstractAjaxButton("btnGravar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                salvar();
            }
        };
        btnSalvar.setDefaultFormProcessing(false);

        Button btnExcluir = new AbstractAjaxButton("btnExcluir") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                excluir();
            }
        };
        btnExcluir.setDefaultFormProcessing(false);

        InputField codIbge = new InputField("codIbge");
        InputField loginBnafar = new InputField("loginBnafar");
        PasswordField passwordBnafar = new PasswordField("passwordBnafar");

        String bundleAmbiente = SystemHelper.isProducao()?"ambienteProducao":"ambienteApoio";
        Label lblAmbiente = new Label("lblAmbiente", BundleManager.getString(bundleAmbiente));


        codIbge.addAjaxUpdateValue();
        loginBnafar.addAjaxUpdateValue();
        passwordBnafar.addAjaxUpdateValue();


        codIbge.addRequiredClass();
        loginBnafar.addRequiredClass();
        passwordBnafar.addRequiredClass();

        form.add(codIbge);
        form.add(loginBnafar);
        form.add(passwordBnafar);
        form.add(btnValidar);
        form.add(lblAmbiente);
        form.add(btnSalvar);
        form.add(btnExcluir);

        add(form);
    }

    private void carregar() {
        UsuariosBnafar usuarioTenant = buscaUsuarioPorTenant();
        if(usuarioTenant != null){
            form.getModel().getObject().setCodIbge(usuarioTenant.getCodigoIbge());
            form.getModel().getObject().setLoginBnafar(usuarioTenant.getLogin());
            form.getModel().getObject().setPasswordBnafar(usuarioTenant.getSenha());
        }
    }
    private void validar(AjaxRequestTarget target) throws DAOException, ValidacaoException{
            String login = form.getModel().getObject().getLoginBnafar();
            String senha = form.getModel().getObject().getPasswordBnafar();
            if(StringUtils.isEmpty(login) || StringUtils.isEmpty(senha)){
                throw new ValidacaoException("Obrigatório informar o login e senha registrados na BNAFAR.");
            }
            if (BOFactory.getBO(MaterialBasicoFacade.class).obterTokenBnafar(login, senha)) {
                exibeDlgConfirmacaoOk(target, "credencial_bnafar_valida");
                Loggable.log.info("Credenciais bnafar validadas");
            } else {
                exibeDlgConfirmacaoOk(target, "credencial_bnafar_invalida");
                Loggable.log.info("Credenciais bnafar INVÁLIDAS");
            }
    }

    private void salvar() {
        //chamar inserção no dynamo
        Loggable.log.info("Chamar inserção no dynamo");

    }
    private void excluir() {
        //chamar exclusão no dynamo
        Loggable.log.info("Chamar exclusão no dynamo");
    }

    public ParametroBnafarDTO getFormObject() {
        return form.getModel().getObject();
    }



    private void exibeDlgConfirmacaoOk(AjaxRequestTarget target, String bundleKey) {
        WindowUtil.addModal(target, this, dlgConfirmacaoOk = new DlgConfirmacaoOk(WindowUtil.newModalId(this), Bundle.getStringApplication(bundleKey)) {
            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {}
        });
        dlgConfirmacaoOk.show(target);
    }




}
