package br.com.celk.view.atendimento.entregaSolicitacao;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgConfirmarEntregaSolicitacaoPaciente extends Window {

    private PnlConfirmarEntregaSolicitacaoPaciente pnlConfirmacaoEntrega;

    public DlgConfirmarEntregaSolicitacaoPaciente(String id) {
        super(id);
        init();
    }

    private void init() {
        setInitialHeight(155);
        setInitialWidth(600);

        setResizable(true);

        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("confirmacaoEntrega");
            }
        });

        setContent(pnlConfirmacaoEntrega = new PnlConfirmarEntregaSolicitacaoPaciente(getContentId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario, String responsavel) throws ValidacaoException, DAOException {
                close(target);
                DlgConfirmarEntregaSolicitacaoPaciente.this.onConfirmar(target, agendaGradeAtendimentoHorario, responsavel);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
                DlgConfirmarEntregaSolicitacaoPaciente.this.onFechar(target);
            }
            
        });
    }

    public void limpar(AjaxRequestTarget target) {
        pnlConfirmacaoEntrega.limpar(target);
    }

    public void show(AjaxRequestTarget target, AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario) {
        show(target);
        limpar(target);
        pnlConfirmacaoEntrega.setModelObject(target, agendaGradeAtendimentoHorario);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario, String responsavel) throws DAOException, ValidacaoException;
    public abstract void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException;
}
