package br.com.celk.view.hospital.faturamento.ipe;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlGuiaAtenidmento extends Panel {

    private AbstractAjaxButton btnFechar;
    private InputField<String> txtGuia;
    private String guia;
    private CompoundPropertyModel<ContaPaciente> model;
    private Form<ContaPaciente> form;

    public PnlGuiaAtenidmento(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        form = new Form<ContaPaciente>("form", model = new CompoundPropertyModel(new ContaPaciente()));
        ContaPaciente proxy = on(ContaPaciente.class);

        form.add(txtGuia = new RequiredInputField<String>(path(proxy.getNumeroGuiaAtendimento())));
        txtGuia.setLabel(new Model(BundleManager.getString("GuiaAtendimento")));

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onConfirmar(target, model.getObject());
                limpar(target);
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });

        add(form);

        btnFechar.setDefaultFormProcessing(false);

    }

    public void setContaPaciente(AjaxRequestTarget target, ContaPaciente contaPaciente) {
        this.model.setObject(contaPaciente);
        update(target);
    }

    private void update(AjaxRequestTarget target) {
        target.add(this);
    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public abstract void onConfirmar(AjaxRequestTarget target, ContaPaciente contaPaciente) throws ValidacaoException, DAOException;

    public void limpar(AjaxRequestTarget target) {
        this.txtGuia.limpar(target);
    }
}
