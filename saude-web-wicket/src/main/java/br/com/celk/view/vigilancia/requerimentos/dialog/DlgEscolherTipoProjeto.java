package br.com.celk.view.vigilancia.requerimentos.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 * <AUTHOR>
 */
public class DlgEscolherTipoProjeto extends Window {

    private PnlEscolherTipoProjeto pnlEscolherTipoProjeto;

    public DlgEscolherTipoProjeto(String id,
                                  Class clazz,
                                  boolean possuiPermissaoPBA,
                                  boolean possuiPermissaoVLTPBA,
                                  boolean possuiPermissaoAPH,
                                  boolean possuiPermissaoVHS,
                                  boolean possuiPermissaoAPHD,
                                  boolean possuiPermissaoHD,
                                  boolean possuiPermissaoPAS) {
        super(id);
        init(clazz, possuiPermissaoPBA, possuiPermissaoVLTPBA, possuiPermissaoAPH, possuiPermissaoVHS, possuiPermissaoAPHD, possuiPermissaoHD, possuiPermissaoPAS);
    }

    private void init(
            Class clazz,
            boolean possuiPermissaoPBA,
            boolean possuiPermissaoVLTPBA,
            boolean possuiPermissaoAPH,
            boolean possuiPermissaoVHS,
            boolean possuiPermissaoAPHD,
            boolean possuiPermissaoHD,
            boolean possuiPermissaoPAS) {
        setOutputMarkupId(true);

        setInitialWidth(550);
        setInitialHeight(360);
        setTitle(BundleManager.getString("tipoAnaliseProjeto"));

        setResizable(false);

        setContent(pnlEscolherTipoProjeto = new PnlEscolherTipoProjeto(getContentId(),
                clazz,
                possuiPermissaoPBA,
                possuiPermissaoVLTPBA,
                possuiPermissaoAPH,
                possuiPermissaoVHS,
                possuiPermissaoAPHD,
                possuiPermissaoHD,
                possuiPermissaoPAS) {
            @Override
            public void onFechar(AjaxRequestTarget target) {
                limpar(target);
                close(target);
            }
        });
    }

    public void update(AjaxRequestTarget target) {
        pnlEscolherTipoProjeto.update(target);
    }


    public void show(AjaxRequestTarget target, TipoSolicitacao tipoSolicitacaoSelecionado) {
        super.show(target);
        pnlEscolherTipoProjeto.setTipoSolicitacao(tipoSolicitacaoSelecionado);
    }
}
