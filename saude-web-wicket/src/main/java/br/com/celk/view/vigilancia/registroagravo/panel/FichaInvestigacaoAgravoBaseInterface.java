package br.com.celk.view.vigilancia.registroagravo.panel;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

public interface FichaInvestigacaoAgravoBaseInterface {
    void carregarFicha();
    void inicializarForm();
    String carregarInformacoesComplementares();
    void inicializarFicha();
    void inicializarRegrasComponentes(AjaxRequestTarget target);
    void adicionarLabels();
    void limparCamposInformacoesComplementares(AjaxRequestTarget target);
    void atualizarCampoInformacoesComplementares(String flag);
    void validarFicha() throws ValidacaoException;
    Object getFichaDTO();
    Object getEncerrarFichaDTO();
    void salvarFicha(Object fichaDTO) throws ValidacaoException, DAOException;
}
