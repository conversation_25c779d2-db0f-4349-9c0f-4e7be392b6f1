package br.com.celk.view.materiais.medicamento.tabbedpanel;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.hospital.convenio.autocomplete.AutoCompleteConsultaConvenio;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProdutoBrasindice;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.CadastroMedicamentoDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.InteracaoMedicamentosa;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.ProdutoBrasindice;
import br.com.ksisolucoes.vo.entradas.estoque.ProdutoConvenio;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class InteracaoMedicamentosaTab extends TabPanel<CadastroMedicamentoDTO> {

    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private InputField txtObservacao;
    private CompoundPropertyModel<InteracaoMedicamentosa> modelIteracaoMedicamentosa;
    private Table tblInteracaoMedicamentosa;

    public InteracaoMedicamentosaTab(String id, CadastroMedicamentoDTO cadastroMedicamentoDTO) {
        super(id, cadastroMedicamentoDTO);
        init();
    }

    public void init() {
        Form formInteracaoMedicamentosa = new Form("formProdutoConvenio", modelIteracaoMedicamentosa = new CompoundPropertyModel(new InteracaoMedicamentosa()));
        InteracaoMedicamentosa proxy = on(InteracaoMedicamentosa.class);

        formInteracaoMedicamentosa.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto(path(proxy.getProduto())));
        autoCompleteConsultaProduto.getTxtDescricao().addRequiredClass();
        formInteracaoMedicamentosa.add(txtObservacao = new InputField(path(proxy.getObservacao())));
        txtObservacao.addRequiredClass();

        formInteracaoMedicamentosa.add(new AbstractAjaxButton("btnAdicionarProduto") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarProduto(target);
            }

        });
        formInteracaoMedicamentosa.add(tblInteracaoMedicamentosa = new Table("tblInteracaoMedicamentosa", getColumns(), getCollectionProvider()));
        tblInteracaoMedicamentosa.populate();
        
        add(formInteracaoMedicamentosa);
        initComponents();
    }

    private void adicionarProduto(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        InteracaoMedicamentosa interacaoMedicamentosa = modelIteracaoMedicamentosa.getObject();
        if (interacaoMedicamentosa.getProduto() == null) {
            throw new ValidacaoException(bundle("informeMedicamento"));
        }
        if (interacaoMedicamentosa.getObservacao() == null) {
            throw new ValidacaoException(bundle("informeObservacao"));
        }
        if(object.getProduto().equals(interacaoMedicamentosa.getProduto())) {
            throw new ValidacaoException(bundle("produtoInteracaoMedicamentosaNaoPodeSerIgualMedicamentoCadastro"));
        }
        for (InteracaoMedicamentosa item : object.getInteracaoMedicamentosaList()) {
            if (item.getProduto().equals(interacaoMedicamentosa.getProduto())) {
                throw new ValidacaoException(bundle("medicamentoJaAdicionado"));
            }
        }

        object.getInteracaoMedicamentosaList().add(interacaoMedicamentosa);
        tblInteracaoMedicamentosa.update(target);
        modelIteracaoMedicamentosa.setObject(new InteracaoMedicamentosa());
        autoCompleteConsultaProduto.limpar(target);
        txtObservacao.limpar(target);
        target.focusComponent(autoCompleteConsultaProduto);
    }
    
    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();

        InteracaoMedicamentosa on = on(InteracaoMedicamentosa.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("medicamento"), on.getProduto().getDescricaoFormatado()));
        columns.add(createColumn(bundle("observacao"), on.getObservacao()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<InteracaoMedicamentosa>() {

            @Override
            public void customizeColumn(InteracaoMedicamentosa rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<InteracaoMedicamentosa>() {
                    @Override
                    public void action(AjaxRequestTarget target, InteracaoMedicamentosa modelObject) throws ValidacaoException, DAOException {
                        removerProduto(target, modelObject);
                    }
                });
                addAction(ActionType.CLONAR, rowObject, new IModelAction<InteracaoMedicamentosa>() {
                    @Override
                    public void action(AjaxRequestTarget target, InteracaoMedicamentosa modelObject) throws ValidacaoException, DAOException {
                      InteracaoMedicamentosa interacaoMedicamentosa = new InteracaoMedicamentosa();
                      interacaoMedicamentosa.setProduto(object.getProduto());
                      interacaoMedicamentosa.setProdutoPrincipal(modelObject.getProduto());
                      interacaoMedicamentosa.setObservacao(modelObject.getObservacao());

                      BOFactoryWicket.save(interacaoMedicamentosa);

                      tblInteracaoMedicamentosa.update(target);
                      target.focusComponent(autoCompleteConsultaProduto);
                    }
                }).setEnabled(!existsInteracao(rowObject));
            }
        };
    }

    private boolean existsInteracao(InteracaoMedicamentosa interacaoMedicamentosa) {
        return LoadManager.getInstance(InteracaoMedicamentosa.class)
                .addParameter(new QueryCustom.QueryCustomParameter(InteracaoMedicamentosa.PROP_PRODUTO_PRINCIPAL, interacaoMedicamentosa.getProduto()))
                .addParameter(new QueryCustom.QueryCustomParameter(InteracaoMedicamentosa.PROP_PRODUTO, interacaoMedicamentosa.getProdutoPrincipal()))
                .start().exists();
    }
    
    private void removerProduto(AjaxRequestTarget target, InteracaoMedicamentosa interacaoMedicamentosa) throws ValidacaoException, DAOException {
        CrudUtils.removerItem(target, tblInteracaoMedicamentosa, object.getInteracaoMedicamentosaList(), interacaoMedicamentosa);
    }
    
    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return object.getInteracaoMedicamentosaList();
            }

        };
    }
    
    private void initComponents() {
        if(object.getProduto() != null && object.getProduto().getCodigo() != null){
            List<InteracaoMedicamentosa> interacaoMedicamentosaList = LoadManager.getInstance(InteracaoMedicamentosa.class)
                    .addProperties(new HQLProperties(InteracaoMedicamentosa.class).getProperties())
                    .addProperties(new HQLProperties(Produto.class, InteracaoMedicamentosa.PROP_PRODUTO).getProperties())
                    .addParameter(new QueryCustomParameter(InteracaoMedicamentosa.PROP_PRODUTO_PRINCIPAL, object.getProduto()))
                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(InteracaoMedicamentosa.PROP_PRODUTO, Produto.PROP_DESCRICAO)))
                    .start().getList();
            
            if(CollectionUtils.isNotNullEmpty(interacaoMedicamentosaList)){
                object.setInteracaoMedicamentosaList(interacaoMedicamentosaList);
            }
        }
    }

    @Override
    public String getTitle() {
        return bundle("interacoesMedicamentosas");
    }
}
