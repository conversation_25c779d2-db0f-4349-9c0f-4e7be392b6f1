package br.com.celk.view.vigilancia.atividadeestabelecimento.customize;

import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.grupoestabelecimento.GrupoEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.SetorVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TabelaCnae;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaAtividadeEstabelecimento extends CustomizeConsultaAdapter{

    @Override
    public Class getClassConsulta() {
        return AtividadeEstabelecimento.class;
    }

    @Override
    public String[] getProperties() {
        return VOUtils.mergeProperties(
                new HQLProperties(AtividadeEstabelecimento.class).getProperties(),
                new HQLProperties(TabelaCnae.class, AtividadeEstabelecimento.PROP_TABELA_CNAE).getProperties(),
                new HQLProperties(GrupoEstabelecimento.class, AtividadeEstabelecimento.PROP_GRUPO_ESTABELECIMENTO).getProperties(),
                new HQLProperties(SetorVigilancia.class, AtividadeEstabelecimento.PROP_SETOR_VIGILANCIA).getProperties()
        );
    }
}
