package br.com.celk.view.vigilancia.requerimentovigilancia;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.checkbox.CheckBoxSimNao;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.link.AjaxActionMultiReportLink;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.SelectionPageableTable;
import br.com.celk.component.utils.ComponentUtils;
import br.com.celk.resources.Icon;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.financeiro.BoletoMultiploVigilanciaPage;
import br.com.celk.view.vigilancia.financeiro.BoletoVigilanciaPage;
import br.com.celk.view.vigilancia.financeiro.DlgEscolherVigilanciaFinanceiro;
import br.com.celk.view.vigilancia.financeiro.dialog.DlgAnexoComprovantePagamentoRequerimento;
import br.com.celk.view.vigilancia.helper.VigilanciaPageHelper;
import br.com.celk.view.vigilancia.profissional.autocomplete.AutoCompleteConsultaProfissionalVigilancia;
import br.com.celk.view.vigilancia.requerimentos.*;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.*;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.analisehidrossanitariodeclaratorio.RequerimentoHidrossanitarioDeclaratorioPage;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.habitesedeclaratorio.RequerimentoHabiteseDeclaratorioPage;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.projetoArquitetonicoSanitario.RequerimentoProjetoArquitetonicoParecerTecnicoPage;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.projetoArquitetonicoSanitario.RequerimentoProjetoArquitetonicoSanitarioPage;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.projetohidrossanitario.*;
import br.com.celk.view.vigilancia.requerimentos.autorizacaosanitaria.RequerimentoAutorizacaoSanitariaPage;
import br.com.celk.view.vigilancia.requerimentos.baixaveiculo.RequerimentoBaixaVeiculoPage;
import br.com.celk.view.vigilancia.requerimentos.contratosocial.RequerimentoAlteracaoEnderecoPage;
import br.com.celk.view.vigilancia.requerimentos.contratosocial.RequerimentoAlteracaoRazaoSocialPage;
import br.com.celk.view.vigilancia.requerimentos.contratosocial.RequerimentoAtividadeEconomicaPage;
import br.com.celk.view.vigilancia.requerimentos.contratosocial.RequerimentoRepresentanteLegalPage;
import br.com.celk.view.vigilancia.requerimentos.declaracaoveracidade.RequerimentoDeclaracaoVeracidadePage;
import br.com.celk.view.vigilancia.requerimentos.inspecaosanitaria.RequerimentoInspecaoSanitariaPage;
import br.com.celk.view.vigilancia.requerimentos.inspecaosanitaria.RequerimentoNovaInspecaoSanitariaPage;
import br.com.celk.view.vigilancia.requerimentos.licencatransporte.RequerimentoLicencaTransportePage;
import br.com.celk.view.vigilancia.requerimentos.receituario.RequerimentoReceitaAPage;
import br.com.celk.view.vigilancia.requerimentos.receituario.RequerimentoReceitaTalidomidaPage;
import br.com.celk.view.vigilancia.requerimentovigilancia.dialog.*;
import br.com.celk.view.vigilancia.responsabilidadetecnica.baixa.RequerimentoBaixaResponsabilidadeTecnicaPage;
import br.com.celk.view.vigilancia.responsabilidadetecnica.inclusao.RequerimentoInclusaoResponsabilidadePage;
import br.com.celk.view.vigilancia.responsabilidadetecnica.nadaconsta.RequerimentoCertidaoNadaConstaPage;
import br.com.celk.view.vigilancia.rotinas.autoIntimacao.CadastroAutoIntimacaoPage;
import br.com.celk.view.vigilancia.rotinas.autoinfracao.CadastroAutoInfracaoPage;
import br.com.celk.view.vigilancia.rotinas.automulta.CadastroAutoMultaPage;
import br.com.celk.view.vigilancia.rotinas.autopenalidade.CadastroAutoPenalidadePage;
import br.com.celk.view.vigilancia.rotinas.parecer.CadastroParecerPage;
import br.com.celk.view.vigilancia.setorvigilancia.autocomplete.AutoCompleteConsultaSetorVigilancia;
import br.com.celk.view.vigilancia.tipoenquadramentoprojeto.autocomplete.AutoCompleteConsultaTipoEnquadramentoProjetoMulti;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.*;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Button;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.util.resource.FileResourceStream;
import org.apache.wicket.util.resource.IResourceStream;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaRequerimentoVigilanciaPage extends BasePage {

    private Form<RequerimentoVigilanciaDTOParam> form;
    private WebMarkupContainer containerFiltros;
    private SelectionPageableTable<RequerimentoVigilanciaDTO> table;
    private QueryPagerProvider<RequerimentoVigilanciaDTO, RequerimentoVigilanciaDTOParam> dataProvider;
    private List<UsuarioSetorVigilancia> lstUsuSetor;
    private DropDown<Long> dropDownSituacao;
    private DropDown<Long> dropDownClassificacaoRisco;
    private DropDown<Long> dropDownVisualizarCancelados;
    private DlgCancelamentoFinalizacaoRequerimentoVigilancia dlgCancelamentoRequerimentoVigilancia;
    private DlgCancelamentoFinalizacaoRequerimentoVigilancia dlgFinalizacaoRequerimentoVigilancia;
    private DlgEntregaDocumentoRequerimentoVigilancia dlgEntregaDocumentoRequerimentoVigilancia;
    private WebMarkupContainer containerVisualizarTodosRequerimentos;
    private DlgImpressaoConsultaRequerimentoVigilancia dlgImpressaoConsultaRequerimentoVigilancia;
    private DlgOcorrenciaConsultaRequerimentoVigilancia dlgOcorrenciaConsultaRequerimentoVigilancia;
    private DlgAcoesRequerimentoVigilancia dlgAcoesRequerimentoVigilancia;
    private DlgRegistrarVisita dlgRegistrarVisita;
    private ProcurarButton btnProcurar;
    private RequerimentoVigilanciaDTO requerimentoVigilanciaDTO;
    private DlgAnexoComprovantePagamentoRequerimento dlgAnexoComprovantePagamentoRequerimento;
    private Class classeVoltar;
    private DlgAdicionarFiscaisRequerimento dlgAdicionarFiscaisRequerimento;
    private DlgEscolherVigilanciaFinanceiro<RequerimentoVigilanciaDTO> dlgEscolherVigilanciaFinanceiro;
    private DlgConfirmacaoSimNao<RequerimentoVigilancia> dlgConfirmacaoSimNaoCancelamento;
    private AjaxPreviewBlank ajaxPreviewBlank;
    private CheckBoxLongValue chkTodosEnquadramentos;
    private AutoCompleteConsultaTipoEnquadramentoProjetoMulti autoCompleteConsultaTipoEnquadramentoProjetoMulti;
    private DropDown<Long> dropVisualizartodos;
    private Button btnPadrao;
    private Button btnTodos;
    CheckBoxSimNao entregaDocumento;

    public ConsultaRequerimentoVigilanciaPage() {
        init();
        getForm().getModel().getObject().setSituacao(RequerimentoVigilancia.Situacao.PENDENTE.value());
    }

    public ConsultaRequerimentoVigilanciaPage(Class classeVoltar) {
        this.classeVoltar = classeVoltar;
        init();
    }

    public ConsultaRequerimentoVigilanciaPage(RequerimentoVigilanciaDTOParam param, Class classeVoltar) {
        getForm().getModel().setObject(param);
        this.classeVoltar = classeVoltar;
        init();
    }

    public ConsultaRequerimentoVigilanciaPage(Long situacao, Long situacaoOcorrencia, Long situacaoAprovacao, boolean considerarAprovacaoNula, Long situacaoAnaliseProjetos, Long visualizarTodos, Class classeVoltar) {
        this.classeVoltar = classeVoltar;
        if (situacao != null) {
            getForm().getModel().getObject().setSituacao(situacao);
            getForm().getModel().getObject().setSituacaoOcorrencia(situacaoOcorrencia);
            getForm().getModel().getObject().setSituacaoAprovacao(situacaoAprovacao);
            getForm().getModel().getObject().setConsiderarAprovacaoNula(considerarAprovacaoNula);
            getForm().getModel().getObject().setSituacaoAnaliseProjetos(situacaoAnaliseProjetos);
            getForm().getModel().getObject().setVisualizarTodosRequerimentos(visualizarTodos);
        }
        init();
    }

    private void init() {
        carregaLstUsuarioSetor();

        RequerimentoVigilanciaDTOParam proxy = on(RequerimentoVigilanciaDTOParam.class);

        getForm().add(DropDownUtil.getIEnumDropDown(path(proxy.getTipoDocumento()), TipoSolicitacao.TipoDocumento.values(), true, "", false, false, true));
        getForm().add(new AutoCompleteConsultaEstabelecimento(path(proxy.getEstabelecimento())));
        getForm().add(new AutoCompleteConsultaProfissionalVigilancia(path(proxy.getVigilanciaProfissional())));
        getForm().add(new InputField<String>(path(proxy.getLogradouro())));
        getForm().add(new InputField<String>(path(proxy.getBairro())));
        getForm().add(new InputField<String>(path(proxy.getNome())));
        getForm().add(new InputField<String>(path(proxy.getNumeroProtocolo())));
        getForm().add(dropDownVisualizarCancelados = DropDownUtil.getNaoSimLongDropDown(path(proxy.getVisualizarCancelados())));

        containerFiltros = new WebMarkupContainer("containerFiltros");
        containerFiltros.setOutputMarkupId(true);
        containerFiltros.add(new AutoCompleteConsultaSetorVigilancia(path(proxy.getSetorVigilancia())));
        containerFiltros.add(new InputField<String>(path(proxy.getSolicitante())));
        containerFiltros.add(new InputField<String>(path(proxy.getNumeroAutorizacao())));
        containerFiltros.add(getDropDownSituacao());
        containerFiltros.add(DropDownUtil.getIEnumDropDown(path(proxy.getOrigem()), RequerimentoVigilancia.Origem.values(), true, "", false, false, true));
        containerFiltros.add(entregaDocumento = new CheckBoxSimNao(path(proxy.getEntregaDocumento())));
        containerFiltros.add(DropDownUtil.getEnumDropDown(path(proxy.getTurno()), RequerimentoVigilancia.Turno.values(), bundle("todos")));
        containerFiltros.add(new InputField<String>(path(proxy.getCpf())));
        containerFiltros.add(new InputField<String>(path(proxy.getNumeroInscricaoImobiliaria())));
        containerFiltros.add(new InputField<String>(path(proxy.getNumeroProjetoAprovado())));
        containerFiltros.add(getDropDownClassificacaoRisco());
        containerFiltros.add(autoCompleteConsultaTipoEnquadramentoProjetoMulti = new AutoCompleteConsultaTipoEnquadramentoProjetoMulti("tipoEnquadramentoProjetoList"));
        autoCompleteConsultaTipoEnquadramentoProjetoMulti.setOutputMarkupId(true);
        autoCompleteConsultaTipoEnquadramentoProjetoMulti.setOutputMarkupPlaceholderTag(true);

        containerFiltros.add(chkTodosEnquadramentos = new CheckBoxLongValue(path(proxy.getTodosEnquadramentos())));
        chkTodosEnquadramentos.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                autoCompleteConsultaTipoEnquadramentoProjetoMulti.limpar(target);

                if (RepositoryComponentDefault.SIM_LONG.equals(form.getModel().getObject().getTodosEnquadramentos())) {
                    List<TipoEnquadramentoProjeto> tipoEnquadramentoProjetoList = LoadManager.getInstance(TipoEnquadramentoProjeto.class)
                            .addProperties(new HQLProperties(TipoEnquadramentoProjeto.class).getProperties())
                            .addSorter(new QueryCustom.QueryCustomSorter(TipoEnquadramentoProjeto.PROP_DESCRICAO, BuilderQueryCustom.QuerySorter.CRESCENTE))
                            .start().getList();

                    if (br.com.celk.util.CollectionUtils.isNotNullEmpty(tipoEnquadramentoProjetoList)) {
                        form.getModel().getObject().setTipoEnquadramentoProjetoList(tipoEnquadramentoProjetoList);
                    }
                } else {
                    form.getModel().getObject().setTipoEnquadramentoProjetoList(null);
                }

                target.add(autoCompleteConsultaTipoEnquadramentoProjetoMulti);
            }
        });
        chkTodosEnquadramentos.setOutputMarkupPlaceholderTag(true);
        chkTodosEnquadramentos.setOutputMarkupId(true);
        chkTodosEnquadramentos.addAjaxUpdateValue();

        containerVisualizarTodosRequerimentos = new WebMarkupContainer("containerVisualizarTodosRequerimentos");
        containerVisualizarTodosRequerimentos.setOutputMarkupId(true);
        containerVisualizarTodosRequerimentos.add(dropVisualizartodos = DropDownUtil.getNaoSimLongDropDown(path(proxy.getVisualizarTodosRequerimentos())));
        if (!isActionPermitted(Permissions.VISUALIZAR_TODOS)) {
            containerVisualizarTodosRequerimentos.setVisible(false);
        }

        containerFiltros.add(containerVisualizarTodosRequerimentos);
        getForm().add(containerFiltros);

        getForm().add(table = new SelectionPageableTable("table", getColumns(), getDataProvider(), 10));
        table.addSelectionAction(new ISelectionAction<RequerimentoVigilanciaDTO>() {
            @Override
            public void onSelection(AjaxRequestTarget target, RequerimentoVigilanciaDTO object) {
                if (!isPermitidoManipularRequerimentoOutroFiscal(object)) {
                    MessageUtil.modalWarn(target, table, new ValidacaoException(bundle("semPermissaoParaManipularRequerimento")));
                    return;
                }
                table.clearSelection(target);
                table.update(target);
                requerimentoVigilanciaDTO = object;
                initDlgAcoesRequerimentoVigilancia(target, object);
            }
        });
        table.populate();

        form.add(btnPadrao = new AbstractAjaxButton("btnPadrao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                setFiltrosPadrao(target);
            }
        });
        btnPadrao.setEnabled(true);

        form.add(btnTodos = new AbstractAjaxButton("btnTodos") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                setFiltrosTodos(target);
            }
        });
        btnTodos.setEnabled(true);

        form.add(btnProcurar = new ProcurarButton("btnProcurar", table) {

            @Override
            public void antesProcurar(AjaxRequestTarget target) throws ValidacaoException {
                getParam().setSituacaoOcorrencia(null);
                getParam().setSituacaoAprovacao(null);
                getParam().setConsiderarAprovacaoNula(false);
                getParam().setSituacaoAnaliseProjetos(null);
            }

            @Override
            public RequerimentoVigilanciaDTOParam getParam() {
                return (RequerimentoVigilanciaDTOParam) getForm().getModel().getObject();
            }
        });

        getForm().add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                voltar();
            }
        }.setDefaultFormProcessing(false).setVisible(classeVoltar != null));

        getForm().add(new AbstractAjaxButton("btnNovo") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new RequerimentosPage(ConsultaRequerimentoVigilanciaPage.class));
            }

            @Override
            public boolean isVisible() {
                return new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().getUsuario(), RequerimentosPage.class.getName());
            }
        });

        getForm().add(ajaxPreviewBlank = new AjaxPreviewBlank());

        add(getForm());

        btnProcurar.procurar();
    }

    private void setFiltrosTodos(AjaxRequestTarget target) {
        ComponentUtils.limparForm(getForm(), target);
        dropDownVisualizarCancelados.setComponentValue(RepositoryComponentDefault.SIM_LONG);
        dropDownVisualizarCancelados.setEnabled(true);
        dropVisualizartodos.setComponentValue(RepositoryComponentDefault.SIM_LONG);
        chkTodosEnquadramentos.setComponentValue(RepositoryComponentDefault.NAO_LONG);
        autoCompleteConsultaTipoEnquadramentoProjetoMulti.limpar(target);

        target.add(dropDownVisualizarCancelados);
        target.add(dropVisualizartodos);
        target.add(chkTodosEnquadramentos);
        target.add(autoCompleteConsultaTipoEnquadramentoProjetoMulti);
    }

    private void setFiltrosPadrao(AjaxRequestTarget target) {
        ComponentUtils.limparForm(getForm(), target);
        dropDownVisualizarCancelados.setComponentValue(RepositoryComponentDefault.NAO_LONG);
        dropDownVisualizarCancelados.setEnabled(true);
        dropVisualizartodos.setComponentValue(RepositoryComponentDefault.NAO_LONG);
        dropDownSituacao.setComponentValue(RequerimentoVigilancia.Situacao.PENDENTE.value());
        chkTodosEnquadramentos.setComponentValue(RepositoryComponentDefault.NAO_LONG);
        autoCompleteConsultaTipoEnquadramentoProjetoMulti.limpar(target);

        target.add(dropDownVisualizarCancelados);
        target.add(dropVisualizartodos);
        target.add(dropDownSituacao);
        target.add(chkTodosEnquadramentos);
        target.add(autoCompleteConsultaTipoEnquadramentoProjetoMulti);
    }

    private void voltar() {
        setResponsePage(classeVoltar);
    }

    private Form<RequerimentoVigilanciaDTOParam> getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel(new RequerimentoVigilanciaDTOParam()));
        }
        return form;
    }

    public DropDown<Long> getDropDownClassificacaoRisco() {
        if (dropDownClassificacaoRisco == null) {
            dropDownClassificacaoRisco = new DropDown<>("classificacaoRisco");
            dropDownClassificacaoRisco.addChoice(null, BundleManager.getString("todos"));
            dropDownClassificacaoRisco.addChoice(VigilanciaAltoBaixoRisco.ClassificacaoRisco.ALTO_RISCO.value(), VigilanciaAltoBaixoRisco.ClassificacaoRisco.ALTO_RISCO.descricao());
            dropDownClassificacaoRisco.addChoice(VigilanciaAltoBaixoRisco.ClassificacaoRisco.BAIXO_RISCO.value(), VigilanciaAltoBaixoRisco.ClassificacaoRisco.BAIXO_RISCO.descricao());

            dropDownClassificacaoRisco.setOutputMarkupId(true);
            dropDownClassificacaoRisco.setOutputMarkupPlaceholderTag(true);
            dropDownClassificacaoRisco.addAjaxUpdateValue();
        }
        return dropDownClassificacaoRisco;
    }

    public DropDown<Long> getDropDownSituacao() {
        if (dropDownSituacao == null) {
            dropDownSituacao = new DropDown<>("situacao");
            dropDownSituacao.addChoice(null, BundleManager.getString("todos"));
            dropDownSituacao.addChoice(RequerimentoVigilancia.Situacao.PENDENTE.value(), RequerimentoVigilancia.Situacao.PENDENTE.descricao());
            dropDownSituacao.addChoice(RequerimentoVigilancia.Situacao.PENDENTE_AGUARDANDO_PAGAMENTO.value(), RequerimentoVigilancia.Situacao.PENDENTE_AGUARDANDO_PAGAMENTO.descricao());
            ConfiguracaoVigilancia configuracaoVigilancia = null;
            try {
                configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
            } catch (ValidacaoException e) {
                Loggable.log.error(e.getMessage(), e);
            }
            if (configuracaoVigilancia != null && ConfiguracaoVigilanciaEnum.TipoGestaoRequerimento.FISCAL.value().equals(configuracaoVigilancia.getFlagTipoGestaoRequerimento())) {
                dropDownSituacao.addChoice(RequerimentoVigilancia.Situacao.PENDENTE_AGUARDANDO_INFORMAR_FISCAL.value(), RequerimentoVigilancia.Situacao.PENDENTE_AGUARDANDO_INFORMAR_FISCAL.descricao());
            }
            dropDownSituacao.addChoice(RequerimentoVigilancia.Situacao.PENDENTE_AGUARDANDO_ANALISE.value(), RequerimentoVigilancia.Situacao.PENDENTE_AGUARDANDO_ANALISE.descricao());
            dropDownSituacao.addChoice(RequerimentoVigilancia.Situacao.ANALISE.value(), RequerimentoVigilancia.Situacao.ANALISE.descricao());
            dropDownSituacao.addChoice(RequerimentoVigilancia.Situacao.FINALIZADO.value(), RequerimentoVigilancia.Situacao.FINALIZADO.descricao());
            dropDownSituacao.addChoice(RequerimentoVigilancia.Situacao.DEFERIDO.value(), RequerimentoVigilancia.Situacao.DEFERIDO.descricao());
            dropDownSituacao.addChoice(RequerimentoVigilancia.Situacao.INDEFERIDO.value(), RequerimentoVigilancia.Situacao.INDEFERIDO.descricao());
            dropDownSituacao.addChoice(RequerimentoVigilancia.Situacao.CANCELADO.value(), RequerimentoVigilancia.Situacao.CANCELADO.descricao());

            dropDownSituacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownVisualizarCancelados.limpar(target);
                    if (getForm().getModel().getObject().getSituacao() != null) {
                        dropDownVisualizarCancelados.setEnabled(false);
                        if (RequerimentoVigilancia.Situacao.CANCELADO.value().equals(dropDownSituacao.getComponentValue())) {
                            dropDownVisualizarCancelados.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                        }
                    } else {
                        dropDownVisualizarCancelados.setEnabled(true);
                    }
                    target.add(dropDownVisualizarCancelados);
                }
            });
        }
        return dropDownSituacao;
    }

    private List<ISortableColumn<RequerimentoVigilanciaDTO>> getColumns() {
        List columns = new ArrayList<>();
        RequerimentoVigilanciaDTO proxy = on(RequerimentoVigilanciaDTO.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(BundleManager.getString("dataRequerimento"), proxy.getRequerimentoVigilancia().getDataRequerimento()));
        columns.add(createSortableColumn(BundleManager.getString("protocolo"), proxy.getRequerimentoVigilancia().getProtocolo(), proxy.getRequerimentoVigilancia().getProtocoloFormatado()));
        columns.add(createColumn(BundleManager.getString("estabelecimentoPessoaProfissional"), proxy.getRequerimentoVigilancia().getEstabelecimentoPessoaProfissionalFormatado()));
        columns.add(createColumn(BundleManager.getString("funcionamento"), proxy.getRequerimentoVigilancia().getEstabelecimento().getDescricaoFuncionamento()));
        columns.add(createSortableColumn(BundleManager.getString("solicitacao"), proxy.getRequerimentoVigilancia().getTipoDocumento(), proxy.getRequerimentoVigilancia().getDescricaoTipoDocumentoComPlaca()));
        columns.add(createColumn(BundleManager.getString("setorResponsavel"), proxy.getDescricaoSetorResponsavel()));
        columns.add(createSortableColumn(BundleManager.getString("situacao"), proxy.getRequerimentoVigilancia().getSituacao(), proxy.getRequerimentoVigilancia().getDescricaoSituacao()));
        columns.add(createSortableColumn(BundleManager.getString("origem"), proxy.getRequerimentoVigilancia().getOrigem(), proxy.getRequerimentoVigilancia().getDescricaoOrigem()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<RequerimentoVigilanciaDTO>() {
            @Override
            public void customizeColumn(RequerimentoVigilanciaDTO rowObject) {

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<RequerimentoVigilanciaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, RequerimentoVigilanciaDTO modelObject) throws ValidacaoException, DAOException {
                        responsePage(target, modelObject.getRequerimentoVigilancia(), false);
                    }
                });

                addAction(ActionType.IMPRIMIR, rowObject, new AjaxActionMultiReportLink<RequerimentoVigilanciaDTO>() {
                    @Override
                    public List<DataReport> getDataReports(AjaxRequestTarget target, RequerimentoVigilanciaDTO modelObject) throws ValidacaoException, DAOException, ReportException {
                        RequerimentoVigilanciaDTO rv = modelObject;
                        Usuario usuarioAlteracao = rv.getRequerimentoVigilancia().getUsuarioAlteracao();
                        if (usuarioAlteracao != null && usuarioAlteracao.getNome() != null) {
                            usuarioAlteracao.setNome(StringUtils.capitalize(usuarioAlteracao.getNome()));
                            rv.getRequerimentoVigilancia().setUsuarioAlteracao(usuarioAlteracao);
                        }
                        return responseImpression(target, rv.getRequerimentoVigilancia());
                    }
                }).setVisible(isVisibleImpressao(rowObject));

                addAction(ActionType.CONJUNTO, rowObject, new IModelAction<RequerimentoVigilanciaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, RequerimentoVigilanciaDTO modelObject) {
                        if (!isPermitidoManipularRequerimentoOutroFiscal(modelObject)) {
                            MessageUtil.modalWarn(target, table, new ValidacaoException(bundle("semPermissaoParaManipularRequerimento")));
                            return;
                        }
                        requerimentoVigilanciaDTO = modelObject;
                        initDlgAcoesRequerimentoVigilancia(target, modelObject);
                    }
                }).setIcon(Icon.ROUND_PLUS).setTitleBundleKey("maisAcoes");
            }
        };
    }

    private boolean isVisibleImpressao(RequerimentoVigilanciaDTO rowObject) {
        return isActionPermitted(Permissions.IMPRIMIR) && (enablePrint(rowObject.getRequerimentoVigilancia())
                && (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(rowObject.getRequerimentoVigilancia().getSituacao())
                || RequerimentoVigilancia.Situacao.FINALIZADO.value().equals(rowObject.getRequerimentoVigilancia().getSituacao())
                || RequerimentoVigilancia.Situacao.EM_INSPECAO.value().equals(rowObject.getRequerimentoVigilancia().getSituacao())
                || RequerimentoVigilancia.Situacao.EM_REINSPECAO.value().equals(rowObject.getRequerimentoVigilancia().getSituacao())
                || RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(rowObject.getRequerimentoVigilancia().getSituacao())
                || RequerimentoVigilancia.Situacao.ANALISE.value().equals(rowObject.getRequerimentoVigilancia().getSituacao())
                || RequerimentoVigilancia.Situacao.INDEFERIDO.value().equals(rowObject.getRequerimentoVigilancia().getSituacao()))
                || habilitarImprimirDiferenteCanceladoIndeferido(rowObject.getRequerimentoVigilancia()));
    }

    private boolean enablePrint(RequerimentoVigilancia rv) {
        Long td = rv.getTipoDocumento();
        if (td != null) {
            List<Long> tiposDocumentos = Arrays.asList(
                    TipoSolicitacao.TipoDocumento.EXUMACAO_RESTOS_MORTAIS.value(),
                    TipoSolicitacao.TipoDocumento.ALVARA_PARTICIPANTE_EVENTO.value(),
                    TipoSolicitacao.TipoDocumento.ALVARA_CADASTRO_EVENTO.value(),
                    TipoSolicitacao.TipoDocumento.ALVARA_INICIAL.value(),
                    TipoSolicitacao.TipoDocumento.ALVARA_REVALIDACAO.value(),
                    TipoSolicitacao.TipoDocumento.LICENCA_TRANSPORTE.value(),
                    TipoSolicitacao.TipoDocumento.ABERTURA_LIVRO_CONTROLE.value(),
                    TipoSolicitacao.TipoDocumento.FECHAMENTO_LIVRO_CONTROLE.value(),
                    TipoSolicitacao.TipoDocumento.CERTIDAO_NADA_CONSTA.value(),
                    TipoSolicitacao.TipoDocumento.REQUISICAO_RECEITUARIO_A.value(),
                    TipoSolicitacao.TipoDocumento.REQUISICAO_RECEITUARIO_B.value(),
                    TipoSolicitacao.TipoDocumento.REQUISICAO_RECEITUARIO_TALIDOMIDA.value(),
                    TipoSolicitacao.TipoDocumento.VACINACAO_EXTRAMURO.value(),
                    TipoSolicitacao.TipoDocumento.ANALISE_PROJETOS.value(),
                    TipoSolicitacao.TipoDocumento.PROJETO_BASICO_ARQUITETURA.value(),
                    TipoSolicitacao.TipoDocumento.VISTORIA_LAUDO_CONFORMIDADE_PBA.value(),
                    TipoSolicitacao.TipoDocumento.ANALISE_PROJETO_HIDROSSANITARIO.value(),
                    TipoSolicitacao.TipoDocumento.HABITE_SE_DECLARATORIO.value(),
                    TipoSolicitacao.TipoDocumento.VISTORIA_HABITESE_SANITARIO.value(),
                    TipoSolicitacao.TipoDocumento.BAIXA_ESTABELECIMENTO.value(),
                    TipoSolicitacao.TipoDocumento.BAIXA_VEICULO.value(),
                    TipoSolicitacao.TipoDocumento.TREINAMENTOS_ALIMENTO.value(),
                    TipoSolicitacao.TipoDocumento.DENUNCIA_RECLAMACAO.value(),
                    TipoSolicitacao.TipoDocumento.ANALISE_PROJETO_HIDROSSANITARIO_DECLARATORIO.value(),
                    TipoSolicitacao.TipoDocumento.PROJETO_ARQUITETONICO_SANITARIO.value(),
                    TipoSolicitacao.TipoDocumento.AUTORIZACAO_SANITARIA.value(),
                    TipoSolicitacao.TipoDocumento.ALTERACAO_ENDERECO.value(),
                    TipoSolicitacao.TipoDocumento.LICENCA_SANITARIA.value(),
                    TipoSolicitacao.TipoDocumento.DISPENSA_SANITARIA.value(),
                    TipoSolicitacao.TipoDocumento.REVALIDACAO_LICENCA_SANITARIA.value()
            );
            return tiposDocumentos.contains(td);
        }
        return false;
    }

    private boolean habilitarImprimirDiferenteCanceladoIndeferido(RequerimentoVigilancia rv) {
        return enablePrintDifferentCanceledRejected(rv)
                && !RequerimentoVigilancia.Situacao.CANCELADO.value().equals(rv.getSituacao()) && !RequerimentoVigilancia.Situacao.INDEFERIDO.value().equals(rv.getSituacao());
    }

    private boolean enablePrintDifferentCanceledRejected(RequerimentoVigilancia rv) {
        Long td = rv.getTipoDocumento();
        if (td != null) {
            List<Long> tiposDocumentos = Arrays.asList(
                    TipoSolicitacao.TipoDocumento.ALTERACAO_RESPONSABILIDADE_LEGAL.value(),
                    TipoSolicitacao.TipoDocumento.ALTERACAO_ATIVIDADE_ECONOMICA.value(),
                    TipoSolicitacao.TipoDocumento.DECLARACAO_VISA_PRODUTOS.value(),
                    TipoSolicitacao.TipoDocumento.DECLARACAO_VISA_ISENCAO_TAXAS.value(),
                    TipoSolicitacao.TipoDocumento.DECLARACAO_VISA_OUTROS.value(),
                    TipoSolicitacao.TipoDocumento.ALTERACAO_RESPONSABILIDADE_LEGAL.value(),
                    TipoSolicitacao.TipoDocumento.ALTERACAO_ATIVIDADE_ECONOMICA.value(),
                    TipoSolicitacao.TipoDocumento.BAIXA_RESPONSABILIDADE_TECNICA.value(),
                    TipoSolicitacao.TipoDocumento.ENTRADA_RESPONSABILIDADE_TECNICA.value(),
                    TipoSolicitacao.TipoDocumento.ALTERACAO_ENDERECO.value(),
                    TipoSolicitacao.TipoDocumento.ALTERACAO_RAZAO_SOCIAL.value(),
                    TipoSolicitacao.TipoDocumento.DECLARACAO_CARTORIO.value(),
                    TipoSolicitacao.TipoDocumento.PRORROGACAO_PRAZO.value(),
                    TipoSolicitacao.TipoDocumento.INSPECAO_SANITARIA_AFE_ANVISA.value(),
                    TipoSolicitacao.TipoDocumento.INSPECAO_SANITARIA_COMUM.value(),
                    TipoSolicitacao.TipoDocumento.PEDIDO_DOCUMENTO.value(),
                    TipoSolicitacao.TipoDocumento.VACINACAO_EXTRAMURO.value(),
                    TipoSolicitacao.TipoDocumento.ANALISE_PROJETOS.value(),
                    TipoSolicitacao.TipoDocumento.BAIXA_ESTABELECIMENTO.value(),
                    TipoSolicitacao.TipoDocumento.BAIXA_VEICULO.value(),
                    TipoSolicitacao.TipoDocumento.TREINAMENTOS_ALIMENTO.value(),
                    TipoSolicitacao.TipoDocumento.AUTORIZACAO_SANITARIA.value(),
                    TipoSolicitacao.TipoDocumento.ALTERACAO_ENDERECO.value(),
                    TipoSolicitacao.TipoDocumento.LICENCA_SANITARIA.value(),
                    TipoSolicitacao.TipoDocumento.DISPENSA_SANITARIA.value(),
                    TipoSolicitacao.TipoDocumento.REVALIDACAO_LICENCA_SANITARIA.value()
            );
            return tiposDocumentos.contains(td);
        }
        return false;
    }

    private void responsePage(AjaxRequestTarget target, RequerimentoVigilancia rv, boolean edicao) throws ValidacaoException {
        Long td = rv.getTipoDocumento();
        rv.getTipoSolicitacao().setTipoDocumento(rv.getTipoDocumento());
        if (td != null) {
            TipoSolicitacao.TipoDocumento tipoDoc = TipoSolicitacao.TipoDocumento.valueOf(td);

            if (tipoDoc != null) {
                switch (TipoSolicitacao.TipoDocumento.valueOf(td)) {
                    case ANALISE_PROJETOS:
                        setResponsePage(new RequerimentoAnaliseProjetosPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class, isActionPermitted(Permissions.PERMITIR_EDITAR_ANALISE_PROJETO)));
                        break;
                    case PROJETO_BASICO_ARQUITETURA:
                        setResponsePage(new RequerimentoAnaliseProjetosPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class, isActionPermitted(Permissions.PERMITIR_EDITAR_ANALISE_PROJETO)));
                        break;
                    case EXUMACAO_RESTOS_MORTAIS:
                        setResponsePage(new RequerimentoExumacaoPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case ALVARA_PARTICIPANTE_EVENTO:
                        setResponsePage(new RequerimentoEventoPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class, isActionPermitted(Permissions.PERMITIR_EDITAR_ALVARAS)));
                        break;
                    case ALVARA_CADASTRO_EVENTO:
                        setResponsePage(new RequerimentoCadastroEventoPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class, isActionPermitted(Permissions.PERMITIR_EDITAR_ALVARAS)));
                        break;
                    case ALVARA_INICIAL:
                        setResponsePage(new RequerimentoAlvaraInicialPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class, isActionPermitted(Permissions.PERMITIR_EDITAR_ALVARAS)));
                        break;
                    case REVALIDACAO_LICENCA_SANITARIA:
                    case ALVARA_REVALIDACAO:
                        setResponsePage(new RequerimentoRevalidacaoAlvaraPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class, isActionPermitted(Permissions.PERMITIR_EDITAR_ALVARAS)));
                        break;
                    case LICENCA_TRANSPORTE:
                        setResponsePage(new RequerimentoLicencaTransportePage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case AUTORIZACAO_SANITARIA:
                        setResponsePage(new RequerimentoAutorizacaoSanitariaPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case ABERTURA_LIVRO_CONTROLE:
                        setResponsePage(new RequerimentoLivroAberturaPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case FECHAMENTO_LIVRO_CONTROLE:
                        setResponsePage(new RequerimentoLivroFechamentoPage(rv, false, false, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case PRORROGACAO_PRAZO:
                        responsePageProrrogacaoPrazo(target, rv, edicao);
                        break;
                    case INSPECAO_SANITARIA_AFE_ANVISA:
                        if(VigilanciaHelper.usarRequerimentoNovaInspecaoSanitaria()){
                            setResponsePage(new RequerimentoNovaInspecaoSanitariaPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class, isActionPermitted(Permissions.PERMITIR_EDITAR_ALVARAS)));
                        }else{
                            setResponsePage(new RequerimentoInspecaoSanitariaPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class, true, isActionPermitted(Permissions.PERMITIR_EDITAR_ALVARAS)));
                        }
                        break;
                    case INSPECAO_SANITARIA_COMUM:
                        setResponsePage(new RequerimentoInspecaoSanitariaPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class, false, isActionPermitted(Permissions.PERMITIR_EDITAR_ALVARAS)));
                        break;
                    case CERTIDAO_NADA_CONSTA:
                        setResponsePage(new RequerimentoCertidaoNadaConstaPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case DECLARACAO_VISA_PRODUTOS:
                        setResponsePage(new RequerimentoDeclaracaoVisaProdutosPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case DECLARACAO_VISA_ISENCAO_TAXAS:
                        setResponsePage(new RequerimentoDeclaracaoVisaIsencaoTaxasPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case DECLARACAO_VISA_OUTROS:
                        setResponsePage(new RequerimentoDeclaracaoVisaOutrosPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case DECLARACAO_CARTORIO:
                        setResponsePage(new RequerimentoDeclaracaoVeracidadePage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case ALTERACAO_RESPONSABILIDADE_LEGAL:
                        setResponsePage(new RequerimentoRepresentanteLegalPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case ALTERACAO_ATIVIDADE_ECONOMICA:
                        setResponsePage(new RequerimentoAtividadeEconomicaPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case REQUISICAO_RECEITUARIO_A:
                        setResponsePage(new RequerimentoReceitaAPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case REQUISICAO_RECEITUARIO_TALIDOMIDA:
                        setResponsePage(new RequerimentoReceitaTalidomidaPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case REQUISICAO_RECEITUARIO_B:
                        setResponsePage(new RequerimentoReceitaBPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case BAIXA_RESPONSABILIDADE_TECNICA:
                        setResponsePage(new RequerimentoBaixaResponsabilidadeTecnicaPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case ENTRADA_RESPONSABILIDADE_TECNICA:
                        setResponsePage(new RequerimentoInclusaoResponsabilidadePage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case ALTERACAO_ENDERECO:
                        setResponsePage(new RequerimentoAlteracaoEnderecoPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case ALTERACAO_RAZAO_SOCIAL:
                        setResponsePage(new RequerimentoAlteracaoRazaoSocialPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case PEDIDO_DOCUMENTO:
                        setResponsePage(new RequerimentoPedidoDocumentoPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case VACINACAO_EXTRAMURO:
                        setResponsePage(new RequerimentoVacinacaoExtramuroPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case BAIXA_ESTABELECIMENTO:
                        setResponsePage(new RequerimentoBaixaEstabelecimentoPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case BAIXA_VEICULO:
                        setResponsePage(new RequerimentoBaixaVeiculoPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case VISTORIA_LAUDO_CONFORMIDADE_PBA:
                        setResponsePage(new RequerimentoVistoriaProjetoBasicoArquiteturaPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class, isActionPermitted(Permissions.PERMITIR_EDITAR_ANALISE_PROJETO)));
                        break;
                    case ANALISE_PROJETO_HIDROSSANITARIO:
                        setResponsePage(new RequerimentoProjetoHidrossanitarioPadraoPage(rv, (edicao && isActionPermitted(Permissions.PERMITIR_EDITAR_ANALISE_PROJETO)), ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case VISTORIA_HABITESE_SANITARIO:
                        setResponsePage(new RequerimentoHabitesePadraoPage(rv, (edicao && isActionPermitted(Permissions.PERMITIR_EDITAR_ANALISE_PROJETO)), ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case HABITE_SE_DECLARATORIO:
                        setResponsePage(new RequerimentoHabiteseDeclaratorioPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class, isActionPermitted(Permissions.PERMITIR_EDITAR_ANALISE_PROJETO)));
                        break;
                    case TREINAMENTOS_ALIMENTO:
                        setResponsePage(new RequerimentoTreinamentoPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case DENUNCIA_RECLAMACAO:
                        setResponsePage(new RequerimentoDenunciaReclamacaoPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case RESTITUICAO_TAXA:
                        setResponsePage(new RequerimentoRestituicaoTaxaPage(rv, edicao, ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case ANALISE_PROJETO_HIDROSSANITARIO_DECLARATORIO:
                        setResponsePage(new RequerimentoHidrossanitarioDeclaratorioPage(rv, (edicao && isActionPermitted(Permissions.PERMITIR_EDITAR_ANALISE_PROJETO)), ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case PROJETO_ARQUITETONICO_SANITARIO:
                        setResponsePage(new RequerimentoProjetoArquitetonicoSanitarioPage(rv, (edicao && isActionPermitted(Permissions.PERMITIR_EDITAR_ANALISE_PROJETO)), ConsultaRequerimentoVigilanciaPage.class));
                        break;
                    case LICENCA_SANITARIA:
                        setResponsePage(new RequerimentoAlvaraInicialPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class, isActionPermitted(Permissions.PERMITIR_EDITAR_ALVARAS)));
                        break;
                    default:
                        break;
                }
            } else {
                throw new ValidacaoException("Tipo de Requerimento não Encontrado!");
            }
        }
    }

    private void responsePageProrrogacaoPrazo(AjaxRequestTarget target, final RequerimentoVigilancia rv, final boolean edicao) {
        RequerimentoProrrogacaoPrazoPage page = new RequerimentoProrrogacaoPrazoPage(rv, edicao, ConsultaRequerimentoVigilanciaFiscalPage.class);
        setResponsePage(page);
    }

    private void initDlgImpressaoConsultaRequerimentoVigilancia(AjaxRequestTarget target, RequerimentoVigilancia rv) {
        if (dlgImpressaoConsultaRequerimentoVigilancia == null) {
            addModal(target, dlgImpressaoConsultaRequerimentoVigilancia = new DlgImpressaoConsultaRequerimentoVigilancia(newModalId()) {
                @Override
                public DataReport onImprimir(RequerimentoVigilancia requerimentoVigilancia) throws ReportException, ValidacaoException {
                    return null;
                }
            });
        }
        dlgImpressaoConsultaRequerimentoVigilancia.show(target, rv);
    }

    private void initDlgAcoesRequerimentoVigilancia(AjaxRequestTarget target, final RequerimentoVigilanciaDTO requerimentoVigilanciaDTO) {
        /*
         *  Validação Necessária para quando o Requerimento for direcionado a um Estabelecimento, o mesmo deve estar com a Situacao Ativa para prosseguir com as ações.
         *  Regra de negócio necessária pois os estabelecimentos cadastrados pelo externo da vigilância (Situacao Provisório) o mesmo necessita ser Ativado
         */
        if (requerimentoVigilanciaDTO.getRequerimentoVigilancia() != null && requerimentoVigilanciaDTO.getRequerimentoVigilancia().getEstabelecimento() != null) {
            if (!Estabelecimento.Situacao.ATIVO.value().equals(requerimentoVigilanciaDTO.getRequerimentoVigilancia().getEstabelecimento().getSituacao())) {
                if (Estabelecimento.Situacao.INATIVO.value().equals(requerimentoVigilanciaDTO.getRequerimentoVigilancia().getEstabelecimento().getSituacao())) {
                    warn(target, bundle("msgParaModificarRequerimentoSolicitadoEstabelecimentoInativoNecessitaReativarEstabelecimentoTelaX",
                            requerimentoVigilanciaDTO.getRequerimentoVigilancia().getEstabelecimento().getDescricaoVO(),
                            bundle("telaAutorizacaoEstabelecimento"))
                    );
                    return;
                }

                if (Estabelecimento.Situacao.PROVISORIO.value().equals(requerimentoVigilanciaDTO.getRequerimentoVigilancia().getEstabelecimento().getSituacao())) {
                    warn(target, bundle("msgParaModificarRequerimentoSolicitadoEstabelecimentoProvisorioNecessitaAutorizarEstabelecimentoTelaX",
                            requerimentoVigilanciaDTO.getRequerimentoVigilancia().getEstabelecimento().getDescricaoVO(),
                            bundle("telaAutorizacaoEstabelecimentoProvisorioNaoAutorizado"))
                    );
                    return;
                }

                if (Estabelecimento.Situacao.NAO_AUTORIZADO.value().equals(requerimentoVigilanciaDTO.getRequerimentoVigilancia().getEstabelecimento().getSituacao())) {
                    warn(target, bundle("msgParaModificarRequerimentoSolicitadoEstabelecimentoNaoAutorizadoNecessitaAutorizarEstabelecimentoTelaX",
                            requerimentoVigilanciaDTO.getRequerimentoVigilancia().getEstabelecimento().getDescricaoVO(),
                            bundle("telaAutorizacaoEstabelecimentoProvisorioNaoAutorizado"))
                    );
                    return;
                }
            }
        }

        if (dlgAcoesRequerimentoVigilancia == null) {
            addModal(target, dlgAcoesRequerimentoVigilancia = new DlgAcoesRequerimentoVigilancia(newModalId(), false) {
                @Override
                public void onEditar(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    responsePage(target, dto.getRequerimentoVigilancia(), true);
                }

                @Override
                public void onAprovar(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    RequerimentoVigilanciaAprovacaoDTO requerimentoVigilanciaAprovacaoDTO = new RequerimentoVigilanciaAprovacaoDTO();
                    requerimentoVigilanciaAprovacaoDTO.setRequerimentoVigilancia(dto.getRequerimentoVigilancia());
                    List<VigilanciaFinanceiro> vigilanciaFinanceiroList = VigilanciaHelper.getVigilanciaFinanceiroList(dto.getRequerimentoVigilancia());
                    requerimentoVigilanciaAprovacaoDTO.setVigilanciaFinanceiroList(vigilanciaFinanceiroList);
                    setResponsePage(new RequerimentoVigilanciaAprovacaoPage(requerimentoVigilanciaAprovacaoDTO, getForm().getModel().getObject(), classeVoltar));
                }

                @Override
                public void onCancelar(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    initDlgCancelamentoRequerimentoVigilancia(target, dto.getRequerimentoVigilancia());
                }

                @Override
                public void onEmAnalise(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto, String justificativa) throws ValidacaoException, DAOException {
                    analisarRequerimento(target, dto, justificativa);
                }

                @Override
                public void onReverterSituacao(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    BOFactoryWicket.getBO(VigilanciaFacade.class).reverterSituacaoRequerimentoVigilancia(dto);
                    table.update(target);
                }

                @Override
                public void onReverterFinalizacao(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    BOFactoryWicket.getBO(VigilanciaFacade.class).reverterSituacaoRequerimentoVigilancia(dto);
                    table.update(target);
                }

                @Override
                public void onLancarOcorrencia(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    initDlgOcorrenciaConsultaRequerimentoVigilancia(target, dto.getRequerimentoVigilancia());
                }

                @Override
                public void onFinalizar(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    responseConfirm(target, dto.getRequerimentoVigilancia());
                }

                @Override
                public void onEntregaDocumento(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    initDlgEntregaDocumentoRequerimentoVigilancia(target, dto.getRequerimentoVigilancia());
                }

                @Override
                public void onConformidadeTecnica(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    redirecionarConformidade(dto.getRequerimentoVigilancia());
                }

                @Override
                public void onEmitirBoleto(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    redirecionarBoletoPage(target, dto);
                }

                @Override
                public void onComprovantePagamento(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    redirecionarComprovantePagamento(target, dto);
                }

                @Override
                public void onInformarFiscais(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    initDlgAdicionarFiscaisRequerimentoVigilancia(target, dto.getRequerimentoVigilancia());
                }

                @Override
                public void onVoltar(AjaxRequestTarget target) {
                }

                @Override
                public void onRelatorioInspecoes(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    VigilanciaHelper.validarAntesProsseguirComDenuncia(dto.getRequerimentoVigilancia());
                    setResponsePage(new CadastroRelatorioInspecaoPage(dto.getRequerimentoVigilancia(), ConsultaRequerimentoVigilanciaPage.class));
                }

                @Override
                public void onRegistroVisitas(AjaxRequestTarget target, final RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    addModal(target, dlgRegistrarVisita = new DlgRegistrarVisita(newModalId()) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target, List<Profissional> profissionalList, Date data, String observacao) throws ValidacaoException, DAOException {
                            if (TipoSolicitacao.TipoDocumento.LICENCA_TRANSPORTE.value().equals(requerimentoVigilanciaDTO.getRequerimentoVigilancia().getTipoDocumento())) {
                                BOFactoryWicket.getBO(VigilanciaFacade.class).gerarFaturamentoInspecaoVeiculos(dto.getRequerimentoVigilancia().getEstabelecimento(), profissionalList, data, observacao, requerimentoVigilanciaDTO.getRequerimentoVigilancia());
                            } else {
                                BOFactoryWicket.getBO(VigilanciaFacade.class).gerarFaturamentoInspecao(dto.getRequerimentoVigilancia().getEstabelecimento(), profissionalList, data, observacao, requerimentoVigilanciaDTO.getRequerimentoVigilancia());
                            }

                            MessageUtil.info(target, this, "Visita gerada com sucesso!");
                            updateNotificationPanel(target);
                        }
                    });
                    dlgRegistrarVisita.show(target);
                }

                @Override
                public void onAutoIntimacao(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    VigilanciaHelper.validarAntesProsseguirComDenuncia(dto.getRequerimentoVigilancia());
                    redirecionarAutoIntimacao(target, dto.getRequerimentoVigilancia());
                }

                @Override
                public void onAutoInfracao(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    VigilanciaHelper.validarAntesProsseguirComDenuncia(dto.getRequerimentoVigilancia());
                    redirecionarAutoInfracao(target, dto.getRequerimentoVigilancia());
                }

                @Override
                public void onAutoMulta(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    VigilanciaHelper.validarAntesProsseguirComDenuncia(dto.getRequerimentoVigilancia());
                    redirecionarAutoMulta(target, dto.getRequerimentoVigilancia());
                }

                @Override
                public void onAutoPenalidade(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    VigilanciaHelper.validarAntesProsseguirComDenuncia(dto.getRequerimentoVigilancia());
                    redirecionarAutoPenalidade(target, dto.getRequerimentoVigilancia());
                }

                @Override
                public void onParecer(AjaxRequestTarget target, final RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    redirecionarParecer(dto.getRequerimentoVigilancia());
                }

                @Override
                public void onDocumentosRequerimento(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    imprimirDocumentosRequerimento(target, dto.getRequerimentoVigilancia());
                }

                @Override
                public void onHistoricoContribuinte(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    imprimirHistorico(target, dto.getRequerimentoVigilancia());
                }

                @Override
                public void onBoletoComplementar(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {

                }
            });
        }

        requerimentoVigilanciaDTO.setPermissaoReverterFinalizado(isActionPermitted(Permissions.PERMITIR_REVERTER_FINALIZADO));
        requerimentoVigilanciaDTO.setPermissaoEditarAlvaras(isActionPermitted(Permissions.PERMITIR_EDITAR_ALVARAS));
        requerimentoVigilanciaDTO.setPermissaoEditarAnaliseProjeto(isActionPermitted(Permissions.PERMITIR_EDITAR_ANALISE_PROJETO));
        requerimentoVigilanciaDTO.setPermissaoEditar(isActionPermitted(Permissions.EDITAR));
        requerimentoVigilanciaDTO.setPermissaoImprimir(isVisibleImpressao(requerimentoVigilanciaDTO));
        requerimentoVigilanciaDTO.setPermissaoHistorico(isActionPermitted(Permissions.HISTORICO));
        requerimentoVigilanciaDTO.setPermissaoAprovarDevolver(isActionPermitted(Permissions.TRANSFERENCIA));
        requerimentoVigilanciaDTO.setPermissaoCancelar(isActionPermitted(Permissions.CANCELAR));
        requerimentoVigilanciaDTO.setPermissaoAnalisar(isActionPermitted(Permissions.ENCAMINHAR));
        requerimentoVigilanciaDTO.setPermissaoReverterSituacao(isActionPermitted(Permissions.ENCAMINHAR));
        requerimentoVigilanciaDTO.setPermissaoLancarOcorrencia(isActionPermitted(Permissions.MANUTENCAO));
        requerimentoVigilanciaDTO.setPermissaoFinalizar(isActionPermitted(Permissions.SALVAR));
        requerimentoVigilanciaDTO.setPermissaoEntregarDocumento(isActionPermitted(Permissions.BAIXA));
        requerimentoVigilanciaDTO.setPermissaoConformidadeTecnica(isActionPermitted(Permissions.CONFORMIDADE_TECNICA));
        requerimentoVigilanciaDTO.setPermissaoParecer(isActionPermitted(Permissions.PARECER));
        requerimentoVigilanciaDTO.setPermissaoComprovantePagamento(isActionPermitted(Permissions.ANEXAR));
        requerimentoVigilanciaDTO.setPermissaoInformarFiscal(isActionPermitted(Permissions.INFORMAR_FISCAIS));
        /*
         Autos não devem ser gerados por esta tela em que a Recepção Usa.
         */
        requerimentoVigilanciaDTO.setPermissaoAutoMulta(false);
        requerimentoVigilanciaDTO.setPermissaoAutoIntimacao(false);
        requerimentoVigilanciaDTO.setPermissaoAutoInfracao(false);
        requerimentoVigilanciaDTO.setPermissaoAutoPenalidade(false); //rever rotina da funcionalidade
        requerimentoVigilanciaDTO.setVigilanciaFinanceiroList(VigilanciaHelper.getVigilanciaFinanceiroList(requerimentoVigilanciaDTO.getRequerimentoVigilancia()));

        dlgAcoesRequerimentoVigilancia.show(target, requerimentoVigilanciaDTO);
    }

    private void analisarRequerimento(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto, String justificativa) throws DAOException, ValidacaoException {
        if (VigilanciaHelper.analisarDocumentosExterno(requerimentoVigilanciaDTO.getRequerimentoVigilancia())) {
            setResponsePage(new RequerimentoVigilanciaAnalisarDocumentacaoPage(requerimentoVigilanciaDTO.getRequerimentoVigilancia(), dto, classeVoltar));
        } else {
            commandAnalisarRequerimento(target, dto, justificativa);
        }
    }

    private void commandAnalisarRequerimento(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto, String justificativa) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(VigilanciaFacade.class).analisarRequerimentoVigilancia(dto, justificativa);
        table.update(target);
    }

    private void imprimirHistorico(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia) throws ValidacaoException, DAOException {
        File historico = null;
        if (requerimentoVigilancia.getEstabelecimento() != null && requerimentoVigilancia.getEstabelecimento().getCodigo() != null) {
            historico = BOFactoryWicket.getBO(VigilanciaReportFacade.class).historico(requerimentoVigilancia.getEstabelecimento());
        } else if (requerimentoVigilancia.getVigilanciaPessoa() != null && requerimentoVigilancia.getVigilanciaPessoa().getCodigo() != null) {
            historico = BOFactoryWicket.getBO(VigilanciaReportFacade.class).historico(requerimentoVigilancia.getVigilanciaPessoa());
        } else if (requerimentoVigilancia.getVigilanciaProfissional() != null && requerimentoVigilancia.getVigilanciaProfissional().getCodigo() != null) {
            historico = BOFactoryWicket.getBO(VigilanciaReportFacade.class).historico(requerimentoVigilancia.getVigilanciaProfissional());
        }

        if (historico != null) {
            IResourceStream resourceStream = new FileResourceStream(new org.apache.wicket.util.file.File(historico));
            ajaxPreviewBlank.initiate(target, "historico.pdf", resourceStream);
        }
    }

    private void imprimirDocumentosRequerimento(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia) throws ValidacaoException, DAOException {
        info(target, "Impressão enviada para processamento e em alguns instantes você receberá uma mensagem com o arquivo gerado");
        BOFactoryWicket.getBO(VigilanciaReportFacade.class).enviarArquivoDocumentosRequerimentoVigilancia(requerimentoVigilancia);
    }

    private void redirecionarBoletoPage(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) {
        List<VigilanciaFinanceiro> vigilanciaFinanceiroList = VigilanciaHelper.getVigilanciaFinanceiroList(dto.getRequerimentoVigilancia(), true, false);

        if (CollectionUtils.isNotNullEmpty(vigilanciaFinanceiroList)) {

            if (vigilanciaFinanceiroList.size() > 1) {

                if (dlgEscolherVigilanciaFinanceiro == null) {
                    addModal(target, dlgEscolherVigilanciaFinanceiro = new DlgEscolherVigilanciaFinanceiro<RequerimentoVigilanciaDTO>(newModalId()) {
                        @Override
                        public void onEscolherVigilanciaFinanceiro(AjaxRequestTarget target, VigilanciaFinanceiro vigilanciaFinanceiro) throws DAOException, ValidacaoException {
                            setResponsePage(new BoletoVigilanciaPage(getObject(), vigilanciaFinanceiro, ConsultaRequerimentoVigilanciaPage.class));
                        }
                    });
                }

                dlgEscolherVigilanciaFinanceiro.setObject(dto);
                dlgEscolherVigilanciaFinanceiro.show(target, vigilanciaFinanceiroList);

            } else {
                setResponsePage(new BoletoVigilanciaPage(dto, vigilanciaFinanceiroList.get(0), ConsultaRequerimentoVigilanciaPage.class));
            }
        } else if (VigilanciaHelper.geraFinanceiro(dto.getRequerimentoVigilancia().getTipoDocumento())) {
            ConfiguracaoVigilanciaFinanceiro configuracaoVigilanciaFinanceiro = null;
            try {
                configuracaoVigilanciaFinanceiro = VigilanciaHelper.getConfiguracaoVigilanciaFinanceiro();
            } catch (ValidacaoException e) {
                Loggable.log.error(e.getMessage(), e);
            }
            if (TipoSolicitacao.TipoDocumento.LICENCA_TRANSPORTE.value().equals(dto.getRequerimentoVigilancia().getTipoDocumento())) {
                if (configuracaoVigilanciaFinanceiro != null && ConfiguracaoVigilanciaFinanceiro.TipoCobrancaLicencaTransporte.POR_VEICULO.value().equals(configuracaoVigilanciaFinanceiro.getTipoCobrancaLicencaTransporte())) {
                    setResponsePage(new BoletoMultiploVigilanciaPage(dto.getRequerimentoVigilancia(), ConsultaRequerimentoVigilanciaPage.class));
                } else {
                    setResponsePage(new BoletoVigilanciaPage(dto, ConsultaRequerimentoVigilanciaPage.class));
                }
            } else {
                setResponsePage(new BoletoVigilanciaPage(dto, ConsultaRequerimentoVigilanciaPage.class));
            }
        }
    }

    private void redirecionarAutoIntimacao(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia) {
        CadastroAutoIntimacaoPage cadAutoIntimacao = new CadastroAutoIntimacaoPage(requerimentoVigilancia) {
            @Override
            public Class getResponsePage() {
                return ConsultaRequerimentoVigilanciaPage.class;
            }
        };

        cadAutoIntimacao.instanceFromRequerimento(target);

        setResponsePage(cadAutoIntimacao);
    }

    private void redirecionarAutoInfracao(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia) {
        CadastroAutoInfracaoPage cadAutoInfracao = new CadastroAutoInfracaoPage(requerimentoVigilancia) {
            @Override
            public Class getResponsePage() {
                return ConsultaRequerimentoVigilanciaPage.class;
            }
        };

        cadAutoInfracao.instanceFromRequerimento(target);

        setResponsePage(cadAutoInfracao);
    }

    private void redirecionarAutoMulta(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia) {
        CadastroAutoMultaPage cadAutoMulta = new CadastroAutoMultaPage() {
            @Override
            public Class getResponsePage() {
                return ConsultaRequerimentoVigilanciaPage.class;
            }
        };

        cadAutoMulta.instanceFromRequerimento(target, requerimentoVigilancia);

        setResponsePage(cadAutoMulta);
    }

    private void redirecionarAutoPenalidade(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia) {
        CadastroAutoPenalidadePage cadAutoPenalidade = new CadastroAutoPenalidadePage() {
            @Override
            public Class getResponsePage() {
                return ConsultaRequerimentoVigilanciaPage.class;
            }
        };

        cadAutoPenalidade.instanceFromRequerimento(target, requerimentoVigilancia);

        setResponsePage(cadAutoPenalidade);
    }

    private void redirecionarParecer(RequerimentoVigilancia rv) {
        Long td = rv.getTipoDocumento();
        if (td != null) {
            switch (TipoSolicitacao.TipoDocumento.valueOf(td)) {
                case PROJETO_BASICO_ARQUITETURA:
                    setResponsePage(new RequerimentoAnaliseProjetosParecerTecnicoPage(rv, ConsultaRequerimentoVigilanciaPage.class));
                    break;
                case ANALISE_PROJETO_HIDROSSANITARIO:
                    setResponsePage(new RequerimentoProjetosHidrossanitarioParecerTecnicoPage(rv, ConsultaRequerimentoVigilanciaPage.class));
                    break;
                case VISTORIA_HABITESE_SANITARIO:
                    setResponsePage(new RequerimentoVistoriaHidrossanitarioParecerTecnicoPage(rv, ConsultaRequerimentoVigilanciaPage.class));
                    break;
                case HABITE_SE_DECLARATORIO:
                    setResponsePage(new RequerimentoHabiteseDeclaratorioParecerTecnicoPage(rv, ConsultaRequerimentoVigilanciaPage.class));
                    break;
                case ANALISE_PROJETO_HIDROSSANITARIO_DECLARATORIO:
                    setResponsePage(new RequerimentoHidrossanitarioDeclaratorioParecerTecnicoPage(rv, ConsultaRequerimentoVigilanciaPage.class));
                    break;
                case PROJETO_ARQUITETONICO_SANITARIO:
                    setResponsePage(new RequerimentoProjetoArquitetonicoParecerTecnicoPage(rv, ConsultaRequerimentoVigilanciaPage.class));
                    break;
                default:
                    CadastroParecerPage cadParecer = new CadastroParecerPage(rv, getForm().getModel().getObject(), false);
                    cadParecer.instanceFromRequerimento();
                    setResponsePage(cadParecer);
                    break;
            }
        }
    }

    private void redirecionarConformidade(RequerimentoVigilancia rv) {
        Long td = rv.getTipoDocumento();
        if (td != null) {
            switch (TipoSolicitacao.TipoDocumento.valueOf(td)) {
                case VISTORIA_LAUDO_CONFORMIDADE_PBA:
                    setResponsePage(new RequerimentoAnaliseProjetosConformidadeTecnicaVistoriaPage(rv, ConsultaRequerimentoVigilanciaPage.class));
                    break;
                default:
                    setResponsePage(new RequerimentoAnaliseProjetosConformidadeTecnicaPage(rv, ConsultaRequerimentoVigilanciaPage.class));
                    break;
            }
        }
    }

    private void redirecionarComprovantePagamento(AjaxRequestTarget target, final RequerimentoVigilanciaDTO requerimentoVigilanciaDTO) {
        List<VigilanciaFinanceiro> vigilanciaFinanceiroList = VigilanciaHelper.getVigilanciaFinanceiroList(requerimentoVigilanciaDTO.getRequerimentoVigilancia());

        if (CollectionUtils.isNotNullEmpty(vigilanciaFinanceiroList)) {

            if (vigilanciaFinanceiroList.size() > 1) {

                if (dlgEscolherVigilanciaFinanceiro == null) {
                    addModal(target, dlgEscolherVigilanciaFinanceiro = new DlgEscolherVigilanciaFinanceiro<RequerimentoVigilanciaDTO>(newModalId()) {
                        @Override
                        public void onEscolherVigilanciaFinanceiro(AjaxRequestTarget target, VigilanciaFinanceiro vigilanciaFinanceiro) throws DAOException, ValidacaoException {
                            if (dlgAnexoComprovantePagamentoRequerimento == null) {
                                addModal(target, dlgAnexoComprovantePagamentoRequerimento = new DlgAnexoComprovantePagamentoRequerimento(newModalId()) {
                                    @Override
                                    public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                                        table.update(target);
                                    }
                                });
                            }

                            dlgAnexoComprovantePagamentoRequerimento.show(target, getObject().getRequerimentoVigilancia(), vigilanciaFinanceiro);
                        }
                    });
                }

                dlgEscolherVigilanciaFinanceiro.setObject(requerimentoVigilanciaDTO);
                dlgEscolherVigilanciaFinanceiro.show(target, vigilanciaFinanceiroList);
            } else {
                if (dlgAnexoComprovantePagamentoRequerimento == null) {
                    addModal(target, dlgAnexoComprovantePagamentoRequerimento = new DlgAnexoComprovantePagamentoRequerimento(newModalId()) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                            table.update(target);
                        }
                    });
                }

                dlgAnexoComprovantePagamentoRequerimento.show(target, requerimentoVigilanciaDTO.getRequerimentoVigilancia(), vigilanciaFinanceiroList.get(0));
            }
        }
    }

    private void initDlgOcorrenciaConsultaRequerimentoVigilancia(AjaxRequestTarget target, RequerimentoVigilancia rv) {
        if (dlgOcorrenciaConsultaRequerimentoVigilancia == null) {
            addModal(target, dlgOcorrenciaConsultaRequerimentoVigilancia = new DlgOcorrenciaConsultaRequerimentoVigilancia(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, CadastroOcorrenciaRequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                    BOFactoryWicket.getBO(VigilanciaFacade.class).gerarOcorrenciaRequerimentoVigilanciaProfissional(dto);
                    table.update(target);
                }
            });
        }
        dlgOcorrenciaConsultaRequerimentoVigilancia.show(target, rv);
    }

    private List<DataReport> responseImpression(AjaxRequestTarget target, RequerimentoVigilancia rv) throws ReportException {
        Long td = rv.getTipoDocumento();
        List<DataReport> lstDataReport = new ArrayList<>();
        if (td != null) {
            switch (TipoSolicitacao.TipoDocumento.valueOf(td)) {
                case EXUMACAO_RESTOS_MORTAIS:
                    RelatorioAutorizacaoExumacaoDTOParam paramExumacao = new RelatorioAutorizacaoExumacaoDTOParam();
                    paramExumacao.setCodigoRequerimentoVigilancia(rv.getCodigo());
                    lstDataReport.add(BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioRequerimentoExumacao(paramExumacao));
                    break;
                case DECLARACAO_VISA_PRODUTOS:
                    RelatorioRequerimentoVigilanciaComprovanteDTOParam paramVisaProdutos = new RelatorioRequerimentoVigilanciaComprovanteDTOParam();
                    paramVisaProdutos.setRequerimentoVigilancia(rv);
                    lstDataReport.add(BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRequerimentoDeclaracaoVisaProdutos(paramVisaProdutos));
                    break;
                case DECLARACAO_VISA_ISENCAO_TAXAS:
                    RelatorioRequerimentoVigilanciaComprovanteDTOParam paramVisaIsencaoTaxas = new RelatorioRequerimentoVigilanciaComprovanteDTOParam();
                    paramVisaIsencaoTaxas.setRequerimentoVigilancia(rv);
                    lstDataReport.add(BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRequerimentoDeclaracaoVisaIsencaoTaxas(paramVisaIsencaoTaxas));
                    break;
                case DECLARACAO_VISA_OUTROS:
                    RelatorioRequerimentoVigilanciaComprovanteDTOParam paramVisaOutros = new RelatorioRequerimentoVigilanciaComprovanteDTOParam();
                    paramVisaOutros.setRequerimentoVigilancia(rv);
                    lstDataReport.add(BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRequerimentoDeclaracaoVisaOutros(paramVisaOutros));
                    break;
                case INSPECAO_SANITARIA_AFE_ANVISA:
                case BAIXA_RESPONSABILIDADE_TECNICA:
                case CERTIDAO_NADA_CONSTA:
                case ALTERACAO_RAZAO_SOCIAL:
                case ALTERACAO_ENDERECO:
                case ALTERACAO_RESPONSABILIDADE_LEGAL:
                case ALTERACAO_ATIVIDADE_ECONOMICA:
                case ABERTURA_LIVRO_CONTROLE:
                case FECHAMENTO_LIVRO_CONTROLE:
                case ALVARA_INICIAL:
                case ALVARA_REVALIDACAO:
                case REVALIDACAO_LICENCA_SANITARIA:
                case ALVARA_PARTICIPANTE_EVENTO:
                case ALVARA_CADASTRO_EVENTO:
                case REQUISICAO_RECEITUARIO_A:
                case REQUISICAO_RECEITUARIO_TALIDOMIDA:
                case REQUISICAO_RECEITUARIO_B:
                case ENTRADA_RESPONSABILIDADE_TECNICA:
                case LICENCA_TRANSPORTE:
                case PRORROGACAO_PRAZO:
                case DECLARACAO_CARTORIO:
                case INSPECAO_SANITARIA_COMUM:
                case PEDIDO_DOCUMENTO:
                case VACINACAO_EXTRAMURO:
                case ANALISE_PROJETOS:
                case PROJETO_BASICO_ARQUITETURA:
                case BAIXA_ESTABELECIMENTO:
                case BAIXA_VEICULO:
                case TREINAMENTOS_ALIMENTO:
                case DENUNCIA_RECLAMACAO:
                case ANALISE_PROJETO_HIDROSSANITARIO:
                case ANALISE_PROJETO_HIDROSSANITARIO_DECLARATORIO:
                case VISTORIA_HABITESE_SANITARIO:
                case HABITE_SE_DECLARATORIO:
                case VISTORIA_LAUDO_CONFORMIDADE_PBA:
                case AUTORIZACAO_SANITARIA:
                case PROJETO_ARQUITETONICO_SANITARIO:
                case LICENCA_SANITARIA:
                case DISPENSA_SANITARIA:
                    initDlgImpressaoConsultaRequerimentoVigilancia(target, rv);
                    break;
                default:
                    return lstDataReport;
            }

        }
        return lstDataReport;
    }

    private void responseConfirm(AjaxRequestTarget target, RequerimentoVigilancia rv) {
        Long td = rv.getTipoDocumento();
        if (td != null) {
            switch (TipoSolicitacao.TipoDocumento.valueOf(td)) {
                case EXUMACAO_RESTOS_MORTAIS:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case ALVARA_PARTICIPANTE_EVENTO:
                    initDlgFinalizacaoRequerimentoAlvara(target, rv);
                    break;
                case ALVARA_CADASTRO_EVENTO:
                    initDlgFinalizacaoRequerimentoAlvara(target, rv);
                    break;
                case ALVARA_INICIAL:
                    initDlgFinalizacaoRequerimentoAlvara(target, rv);
                    break;
                case LICENCA_SANITARIA:
                    initDlgFinalizacaoRequerimentoAlvara(target, rv);
                    break;
                case REVALIDACAO_LICENCA_SANITARIA:
                case ALVARA_REVALIDACAO:
                    initDlgFinalizacaoRequerimentoAlvara(target, rv);
                    break;
                case AUTORIZACAO_SANITARIA:
                    initDlgFinalizacaoRequerimentoAlvara(target, rv);
                    break;
                case LICENCA_TRANSPORTE:
                    setResponsePage(new DeferimentoLicencaVeiculosPage(rv, true, ConsultaRequerimentoVigilanciaPage.class));
                    break;
                case ABERTURA_LIVRO_CONTROLE:
                    setResponsePage(new RequerimentoLivroFechamentoPage(rv, false, true, ConsultaRequerimentoVigilanciaPage.class));
                    break;
                case PRORROGACAO_PRAZO:
                    setResponsePage(new DeferimentoProrrogacaoPrazoPage(rv, true, ConsultaRequerimentoVigilanciaPage.class));
                    break;
                case INSPECAO_SANITARIA_AFE_ANVISA:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case INSPECAO_SANITARIA_COMUM:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case ALTERACAO_RESPONSABILIDADE_LEGAL:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case ALTERACAO_ATIVIDADE_ECONOMICA:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case BAIXA_RESPONSABILIDADE_TECNICA:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case ENTRADA_RESPONSABILIDADE_TECNICA:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case REQUISICAO_RECEITUARIO_B:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case ALTERACAO_ENDERECO:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case ALTERACAO_RAZAO_SOCIAL:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case REQUISICAO_RECEITUARIO_A:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case REQUISICAO_RECEITUARIO_TALIDOMIDA:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case DECLARACAO_CARTORIO:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case CERTIDAO_NADA_CONSTA:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case PEDIDO_DOCUMENTO:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case VACINACAO_EXTRAMURO:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case ANALISE_PROJETOS:
                    setResponsePage(new RequerimentoAnaliseProjetosParecerTecnicoPage(rv, ConsultaRequerimentoVigilanciaPage.class));
                    break;
                case PROJETO_BASICO_ARQUITETURA:
                    setResponsePage(new RequerimentoAnaliseProjetosParecerTecnicoPage(rv, ConsultaRequerimentoVigilanciaPage.class));
                    break;
                case BAIXA_ESTABELECIMENTO:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case BAIXA_VEICULO:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case TREINAMENTOS_ALIMENTO:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                case DENUNCIA_RECLAMACAO:
                    initDlgFinalizacaoRequerimentoVigilancia(target, rv);
                    break;
                default:
                    break;
            }
        }
    }

    private void initDlgEntregaDocumentoRequerimentoVigilancia(AjaxRequestTarget target, RequerimentoVigilancia rv) {
        if (dlgEntregaDocumentoRequerimentoVigilancia == null) {
            addModal(target, dlgEntregaDocumentoRequerimentoVigilancia = new DlgEntregaDocumentoRequerimentoVigilancia(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, RequerimentoVigilanciaEntregaDocumentoDTO dto) throws ValidacaoException, DAOException {
                    BOFactoryWicket.getBO(VigilanciaFacade.class).entregarDocumentoRequerimentoVigilancia(dto);
                    table.update(target);
                }
            });
        }
        dlgEntregaDocumentoRequerimentoVigilancia.show(target, rv);
    }

    private void initDlgFinalizacaoRequerimentoVigilancia(AjaxRequestTarget target, RequerimentoVigilancia rv) {
        addModal(target, dlgFinalizacaoRequerimentoVigilancia = new DlgCancelamentoFinalizacaoRequerimentoVigilancia(newModalId(), BundleManager.getString("finalizacaoRequerimento")) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, RequerimentoVigilanciaCancelamentoFinalizacaoDTO dto) throws ValidacaoException, DAOException {
                if (dto.getSituacao() == null) {
                    dto.setSituacao(RequerimentoVigilancia.Situacao.FINALIZADO);
                }
                if (RequerimentoVigilancia.Situacao.FINALIZADO.value().equals(dto.getSituacao())) {
                    VigilanciaHelper.validarAntesProsseguirComDenuncia(dto.getRequerimentoVigilancia());
                }
                BOFactoryWicket.getBO(VigilanciaFacade.class).cancelarFinalizarRequerimentoVigilancia(dto);
                table.update(target);
            }
        });
        dlgFinalizacaoRequerimentoVigilancia.show(target, rv, true);
    }

    private void initDlgFinalizacaoRequerimentoAlvara(AjaxRequestTarget target, RequerimentoVigilancia rv) {
        addModal(target, dlgFinalizacaoRequerimentoVigilancia = new DlgCancelamentoFinalizacaoRequerimentoVigilancia(newModalId(), BundleManager.getString("finalizacaoRequerimento")) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, RequerimentoVigilanciaCancelamentoFinalizacaoDTO dto) throws ValidacaoException, DAOException {
                if (dto.getRequerimentoVigilancia() != null && dto.getRequerimentoVigilancia().getSituacao() != null) {
                    dto.getRequerimentoVigilancia().setSituacaoAnteriorConclusao(dto.getRequerimentoVigilancia().getSituacao());
                }
                if (dto.getSituacao() == null) {
                    dto.setSituacao(RequerimentoVigilancia.Situacao.FINALIZADO);
                } else {
                    dto.getRequerimentoVigilancia().setSituacao(dto.getSituacao().value());
                }
                dto.getRequerimentoVigilancia().setDataFinalizacao(dto.getDataFinalizacao());
                if (dto.getRequerimentoVigilancia().getChaveQRcode() != null) {
                    String urlQrcode = new StringBuilder().append(VigilanciaHelper.getURLQRCodePageAlvara()).append("?CHQRC=").append(dto.getRequerimentoVigilancia().getChaveQRcode()).toString();
                    dto.setUrlAlvara(urlQrcode);
                }

                BOFactoryWicket.getBO(VigilanciaFacade.class).finalizarRequerimentoAlvara(dto.getRequerimentoVigilancia(), dto);
                table.update(target);
            }
        });
        dlgFinalizacaoRequerimentoVigilancia.show(target, rv, true);
    }

    private void initDlgCancelamentoRequerimentoVigilancia(AjaxRequestTarget target, RequerimentoVigilancia rv) {
        addModal(target, dlgCancelamentoRequerimentoVigilancia = new DlgCancelamentoFinalizacaoRequerimentoVigilancia(newModalId(), BundleManager.getString("cancelamentoRequerimento")) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, RequerimentoVigilanciaCancelamentoFinalizacaoDTO dto) throws ValidacaoException, DAOException {
                if (dto.getSituacao() == null) {
                    dto.setSituacao(RequerimentoVigilancia.Situacao.CANCELADO);
                }
                BOFactoryWicket.getBO(VigilanciaFacade.class).cancelarFinalizarRequerimentoVigilancia(dto);
                table.update(target);
            }
        });
        String validacaoBoletosPendentes = VigilanciaHelper.validacaoBoletosPendentes(rv);
        if (StringUtils.trimToNull(validacaoBoletosPendentes) != null) {
            initDlgConfirmacaoCancelamento(target, validacaoBoletosPendentes.concat(". Deseja realmente cancelar?"), rv);
        } else {
            dlgCancelamentoRequerimentoVigilancia.show(target, rv, false);
        }
    }

    private void initDlgConfirmacaoCancelamento(AjaxRequestTarget target, String mensagemErro, RequerimentoVigilancia requerimentoVigilancia) {
        dlgConfirmacaoSimNaoCancelamento = new DlgConfirmacaoSimNao<RequerimentoVigilancia>(newModalId(), mensagemErro) {
            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                dlgCancelamentoRequerimentoVigilancia.show(target, getObject(), false);
            }
        };
        addModal(target, dlgConfirmacaoSimNaoCancelamento);
        dlgConfirmacaoSimNaoCancelamento.setObject(requerimentoVigilancia);
        dlgConfirmacaoSimNaoCancelamento.show(target);
    }

    private void initDlgAdicionarFiscaisRequerimentoVigilancia(AjaxRequestTarget target, final RequerimentoVigilancia requerimentoVigilancia) {
        addModal(target, dlgAdicionarFiscaisRequerimento = new DlgAdicionarFiscaisRequerimento(newModalId(), false) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, RequerimentoVigilancia rv, List<RequerimentoVigilanciaFiscal> fiscalList, List<RequerimentoVigilanciaFiscal> fiscalExcluirList) throws ValidacaoException, DAOException {
                String nomeFiscais = BOFactoryWicket.getBO(VigilanciaFacade.class).adicionarFiscaisRequerimento(rv, fiscalList, fiscalExcluirList);
                table.update(target);
                MessageUtil.info(target, this, bundle("requerimentoDirecionadoAosFiscaisX", nomeFiscais));
            }
        });
        dlgAdicionarFiscaisRequerimento.show(target, requerimentoVigilancia);
    }

    private QueryPagerProvider getDataProvider() {
        if (dataProvider == null) {
            dataProvider = new QueryPagerProvider<RequerimentoVigilanciaDTO, RequerimentoVigilanciaDTOParam>() {

                @Override
                public DataPagingResult executeQueryPager(DataPaging<RequerimentoVigilanciaDTOParam> dataPaging) throws DAOException, ValidacaoException {
                    if (getSort() != null) {
                        getForm().getModel().getObject().setAscending(getSort().isAscending());
                        getForm().getModel().getObject().setPropSort(getSort().getProperty());
                    }
                    getForm().getModel().getObject().setSortDataRequerimento(VigilanciaHelper.getSorterDataRequerimento());

                    return BOFactoryWicket.getBO(VigilanciaFacade.class).consultarRequerimentoVigilancia(dataPaging);
                }
            };
        }
        return dataProvider;
    }

    private boolean isPermitidoManipularRequerimentoOutroFiscal(RequerimentoVigilanciaDTO rowObject) {
        try {
            return VigilanciaPageHelper.isPermitidoManipularRequerimentoOutroFiscal(rowObject.getRequerimentoVigilancia(), ConsultaRequerimentoVigilanciaPage.class);
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage());
        }
        return true;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaRequerimentosProtocolo");
    }

    private void carregaLstUsuarioSetor() {
        Usuario usuario = br.com.celk.system.session.ApplicationSession.get().getSession().getUsuario();
        lstUsuSetor = LoadManager.getInstance(UsuarioSetorVigilancia.class)
                .addProperties(new HQLProperties(UsuarioSetorVigilancia.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioSetorVigilancia.PROP_USUARIO, usuario))
                .start().getList();
        if (CollectionUtils.isNotNullEmpty(lstUsuSetor)) {
            getForm().getModel().getObject().setLstUsuSetor(lstUsuSetor);
        }
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.initExpandLinks()));
        if (getForm().getModel().getObject().getSituacao() != null && classeVoltar != null) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerFiltros)));
        }
    }

}
