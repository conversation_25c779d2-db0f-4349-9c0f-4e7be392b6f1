package br.com.celk.view.consorcio.pedidotransferencialicitacaoitem.autocomplete.restricaocontainer;

import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.QueryConsultaProdutoPedidoTransferenciaLicitacaoItemDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class RestricaoContainerProdutoPedidoTransferenciaLicitacaoItem extends Panel implements IRestricaoContainer<QueryConsultaProdutoPedidoTransferenciaLicitacaoItemDTOParam> {

    private QueryConsultaProdutoPedidoTransferenciaLicitacaoItemDTOParam param = new QueryConsultaProdutoPedidoTransferenciaLicitacaoItemDTOParam();
    
    private InputField txtDescricao;
    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    
    public RestricaoContainerProdutoPedidoTransferenciaLicitacaoItem(String id) {
        super(id);
        
        WebMarkupContainer root = new WebMarkupContainer("root", new CompoundPropertyModel<QueryConsultaProdutoPedidoTransferenciaLicitacaoItemDTOParam>(param));
        
        root.add(txtDescricao = new InputField("descricao", String.class));
        root.add(getDropDownGrupo());
        root.add(getDropDownSubGrupo());
        
        txtDescricao.addAjaxUpdateValue();
        
        add(root);
    }

    @Override
    public QueryConsultaProdutoPedidoTransferenciaLicitacaoItemDTOParam getRestricoes() {
        return param;
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        txtDescricao.limpar(target);
        dropDownGrupoProduto.limpar(target);
        dropDownSubGrupo.limpar(target);
        dropDownSubGrupo.removeAllChoices(target);
        dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
        
        target.add(txtDescricao);
    }
    
    private DropDown<SubGrupo> getDropDownSubGrupo(){
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo");
            
            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
            
            dropDownSubGrupo.addAjaxUpdateValue();
        }
        
        return this.dropDownSubGrupo;
    }

    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProduto");
            
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.limpar(target);
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                    
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    
                    if (grupoProduto!=null) {
                            List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                    .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                    .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                    .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                    .start().getList();

                            if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                                for (SubGrupo subGrupo : subGrupos) {
                                    dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                                }
                            }
                            param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });
            
                List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                        .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                        .start().getList();

                dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));
                
                if (CollectionUtils.isNotNullEmpty(grupos)) {
                    for (GrupoProduto grupoProduto : grupos) {
                        dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                    }
                }
        }
        
        return this.dropDownGrupoProduto;
    }

    @Override
    public Component getComponentRequestFocus() {
        return txtDescricao;
    }
    
}
