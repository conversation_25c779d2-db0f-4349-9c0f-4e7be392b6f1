package br.com.celk.view.materiais.tipodocumento.customize;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaTipoDocumento extends CustomizeConsultaAdapter {

    private boolean apenasInterno = false;
    private boolean apenasNFEntrada = false;
    
    @Override
    public void consultaCustomizeFilterProperties(Map<String, BuilderQueryCustom.QueryParameter> filterProperties) {
        filterProperties.put(Bundle.getStringApplication("rotulo_codigo"), new QueryCustom.QueryCustomParameter(TipoDocumento.PROP_CODIGO, BuilderQueryCustom.QueryParameter.IGUAL));
        filterProperties.put(Bundle.getStringApplication("rotulo_descricao"), new QueryCustom.QueryCustomParameter(TipoDocumento.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.ILIKE));
        filterProperties.put(Bundle.getStringApplication("rotulo_sigla"), new QueryCustom.QueryCustomParameter(TipoDocumento.PROP_FLAG_SIGLA, BuilderQueryCustom.QueryParameter.ILIKE));
    }

    @Override
    public void consultaCustomizeViewProperties(Map<String, String> properties) {
        properties.put(Bundle.getStringApplication("rotulo_codigo"), TipoDocumento.PROP_CODIGO);
        properties.put(Bundle.getStringApplication("rotulo_descricao"), TipoDocumento.PROP_DESCRICAO);
        properties.put(Bundle.getStringApplication("rotulo_sigla"), TipoDocumento.PROP_FLAG_SIGLA);
    }

    @Override
    public void consultaCustomizeParameters(List<QueryParameter> parameters) {
        if (apenasInterno) {
            parameters.add(new QueryCustom.QueryCustomParameter(TipoDocumento.PROP_FLAG_INTERNO, RepositoryComponentDefault.SIM));
        }
        
        if (apenasNFEntrada) {
            parameters.add(new QueryCustom.QueryCustomParameter(TipoDocumento.PROP_FLAG_N_F_ENTRADA, RepositoryComponentDefault.SIM));
        }
    }

    @Override
    public Class getClassConsulta() {
        return TipoDocumento.class;
    }

    @Override
    public String[] getProperties() {
        return VOUtils.mergeProperties(new HQLProperties(TipoDocumento.class).getProperties());
    }

    public void setApenasInterno(boolean apenasInterno) {
        this.apenasInterno = apenasInterno;
    }
    
    public void setApenasNotaFiscalEntrada(boolean apenasNFEntrada) {
        this.apenasNFEntrada = apenasNFEntrada;
    }
}
