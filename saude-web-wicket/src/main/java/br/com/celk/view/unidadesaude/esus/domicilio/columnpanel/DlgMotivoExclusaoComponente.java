package br.com.celk.view.unidadesaude.esus.domicilio.columnpanel;

import br.com.celk.component.dialog.DlgMotivo;
import br.com.celk.component.window.Window;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.IModel;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgMotivoExclusaoComponente extends Window {

    private PnlMotivoExclusaoComponente pnlMotivoExclusaoComponente;

    public DlgMotivoExclusaoComponente(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        setInitialWidth(600);
        setInitialHeight(100);

        setResizable(false);

        setTitle(Bundle.getStringApplication("pergunta_excluir"));

        setContent(pnlMotivoExclusaoComponente = new PnlMotivoExclusaoComponente(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, Long motivo, Date dtObito, String numeroDo) throws ValidacaoException, DAOException {
                close(target);
                DlgMotivoExclusaoComponente.this.onConfirmar(target, motivo, dtObito, numeroDo);
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target, Long motivo, Date dtObito, String numeroDo) throws ValidacaoException, DAOException;

}
