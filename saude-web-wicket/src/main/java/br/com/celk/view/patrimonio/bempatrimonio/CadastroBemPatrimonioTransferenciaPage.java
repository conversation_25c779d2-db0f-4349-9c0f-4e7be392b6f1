package br.com.celk.view.patrimonio.bempatrimonio;

import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.longfield.DisabledLongField;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.methods.WicketMethods;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.centrocusto.autocomplete.AutoCompleteConsultaCentroCusto;
import br.com.celk.view.patrimonio.setor.autocomplete.AutoCompleteConsultaSetor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.patrimonio.interfaces.facade.PatrimonioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.patrimonio.BemPatrimonio;
import br.com.ksisolucoes.vo.patrimonio.Setor;
import br.com.ksisolucoes.vo.patrimonio.SetorLocalizacao;
import static ch.lambdaj.Lambda.on;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
public class CadastroBemPatrimonioTransferenciaPage extends BasePage {

    private final BemPatrimonio bemPatrimonio;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaCentroCusto autoCompleteConsultaCentroCusto;
    private AutoCompleteConsultaSetor autoCompleteConsultaSetor;
    private DropDown<SetorLocalizacao> dropDownSetorLocalizacao;
    private String descricaoOcorrencia = bundle("alteracaoLocalizacao") + ":";

    public CadastroBemPatrimonioTransferenciaPage(BemPatrimonio object) {
        super();
        this.bemPatrimonio = object;
        descricaoOcorrencia = descricaoOcorrencia + " " + bundle("estabelecimento") + ": " + object.getEmpresa().getDescricao();
        if(object.getCentroCusto() != null){
            descricaoOcorrencia = descricaoOcorrencia + " " + bundle("centroCusto") +": " + object.getCentroCusto().getDescricao();
        }
        if(object.getSetor() != null){
            descricaoOcorrencia = descricaoOcorrencia + " " + bundle("setor") +": " + object.getSetor().getDescricao();
        }
        if(object.getSetorLocalizacao() != null){
            descricaoOcorrencia = descricaoOcorrencia + " " + bundle("localizacao") +": " + object.getSetorLocalizacao().getDescricao();
        }
    }

    @Override
    protected void postConstruct() {
        Form form = new Form("form", new CompoundPropertyModel(bemPatrimonio));
        BemPatrimonio proxy = on(BemPatrimonio.class);

        configurarComponentes();

        form.add(new DisabledLongField("referencia"));
        form.add(new DisabledInputField("descricao"));
        form.add(new VoltarButton("btnVoltar"));

        form.add(autoCompleteConsultaEmpresa);
        form.add(autoCompleteConsultaCentroCusto);
        form.add(autoCompleteConsultaSetor);
        form.add(getDropDownSetorLocalizacao(path(proxy.getSetorLocalizacao())));
        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }));

        add(form);
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(PatrimonioFacade.class).transferirBemPatrimonio(bemPatrimonio, descricaoOcorrencia);

        setResponsePage(new ConsultaBensPatrimonioPage());
    }

    private void configurarComponentes() {
        if(bemPatrimonio != null){
            bemPatrimonio.setEmpresa(null);
            bemPatrimonio.setCentroCusto(null);
            bemPatrimonio.setSetor(null);
            bemPatrimonio.setSetorLocalizacao(null);
        }
        autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa", true);
        autoCompleteConsultaEmpresa.setLabel(new Model(bundle("estabelecimento")));
        autoCompleteConsultaCentroCusto = new AutoCompleteConsultaCentroCusto("centroCusto");
        autoCompleteConsultaSetor = new AutoCompleteConsultaSetor("setor");
        autoCompleteConsultaSetor.add(new ConsultaListener<Setor>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Setor setor) {
                procurarSetorLocalizacao(target, setor);
            }
        });
        autoCompleteConsultaSetor.add(new RemoveListener<Setor>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Setor setor) {
                if (setor != null) {
                    removeChoices(target);
                }
            }
        });
    }

    private DropDown getDropDownSetorLocalizacao(String id) {
        if (dropDownSetorLocalizacao == null) {
            dropDownSetorLocalizacao = new DropDown(id);

            procurarSetorLocalizacao(null, (Setor) autoCompleteConsultaSetor.getComponentValue());
        }
        return dropDownSetorLocalizacao;
    }

    private void procurarSetorLocalizacao(AjaxRequestTarget target, Setor setor) {
        if (setor != null) {
            List<SetorLocalizacao> list = LoadManager.getInstance(SetorLocalizacao.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(SetorLocalizacao.PROP_SETOR, setor))
                    .start().getList();

            dropDownSetorLocalizacao.addChoice(null, "");

            if (CollectionUtils.isNotNullEmpty(list)) {
                for (SetorLocalizacao item : list) {
                    dropDownSetorLocalizacao.addChoice(item, item.getDescricao());
                }
                dropDownSetorLocalizacao.setEnabled(true);
            }

        } else {
            dropDownSetorLocalizacao.setEnabled(false);
        }

        if (target != null) {
            target.add(dropDownSetorLocalizacao);
        }
    }

    private void removeChoices(AjaxRequestTarget target) {
        dropDownSetorLocalizacao.removeAllChoices(target);
        dropDownSetorLocalizacao.setComponentValue(null);
        dropDownSetorLocalizacao.setEnabled(false);
    }

    @Override
    public String getTituloPrograma() {
        return bundle("transferenciaDoBem");
    }
}
