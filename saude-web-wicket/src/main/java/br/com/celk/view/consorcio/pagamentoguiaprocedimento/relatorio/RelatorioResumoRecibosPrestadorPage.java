package br.com.celk.view.consorcio.pagamentoguiaprocedimento.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioResumoRecibosPrestadorDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Pessoa;
import org.apache.wicket.markup.html.form.Form;

import java.util.Arrays;


/**
 *
 * <AUTHOR>
 */
@Private

public class RelatorioResumoRecibosPrestadorPage extends RelatorioPage<RelatorioResumoRecibosPrestadorDTOParam> {

    private DropDown<String> dropDownTipoPessoa;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresaPrestador;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresaPrestador = new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));
        form.add(new AutoCompleteConsultaEmpresa("prestador").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_PRESTADOR_SERVICO)).setValidarTipoEstabelecimento(true));
        form.add(new PnlChoicePeriod("periodo"));
        form.add(getDropDownTipoPessoa());
        form.add(DropDownUtil.getNaoSimDropDown("paginaConsorciado"));
    }

    @Override
    public Class<RelatorioResumoRecibosPrestadorDTOParam> getDTOParamClass() {
        return RelatorioResumoRecibosPrestadorDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioResumoRecibosPrestadorDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(ConsorcioFacade.class).relatorioResumoRecibosPrestador(param);
    }

    public DropDown getDropDownTipoPessoa() {
        if (this.dropDownTipoPessoa == null) {
            this.dropDownTipoPessoa = new DropDown<String>("tipoPessoaPrestador");
            this.dropDownTipoPessoa.addChoice(null, BundleManager.getString("ambas"));
            this.dropDownTipoPessoa.addChoice(Pessoa.PESSOA_JURIDICA, BundleManager.getString("juridica"));
            this.dropDownTipoPessoa.addChoice(Pessoa.PESSOA_FISICA, BundleManager.getString("fisica"));
        }
        return dropDownTipoPessoa;
    }


    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("resumoRecibosPrestador");
    }

}
