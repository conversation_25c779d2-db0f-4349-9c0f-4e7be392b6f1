package br.com.celk.view.hospital.faturamento.dialogs.ipe;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.hospital.ipe.autocomplete.AutoCompleteConsultaHonorariosIpe;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.hospital.ipe.HonorariosIpe;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import static ch.lambdaj.Lambda.on;
import java.io.File;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.upload.FileUpload;
import org.apache.wicket.markup.html.form.upload.FileUploadField;
import org.apache.wicket.markup.html.form.upload.MultiFileUploadField;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.util.lang.Bytes;
import org.odlabs.wiquery.ui.datepicker.DateOption;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlNovoLancamentoAtendimentoComplementar extends Panel {

    private CompoundPropertyModel<ItemContaPaciente> model;
    private AbstractAjaxButton btnFechar;
    private Form<ItemContaPaciente> form;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private AutoCompleteConsultaHonorariosIpe autoCompleteConsultaHonorariosIpe;
    private HonorariosIpe honorarioIpe;
    private DateChooser dataChooser;
    private MultiFileUploadField mfuAnexo;
    private FileUploadField anexo;
    private List<FileUpload> upload;
    private FileUploadField fileUploadField;
    private AbstractAjaxButton btnDeletar;
    private Label lblAnexo;
    private String nomeAnexo;
    private AtendimentoInformacao atendimentoInformacao;

    public PnlNovoLancamentoAtendimentoComplementar(String id) {
        super(id);
        init();
    }

    private void init() {
        form = new Form<ItemContaPaciente>("form", model = new CompoundPropertyModel(new ItemContaPaciente()));
        ItemContaPaciente proxy = on(ItemContaPaciente.class);

        form.add(autoCompleteConsultaHonorariosIpe = new AutoCompleteConsultaHonorariosIpe("honorarioIpe", new PropertyModel(this, "honorarioIpe")));

        form.add(new InputField(path(proxy.getQuantidade())).setRequired(true));
        form.add(dataChooser = new DateChooser(path(proxy.getDataLancamento())));
        dataChooser.setRequired(true);
        dataChooser.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        
        form.add(fileUploadField = new FileUploadField("upload", new PropertyModel(this, "upload")));
        fileUploadField.setOutputMarkupId(true);

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validaLancamento();
                model.getObject().setProcedimento(honorarioIpe.getProcedimento());
                model.getObject().setPrecoUnitario(new Dinheiro(honorarioIpe.getValor()).multiplicar(model.getObject().getQuantidade()).doubleValue());

                if (upload != null) {
                    String clientFileName = upload.get(0).getClientFileName();
                    if (!clientFileName.endsWith(".pdf")) {
                        throw new ValidacaoException(bundle("msgSomentePermitidoAnexoTipoArquivoPdf"));
                    }
                    if (Bytes.bytes(upload.get(0).getSize()).greaterThan(Bytes.megabytes(getTamanhoMaxPdfGem()))) {
                        throw new ValidacaoException(BundleManager.getString("arquivoSelecionadoTamanhoInvalido", getTamanhoMaxPdfGem()));
                    }
                    onConfirmar(target, model.getObject(), upload.iterator().next());
                } else {
                    onConfirmar(target, model.getObject(), null);
                }
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnCancelar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        btnFechar.setDefaultFormProcessing(false);

        setOutputMarkupId(true);

        form.add(btnDeletar = new AbstractAjaxButton("btnDeletar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {

                model.getObject().setCaminhoAnexo(null);
                fileUploadField.setEnabled(true);
                lblAnexo.setVisible(false);
                btnDeletar.setVisible(false);
                target.add(fileUploadField);
                target.add(lblAnexo);
                target.add(btnDeletar);
            }
        });

        form.add(lblAnexo = new Label("lblAnexo", new PropertyModel(this, "nomeAnexo")));
        lblAnexo.setOutputMarkupId(true);

        form.setMultiPart(true);
        add(form);

        btnDeletar.setVisible(false);
    }

    private void validaLancamento() throws ValidacaoException, DAOException {

        if (honorarioIpe == null) {
            throw new ValidacaoException(BundleManager.getString("informeHonorario"));
        }

        if (model.getObject().getQuantidade().equals(0D)) {
            throw new ValidacaoException(BundleManager.getString("quantidadeNaoPodeZero"));
        }
        
        if(Data.adjustRangeHour(model.getObject().getDataLancamento()).getDataInicial().before(Data.adjustRangeHour(atendimentoInformacao.getDataChegada()).getDataInicial())){
            throw new ValidacaoException(BundleManager.getString("competenciaNaoPodeSerMenorDataChegadaAtendimentoDataChegadaX", 
                    Data.formatar(atendimentoInformacao.getDataChegada())));
        }
        
        if (DataUtil.getDataAtual().before(model.getObject().getDataLancamento())) {
            throw new ValidacaoException(BundleManager.getString("dataLancamentoNaoMaiorDataAtual"));
        }
        
        if (model.getObject().getProfissional() == null) {
            throw new ValidacaoException(BundleManager.getString("msgInformeProfissionalSolicitante"));
        }
        
        if (model.getObject().getProfissional().getNumeroRegistro() == null) {
            throw new ValidacaoException(BundleManager.getString("msgProfissionalSolicitanteDevePossuirNumeroRegistro"));
        }
        if(Coalesce.asDouble(this.model.getObject().getPrecoUnitario()) > 99999999.9999D){
            throw new ValidacaoException(BundleManager.getString("msgValorUltrapassouLimiteFavorVerificar"));
        }
    }

    public void limpar(AjaxRequestTarget target) {
        form.getModel().setObject(new ItemContaPaciente());
        target.add(form);
    }

    public void setItemContaPaciente(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente, AtendimentoInformacao atendimentoInformacao) {
        this.model.setObject(itemContaPaciente);
        this.atendimentoInformacao = atendimentoInformacao;
        
        if (itemContaPaciente != null && itemContaPaciente.getProcedimento() != null) {
            List<HonorariosIpe> hiList = LoadManager.getInstance(HonorariosIpe.class)
                    .addProperties(new HQLProperties(HonorariosIpe.class).getProperties())
                    .addProperties(new HQLProperties(Procedimento.class, HonorariosIpe.PROP_PROCEDIMENTO).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(HonorariosIpe.PROP_PROCEDIMENTO, itemContaPaciente.getProcedimento()))
                    .start().getList();
            
            if(CollectionUtils.isNotNullEmpty(hiList)){
                honorarioIpe = hiList.get(0);
            }
        }
        if (itemContaPaciente.getCaminhoAnexo() != null) {
            fileUploadField.setEnabled(false);
            btnDeletar.setVisible(true);
            this.nomeAnexo = itemContaPaciente.getCaminhoAnexo().substring(itemContaPaciente.getCaminhoAnexo().lastIndexOf(File.separator) + 1, itemContaPaciente.getCaminhoAnexo().length());
        }
        update(target);
        target.focusComponent(autoCompleteConsultaHonorariosIpe.getTxtDescricao().getTextField());
    }

    private void update(AjaxRequestTarget target) {
        target.add(this);
    }

    private Long getTamanhoMaxPdfGem() throws DAOException {
        Long tamanhoMaximo = (Long) BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("tamanhoMaximoPDFComplementarIPE");
        return tamanhoMaximo;
    }

    public abstract void onConfirmar(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente, FileUpload file) throws DAOException, ValidacaoException;

    public abstract void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException;
}
