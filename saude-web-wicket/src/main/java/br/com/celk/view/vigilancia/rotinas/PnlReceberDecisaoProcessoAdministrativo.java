package br.com.celk.view.vigilancia.rotinas;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.Date;

/**
 * Created by Leonardo.
 */
public abstract class PnlReceberDecisaoProcessoAdministrativo extends Panel {

    private DateChooser dchRecebimento;
//    private AutoCompleteConsultaMotivoRetorno autoCompleteConsultaMotivoRetorno;
//    private AbstractAjaxLink btnCadastrarMotivo;
//    private DlgCadastroMotivoRetorno dlgCadastroMotivoRetorno;
    private Form<ProcessoAdministrativo> form;
    private ProcessoAdministrativo processoAdministrativo;
    private Date dataRecebimento;

    public PnlReceberDecisaoProcessoAdministrativo(String id, ProcessoAdministrativo processoAdministrativo) {
        super(id);
        this.processoAdministrativo = processoAdministrativo;
        init();
    }


    public void init() {
        setOutputMarkupId(true);
        form = new Form("form", new CompoundPropertyModel(this));

        form.add(dchRecebimento = new RequiredDateChooser("dataRecebimento"));
        dchRecebimento.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        dchRecebimento.addAjaxUpdateValue();


        form.add(new SubmitButton("btnSalvar",new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onSalvar(target, dataRecebimento, processoAdministrativo);
            }
        }).setDefaultFormProcessing(true));

        form.add(new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        add(form);

    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    public abstract void onSalvar(AjaxRequestTarget target, Date dataRecebimento, ProcessoAdministrativo processoAdministrativo) throws ValidacaoException, DAOException;

//    private void showDlgCadastrarMotivoRetorno(AjaxRequestTarget target) {
//        if (dlgCadastroMotivoRetorno == null) {
//            WindowUtil.addModal(target, this, dlgCadastroMotivoRetorno = new DlgCadastroMotivoRetorno(WindowUtil.newModalId(PnlReceberDecisaoProcessoAdministrativo.this)){
//                @Override
//                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
//                    target.add(autoCompleteConsultaMotivoRetorno);
//                    autoCompleteConsultaMotivoRetorno.focus(target);
//                }
//
//                @Override
//                public void onExcluir(AjaxRequestTarget target, MotivoRetorno motivoRetorno) throws ValidacaoException, DAOException {
//                    if (form.getModel().getObject().getMotivoRetorno() != null && motivoRetorno.equals(form.getModel().getObject().getMotivoRetorno())) {
//                        autoCompleteConsultaMotivoRetorno.limpar(target);
//                    }
//                }
//            });
//        }
//        dlgCadastroMotivoRetorno.show(target);
//    }
}
