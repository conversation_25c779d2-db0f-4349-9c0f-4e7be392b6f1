package br.com.celk.view.vigilancia.configuracao;

import br.com.celk.component.tabbedpanel.cadastro.CadastroTabbedPanel;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.CadastroConfiguracaoVigilanciaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaGerarFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaEnum;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ControleNumeracaoReceitaB;
import ch.lambdaj.Lambda;
import org.apache.wicket.extensions.markup.html.tabs.ITab;
import org.hamcrest.Matchers;

import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 * <AUTHOR>
 */
public class ConfiguracaoVigilanciaTabbedPanel extends CadastroTabbedPanel<CadastroConfiguracaoVigilanciaDTO> {

    public ConfiguracaoVigilanciaTabbedPanel(String id, CadastroConfiguracaoVigilanciaDTO dto, boolean viewOnly, List<ITab> tabs) {
        super(id, dto, viewOnly, tabs, true);
        getCadastroTabbedPanelBar().getBtnVoltar().setVisible(false);
    }

    public ConfiguracaoVigilanciaTabbedPanel(String id, CadastroConfiguracaoVigilanciaDTO dto, List<ITab> tabs) {
        super(id, dto, tabs);
        getCadastroTabbedPanelBar().getBtnVoltar().setVisible(false);
        getForm().setMultiPart(true);
    }

    public ConfiguracaoVigilanciaTabbedPanel(String id, List<ITab> tabs) {
        super(id, tabs);
    }

    @Override
    public Class<CadastroConfiguracaoVigilanciaDTO> getReferenceClass() {
        return CadastroConfiguracaoVigilanciaDTO.class;
    }

    @Override
    public Object salvar(CadastroConfiguracaoVigilanciaDTO dto) throws DAOException, ValidacaoException {
        validarDataFixa(dto);
        validarFormaCobranca(dto);
        validarTamanhoCampos(dto);

        if (ConfiguracaoVigilanciaFinanceiro.TipoCalculoTaxa.TALAO.value().equals(dto.getConfiguracaoVigilanciaFinanceiro().getTipoCalculoTaxaReceita()) && dto.getConfiguracaoVigilanciaFinanceiro().getNumeroFolhasTalao() == null) {
            throw new ValidacaoException(bundle("msgInformeNumeroFolhasTalaoReceita"));
        }

        if (ConfiguracaoVigilanciaEnum.TipoGestaoAtividade.CNAE.value().equals(dto.getConfiguracaoVigilancia().getFlagTipoGestaoAtividade())
                && ConfiguracaoVigilanciaEnum.TipoAtividadeAlvara.CNAE.value().equals(dto.getConfiguracaoVigilancia().getTipoAtividadeAlvara())) {
            throw new ValidacaoException("Para o tipo de gestão de atividades por CNAE informe na Aba Alvará o tipo de impressão como Atividades");
        }

        validarControleNumeracaoReceitaB(dto);
        validarFinanceiro(dto);

        BOFactoryWicket.getBO(VigilanciaFacade.class).salvarConfiguracaoVigilancia(dto);
        return null;
    }

    private void validarControleNumeracaoReceitaB(CadastroConfiguracaoVigilanciaDTO dto) throws ValidacaoException {
        List<ControleNumeracaoReceitaB> controleNumeracaoReceitaBList = LoadManager.getInstance(ControleNumeracaoReceitaB.class)
                .addProperties(new HQLProperties(ControleNumeracaoReceitaB.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(ControleNumeracaoReceitaB.PROP_SUBTIPO, BuilderQueryCustom.QueryParameter.NOT_IN, Arrays.asList(ControleNumeracaoReceitaB.Subtipo.C2_RETINOIDES.value(), ControleNumeracaoReceitaB.Subtipo.TALIDOMIDA.value())))
                .start().getList();

        for (ControleNumeracaoReceitaB controleNumeracaoReceitaB : controleNumeracaoReceitaBList) {
            String numeracao = Coalesce.asLong(controleNumeracaoReceitaB.getNumeracaoAtual(), 0L).toString();
            if (!ConfiguracaoVigilanciaEnum.FormatoSequencial.OITO_DIGITOS.value().equals(dto.getConfiguracaoVigilancia().getFlagFormatoSequencial())) {
                if (numeracao.length() > 6) {
                    throw new ValidacaoException(bundle("msgInformeNumeroReceitaBMaximoXDigitos", " 6 "));
                }
            }
        }
    }

    private boolean isAlvaraInicialDataFixa(CadastroConfiguracaoVigilanciaDTO dto) {
        return RepositoryComponentDefault.SIM_LONG.equals(dto.getConfiguracaoVigilancia().getValidadeAlvaraDataFixa());
    }

    private boolean isAlvaraInicialUsandoDataAtividadeEstabelecimento(CadastroConfiguracaoVigilanciaDTO dto) {
        return RepositoryComponentDefault.SIM_LONG.equals(dto.getConfiguracaoVigilancia().getAlvaraInicialUsaDataVencimentoDaAtividadeEstabelecimento());
    }

    private boolean isRevalidacaoDataFixa(CadastroConfiguracaoVigilanciaDTO dto) {
        return RepositoryComponentDefault.SIM_LONG.equals(dto.getConfiguracaoVigilancia().getAlvaraRevalidacaoUsaDataFixa());
    }

    private boolean isRevalidacaoAlvaraUsandoDataAtividadeEstabelecimento(CadastroConfiguracaoVigilanciaDTO dto) {
        return RepositoryComponentDefault.SIM_LONG.equals(dto.getConfiguracaoVigilancia().getRevalidacaoAlvaraUsaDataVencimentoDaAtividadeEstabelecimento());
    }

    private boolean isAutorizacaoSanitariaDataFixa(CadastroConfiguracaoVigilanciaDTO dto) {
        return RepositoryComponentDefault.SIM_LONG.equals(dto.getConfiguracaoVigilancia().getAutorizacaoSanitariaUsaDataFixa());
    }

    private boolean isCredenciamentoTreinamentoDataFixa(CadastroConfiguracaoVigilanciaDTO dto) {
        return RepositoryComponentDefault.SIM_LONG.equals(dto.getConfiguracaoVigilancia().getValidadeTreinamentoDataFixa());
    }

    private boolean isLicencaTransporteDataFixa(CadastroConfiguracaoVigilanciaDTO dto) {
        return RepositoryComponentDefault.SIM_LONG.equals(dto.getConfiguracaoVigilancia().getValidadeLicencaDataFixa());
    }

    private boolean isLicencaTransporteUsandoDataAtividadeEstabelecimento(CadastroConfiguracaoVigilanciaDTO dto) {
        return RepositoryComponentDefault.SIM_LONG.equals(dto.getConfiguracaoVigilancia().getLicencaTransporteUsaDataVencimentoDaAtividadeEstabelecimento());
    }

    private void validarDataFixa(CadastroConfiguracaoVigilanciaDTO dto) throws ValidacaoException {
        if (isAlvaraInicialDataFixa(dto) && !isAlvaraInicialUsandoDataAtividadeEstabelecimento(dto) && dto.getConfiguracaoVigilancia().getValidadeAlvaraDataVencimento() == null) {
            throw new ValidacaoException("Informe a data fixa de vencimento do Alvará Inicial!");
        }

        if (isRevalidacaoDataFixa(dto) && !isRevalidacaoAlvaraUsandoDataAtividadeEstabelecimento(dto)) {
            if (dto.getConfiguracaoVigilancia().getDataVencimentoRevalidacaoAlvara() == null) {
                throw new ValidacaoException("Informe a data fixa de vencimento da Revalidação do Alvará!");
            }
            if (dto.getConfiguracaoVigilancia().getDataInicialRevalidacaoAlvara() == null || dto.getConfiguracaoVigilancia().getDataFinalRevalidacaoAlvara() == null) {
                throw new ValidacaoException(bundle("msgInformePeriodoRevalidacaoAlvara"));
            }
            if (dto.getConfiguracaoVigilancia().getDataFinalRevalidacaoAlvara().compareTo(dto.getConfiguracaoVigilancia().getDataInicialRevalidacaoAlvara()) <= 0) {
                throw new ValidacaoException(bundle("msgDataFinalDeveSerMaiorDataInicialPeriodoRevalidacaoAlvara"));
            }
        }

        if (isAutorizacaoSanitariaDataFixa(dto) && dto.getConfiguracaoVigilancia().getDataVencimentoAutorizacaoSanitaria() == null) {
            throw new ValidacaoException("Informe a data fixa de vencimento da Autorização Sanitária!");
        }

        if (isCredenciamentoTreinamentoDataFixa(dto) && dto.getConfiguracaoVigilancia().getValidadeTreinamentoDataVencimento() == null) {
            throw new ValidacaoException("Informe a data fixa de vencimento do Credenciamento para Treinamento!");
        }

        if (isLicencaTransporteDataFixa(dto) && !isLicencaTransporteUsandoDataAtividadeEstabelecimento(dto)) {
            if (dto.getConfiguracaoVigilancia().getValidadeLicencaDataVencimento() == null) {
                throw new ValidacaoException("Informe a data fixa de vencimento da Revalidação do Alvará!");
            }
            if (dto.getConfiguracaoVigilancia().getDataInicialLicencaTransporte() == null || dto.getConfiguracaoVigilancia().getDataFinalLicencaTransporte() == null) {
                throw new ValidacaoException(bundle("msgInformePeriodoRevalidacaoAlvara"));
            }
            if (dto.getConfiguracaoVigilancia().getDataFinalLicencaTransporte().compareTo(dto.getConfiguracaoVigilancia().getDataInicialLicencaTransporte()) <= 0) {
                throw new ValidacaoException(bundle("msgDataFinalDeveSerMaiorDataInicialPeriodoLicencaTransporte"));
            }
        }
    }

    private void validarFormaCobranca(CadastroConfiguracaoVigilanciaDTO dto) throws ValidacaoException {
        if (isFormaCobrancaMemorando(dto)) {
            if (isAlvaraInicialAprovacaoPagamento(dto)) {
                throw new ValidacaoException(bundle("msgValidacaoMemorando", "Alvará Inicial"));
            }
            if (isRevalidacaoAprovacaoPagamento(dto)) {
                throw new ValidacaoException(bundle("msgValidacaoMemorando", "Revalidação de Alvará"));
            }
            if (isAutorizacaoSanitariaAprovacaoPagamento(dto)) {
                throw new ValidacaoException(bundle("msgValidacaoMemorando", "Autorização Sanitária"));
            }
            if (isTreinamentoCredenciamentoAprovacaoPagamento(dto)) {
                throw new ValidacaoException(bundle("msgValidacaoMemorando", "Treinamento/Credenciamento"));
            }
        }
    }

    private boolean isFormaCobrancaMemorando(CadastroConfiguracaoVigilanciaDTO dto) {
        return ConfiguracaoVigilanciaFinanceiro.FormaCobranca.MEMORANDO.value().equals(dto.getConfiguracaoVigilanciaFinanceiro().getFormaCobranca());
    }

    private boolean isAlvaraInicialAprovacaoPagamento(CadastroConfiguracaoVigilanciaDTO dto) {
        return ConfiguracaoVigilanciaEnum.TipoDataCalculoAlvaraInicial.APROVACAO_PAGAMENTO.value().equals(dto.getConfiguracaoVigilancia().getTipoDataCalcAlvara());
    }

    private boolean isRevalidacaoAprovacaoPagamento(CadastroConfiguracaoVigilanciaDTO dto) {
        return ConfiguracaoVigilanciaEnum.TipoDataCalculoRevalidacao.APROVACAO_PAGAMENTO.value().equals(dto.getConfiguracaoVigilancia().getTipoDataCalculoRevalidacaoAlvara());
    }

    private boolean isAutorizacaoSanitariaAprovacaoPagamento(CadastroConfiguracaoVigilanciaDTO dto) {
        return ConfiguracaoVigilanciaEnum.TipoDataCalculoAutorizacaoSanitaria.APROVACAO_PAGAMENTO.value().equals(dto.getConfiguracaoVigilancia().getTipoDataCalculoAutorizacaoSanitaria());
    }

    private boolean isTreinamentoCredenciamentoAprovacaoPagamento(CadastroConfiguracaoVigilanciaDTO dto) {
        return ConfiguracaoVigilanciaEnum.TipoDataBaseCredenciamento.APROVACAO_PAGAMENTO.value().equals(dto.getConfiguracaoVigilancia().getTipoDataCalcCredenciamento());
    }

    private void validarTamanhoCampos(CadastroConfiguracaoVigilanciaDTO dto) throws ValidacaoException {
        if (dto.getConfiguracaoVigilancia().getTextoAprovado() != null && dto.getConfiguracaoVigilancia().getTextoAprovado().length() < 3) {
            throw new ValidacaoException(bundle("campoAprovadoRequerimentoAnaliseMenorXCaracteres", " 3 "));
        }
        if (dto.getConfiguracaoVigilancia().getTextoReprovado() != null && dto.getConfiguracaoVigilancia().getTextoReprovado().length() < 3) {
            throw new ValidacaoException(bundle("campoReprovadoRequerimentoAnaliseMenorXCaracteres", " 3 "));
        }

        if (dto.getConfiguracaoVigilancia().getCienciaAutoIntimacao() != null && dto.getConfiguracaoVigilancia().getCienciaAutoIntimacao().length() > 2500){
            throw new ValidacaoException(bundle("campoReprovadoMultasMaior2500Caracteres", " Ciência do Auto de Intimação "));
        }

        if (dto.getConfiguracaoVigilancia().getCienciaAutoInfracao() != null && dto.getConfiguracaoVigilancia().getCienciaAutoInfracao().length() > 2500){
            throw new ValidacaoException(bundle("campoReprovadoMultasMaior2500Caracteres", " Ciência do Auto de Infração"));
        }

        if (dto.getConfiguracaoVigilancia().getCienciaAutoPenalidade() != null && dto.getConfiguracaoVigilancia().getCienciaAutoPenalidade().length() > 2500){
            throw new ValidacaoException(bundle("campoReprovadoMultasMaior2500Caracteres", " Ciência do Auto de Penalidade "));
        }

        if (dto.getConfiguracaoVigilancia().getCienciaAutoMulta() != null && dto.getConfiguracaoVigilancia().getCienciaAutoMulta().length() > 2500){
            throw new ValidacaoException(bundle("campoReprovadoMultasMaior2500Caracteres", " Ciência do Auto de Multa "));
        }
    }


    private void validarFinanceiro(CadastroConfiguracaoVigilanciaDTO dto) throws ValidacaoException {
        if (br.com.celk.util.CollectionUtils.isNotNullEmpty(dto.getVigilanciaGerarFinanceiroList())) {
            List<VigilanciaGerarFinanceiro> requerimentos = dto.getVigilanciaGerarFinanceiroList();
            List<VigilanciaGerarFinanceiro> inspecao = Lambda.select(requerimentos, Lambda.having(Lambda.on(VigilanciaGerarFinanceiro.class).getTipoDocumento(), Matchers.equalTo(VigilanciaGerarFinanceiro.GerarFinanceiro.INSPECAO_SANITARIA_AFE_ANVISA.value())));
            List<VigilanciaGerarFinanceiro> livro = Lambda.select(requerimentos, Lambda.having(Lambda.on(VigilanciaGerarFinanceiro.class).getTipoDocumento(), Matchers.equalTo(VigilanciaGerarFinanceiro.GerarFinanceiro.ABERTURA_LIVRO_CONTROLE.value())));
            List<VigilanciaGerarFinanceiro> baixaRt = Lambda.select(requerimentos, Lambda.having(Lambda.on(VigilanciaGerarFinanceiro.class).getTipoDocumento(), Matchers.equalTo(VigilanciaGerarFinanceiro.GerarFinanceiro.BAIXA_RESPONSABILIDADE_TECNICA.value())));
            List<VigilanciaGerarFinanceiro> licencaTransporte = Lambda.select(requerimentos, Lambda.having(Lambda.on(VigilanciaGerarFinanceiro.class).getTipoDocumento(), Matchers.equalTo(VigilanciaGerarFinanceiro.GerarFinanceiro.LICENCA_TRANSPORTE.value())));
            List<VigilanciaGerarFinanceiro> receitaB = Lambda.select(requerimentos, Lambda.having(Lambda.on(VigilanciaGerarFinanceiro.class).getTipoDocumento(), Matchers.equalTo(VigilanciaGerarFinanceiro.GerarFinanceiro.REQUISICAO_RECEITUARIO_B.value())));
            List<VigilanciaGerarFinanceiro> extramuro = Lambda.select(requerimentos, Lambda.having(Lambda.on(VigilanciaGerarFinanceiro.class).getTipoDocumento(), Matchers.equalTo(VigilanciaGerarFinanceiro.GerarFinanceiro.VACINACAO_EXTRAMURO.value())));
            List<VigilanciaGerarFinanceiro> eventoParticipantes = Lambda.select(requerimentos, Lambda.having(Lambda.on(VigilanciaGerarFinanceiro.class).getTipoDocumento(), Matchers.equalTo(VigilanciaGerarFinanceiro.GerarFinanceiro.ALVARA_PARTICIPANTE_EVENTO.value())));
            List<VigilanciaGerarFinanceiro> eventoCadastro = Lambda.select(requerimentos, Lambda.having(Lambda.on(VigilanciaGerarFinanceiro.class).getTipoDocumento(), Matchers.equalTo(VigilanciaGerarFinanceiro.GerarFinanceiro.ALVARA_CADASTRO_EVENTO.value())));
            List<VigilanciaGerarFinanceiro> inclusaoRt = Lambda.select(requerimentos, Lambda.having(Lambda.on(VigilanciaGerarFinanceiro.class).getTipoDocumento(), Matchers.equalTo(VigilanciaGerarFinanceiro.GerarFinanceiro.ENTRADA_RESPONSABILIDADE_TECNICA.value())));
            List<VigilanciaGerarFinanceiro> inspecaoRotina = Lambda.select(requerimentos, Lambda.having(Lambda.on(VigilanciaGerarFinanceiro.class).getTipoDocumento(), Matchers.equalTo(VigilanciaGerarFinanceiro.GerarFinanceiro.INSPECAO_SANITARIA_COMUM.value())));
            List<VigilanciaGerarFinanceiro> credenciamentoTreinamento = Lambda.select(requerimentos, Lambda.having(Lambda.on(VigilanciaGerarFinanceiro.class).getTipoDocumento(), Matchers.equalTo(VigilanciaGerarFinanceiro.GerarFinanceiro.CREDENCIAMENTO_TREINAMENTO.value())));
            List<VigilanciaGerarFinanceiro> baixaEstabelecimento = Lambda.select(requerimentos, Lambda.having(Lambda.on(VigilanciaGerarFinanceiro.class).getTipoDocumento(), Matchers.equalTo(VigilanciaGerarFinanceiro.GerarFinanceiro.BAIXA_ESTABELECIMENTO.value())));
            List<VigilanciaGerarFinanceiro> autorizacaoSanitaria = Lambda.select(requerimentos, Lambda.having(Lambda.on(VigilanciaGerarFinanceiro.class).getTipoDocumento(), Matchers.equalTo(VigilanciaGerarFinanceiro.GerarFinanceiro.AUTORIZACAO_SANITARIA.value())));

            List<VigilanciaGerarFinanceiro> baixaVeiculo = Lambda.select(requerimentos, Lambda.having(Lambda.on(VigilanciaGerarFinanceiro.class).getTipoDocumento(), Matchers.equalTo(VigilanciaGerarFinanceiro.GerarFinanceiro.BAIXA_VEICULO.value())));
            List<VigilanciaGerarFinanceiro> alteracaoResponsabilidadeLegal = Lambda.select(requerimentos, Lambda.having(Lambda.on(VigilanciaGerarFinanceiro.class).getTipoDocumento(), Matchers.equalTo(VigilanciaGerarFinanceiro.GerarFinanceiro.ALTERACAO_RESPONSABILIDADE_LEGAL.value())));
            List<VigilanciaGerarFinanceiro> alteracaoRazaoSocial = Lambda.select(requerimentos, Lambda.having(Lambda.on(VigilanciaGerarFinanceiro.class).getTipoDocumento(), Matchers.equalTo(VigilanciaGerarFinanceiro.GerarFinanceiro.ALTERACAO_RAZAO_SOCIAL.value())));
            List<VigilanciaGerarFinanceiro> alteracaoAtividadeEconomica = Lambda.select(requerimentos, Lambda.having(Lambda.on(VigilanciaGerarFinanceiro.class).getTipoDocumento(), Matchers.equalTo(VigilanciaGerarFinanceiro.GerarFinanceiro.ALTERACAO_ATIVIDADE_ECONOMICA.value())));


            if (CollectionUtils.isNotNullEmpty(inspecao)) {
                if (RepositoryComponentDefault.NAO_LONG.equals(dto.getConfiguracaoVigilanciaFinanceiro().getFlagTaxaUnicaInspecao())) {
                    if (dto.getConfiguracaoVigilanciaFinanceiro().getTaxaInspecaoNaturezaSimples() == null || dto.getConfiguracaoVigilanciaFinanceiro().getTaxaInspecaoNaturezaComplexa() == null) {
                        throw new ValidacaoException(bundle("msgInformeTaxaInspecao"));
                    }
                    if (dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaInspecaoNaturezaSimples() == null || dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaInspecaoNaturezaComplexa() == null) {
                        throw new ValidacaoException(bundle("msgInformeQuantidadeTaxaInspecao"));
                    }
                } else {
                    if (dto.getConfiguracaoVigilanciaFinanceiro().getTaxaUnicaInspecao() == null) {
                        throw new ValidacaoException(bundle("msgInformeTaxaInspecao"));
                    }
                    if (dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaUnicaInspecao() == null) {
                        throw new ValidacaoException(bundle("msgInformeQuantidadeTaxaInspecao"));
                    }
                }
            }
            if (CollectionUtils.isNotNullEmpty(livro)) {
                if (dto.getConfiguracaoVigilanciaFinanceiro().getTaxaLivro() == null) {
                    throw new ValidacaoException(bundle("msgInformeTaxaLivro"));
                }
                if (dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaLivro() == null) {
                    throw new ValidacaoException(bundle("msgInformeQuantidadeTaxaLivro"));
                }
            }
            if (CollectionUtils.isNotNullEmpty(baixaRt)) {
                if (dto.getConfiguracaoVigilanciaFinanceiro().getTaxaBaixaRt() == null) {
                    throw new ValidacaoException(bundle("msgInformeTaxaBaixaRt"));
                }
                if (dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaBaixaRt() == null) {
                    throw new ValidacaoException(bundle("msgInformeQuantidadeTaxaBaixaRt"));
                }
            }

            if (CollectionUtils.isNotNullEmpty(inclusaoRt)) {
                if (dto.getConfiguracaoVigilanciaFinanceiro().getTaxaInclusaoRt() == null) {
                    throw new ValidacaoException(bundle("msgInformeTaxaInclusaoRt"));
                }
                if (dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaInclusaoRt() == null) {
                    throw new ValidacaoException(bundle("msgInformeQuantidadeTaxaInclusaoRt"));
                }
            }

            if (CollectionUtils.isNotNullEmpty(inspecaoRotina)) {
                if (dto.getConfiguracaoVigilanciaFinanceiro().getTaxaInspecaoRotina() == null) {
                    throw new ValidacaoException(bundle("msgInformeTaxaInspecaoRotina"));
                }
                if (dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaInspecaoRotina() == null) {
                    throw new ValidacaoException(bundle("msgInformeQuantidadeTaxaInspecaoSanitaria"));
                }
            }

            if (CollectionUtils.isNotNullEmpty(licencaTransporte)) {
                if (ConfiguracaoVigilanciaFinanceiro.TipoCalculoLicencaTransporte.CONSIDERAR_REFRIGERACAO.value().equals(dto.getConfiguracaoVigilanciaFinanceiro().getTipoCalculoTaxaLicencaTransporte())) {
                    if (dto.getConfiguracaoVigilanciaFinanceiro().getTaxaVeiculoRefrigerado() == null || dto.getConfiguracaoVigilanciaFinanceiro().getTaxaVeiculoNaoRefrigerado() == null) {
                        throw new ValidacaoException(bundle("msgInformeTaxaLicTransporte"));
                    }
                    if (dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaVeiculoRefrigerado() == null || dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaVeiculoNaoRefrigerado() == null) {
                        throw new ValidacaoException(bundle("msgInformeQuantidadeTaxaLicTransporte"));
                    }
                } else if (ConfiguracaoVigilanciaFinanceiro.TipoCalculoLicencaTransporte.TAXA_UNICA.value().equals(dto.getConfiguracaoVigilanciaFinanceiro().getTipoCalculoTaxaLicencaTransporte())) {
                    if (dto.getConfiguracaoVigilanciaFinanceiro().getTaxaUnicaLicencaTransporte() == null) {
                        throw new ValidacaoException(bundle("msgInformeTaxaLicTransporte"));
                    }
                    if (dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaUnicaLicencaTransporte() == null) {
                        throw new ValidacaoException(bundle("msgInformeQuantidadeTaxaLicTransporte"));
                    }
                }


                if (ConfiguracaoVigilanciaFinanceiro.FormaCobranca.MEMORANDO.value().equals(dto.getConfiguracaoVigilanciaFinanceiro().getFormaCobranca())) {
                    if (ConfiguracaoVigilanciaFinanceiro.TipoCobrancaLicencaTransporte.POR_VEICULO.value().equals(dto.getConfiguracaoVigilanciaFinanceiro().getTipoCobrancaLicencaTransporte())) {
                        throw new ValidacaoException("A cobrança de forma separada por veículo é possível apenas quando a forma de cobrança dos recursos for boleto");
                    }
                }
            }

            if (CollectionUtils.isNotNullEmpty(autorizacaoSanitaria)) {
                if (dto.getConfiguracaoVigilanciaFinanceiro().getTaxaAutorizacaoSanitaria() == null) {
                    throw new ValidacaoException(bundle("msgInformeTaxaAutorizacaoSanitaria"));
                }
                if (dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaAutorizacaoSanitaria() == null) {
                    throw new ValidacaoException(bundle("msgInformeQuantidadeTaxaAutorizacaoSanitaria"));
                }
            }
            if (CollectionUtils.isNotNullEmpty(receitaB)) {
                if (dto.getConfiguracaoVigilanciaFinanceiro().getTaxaReceita() == null) {
                    throw new ValidacaoException(bundle("msgInformeTaxaReceita"));
                }
                if (dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaReceita() == null) {
                    throw new ValidacaoException(bundle("msgInformeQuantidadeTaxaReceita"));
                }
            }
            if (CollectionUtils.isNotNullEmpty(extramuro)) {
                if (dto.getConfiguracaoVigilanciaFinanceiro().getTaxaVacinacaoExtramuro() == null) {
                    throw new ValidacaoException(bundle("msgInformeTaxaVacinacaoExtramuro"));
                }
                if (dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaVacinacaoExtramuro() == null) {
                    throw new ValidacaoException(bundle("msgInformeQuantidadeTaxaVacinacaoExtramuro"));
                }
            }
            if (CollectionUtils.isNotNullEmpty(eventoCadastro)) {
                if (dto.getConfiguracaoVigilanciaFinanceiro().getTaxaAlvaraEvento() == null) {
                    throw new ValidacaoException(bundle("msgInformeTaxaAlvaraEvento"));
                }
                if (dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaAlvaraEvento() == null) {
                    throw new ValidacaoException(bundle("msgInformeQuantidadeTaxaAlvaraEvento"));
                }
            }
            if (CollectionUtils.isNotNullEmpty(eventoParticipantes)) {
                if (dto.getConfiguracaoVigilanciaFinanceiro().getTaxaAlvaraParticipanteEvento() == null) {
                    throw new ValidacaoException(bundle("msgInformeTaxaAlvaraParticipanteEvento"));
                }
                if (dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaAlvaraParticipanteEvento() == null) {
                    throw new ValidacaoException(bundle("msgInformeQuantidadeTaxaAlvaraParticipanteEvento"));
                }
            }
            if (CollectionUtils.isNotNullEmpty(credenciamentoTreinamento)) {
                if (dto.getConfiguracaoVigilanciaFinanceiro().getTaxaCredenciamentoTreinamento() == null) {
                    throw new ValidacaoException(bundle("msgInformeTaxaX", " do Credenciamento para Treinamento"));
                }
                if (dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaCredenciamentoTreinamento() == null) {
                    throw new ValidacaoException(bundle("msgInformeValorTaxaX", " do Credenciamento para Treinamento"));
                }
            }

            if (CollectionUtils.isNotNullEmpty(baixaEstabelecimento)) {
                if (dto.getConfiguracaoVigilanciaFinanceiro().getTaxaBaixaEstabelecimento() == null) {
                    throw new ValidacaoException(bundle("msgInformeTaxaX", " da Baixa de Estabelecimento"));
                }
                if (dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaBaixaEstabelecimento() == null) {
                    throw new ValidacaoException(bundle("msgInformeValorTaxaX", " da Baixa de Estabelecimento"));
                }
            }

            if (CollectionUtils.isNotNullEmpty(baixaVeiculo)) {
                if (dto.getConfiguracaoVigilanciaFinanceiro().getTaxaBaixaVeiculo() == null) {
                    throw new ValidacaoException(bundle("msgInformeTaxaX", " da Baixa de Veículo"));
                }
                if (dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaBaixaVeiculo() == null ||
                    dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaBaixaVeiculo() <= 0L) {
                    throw new ValidacaoException(bundle("msgInformeValorTaxaX", " da Baixa de Veículo"));
                }
            }

            if (CollectionUtils.isNotNullEmpty(alteracaoResponsabilidadeLegal)) {
                if (dto.getConfiguracaoVigilanciaFinanceiro().getTaxaAlteracaoResponsabilidadeLegal() == null) {
                    throw new ValidacaoException(bundle("msgInformeTaxaX", " da Alteração de Responsabilidade Legal"));
                }
                if (dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaAlteracaoResponsabilidadeLegal() == null ||
                    dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaAlteracaoResponsabilidadeLegal() <= 0L) {
                    throw new ValidacaoException(bundle("msgInformeValorTaxaX", " da Alteração de Responsabilidade Legal"));
                }
            }

            if (CollectionUtils.isNotNullEmpty(alteracaoRazaoSocial)) {
                if (dto.getConfiguracaoVigilanciaFinanceiro().getTaxaAlteracaoRazaoSocial() == null) {
                    throw new ValidacaoException(bundle("msgInformeTaxaX", " da Alteração de Razão Social"));
                }
                if (dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaAlteracaoRazaoSocial() == null ||
                    dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaAlteracaoRazaoSocial() <= 0L) {
                    throw new ValidacaoException(bundle("msgInformeValorTaxaX", " da Alteração de Razão Social"));
                }
            }

            if (CollectionUtils.isNotNullEmpty(alteracaoAtividadeEconomica)) {
                if (dto.getConfiguracaoVigilanciaFinanceiro().getTaxaAlteracaoAtividadeEconomica() == null) {
                    throw new ValidacaoException(bundle("msgInformeTaxaX", " da Alteração de Atividade Econômica"));
                }
                if (dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaAlteracaoAtividadeEconomica() == null ||
                    dto.getConfiguracaoVigilanciaFinanceiro().getQuantidadeTaxaAlteracaoAtividadeEconomica() <= 0L) {
                    throw new ValidacaoException(bundle("msgInformeValorTaxaX", " da Alteração de Atividade Econômica"));
                }
            }
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(dto.getConfiguracaoVigilancia().getFlagCadastraRequerimentoExterno())) {
            if (dto.getConfiguracaoVigilancia().getEmpresaPadraoUsuarioExterno() == null) {
                throw new ValidacaoException("Complete a configuração do acesso externo informando a empresa padrão");
            }
            if (dto.getConfiguracaoVigilancia().getAtividadePadraoUsuarioExterno() == null) {
                throw new ValidacaoException("Complete a configuração do acesso externo informando a atividade padrão");
            }
            if (Coalesce.asLong(dto.getConfiguracaoVigilancia().getValidadeEmailConfirmacao()) == 0L) {
                throw new ValidacaoException("Complete a configuração do acesso externo informando a validade do e-mail de confirmação");
            }
        }

    }

    @Override
    public Class getResponsePage() {
        return ConfiguracaoVigilanciaPage.class;
    }
}