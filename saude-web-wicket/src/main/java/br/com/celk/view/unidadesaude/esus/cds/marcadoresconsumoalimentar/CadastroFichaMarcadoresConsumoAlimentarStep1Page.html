<wicket:extend>
    <form wicket:id="form" class="dirty">
        <div class="span-10 last">
            <fieldset>
                <h2><label><wicket:message key="dadosPrincipais"/></label></h2>
                <div class="field"><label><wicket:message key="estabelecimento"/></label><div class="group" wicket:id="esusFichaMarcadoresConsumoAlimentar.empresa"/></div>
                <div class="field"><label><wicket:message key="profissional"/></label><div class="group" wicket:id="esusFichaMarcadoresConsumoAlimentar.profissional"/></div>
                <div class="field"><label><wicket:message key="cbo"/></label><select wicket:id="esusFichaMarcadoresConsumoAlimentar.cbo" /></div>
                <div class="field">
                    <label><wicket:message key="codIne"/></label><input type="text" maxlength="10" wicket:id="esusFichaMarcadoresConsumoAlimentar.codigoIne" size="15"/>
                    <label><wicket:message key="dataAtendimento"/></label><div class="group" wicket:id="esusFichaMarcadoresConsumoAlimentar.dataAtendimento"/>
                    <label><wicket:message key="hora"/></label><input type="text" wicket:id="hora" size="6"/>
                </div>
                <div class="field"><label><wicket:message key="cns"/></label><input type="text" class="cns" wicket:id="cns"/></div>
                <div class="field no-line"><label><wicket:message key="paciente" /></label><div class="group" wicket:id="esusFichaMarcadoresConsumoAlimentar.usuarioCadsus" /></div>
                <div class="field">
                    <div class="span-4"><label><wicket:message key="dataNascimento"/></label><div class="group" wicket:id="esusFichaMarcadoresConsumoAlimentar.dataNascimento"/></div>
                    <div class="span-6 last"><label><wicket:message key="sexo"/></label><select wicket:id="sexo"/></div>
                </div>
                <div class="field">
                    <div class="span-4"><label><wicket:message key="localAtendimento"/></label><select wicket:id="esusFichaMarcadoresConsumoAlimentar.localAtendimento"/></div>
                </div>
            </fieldset>
        </div>
        <div id="control-bottom">
            <input type="button" class="arrow-left" wicket:id="btnVoltar" wicket:message="value:voltar"/>
            <input type="button" class="arrow-right" wicket:id="btnAvancar" wicket:message="value:avancar"/>
        </div>
    </form>
</wicket:extend>