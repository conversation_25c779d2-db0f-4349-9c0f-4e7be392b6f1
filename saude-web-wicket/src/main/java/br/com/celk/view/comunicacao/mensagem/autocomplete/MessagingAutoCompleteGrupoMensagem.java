package br.com.celk.view.comunicacao.mensagem.autocomplete;

import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.messaging.autocomplete.MessagingAutoComplete;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.vo.comunicacao.GrupoMensagem;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class MessagingAutoCompleteGrupoMensagem extends MessagingAutoComplete<GrupoMensagem>{

    public MessagingAutoCompleteGrupoMensagem(String wicketId, IModel model) {
        super(wicketId, model);
    }

    public MessagingAutoCompleteGrupoMensagem(String wicketId) {
        super(wicketId);
    }

    @Override
    public IConsultaConfigurator getConsultaConfiguratorInstance() {
        return new CustomizeConsultaConfigurator() {

            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaAdapter(){

                    @Override
                    public Class getClassConsulta() {
                        return GrupoMensagem.class;
                    }
                  
                };
            }

            @Override
            public List<BuilderQueryCustom.QueryParameter> getSearchParam(String searchCriteria) {
                List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
                
                parameters.add(new QueryCustom.QueryCustomParameter(GrupoMensagem.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.ILIKE, searchCriteria));
                
                return parameters;
            }
        };
    }
    
}
