package br.com.celk.view.hospital.faturamento.dialogs;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.OcorrenciaContaPaciente;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgNovaOcorrencia extends Window {

    private PnlNovaOcorrencia pnlNovaOcorrencia;

    public DlgNovaOcorrencia(String id, ContaPaciente contaPaciente) {
        super(id);
        init(contaPaciente);
    }

    private void init(ContaPaciente contaPaciente) {
        setInitialHeight(200);
        setInitialWidth(600);

        setResizable(false);

        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("novaOcorrencia");
            }
        });

        setContent(pnlNovaOcorrencia = new PnlNovaOcorrencia(getContentId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, OcorrenciaContaPaciente ocorrenciaContaPaciente) throws DAOException, ValidacaoException {
                DlgNovaOcorrencia.this.onConfirmar(target, ocorrenciaContaPaciente);
                onFechar(target);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                DlgNovaOcorrencia.this.onFechar(target);
            }
        });

        pnlNovaOcorrencia.setContaPaciente(contaPaciente);
    }

    @Override
    public void show(AjaxRequestTarget target) {
        pnlNovaOcorrencia.limpar(target);
        super.show(target);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, OcorrenciaContaPaciente ocorrenciaContaPaciente) throws DAOException, ValidacaoException;

    public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        close(target);
    }
}
