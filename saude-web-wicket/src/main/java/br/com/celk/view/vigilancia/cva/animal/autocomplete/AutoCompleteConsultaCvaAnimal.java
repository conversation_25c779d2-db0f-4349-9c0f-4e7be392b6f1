package br.com.celk.view.vigilancia.cva.animal.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.configurator.autocomplete.IAutoCompleteSettings;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.vigilancia.cva.animal.autocomplete.settings.AnimalAutoCompleteSettings;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.vigilancia.EspecieAnimal;
import br.com.ksisolucoes.vo.vigilancia.cva.CvaCorAnimal;
import br.com.ksisolucoes.vo.vigilancia.cva.CvaRacaAnimal;
import br.com.ksisolucoes.vo.vigilancia.cva.animal.CvaAnimal;
import br.com.ksisolucoes.vo.vigilancia.cva.proprietarioresponsavel.CvaProprietarioResponsavel;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaCvaAnimal extends AutoCompleteConsulta<CvaAnimal> {

    private CvaProprietarioResponsavel proprietarioResponsavel;
    private Long status;

    public AutoCompleteConsultaCvaAnimal(String id) {
        super(id);
    }

    public AutoCompleteConsultaCvaAnimal(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaCvaAnimal(String id, IModel<CvaAnimal> model) {
        super(id, model);
    }

    public AutoCompleteConsultaCvaAnimal(String id, IModel<CvaAnimal> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public String[] getPropertiesLoad() {
        return VOUtils.mergeProperties(new HQLProperties(CvaAnimal.class).getProperties(),
                new HQLProperties(CvaProprietarioResponsavel.class, CvaAnimal.PROP_CVA_PROPRIETARIO_RESPONSAVEL).getProperties(),
                new HQLProperties(CvaCorAnimal.class, CvaAnimal.PROP_CVA_COR_ANIMAL).getProperties(),
                new HQLProperties(CvaRacaAnimal.class, CvaAnimal.PROP_CVA_RACA_ANIMAL).getProperties(),
                new HQLProperties(EspecieAnimal.class, VOUtils.montarPath(CvaAnimal.PROP_CVA_RACA_ANIMAL, CvaRacaAnimal.PROP_ESPECIE_ANIMAL)).getProperties());
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("animal");
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {

            @Override
            public Class getReferenceClass() {
                return CvaAnimal.class;
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(CvaAnimal.PROP_NOME_ANIMAL, true);
            }

            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaAdapter() {

                    @Override
                    public String[] getProperties() {
                        return VOUtils.mergeProperties(new HQLProperties(CvaAnimal.class).getProperties(),
                                new HQLProperties(CvaProprietarioResponsavel.class, CvaAnimal.PROP_CVA_PROPRIETARIO_RESPONSAVEL).getProperties(),
                                new HQLProperties(CvaCorAnimal.class, CvaAnimal.PROP_CVA_COR_ANIMAL).getProperties(),
                                new HQLProperties(CvaRacaAnimal.class, CvaAnimal.PROP_CVA_RACA_ANIMAL).getProperties(),
                                new HQLProperties(EspecieAnimal.class, VOUtils.montarPath(CvaAnimal.PROP_CVA_RACA_ANIMAL, CvaRacaAnimal.PROP_ESPECIE_ANIMAL)).getProperties());
                    }

                    @Override
                    public void consultaCustomizeViewProperties(Map<String, String> properties) {
                        properties.put(BundleManager.getString("codigo"), CvaAnimal.PROP_CODIGO);
                        properties.put(BundleManager.getString("animal"), CvaAnimal.PROP_NOME_ANIMAL);
                    }

                    @Override
                    public void consultaCustomizeFilterProperties(Map<String, BuilderQueryCustom.QueryParameter> filterProperties) {
                        filterProperties.put(BundleManager.getString("animal"), new QueryCustom.QueryCustomParameter(CvaAnimal.PROP_NOME_ANIMAL, BuilderQueryCustom.QueryParameter.CONSULTA_LIKED));
                        filterProperties.put(BundleManager.getString("codigo"), new QueryCustom.QueryCustomParameter(CvaAnimal.PROP_CODIGO));
                    }

                    @Override
                    public Class getClassConsulta() {
                        return CvaAnimal.class;
                    }

                    @Override
                    public void consultaCustomizeParameters(List<BuilderQueryCustom.QueryParameter> parameters) {
                        parameters.add(new QueryCustom.QueryCustomParameter(CvaAnimal.PROP_CVA_PROPRIETARIO_RESPONSAVEL, AutoCompleteConsultaCvaAnimal.this.proprietarioResponsavel));
                        parameters.add(new QueryCustom.QueryCustomParameter(CvaAnimal.PROP_STATUS, AutoCompleteConsultaCvaAnimal.this.status));
                    }
                };
            }

            @Override
            public List<BuilderQueryCustom.QueryParameter> getSearchParam(String searchCriteria) {
                return Arrays.<BuilderQueryCustom.QueryParameter>asList(new QueryCustom.QueryCustomParameter(CvaAnimal.PROP_NOME_ANIMAL, BuilderQueryCustom.QueryParameter.CONSULTA_LIKED, (searchCriteria != null ? searchCriteria.trim() : null)));
            }

            @Override
            public IAutoCompleteSettings getAutoCompleteSettingsInstance() {
                return new AnimalAutoCompleteSettings();
            }
        };
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        super.limpar(target);
        setProprietarioResponsavel(null);
    }

    public AutoCompleteConsultaCvaAnimal setProprietarioResponsavel(CvaProprietarioResponsavel proprietarioResponsavel) {
        this.proprietarioResponsavel = proprietarioResponsavel;
        return this;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }
}
