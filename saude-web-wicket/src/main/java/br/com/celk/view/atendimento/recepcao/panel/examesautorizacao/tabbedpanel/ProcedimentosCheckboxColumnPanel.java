package br.com.celk.view.atendimento.recepcao.panel.examesautorizacao.tabbedpanel;

import br.com.celk.component.checkbox.CheckBox;
import br.com.ksisolucoes.agendamento.exame.dto.ExameProcedimentoDTO;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class ProcedimentosCheckboxColumnPanel extends Panel {

    private CheckBox checkBox;
    private boolean selectionAction = false;
    private final ExameProcedimentoDTO item;

    public ProcedimentosCheckboxColumnPanel(String id, ExameProcedimentoDTO item) {
        super(id);
        this.item = item;
        init();
    }

    private void init() {
        add(checkBox = new CheckBox("selectionAction", new PropertyModel<Boolean>(this, "selectionAction")));

        checkBox.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                onSelectionAction(target, checkBox.getComponentValue());
            }
        });
    }

    public abstract void onSelectionAction(AjaxRequestTarget target, boolean selectionAction);
}
