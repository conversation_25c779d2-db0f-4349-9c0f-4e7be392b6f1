package br.com.celk.view.materiais.estoque.relatorio;

import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioRelacaoPerdasDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 */
@Private
public class RelatorioRelacaoPerdasPage extends RelatorioPage<RelatorioRelacaoPerdasDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresaOrigem;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresaOrigem = new AutoCompleteConsultaEmpresa("empresas").setValidaUsuarioEmpresa(!isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario(), Permissions.EMPRESA)));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produtos"));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(new PnlDatePeriod("periodo"));

        autoCompleteConsultaEmpresaOrigem.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresaOrigem.setOperadorValor(true);
        autoCompleteConsultaProduto.setMultiplaSelecao(true);
        autoCompleteConsultaProduto.setOperadorValor(true);

    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioRelacaoPerdasDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoPerdasDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioRelacaoPerdas(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relacaoPerdas");
    }
}
