package br.com.celk.view.unidadesaude.receituario;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.MedicamentoPacienteReceituarioDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;


/**
 *
 * <AUTHOR>
 */
@Private

public class CadastroMedicamentoPacientePage extends BasePage {

    private UsuarioCadsus usuarioCadsus;
    private AbstractAjaxButton btnAvancar;
    private DlgConfirmacao dialogConfirmacao;
    private CadastroMedicamentoPanel cadastroMedicamentoPanel;

    public CadastroMedicamentoPacientePage(UsuarioCadsus usuarioCadsus) throws ValidacaoException, DAOException {
        this.usuarioCadsus = usuarioCadsus;
        init();
    }

    private void init() throws ValidacaoException, DAOException {
        final Form form = new Form("form", new CompoundPropertyModel(this));
        form.add(new DisabledInputField("usuarioCadsus.descricaoSocialFormatado"));
        
        form.add(cadastroMedicamentoPanel = new CadastroMedicamentoPanel("panel", this.usuarioCadsus, null, true, false));

        form.add(new VoltarButton("btnVoltar"));
        form.add(btnAvancar = new SubmitButton("btnSalvar", new ISubmitAction() {

            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                boolean validarAtendimeno = true;
                for (MedicamentoPacienteReceituarioDTO dto : cadastroMedicamentoPanel.getLstMedicamentoPaciente()) {
                    if (dto.isReceituario()) {
                        validarAtendimeno = false;
                        break;
                    }
                }
                if (validarAtendimeno) {
                    dialogConfirmacao.show(target);
                } else {
                    salvar(target);
                }
            }
        }));

        addModal(dialogConfirmacao =  new DlgConfirmacao(newModalId(), BundleManager.getString("atendimentoMedicoNaoSeraGeradoPorNaoPosuirItemSelecioando")) {

            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                salvar(target);
            }
        });
        
        btnAvancar.setDefaultFormProcessing(false);
        add(form);

    }

    public void salvar(AjaxRequestTarget art) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(AtendimentoFacade.class).salvarMedicamentoPaciente(cadastroMedicamentoPanel.getLstMedicamentoPaciente(), usuarioCadsus);
        
        MedicamentoPacientePage page = new MedicamentoPacientePage();
        setResponsePage(page);
        getSession().getFeedbackMessages().info(page, BundleManager.getString("registro_salvo_sucesso"));
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return cadastroMedicamentoPanel.getAutoCompleteConsultaCid().getTxtDescricao();
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroReceitaPaciente");
    }

}
