package br.com.celk.view.atendimento.prontuario.cancelamentoatendimento.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlMotivoCancelamentoAtendimento extends Panel {

    private String motivoCancelamento;
    private InputArea txtMotivoCancelamento;
    private Atendimento atendimento;
    private String nomeUsuarioCadsus;
    private InputField txtNome;

    public PnlMotivoCancelamentoAtendimento(String id) {
        super(id);
        init();
    }

    private void init() {
        Form form = new Form("form");
        setOutputMarkupId(true);
        
        form.add(txtNome = new DisabledInputField("nomeUsuarioCadsus", new PropertyModel<String>(this, "nomeUsuarioCadsus")));
        form.add(txtMotivoCancelamento = new InputArea("motivoCancelamento", new PropertyModel(this, "motivoCancelamento")));

        form.add(new AbstractAjaxButton("btnCancelar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                PnlMotivoCancelamentoAtendimento.this.onCancelar(target);
            }
        }.setDefaultFormProcessing(false));

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (validarCadastro(target)) {
                    PnlMotivoCancelamentoAtendimento.this.onConfirmar(target, atendimento, motivoCancelamento);
                }
            }
        });

        add(form);
    }

    private boolean validarCadastro(AjaxRequestTarget target) {
        try {
            if ("".equals(Coalesce.asString(motivoCancelamento))) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_infome_motivo_cancelamento"));
            }
        } catch (ValidacaoException ex) {
            MessageUtil.modalWarn(target, this, ex);
            return false;
        }

        return true;
    }

    public void setObject(AjaxRequestTarget target, Atendimento atendimento) {
        this.atendimento = atendimento;
        txtNome.limpar(target);
        txtMotivoCancelamento.limpar(target);
        this.nomeUsuarioCadsus = atendimento.getUsuarioCadsus().getNomeSocial();
        target.add(txtNome);
        target.focusComponent(txtMotivoCancelamento);
        target.add(txtMotivoCancelamento);
    }

    public abstract void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    public abstract void onConfirmar(AjaxRequestTarget target, Atendimento atendimento, String motivoCancelamento) throws ValidacaoException, DAOException;
}
