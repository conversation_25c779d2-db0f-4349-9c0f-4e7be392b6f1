package br.com.celk.view.controle.parametrogem;

import br.com.celk.component.dropdown.DropDown;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class DropDownPanel extends Panel{

    private DropDown dropDown;
    
    public DropDownPanel(String id, IModel model) {
        super(id, model);
        
        dropDown = new DropDown("valor", model);
        dropDown.addAjaxUpdateValue();
        add(dropDown);
    }

    public DropDown getDropDown() {
        return dropDown;
    }
    
}
