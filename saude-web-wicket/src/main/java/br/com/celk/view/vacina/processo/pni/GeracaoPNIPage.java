package br.com.celk.view.vacina.processo.pni;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.duracaofield.MesAnoField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.asyncprocess.interfaces.IAsyncProcessNotification;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import java.util.List;
import org.apache.wicket.markup.html.form.Form;
import static ch.lambdaj.Lambda.*;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.celk.view.vacina.processo.pni.customcolumn.GeracaoPniColumnPanel;
import br.com.celk.view.vacina.processo.pni.customcolumn.StatusPniProcessoColumnPanel;
import br.com.celk.view.vacina.processo.pni.dialog.DlgGerarArquivoPni;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import br.com.ksisolucoes.vo.vacina.pni.PniProcesso;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class GeracaoPNIPage extends BasePage implements IAsyncProcessNotification {

    private Date competencia;
    private List<Long> situacao;
    private DlgGerarArquivoPni dlgGerarArquivoPni;
    private PageableTable table;
    private ProcurarButton btnProcurar;
    private MesAnoField txtCompetencia;
    private DropDown dropDownSituacao;

    public GeracaoPNIPage() {
        super();
        init();
    }

    private void init() {
        Form form = new Form("form");
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(txtCompetencia = new MesAnoField("competencia"));
        form.add(getDropDownSituacao("situacao"));

        form.add(table = new PageableTable("table", getColumns(), getPagerProviderInstance()));
        table.populate();

        form.add(btnProcurar = new ProcurarButton("btnProcurar", table) {
            @Override
            public Object getParam() {
                return getParameters();
            }
        });

        form.add(new AbstractAjaxButton("btnGerarArquivo") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                dlgGerarArquivoPni.show(target);
            }
        });

        form.add(new ProcurarButton("btnAtualizarRegistros", table) {
            @Override
            public Object getParam() {
                return getParameters();
            }
        });

        form.add(new AbstractAjaxButton("btnConfiguracoes") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new ConfiguracaoPNIPage());
            }
        });

        add(form);

        addModal(dlgGerarArquivoPni = new DlgGerarArquivoPni(newModalId()) {
            @Override
            public void atualizarTabela(AjaxRequestTarget target) {
                table.update(target);
            }
        });

        btnProcurar.procurar();
    }

    private DropDown getDropDownSituacao(String id) {
        if (dropDownSituacao == null) {
            dropDownSituacao = new DropDown(id);
            dropDownSituacao.addChoice(
                    Arrays.asList(
                            PniProcesso.Status.STATUS_ARQUIVO_GERADO.value(),
                            PniProcesso.Status.STATUS_ERRO.value(),
                            PniProcesso.Status.STATUS_GERANDO_ARQUIVO.value(),
                            PniProcesso.Status.STATUS_GERANDO_PNI.value(),
                            PniProcesso.Status.STATUS_PNI_GERADO.value(),
                            PniProcesso.Status.STATUS_SEM_REGISTROS.value()
                    ), bundle("todos"));
            dropDownSituacao.addChoice(Arrays.asList(PniProcesso.Status.STATUS_ARQUIVO_GERADO.value()), bundle("arquivoGerado"));
            dropDownSituacao.addChoice(Arrays.asList(PniProcesso.Status.STATUS_CANCELADO.value()), bundle("cancelado"));
        }

        return dropDownSituacao;
    }

    public List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();

        PniProcesso proxy = on(PniProcesso.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("mes"), proxy.getDescricaoMes()));
        columns.add(createColumn(bundle("ano"), proxy.getAno()));
        columns.add(createColumn(bundle("dataGeracao"), proxy.getDataGeracao()));
        columns.add(createColumn(bundle("situacao"), proxy.getDescricaoStatus()));
        columns.add(getCustomColumnStatus());

        return columns;
    }
    
    public CustomColumn getCustomColumn(){
        return new CustomColumn<PniProcesso>() {

            @Override
            public Component getComponent(String componentId, PniProcesso rowObject) {
                return new GeracaoPniColumnPanel(componentId, rowObject) {

                    @Override
                    public void updateTable(AjaxRequestTarget target) {
                        table.update(target);
                    }
                };
            }
        };
    }

    public CustomColumn getCustomColumnStatus() {
        return new CustomColumn<PniProcesso>() {
            @Override
            public Component getComponent(String componentId, PniProcesso rowObject) {
                return new StatusPniProcessoColumnPanel(componentId, rowObject);
            }
        };
    }

    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return PniProcesso.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(PniProcesso.class).getProperties(),
                        new String[]{
                            VOUtils.montarPath(PniProcesso.PROP_ASYNC_PROCESS, AsyncProcess.PROP_CODIGO),
                            VOUtils.montarPath(PniProcesso.PROP_ASYNC_PROCESS, AsyncProcess.PROP_STATUS)
                        }
                );
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(PniProcesso.PROP_DATA_GERACAO, false);
            }
        };
    }

    private List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        if (competencia != null) {
            Calendar c = Calendar.getInstance();
            c.setTime(competencia);

            Long mes = (long) c.get(Calendar.MONTH) + 1;
            Long ano = (long) c.get(Calendar.YEAR);

            parameters.add(new QueryCustom.QueryCustomParameter(PniProcesso.PROP_MES, mes));
            parameters.add(new QueryCustom.QueryCustomParameter(PniProcesso.PROP_ANO, ano));
        }

        if (situacao == null) {
            parameters.add(new QueryCustom.QueryCustomParameter(PniProcesso.PROP_STATUS, QueryCustom.QueryCustomParameter.DIFERENTE, PniProcesso.Status.STATUS_CANCELADO.value()));
        } else {
            parameters.add(new QueryCustom.QueryCustomParameter(PniProcesso.PROP_STATUS, QueryCustom.QueryCustomParameter.IN, situacao));
        }

        return parameters;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("geracaoPNI");
    }

    @Override
    public void notifyProcess(AjaxRequestTarget target, AsyncProcess event) {
        dropDownSituacao.limpar(target);
        txtCompetencia.limpar(target);

        btnProcurar.procurar();
        table.update(target);
    }

}