package br.com.celk.view.hospital.faturamento.tabbedpanel;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.view.hospital.faturamento.dialogs.DlgLancamentosConfirmadosServicos;
import br.com.celk.view.hospital.faturamento.dialogs.DlgNovoLancamentoServico;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.FechamentoContaDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 */
public class ServicosTab extends TabPanel<FechamentoContaDTO> {

    private Table<ItemContaPaciente> tabela;
    private DlgNovoLancamentoServico dlgNovoLancamento;
    private DlgLancamentosConfirmadosServicos dlgLancamentosConfirmadosServicos;
    private AbstractAjaxButton btnLancamentosConfirmados;
    private FechamentoContaDTO fechamentoContaDTO;
    private List<ItemContaPaciente> servicosList;
    private List<ItemContaPaciente> servicosConfirmadosList;
    private boolean btnConfirmadoOK;

    public ServicosTab(String id, FechamentoContaDTO object) {
        super(id, object);
        this.fechamentoContaDTO = object;
        init();
    }

    private void init() {
        add(tabela = new Table("tabela", getColumns(), getCollectionProvider()));
        tabela.populate();

        add(new AbstractAjaxButton("btnNovoLancamento") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                ItemContaPaciente itemContaPaciente = new ItemContaPaciente();
                itemContaPaciente.setContaPaciente(fechamentoContaDTO.getContaPaciente());
                itemContaPaciente.setOrigemLancamento(ItemContaPaciente.OrigemLancamento.FECHAMENTO_CONTA.value());
                viewDlgNovoLancamento(target, itemContaPaciente);
            }
        });

        add(getBtnLanctosConfirmados());
    }

    private void viewDlgNovoLancamento(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) {
        if (dlgNovoLancamento == null) {
            WindowUtil.addModal(target, this, dlgNovoLancamento = new DlgNovoLancamentoServico(WindowUtil.newModalId(this)) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException {
                    adicionaNovoItem(target, itemContaPaciente);
                }
            });
        }

        dlgNovoLancamento.show(target, itemContaPaciente);
    }

    private void adicionaNovoItem(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) {
        fechamentoContaDTO.getListaItensContaPaciente().add(itemContaPaciente);

        tabela.update(target);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ItemContaPaciente proxy = on(ItemContaPaciente.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("procedimento"), proxy.getProcedimento().getDescricao()));
        columns.add(createSortableColumn(bundle("quantidade"), proxy.getQuantidade()));
        columns.add(createSortableColumn(bundle("preco"), proxy.getPrecoUnitario()));
        columns.add(createSortableColumn(bundle("total"), proxy.getValorTotal()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<ItemContaPaciente>() {
            @Override
            public void customizeColumn(ItemContaPaciente rowObject) {
                addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<ItemContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ItemContaPaciente modelObject) throws ValidacaoException, DAOException {
                        changeStatus(target, modelObject, ItemContaPaciente.Status.CONFIRMADO.value());
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<ItemContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ItemContaPaciente modelObject) throws ValidacaoException, DAOException {
                        changeStatus(target, modelObject, ItemContaPaciente.Status.CANCELADO.value());
                    }
                });
            }
        };
    }

    private void changeStatus(AjaxRequestTarget target, ItemContaPaciente icp, Long status) {
        for (int i = 0; i < fechamentoContaDTO.getListaItensContaPaciente().size(); i++) {
            if (fechamentoContaDTO.getListaItensContaPaciente().get(i) == icp) {
                fechamentoContaDTO.getListaItensContaPaciente().get(i).setStatus(status);
                break;
            }
        }

        tabela.update(target);
        target.add(getBtnLanctosConfirmados());
    }

    private AbstractAjaxButton getBtnLanctosConfirmados() {
        if (btnLancamentosConfirmados == null) {
            btnLancamentosConfirmados = new AbstractAjaxButton("btnLancamentosConfirmados") {
                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    enviaConfirmados(target);
                    dlgLancamentosConfirmadosServicos.show(target);
                }
            };
        }

        btnLancamentosConfirmados.setEnabled(dlgLancamentosConfirmadosServicos != null && !dlgLancamentosConfirmadosServicos.getListaItens().isEmpty() || btnConfirmadoOK);

        return btnLancamentosConfirmados;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                servicosList = new ArrayList();

                for (ItemContaPaciente itemContaPaciente : fechamentoContaDTO.getListaItensContaPaciente()) {
                    if (itemContaPaciente.getTipo().equals(ItemContaPaciente.Tipo.SERVICO.value())) {

                        if (itemContaPaciente.getStatus().equals(ItemContaPaciente.Status.ABERTO.value())) {
                            servicosList.add(itemContaPaciente);
                        }
                        if (itemContaPaciente.getStatus().equals(ItemContaPaciente.Status.CONFIRMADO.value())) {
                            btnConfirmadoOK = true;
                            getBtnLanctosConfirmados();
                        }
                    }
                }

                return servicosList;
            }
        };
    }

    private List<ItemContaPaciente> getListConfirmados() {
        servicosConfirmadosList = new ArrayList();

        for (ItemContaPaciente itemContaPaciente : fechamentoContaDTO.getListaItensContaPaciente()) {
            if (itemContaPaciente.getTipo().equals(ItemContaPaciente.Tipo.SERVICO.value()) && itemContaPaciente.getStatus().equals(ItemContaPaciente.Status.CONFIRMADO.value())) {
                servicosConfirmadosList.add(itemContaPaciente);
            }
        }

        return servicosConfirmadosList;
    }

    private void enviaConfirmados(AjaxRequestTarget target) {
        if (dlgLancamentosConfirmadosServicos == null) {
            WindowUtil.addModal(target, this, dlgLancamentosConfirmadosServicos = new DlgLancamentosConfirmadosServicos(WindowUtil.newModalId(this)) {
                @Override
                public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                    if (dlgLancamentosConfirmadosServicos.getListaItens().isEmpty()) {
                        btnConfirmadoOK = false;
                    }

                    target.add(getBtnLanctosConfirmados());
                }

                @Override
                public void onReverter(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException {
                    changeStatus(target, itemContaPaciente, ItemContaPaciente.Status.ABERTO.value());
                }
            });
        }

        if (CollectionUtils.isNotNullEmpty(getListConfirmados())) {
            dlgLancamentosConfirmadosServicos.setListItem(target, getListConfirmados());
        }

        target.add(getBtnLanctosConfirmados());
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("servicos");
    }
}
