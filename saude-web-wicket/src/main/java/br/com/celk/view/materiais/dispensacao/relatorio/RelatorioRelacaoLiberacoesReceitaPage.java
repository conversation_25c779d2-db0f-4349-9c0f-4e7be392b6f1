package br.com.celk.view.materiais.dispensacao.relatorio;

import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.basico.usuario.autocomplete.AutoCompleteConsultaUsuario;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.controle.usuario.pnl.PnlConsultaUsuario;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioRelacaoLiberacoesReceitaParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento;
import br.com.ksisolucoes.vo.entradas.dispensacao.LiberacaoReceita;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class RelatorioRelacaoLiberacoesReceitaPage extends RelatorioPage<RelatorioRelacaoLiberacoesReceitaParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DropDown<Long> dropDownSituacao;
    private DropDown<String> dropDownTipoPreco;
    private DropDown<String> dropDownFormaApresentacao;
    private DropDown<Long> dropDownTipoRelatorio;
    private DropDown<String> dropDownOrdenacao;
    private DropDown<String> dropDownTipoResumo;
    private DropDown<String> dropDownTipoOrdenacao;
    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private DatePeriod periodo;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("unidade")
                .setOperadorValor(true)
                .setMultiplaSelecao(true));

        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isPermissaoEmpresa);

        form.add(new AutoCompleteConsultaEmpresa("unidadeOrigem")
                .setMultiplaSelecao(true));

        form.add(new AutoCompleteConsultaUsuarioCadsus("usuarioSus")
                .setOperadorValor(true)
                .setMultiplaSelecao(true));

        form.add(autoCompleteConsultaProduto = (AutoCompleteConsultaProduto) new AutoCompleteConsultaProduto("produto")
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        autoCompleteConsultaProduto.setIncluirInativos(true);

        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());
        form.add(new AutoCompleteConsultaProfissional("profissional")
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaUsuario("usuario")
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        form.add(new RequiredPnlDatePeriod("periodo", new PropertyModel<DatePeriod>(this, "periodo")));
        form.add(getDropDownSituacao());
        form.add(getDropDownTipoPreco());
        form.add(getTipoRelatorio());
        form.add(getDropDownFormaApresentacao());
        form.add(getTipoResumo());
        form.add(getOrdenacao());
        ativaCampos(false, null);

    }

    public DropDown<String> getTipoResumo() {
        if (dropDownTipoResumo == null) {
            dropDownTipoResumo = new DropDown<String>("tipoResumo");

            dropDownTipoResumo.addChoice(LiberacaoReceita.PROP_USUARIO, BundleManager.getString("usuario"));
            dropDownTipoResumo.addChoice(LiberacaoReceita.PROP_DATA_USUARIO, BundleManager.getString("dataLiberacao"));
            dropDownTipoResumo.addChoice(LiberacaoReceita.PROP_EMPRESA, BundleManager.getString("empresaOrigem"));
            dropDownTipoResumo.addChoice(Produto.PROP_SUB_GRUPO, BundleManager.getString("grupoProduto"));
            dropDownTipoResumo.addChoice(LiberacaoReceita.PROP_PRODUTO, BundleManager.getString("produto"));
            dropDownTipoResumo.addChoice(DispensacaoMedicamento.PROP_PROFISSIONAL, BundleManager.getString("profissional"));

        }
        return dropDownTipoResumo;
    }

    public DropDown<String> getOrdenacao() {
        if (dropDownOrdenacao == null) {
            dropDownOrdenacao = new DropDown<String>("ordenacao");
            this.dropDownOrdenacao.addChoice(DispensacaoMedicamento.PROP_CODIGO, BundleManager.getString("nrDispensacao"));
            this.dropDownOrdenacao.addChoice(DispensacaoMedicamento.PROP_DATA_DISPENSACAO, BundleManager.getString("dataDispensacao"));
            this.dropDownOrdenacao.addChoice(LiberacaoReceita.PROP_PRODUTO, BundleManager.getString("produto"));
        }
        return dropDownOrdenacao;
    }

    public DropDown<Long> getTipoRelatorio() {
        if (dropDownTipoRelatorio == null) {
            dropDownTipoRelatorio = new DropDown<Long>("tipoRelatorio");
            dropDownTipoRelatorio.addChoice(new Long(ReportProperties.DETALHADO), BundleManager.getString("detalhado"));
            dropDownTipoRelatorio.addChoice(new Long(ReportProperties.RESUMIDO), BundleManager.getString("resumido"));

            dropDownTipoRelatorio.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    if (param != null) {
                        if (new Long(ReportProperties.RESUMIDO).equals(param.getTipoRelatorio())) {
                            dropDownTipoResumo.setEnabled(true);
                            dropDownOrdenacao.setEnabled(false);
                        } else {
                            dropDownTipoResumo.setEnabled(false);
                            dropDownTipoResumo.setComponentValue(LiberacaoReceita.PROP_USUARIO);
                            dropDownOrdenacao.setEnabled(true);
                            dropDownOrdenacao.setComponentValue(DispensacaoMedicamento.PROP_CODIGO);
                        }
                        target.add(dropDownTipoResumo);
                        target.add(dropDownOrdenacao);
                    }
                }
            });
        }
        return dropDownTipoRelatorio;
    }

    public DropDown<String> getDropDownFormaApresentacao() {
        if (dropDownFormaApresentacao == null) {
            dropDownFormaApresentacao = new DropDown<String>("formaApresentacao");
            formaApresentacaoPadrao();

        }
        return dropDownFormaApresentacao;
    }

    public DropDown<String> getDropDownTipoPreco() {
        if (dropDownTipoPreco == null) {
            dropDownTipoPreco = new DropDown<String>("tipoPreco");
            dropDownTipoPreco.addChoice(EstoqueEmpresa.PROP_PRECO_MEDIO, BundleManager.getString("precoMedio"));
            dropDownTipoPreco.addChoice(EstoqueEmpresa.PROP_ULTIMO_PRECO, BundleManager.getString("ultimoPreco"));

        }
        return dropDownTipoPreco;
    }

    public DropDown<Long> getDropDownSituacao() {
        if (dropDownSituacao == null) {
            dropDownSituacao = new DropDown<Long>("situacao");
            dropDownSituacao.addChoice(null, BundleManager.getString("ambos"));
            dropDownSituacao.addChoice(LiberacaoReceita.STATUS_ABERTO, BundleManager.getString("naoDispensada"));
            dropDownSituacao.addChoice(LiberacaoReceita.STATUS_LIBERADO, BundleManager.getString("dispensada"));

            dropDownSituacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    if (param != null) {
                        if (LiberacaoReceita.STATUS_LIBERADO.equals(param.getSituacao())) {
                            ativaCampos(true, target);
                            dropDownFormaApresentacao.removeAllChoices();
                            formaApresentacaoDispensada();
                        } else {
                            dropDownTipoRelatorio.setComponentValue(new Long(ReportProperties.DETALHADO));
                            ativaCampos(false, target);
                            dropDownFormaApresentacao.removeAllChoices();
                            formaApresentacaoPadrao();
                        }
                        target.add(dropDownFormaApresentacao);
                    }
                }
            });
        }
        return dropDownSituacao;
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioRelacaoLiberacoesReceitaParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoLiberacoesReceitaParam param) throws ReportException {
        Date dataInicial = periodo.getDataInicial();
        Date dataFinal = periodo.getDataFinal();
        param.setDataFinal(dataFinal);
        param.setDataInicial(dataInicial);
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioRelacaoLiberacoesReceita(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("liberacoesReceitas");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

    private DropDown<SubGrupo> getDropDownSubGrupo() {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo");
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
        }
        return this.dropDownSubGrupo;
    }

    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProdutoSubGrupo");
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                        param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();
            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));
            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }
        return this.dropDownGrupoProduto;
    }

    public void formaApresentacaoPadrao() {
        dropDownFormaApresentacao.addChoice(LiberacaoReceita.PROP_USUARIO, BundleManager.getString("usuario"));
        dropDownFormaApresentacao.addChoice(LiberacaoReceita.PROP_DATA_USUARIO, BundleManager.getString("dataLiberacao"));
    }

    public void formaApresentacaoDispensada() {
        dropDownFormaApresentacao.addChoice(LiberacaoReceita.PROP_USUARIO, BundleManager.getString("usuario"));
        dropDownFormaApresentacao.addChoice(LiberacaoReceita.PROP_DATA_USUARIO, BundleManager.getString("dataLiberacao"));
        dropDownFormaApresentacao.addChoice(LiberacaoReceita.PROP_EMPRESA, BundleManager.getString("empresaOrigem"));
        dropDownFormaApresentacao.addChoice(VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), BundleManager.getString("grupoProduto"));
        dropDownFormaApresentacao.addChoice(LiberacaoReceita.PROP_PRODUTO, BundleManager.getString("produto"));
        dropDownFormaApresentacao.addChoice(DispensacaoMedicamento.PROP_PROFISSIONAL, BundleManager.getString("profissional"));
        dropDownFormaApresentacao.addChoice(LiberacaoReceita.PROP_USUARIO_CADSUS, BundleManager.getString("usuarioCadsus"));
    }

    public void ativaCampos(boolean enable, AjaxRequestTarget target) {
        dropDownTipoPreco.setEnabled(enable);
        if (!enable) {
            dropDownTipoResumo.setEnabled(enable);
        }
        dropDownTipoRelatorio.setEnabled(enable);
        dropDownOrdenacao.setEnabled(enable);

        if (target != null) {
            target.add(dropDownTipoPreco);
            target.add(dropDownTipoResumo);
            target.add(dropDownTipoRelatorio);
            target.add(dropDownOrdenacao);
        }
    }
}
