package br.com.celk.view.agenda.agendamento.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgSelecionarEmpresaAgenda extends Window{
    
    private PnlSelecionarEmpresaAgenda pnlSelecionarEmpresaAgenda;
    private boolean regulacao;

    public DlgSelecionarEmpresaAgenda(String id){
        super(id);
        init();
    }

    public DlgSelecionarEmpresaAgenda(String id, boolean regulacao){
        super(id);
        this.regulacao = regulacao;
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("selecionarUnidadeAgenda");
            }
        });
                
        setInitialWidth(600);
        setInitialHeight(100);
        setResizable(true);
        
        setContent(pnlSelecionarEmpresaAgenda = new PnlSelecionarEmpresaAgenda(getContentId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, SolicitacaoAgendamento sa, Empresa unidadeAgenda, List<AgendaGradeAtendimentoDTO> horarios) throws ValidacaoException, DAOException {
                close(target);
                DlgSelecionarEmpresaAgenda.this.onConfirmar(target, sa, unidadeAgenda, horarios);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target, SolicitacaoAgendamento sa, Empresa unidadeAgenda, List<AgendaGradeAtendimentoDTO> horarios) throws ValidacaoException, DAOException;

    public void show(AjaxRequestTarget target, SolicitacaoAgendamento sa, List<AgendaGradeAtendimentoDTO> list){
        show(target);
        pnlSelecionarEmpresaAgenda.setDTO(target, sa, list, regulacao);
    }    
}