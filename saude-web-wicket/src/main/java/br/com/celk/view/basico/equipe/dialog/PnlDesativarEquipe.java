package br.com.celk.view.basico.equipe.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.DataUtil;
import br.com.celk.util.validacao.ValidacaoProcesso;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.basico.MotivoDesativacao;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlDesativarEquipe extends Panel {

    private CompoundPropertyModel<Equipe> model;
    private DateChooser dataDesativacao;
    private DropDown<MotivoDesativacao> dropDownMotivoDesativacao;
    private DropDown dropDownTipoDesativacao;

    public PnlDesativarEquipe(String id) {
        super(id);
        init();
    }

    private void init() {
        Form form = new Form("form", model = new CompoundPropertyModel<Equipe>(new Equipe()));

        form.add(dataDesativacao = new DateChooser(Equipe.PROP_DATA_DESATIVACAO));
        form.add(getDropDownTipoDesativacao());
        form.add(getDropDownMotivoDesativacao());

        form.add(new AbstractAjaxButton("btnDesativar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                PnlDesativarEquipe.this.onFechar(target);
                desativarEquipe(target);
            }
        });
        form.add(new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                PnlDesativarEquipe.this.onFechar(target);
            }
        });

        add(form);
    }

    private DropDown getDropDownTipoDesativacao() {
        dropDownTipoDesativacao = new DropDown(Equipe.PROP_TIPO_DESATIVACAO);

        dropDownTipoDesativacao.addChoice(null, "");
        dropDownTipoDesativacao.addChoice(Equipe.DESATIVACAO_TEMPORARIA, BundleManager.getString("temporaria"));
        dropDownTipoDesativacao.addChoice(Equipe.DESATIVACAO_DEFINITIVA, BundleManager.getString("definitiva"));

        return dropDownTipoDesativacao;
    }

    private DropDown<MotivoDesativacao> getDropDownMotivoDesativacao() {
        dropDownMotivoDesativacao = new DropDown(Equipe.PROP_MOTIVO_DESATIVACAO);

        dropDownMotivoDesativacao.addChoice(null, "");
        List<MotivoDesativacao> motivoDesativacaoList = LoadManager.getInstance(MotivoDesativacao.class)
                .start().getList();

        for (MotivoDesativacao motivoDesativacao : motivoDesativacaoList) {
            dropDownMotivoDesativacao.addChoice(motivoDesativacao, motivoDesativacao.getDescricao());
        }

        return dropDownMotivoDesativacao;
    }

    private void desativarEquipe(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        Equipe equipe = model.getObject();
        validarDesativar(equipe);

        equipe.setAtivo(RepositoryComponentDefault.NAO);
        equipe.setDataInativacao(DataUtil.getDataAtual());

        BOFactoryWicket.save(equipe);
        PnlDesativarEquipe.this.updateTable(target);
    }

    private void validarDesativar(Equipe equipe) throws ValidacaoException {
        ValidacaoProcesso validacaoProcesso = new ValidacaoProcesso();
        if (equipe.getDataDesativacao() == null) {
            validacaoProcesso.add(BundleManager.getString("campoXObrigatorio", BundleManager.getString("data")));
        }
        if (equipe.getTipoDesativacao() == null) {
            validacaoProcesso.add(BundleManager.getString("campoXObrigatorio", BundleManager.getString("tipo")));
        }
        if (equipe.getMotivoDesativacao() == null) {
            validacaoProcesso.add(BundleManager.getString("campoXObrigatorio", BundleManager.getString("motivo")));
        }

        if (!validacaoProcesso.getMensagemList().isEmpty()) {
            throw new ValidacaoException(validacaoProcesso);
        }
    }

    private void limpar(AjaxRequestTarget target) {
        dataDesativacao.limpar(target);
        dropDownMotivoDesativacao.limpar(target);
        dropDownTipoDesativacao.limpar(target);
    }

    public void setEquipe(AjaxRequestTarget target, Equipe equipe) {
        limpar(target);
        model.setObject(equipe);
        target.add(this);
    }

    public abstract void onFechar(AjaxRequestTarget target);
    public abstract void updateTable(AjaxRequestTarget target);

}
