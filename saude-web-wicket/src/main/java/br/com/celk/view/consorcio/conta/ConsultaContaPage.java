package br.com.celk.view.consorcio.conta;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.consorcio.conta.customize.CustomizeConsultaConta;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.Conta;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaContaPage extends ConsultaPage<Conta, List<BuilderQueryCustom.QueryParameter>> {

    private UpperField txtReferencia;
    
    private String referencia;
    private String descricao;
    private Empresa consorciado;
    private Long status;
    
    @Override
    public void initForm(Form form) {
        form.setModel(new CompoundPropertyModel(this));
        form.add(txtReferencia = new UpperField("referencia"));
        form.add(new UpperField("descricao"));
        form.add(new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));
        form.add(DropDownUtil.getIEnumDropDown("status", Conta.StatusConta.values(), true));
        setExibeExpandir(true);
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(Conta.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("referencia"), VOUtils.montarPath(Conta.PROP_REFERENCIA)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(Conta.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("consorciado"), VOUtils.montarPath(Conta.PROP_CONSORCIADO, Empresa.PROP_DESCRICAO), VOUtils.montarPath(Conta.PROP_CONSORCIADO, Empresa.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("situacao"), VOUtils.montarPath(Conta.PROP_STATUS), VOUtils.montarPath(Conta.PROP_DESCRICAO_STATUS)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("saldoAtual"), VOUtils.montarPath(Conta.PROP_SALDO_ATUAL)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("valorReservado"), VOUtils.montarPath(Conta.PROP_VALOR_RESERVADO)));
        
        return columns;
    }
    
    private CustomColumn getCustomColumn(){
        return new CustomColumn<Conta>() {

            @Override
            public Component getComponent(String componentId, final Conta rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroContaPage(rowObject));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(ConsorcioFacade.class).removerConta(rowObject);
                        getPageableTable().update(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    }

                    @Override
                    public boolean isConsultarVisible() {
                        return false;
                    }
                    
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaConta());
    }

    @Override
    public List<QueryParameter> getParameters() {
        List<QueryParameter> parameters = new ArrayList<QueryParameter>();
        
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Conta.PROP_REFERENCIA), referencia));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Conta.PROP_DESCRICAO), QueryParameter.ILIKE, descricao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Conta.PROP_CONSORCIADO), consorciado));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Conta.PROP_STATUS), status));
        
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroContaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaConta");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtReferencia;
    }

}
