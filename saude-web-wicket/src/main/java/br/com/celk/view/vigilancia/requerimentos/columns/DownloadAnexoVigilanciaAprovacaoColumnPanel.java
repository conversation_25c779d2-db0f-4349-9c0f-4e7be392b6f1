package br.com.celk.view.vigilancia.requerimentos.columns;

import br.com.celk.component.behavior.AjaxDownload;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaAnexoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.io.FileUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.util.resource.FileResourceStream;
import org.apache.wicket.util.resource.IResourceStream;

import java.io.File;
import java.io.IOException;

/**
 *
 * <AUTHOR>
 */
public class DownloadAnexoVigilanciaAprovacaoColumnPanel extends Panel {

    private AbstractAjaxLink linkAnexo;
    private AjaxDownload ajaxDownload;
    private RequerimentoVigilanciaAnexoDTO rowObject;
    private Label label;

    public DownloadAnexoVigilanciaAprovacaoColumnPanel(String id, RequerimentoVigilanciaAnexoDTO rowObject) {
        super(id);
        this.rowObject = rowObject;
        init();
    }

    private void init() {
        setOutputMarkupId(true);
        add(linkAnexo = new AbstractAjaxLink("linkAnexo") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                downloadArquivo(target);
            }

        }).setEnabled(rowObject.getRequerimentoVigilanciaAnexo() != null && rowObject.getRequerimentoVigilanciaAnexo().getCodigo() != null);

        add(ajaxDownload = new AjaxDownload());
        label = (Label) new Label("nomeArquivo", rowObject.getNomeArquivoOriginal()).setOutputMarkupId(true);
        if(rowObject.getRequerimentoVigilanciaAnexo() == null || rowObject.getRequerimentoVigilanciaAnexo().getCodigo() == null
                || RepositoryComponentDefault.SIM_LONG.equals(rowObject.getRequerimentoVigilanciaAnexo().getVisualizado())){
            label.add(new AttributeModifier("style", "color: #939393;"));
        }
        linkAnexo.add(label);
    }
    
    public void downloadArquivo(AjaxRequestTarget target) throws ValidacaoException, DAOException{
        try{
            File f = File.createTempFile("anexo", "vigilancia");
            FileUtils.buscarArquivoFtp(rowObject.getRequerimentoVigilanciaAnexo().getGerenciadorArquivo().getCaminho(), f.getAbsolutePath());
            
            IResourceStream resourceStream = new FileResourceStream(new org.apache.wicket.util.file.File(f));
            ajaxDownload.initiate(target, rowObject.getNomeArquivoOriginal(), resourceStream);

            rowObject.getRequerimentoVigilanciaAnexo().setVisualizado(RepositoryComponentDefault.SIM_LONG);
            rowObject.setRequerimentoVigilanciaAnexo(BOFactoryWicket.save(rowObject.getRequerimentoVigilanciaAnexo()));

            label.add(new AttributeModifier("style", "color: #939393;"));
            target.add(label);
            target.add(linkAnexo);
        }   catch (IOException ex) {
            throw new DAOException(ex.getMessage());
        }
    }

}