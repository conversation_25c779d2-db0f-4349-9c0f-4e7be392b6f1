package br.com.celk.view.agenda.exportacaoagenda;

import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.unidadesaude.esus.relatorios.dto.ExportacaAgendaDTOParam;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.TipoAtendimentoAgenda;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import org.apache.wicket.markup.html.form.Form;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class ExportacaoAgendaPage extends RelatorioPage<ExportacaAgendaDTOParam> {
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DropDown<Long> dropDownTipoConsultaAtendimento;
    private Empresa estabelecimento;
    private TipoAtendimentoAgenda tipoAtendimento;
    private PnlDatePeriod periodo;

    @Override
    public void init(Form form) {

        ExportacaAgendaDTOParam proxy = on(ExportacaAgendaDTOParam.class);

        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEstabelecimento())));
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(isActionPermitted(ApplicationSession.get().getSession().getUsuario(), Permissions.EMPRESA));
        autoCompleteConsultaEmpresa.setRequired(true);
        form.add(getDropDownTipoAtendimentoConsulta("tipoAtendimento"));
        dropDownTipoConsultaAtendimento.setRequired(true);
        form.add(new PnlDatePeriod("periodo").setRequired(true));

    }

    private DropDown getDropDownTipoAtendimentoConsulta(String id) {
        if (dropDownTipoConsultaAtendimento == null) {
            dropDownTipoConsultaAtendimento = new DropDown<Long>(id);

            dropDownTipoConsultaAtendimento.addChoice(SolicitacaoAgendamento.TIPO_CONSULTA_NORMAL, bundle("consulta"));
            dropDownTipoConsultaAtendimento.addChoice(SolicitacaoAgendamento.TIPO_CONSULTA_RETORNO, bundle("retorno"));
        }

        return dropDownTipoConsultaAtendimento;
    }

    @Override
    public Class<ExportacaAgendaDTOParam> getDTOParamClass() {
        return ExportacaAgendaDTOParam.class;
    }

    @Override
    public DataReport getDataReport(ExportacaAgendaDTOParam param) throws ReportException, ValidacaoException {
        return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioExportacaoAgenda(param);
    }

    @Override
    public String getTituloPrograma() {
        return Bundle.getStringApplication("exportacaoAgendaConsulta");
    }
}
