package br.com.celk.view.atendimento.recepcao.panel.confirmacaochegada;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.agendamento.exame.dto.ConfirmacaoChegadaAihDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;


public abstract class DlgConfirmarLeitoQuarto extends Window {

    private PnlConfirmarLeitoQuarto pnlConfirmarLeitoQuarto;
    private String message;
    private ConfirmacaoChegadaAihDTO dto;

    public DlgConfirmarLeitoQuarto(String id, String message, ConfirmacaoChegadaAihDTO dto) {
        super(id);
        this.message = message;
        this.dto = dto;
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>() {
            @Override
            protected String load() {
                return BundleManager.getString("confirmarLeito");
            }
        });

        setInitialWidth(530);
        setInitialHeight(200);
        setResizable(true);

        setContent(pnlConfirmarLeitoQuarto = new PnlConfirmarLeitoQuarto(getContentId(), message, dto) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, ConfirmacaoChegadaAihDTO dto) throws ValidacaoException, DAOException {
                DlgConfirmarLeitoQuarto.this.onConfirmar(target, dto);
                close(target);
            }

            @Override
            public void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }

    public void show(AjaxRequestTarget target, ConfirmacaoChegadaAihDTO dto) {
        show(target);
        pnlConfirmarLeitoQuarto.limpar(target);
        pnlConfirmarLeitoQuarto.setConfirmacaoChegadaDTO(target, dto);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, ConfirmacaoChegadaAihDTO dto) throws ValidacaoException, DAOException;
}
