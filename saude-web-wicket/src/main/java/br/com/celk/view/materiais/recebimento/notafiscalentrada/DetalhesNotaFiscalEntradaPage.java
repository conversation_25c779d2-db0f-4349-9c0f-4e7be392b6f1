package br.com.celk.view.materiais.recebimento.notafiscalentrada;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.doublefield.DisabledDoubleField;
import br.com.celk.component.inputarea.DisabledInputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.column.panel.DetalhesActionColumnPanel;
import br.com.celk.component.table.selection.MultiSelectionTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.materiais.recebimento.notafiscalentrada.dialog.DlgDetalhesLote;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.recebimento.interfaces.facade.RegistroNotaFiscalFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.recebimento.interfaces.dto.RelatorioRegistroItemNotaFiscalParam;
import br.com.ksisolucoes.report.entrada.recebimento.interfaces.facade.RegistroNotaFiscalReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;
import br.com.ksisolucoes.vo.entradas.recebimento.RecebimentoGrupoEstoque;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroNotaFiscal;
import br.com.ksisolucoes.vo.financeiro.Serie;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.link.BookmarkablePageLink;
import org.apache.wicket.markup.repeater.RepeatingView;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.*;


/**
 *
 * <AUTHOR>
 */
@Private

public class DetalhesNotaFiscalEntradaPage extends BasePage {

    private List<RegistroItemNotaFiscal> itens = new ArrayList<RegistroItemNotaFiscal>();
    
    private MultiSelectionTable<RegistroItemNotaFiscal> table;
    private DlgDetalhesLote dlgDetalhesLotes;
    private DlgImpressaoObject<RegistroNotaFiscal> dlgImpressao;
    private RegistroNotaFiscal registroNotaFiscal;
    
    private RepeatingView controls;
    
    private boolean confirmar;
    private AbstractAjaxButton btnEditar;
    private AbstractAjaxButton btnConfirmar;
    private Long entradaNotaFiscalParcial;

    public DetalhesNotaFiscalEntradaPage(RegistroNotaFiscal notaFiscal) {
        this.registroNotaFiscal = notaFiscal;
        initItens();
        init();
    }
    
    public DetalhesNotaFiscalEntradaPage(RegistroNotaFiscal notaFiscal, boolean confirmar) {
        this.registroNotaFiscal = notaFiscal;
        this.confirmar = confirmar;
        initItens();
        init();
    }

    private void init() {
        try {
            entradaNotaFiscalParcial = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("entradaNotaFiscalParcial");
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage());
        }

        Form form = new Form("form", new CompoundPropertyModel<RegistroNotaFiscal>(registroNotaFiscal));
        form.add(new DisabledInputField(VOUtils.montarPath(RegistroNotaFiscal.PROP_NUMERO_NOTA_FISCAL)));
        form.add(new DisabledInputField(VOUtils.montarPath(RegistroNotaFiscal.PROP_STATUS_DESCRICAO_FORMATADO)));
        form.add(new DisabledInputField(VOUtils.montarPath(RegistroNotaFiscal.PROP_FORNECEDOR, Pessoa.PROP_DESCRICAO_FORMATADO)));
        form.add(new DisabledInputField(VOUtils.montarPath(RegistroNotaFiscal.PROP_DATA_EMISSAO)));
        form.add(new DisabledInputField(VOUtils.montarPath(RegistroNotaFiscal.PROP_SERIE, Serie.PROP_DESCRICAO)));
        form.add(new DisabledInputField(VOUtils.montarPath(RegistroNotaFiscal.PROP_DATA_PORTARIA)));
        form.add(new DisabledInputField(VOUtils.montarPath(RegistroNotaFiscal.PROP_TIPO_DOCUMENTO, TipoDocumento.PROP_DESCRICAO)));
        form.add(new DisabledInputField(VOUtils.montarPath(RegistroNotaFiscal.PROP_USUARIO, Usuario.PROP_NOME)));
        form.add(new DisabledInputField(VOUtils.montarPath(RegistroNotaFiscal.PROP_DATA_LANCAMENTO)));
        form.add(new DisabledInputArea(VOUtils.montarPath(RegistroNotaFiscal.PROP_OBSERVACAO)));
        form.add(new DisabledDoubleField(VOUtils.montarPath(RegistroNotaFiscal.PROP_VALOR_TOTAL)));

        form.add(controls = new RepeatingView("controls"));

        controls.add(new BookmarkablePageLink(controls.newChildId(), ConsultaNotaFiscalEntradaPage.class) {
            {
                add(new AttributeModifier("class", "arrow-left"));
                add(new AttributeModifier("value", BundleManager.getString("voltar")));
            }
        });
        if (confirmar) {
            if (RepositoryComponentDefault.SIM_LONG.equals(entradaNotaFiscalParcial)) {
                form.add(table = new MultiSelectionTable<RegistroItemNotaFiscal>("tableItens", getColumns(), getCollectionProvider()));
            } else {
                form.add(table = new MultiSelectionTable("tableItens", getColumns(), getCollectionProvider()) {
                    @Override
                    public List customizeColumns(List columns) {
                        columns = super.customizeColumns(columns);
                        columns.remove(0);
                        return columns;
                    }
                });
            }

            controls.add(btnConfirmar = new AbstractAjaxButton(controls.newChildId()) {
                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    try {
                        confirmar(target);
                    } catch (InstantiationException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                    } catch (IllegalAccessException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                    } catch (ReportException e) {
                         br.com.ksisolucoes.util.log.Loggable.log.error(e);
                    }
                }
            });
            
            controls.add(btnEditar = new AbstractAjaxButton(controls.newChildId()) {
                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    setResponsePage(new CadastroNotaFiscalStep1Page(registroNotaFiscal));
                }
            });
            btnEditar.add(new AttributeModifier("class", "doc-edit"));
            btnEditar.add(new AttributeModifier("value", BundleManager.getString("editar")));
            btnEditar.add(new AttributeModifier("style", "margin-left: 5px;"));
            btnConfirmar.add(new AttributeModifier("class", "checkmark"));
            btnConfirmar.add(new AttributeModifier("value", BundleManager.getString("confirmar")));
            btnConfirmar.add(new AttributeModifier("style", "margin-left: 5px;"));

            btnEditar.setEnabled(RegistroNotaFiscal.ORIGEM_NORMAL.equals(registroNotaFiscal.getOrigem()));
        } else {
            form.add(table = new MultiSelectionTable("tableItens", getColumns(), getCollectionProvider()) {
                @Override
                public List customizeColumns(List columns) {
                    columns = super.customizeColumns(columns);
                    columns.remove(0);
                    return columns;
                }
            });
        }

        table.populate();

        add(form);
    }

    public RepeatingView getControls() {
        return controls;
    }

    public void setControls(RepeatingView controls) {
        this.controls = controls;
    }
    
    private List<ISortableColumn<RegistroItemNotaFiscal>> getColumns(){
        List<ISortableColumn<RegistroItemNotaFiscal>> columns = new ArrayList<ISortableColumn<RegistroItemNotaFiscal>>();
        
        ColumnFactory columnFactory = new ColumnFactory(RegistroItemNotaFiscal.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("produto"), VOUtils.montarPath(RegistroItemNotaFiscal.PROP_PRODUTO, Produto.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("unidade"), VOUtils.montarPath(RegistroItemNotaFiscal.PROP_UNIDADE)));
        columns.add(columnFactory.createColumn(BundleManager.getString("quantidade"), VOUtils.montarPath(RegistroItemNotaFiscal.PROP_QUANTIDADE)));
        columns.add(new DoubleColumn(BundleManager.getString("valorUnitario"), VOUtils.montarPath(RegistroItemNotaFiscal.PROP_PRECO_UNITARIO)).setCasasDecimais(4));
        columns.add(columnFactory.createColumn(BundleManager.getString("valorTotal"), VOUtils.montarPath(RegistroItemNotaFiscal.PROP_VALOR_ITEM)));
        columns.add(columnFactory.createColumn(BundleManager.getString("situacao"), VOUtils.montarPath(RegistroItemNotaFiscal.PROP_DESCRICAO_STATUS)));
        
        return columns;
    }

    private CustomColumn getCustomColumn(){
        return new CustomColumn<RegistroItemNotaFiscal>() {

            @Override
            public Component getComponent(String componentId, final RegistroItemNotaFiscal rowObject) {
                return new DetalhesActionColumnPanel(componentId) {

                    @Override
                    public void onDetalhar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        if(dlgDetalhesLotes == null){
                            dlgDetalhesLotes = new DlgDetalhesLote(newModalId());
                            addModal(target, dlgDetalhesLotes);
                        }
                        dlgDetalhesLotes.setRegistroItemNotaFiscal(rowObject);
                        dlgDetalhesLotes.show(target);
                    }
                    
                    @Override
                    public boolean isEnabled() {
                        return rowObject.getRecebimentoGruposEstoque() != null;
                    }
                };
            }
        };
    }
    
    private ICollectionProvider getCollectionProvider(){
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return itens;
            }
        };
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("detalhesEntradaMateriaisMedicamentos");
    }
    
    private void initItens() {
            itens = LoadManager.getInstance(RegistroItemNotaFiscal.class)
                    .addProperties(new HQLProperties(RegistroItemNotaFiscal.class).getProperties())
                    .addProperty(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_ITEM))
                    .addProperty(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_REGISTRO_NOTA_FISCAL, RegistroNotaFiscal.PROP_NUMERO_NOTA_FISCAL))
                    .addProperty(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_REGISTRO_NOTA_FISCAL, RegistroNotaFiscal.PROP_SERIE, Serie.PROP_SERIE))
                    .addProperty(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_REGISTRO_NOTA_FISCAL, RegistroNotaFiscal.PROP_SERIE, Serie.PROP_DESCRICAO))
                    .addProperty(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_REGISTRO_NOTA_FISCAL, RegistroNotaFiscal.PROP_EMPRESA, Empresa.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_REGISTRO_NOTA_FISCAL, RegistroNotaFiscal.PROP_EMPRESA, Empresa.PROP_DESCRICAO))
                    .addProperty(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_REGISTRO_NOTA_FISCAL, RegistroNotaFiscal.PROP_FORNECEDOR, Pessoa.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_REGISTRO_NOTA_FISCAL, RegistroNotaFiscal.PROP_FORNECEDOR, Pessoa.PROP_DESCRICAO))
                    .addProperty(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_QUANTIDADE))
                    .addProperty(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_VALOR_ITEM))
                    .addProperty(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_STATUS))
                    .addProperty(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_PRECO_UNITARIO))
                    .addProperty(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_PRODUTO, Produto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_PRODUTO, Produto.PROP_DESCRICAO))
                    .addProperty(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_UNIDADE))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_REGISTRO_NOTA_FISCAL), this.registroNotaFiscal))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_STATUS), BuilderQueryCustom.QueryParameter.DIFERENTE, confirmar ? RegistroItemNotaFiscal.STATUS_RECEBIDO: null))
                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_PRODUTO, Produto.PROP_DESCRICAO)))
                    .start().getList();
        
        for (RegistroItemNotaFiscal registroItemNotaFiscal : itens) {
               List<RecebimentoGrupoEstoque> lstRecebimentoGrupoEstoques = LoadManager.getInstance(RecebimentoGrupoEstoque.class)
                       .addProperties(new HQLProperties(RecebimentoGrupoEstoque.class).getProperties())
                       .addProperties(new HQLProperties(LocalizacaoEstrutura.class, RecebimentoGrupoEstoque.PROP_LOCALIZACAO_ESTRUTURA).getProperties())
                       .addProperties(new HQLProperties(Empresa.class, RecebimentoGrupoEstoque.PROP_EMPRESA_SETOR).getProperties())
                        .addProperty(VOUtils.montarPath(RecebimentoGrupoEstoque.PROP_DATA_VENCIMENTO))
                        .addProperty(VOUtils.montarPath(RecebimentoGrupoEstoque.PROP_QUANTIDADE_LOTE))
                        .addProperty(VOUtils.montarPath(RecebimentoGrupoEstoque.PROP_GRUPO_ESTOQUE))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RecebimentoGrupoEstoque.PROP_REGISTRO_ITEM_NOTA_FISCAL), registroItemNotaFiscal))
                        .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(RecebimentoGrupoEstoque.PROP_GRUPO_ESTOQUE)))
                        .start().getList();
                if (CollectionUtils.isNotNullEmpty(lstRecebimentoGrupoEstoques)) {
                    registroItemNotaFiscal.setRecebimentoGruposEstoque(lstRecebimentoGrupoEstoques);
                }
        }
    }

    private void confirmar(AjaxRequestTarget target) throws DAOException, ValidacaoException, InstantiationException, IllegalAccessException, ReportException {
            List<RegistroItemNotaFiscal> selectedObjects = table.getSelectedObjects();
        if (RepositoryComponentDefault.SIM_LONG.equals(entradaNotaFiscalParcial)) {
            selectedObjects = table.getSelectedObjects();
        } else {
            selectedObjects = itens;
        }

        Set<RegistroItemNotaFiscal> setRegistroItemNotaFiscal = new HashSet<RegistroItemNotaFiscal>(selectedObjects);

        BOFactoryWicket.getBO(RegistroNotaFiscalFacade.class).aprovarRegistroItemNotaFiscal(setRegistroItemNotaFiscal, registroNotaFiscal.getTipoDocumento().getFlagDigitaOrdemCompra());
        initDlgImpressao(target);


    }

    private void initDlgImpressao(AjaxRequestTarget target) {
        if (dlgImpressao == null) {
            addModal(target, dlgImpressao = new DlgImpressaoObject<RegistroNotaFiscal>(newModalId(), BundleManager.getString("notaFiscalXConfirmada", registroNotaFiscal.getNumeroNotaFiscal())) {
                @Override
                public DataReport getDataReport(RegistroNotaFiscal object) throws ReportException {
                    RelatorioRegistroItemNotaFiscalParam param = new RelatorioRegistroItemNotaFiscalParam();
                    param.setTipoData(RegistroNotaFiscal.PROP_DATA_EMISSAO);
                    param.setFormaApresentacao(RegistroNotaFiscal.PROP_FORNECEDOR);
                    param.setTipoRelatorio(new Long(ReportProperties.DETALHADO));
                    param.setOrdenacao(RegistroNotaFiscal.PROP_NUMERO_NOTA_FISCAL);
                    param.setCodigoFornecedor(registroNotaFiscal.getFornecedor().getCodigo());
                    param.setNumeroNota(registroNotaFiscal.getNumeroNotaFiscal());
                    return BOFactoryWicket.getBO(RegistroNotaFiscalReportFacade.class).getRelatorioRegistroItemNotaFiscalQWE(param);
                }

                @Override
                public void onFechar(AjaxRequestTarget target, RegistroNotaFiscal object) throws ValidacaoException, DAOException {
                    Page page = null;
                    try {
                        page = (Page) getResponsePage().newInstance();
                    } catch (InstantiationException e) {
                         br.com.ksisolucoes.util.log.Loggable.log.error(e);
                    } catch (IllegalAccessException e) {
                         br.com.ksisolucoes.util.log.Loggable.log.error(e);
                    }
                    setResponsePage(page);
                }
            });
        }
        dlgImpressao.show(target, registroNotaFiscal);
    }

    public Class getResponsePage() {
        return ConsultaNotaFiscalEntradaPage.class;
    }


}
