package br.com.celk.view.vigilancia.rotinas;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.javascript.JScript;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.roteiro.RegistroInspecaoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgAcoesGeracaoRotinas extends Window {

    private PnlAcoesGeracaoRotinas pnlAcoesGeracaoRotinas;

    public DlgAcoesGeracaoRotinas(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("geracaoAutos");
            }
        });

        setInitialWidth(900);
        setInitialHeight(350);
        setResizable(false);

        setContent(pnlAcoesGeracaoRotinas = new PnlAcoesGeracaoRotinas(getContentId()) {

            @Override
            public void onVoltar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                voltar(target);
            }

            @Override
            public void onAutoIntimacao(AjaxRequestTarget target, RegistroInspecaoDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesGeracaoRotinas.this.onAutoIntimacao(target, dto);
            }

            @Override
            public void onAutoInfracao(AjaxRequestTarget target, RegistroInspecaoDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesGeracaoRotinas.this.onAutoInfracao(target, dto);
            }

            @Override
            public void onoAutoInfracaoIntimacao(AjaxRequestTarget target, RegistroInspecaoDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesGeracaoRotinas.this.onoAutoInfracaoIntimacao(target, dto);
            }

        });

        setCloseButtonCallback(new CloseButtonCallback() {

            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                voltar(target);
                return true;
            }
        });
    }

    private void voltar(AjaxRequestTarget target) {
        close(target);
        DlgAcoesGeracaoRotinas.this.onVoltar(target);
    }

    public abstract void onAutoIntimacao(AjaxRequestTarget target, RegistroInspecaoDTO dto) throws ValidacaoException, DAOException;

    public abstract void onAutoInfracao(AjaxRequestTarget target, RegistroInspecaoDTO dto) throws ValidacaoException, DAOException;

    public abstract void onoAutoInfracaoIntimacao(AjaxRequestTarget target, RegistroInspecaoDTO dto) throws ValidacaoException, DAOException;

    public abstract void onVoltar(AjaxRequestTarget target);


    public void show(AjaxRequestTarget target, RegistroInspecaoDTO dto) {
        show(target);
        pnlAcoesGeracaoRotinas.setObject(target, dto);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnLoadHeaderItem.forScript(JScript.initMasks()));
        response.render(OnLoadHeaderItem.forScript(JScript.initTextAreaLimit()));
        response.render(OnLoadHeaderItem.forScript(JScript.removeAutoCompleteDrop()));
        response.render(OnLoadHeaderItem.forScript(JScript.removeEnterSubmitFromForm()));
    }

}