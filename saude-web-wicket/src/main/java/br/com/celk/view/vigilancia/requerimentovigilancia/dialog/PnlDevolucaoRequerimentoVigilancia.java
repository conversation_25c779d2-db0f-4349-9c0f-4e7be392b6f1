package br.com.celk.view.vigilancia.requerimentovigilancia.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.CadastroDevolucaoRequerimentoVigilanciaDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlDevolucaoRequerimentoVigilancia extends Panel{
    
    private Form form;
    private CompoundPropertyModel<CadastroDevolucaoRequerimentoVigilanciaDTO> model;
    private RequerimentoVigilancia rv;
    private Profissional profissional;
    private DateChooser dchData;
    private InputArea txaMotivo;
    private DropDown dropDownSituacao;
    
    public PnlDevolucaoRequerimentoVigilancia(String id){
        super(id);
        init();
    }

    private void init() {
        form = new Form<>("form", model = new CompoundPropertyModel(new CadastroDevolucaoRequerimentoVigilanciaDTO()));
        CadastroDevolucaoRequerimentoVigilanciaDTO proxy = on(CadastroDevolucaoRequerimentoVigilanciaDTO.class);
        setOutputMarkupId(true);
        
        form.add(dchData = new DateChooser(path(proxy.getData())));
        form.add(txaMotivo = new InputArea(path(proxy.getMotivo())));
        
        form.add(new AbstractAjaxButton("btnConfirmar") {
            
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if(validarCadastro(target)){
                    model.getObject().setRequerimentoVigilancia(rv);

                    onConfirmar(target, model.getObject());
                }
            }
        });
        
        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));
        
        add(form);
    }
    
    public boolean validarCadastro(AjaxRequestTarget target) {
        try {            
            if(dchData.getComponentValue() == null){
                throw new ValidacaoException(bundle("informeData"));
            } else if (Data.adjustRangeHour(dchData.getComponentValue()).getDataInicial().after(Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial())) {
                throw new ValidacaoException(bundle("msgDataNaoPodeSerMaiorDataAtual"));
            }
//              else if(txaMotivo.getComponentValue() == null){
//                throw new ValidacaoException(bundle("msgInformeMotivo"));
//            }
        } catch (ValidacaoException e) {              
            MessageUtil.modalWarn(target, this, e);
            return false;
        }
        
        return true;
    }
   
    public abstract void onConfirmar(AjaxRequestTarget target, CadastroDevolucaoRequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;
    
    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public void setObject(AjaxRequestTarget target, RequerimentoVigilancia rv){
        this.rv = rv;
        dchData.limpar(target);
        txaMotivo.limpar(target);
        dchData.setComponentValue(DataUtil.getDataAtual());
        
        target.add(dchData);
        target.focusComponent(dchData);
    }
}