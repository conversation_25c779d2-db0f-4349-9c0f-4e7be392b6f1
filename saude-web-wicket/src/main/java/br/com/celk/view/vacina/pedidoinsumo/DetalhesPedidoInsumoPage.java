package br.com.celk.view.vacina.pedidoinsumo;

import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputarea.DisabledInputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.column.panel.DetalhesActionColumnPanel;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.vacina.itempedidoinsumo.DlgDetalhesItemPedidoInsumo;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.insumo.ItemPedidoInsumo;
import br.com.ksisolucoes.vo.vacina.PedidoVacinaInsumo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class DetalhesPedidoInsumoPage extends BasePage {

    private Integer totalItens = 0;
    private List<ItemPedidoInsumo> itens = new ArrayList<ItemPedidoInsumo>();
    
    private Table<ItemPedidoInsumo> table;
    private DlgDetalhesItemPedidoInsumo dlgDetalhesItemPedidoInsumo;
    
    private PedidoVacinaInsumo pedidoVacinaInsumo;
    
    public DetalhesPedidoInsumoPage(PedidoVacinaInsumo pedidoVacinaInsumo) {
        this.pedidoVacinaInsumo = pedidoVacinaInsumo;
        initItens();
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel<PedidoVacinaInsumo>(pedidoVacinaInsumo));
        form.add(new DisabledInputField(VOUtils.montarPath(PedidoVacinaInsumo.PROP_NUMERO_PEDIDO_FORMATADO)));
        form.add(new DisabledInputField(VOUtils.montarPath(PedidoVacinaInsumo.PROP_DATA_CADASTRO)));
        form.add(new DisabledInputField(VOUtils.montarPath(PedidoVacinaInsumo.PROP_DESCRICAO_STATUS)));
        form.add(new DisabledInputField(VOUtils.montarPath(PedidoVacinaInsumo.PROP_DESCRICAO_TIPO_PEDIDO)));
        form.add(new DisabledInputField(VOUtils.montarPath(PedidoVacinaInsumo.PROP_EMPRESA, Empresa.PROP_DESCRICAO_FORMATADO)));
        form.add(new DisabledInputArea(VOUtils.montarPath(PedidoVacinaInsumo.PROP_OBSERVACAO)));
        form.add(new DisabledInputField(VOUtils.montarPath(PedidoVacinaInsumo.PROP_USUARIO, Usuario.PROP_DESCRICAO_FORMATADO)));
        form.add(new DisabledInputField(VOUtils.montarPath(PedidoVacinaInsumo.PROP_DATA_USUARIO)));
        form.add(new DisabledInputField(VOUtils.montarPath(PedidoVacinaInsumo.PROP_USUARIO_ENCAMINHAMENTO, Usuario.PROP_DESCRICAO_FORMATADO)));
        form.add(new DisabledInputField(VOUtils.montarPath(PedidoVacinaInsumo.PROP_DATA_ENCAMINHAMENTO)));
        form.add(new DisabledInputField(VOUtils.montarPath(PedidoVacinaInsumo.PROP_USUARIO_CANCELAMENTO, Usuario.PROP_DESCRICAO_FORMATADO)));
        form.add(new DisabledInputField(VOUtils.montarPath(PedidoVacinaInsumo.PROP_DATA_CANCELAMENTO)));
        
        form.add(table = new Table<ItemPedidoInsumo>("tableItens", getColumns(), getCollectionProvider()));
        
        form.add(new DisabledInputField("totalItens", new PropertyModel(this, "totalItens")));
        
        table.populate();
        
        form.add(new AbstractAjaxLink("linkVoltar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                setResponsePage(ConsultaPedidoInsumoPage.class);
            }
        });
        
        add(form);
        
        addModal(dlgDetalhesItemPedidoInsumo = new DlgDetalhesItemPedidoInsumo(newModalId()));
    }
    
    private List<ISortableColumn<ItemPedidoInsumo>> getColumns(){
        List<ISortableColumn<ItemPedidoInsumo>> columns = new ArrayList<ISortableColumn<ItemPedidoInsumo>>();
        
        ColumnFactory columnFactory = new ColumnFactory(ItemPedidoInsumo.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("tipoVacina"), VOUtils.montarPath(ItemPedidoInsumo.PROP_PRODUTO, Produto.PROP_DESCRICAO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("pedida"), VOUtils.montarPath(ItemPedidoInsumo.PROP_QUANTIDADE_PEDIDA)));
        columns.add(columnFactory.createColumn(BundleManager.getString("recebida"), VOUtils.montarPath(ItemPedidoInsumo.PROP_QUANTIDADE_RECEBIDA)));
        columns.add(columnFactory.createColumn(BundleManager.getString("cancelada"), VOUtils.montarPath(ItemPedidoInsumo.PROP_QUANTIDADE_CANCELADA)));
        columns.add(columnFactory.createColumn(BundleManager.getString("situacao"), VOUtils.montarPath(ItemPedidoInsumo.PROP_DESCRICAO_STATUS)));
        
        return columns;
    }
    
    private CustomColumn getCustomColumn(){
        return new CustomColumn<ItemPedidoInsumo>() {

            @Override
            public Component getComponent(String componentId, final ItemPedidoInsumo rowObject) {
                return new DetalhesActionColumnPanel(componentId) {

                    @Override
                    public void onDetalhar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        dlgDetalhesItemPedidoInsumo.setModelObject(rowObject);
                        dlgDetalhesItemPedidoInsumo.show(target);
                    }

                };
            }
        };
    }
    
    private ICollectionProvider getCollectionProvider(){
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return itens;
            }
        };
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("detalhesPedidoInsumo");
    }
    
    private void initItens() {
            itens = LoadManager.getInstance(ItemPedidoInsumo.class)
                    .addProperties(new HQLProperties(ItemPedidoInsumo.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ItemPedidoInsumo.PROP_PEDIDO_VACINA_INSUMO), this.pedidoVacinaInsumo))
                    .start().getList();
            
            totalItens = itens.size();
    }

}
