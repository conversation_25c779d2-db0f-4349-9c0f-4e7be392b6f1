package br.com.celk.view.unidadesaude.exames.resultadoexame;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.unidadesaude.exames.autocomplete.AutoCompleteConsultaExameProcedimento;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExame;
import br.com.ksisolucoes.vo.prontuario.exame.ResultadoCidExame;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.jrimum.utilix.Objects;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

public class ResultadoExameComCidPage extends ConsultaPage<AtendimentoExame, InformarCidDTO> {

    private CompoundPropertyModel<InformarCidDTO> model;
    private DropDown<Long> dropDownSituacao;
    private ExameProcedimento exameProcedimento;
    private UsuarioCadsus paciente;
    private DatePeriod periodo;
    private List<Long> situacao;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(model = new CompoundPropertyModel(new InformarCidDTO()));
        form.add(new AutoCompleteConsultaUsuarioCadsus("paciente"));
        form.add(new AutoCompleteConsultaExameProcedimento("exameProcedimento").setIncluirInativos(true));
        form.add(new PnlDatePeriod("periodo"));
        form.add(getDropDownSituacao());
        getLinkNovo().setVisible(false);
    }

    public DropDown<Long> getDropDownSituacao() {
        if (Objects.isNull(dropDownSituacao)) {
            dropDownSituacao = new DropDown<>("situacao");
            dropDownSituacao.addChoice(null, BundleManager.getString("todos"));
            dropDownSituacao.addChoice(ResultadoCidExame.Status.PENDENTE.value(), BundleManager.getString("pendente"));
            dropDownSituacao.addChoice(ResultadoCidExame.Status.CONCLUIDO.value(), BundleManager.getString("concluido"));
        }
        return dropDownSituacao;
    }

    @Override
    public List getColumns(List columns) {
        InformarCidDTO proxy = on(InformarCidDTO.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("exame"), proxy.getResultadoCidExame().getCodigo(), proxy.getResultadoCidExame().getCodigo()));
        columns.add(createSortableColumn(bundle("paciente"), proxy.getPaciente().getNome(), proxy.getNomeSocial()));
        columns.add(createSortableColumn(bundle("executante"), proxy.getNomeExecutanteExame(), proxy.getNomeExecutanteExame()));
        columns.add(createSortableColumn(bundle("dataExame"), proxy.getDataExame(), proxy.getDataExame()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getResultadoCidExame().getStatus(), proxy.getStatusExame()));
        columns.add(createSortableColumn(bundle("tipoExame"), proxy.getTipoExame().getDescricao(), proxy.getTipoExame().getDescricao()));
        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<InformarCidDTO>() {

            @Override
            public void customizeColumn(final InformarCidDTO rowObject) {
                ActionType action = (rowObject.getResultadoCidExame().getStatus().equals(ResultadoCidExame.Status.PENDENTE.value())) ? ActionType.LAUDAR : ActionType.CONSULTAR;
                addAction(action, rowObject, new IModelAction<InformarCidDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, InformarCidDTO modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new InformarCidResultadoExamePage(modelObject));
                    }
                });
            }
        };

    }

    @Override
    public IPagerProvider<AtendimentoExame, InformarCidDTO> getPagerProviderInstance() {
        return new QueryPagerProvider<AtendimentoExame, InformarCidDTO>() {

            @Override
            public DataPagingResult executeQueryPager(DataPaging<InformarCidDTO> dataPaging) throws DAOException, ValidacaoException {
                dataPaging.getParam().setSortProperty(getSort().getProperty());
                dataPaging.getParam().setSortCommand(getSort().isAscending() ? "asc" : "desc");
                boolean isVisualizaSomenteUnidade = !isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EDITAR, ResultadoExameComCidPage.class);
                return BOFactoryWicket.getBO(AtendimentoFacade.class).consultarLaudosPendentesCID(dataPaging, isVisualizaSomenteUnidade);
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam("(coalesce(exame.dataCadastro, atendimentoExame.dataExame))", true);
            }
        };

    }

    @Override
    public Class getCadastroPage() {
        return ResultadoExameComCidPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("resultadoDoExameComCid");
    }

    @Override
    public InformarCidDTO getParameters() {
        return model.getObject();
    }

}
