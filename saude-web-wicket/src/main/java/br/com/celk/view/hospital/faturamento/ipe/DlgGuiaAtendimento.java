package br.com.celk.view.hospital.faturamento.ipe;

import br.com.celk.component.window.Window;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgGuiaAtendimento extends Window {

    private String titulo;
    private ContaPaciente contaPaciente;
    private PnlGuiaAtenidmento pnlGuiaAtenidmento;

    public DlgGuiaAtendimento(String id, String titulo) {
        super(id);
        this.titulo = titulo;
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        setInitialWidth(270);
        setInitialHeight(70);

        setResizable(false);

        setTitle(titulo);

        setContent(pnlGuiaAtenidmento = new PnlGuiaAtenidmento(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
                DlgGuiaAtendimento.this.onFechar(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, ContaPaciente contaPaciente) throws ValidacaoException, DAOException {
                close(target);
                DlgGuiaAtendimento.this.onConfirmar(target, contaPaciente);
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target, ContaPaciente contaPaciente) throws ValidacaoException, DAOException;

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void show(AjaxRequestTarget target, ContaPaciente contaPaciente) {
        show(target);
        pnlGuiaAtenidmento.limpar(target);
        pnlGuiaAtenidmento.setContaPaciente(target, contaPaciente);
    }
}