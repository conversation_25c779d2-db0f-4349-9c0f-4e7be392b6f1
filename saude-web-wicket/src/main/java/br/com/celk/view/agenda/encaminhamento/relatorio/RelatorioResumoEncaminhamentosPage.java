package br.com.celk.view.agenda.encaminhamento.relatorio;

import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.atendimento.encaminhamento.autocomplete.AutoCompleteConsultaTipoEncaminhamento;
import br.com.celk.view.atendimento.tabelacbo.autocomplete.AutoCompleteConsultaTabelaCboMulti;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QuerySorter;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.AtendimentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioResumoEncaminhamentosDTOParam;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.TipoEncaminhamento;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;


/**
 *
 * <AUTHOR>
 */
@Private

public class RelatorioResumoEncaminhamentosPage extends RelatorioPage<RelatorioResumoEncaminhamentosDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DropDown<String> dropDownFormaApresentacao;
    private DropDown<String> dropDownTipoResumo;
    private DropDown<Long> dropDownOrdenacao;
    private DropDown<String> dropDownTipoOrdenacao;
    private DropDown<Long> dropDownApenasCaps;
    private DropDown<String> dropDownRetorno;
    private AutoCompleteConsultaTabelaCboMulti autoCompleteConsultaTabelaCboMulti;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("unidadeOrigem")
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaUsuarioCadsus("paciente")
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaProfissional("profissional")
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaTipoEncaminhamento("tipoEncaminhamento").setIncluirInativos(true)
                .setMultiplaSelecao(true));
        form.add(autoCompleteConsultaTabelaCboMulti = new AutoCompleteConsultaTabelaCboMulti("tabelasCbo"));
        form.add(new RequiredPnlChoicePeriod("periodo"));
        form.add(getDropDownFormaApresentacao());
        form.add(getDropDownTipoResumo());
        form.add(getDropDownOrdenacao());
        form.add(getDropDownTipoOrdenacao());
        form.add(getDropDownApenasCaps());
        form.add(getDropDownRetorno());
    }

    public DropDown getDropDownTipoOrdenacao(){
        if (this.dropDownTipoOrdenacao == null) {
            this.dropDownTipoOrdenacao = new DropDown<String>("tipoOrdenacao");
            this.dropDownTipoOrdenacao.addChoice(QuerySorter.DECRESCENTE, BundleManager.getString("decrescente"));
            this.dropDownTipoOrdenacao.addChoice(QuerySorter.CRESCENTE, BundleManager.getString("crescente"));
        }
        return this.dropDownTipoOrdenacao;    
    }
    
    public DropDown getDropDownOrdenacao(){
        if (this.dropDownOrdenacao == null) {
            this.dropDownOrdenacao = new DropDown<Long>("ordenacao");
            this.dropDownOrdenacao.addChoice(RepositoryComponentDefault.TOTAL_ENCAMINHAMENTO, BundleManager.getString("totalEncaminhamento"));
            this.dropDownOrdenacao.addChoice(RepositoryComponentDefault.DIAS_MEDIA_ESPERA, BundleManager.getString("diasMediaEspera"));
            this.dropDownOrdenacao.addChoice(RepositoryComponentDefault.PORCENTAGEM_FALTA, BundleManager.getString("porcentagemFalta"));
        }
        return this.dropDownOrdenacao;    
    }
    
    public DropDown getDropDownTipoResumo(){
        if (this.dropDownTipoResumo == null) {
            this.dropDownTipoResumo = new DropDown<String>("tipoResumo");
            this.dropDownTipoResumo.addChoice(Empresa.REF, BundleManager.getString("empresa"));
            this.dropDownTipoResumo.addChoice(Profissional.REF, BundleManager.getString("profissional"));
            this.dropDownTipoResumo.addChoice(TipoEncaminhamento.REF, BundleManager.getString("tipoEncaminhamento"));
            this.dropDownTipoResumo.addChoice(UsuarioCadsus.REF, BundleManager.getString("paciente"));
            this.dropDownTipoResumo.addChoice(TabelaCbo.REF, BundleManager.getString("cbo"));
        }
        return this.dropDownTipoResumo;    
    }
    
    public DropDown getDropDownFormaApresentacao(){
        if (this.dropDownFormaApresentacao == null) {
            this.dropDownFormaApresentacao = new DropDown<String>("formaApresentacao");
            this.dropDownFormaApresentacao.addChoice(Empresa.REF, BundleManager.getString("empresa"));
            this.dropDownFormaApresentacao.addChoice(Profissional.REF, BundleManager.getString("profissional"));
            this.dropDownFormaApresentacao.addChoice(TipoEncaminhamento.REF, BundleManager.getString("tipoEncaminhamento"));
            this.dropDownFormaApresentacao.addChoice(UsuarioCadsus.REF, BundleManager.getString("paciente"));
            this.dropDownFormaApresentacao.addChoice(TabelaCbo.REF, BundleManager.getString("cbo"));
        }
        return this.dropDownFormaApresentacao;    
    }

    public DropDown getDropDownRetorno() {
        if (dropDownRetorno == null) {
            dropDownRetorno = new DropDown<String>("retorno");
            dropDownRetorno.addChoice(null, BundleManager.getString("ambos"));
            dropDownRetorno.addChoice(RepositoryComponentDefault.NAO, BundleManager.getString("nao"));
            dropDownRetorno.addChoice(RepositoryComponentDefault.SIM, BundleManager.getString("sim"));
        }
        return dropDownRetorno;
    }

    public DropDown getDropDownApenasCaps() {
        if (this.dropDownApenasCaps == null) {
            this.dropDownApenasCaps = new DropDown<Long>("apenasCaps");
            this.dropDownApenasCaps.addChoice(RepositoryComponentDefault.NAO_LONG, BundleManager.getString("nao"));
            this.dropDownApenasCaps.addChoice(RepositoryComponentDefault.SIM_LONG, BundleManager.getString("sim"));
        }
        return this.dropDownApenasCaps;
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioResumoEncaminhamentosDTOParam.class;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

    @Override
    public DataReport getDataReport(RelatorioResumoEncaminhamentosDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(AtendimentoReportFacade.class).relatorioResumoEncaminhamentos(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("resumoEncaminhamentos");
    }
}
