package br.com.celk.view.controle.menuweb.panel;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dialog.DlgConfirmacaoOk;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.menu.MenuCache;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.controlemenu.dto.EdicaoMenuDinamicoDTO;
import br.com.celk.controlemenu.dto.MenuDinamicoDTO;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.annotation.PainelControle;
import br.com.celk.view.controle.menuweb.CadastroMenuWebDinamico;
import br.com.celk.view.controle.menuweb.panel.dialog.DlgCadastroMenuWeb;
import br.com.celk.view.controle.menuweb.panel.dialog.DlgCadastroModuloWeb;
import br.com.celk.view.controle.menuweb.panel.dialog.DlgCadastroSubmenuWeb;
import br.com.celk.view.controle.menuweb.panel.template.MenuDinamicoCadastroPanel;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.web.MenuWeb;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import static ch.lambdaj.Lambda.on;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
@PainelControle
public class EditarMenuDinamicoPanel extends MenuDinamicoCadastroPanel {
    
    private Form<EdicaoMenuDinamicoDTO> formEdicao;
    private MenuDinamicoDTO menuDinamicoDTO;
    private MenuCache menuCache;
    private AbstractAjaxLink btnCadadastroSubmenu;
    
    private InputField txtDescricao;
    private DropDown<MenuWeb> dropDownModulo;
    private DropDown<MenuWeb> dropDownMenu;
    private DropDown<MenuWeb> dropDownSubmenu;
    private DlgCadastroModuloWeb dlgCadastroModuloWeb;
    private DlgCadastroMenuWeb dlgCadastroMenuWeb;
    private DlgConfirmacaoOk dlgConfirmacaoOk;
    private DlgCadastroSubmenuWeb dlgCadastroSubmenuWeb;
    
    public EditarMenuDinamicoPanel(String id) {
        super(id);
        init();
    }

    private void init() {
        EdicaoMenuDinamicoDTO proxy = on(EdicaoMenuDinamicoDTO.class);
        setOutputMarkupId(true);
        
        getForm().add(txtDescricao = (InputField) new InputField(path(proxy.getDescricao())).setEnabled(true));
        
        getForm().add(dropDownModulo = (DropDown<MenuWeb>) new DropDown(path(proxy.getModulo())).setEnabled(true));
        getForm().add(new AbstractAjaxLink("btnCadadastroModulo") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                getDlgCadastrarModuloWeb(target).showDlg(target);
            }
        }).setEnabled(true);
        
        getForm().add(dropDownMenu = (DropDown<MenuWeb>) new DropDown(path(proxy.getMenu())).setEnabled(true));
        getForm().add(new AbstractAjaxLink("btnCadadastroMenu") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                getDlgCadastrarMenuWeb(target).showDlg(target, dropDownModulo.getComponentValue());
            }
        }).setEnabled(true);
        
        getForm().add(dropDownSubmenu = (DropDown<MenuWeb>) new DropDown(path(proxy.getSubmenu())).setEnabled(true));
        getForm().add(btnCadadastroSubmenu = new AbstractAjaxLink("btnCadadastroSubmenu") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                getDlgCadastrarSubmenuWeb(target).showDlg(target, dropDownModulo.getComponentValue(), dropDownMenu.getComponentValue());
            }
        }).setEnabled(true);
        
        getForm().add(new AbstractAjaxButton("btnSalvar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar();
            }
        });
        
        add(getForm());
    }
    
    private DlgCadastroModuloWeb getDlgCadastrarModuloWeb(AjaxRequestTarget target) {
        if (this.dlgCadastroModuloWeb == null) {
            WindowUtil.addModal(target, this, dlgCadastroModuloWeb = new DlgCadastroModuloWeb(WindowUtil.newModalId(this)) {

                @Override
                public void onSalvar(AjaxRequestTarget target, MenuWeb modulo) throws ValidacaoException, DAOException {
                    salvarNovoMenu(target, modulo, MenuDinamicoDTO.TipoMenu.MODULO.value());
                }
            });
            
        }
        return dlgCadastroModuloWeb;
    }
    
    private DlgCadastroMenuWeb getDlgCadastrarMenuWeb(AjaxRequestTarget target) {
        if (this.dlgCadastroMenuWeb == null) {
            WindowUtil.addModal(target, this, dlgCadastroMenuWeb = new DlgCadastroMenuWeb(WindowUtil.newModalId(this)) {

                @Override
                public void onSalvar(AjaxRequestTarget target, MenuWeb modulo) throws ValidacaoException, DAOException {
                    salvarNovoMenu(target, modulo, MenuDinamicoDTO.TipoMenu.MENU.value());
                }
            });
            
        }
        return dlgCadastroMenuWeb;
    }
    
    private DlgCadastroSubmenuWeb getDlgCadastrarSubmenuWeb(AjaxRequestTarget target) {
        if (this.dlgCadastroSubmenuWeb == null) {
            WindowUtil.addModal(target, this, dlgCadastroSubmenuWeb = new DlgCadastroSubmenuWeb(WindowUtil.newModalId(this)) {

                @Override
                public void onSalvar(AjaxRequestTarget target, MenuWeb modulo) throws ValidacaoException, DAOException {
                    salvarNovoMenu(target, modulo, MenuDinamicoDTO.TipoMenu.SUB_MENU.value());
                }
            });
            
        }
        return dlgCadastroSubmenuWeb;
    }
    
    private Form<EdicaoMenuDinamicoDTO> getForm(){
        if(this.formEdicao == null){
            formEdicao = new Form("formEdicao", new CompoundPropertyModel(new EdicaoMenuDinamicoDTO()));
        }
        return this.formEdicao;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDescricao;
    }
    
    private void limparDropDown(AjaxRequestTarget target, DropDown dropDown){
        if(dropDown != null){
            dropDown.removeAllChoices();
            if(target != null){
                dropDown.limpar(target);
            }
        }
    }
    
    private DropDown getDropDownModulos(AjaxRequestTarget target) {

        for (MenuWeb menuModulo : menuCache.getModulos()) {
            dropDownModulo.addChoice(menuModulo, menuModulo.getDescricao());
        }
        
        if(target != null){
            target.add(dropDownModulo);
        }
        return dropDownModulo;
    }
    
    private DropDown getDropDownMenus(MenuWeb modulo) {
        List<MenuWeb> menuList = menuCache.getChilds(modulo);
        
        if (CollectionUtils.isNotNullEmpty(menuList)) {
            for (MenuWeb menu : menuList) {
                if(dropDownMenu.getComponentValue() == null){
                    dropDownMenu.setComponentValue(menu);                    
                }
                dropDownMenu.addChoice(menu, menu.getDescricao());
            }
        }
        
        return dropDownMenu;
    }
    
    private void salvarNovoMenu(AjaxRequestTarget target, MenuWeb menuWeb, Long tipoMenuSalvar) throws DAOException, ValidacaoException{
        if(menuWeb.getDescricaoBundle() == null){
            menuWeb.setDescricaoBundle("");
        }
        if(MenuDinamicoDTO.TipoMenu.MENU.value().equals(tipoMenuSalvar)){
            menuWeb.setMenuWebPai(dropDownModulo.getComponentValue());
        } else if(MenuDinamicoDTO.TipoMenu.SUB_MENU.value().equals(tipoMenuSalvar)){
            menuWeb.setMenuWebPai(dropDownMenu.getComponentValue());
        }
            
        MenuWeb menuWebSave = BOFactoryWicket.save(menuWeb);
        
        menuCache.recarregarMenus();
        
        if(MenuDinamicoDTO.TipoMenu.MODULO.value().equals(tipoMenuSalvar)){
            limparDropDown(target, dropDownModulo);
            getDropDownModulos(target);
            dropDownModulo.setComponentValue(menuWebSave);
            target.add(dropDownModulo);
        }
        
        initConfirmarNovoMenu(target);
    }
    
    private void initConfirmarNovoMenu(AjaxRequestTarget target){
        if (dlgConfirmacaoOk == null) {
            WindowUtil.addModal(target, this, dlgConfirmacaoOk = new DlgConfirmacaoOk(WindowUtil.newModalId(this), bundle("registro_salvo_sucesso"), "img-info") {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                }
            });
        }
        
        dlgConfirmacaoOk.show(target);
    }
    
    public void setDTO(AjaxRequestTarget target, MenuDinamicoDTO menuDinamicoDTO){
        this.menuDinamicoDTO = menuDinamicoDTO;
        MenuWeb menuWeb = carregarMenuWeb(this.menuDinamicoDTO.getMenuWeb());
        
        formEdicao = null;
        txtDescricao.limpar(target);
        dropDownModulo.limpar(target);
        dropDownMenu.limpar(target);
        dropDownSubmenu.limpar(target);
        
        iniciarMenuWeb(menuWeb);
        
        recarregarObjetos(target);
        target.focusComponent(txtDescricao);
    }
    
    private void recarregarObjetos(AjaxRequestTarget target){
        if(this.menuDinamicoDTO != null){
            menuCache = MenuCache.get();
            menuCache.recarregarMenus();
            
            EdicaoMenuDinamicoDTO edicaoDTO = getForm().getModel().getObject();

            limparDropDown(target, dropDownModulo);
            limparDropDown(target, dropDownMenu);
            limparDropDown(target, dropDownSubmenu);
            
            btnCadadastroSubmenu.setEnabled(true);
            
            txtDescricao.setComponentValue(edicaoDTO.getDescricao());
            
            if(MenuDinamicoDTO.TipoMenu.MENU.value().equals(menuDinamicoDTO.getTipoMenu())) {
                dropDownModulo.setComponentValue(edicaoDTO.getModulo());
                dropDownMenu.setComponentValue(edicaoDTO.getMenu());
                dropDownMenu.setEnabled(false);
                dropDownSubmenu.setEnabled(false);
                getDropDownMenus(dropDownModulo.getComponentValue());
            }

            getDropDownModulos(null);
            target.add(txtDescricao);
        }
    }   
    
    private void iniciarMenuWeb(MenuWeb menuWeb){
        getForm().getModel().getObject().setDescricao(menuWeb.getDescricao());
        getForm().getModel().getObject().setModulo(menuWeb.getMenuWebPai());
        getForm().getModel().getObject().setMenu(menuWeb);
        getForm().getModel().getObject().setLayoutMenu(menuWeb.getLayoutMenu());
    }
    
    private MenuWeb carregarMenuWeb(MenuWeb menuWeb){
        return LoadManager.getInstance(MenuWeb.class)
            .addProperties(new HQLProperties(MenuWeb.class).getProperties())
            .addProperties(new HQLProperties(MenuWeb.class, MenuWeb.PROP_MENU_MODULO).getProperties())
            .addProperties(new HQLProperties(MenuWeb.class, MenuWeb.PROP_MENU_WEB_PAI).getProperties())
            .addProperties(new HQLProperties(MenuWeb.class, VOUtils.montarPath(MenuWeb.PROP_MENU_WEB_PAI, MenuWeb.PROP_MENU_WEB_PAI)).getProperties())
            .addProperties(new HQLProperties(MenuWeb.class, VOUtils.montarPath(MenuWeb.PROP_MENU_WEB_PAI, MenuWeb.PROP_MENU_WEB_PAI, MenuWeb.PROP_MENU_WEB_PAI)).getProperties())
            .addProperties(new HQLProperties(ProgramaWeb.class, MenuWeb.PROP_PROGRAMA_WEB).getProperties())
            .addParameter(new QueryCustom.QueryCustomParameter(MenuWeb.PROP_CODIGO, menuWeb.getCodigo()))
            .start().getVO();
    }
    
    private void salvar() throws ValidacaoException, DAOException{
        EdicaoMenuDinamicoDTO dto = getForm().getModel().getObject();
        
        if(txtDescricao.getComponentValue() == null){
           throw new ValidacaoException(bundle("informeDescricao")); 
        }
        
        if(dropDownModulo == null || dropDownModulo.getComponentValue() == null){   
            throw new ValidacaoException(bundle("selecioneModulo"));
        }
        
        dto.getMenu().setMenuWebPai(dropDownModulo.getComponentValue());
        dto.getMenu().setDescricao((String) txtDescricao.getComponentValue());
        dto.getMenu().setLayoutMenu(MenuWeb.LayoutMenu.PADRAO.value());
        dto.setTipoMenu(menuDinamicoDTO.getTipoMenu());
        
        BOFactoryWicket.getBO(BasicoFacade.class).salvarEdicaoMenuDinamico(dto);
        
        CadastroMenuWebDinamico page = new CadastroMenuWebDinamico();
        page.info(bundle("menuSalvoComSucesso"));
        setResponsePage(page);
    }
}
