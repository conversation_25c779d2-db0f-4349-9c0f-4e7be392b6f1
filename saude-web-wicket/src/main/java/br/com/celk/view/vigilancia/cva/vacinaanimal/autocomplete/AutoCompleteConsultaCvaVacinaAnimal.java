package br.com.celk.view.vigilancia.cva.vacinaanimal.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.vo.vigilancia.cva.CvaVacinaAnimal;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class AutoCompleteConsultaCvaVacinaAnimal extends AutoCompleteConsulta<CvaVacinaAnimal> {

    private Long situacao;

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }

    public AutoCompleteConsultaCvaVacinaAnimal(String id) {
        super(id);
    }

    public AutoCompleteConsultaCvaVacinaAnimal(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaCvaVacinaAnimal(String id, IModel<CvaVacinaAnimal> model) {
        super(id, model);
    }

    public AutoCompleteConsultaCvaVacinaAnimal(String id, IModel<CvaVacinaAnimal> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("cvaRacaAnimal");
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {

            @Override
            public Class getReferenceClass() {
                return CvaVacinaAnimal.class;
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(CvaVacinaAnimal.PROP_DESCRICAO, true);
            }

            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaAdapter() {

                    @Override
                    public void consultaCustomizeViewProperties(Map<String, String> properties) {
                        properties.put(BundleManager.getString("codigo"), CvaVacinaAnimal.PROP_CODIGO);
                        properties.put(BundleManager.getString("descricao"), CvaVacinaAnimal.PROP_DESCRICAO);
                    }

                    @Override
                    public void consultaCustomizeFilterProperties(Map<String, BuilderQueryCustom.QueryParameter> filterProperties) {
                        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(CvaVacinaAnimal.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.CONSULTA_LIKED));
                        filterProperties.put(BundleManager.getString("codigo"), new QueryCustom.QueryCustomParameter(CvaVacinaAnimal.PROP_CODIGO));
                    }

                    @Override
                    public void consultaCustomizeParameters(List<BuilderQueryCustom.QueryParameter> parameters) {
                        if (situacao != null) {
                            parameters.add(new QueryCustom.QueryCustomParameter(CvaVacinaAnimal.PROP_SITUACAO, situacao));
                        }
                    }

                    @Override
                    public Class getClassConsulta() {
                        return CvaVacinaAnimal.class;
                    }

                };
            }

            @Override
            public List<BuilderQueryCustom.QueryParameter> getSearchParam(String searchCriteria) {
                List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList();
                parameters.add(new QueryCustom.QueryCustomParameter(CvaVacinaAnimal.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.CONSULTA_LIKED, (searchCriteria != null ? searchCriteria.trim() : null)));
                return parameters;
            }

        };
    }
}
