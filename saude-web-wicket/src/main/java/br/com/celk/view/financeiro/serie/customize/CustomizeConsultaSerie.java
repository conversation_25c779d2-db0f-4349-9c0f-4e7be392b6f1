package br.com.celk.view.financeiro.serie.customize;

import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.financeiro.Serie;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaSerie extends CustomizeConsultaAdapter{

    @Override
    public void consultaCustomizeFilterProperties(Map<String, QueryParameter> filterProperties) {
        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Serie.PROP_DESCRICAO), QueryParameter.ILIKE));
        filterProperties.put(BundleManager.getString("serie"), new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Serie.PROP_SERIE), QueryParameter.ILIKE));
    }

    @Override
    public void consultaCustomizeViewProperties(Map<String, String> properties) {
        properties.put(BundleManager.getString("serie"), VOUtils.montarPath(Serie.PROP_SERIE));
        properties.put(BundleManager.getString("descricao"), VOUtils.montarPath(Serie.PROP_DESCRICAO));
    }

    @Override
    public Class getClassConsulta() {
        return Serie.class;
    }

    @Override
    public String[] getProperties() {
        return VOUtils.mergeProperties(new HQLProperties(Serie.class).getProperties());
    }

}
