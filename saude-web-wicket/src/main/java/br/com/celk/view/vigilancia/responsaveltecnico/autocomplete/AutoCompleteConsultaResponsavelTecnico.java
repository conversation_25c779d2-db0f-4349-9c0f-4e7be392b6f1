package br.com.celk.view.vigilancia.responsaveltecnico.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.vigilancia.responsaveltecnico.autocomplete.restricaocontainer.RestricaoContainerResponsavelTecnico;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QueryConsultaResponsavelTecnicoDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnico;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaResponsavelTecnico extends AutoCompleteConsulta<ResponsavelTecnico> { 

    private boolean filtrarEstabelecimento;

    public AutoCompleteConsultaResponsavelTecnico(String id) {
        super(id);
    }

    public AutoCompleteConsultaResponsavelTecnico(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaResponsavelTecnico(String id, IModel<ResponsavelTecnico> model) {
        super(id, model);
    }

    public AutoCompleteConsultaResponsavelTecnico(String id, IModel<ResponsavelTecnico> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {

            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(ResponsavelTecnico.class);
                
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), VOUtils.montarPath(ResponsavelTecnico.PROP_CODIGO)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("nome"), VOUtils.montarPath(ResponsavelTecnico.PROP_NOME)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerResponsavelTecnico(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<ResponsavelTecnico, QueryConsultaResponsavelTecnicoDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaResponsavelTecnicoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(BasicoFacade.class).consultarResponsavelTecnico(dataPaging);
                    }

                    @Override
                    public QueryConsultaResponsavelTecnicoDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaResponsavelTecnicoDTOParam param = new QueryConsultaResponsavelTecnicoDTOParam();
                        param.setFiltrarEstabelecimento(filtrarEstabelecimento);
                        param.setKeyword(searchCriteria);
                        return param;
                    }
                    
                    @Override
                    public void customizeParam(QueryConsultaResponsavelTecnicoDTOParam param) {
                        param.setPropSort(getSort().getProperty());
                        param.setAscending(getSort().isAscending());
                    }
                    
                    @Override
                    public SortParam getDefaultSort() {
                        return new SortParam(VOUtils.montarPath(ResponsavelTecnico.PROP_NOME), true);
                    }
                };
            }

            @Override
            public Class getReferenceClass() {
                return ResponsavelTecnico.class;
            }
        };
    }

    public void setFiltrarEstabelecimento(boolean filtrarEstabelecimento) {
        this.filtrarEstabelecimento = filtrarEstabelecimento;
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("responsavelTecnico");
    }

}
