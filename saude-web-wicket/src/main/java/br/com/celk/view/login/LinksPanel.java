package br.com.celk.view.login;

import org.apache.wicket.markup.html.form.Button;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public class LinksPanel extends Panel{

    public LinksPanel(String id) {
        super(id);
        init();
    }

    private void init() {
        add(new But<PERSON>("linkLimpar") {
            @Override
            public void onSubmit() {
                setResponsePage(LoginPage.class, null);
            }
        });
        
        add(new But<PERSON>("linkEntrar").setEnabled(false));
    }
    
}
