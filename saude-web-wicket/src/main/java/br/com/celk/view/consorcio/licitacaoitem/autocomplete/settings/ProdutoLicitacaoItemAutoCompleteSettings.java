package br.com.celk.view.consorcio.licitacaoitem.autocomplete.settings;

import br.com.celk.component.consulta.configurator.autocomplete.AbstractAutoCompleteSettings;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class ProdutoLicitacaoItemAutoCompleteSettings extends AbstractAutoCompleteSettings {

    @Override
    public Map<String, String> getJsonPropertyMap(PesquisaObjectInterface o) {
        Map<String, String> propertiesMap = new HashMap<String, String>();
        
        propertiesMap.put("id", o.getIdentificador());
        propertiesMap.put("name", o.getDescricaoVO());
        
        if (o instanceof Produto) {
            propertiesMap.put("exigeGrupo", ((Produto)o).getSubGrupo().getFlagControlaGrupoEstoque());
        }
        
        return propertiesMap;
    }

}
