package     br.com.celk.view.unidadesaude.atendimento.preventivo.dialog;

import br.com.celk.component.table.Table;
import br.com.celk.component.window.Window;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgLancarPreventivoOcorrenciaContato extends Window {

    private PnlLancarPreventivoOcorrenciaContato pnlLancarOcorrencia;

    public DlgLancarPreventivoOcorrenciaContato(String id) {
        super(id);
        init();
    }

    private void init() {
        setInitialWidth(500);
        setInitialHeight(115);
        
        setTitle(bundle("ocorrencia"));
        
        setResizable(false);

        setContent(pnlLancarOcorrencia = new PnlLancarPreventivoOcorrenciaContato(getContentId()) {
            @Override
            public void onSalvar(AjaxRequestTarget target, String ocorrencia) throws DAOException, ValidacaoException {
                DlgLancarPreventivoOcorrenciaContato.this.onSalvar(target, ocorrencia);
                close(target);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                close(target);
            }
        });
    }

    public abstract void onSalvar(AjaxRequestTarget target, String ocorrencia) throws DAOException, ValidacaoException;

    public void show(AjaxRequestTarget target) {
        super.show(target);
        this.pnlLancarOcorrencia.update(target);
    }
}
