package br.com.celk.view.vigilancia.dengue.registrodiarioantivetorial.relatorio;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.RequiredDropDown;
import br.com.celk.report.vigilancia.registrodiarioantivetorial.dto.RelatorioProducaoServicoAntivetorialDTOParam;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.vigilancia.dengue.autocomplete.AutoCompleteConsultaDengueAtividade;
import br.com.celk.view.vigilancia.dengue.autocomplete.AutoCompleteConsultaDengueLocalidade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueCiclo;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.Model;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class RelatorioProducaoServicoAntivetorialPage extends RelatorioPage<RelatorioProducaoServicoAntivetorialDTOParam> {

    private AutoCompleteConsultaDengueLocalidade autoCompleteConsultaLocalidade;

    @Override
    public void init(Form<RelatorioProducaoServicoAntivetorialDTOParam> form) {
        RelatorioProducaoServicoAntivetorialDTOParam proxy = on(RelatorioProducaoServicoAntivetorialDTOParam.class);
        form.add(autoCompleteConsultaLocalidade = new AutoCompleteConsultaDengueLocalidade(path(proxy.getLocalidade())));
        form.add(new AutoCompleteConsultaDengueAtividade(path(proxy.getAtividade())));
        form.add(getCiclo(path(proxy.getCiclo())));
    }

    private DropDown getCiclo(String id) {
        List<DengueCiclo> ciclos = LoadManager.getInstance(DengueCiclo.class)
                .addProperties(new HQLProperties(DengueCiclo.class).getProperties())
                .addSorter(new QueryCustom.QueryCustomSorter(DengueCiclo.PROP_DATA_SEMANA_FINAL))
                .start().getList();

        DropDown<DengueCiclo> dropDownCiclo = new RequiredDropDown<>(id);
        dropDownCiclo.setLabel(new Model(bundle("ciclo")));
        dropDownCiclo.addChoice(null, "");

        for (DengueCiclo ciclo : ciclos) {
            dropDownCiclo.addChoice(ciclo, ciclo.getDescricaoPeriodoCiclo());
        }

        return dropDownCiclo;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaLocalidade.getTxtDescricao().getTextField();
    }

    @Override
    public Class<RelatorioProducaoServicoAntivetorialDTOParam> getDTOParamClass() {
        return RelatorioProducaoServicoAntivetorialDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioProducaoServicoAntivetorialDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioProducaoServicoAntivetorial(param);
    }

    @Override
    public String getTituloPrograma() {
        return bundle("relatorioProducaoServicoAntivetorial");
    }

}
