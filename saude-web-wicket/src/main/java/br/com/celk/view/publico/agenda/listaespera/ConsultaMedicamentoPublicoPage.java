package br.com.celk.view.publico.agenda.listaespera;

import br.com.celk.bo.esus.interfaces.facade.EsusFacade;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.recaptcha.ReCaptchaV3Helper;
import br.com.celk.component.recaptcha.ReCaptchaV3Util;
import br.com.celk.component.recaptcha.ReCaptchaV3WebComponent;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.io.LogoHelper;
import br.com.celk.resources.Resources;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.unidadesaude.esus.cds.interfaces.dto.ConsultaMedicamentoPublicoDTO;
import br.com.celk.unidadesaude.esus.cds.interfaces.dto.ConsultaMedicamentoPublicoDTOParam;
import br.com.celk.view.publico.template.base.BasePagePublico;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.image.Image;
import org.apache.wicket.markup.html.image.NonCachingImage;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.resource.ResourceStreamResource;
import org.apache.wicket.util.resource.FileResourceStream;
import org.wicketstuff.annotation.mount.MountPath;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@MountPath("consulta-medicamento")
public class ConsultaMedicamentoPublicoPage extends BasePagePublico {

    private Form<ConsultaMedicamentoPublicoDTOParam> form;
    private PageableTable tblConsultaMedica;
    private ConsultaMedicamentoPublicoDTOParam param;
    private QueryPagerProvider<ConsultaMedicamentoPublicoDTO, ConsultaMedicamentoPublicoDTOParam> dataProvider;
    private ReCaptchaV3WebComponent reCaptcha;

    private WebMarkupContainer notificationMessage;

    @Override
    protected void onInitialize() {
        super.onInitialize();

        add(notificationMessage = new WebMarkupContainer("notificationMessage"));
        notificationMessage.setOutputMarkupId(true);

        ConsultaMedicamentoPublicoDTOParam proxy = on(ConsultaMedicamentoPublicoDTOParam.class);

        getForm().add(new InputField<String>(path(proxy.getDescricao())));

        // Adicionar WebComponent reCAPTCHA v3 (invisível, sem necessidade de HTML)
        reCaptcha = ReCaptchaV3Helper.addToForm(getForm(), "reCaptcha", "search");

        tblConsultaMedica = new PageableTable("table", getColumns(), getDataProvider());
        tblConsultaMedica.populate();
        getForm().add(tblConsultaMedica);
        form.add(new ProcurarButton<ConsultaMedicamentoPublicoDTOParam>("btnProcurar", tblConsultaMedica) {
            @Override
            public ConsultaMedicamentoPublicoDTOParam getParam() {
                return ConsultaMedicamentoPublicoPage.this.getParam();
            }

            @Override
            public void antesProcurar(AjaxRequestTarget target) throws ValidacaoException {
                executarAntesProcurar();
                super.antesProcurar(target);
            }
        });
        add(form);
    }



    private void executarAntesProcurar() throws ValidacaoException {
        if (StringUtils.trimToNull(getParam().getDescricao()) == null || getParam().getDescricao().length() < 3) {
            throw new ValidacaoException(BundleManager.getString("msgInformeMedicamento"));
        }

        try {
            // Validar reCAPTCHA v3 (cache automático no ReCaptchaV3Util)
            ReCaptchaV3Helper.validate(reCaptcha);

            // Validação no backend com cache inteligente
            ReCaptchaV3Util.ValidationResult result = ReCaptchaV3Util.validateComponent(reCaptcha);
            if (!result.isSuccess()) {
                String errorMsg = String.format("Falha na verificação reCAPTCHA v3 (score: %.2f)", result.getScore());
                throw new ValidacaoException(BundleManager.getString("recaptcha.required", errorMsg));
            }

            System.out.println("DEBUG: reCAPTCHA v3 validado com sucesso - Score: " + result.getScore());

        } catch (RuntimeException e) {
            throw new ValidacaoException(BundleManager.getString("recaptcha.required", e.getMessage()));
        }
    }

    private Form<ConsultaMedicamentoPublicoDTOParam> getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel(getParam()));
        }
        return this.form;
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ConsultaMedicamentoPublicoDTO proxy = on(ConsultaMedicamentoPublicoDTO.class);
        try {
            columns.add(getCustomColumn());
            if (RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("ApresentaColunaCódigoMaterial"))) {
                columns.add(createSortableColumn(BundleManager.getString("codigo"), proxy.getReferencia()));
            }
            columns.add(createSortableColumn(BundleManager.getString("classificacao"), proxy.getClassificacao()));
            columns.add(createSortableColumn(BundleManager.getString("medicamento"), proxy.getDescricao()));
            columns.add(createSortableColumn(BundleManager.getString("unidade"), proxy.getUnidade()));
            columns.add(createSortableColumn(BundleManager.getString("saldoDisponivel"), proxy.getSaldoDisponivelFormatado()));
        } catch (DAOException e) {

        }

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<ConsultaMedicamentoPublicoDTO>() {
            @Override
            public void customizeColumn(ConsultaMedicamentoPublicoDTO rowObject) {
                addAction(ActionType.CONSULTAR_PUBLICO, rowObject, new IModelAction<ConsultaMedicamentoPublicoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ConsultaMedicamentoPublicoDTO modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new ConsultaDetalheMedicamentoPublicoPage(modelObject.getCodigo(), modelObject.getDescricao()));
                    }
                }).setTitleBundleKey("detalhes").setEnabled(rowObject.getSaldoDisponivel() > 0);
            }
        };
    }

    public ConsultaMedicamentoPublicoDTOParam getParam() {
        if (param == null) {
            param = new ConsultaMedicamentoPublicoDTOParam();
        }
        return param;
    }

    private QueryPagerProvider getDataProvider() {
        if (dataProvider == null) {
            dataProvider = new QueryPagerProvider<ConsultaMedicamentoPublicoDTO, ConsultaMedicamentoPublicoDTOParam>() {

                @Override
                public DataPagingResult executeQueryPager(DataPaging<ConsultaMedicamentoPublicoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                    executarAntesProcurar();
                    dataPaging.setParam(getParam());
                    return BOFactoryWicket.getBO(EsusFacade.class).consultarListaPublicaMedicamento(dataPaging);
                }
            };
        }
        return dataProvider;
    }

    @Override
    protected Image carregarLogo() {
        Image logoSistemaListaPublica = null;
        try {
            File logoSistemaTelaLoginFile = LogoHelper.getLogoListaPublica();
            if (logoSistemaTelaLoginFile != null) {
                FileResourceStream fileResourceStream = new FileResourceStream(logoSistemaTelaLoginFile);
                logoSistemaListaPublica = new NonCachingImage("gemIco", new ResourceStreamResource(fileResourceStream)) {
                    @Override
                    protected boolean getStatelessHint() {
                        return true;
                    }
                };
            }
        } finally {
            if (logoSistemaListaPublica == null) {
                logoSistemaListaPublica = new Image("gemIco", Resources.Images.CELK_SAUDE_LIGHT.resourceReference());
            }
        }
        return logoSistemaListaPublica;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaMedicamentos");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnLoadHeaderItem.forScript(JScript.removeByClass("close")));
    }
}
