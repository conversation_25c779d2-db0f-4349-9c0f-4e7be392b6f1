package br.com.celk.view.vigilancia.requerimentos.analiseprojetos.base;

import br.com.celk.component.dropdown.DropDown;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;

import java.util.List;

public class RequerimentosHelper {

    public static boolean allInfoDropsContainValues(List<DropDown> dds) {

        for (DropDown dd : dds) {
            if (isDDEmpty(dd)) {
                return false;
            }
        }
        return true;
    }

    public static boolean isDDEmpty(DropDown dd) {
        return (dd == null || (dd != null && dd.getComponentValue() == null) || (dd != null && dd.getComponentValue() == ""));
    }

    public static boolean isLongSim(DropDown dd) {
        return (dd != null && dd.getComponentValue() != null && RepositoryComponentDefault.SIM_LONG.equals(
                dd.getComponentValue()));
    }

    public static boolean isLongNao(DropDown dd) {
        return (dd != null && dd.getComponentValue() != null && RepositoryComponentDefault.NAO_LONG.equals(
                dd.getComponentValue()));
    }
}
