package br.com.celk.view.unidadesaude.exames.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.basico.tipoexame.autocomplete.AutoCompleteConsultaTipoExame;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioResumoExamesDTOParam;
import br.com.ksisolucoes.report.agendamento.interfaces.facade.AgendamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;


/**
 *
 * <AUTHOR>
 */
@Private

public class ResumoExamesPage extends RelatorioPage<RelatorioResumoExamesDTOParam> {
        
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    
    
    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("unidadeSolicitante")
                .setValidarTipoEstabelecimento(true)
                .setOperadorValor(true) 
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaProfissional("profissionalSolicitante")
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaUsuarioCadsus("paciente")
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaTipoExame("tipoExame")
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaEmpresa("estabelecimentoExecutante"));
        form.add(DropDownUtil.getEnumDropDown("formaApresentacao", RelatorioResumoExamesDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoResumo", RelatorioResumoExamesDTOParam.TipoResumo.values()));
        form.add(new RequiredPnlDatePeriod("periodo"));
        form.add(DropDownUtil.getEnumDropDown("ordenacao", RelatorioResumoExamesDTOParam.Ordenacao.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoDado", RelatorioResumoExamesDTOParam.TipoDado.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoPeriodo", RelatorioResumoExamesDTOParam.TipoPeriodo.values()));

    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioResumoExamesDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioResumoExamesDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(AgendamentoReportFacade.class).relatorioResumoExames(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("resumoExames");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }
}
