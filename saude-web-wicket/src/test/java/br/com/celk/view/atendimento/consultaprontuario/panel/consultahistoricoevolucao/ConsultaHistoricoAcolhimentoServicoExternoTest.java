package br.com.celk.view.atendimento.consultaprontuario.panel.consultahistoricoevolucao;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.prontuario.web.evolucao.dto.HistoricoEvolucaoProntuarioDTO;
import br.com.ksisolucoes.bo.prontuario.web.evolucao.dto.HistoricoEvolucaoProntuarioDTOParam;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.AcolhimentoServicoExterno;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.ProfissionalCargaHoraria;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({LoadManager.class})
public class ConsultaHistoricoAcolhimentoServicoExternoTest {

    @Mock
    private LoadManager loadManagerAcolhimentoMock;

    @Mock
    private LoadManager loadManagerProfissionalCargaHorariaMock;

    HistoricoEvolucaoProntuarioDTOParam param;

    @Before
    public void setUp() throws Exception {
        this.param = new HistoricoEvolucaoProntuarioDTOParam();

        mockStatic(LoadManager.class);
        mockLoadManager(loadManagerAcolhimentoMock, AcolhimentoServicoExterno.class);
        mockLoadManager(loadManagerAcolhimentoMock, AcolhimentoServicoExterno.class);
        mockLoadManager(loadManagerProfissionalCargaHorariaMock, ProfissionalCargaHoraria.class);

        when(loadManagerAcolhimentoMock.getList()).thenReturn(getAcolhimentoServicoExternoList());
        when(loadManagerProfissionalCargaHorariaMock.getList()).thenReturn(getProfissionalCargaHorariaList());
    }

    private void mockLoadManager(LoadManager loadManager, Class clazz) {
        when(LoadManager.getInstance(clazz)).thenReturn(loadManager);
        when(loadManager.addProperty(any())).thenReturn(loadManager);
        when(loadManager.addParameter(any())).thenReturn(loadManager);
        when(loadManager.start()).thenReturn(loadManager);
        when(loadManager.startLeitura()).thenReturn(loadManager);
    }

    private List<Object> getAcolhimentoServicoExternoList() throws Exception {
        List<Object> acolhimentoServicoExternoList = new ArrayList<>();
        AcolhimentoServicoExterno acolhimentoServicoExterno = new AcolhimentoServicoExterno();

        acolhimentoServicoExterno.setDadoAtendimento("dados atendimento");
        acolhimentoServicoExterno.setDataCadastro(DataUtil.stringToDate("22/02/2021"));
        acolhimentoServicoExterno.setUsuario(new Usuario());
        acolhimentoServicoExterno.getUsuario().setNome("User name");
        acolhimentoServicoExterno.setEmpresa(new Empresa());
        acolhimentoServicoExterno.getEmpresa().setDescricao("Company");

        acolhimentoServicoExternoList.add(acolhimentoServicoExterno);

        return acolhimentoServicoExternoList;
    }

    private List<Object> getProfissionalCargaHorariaList() {
        List<Object> profissionalCargaHorariaList = new ArrayList<>();
        ProfissionalCargaHoraria profissionalCargaHoraria = new ProfissionalCargaHoraria();
        profissionalCargaHoraria.setTabelaCbo(new TabelaCbo());
        profissionalCargaHoraria.getTabelaCbo().setCbo("123456");
        profissionalCargaHoraria.getTabelaCbo().setDescricao("CBO description");

        profissionalCargaHorariaList.add(profissionalCargaHoraria);

        return profissionalCargaHorariaList;
    }

    @Test
    public void buildProntuarios() throws Exception {
        HistoricoEvolucaoProntuarioDTO historicoEvolucaoProntuarioDTO = ConsultaHistoricoAcolhimentoServicoExterno.buildProntuarios(param).get(0);

        assertEquals("should set descrição tipo Atendimento", "Acolhimento por Serviço Externo", historicoEvolucaoProntuarioDTO.getDescricaoTipoAtendimento());
        assertEquals("should set evolução text", "dados atendimento", historicoEvolucaoProntuarioDTO.getHtmlEvolucao());
        assertEquals("should set data atendimento", DataUtil.stringToDate("22/02/2021"), historicoEvolucaoProntuarioDTO.getDataAtendimento());
        assertEquals("should set descrição unidade", "Company", historicoEvolucaoProntuarioDTO.getDescricaoUnidade());
        assertEquals("should set nome usuario", "User name", historicoEvolucaoProntuarioDTO.getNomeProfissional());
        assertEquals("should set cbo usuario", " (123456) CBO description", historicoEvolucaoProntuarioDTO.getCodigoCbo());

        AcolhimentoServicoExterno acolhimentoServicoExterno = (AcolhimentoServicoExterno) getAcolhimentoServicoExternoList().get(0);
        acolhimentoServicoExterno.getUsuario().setProfissional(new Profissional());
        acolhimentoServicoExterno.getUsuario().getProfissional().setNome("Professional name");

        when(loadManagerAcolhimentoMock.getList()).thenReturn(Collections.singletonList(acolhimentoServicoExterno));
        historicoEvolucaoProntuarioDTO = ConsultaHistoricoAcolhimentoServicoExterno.buildProntuarios(param).get(0);

        assertEquals("should set nome profissional", "Professional name", historicoEvolucaoProntuarioDTO.getNomeProfissional());
    }
}