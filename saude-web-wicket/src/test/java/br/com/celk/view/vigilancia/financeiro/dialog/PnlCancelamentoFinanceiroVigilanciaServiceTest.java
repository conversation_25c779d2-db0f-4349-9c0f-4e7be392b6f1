package br.com.celk.view.vigilancia.financeiro.dialog;

import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import junit.framework.TestCase;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

public class PnlCancelamentoFinanceiroVigilanciaServiceTest extends TestCase {

    PnlCancelamentoFinanceiroVigilanciaService instanceService;

    @Before
    public void setUp() {
        instanceService = PnlCancelamentoFinanceiroVigilanciaService.getInstance();

    }

    @Test
    public void testGetMsgCancelamento_parametroNullNaoDeveQuebrar() {
        assertTrue(instanceService.getMsgCancelamento(null).isEmpty());
    }

    @Test
    public void testGetMsgCancelamento_deveRetornarCorretamente() {
        RequerimentoVigilancia requerimentoVigilancia = new RequerimentoVigilancia();
        requerimentoVigilancia.setProtocolo(1234562020L);
        assertFalse(instanceService.getMsgCancelamento(requerimentoVigilancia).isEmpty());
        assertEquals("Cancelar também o requerimento com protocolo 123456/2020.", instanceService.getMsgCancelamento(requerimentoVigilancia));
    }

    @Test
    public void testValidarCadastro_parametroNullDeveRetornarExeption() {
        try {
            instanceService.validarCadastro(null);
        } catch (ValidacaoException e) {
            assertTrue(e.getMessage().equals("Informe o Motivo!"));
        }
        try {
            instanceService.validarCadastro("");
        } catch (ValidacaoException e) {
            assertTrue(e.getMessage().equals("Informe o Motivo!"));
        }
    }

    @Test
    public void testValidarCadastro_motivoinformadoDeveRetornarTrue() throws ValidacaoException {
        assertTrue(instanceService.validarCadastro("motivo"));
    }

    public void testGetRequerimentoVigilanciaParaCancelar() {
        RequerimentoVigilancia requerimentoVigilancia = new RequerimentoVigilancia();
        requerimentoVigilancia.setSituacao(RequerimentoVigilancia.Situacao.FINALIZADO.value());
        assertNull(instanceService.getRequerimentoVigilanciaParaCancelar(requerimentoVigilancia));

        requerimentoVigilancia.setSituacao(RequerimentoVigilancia.Situacao.CANCELADO.value());
        assertNull(instanceService.getRequerimentoVigilanciaParaCancelar(requerimentoVigilancia));

        List<RequerimentoVigilancia.Situacao> values = Arrays.asList(RequerimentoVigilancia.Situacao.values());
        requerimentoVigilancia.setCodigo(1L);
        for (RequerimentoVigilancia.Situacao value : values) {
            if (value.value().equals(RequerimentoVigilancia.Situacao.CANCELADO.value()) || value.value().equals(RequerimentoVigilancia.Situacao.FINALIZADO.value()))
                continue;
            requerimentoVigilancia.setSituacao(value.value());
            assertEquals(requerimentoVigilancia.getCodigo(), instanceService.getRequerimentoVigilanciaParaCancelar(requerimentoVigilancia).getCodigo());
        }
    }

    public void testGetRequerimentoVigilanciaParaCancelar_parametroNullNaoDeveQuebrar() {
        assertNull(instanceService.getRequerimentoVigilanciaParaCancelar(null));
    }
}