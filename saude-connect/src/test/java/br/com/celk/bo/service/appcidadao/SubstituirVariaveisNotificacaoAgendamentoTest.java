package br.com.celk.bo.service.appcidadao;

import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.joda.time.LocalDate;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.Locale;

public class SubstituirVariaveisNotificacaoAgendamentoTest {
    private final String mensagem = "Data: $data$, Nome: $nome_paciente$";
    private AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario;
    private TipoProcedimento tipoProcedimento;

    @Before
    public void setUp() throws Exception {
        tipoProcedimento = new TipoProcedimento();
        tipoProcedimento.setDescricao("Procedimento teste");
        UsuarioCadsus usuarioCadsus = new UsuarioCadsus();
        usuarioCadsus.setNome("Adriano");
        agendaGradeAtendimentoHorario = new AgendaGradeAtendimentoHorario();
        agendaGradeAtendimentoHorario.setTipoProcedimento(tipoProcedimento);
        agendaGradeAtendimentoHorario.setUsuarioCadsus(usuarioCadsus);
        agendaGradeAtendimentoHorario.setDataAgendamento(new LocalDate("2020-02-21").toDate());
        agendaGradeAtendimentoHorario.setLocalAgendamento(new Empresa());
    }

    @Test
    public void substituirVariaveis() {
        Locale.setDefault(new Locale("pt", "BR"));
        SubstituirVariaveisNotificacaoAgendamento substituirVariaveisNotificacaoAgendamento =
                new SubstituirVariaveisNotificacaoAgendamento(agendaGradeAtendimentoHorario);
        String resultado = substituirVariaveisNotificacaoAgendamento.substituirVariaveis(mensagem);
        Assert.assertEquals("Data: 21/02/2020, Nome: Adriano", resultado);
    }

    @Test
    public void substituirVariaveisQuandoLocaleNaoForPtBrDeveRetornarDataNoPadraoPtBr() {
        Locale.setDefault(new Locale("en", "US"));
        SubstituirVariaveisNotificacaoAgendamento substituirVariaveisNotificacaoAgendamento =
                new SubstituirVariaveisNotificacaoAgendamento(agendaGradeAtendimentoHorario);
        String resultado = substituirVariaveisNotificacaoAgendamento.substituirVariaveis(mensagem);
        Assert.assertEquals("Data: 21/02/2020, Nome: Adriano", resultado);
    }
}