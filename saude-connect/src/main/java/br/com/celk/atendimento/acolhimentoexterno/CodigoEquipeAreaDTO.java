package br.com.celk.atendimento.acolhimentoexterno;

import java.io.Serializable;

public class CodigoEquipeAreaDTO implements Serializable {

    private Long codigoEquipeUsuario;

    private Long codigoEquipeEnderecoEstruturado;

    private Long codigoEquipeDomicilio;


    public Long getCodigoEquipeUsuario() {
        return codigoEquipeUsuario;
    }

    public void setCodigoEquipeUsuario(Long codigoEquipeUsuario) {
        this.codigoEquipeUsuario = codigoEquipeUsuario;
    }

    public Long getCodigoEquipeEnderecoEstruturado() {
        return codigoEquipeEnderecoEstruturado;
    }

    public void setCodigoEquipeEnderecoEstruturado(Long codigoEquipeEnderecoEstruturado) {
        this.codigoEquipeEnderecoEstruturado = codigoEquipeEnderecoEstruturado;
    }

    public Long getCodigoEquipeDomicilio() {
        return codigoEquipeDomicilio;
    }

    public void setCodigoEquipeDomicilio(Long codigoEquipeDomicilio) {
        this.codigoEquipeDomicilio = codigoEquipeDomicilio;
    }

    public Long getCodigoMicroArea() {
        if (this.codigoEquipeUsuario != null) {
            return this.codigoEquipeUsuario;
        }
        if (this.codigoEquipeEnderecoEstruturado != null) {
            return this.codigoEquipeEnderecoEstruturado;
        } else {
            return this.codigoEquipeDomicilio;
        }
    }
}