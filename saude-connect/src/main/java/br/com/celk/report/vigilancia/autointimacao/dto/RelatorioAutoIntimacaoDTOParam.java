package br.com.celk.report.vigilancia.autointimacao.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.grupoestabelecimento.GrupoEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioAutoIntimacaoDTOParam implements Serializable {

    private Empresa empresa;
    private Estabelecimento estabelecimento;
    private VigilanciaPessoa vigilanciaPessoa;
    private Long tipoDenunciado;
    private Long situacao;
    private Profissional profissional;
    private DatePeriod periodo;
    private FormaApresentacao formaApresentacao;
    private PrazoDefesa prazoDefesa;
    private TipoPeriodo tipoPeriodo;
    private Ordenacao ordenacao;
    private GrupoEstabelecimento grupoEstabelecimento;
    private AtividadeEstabelecimento atividadeEstabelecimento;

    public static enum FormaApresentacao {

        GERAL(Bundle.getStringApplication("rotulo_geral")),
        ATIVIDADE(Bundle.getStringApplication("rotulo_atividade_cnae")),
        GRUPO(Bundle.getStringApplication("rotulo_grupo")),
        FISCAL(Bundle.getStringApplication("rotulo_fiscal")),
        PRAZO(Bundle.getStringApplication("rotulo_prazo"));

        private final String descricao;

        private FormaApresentacao(String descricao) {
            this.descricao = descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    public static enum TipoPeriodo {

        PRAZO(Bundle.getStringApplication("rotulo_prazo")),
        FISCALIZACAO(Bundle.getStringApplication("rotulo_data_intimacao"));

        private final String descricao;

        private TipoPeriodo(String descricao) {
            this.descricao = descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    public static enum PrazoDefesa {

        A_VENCER(Bundle.getStringApplication("rotulo_a_vencer")),
        VENCIDO(Bundle.getStringApplication("rotulo_vencido")),;

        private final String descricao;

        private PrazoDefesa(String descricao) {
            this.descricao = descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    @DescricaoParametro("rotulo_estabelecimento")
    public Estabelecimento getEstabelecimento() {
        return estabelecimento;
    }

    public void setEstabelecimento(Estabelecimento estabelecimento) {
        this.estabelecimento = estabelecimento;
    }

    public static enum Ordenacao {

        ESTABELECIMENTO(Bundle.getStringApplication("rotulo_estabelecimento")),
        DATA_REGISTRO(Bundle.getStringApplication("rotulo_data_registro"));

        private final String descricao;

        private Ordenacao(String descricao) {
            this.descricao = descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    @DescricaoParametro("rotulo_prazo_defesa")
    public String getDescricaoPrazoDefesa() {
        if (getPrazoDefesa() == null) {
            return Bundle.getStringApplication("rotulo_ambos");
        }
        return getPrazoDefesa().toString();
    }

    public PrazoDefesa getPrazoDefesa() {
        return prazoDefesa;
    }

    public void setPrazoDefesa(PrazoDefesa prazoDefesa) {
        this.prazoDefesa = prazoDefesa;
    }

    @DescricaoParametro("rotulo_tipo_periodo")
    public TipoPeriodo getTipoPeriodo() {
        return tipoPeriodo;
    }

    public void setTipoPeriodo(TipoPeriodo tipoPeriodo) {
        this.tipoPeriodo = tipoPeriodo;
    }

    @DescricaoParametro("rotulo_empresa")
    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_profissional")
    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    @DescricaoParametro("rotulo_ordenacao")
    public Ordenacao getOrdenacao() {
        return ordenacao;
    }

    public void setOrdenacao(Ordenacao ordenacao) {
        this.ordenacao = ordenacao;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_grupo_estabelecimento")
    public GrupoEstabelecimento getGrupoEstabelecimento() {
        return grupoEstabelecimento;
    }

    public void setGrupoEstabelecimento(GrupoEstabelecimento grupoEstabelecimento) {
        this.grupoEstabelecimento = grupoEstabelecimento;
    }

    @DescricaoParametro("rotulo_atividade_estabelecimento")
    public AtividadeEstabelecimento getAtividadeEstabelecimento() {
        return atividadeEstabelecimento;
    }

    public void setAtividadeEstabelecimento(AtividadeEstabelecimento atividadeEstabelecimento) {
        this.atividadeEstabelecimento = atividadeEstabelecimento;
    }

    @DescricaoParametro("rotulo_tipo_autuado")
    public String getDescricaoTipoDenunciado() {
        if (getTipoDenunciado() != null) {
            return AutoIntimacao.TipoDenunciado.valeuOf(getTipoDenunciado()).descricao();
        }
        return Bundle.getStringApplication("rotulo_ambos");
    }

    public Long getTipoDenunciado() {
        return tipoDenunciado;
    }

    public void setTipoDenunciado(Long tipoDenunciado) {
        this.tipoDenunciado = tipoDenunciado;
    }

    @DescricaoParametro("rotulo_pessoa")
    public VigilanciaPessoa getVigilanciaPessoa() {
        return vigilanciaPessoa;
    }

    public void setVigilanciaPessoa(VigilanciaPessoa vigilanciaPessoa) {
        this.vigilanciaPessoa = vigilanciaPessoa;
    }


    @DescricaoParametro("rotulo_situacao")
    public String getDescricaoSituacao() {
        if (getSituacao() != null) {
            return AutoIntimacao.Status.valeuOf(getSituacao()).descricao();
        }
        return "Todas";
    }

    public Long getSituacao() {
        return situacao;
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }

}
