package br.com.celk.unidadesaude.esus.relatorios;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.cadsus.MotivoVisitaDomiciliar;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.VisitaDomiciliar;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class RelatorioVisitaDomiciliarAcsDTO implements Serializable {
    private Long nrVisitas;
    private Long quantidade;
    private Long quantidadeDomicilio;
    private Long quantidadeDentroArea;
    private Long quantidadeForaArea;
    private Long quantidadeFamiliaMicroArea;
    private Profissional profissional;
    private UsuarioCadsus usuarioCadsus;
    private EquipeMicroArea equipeMicroArea;
    private VisitaDomiciliar visitaDomiciliar;
    private MotivoVisitaDomiciliar motivoVisitaDomiciliar;
    private String descricaoEnderecoTelefone;

    public Long getNrVisitas() {
        return nrVisitas;
    }

    public void setNrVisitas(Long nrVisitas) {
        this.nrVisitas = nrVisitas;
    }

    public Long getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Long quantidade) {
        this.quantidade = quantidade;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public EquipeMicroArea getEquipeMicroArea() {
        return equipeMicroArea;
    }

    public void setEquipeMicroArea(EquipeMicroArea equipeMicroArea) {
        this.equipeMicroArea = equipeMicroArea;
    }

    public VisitaDomiciliar getVisitaDomiciliar() {
        return visitaDomiciliar;
    }

    public void setVisitaDomiciliar(VisitaDomiciliar visitaDomiciliar) {
        this.visitaDomiciliar = visitaDomiciliar;
    }

    public MotivoVisitaDomiciliar getMotivoVisitaDomiciliar() {
        return motivoVisitaDomiciliar;
    }

    public void setMotivoVisitaDomiciliar(MotivoVisitaDomiciliar motivoVisitaDomiciliar) {
        this.motivoVisitaDomiciliar = motivoVisitaDomiciliar;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public Long getQuantidadeDomicilio() {
        return quantidadeDomicilio;
    }

    public void setQuantidadeDomicilio(Long quantidadeDomicilio) {
        this.quantidadeDomicilio = quantidadeDomicilio;
    }

    public Long getQuantidadeDentroArea() { return quantidadeDentroArea; }

    public void setQuantidadeDentroArea(Long quantidadeDentroArea) { this.quantidadeDentroArea = quantidadeDentroArea; }

    public Long getQuantidadeForaArea() { return quantidadeForaArea; }

    public void setQuantidadeForaArea(Long quantidadeForaArea) { this.quantidadeForaArea = quantidadeForaArea; }

    public Long getQuantidadeFamiliaMicroArea() {
        return quantidadeFamiliaMicroArea;
    }

    public void setQuantidadeFamiliaMicroArea(Long quantidadeFamiliaMicroArea) {
        this.quantidadeFamiliaMicroArea = quantidadeFamiliaMicroArea;
    }

    public Double getPorcentagemDomiciliosVisitados() {
        if(Coalesce.asLong(getQuantidadeFamiliaMicroArea()) > 0L){
            return new Dinheiro(new Dinheiro(Coalesce.asLong(getQuantidadeDentroArea())).multiplicar(100D).doubleValue()).dividir(Coalesce.asLong(getQuantidadeFamiliaMicroArea()), 2).doubleValue();
        }
        return 0D;
    }

    public String getDescricaoEnderecoTelefone() {
        StringBuilder builder = new StringBuilder();
        if(getVisitaDomiciliar().getEnderecoDomicilio() != null && getVisitaDomiciliar().getEnderecoDomicilio().getEnderecoUsuarioCadsus() != null
                && StringUtils.trimToNull(getVisitaDomiciliar().getEnderecoDomicilio().getEnderecoUsuarioCadsus().getEnderecoFormatadoComCidade()) != null) {
            builder.append(getVisitaDomiciliar().getEnderecoDomicilio().getEnderecoUsuarioCadsus().getEnderecoFormatadoComCidade());
        }
        if(StringUtils.trimToNull(getUsuarioCadsus().getTelefonesCelularFormatado()) != null) {
            builder.append(" / ");
            builder.append(getUsuarioCadsus().getTelefonesCelularFormatado());
        }
        return builder.toString();
    }

    public void setDescricaoEnderecoTelefone(String descricaoEnderecoTelefone) {
        this.descricaoEnderecoTelefone = descricaoEnderecoTelefone;
    }
}
