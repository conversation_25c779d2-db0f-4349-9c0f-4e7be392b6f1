package br.com.celk.bo.hospital.endereco;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.CepBrasil;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.enderecoestruturado.EnderecoEstruturado;
import ch.lambdaj.Lambda;
import org.hamcrest.Matchers;

import java.util.Arrays;
import java.util.List;

import static ch.lambdaj.Lambda.having;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class EnderecoHelper {

    /**
     * @param codigoIbge com seis digitos
     * @return digito verificador
     * @throws DAOException
     * @throws ValidacaoException Pega-se cada digito do codigo e multiplica cada um pelo seu respectivo
     *                            peso (121212), em ordem, obtendo um resultante r, pega-se r e soma todos
     *                            os digitos em um somatorio s, divide-se s por 10, pega-se 10 e subtrai o
     *                            resto da divisao anterior, este é o digito verificador
     */
    public static Long getDigitoVerificadorIbge(Long codigoIbge) throws ValidacaoException {

        Long dv;
        String cod = codigoIbge.toString();
        String peso = "121212";
        StringBuilder r = new StringBuilder();
        for (int i = 0; i < 6; i++) {
            Long l = (Long.valueOf(cod.substring(i, i + 1)) * Long.valueOf(peso.substring(i, i + 1)));
            r.append(l.toString());
        }
        Long somatorio = 0L;
        for (int i = 0; i < r.length(); i++) {
            somatorio += Long.valueOf(r.substring(i, i + 1));
        }
        Long resto = somatorio % 10;
        dv = 10 - resto;
        if (dv.equals(10L)) {
            return 0L;
        }
        return dv;
    }

    /**
     * @param codigoIbge codigo com seis digitos
     * @return codigo com sete digitos
     * @throws DAOException
     * @throws ValidacaoException
     */
    public static Long getCodigoIbgeComDV(Long codigoIbge) throws DAOException, ValidacaoException {
        if (codigoIbge.toString().length() != 6) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_codigo_ibge_deve_ter_seis_digitos", codigoIbge));
        }

        StringBuilder codigo = new StringBuilder();
        codigo.append(codigoIbge);
        Long code = codigoIbgeNaoPadronizado(codigoIbge);
        if (code == 0) {
            codigo.append(getDigitoVerificadorIbge(codigoIbge));
            return Long.valueOf(codigo.toString());
        } else {
            return code;
        }
    }

    public static boolean isCepValido(String cep) {
        LoadManager loadManager = LoadManager.getInstance(CepBrasil.class)
                .addParameter(new QueryCustom.QueryCustomParameter(CepBrasil.PROP_CEP, Long.valueOf(cep)))
                .setMaxResults(1).start();

        return loadManager.exists();
    }

    public static int getNumeroMaisProximo(int numero, List<Long> listaNumeros) {
        int primeiroElemento = listaNumeros.get(0).intValue();
        int distance = Math.abs(primeiroElemento - numero);
        int idx = 0;
        for (int c = 1; c < listaNumeros.size(); c++) {
            int numC = listaNumeros.get(c).intValue();
            int cdistance = Math.abs(numC - numero);
            if (cdistance < distance) {
                idx = c;
                distance = cdistance;
            }
        }
        return listaNumeros.get(idx).intValue();
    }

    public static EnderecoEstruturado getEnderecoEstruturadoMaisProximo(List<EnderecoEstruturado> enderecoEstruturadoLogradouroList, String numero) {
        if (CollectionUtils.isNotNullEmpty(enderecoEstruturadoLogradouroList)) {
            EnderecoEstruturado enderecoEstruturado;
            EnderecoEstruturado enderecoExistente = Lambda.selectFirst(enderecoEstruturadoLogradouroList, having(on(EnderecoEstruturado.class).getNumero(), Matchers.equalTo(numero)));
            if (enderecoExistente == null) {
                List<Long> numeros = Lambda.extract(enderecoEstruturadoLogradouroList, on(EnderecoEstruturado.class).getnumeroAbsoluto());

                int numeroMaisProximo = EnderecoHelper.getNumeroMaisProximo(Long.valueOf(StringUtil.getDigits(numero)).intValue(), numeros);

                enderecoEstruturado = Lambda.selectFirst(enderecoEstruturadoLogradouroList, having(on(EnderecoEstruturado.class).getnumeroAbsoluto(), Matchers.equalTo(Long.valueOf(numeroMaisProximo))));
            } else {
                enderecoEstruturado = enderecoExistente;
            }
            if (enderecoEstruturado.getEquipeMicroArea() == null) {
                EquipeMicroArea equipeMicroArea = localizarEquipeMicroAreaMaisProxima(enderecoEstruturadoLogradouroList);
                enderecoEstruturado.setEquipeMicroArea(equipeMicroArea);
            }
            return enderecoEstruturado;
        }
        return null;
    }

    public static EquipeMicroArea localizarEquipeMicroAreaMaisProxima(List<EnderecoEstruturado> enderecoEstruturadoLogradouroList) {
        EnderecoEstruturado enderecoMicroArea = Lambda.selectFirst(enderecoEstruturadoLogradouroList, having(on(EnderecoEstruturado.class).getEquipeMicroArea(), Matchers.notNullValue()));
        if (enderecoMicroArea != null) return enderecoMicroArea.getEquipeMicroArea();

        return null;
    }

    private static Long codigoIbgeNaoPadronizado(Long codigoIbge) {
        List<Long> codigosNaoPadronizados = Arrays.asList(2201919L, 2202251L, 2201988L, 2611533L, 3117836L, 3152131L, 4305871L, 5203939L, 5203962L);
        for (Long codigo : codigosNaoPadronizados) {
            if (codigoIbge.toString().startsWith(codigo.toString().substring(0, 6))) {
                return codigo;
            }
        }
        return 0L;
    }
}
