package br.com.celk.agendamento.ppi.dto;

import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.prontuario.basico.PpiOcorrencia;

import java.io.Serializable;
import java.util.Date;

public class PpiOcorrenciaDTO implements Serializable {
    private Long cdPpiOcorrencia;
    private Long cdPpiSecretaria;
    private Long cdPpiTipoExame;
    private Long cdPpiExame;
    private Long tpOcorrencia;
    private Date dtOcorrencia;
    private String dsOcorrencia;
    private Long cdUsuario;
    private String nomeUsuario;
    private Long cdTipoExame;
    private Long cdExameProcedimento;
    private Long cdPpiSecretariaDestino;
    private Long cdPpiTipoExameDestino;
    private Long cdPpiExameDestino;
    private Long cdSolicitacaoAgendamento;

    public Long getCdPpiOcorrencia() {
        return cdPpiOcorrencia;
    }

    public void setCdPpiOcorrencia(Long cdPpiOcorrencia) {
        this.cdPpiOcorrencia = cdPpiOcorrencia;
    }

    public Long getCdPpiSecretaria() {
        return cdPpiSecretaria;
    }

    public void setCdPpiSecretaria(Long cdPpiSecretaria) {
        this.cdPpiSecretaria = cdPpiSecretaria;
    }

    public Long getCdPpiTipoExame() {
        return cdPpiTipoExame;
    }

    public void setCdPpiTipoExame(Long cdPpiTipoExame) {
        this.cdPpiTipoExame = cdPpiTipoExame;
    }

    public Long getCdPpiExame() {
        return cdPpiExame;
    }

    public void setCdPpiExame(Long cdPpiExame) {
        this.cdPpiExame = cdPpiExame;
    }

    public Long getTpOcorrencia() {
        return tpOcorrencia;
    }

    public void setTpOcorrencia(Long tpOcorrencia) {
        this.tpOcorrencia = tpOcorrencia;
    }

    public Date getDtOcorrencia() {
        return dtOcorrencia;
    }

    public void setDtOcorrencia(Date dtOcorrencia) {
        this.dtOcorrencia = dtOcorrencia;
    }

    public String getDsOcorrencia() {
        return dsOcorrencia;
    }

    public void setDsOcorrencia(String dsOcorrencia) {
        this.dsOcorrencia = dsOcorrencia;
    }

    public Long getCdUsuario() {
        return cdUsuario;
    }

    public void setCdUsuario(Long cdUsuario) {
        this.cdUsuario = cdUsuario;
    }

    public String getNomeUsuario(){
        return nomeUsuario;
    }

    public void setNomeUsuario(String nomeUsuario) {
        this.nomeUsuario = nomeUsuario;
    }

    public String getTipoOcorrenciaDescricao(){
        if (tpOcorrencia == null) return "";
        return PpiOcorrencia.TipoOcorrencia.valueOf(tpOcorrencia).descricao();
    }

    public String getOcorrenciaDataHora(){
        if (dtOcorrencia == null) return "";
        return Data.formatarDataHora(dtOcorrencia);
    }

    public Long getCdTipoExame() {
        return cdTipoExame;
    }

    public void setCdTipoExame(Long cdTipoExame) {
        this.cdTipoExame = cdTipoExame;
    }

    public Long getCdExameProcedimento() {
        return cdExameProcedimento;
    }

    public void setCdExameProcedimento(Long cdExameProcedimento) {
        this.cdExameProcedimento = cdExameProcedimento;
    }

    public Long getCdPpiSecretariaDestino() {
        return cdPpiSecretariaDestino;
    }

    public void setCdPpiSecretariaDestino(Long cdPpiSecretariaDestino) {
        this.cdPpiSecretariaDestino = cdPpiSecretariaDestino;
    }

    public Long getCdPpiTipoExameDestino() {
        return cdPpiTipoExameDestino;
    }

    public void setCdPpiTipoExameDestino(Long cdPpiTipoExameDestino) {
        this.cdPpiTipoExameDestino = cdPpiTipoExameDestino;
    }

    public Long getCdPpiExameDestino() {
        return cdPpiExameDestino;
    }

    public void setCdPpiExameDestino(Long cdPpiExameDestino) {
        this.cdPpiExameDestino = cdPpiExameDestino;
    }

    public Long getCdSolicitacaoAgendamento() {
        return cdSolicitacaoAgendamento;
    }

    public void setCdSolicitacaoAgendamento(Long cdSolicitacaoAgendamento) {
        this.cdSolicitacaoAgendamento = cdSolicitacaoAgendamento;
    }
}
