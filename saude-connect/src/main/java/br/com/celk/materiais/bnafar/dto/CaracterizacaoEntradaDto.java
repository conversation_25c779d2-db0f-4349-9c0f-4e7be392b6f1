package br.com.celk.materiais.bnafar.dto;

import java.io.Serializable;

public class CaracterizacaoEntradaDto implements Serializable {

    public CaracterizacaoEntradaDto() {
    }

    public CaracterizacaoEntradaDto(String codigoOrigem, String cnesCnpjDistribuidor, String dataEntrada, String numeroDocumento, String tipoEntrada) {
        this.codigoOrigem = codigoOrigem;
        this.cnesCnpjDistribuidor = cnesCnpjDistribuidor;
        this.dataEntrada = dataEntrada;
        this.numeroDocumento = numeroDocumento;
        this.tipoEntrada = tipoEntrada;
    }

    private String codigoOrigem;
    private String cnesCnpjDistribuidor;
    private String dataEntrada;
    private String numeroDocumento;
    private String tipoEntrada;

    public String getCodigoOrigem() {
        return codigoOrigem;
    }

    public void setCodigoOrigem(String codigoOrigem) {
        this.codigoOrigem = codigoOrigem;
    }

    public String getCnesCnpjDistribuidor() {
        return cnesCnpjDistribuidor;
    }

    public void setCnesCnpjDistribuidor(String cnesCnpjDistribuidor) {
        this.cnesCnpjDistribuidor = cnesCnpjDistribuidor;
    }

    public String getDataEntrada() {
        return dataEntrada;
    }

    public void setDataEntrada(String dataEntrada) {
        this.dataEntrada = dataEntrada;
    }

    public String getNumeroDocumento() {
        return numeroDocumento;
    }

    public void setNumeroDocumento(String numeroDocumento) {
        this.numeroDocumento = numeroDocumento;
    }

    public String getTipoEntrada() {
        return tipoEntrada;
    }

    public void setTipoEntrada(String tipoEntrada) {
        this.tipoEntrada = tipoEntrada;
    }

    public String toJson() {
        StringBuilder sb = new StringBuilder();
        sb.append("{");
        sb.append("\"codigoOrigem\"").append(": \"").append(codigoOrigem).append("\",");
        sb.append("\"cnesCnpjDistribuidor\"").append(": \"").append(cnesCnpjDistribuidor).append("\",");
        sb.append("\"dataEntrada\"").append(": \"").append(dataEntrada).append("\",");
        sb.append("\"numeroDocumento\"").append(": \"").append(numeroDocumento).append("\",");
        sb.append("\"tipoEntrada\"").append(": \"").append(tipoEntrada).append("\"");
        sb.append("}");
        return sb.toString();
    }


}
