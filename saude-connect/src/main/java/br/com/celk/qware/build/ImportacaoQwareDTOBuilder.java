package br.com.celk.qware.build;

import br.com.celk.importacaoExame.dto.ImportacaoExameLacenDTO;
import br.com.celk.qware.dto.ImportacaoQwareDTO;

import java.util.Date;

public class ImportacaoQwareDTOBuilder {

    private ImportacaoQwareDTO importacaoQwareDTO;


    public ImportacaoQwareDTOBuilder() {
        this.importacaoQwareDTO = new ImportacaoQwareDTO();
    }

    public ImportacaoQwareDTOBuilder setCodigoFamiliar(String codigoFamiliar) {
        this.importacaoQwareDTO.setCodigoFamiliar(codigoFamiliar.equals("") ? null: Long.valueOf(codigoFamiliar));
        return this;
    }

    public ImportacaoQwareDTOBuilder setNis(String nis) {
        this.importacaoQwareDTO.setNis(nis);
        return this;
    }

    public ImportacaoQwareDTOBuilder setNomeIntegrante(String nomeIntegrante) {
        this.importacaoQwareDTO.setNomeIntegrante(nomeIntegrante);
        return this;
    }

    public ImportacaoQwareDTOBuilder setDataUltimaAlteracaoPessoa(Date dataUltimaAlteracaoPessoa) {
        this.importacaoQwareDTO.setDataUltimaAlteracaoPessoa(dataUltimaAlteracaoPessoa);
        return this;
    }

    public ImportacaoQwareDTOBuilder setDataNascimento(Date dataNascimento) {
        this.importacaoQwareDTO.setDataNascimento(dataNascimento);
        return this;
    }

    public ImportacaoQwareDTOBuilder setSexo(String sexo) {
        this.importacaoQwareDTO.setSexo(sexo.equals("") ? null: Long.valueOf(sexo));
        return this;
    }

    public ImportacaoQwareDTOBuilder setNomeMae(String nomeMae) {
        this.importacaoQwareDTO.setNomeMae(nomeMae);
        return this;
    }

    public ImportacaoQwareDTOBuilder setRaca(String raca) {
        this.importacaoQwareDTO.setRaca( raca.equals("") ? null: Long.valueOf(raca) );
        return this;
    }

    public ImportacaoQwareDTOBuilder setCpf(String cpf) {
        this.importacaoQwareDTO.setCpf(cpf);
        return this;
    }

    public ImportacaoQwareDTOBuilder setInep(String inep) {
        this.importacaoQwareDTO.setInep(inep);
        return this;
    }

    public ImportacaoQwareDTOBuilder setNumeroSerieEscolar(String numeroSerieEscolar) {
        this.importacaoQwareDTO.setNumeroSerieEscolar(numeroSerieEscolar);
        return this;
    }

    public ImportacaoQwareDTOBuilder setInPA(String inPA) {
        this.importacaoQwareDTO.setInPA(inPA);
        return this;
    }

    public ImportacaoQwareDTOBuilder setTipoIntegrante(String tipoIntegrante) {
        this.importacaoQwareDTO.setTipoIntegrante(tipoIntegrante.equals("") ? null: Long.valueOf(tipoIntegrante));
        return this;
    }


    public ImportacaoQwareDTO build() {
        return this.importacaoQwareDTO;
    }
}
