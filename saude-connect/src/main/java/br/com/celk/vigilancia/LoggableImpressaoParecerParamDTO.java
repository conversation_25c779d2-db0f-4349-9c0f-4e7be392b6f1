package br.com.celk.vigilancia;

import java.io.Serializable;

public class LoggableImpressaoParecerParamDTO implements Serializable {

    private String tenant;
    private String usuarioLogado;
    private String numeroParecer;
    private Long codigoRequerimentoVigilanciaParecer;

    public String getTenant() {
        return tenant;
    }

    public void setTenant(String tenant) {
        this.tenant = tenant;
    }

    public String getNumeroParecer() {
        return numeroParecer;
    }

    public void setNumeroParecer(String numeroParecer) {
        this.numeroParecer = numeroParecer;
    }

    public Long getCodigoRequerimentoVigilanciaParecer() {
        return codigoRequerimentoVigilanciaParecer;
    }

    public void setCodigoRequerimentoVigilanciaParecer(Long codigoRequerimentoVigilanciaParecer) {
        this.codigoRequerimentoVigilanciaParecer = codigoRequerimentoVigilanciaParecer;
    }

    public String getUsuarioLogado() {
        return usuarioLogado;
    }

    public void setUsuarioLogado(String usuarioLogado) {
        this.usuarioLogado = usuarioLogado;
    }
}
