package br.com.celk.vigilancia.dto;

import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoDecisao;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoDecisaoProcessoAdministrativoDTO implements Serializable {

    private ProcessoAdministrativoDecisao processoAdministrativoDecisao;
    private ProcessoAdministrativo processoAdministrativo;
    private VigilanciaEndereco vigilanciaEndereco;
    private String descricaoAtividadePrincipal;
    private Profissional profissionalImpressao;

    public ProcessoAdministrativoDecisao getProcessoAdministrativoDecisao() {
        return processoAdministrativoDecisao;
    }

    public void setProcessoAdministrativoDecisao(ProcessoAdministrativoDecisao processoAdministrativoDecisao) {
        this.processoAdministrativoDecisao = processoAdministrativoDecisao;
    }

    public ProcessoAdministrativo getProcessoAdministrativo() {
        return processoAdministrativo;
    }

    public void setProcessoAdministrativo(ProcessoAdministrativo processoAdministrativo) {
        this.processoAdministrativo = processoAdministrativo;
    }

    public VigilanciaEndereco getVigilanciaEndereco() {
        return vigilanciaEndereco;
    }

    public void setVigilanciaEndereco(VigilanciaEndereco vigilanciaEndereco) {
        this.vigilanciaEndereco = vigilanciaEndereco;
    }

    public String getEnderecoFormatado() {
        StringBuilder builder = new StringBuilder();

        if (this.getVigilanciaEndereco().getRuaFormatada() != null) {
            builder.append(this.getVigilanciaEndereco().getRuaFormatada());
        }

        if (this.getProcessoAdministrativo().getEstabelecimento() != null && this.getProcessoAdministrativo().getEstabelecimento().getCodigo() != null) { //autuado é um Estabelecimento
            builder.append(", ");
            if (this.getProcessoAdministrativo().getEstabelecimento().getNumeroLogradouro() != null) {
                builder.append(this.getProcessoAdministrativo().getEstabelecimento().getNumeroLogradouro());
            } else {
                builder.append(" S/N");
            }
        } else { //autuado é pessoa
            builder.append(", ");
            if (this.getProcessoAdministrativo().getVigilanciaPessoa() != null && this.getProcessoAdministrativo().getVigilanciaPessoa().getNumeroLogradouro() != null) {
                builder.append(this.getProcessoAdministrativo().getVigilanciaPessoa().getNumeroLogradouro());
            } else {
                builder.append(" S/N");
            }
        }
        if (this.getVigilanciaEndereco().getBairro() != null) {
            builder.append(", ");
            builder.append(this.getVigilanciaEndereco().getBairro());
        }
        if (this.getVigilanciaEndereco().getCep() != null) {
            builder.append(", CEP - ");
            builder.append(this.getVigilanciaEndereco().getCepFormatado());
        }
        if (this.getVigilanciaEndereco().getCidade() != null) {
            builder.append(", ");
            builder.append(this.getVigilanciaEndereco().getCidadeFormatado());
        }
        return builder.toString();
    }

    public String getDescricaoAtividadePrincipal() {
        return descricaoAtividadePrincipal;
    }

    public void setDescricaoAtividadePrincipal(String descricaoAtividadePrincipal) {
        this.descricaoAtividadePrincipal = descricaoAtividadePrincipal;
    }

    public Profissional getProfissionalImpressao() {
        return profissionalImpressao;
    }

    public void setProfissionalImpressao(Profissional profissionalImpressao) {
        this.profissionalImpressao = profissionalImpressao;
    }
}
