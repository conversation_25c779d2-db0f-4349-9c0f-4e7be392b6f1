package br.com.celk.vigilancia.dto;

import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoFebreNiloOcidental;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoRubeolaSarampo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoRubeolaSarampoDeslocamento;

import java.io.Serializable;
import java.util.List;

public class FichaInvestigacaoAgravoFebreNiloOcidentalDTO implements Serializable {

    private RegistroAgravo registroAgravo;
    private InvestigacaoAgravoFebreNiloOcidental investigacaoAgravoFebreNiloOcidental;
    private boolean encerrarFicha;

    public RegistroAgravo getRegistroAgravo() {
        return registroAgravo;
    }

    public void setRegistroAgravo(RegistroAgravo registroAgravo) {
        this.registroAgravo = registroAgravo;
    }

    public InvestigacaoAgravoFebreNiloOcidental getInvestigacaoAgravoFebreNiloOcidental() {
        return investigacaoAgravoFebreNiloOcidental;
    }

    public void setInvestigacaoAgravoFebreNiloOcidental(InvestigacaoAgravoFebreNiloOcidental investigacaoAgravoFebreNiloOcidental) {
        this.investigacaoAgravoFebreNiloOcidental = investigacaoAgravoFebreNiloOcidental;
    }

    public boolean isEncerrarFicha() {
        return encerrarFicha;
    }

    public void setEncerrarFicha(boolean encerrarFicha) {
        this.encerrarFicha = encerrarFicha;
    }
}
