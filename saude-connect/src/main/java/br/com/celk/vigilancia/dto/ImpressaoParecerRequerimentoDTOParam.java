package br.com.celk.vigilancia.dto;

import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;

import java.io.Serializable;

public class ImpressaoParecerRequerimentoDTOParam implements Serializable {

    private Long codigoParecer;
    private String numeroParecer;
    private String urlQRcode;
    private String chaveQrcode;
    private RequerimentoVigilancia requerimentoVigilancia;

    public Long getCodigoParecer() {
        return codigoParecer;
    }

    public void setCodigoParecer(Long codigoParecer) {
        this.codigoParecer = codigoParecer;
    }

    public String getNumeroParecer() {
        return numeroParecer;
    }

    public void setNumeroParecer(String numeroParecer) {
        this.numeroParecer = numeroParecer;
    }

    public String getUrlQRcode() {
        return new StringBuilder().append(urlQRcode).append("?CHQRC=").append(getChaveQrcode()).toString();
    }

    public void setUrlQRcode(String urlQRcode) {
        this.urlQRcode = urlQRcode;
    }

    public String getChaveQrcode() {
        return chaveQrcode;
    }

    public void setChaveQrcode(String chaveQrcode) {
        this.chaveQrcode = chaveQrcode;
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }

    public void setRequerimentoVigilancia(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }

}
