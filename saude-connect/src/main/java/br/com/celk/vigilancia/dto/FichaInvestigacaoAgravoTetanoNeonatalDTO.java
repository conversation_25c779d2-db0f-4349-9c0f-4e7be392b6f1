package br.com.celk.vigilancia.dto;

import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoTetanoNeonatal;

import java.io.Serializable;
import java.util.List;

public class FichaInvestigacaoAgravoTetanoNeonatalDTO implements Serializable {

    private RegistroAgravo registroAgravo;
    private InvestigacaoAgravoTetanoNeonatal investigacaoAgravoTetanoNeonatal;
    private boolean encerrarFicha;

    public RegistroAgravo getRegistroAgravo() {
        return registroAgravo;
    }

    public void setRegistroAgravo(RegistroAgravo registroAgravo) {
        this.registroAgravo = registroAgravo;
    }

    public InvestigacaoAgravoTetanoNeonatal getInvestigacaoAgravoTetanoNeonatal() {
        return investigacaoAgravoTetanoNeonatal;
    }

    public void setInvestigacaoAgravoTetanoNeonatal(InvestigacaoAgravoTetanoNeonatal investigacaoAgravoTetanoNeonatal) {
        this.investigacaoAgravoTetanoNeonatal = investigacaoAgravoTetanoNeonatal;
    }

    public boolean isEncerrarFicha() {
        return encerrarFicha;
    }

    public void setEncerrarFicha(boolean encerrarFicha) {
        this.encerrarFicha = encerrarFicha;
    }
}
