package br.com.celk.cadsus.relatorio.pacientesconferencia;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.*;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioPacientesConferenciaDTOParam implements Serializable {

    public enum TipoCadastro {

        COMPLETO(Bundle.getStringApplication("rotulo_completo")),
        SIMPLIFICADO(Bundle.getStringApplication("rotulo_simplificado")),
        AMBOS(Bundle.getStringApplication("rotulo_ambos"));

        private TipoCadastro(String descricao) {
            this.descricao = descricao;
        }

        private final String descricao;

        @Override
        public String toString() {
            return descricao;
        }
    }

    public enum Tipo {

        PACIENTES_CADASTRADOS(Bundle.getStringApplication("rotulo_pacientes_cadastrados")),
        PACIENTES_ATENDIDOS(Bundle.getStringApplication("rotulo_pacientes_atendidos")),
        SEM_DADOS_ESUS(Bundle.getStringApplication("rotulo_sem_dados_esus"));

        private Tipo(String descricao) {
            this.descricao = descricao;
        }

        private final String descricao;

        @Override
        public String toString() {
            return descricao;
        }
    }

    private List<Empresa> empresas;
    private Tipo tipo;
    private DatePeriod periodo;
    private Empresa empresaLogada;
    private TipoCadastro tipoCadastro;
    private TipoRelatorio tipoArquivo;
    private String sexo;
    private FaixaEtaria faixaEtariaPadrao;
    private FaixaEtariaItem faixaEtaria;
    private Equipe equipe;
    private EquipeMicroArea microArea;
    private EquipeArea area;


    @DescricaoParametro("rotulo_tipo_cadastro")
    public TipoCadastro getTipoCadastro() {
        return tipoCadastro;
    }

    public void setTipoCadastro(TipoCadastro tipoCadastro) {
        this.tipoCadastro = tipoCadastro;
    }

    @DescricaoParametro("rotulo_unidade")
    public List<Empresa> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<Empresa> empresas) {
        this.empresas = empresas;
    }

    @DescricaoParametro("rotulo_tipo")
    public Tipo getTipo() {
        return tipo;
    }

    public void setTipo(Tipo tipo) {
        this.tipo = tipo;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public Empresa getEmpresaLogada() {
        return empresaLogada;
    }

    public void setEmpresaLogada(Empresa empresaLogada) {
        this.empresaLogada = empresaLogada;
    }

    public TipoRelatorio getTipoArquivo() {
        return tipoArquivo;
    }

    public void setTipoArquivo(TipoRelatorio tipoArquivo) {
        this.tipoArquivo = tipoArquivo;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public FaixaEtariaItem getFaixaEtaria() {
        return faixaEtaria;
    }

    public void setFaixaEtaria(FaixaEtariaItem faixaEtaria) {
        this.faixaEtaria = faixaEtaria;
    }

    public Equipe getEquipe() {
        return equipe;
    }

    public void setEquipe(Equipe equipe) {
        this.equipe = equipe;
    }

    public EquipeMicroArea getMicroArea() {
        return microArea;
    }

    public void setMicroArea(EquipeMicroArea microArea) {
        this.microArea = microArea;
    }

    public EquipeArea getArea() {
        return area;
    }

    public void setArea(EquipeArea area) {
        this.area = area;
    }

    public FaixaEtaria getFaixaEtariaPadrao() {
        return faixaEtariaPadrao;
    }

    public void setFaixaEtariaPadrao(FaixaEtaria faixaEtariaPadrao) {
        this.faixaEtariaPadrao = faixaEtariaPadrao;
    }
}
