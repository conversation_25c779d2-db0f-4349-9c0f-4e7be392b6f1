package br.com.celk.inovamfri.integracao.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class DispensacaoMedicamentosIntegrationDTO implements Serializable {

    private String idRegistroClient;
    private String nomePaciente;
    private String cnsPaciente;
    private String estabelecimentoDispensa;
    private String idRegistroClientEstabelecimentoDispensa;
    private String idRegistroClientEstabelecimentoOrigem;
    private String tipoReceita;
    private String dataDispensa;
    private String descricaoMedicamento;
    private String quantidade;
    private String estabelecimentoOrigem;
    private String profissionalPrescritor;
    @JsonIgnore
    private Long codigoUsuarioCadsus;
    private String unidadeMedida;

    public DispensacaoMedicamentosIntegrationDTO() {
    }

    public DispensacaoMedicamentosIntegrationDTO(String idRegistroClient, String nomePaciente, String cnsPaciente, String estabelecimentoDispensa, String tipoReceita, String dataDispensa, String descricaoMedicamento, String quantidade, String estabelecimentoOrigem, String profissionalPrescritor, Long codigoUsuarioCadsus) {
        this.idRegistroClient = idRegistroClient;
        this.nomePaciente = nomePaciente;
        this.cnsPaciente = cnsPaciente;
        this.estabelecimentoDispensa = estabelecimentoDispensa;
        this.idRegistroClientEstabelecimentoDispensa = idRegistroClientEstabelecimentoDispensa;
        this.idRegistroClientEstabelecimentoOrigem = idRegistroClientEstabelecimentoOrigem;
        this.tipoReceita = tipoReceita;
        this.dataDispensa = dataDispensa;
        this.descricaoMedicamento = descricaoMedicamento;
        this.quantidade = quantidade;
        this.estabelecimentoOrigem = estabelecimentoOrigem;
        this.profissionalPrescritor = profissionalPrescritor;
        this.codigoUsuarioCadsus = codigoUsuarioCadsus;
    }

    public String getIdRegistroClientEstabelecimentoDispensa() {
        return idRegistroClientEstabelecimentoDispensa;
    }

    public void setIdRegistroClientEstabelecimentoDispensa(String idRegistroClientEstabelecimentoDispensa) {
        this.idRegistroClientEstabelecimentoDispensa = idRegistroClientEstabelecimentoDispensa;
    }

    public String getIdRegistroClientEstabelecimentoOrigem() {
        return idRegistroClientEstabelecimentoOrigem;
    }

    public void setIdRegistroClientEstabelecimentoOrigem(String idRegistroClientEstabelecimentoOrigem) {
        this.idRegistroClientEstabelecimentoOrigem = idRegistroClientEstabelecimentoOrigem;
    }

    public String getIdRegistroClient() {
        return idRegistroClient;
    }

    public void setIdRegistroClient(String idRegistroClient) {
        this.idRegistroClient = idRegistroClient;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public String getCnsPaciente() {
        return cnsPaciente;
    }

    public void setCnsPaciente(String cnsPaciente) {
        this.cnsPaciente = cnsPaciente;
    }

    public String getEstabelecimentoDispensa() {
        return estabelecimentoDispensa;
    }

    public void setEstabelecimentoDispensa(String estabelecimentoDispensa) {
        this.estabelecimentoDispensa = estabelecimentoDispensa;
    }

    public String getTipoReceita() {
        return this.tipoReceita;
    }

    public void setTipoReceita(String tipoReceita) {
        this.tipoReceita = tipoReceita;
    }

    public String getDataDispensa() {
        return dataDispensa;
    }

    public void setDataDispensa(String dataDispensa) {
        this.dataDispensa = dataDispensa;
    }

    public String getDescricaoMedicamento() {
        return descricaoMedicamento;
    }

    public void setDescricaoMedicamento(String descricaoMedicamento) {
        this.descricaoMedicamento = descricaoMedicamento;
    }

    public String getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(String quantidade) {
        this.quantidade = quantidade;
    }

    public String getEstabelecimentoOrigem() {
        return estabelecimentoOrigem;
    }

    public void setEstabelecimentoOrigem(String estabelecimentoOrigem) {
        this.estabelecimentoOrigem = estabelecimentoOrigem;
    }

    public String getProfissionalPrescritor() {
        return profissionalPrescritor;
    }

    public void setProfissionalPrescritor(String profissionalPrescritor) {
        this.profissionalPrescritor = profissionalPrescritor;
    }

    public Long getCodigoUsuarioCadsus() {
        return codigoUsuarioCadsus;
    }

    public void setCodigoUsuarioCadsus(Long codigoUsuarioCadsus) {
        this.codigoUsuarioCadsus = codigoUsuarioCadsus;
    }

    public String getUnidadeMedida() {
        return unidadeMedida;
    }

    public void setUnidadeMedida(String unidadeMedida) {
        this.unidadeMedida = unidadeMedida;
    }

    @JsonIgnore
    @Override
    public String toString() {
        return "Código: " + getIdRegistroClient() + ", paciente: " + getNomePaciente() + ", data da dispensa: " + getDataDispensa()
                + ", medicamento: " + getDescricaoMedicamento() + ", quantidade: " + getQuantidade() + ", estabelecimento de dispensa:" + getEstabelecimentoDispensa();
    }
}
