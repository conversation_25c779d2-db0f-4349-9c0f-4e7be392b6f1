package br.com.celk.importacaoExame.build;

import br.com.celk.importacaoExame.dto.ImportacaoExameLacenDTO;
import br.com.ksisolucoes.vo.controle.Usuario;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class ImportacaoExameDTOBuild {
    private Date getDate(String data) {
        SimpleDateFormat sdf1 = new SimpleDateFormat("dd/MM/yyyy hh:mm:ss");
        try {
            return sdf1.parse(data);
        } catch (ParseException e1) {
            try {
                SimpleDateFormat sdf2 = new SimpleDateFormat("dd/MM/yyyy hh:mm");
                return sdf2.parse(data);
            } catch (ParseException e2) {
                return null;
            }
        }
    }

    public ImportacaoExameLacenDTO build(String[] valores) {
        return new ImportacaoExameDTOBuilder().setRequisicao(valores[0])
                .setPaciente(valores[1])
                .setCns(valores[2])
                .setMunicipioResidencia(valores[3])
                .setUfResidencia(valores[4])
                .setRequisitante(valores[5])
                .setMunicipioRequisitante(valores[6])
                .setExame(valores[7])
                .setMetodo(valores[8])
                .setMaterial(valores[9])
                .setAmostra(valores[10])
                .setRestricao(valores[11])
                .setLaboratorioCadastro(valores[12])
                .setDataCadastro(getDate(valores[13]))
                .setDataRecebimento(getDate(valores[14]))
                .setDataLiberacao(getDate(valores[15]))
                .setLaboratorioExecutor(valores[16])
                .setStatusExame(valores[17])
                .setResultado(valores[18])
                .build();
    }

}
