package br.com.ksisolucoes.report.agendamento.tfd.requisicaoenvioexame.dto;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.agendamento.tfd.RequisicaoEnvioExame;
import java.io.Serializable;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoComprovanteRequisicaoEnvioExameDTO implements Serializable {

    private RequisicaoEnvioExame requisicaoEnvioExame;
    private UsuarioCadsusCns usuarioCadsusCns;

    public RequisicaoEnvioExame getRequisicaoEnvioExame() {
        return requisicaoEnvioExame;
    }

    public void setRequisicaoEnvioExame(RequisicaoEnvioExame requisicaoEnvioExame) {
        this.requisicaoEnvioExame = requisicaoEnvioExame;
    }

    public UsuarioCadsusCns getUsuarioCadsusCns() {
        return usuarioCadsusCns;
    }

    public void setUsuarioCadsusCns(UsuarioCadsusCns usuarioCadsusCns) {
        this.usuarioCadsusCns = usuarioCadsusCns;
    }

    public String getNomeEmpresaRelatorio(){
        Empresa empresa = SessaoAplicacaoImp.getInstance().getEmpresa();

        if (StringUtils.trimToNull(empresa.getNomeRelatorio())!=null) {
            return empresa.getNomeRelatorio();
        }

        return empresa.getDescricao();
    }

    public String getNomeUsuario(){
        Usuario usuario = SessaoAplicacaoImp.getInstance().getUsuario();

        return usuario.getNome();
    }

    public String getObservacaoTfd() throws DAOException{
        return BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("observacaoComprovanteTFD");
    }

}
