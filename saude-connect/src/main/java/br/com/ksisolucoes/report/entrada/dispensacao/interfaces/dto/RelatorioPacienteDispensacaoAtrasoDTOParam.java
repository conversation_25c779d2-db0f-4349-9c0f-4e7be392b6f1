/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto;

import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioPacienteDispensacaoAtrasoDTOParam implements Serializable {

    public enum FormaApresentacao {
        GERAL(Bundle.getStringApplication("rotulo_geral")),
        PRODUTO(Bundle.getStringApplication("rotulo_produto")),
        DATA(Bundle.getStringApplication("rotulo_data"));

        private String label;

        private FormaApresentacao(String label) {
            this.label = label;
        }

        @Override
        public String toString() {
            return label;
        }
    }

    private OperadorValor<List<Empresa>> empresas;
    private OperadorValor<List<Produto>> produtos;
    private OperadorValor<List<UsuarioCadsus>> pacientes;
    private DatePeriod periodo;
    private FormaApresentacao formaApresentacao;

    @DescricaoParametro("rotulo_empresa")
    public OperadorValor<List<Empresa>> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(OperadorValor<List<Empresa>> empresas) {
        this.empresas = empresas;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_paciente")
    public OperadorValor<List<UsuarioCadsus>> getPacientes() {
        return pacientes;
    }

    public void setPacientes(OperadorValor<List<UsuarioCadsus>> pacientes) {
        this.pacientes = pacientes;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_produto")
    public OperadorValor<List<Produto>> getProdutos() {
        return produtos;
    }

    public void setProdutos(OperadorValor<List<Produto>> produtos) {
        this.produtos = produtos;
    }

}
