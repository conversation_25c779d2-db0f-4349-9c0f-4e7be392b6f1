package br.com.ksisolucoes.report.prontuario.exame.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import br.com.ksisolucoes.vo.prontuario.basico.RequisicaoAntiHcv;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoExameAntiHcvDTO implements Serializable {

    private EnderecoUsuarioCadsus enderecoUsuarioCadsus;
    private ExameRequisicao exameRequisicao;
    private RequisicaoAntiHcv requisicaoAntiHcv;

    public ExameRequisicao getExameRequisicao() {
        return exameRequisicao;
    }

    public void setExameRequisicao(ExameRequisicao exameRequisicao) {
        this.exameRequisicao = exameRequisicao;
    }

    public RequisicaoAntiHcv getRequisicaoAntiHcv() {
        return requisicaoAntiHcv;
    }

    public void setRequisicaoAntiHcv(RequisicaoAntiHcv requisicaoAntiHcv) {
        this.requisicaoAntiHcv = requisicaoAntiHcv;
    }

    public EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return enderecoUsuarioCadsus;
    }

    public void setEnderecoUsuarioCadsus(EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
        this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
    }
}