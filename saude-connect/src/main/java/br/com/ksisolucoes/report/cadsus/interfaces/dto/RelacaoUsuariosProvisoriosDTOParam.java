/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.cadsus.interfaces.dto;

import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelacaoUsuariosProvisoriosDTOParam implements Serializable{

    private List<Empresa> unidade;
    private String formaApresentacao;
    private String ordenacao;
    private String tipoRelatorio;
    private DatePeriod periodo;

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public String getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(String formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_ordenacao")
    public String getOrdenacao() {
        return ordenacao;
    }

    public void setOrdenacao(String ordenacao) {
        this.ordenacao = ordenacao;
    }

    @DescricaoParametro("rotulo_tipo_relatorio")
    public String getSituacao(){
        return getDescricaoSituacao(getTipoRelatorio());
    }
 
    public static String getDescricaoSituacao(String situacao){
        if(ReportProperties.SEM_CNS_APROVADO.equals(situacao)){
            return Bundle.getStringApplication("rotulo_sem_cns_aprovado");
        }else if (ReportProperties.SEM_CNS_NAO_APROVADO.equals(situacao)){
            return Bundle.getStringApplication("rotulo_sem_cns_nao_aprovados");
        }else if (ReportProperties.SEM_DOCUMENTOS.equals(situacao)){
            return Bundle.getStringApplication("rotulo_sem_documentos");
        }
        return Bundle.getStringApplication("rotulo_com_cns");
    }

    public String getTipoRelatorio() {
        return tipoRelatorio;
    }

    public void setTipoRelatorio(String tipoRelatorio) {
        this.tipoRelatorio = tipoRelatorio;
    }

    @DescricaoParametro("rotulo_unidade")
    public List<Empresa> getUnidade() {
        return unidade;
    }

    public void setUnidade(List<Empresa> unidade) {
        this.unidade = unidade;
    }

}

