/*
 * RelatorioGraficoComparativoSaidaEntradaDTO.java
 *
 * Created on 8 de Setembro de 2005, 11:11
 *
 * To change this template, choose Tools | Options and locate the template under
 * the Source Creation and Management node. Right-click the template and choose
 * Open. You can then make changes to the template in the Source Editor.
 */

package br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto;

import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Valor;
import br.com.ksisolucoes.vo.basico.Empresa;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class RelatorioGraficoDemonstrativoDispensacaoJudicialDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** Creates a new instance of RelatorioGraficoDemonstrativoDispensacaoDTO */
    public RelatorioGraficoDemonstrativoDispensacaoJudicialDTO(){}
    
    /**
     *
     * @param codigoEmpresa
     * @param descricaoEmpresa
     * @param valor
     * @param descricao
     */
    public RelatorioGraficoDemonstrativoDispensacaoJudicialDTO(Long codigoEmpresa, String referenciaEmpresa, String descricaoEmpresa, Double valor, Date periodo, String descricao){
        Empresa empresa = new Empresa(codigoEmpresa);
        empresa.setDescricao( descricaoEmpresa );
        empresa.setReferencia(referenciaEmpresa);
        
        this.setEmpresa( empresa );
        this.setDescricao(descricao);
        this.setValor(valor);
        this.setPeriodo(periodo);
    }
    
    /**
     *
     * @param periodo
     * @param quebra1
     * @param descricaoQuebra1
     * @param codigoEmpresa
     * @param descricaoEmpresa
     * @param valor
     * @param descricao
     */
    public RelatorioGraficoDemonstrativoDispensacaoJudicialDTO(Long codigoEmpresa,
            String referenciaEmpresa,
            String descricaoEmpresa,
            String quebra1,
            String descricaoQuebra1,
            Double valor,
            Date periodo,
            String descricao){
        
        this( codigoEmpresa, referenciaEmpresa, descricaoEmpresa, valor, periodo, descricao );
        
        this.setQuebra1( quebra1 );
        this.setDescricaoQuebra1( descricaoQuebra1 );
    }
    
    /**
     *
     * @param codigoEmpresa
     * @param descricaoEmpresa
     * @param valor
     * @param periodo
     */
    public RelatorioGraficoDemonstrativoDispensacaoJudicialDTO(Long codigoEmpresa, String referenciaEmpresa, String descricaoEmpresa, Double valor, Date periodo){
        this( codigoEmpresa, referenciaEmpresa, descricaoEmpresa, valor, periodo, null );
    }
    
    /**
     *
     * @param codigoEmpresa
     * @param descricaoEmpresa
     * @param quebra1
     * @param descricaoQuebra1
     * @param valor
     * @param periodo
     */
    public RelatorioGraficoDemonstrativoDispensacaoJudicialDTO(Long codigoEmpresa,
            String referenciaEmpresa,
            String descricaoEmpresa,
            String quebra1,
            String descricaoQuebra1,
            Double valor,
            Date periodo){
        
        this( codigoEmpresa, referenciaEmpresa, descricaoEmpresa, valor, periodo );
        
        this.setQuebra1( quebra1 );
        this.setDescricaoQuebra1( descricaoQuebra1 );
    }
    
    private String descricao;
    
    public String getDescricao()  {
        return descricao;
    }
    
    public void setDescricao(String descricao)  {
        
        this.descricao = descricao;
    }
    
    public String toString() {
        return this.getEmpresa().getDescricao() + "\t" + this.getDescricao() + "\t" + Data.formatar( this.getPeriodo() ) + "\t" + Valor.adicionarFormatacaoMonetaria( this.getValor() );
    }
    
    /**
     * Holds value of property empresa.
     */
    private Empresa empresa;
    
    /**
     * Getter for property empresa.
     * @return Value of property empresa.
     */
    public Empresa getEmpresa() {
        
        return this.empresa;
    }
    
    /**
     * Setter for property empresa.
     * @param empresa New value of property empresa.
     */
    public void setEmpresa(Empresa empresa) {
        
        this.empresa = empresa;
    }
    
    /**
     * Holds value of property valor.
     */
    private Double valor;
    
    /**
     * Caso haja um f definido, ser retornado o valor percentual
     * entre o valor e o valorTotal, ou seja, entende-se que o valor ser
     * percentual em relao ao total.
     * @return Value of property valor.
     */
    public Double getValor() {
        Double valor = this.valor;
        
        /*
         * AVALIACAO DE PERCENTAGEM/VALOR
         * ------------------------------
         *---------------------------------------------------------------------*/
        if ( this.valorTotal != null) {
            valor = ( 100 * Coalesce.asDouble( this.valor ).doubleValue() ) / Coalesce.asDouble( this.valorTotal, 1D ).doubleValue();
        }
        
        /*---------------------------------------------------------------------*/
        
        return valor;
    }
    
    /**
     * Setter for property valor.
     * @param valor New value of property valor.
     */
    public void setValor(Double valor) {
        
        this.valor = valor;
    }
    
    /**
     * Holds value of property valorTotal.
     */
    private Double valorTotal;
    
    /**
     * Getter for property valorTotal.
     * @return Value of property valorTotal.
     */
    public Double getValorTotal() {
        
        return this.valorTotal;
    }
    
    /**
     * Setter for property valorTotal.
     * @param valorTotal New value of property valorTotal.
     */
    public void setValorTotal(Double valorTotal) {
        
        this.valorTotal = valorTotal;
    }
    
    /**
     * Holds value of property periodo.
     */
    private Date periodo;
    
    /**
     * Getter for property periodo.
     * @return Value of property periodo.
     */
    public Date getPeriodo() {
        
        return this.periodo;
    }
    
    /**
     * Setter for property periodo.
     * @param periodo New value of property periodo.
     */
    public void setPeriodo(Date periodo) {
        
        this.periodo = periodo;
    }
    
    /**
     * Holds value of property quebra1.
     */
    private String quebra1;
    
    /**
     * Getter for property quebra1.
     * @return Value of property quebra1.
     */
    public String getQuebra1() {
        return this.quebra1;
    }
    
    /**
     * Setter for property quebra1.
     * @param quebra1 New value of property quebra1.
     */
    public void setQuebra1(String quebra1) {
        this.quebra1 = quebra1;
    }
    
    /**
     * Holds value of property descricaoQuebra1.
     */
    private String descricaoQuebra1;
    
    /**
     * Getter for property descricaoQuebra1.
     * @return Value of property descricaoQuebra1.
     */
    public String getDescricaoQuebra1() {
        return this.descricaoQuebra1;
    }
    
    /**
     * Setter for property descricaoQuebra1.
     * @param descricaoQuebra1 New value of property descricaoQuebra1.
     */
    public void setDescricaoQuebra1(String descricaoQuebra1) {
        this.descricaoQuebra1 = descricaoQuebra1;
    }
    
    
}
