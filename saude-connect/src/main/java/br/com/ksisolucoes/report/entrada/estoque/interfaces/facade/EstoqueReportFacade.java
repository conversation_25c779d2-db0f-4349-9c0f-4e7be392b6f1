/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.entrada.estoque.interfaces.facade;

import br.com.celk.provider.ejb.EJBLocation;
import br.com.ksisolucoes.bo.interfaces.FacadeBO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.*;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.materiais.estoque.ImpressaoRotuloDTOParam;
import br.com.ksisolucoes.report.materiais.pedido.interfaces.dto.RelacaoPedidoTransferenciaDTOParam;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.Inventario;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferencia;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@EJBLocation("br.com.ksisolucoes.report.entrada.estoque.EstoqueReportBO")
public interface EstoqueReportFacade extends FacadeBO {

    public DataReport relatorioAnaliseEstoque(RelatorioAnaliseEstoqueDTOParam dtoParam) throws ReportException;

    public DataReport relatorioResumoEstoque(RelatorioResumoEstoqueDTOParam dtoPram) throws ReportException;

    public DataReport relatorioRelacaoMovimentacaoDiaria(RelatorioMovimentacaoDiariaDTOParam param) throws ReportException;

    public DataReport relatorioProduto(RelatorioProdutoParam param) throws ReportException;

    public DataReport relatorioResumoConsumoProduto(RelatorioResumoConsumoProdutoDTOParam param) throws ReportException;

    public DataReport relatorioRelacaoMovimentacaoEmDeterminadaData(RelatorioRelacaoMovimentacaoEmDeterminadaDataDTOParam param) throws ReportException;

    public DataReport relatorioRelacaoProdutosUnidade(RelatorioRelacaoProdutosUnidadeDTOParam param) throws ReportException;

    public DataReport relatorioPedidoTransferencia(RelatorioPedidoTransferenciaDTOParam bean) throws ReportException;

    public DataReport relatorioPedidoTransferenciaWeb(RelatorioPedidoTransferenciaDTOParam bean) throws ReportException;

    public DataReport relatorioEmbarquePedidoTransferencia(RelatorioEmbarquePedidoTransferenciaDTOParam bean) throws ReportException;

    public DataReport relatorioEmbarquePedidoTransferenciaWeb(RelatorioEmbarquePedidoTransferenciaDTOParam bean) throws ReportException;

    public DataReport relatorioRelacaoLiberacoesReceita(RelatorioRelacaoLiberacoesReceitaParam param) throws ReportException;

    public DataReport relatorioBMPO(RelatorioBMPOParam param) throws ReportException;

    public DataReport relatorioLivroRegistroEspecifico(RelatorioLivroRegistroEspecificoDTOParam param) throws ReportException;

    public DataReport relatorioRelacaoUltimoMovimentoEstoque(RelacaoUltimoMovimentoEstoqueDTOParam param) throws ReportException;

    public DataReport relatorioRelacaoProduto(RelatorioRelacaoProdutoDTOParam param) throws ReportException;

    public DataReport relatorioRelacaoEstoqueMinimo(RelatorioRelacaoEstoqueMinimoDTOParam param) throws ReportException;

    public DataReport relatorioMovimentacaoEstoque(RelatorioMovimentacaoEstoqueParam param) throws ReportException;

    public DataReport relatorioConsumoProduto(RelatorioConsumoProdutoDTOParam param) throws ReportException;

    public DataReport relatorioControleInventario(RelatorioControleInventarioDTOParam param) throws ReportException;

    public DataReport relatorioDivergenciaInventario(RelatorioRelacaoDivergenciaInventarioDTOParam param) throws ReportException;

    public DataReport relatorioRelacaoInventario(RelatorioRelacaoInventarioDTOParam param) throws ReportException;

    public DataReport relatorioProdutosInventarioContagem(Inventario param) throws ReportException;

    public DataReport relatorioRelacaoPerdas(RelatorioRelacaoPerdasDTOParam param) throws ReportException;

    public DataReport relatorioDemonstrativoProduto(RelatorioDemonstrativoProdutoDTOParam param) throws ReportException;

    public void confirmarImpressaoPedidoTransferencia(List<Empresa> empresasDestino, Long numeroPedido) throws DAOException, ValidacaoException;

    public void confirmarImpressaoPedidoTransferencia(Empresa empresa, Long numeroPedido) throws DAOException, ValidacaoException;

    public void confirmarImpressaoPedidoTransferencia(PedidoTransferencia pedidoTransferencia) throws DAOException, ValidacaoException;

    public void confirmarImpressaoEmbarquePedidoTransferencia(List<Long> pedidoTransferenciaList) throws DAOException, ValidacaoException;

    public DataReport relatorioRelacaoPedidoTransferencia(RelacaoPedidoTransferenciaDTOParam param) throws ReportException;

    public DataReport relatorioResumoPedidoEstoque(QueryRelatorioResumoPedidoEstoqueDTOParam param) throws ReportException;

    public DataReport relatorioPedidosDivergenciaEntrega(QueryRelatorioPedidosDivergenciaEntregaDTOParam param) throws ReportException;

    public DataReport relacaoProdutosPrograma(RelacaoProdutosProgramaDTOParam param) throws ReportException;

    public DataReport relatorioListagemProdutos(RelatorioListagemProdutosDTOParam param) throws ReportException;

    public DataReport relatorioPedidosEnviadosSeparacao(RelatorioPedidosEnviadosSeparacaoDTOParam param) throws ReportException;

    public DataReport relatorioPedidosEnviadosSeparacaoSync(RelatorioPedidosEnviadosSeparacaoDTOParam param) throws ReportException;

    public DataReport relatorioSeparacaoPedido(Long codigoPedido) throws ReportException;

    public DataReport relatorioValorizacaoEstoque(RelatorioValorizacaoEstoqueDTOParam param) throws ReportException;

    public DataReport graficoValorizacaoEstoque(GraficoValorizacaoEstoqueDTOParam param) throws ReportException;

    public DataReport relatorioProdutosVencendo(QueryRelatorioProdutosVencendoDTOParam param) throws ReportException;

    public DataReport graficoProdutosVencendo(QueryGraficoProdutosVencendoDTOParam param) throws ReportException;

    public DataReport relatorioGiroEstoque(RelatorioGiroEstoqueDTOParam dtoPram) throws ReportException;

    public DataReport relatorioResumoSaldoEstoque(RelatorioResumoSaldoEstoqueDTOParam dtoParam) throws ReportException;

    public DataReport relatorioConfiguracaoEstoqueMinimo(RelatorioRelacaoEstoqueMinimoDTOParam param) throws ReportException;

    public DataReport relatorioProdutosValidadeVencidaVencerAsync(QueryProdutosValidadeVencidaVencerDTOParam param) throws ReportException;

    public DataReport relatorioProdutosValidadeVencidaVencer(QueryProdutosValidadeVencidaVencerDTOParam param) throws ReportException;

    public DataReport relatorioImpressaoEtiquetaProduto(ImpressaoEtiquetaDTOParam param) throws ReportException;

    public DataReport relatorioImpressaoEtiquetaProdutoNfEntrada( ImpressaoEtiquetaNfEntradaDTOParam param) throws ReportException;

    public DataReport relatorioImpressaoEtiquetaVolume(ImpressaoEtiquetaVolumeDTOParam param) throws ReportException;

    public DataReport relatorioResumoSaldoDivergente(List<RelatorioResumoSaldoEstoqueDTO> lstDivergencia) throws ReportException;

    public DataReport relatorioPrevisaoEstoque(RelatorioPrevisaoEstoqueDTOParam param) throws ReportException;

    public DataReport relatorioImpressaoReciboLancamentoEstoque(ImpressaoReciboLancamentoEstoqueDTOParam param) throws ReportException;

    public DataReport relatorioImpressaoRotulo(ImpressaoRotuloDTOParam param) throws ReportException;

    public DataReport relatorioImpressaoEtiquetaEmbarque(ImpressaoEtiquetaEmbarqueDTOParam param) throws ReportException;

    public DataReport relatorioClassificacaoEstoque(RelatorioClassificacaoEstoqueDTOParam param) throws ReportException;
}
