package br.com.ksisolucoes.report.entrada.estoque.interfaces.dto;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.CentroCusto;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Localizacao;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class RelatorioResumoEstoqueDTOParam implements Serializable {

        private OperadorValor<List<Empresa>> lstEmpresa;
        private OperadorValor<List<Empresa>> lstEmpresaDestino;
        private OperadorValor<List<Produto>> lstProduto;
        private OperadorValor<List<GrupoProduto>> lstGrupoProduto;
        private SubGrupo subGrupo;
        private OperadorValor<List<TipoDocumento>> lstTipoDocumento;
        private OperadorValor<List<Localizacao>> lstLocalizacao;
        private OperadorValor<List<CentroCusto>> lstCentroCusto;
        private Date dataComeco;
        private Date dataFim;
        private Long tipoRelatorio;
        private Long formaApresentacao;
        private Long tipo;
        private String ordenacao;
        private String agruparEmpresa;
        private GrupoProduto grupoProdutoSubGrupo;
        private String tipoMovimentacao;
        private DatePeriod periodo;
        private TipoRelatorio tipoArquivo;
        private RepositoryComponentDefault.TipoPreco tipoPreco;


    public TipoRelatorio getTipoArquivo() {
        return tipoArquivo;
    }

    public void setTipoArquivo(TipoRelatorio tipoArquivo) {
        this.tipoArquivo = tipoArquivo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
        if (this.periodo.getDataFinal() != null && this.periodo.getDataInicial() != null) {
            setDataComeco(this.periodo.getDataInicial());
            setDataFim(this.periodo.getDataFinal());
        }
    }

    public GrupoProduto getGrupoProdutoSubGrupo() {
        return grupoProdutoSubGrupo;
    }

    public void setGrupoProdutoSubGrupo(GrupoProduto grupoProdutoSubGrupo) {
        this.grupoProdutoSubGrupo = grupoProdutoSubGrupo;
    }


    @DescricaoParametro("rotulo_agrupar_empresa")
    public String getAgruparEmpresa() {
        return agruparEmpresa;
    }

    public void setAgruparEmpresa(String agruparEmpresa) {
        this.agruparEmpresa = agruparEmpresa;
    }

    public Date getDataComeco() {
        return this.dataComeco;
    }

    public void setDataComeco(Date dataComeco) {
        this.dataComeco = dataComeco;
    }

    public Date getDataFim() {
        return this.dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Long getFormaApresentacao() {
        return this.formaApresentacao;
    }

    public String getFormaApresentacaoFormatado() {
        if (getFormaApresentacao().intValue() == ReportProperties.AGRUPAR_GRUPO){
            return Bundle.getStringApplication( "rotulo_grupo" );
        }else{
            return Bundle.getStringApplication( "rotulo_geral" );
        }
    }

    public void setFormaApresentacao(Long formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_empresa")
    public OperadorValor<List<Empresa>> getLstEmpresa() {
        return lstEmpresa;
    }

    public void setLstEmpresa(OperadorValor<List<Empresa>> lstEmpresa) {
        this.lstEmpresa = lstEmpresa;
    }

    @DescricaoParametro("rotulo_empresa_destino")
    public OperadorValor<List<Empresa>> getLstEmpresaDestino() {
        return lstEmpresaDestino;
    }

    public void setLstEmpresaDestino(OperadorValor<List<Empresa>> lstEmpresaDestino) {
        this.lstEmpresaDestino = lstEmpresaDestino;
    }

    @DescricaoParametro("rotulo_grupo")
    public OperadorValor<List<GrupoProduto>> getLstGrupoProduto() {
        return lstGrupoProduto;
    }

    public void setLstGrupoProduto(OperadorValor<List<GrupoProduto>> lstGrupoProduto) {
        this.lstGrupoProduto = lstGrupoProduto;
    }

    @DescricaoParametro("rotulo_localizacao")
    public OperadorValor<List<Localizacao>> getLstLocalizacao() {
        return lstLocalizacao;
    }

    public void setLstLocalizacao(OperadorValor<List<Localizacao>> lstLocalizacao) {
        this.lstLocalizacao = lstLocalizacao;
    }

    @DescricaoParametro("rotulo_produto")
    public OperadorValor<List<Produto>> getLstProduto() {
        return lstProduto;
    }

    public void setLstProduto(OperadorValor<List<Produto>> lstProduto) {
        this.lstProduto = lstProduto;
    }

    @DescricaoParametro("rotulo_tipo_documento")
    public OperadorValor<List<TipoDocumento>> getLstTipoDocumento() {
        return lstTipoDocumento;
    }

    public void setLstTipoDocumento(OperadorValor<List<TipoDocumento>> lstTipoDocumento) {
        this.lstTipoDocumento = lstTipoDocumento;
    }

    public String getOrdenacao() {
        return this.ordenacao;
    }

    @DescricaoParametro("rotulo_ordenacao")
    public String getOrdenacaoFormatado() {
        if(getOrdenacao().equals(Produto.PROP_CODIGO)){
            return Bundle.getStringApplication( "rotulo_codigo" );
        }else{
            return Bundle.getStringApplication( "rotulo_descricao" );
        }
    }

    public void setOrdenacao(String ordenacao) {
        this.ordenacao = ordenacao;
    }

    @DescricaoParametro("rotulo_subgrupo")
    public SubGrupo getSubGrupo() {
        return this.subGrupo;
    }

    public void setSubGrupo(SubGrupo subGrupo) {
        this.subGrupo = subGrupo;
    }

    public Long getTipo() {
        return this.tipo;
    }

    @DescricaoParametro("rotulo_avalia_tipo_documento")
    public String getTipoFormatado() {
        if (getTipo().intValue() == ReportProperties.TIPO_SIM){
            return Bundle.getStringApplication( "rotulo_sim" );
        }else{
            return Bundle.getStringApplication( "rotulo_nao" );
        }
    }

    public void setTipo(Long tipo) {
        this.tipo = tipo;
    }

    public Long getTipoRelatorio() {
        return this.tipoRelatorio;
    }

    @DescricaoParametro("rotulo_tipo_relatorio")
    public String getTipoRelatorioFormatado() {
        if(getTipoRelatorio().intValue() == ReportProperties.RESUMIDO){
            return Bundle.getStringApplication( "rotulo_resumido" );
        }else if(getTipoRelatorio().intValue() == ReportProperties.DETALHADO){
            return Bundle.getStringApplication( "rotulo_detalhado" );
        }
        return "";
    }

    public void setTipoRelatorio(Long tipoRelatorio) {
        this.tipoRelatorio = tipoRelatorio;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo(){
        return new DatePeriod(dataComeco, dataFim);
    }

    @DescricaoParametro("rotulo_centro_custo")
    public OperadorValor<List<CentroCusto>> getLstCentroCusto() {
        return lstCentroCusto;
    }

    public void setLstCentroCusto(OperadorValor<List<CentroCusto>> lstCentroCusto) {
        this.lstCentroCusto = lstCentroCusto;
    }

    @DescricaoParametro("rotulo_tipo_movimentacao")
    public String getTipoMovimentacao() {
        return tipoMovimentacao;
    }

    public void setTipoMovimentacao(String tipoMovimentacao) {
        this.tipoMovimentacao = tipoMovimentacao;
    }

    @DescricaoParametro("rotulo_tipo_preco")
    public RepositoryComponentDefault.TipoPreco getTipoPreco() {
        return tipoPreco;
    }

    public void setTipoPreco(RepositoryComponentDefault.TipoPreco tipoPreco) {
        this.tipoPreco = tipoPreco;
    }
}
