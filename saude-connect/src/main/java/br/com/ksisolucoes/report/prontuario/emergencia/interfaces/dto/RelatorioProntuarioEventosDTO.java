package br.com.ksisolucoes.report.prontuario.emergencia.interfaces.dto;

import java.io.Serializable;
import java.util.Date;

public class RelatorioProntuarioEventosDTO implements Serializable {

    public RelatorioProntuarioEventosDTO(Long numeroAtendimento, String classificacao, String tipoAtendimento, Date data, Long empresaAtendimento, String descricao, Long usuarioCadSus, String empresaAtendimentoDescricao, String usuarioCadSusNome) {
        super();
        this.numeroAtendimento = numeroAtendimento;
        this.classificacao = classificacao;
        this.tipoAtendimento = tipoAtendimento;
        this.data = data;
        this.empresaAtendimento = empresaAtendimento;
        this.descricao = descricao;
        this.usuarioCadSus = usuarioCadSus;
        this.empresaAtendimentoDescricao = empresaAtendimentoDescricao;
        this.usuarioCadSusNome = usuarioCadSusNome;
    }
    /**
     * Holds value of property numeroAtendimento.
     */
    private Long numeroAtendimento;

    /**
     * Getter for property numeroAtendimento.
     * @return Value of property numeroAtendimento.
     */
    public Long getNumeroAtendimento() {
        return this.numeroAtendimento;
    }

//        public String getDescricaoTipoAtendimento() {
//            return ProntuarioEventos.getDescricaoTipoAtendimento( this.getTipoAtendimento() );
//        }
    /**
     * Setter for property numeroAtendimento.
     * @param numeroAtendimento New value of property numeroAtendimento.
     */
    public void setNumeroAtendimento(Long numeroAtendimento) {
        this.numeroAtendimento = numeroAtendimento;
    }
    /**
     * Holds value of property classificacao.
     */
    private String classificacao;

    /**
     * Getter for property classificacao.
     * @return Value of property classificacao.
     */
    public String getClassificacao() {
        return this.classificacao;
    }

    /**
     * Setter for property classificacao.
     * @param classificacao New value of property classificacao.
     */
    public void setClassificacao(String classificacao) {
        this.classificacao = classificacao;
    }
    /**
     * Holds value of property tipoAtendimento.
     */
    private String tipoAtendimento;

    /**
     * Getter for property tipoAtendimento.
     * @return Value of property tipoAtendimento.
     */
    public String getTipoAtendimento() {
        return this.tipoAtendimento;
    }

    /**
     * Setter for property tipoAtendimento.
     * @param tipoAtendimento New value of property tipoAtendimento.
     */
    public void setTipoAtendimento(String tipoAtendimento) {
        this.tipoAtendimento = tipoAtendimento;
    }
    /**
     * Holds value of property data.
     */
    private Date data;

    /**
     * Getter for property data.
     * @return Value of property data.
     */
    public Date getData() {
        return this.data;
    }

    /**
     * Setter for property data.
     * @param data New value of property data.
     */
    public void setData(Date data) {
        this.data = data;
    }
    /**
     * Holds value of property empresaAtendimento.
     */
    private Long empresaAtendimento;

    /**
     * Getter for property empresaAtendimento.
     * @return Value of property empresaAtendimento.
     */
    public Long getEmpresaAtendimento() {
        return this.empresaAtendimento;
    }

    /**
     * Setter for property empresaAtendimento.
     * @param empresaAtendimento New value of property empresaAtendimento.
     */
    public void setEmpresaAtendimento(Long empresaAtendimento) {
        this.empresaAtendimento = empresaAtendimento;
    }
    /**
     * Holds value of property descricao.
     */
    private String descricao;

    /**
     * Getter for property descricao.
     * @return Value of property descricao.
     */
    public String getDescricao() {
        return this.descricao;
    }

    /**
     * Setter for property descricao.
     * @param descricao New value of property descricao.
     */
    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
    /**
     * Holds value of property usuarioCadSus.
     */
    private Long usuarioCadSus;

    /**
     * Getter for property usuarioCadSus.
     * @return Value of property usuarioCadSus.
     */
    public Long getUsuarioCadSus() {
        return this.usuarioCadSus;
    }

    /**
     * Setter for property usuarioCadSus.
     * @param usuarioCadSus New value of property usuarioCadSus.
     */
    public void setUsuarioCadSus(Long usuarioCadSus) {
        this.usuarioCadSus = usuarioCadSus;
    }
    private String usuarioCadSusNome;

    public String getUsuarioCadSusNome() {
        return this.usuarioCadSusNome;
    }

    public void setUsuarioCadSusNome(String usuarioCadSusNome) {
        this.usuarioCadSusNome = usuarioCadSusNome;
    }
    private String empresaAtendimentoDescricao;

    public String getempresaAtendimentoDescricao() {
        return this.empresaAtendimentoDescricao;
    }

    public void setempresaAtendimentoDescricao(String empresaAtendimentoDescricao) {
        this.empresaAtendimentoDescricao = empresaAtendimentoDescricao;
    }
}
