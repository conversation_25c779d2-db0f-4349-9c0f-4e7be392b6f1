/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.entrada.estoque.interfaces.dto;

import br.com.ksisolucoes.report.util.MapBean;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelacaoUltimoMovimentoEstoqueDTOParam implements Serializable{

    private List empresa;
    private List produto;
    private List localizacao;
    private String ordenacao;
    private MapBean curva;
    private Date dataBase;
    private String tipoMovimento;

    /**
     * @return the empresa
     */
    public List getEmpresa() {
        return empresa;
    }

    /**
     * @param empresa the empresa to set
     */
    public void setEmpresa(List empresa) {
        this.empresa = empresa;
    }

    /**
     * @return the produto
     */
    public List getProduto() {
        return produto;
    }

    /**
     * @param produto the produto to set
     */
    public void setProduto(List produto) {
        this.produto = produto;
    }

    /**
     * @return the localizacao
     */
    public List getLocalizacao() {
        return localizacao;
    }

    /**
     * @param localizacao the localizacao to set
     */
    public void setLocalizacao(List localizacao) {
        this.localizacao = localizacao;
    }

    /**
     * @return the ordenacao
     */
    public String getOrdenacao() {
        return ordenacao;
    }

    /**
     * @param ordenacao the ordenacao to set
     */
    public void setOrdenacao(String ordenacao) {
        this.ordenacao = ordenacao;
    }

    /**
     * @return the curva
     */
    public MapBean getCurva() {
        return curva;
    }

    /**
     * @param curva the curva to set
     */
    public void setCurva(MapBean curva) {
        this.curva = curva;
    }

    /**
     * @return the dataBase
     */
    public Date getDataBase() {
        return dataBase;
    }

    /**
     * @param dataBase the dataBase to set
     */
    public void setDataBase(Date dataBase) {
        this.dataBase = dataBase;
    }

    /**
     * @return the tipoMovimento
     */
    public String getTipoMovimento() {
        return tipoMovimento;
    }

    /**
     * @param tipoMovimento the tipoMovimento to set
     */
    public void setTipoMovimento(String tipoMovimento) {
        this.tipoMovimento = tipoMovimento;
    }

}
