package br.com.ksisolucoes.report.consorcio.dto;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioSaldoOrcamentarioDTO implements Serializable{
 
    private Long ano;
    private Double repasse;
    private Double orcamento;
    private String descricaoEmpresa;
    private String descricaoTipoConta;

    public String getDescricaoTipoConta() {
        return descricaoTipoConta;
    }

    public void setDescricaoTipoConta(String descricaoTipoConta) {
        this.descricaoTipoConta = descricaoTipoConta;
    }
    
    public String getDescricaoEmpresa() {
        return descricaoEmpresa;
    }

    public void setDescricaoEmpresa(String descricaoEmpresa) {
        this.descricaoEmpresa = descricaoEmpresa;
    }

    public Long getAno() {
        return ano;
    }

    public void setAno(Long ano) {
        this.ano = ano;
    }

    public Double getRepasse() {
        return repasse;
    }

    public void setRepasse(Double repasse) {
        this.repasse = repasse;
    }

    public Double getOrcamento() {
        return orcamento;
    }

    public void setOrcamento(Double orcamento) {
        this.orcamento = orcamento;
    }
    
}
