package br.com.ksisolucoes.report.hospital.interfaces.dto;

import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioImpressaoProntuarioDTO;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.basico.OrgaoEmissor;
import br.com.ksisolucoes.vo.cadsus.*;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoProntuario;
import br.com.ksisolucoes.vo.prontuario.basico.ClassificacaoRisco;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;
import br.com.ksisolucoes.vo.prontuario.hospital.OcorrenciaAih;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class RelatorioImpressaoEspelhoAIHDTO extends Aih implements Serializable, Comparable<RelatorioImpressaoEspelhoAIHDTO> {

    private OcorrenciaAih ocorrenciaAih;
    private AtendimentoProntuario atendimentoProntuario;
    private Aih aih;
    private OrgaoEmissor conselhoClasse;
    private Long codigo;
    private UsuarioCadsus usuarioCadsus;
    private UsuarioCadsusCns usuarioCadsusCns;
    private Raca raca;
    private EtniaIndigena etniaIndigena;
    private EnderecoUsuarioCadsus enderecoUsuarioCadsus;
    private Cidade cidade;
    private Estado estado;
    private Profissional profissional;
    private SolicitacaoAgendamento solicitacaoAgendamento;
    private ClassificacaoRisco classificacaoRisco;
    private Empresa unidadeExecutante;
    private LeitoQuarto leitoQuarto;
    private String descricaoProntuario;
    private Date dataCadastroProntuario;
    private Date dataCadastro;
    private Empresa usuarioEmpresaProntuario;
    private String usuarioCadastroProntuario;
    private String usuarioCadastroRegistroProntuario;
    private String usuarioCadastroRegistroOcorrencia;
    private RelatorioImpressaoProntuarioDTO relatorioImpressaoProntuarioDTO;
    private Usuario usuarioCadastro;
    private String usuarioEmpresas;
    private String ocorrenciaDescricao;

    public OrgaoEmissor getConselhoClasse() {
        return conselhoClasse;
    }

    public void setConselhoClasse(OrgaoEmissor conselhoClasse) {
        this.conselhoClasse = conselhoClasse;
    }

    public String getUsuarioCadastroRegistroOcorrencia() {
        return usuarioCadastroRegistroOcorrencia;
    }

    public void setUsuarioCadastroRegistroOcorrencia(String usuarioCadastroRegistroOcorrencia) {
        this.usuarioCadastroRegistroOcorrencia = usuarioCadastroRegistroOcorrencia;
    }

    public OcorrenciaAih getOcorrenciaAih() {
        return ocorrenciaAih;
    }

    public void setOcorrenciaAih(OcorrenciaAih ocorrenciaAih) {
        this.ocorrenciaAih = ocorrenciaAih;
    }

    public AtendimentoProntuario getAtendimentoProntuario() {
        return atendimentoProntuario;
    }

    public void setAtendimentoProntuario(AtendimentoProntuario atendimentoProntuario) {
        this.atendimentoProntuario = atendimentoProntuario;
    }

    public Aih getAih() {
        return aih;
    }

    public void setAih(Aih aih) {
        this.aih = aih;
    }

    @Override
    public Long getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public UsuarioCadsusCns getUsuarioCadsusCns() {
        return usuarioCadsusCns;
    }

    public void setUsuarioCadsusCns(UsuarioCadsusCns usuarioCadsusCns) {
        this.usuarioCadsusCns = usuarioCadsusCns;
    }

    public Raca getRaca() {
        return raca;
    }

    public void setRaca(Raca raca) {
        this.raca = raca;
    }

    public EtniaIndigena getEtniaIndigena() {
        return etniaIndigena;
    }

    public void setEtniaIndigena(EtniaIndigena etniaIndigena) {
        this.etniaIndigena = etniaIndigena;
    }

    public EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return enderecoUsuarioCadsus;
    }

    public void setEnderecoUsuarioCadsus(EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
        this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
    }

    public Cidade getCidade() {
        return cidade;
    }

    public void setCidade(Cidade cidade) {
        this.cidade = cidade;
    }

    public Estado getEstado() {
        return estado;
    }

    public void setEstado(Estado estado) {
        this.estado = estado;
    }

    @Override
    public Profissional getProfissional() {
        return profissional;
    }

    @Override
    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    @Override
    public SolicitacaoAgendamento getSolicitacaoAgendamento() {
        return solicitacaoAgendamento;
    }

    @Override
    public void setSolicitacaoAgendamento(SolicitacaoAgendamento solicitacaoAgendamento) {
        this.solicitacaoAgendamento = solicitacaoAgendamento;
    }

    @Override
    public ClassificacaoRisco getClassificacaoRisco() {
        return classificacaoRisco;
    }

    @Override
    public void setClassificacaoRisco(ClassificacaoRisco classificacaoRisco) {
        this.classificacaoRisco = classificacaoRisco;
    }

    public Empresa getUnidadeExecutante() {
        return unidadeExecutante;
    }

    public void setUnidadeExecutante(Empresa unidadeExecutante) {
        this.unidadeExecutante = unidadeExecutante;
    }

    @Override
    public LeitoQuarto getLeitoQuarto() {
        return leitoQuarto;
    }

    @Override
    public void setLeitoQuarto(LeitoQuarto leitoQuarto) {
        this.leitoQuarto = leitoQuarto;
    }

    public String getDescricaoProntuario() {
        return descricaoProntuario;
    }

    public void setDescricaoProntuario(String descricaoProntuario) {
        this.descricaoProntuario = descricaoProntuario;
    }

    public Date getDataCadastroProntuario() {
        return dataCadastroProntuario;
    }

    public void setDataCadastroProntuario(Date dataCadastroProntuario) {
        this.dataCadastroProntuario = dataCadastroProntuario;
    }

    @Override
    public Date getDataCadastro() {
        return dataCadastro;
    }

    @Override
    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Empresa getUsuarioEmpresaProntuario() {
        return usuarioEmpresaProntuario;
    }

    public void setUsuarioEmpresaProntuario(Empresa usuarioEmpresaProntuario) {
        this.usuarioEmpresaProntuario = usuarioEmpresaProntuario;
    }

    public String getUsuarioCadastroProntuario() {
        return usuarioCadastroProntuario;
    }

    public void setUsuarioCadastroProntuario(String usuarioCadastroProntuario) {
        this.usuarioCadastroProntuario = usuarioCadastroProntuario;
    }

    public String getUsuarioCadastroRegistroProntuario() {
        return usuarioCadastroRegistroProntuario;
    }

    public void setUsuarioCadastroRegistroProntuario(String usuarioCadastroRegistroProntuario) {
        this.usuarioCadastroRegistroProntuario = usuarioCadastroRegistroProntuario;
    }

    public RelatorioImpressaoProntuarioDTO getRelatorioImpressaoProntuarioDTO() {
        return relatorioImpressaoProntuarioDTO;
    }

    public void setRelatorioImpressaoProntuarioDTO(RelatorioImpressaoProntuarioDTO relatorioImpressaoProntuarioDTO) {
        this.relatorioImpressaoProntuarioDTO = relatorioImpressaoProntuarioDTO;
    }

    @Override
    public Usuario getUsuarioCadastro() {
        return usuarioCadastro;
    }

    @Override
    public void setUsuarioCadastro(Usuario usuarioCadastro) {
        this.usuarioCadastro = usuarioCadastro;
    }

    public String getUsuarioEmpresas() {
        return usuarioEmpresas;
    }

    public void setUsuarioEmpresas(String usuarioEmpresas) {
        this.usuarioEmpresas = usuarioEmpresas;
    }

    public String getOcorrenciaDescricao() {
        return ocorrenciaDescricao;
    }

    public void setOcorrenciaDescricao(String ocorrenciaDescricao) {
        this.ocorrenciaDescricao = ocorrenciaDescricao;
    }

    @Override
    public int compareTo(RelatorioImpressaoEspelhoAIHDTO ordenaLista) {
        Date ocorrencia = this.getDataCadastro();
        Date ordOcorrencia = ordenaLista.getDataCadastro();
        Date prontuario = this.getDataCadastroProntuario();
        Date ordProntuario = ordenaLista.getDataCadastroProntuario();

        if ((ordOcorrencia != null) && (ocorrencia != null)) {
            return ocorrencia.compareTo(ordOcorrencia);
        } else if ((ocorrencia != null) && (prontuario != null)) {
            return prontuario.compareTo(prontuario);
        } else if ((prontuario != null) && (ordOcorrencia != null)) {
            return prontuario.compareTo(ordOcorrencia);
        } else if ((ordProntuario != null) && (prontuario != null)) {
            return prontuario.compareTo(ordProntuario);
        } else if ((ocorrencia != null) && (ordProntuario != null)) {
            return ocorrencia.compareTo(ordProntuario);
        } else if ((ordOcorrencia != null) && (ordProntuario != null)) {
            return ordOcorrencia.compareTo(ordProntuario);
        }
        return 0;
    }

}

