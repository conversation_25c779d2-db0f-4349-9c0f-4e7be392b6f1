package br.com.ksisolucoes.report.vacina.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.vacina.TipoVacina;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;

import java.awt.*;
import java.io.Serializable;
import java.util.Date;


public class RelatorioCertificadoVacinacaoDigitalDTO implements Serializable {

    private String cpf;
    private Long cns;
    private Date dataNascimento;
    private String dataNascimentoFormatado;
    private String nomeVacinado;
    private String nacionalidade;
    private String sexo;
    private Date dataEmissao;
    private UsuarioCadsus paciente;
    private VacinaAplicacao vacinaAplicacao;
    private Profissional profissional;
    private Empresa empresa;
    private TipoVacina tipoVacina;
    private String laboratorio;

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public Long getCns() {
        return cns;
    }

    public void setCns(Long cns) {
        this.cns = cns;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getNomeVacinado() {
        return nomeVacinado;
    }

    public void setNomeVacinado(String nomeVacinado) {
        this.nomeVacinado = nomeVacinado;
    }

    public String getNacionalidade() {
        return nacionalidade;
    }

    public void setNacionalidade(String nacionalidade) {
        this.nacionalidade = nacionalidade;
    }

    public Date getDataEmissao() {
        return dataEmissao;
    }

    public void setDataEmissao(Date dataEmissao) {
        this.dataEmissao = dataEmissao;
    }

    public String getDataNascimentoFormatado() {
        return dataNascimentoFormatado;
    }

    public void setDataNascimentoFormatado(String dataNascimentoFormatado) {
        this.dataNascimentoFormatado = dataNascimentoFormatado;
    }

    public VacinaAplicacao getVacinaAplicacao() {
        return vacinaAplicacao;
    }

    public void setVacinaAplicacao(VacinaAplicacao vacinaAplicacao) {
        this.vacinaAplicacao = vacinaAplicacao;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public TipoVacina getTipoVacina() {
        return tipoVacina;
    }

    public void setTipoVacina(TipoVacina tipoVacina) {
        this.tipoVacina = tipoVacina;
    }

    public UsuarioCadsus getPaciente() {
        return paciente;
    }

    public void setPaciente(UsuarioCadsus paciente) {
        this.paciente = paciente;
    }

    public String getLaboratorio() {
        return laboratorio;
    }

    public void setLaboratorio(String laboratorio) {
        this.laboratorio = laboratorio;
    }
}
