/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.agendamento.exame.dto;

import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelacaoVagasAgendaDTOParam implements Serializable {


    private OperadorValor<List<TipoProcedimento>> tipoProcedimentos;
    private List<ExameProcedimento> exameProcedimentoList;
    private OperadorValor<List<Empresa>> empresas;
    private DatePeriod datePeriodo;
    private Long exibirProfissional;

    @DescricaoParametro("rotulo_exibir_profissional")
    public Long getExibirProfissional() {
        return exibirProfissional;
    }

    public void setExibirProfissional(Long exibirProfissional) {
        this.exibirProfissional = exibirProfissional;
    }

    @DescricaoParametro("rotulo_tipo_procedimento")
    public OperadorValor<List<TipoProcedimento>> getTipoProcedimentos() {
        return tipoProcedimentos;
    }

    public void setTipoProcedimentos(OperadorValor<List<TipoProcedimento>> tipoProcedimentos) {
        this.tipoProcedimentos = tipoProcedimentos;
    }

    @DescricaoParametro("rotulo_exame")
    public List<ExameProcedimento> getExameProcedimentoList() {
        return exameProcedimentoList;
    }

    public void setExameProcedimentoList(List<ExameProcedimento> exameProcedimentoList) {
        this.exameProcedimentoList = exameProcedimentoList;
    }

    @DescricaoParametro("rotulo_unidade_executante")
    public OperadorValor<List<Empresa>> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(OperadorValor<List<Empresa>> empresas) {
        this.empresas = empresas;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getDatePeriodo() {
        return datePeriodo;
    }

    public void setDatePeriodo(DatePeriod datePeriodo) {
        this.datePeriodo = datePeriodo;
    }

}
