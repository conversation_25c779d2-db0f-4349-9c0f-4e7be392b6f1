package br.com.ksisolucoes.report.agendamento.exame.dto;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.EquipeArea;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */

public class RelacaoPacientesSemAderenciaDTOParam implements Serializable {


    private TipoRelatorio tipoArquivo;
    private EquipeArea equipeArea;
    private String sixIndicator;
    private String indicatorFive;

    public String getSixIndicator() {
        return sixIndicator;
    }

    public void setSixIndicator(String sixIndicator) {
        this.sixIndicator = sixIndicator;
    }
    private String indicador7;
    private String indicatorFour;

    public TipoRelatorio getTipoArquivo() {
        return tipoArquivo;
    }

    public void setTipoArquivo(TipoRelatorio tipoArquivo) {
        this.tipoArquivo = tipoArquivo;
    }

    @DescricaoParametro("rotulo_area")
    public EquipeArea getEquipeArea() {
        return equipeArea;
    }

    public void setEquipeArea(EquipeArea equipeArea) {
        this.equipeArea = equipeArea;
    }

    public String getIndicador7() {
        return indicador7;
    }

    public void setIndicador7(String indicador7) {
        this.indicador7 = indicador7;
    }

    public String getIndicatorFour() { return indicatorFour;}

    public void setIndicatorFour(String indicatorFour)  {this.indicatorFour = indicatorFour;}

    public String getIndicatorFive() {
        return indicatorFive;
    }

    public void setIndicatorFive(String indicatorFive) {
        this.indicatorFive = indicatorFive;
    }
}
