package br.com.ksisolucoes.report.prontuario.exame.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.*;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import br.com.ksisolucoes.vo.prontuario.basico.RequisicaoCargaViralHIV;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoExameCargaViralHIVDTO implements Serializable {

    private EnderecoUsuarioCadsus enderecoUsuarioCadsus;
    private UsuarioCadsusCns usuarioCadsusCns;
    private UsuarioCadsusDado usuarioCadsusDado;
    private ExameRequisicao exameRequisicao;
    private UsuarioCadsus usuarioCadsus;
    private RequisicaoCargaViralHIV requisicaoCargaViralHIV;
    private Cid cid;
    private Profissional profissional;
    private Exame exame;
    private Empresa empresaSolicitante;
    private Long idadeGestacional;
    private Long escolaridade;
    private Long gestante;

    public Long getEscolaridade() {
        return escolaridade;
    }

    public void setEscolaridade(Long escolaridade) {
        this.escolaridade = escolaridade;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public Empresa getEmpresaSolicitante() {
        return empresaSolicitante;
    }

    public void setEmpresaSolicitante(Empresa empresaSolicitante) {
        this.empresaSolicitante = empresaSolicitante;
    }

    public ExameRequisicao getExameRequisicao() {
        return exameRequisicao;
    }

    public void setExameRequisicao(ExameRequisicao exameRequisicao) {
        this.exameRequisicao = exameRequisicao;
    }

    public EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return enderecoUsuarioCadsus;
    }

    public void setEnderecoUsuarioCadsus(EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
        this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
    }

    public RequisicaoCargaViralHIV getRequisicaoCargaViralHIV() {
        return requisicaoCargaViralHIV;
    }

    public void setRequisicaoCargaViralHIV(RequisicaoCargaViralHIV requisicaoCargaViralHIV) {
        this.requisicaoCargaViralHIV = requisicaoCargaViralHIV;
    }

    public UsuarioCadsusCns getUsuarioCadsusCns() {
        return usuarioCadsusCns;
    }

    public void setUsuarioCadsusCns(UsuarioCadsusCns usuarioCadsusCns) {
        this.usuarioCadsusCns = usuarioCadsusCns;
    }

    public UsuarioCadsusDado getUsuarioCadsusDado() {
        return usuarioCadsusDado;
    }

    public void setUsuarioCadsusDado(UsuarioCadsusDado usuarioCadsusDado) {
        this.usuarioCadsusDado = usuarioCadsusDado;
    }

    public Cid getCid() {
        return cid;
    }

    public void setCid(Cid cid) {
        this.cid = cid;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public Exame getExame() {
        return exame;
    }

    public void setExame(Exame exame) {
        this.exame = exame;
    }

    public Long getIdadeGestacional() {
        return idadeGestacional;
    }

    public void setIdadeGestacional(Long idadeGestacional) {
        this.idadeGestacional = idadeGestacional;
    }

    public Long getGestante() {
        return gestante;
    }

    public void setGestante(Long gestante) {
        this.gestante = gestante;
    }
}