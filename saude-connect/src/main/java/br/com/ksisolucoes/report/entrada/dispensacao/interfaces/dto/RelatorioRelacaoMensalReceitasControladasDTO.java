package br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto;

import br.com.ksisolucoes.util.Coalesce;
import java.io.Serializable;
import java.util.Date;

public class RelatorioRelacaoMensalReceitasControladasDTO implements Serializable {

    private String numeroReceita;
    private Long codigoProfissional;
    private String nomeProfissional;
    private String numeroRegistroProfissional;
    private Date dataReceita;
    private Date dataUltimaDispensacao;
    private Double quantidadePrescrita;
    private Double quantidadeDispensada;
    private String codigoDcb;
    private String nomeDcb;
    private String nomeProduto;
    private String concentracao;
    private Long numeroLista;
    private Long numLicencaFuncionamento;
    private Long numeroAutorizacaoFuncionamento;
    private String nomePaciente;
    private String descricaoReceita;
    private String descricaoSubGrupo;
    private String descricaoGrupo;

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public String getNumeroReceita() {
        return numeroReceita;
    }

    public void setNumeroReceita(String numeroReceita) {
        this.numeroReceita = numeroReceita;
    }

    public Long getCodigoProfissional() {
        return codigoProfissional;
    }

    public void setCodigoProfissional(Long codigoProfissional) {
        this.codigoProfissional = codigoProfissional;
    }

    public String getNomeProfissional() {
        return Coalesce.asString(nomeProfissional);
    }

    public void setNomeProfissional(String nomeProfissional) {
        this.nomeProfissional = nomeProfissional;
    }

    public String getNumeroRegistroProfissional() {
        return Coalesce.asString(numeroRegistroProfissional);
    }

    public void setNumeroRegistroProfissional(String numeroRegistroProfissional) {
        this.numeroRegistroProfissional = numeroRegistroProfissional;
    }

    public Date getDataReceita() {
        return dataReceita;
    }

    public void setDataReceita(Date dataReceita) {
        this.dataReceita = dataReceita;
    }

    public Double getQuantidadePrescrita() {
        return quantidadePrescrita;
    }

    public void setQuantidadePrescrita(Double quantidadePrescrita) {
        this.quantidadePrescrita = quantidadePrescrita;
    }

    public Double getQuantidadeDispensada() {
        return quantidadeDispensada;
    }

    public void setQuantidadeDispensada(Double quantidadeDispensada) {
        this.quantidadeDispensada = quantidadeDispensada;
    }

    public String getCodigoDcb() {
        return Coalesce.asString(codigoDcb);
    }

    public void setCodigoDcb(String codigoDcb) {
        this.codigoDcb = codigoDcb;
    }

    public String getNomeDcb() {
        return Coalesce.asString(nomeDcb);
    }

    public void setNomeDcb(String nomeDcb) {
        this.nomeDcb = nomeDcb;
    }

    public String getNomeProduto() {
        return Coalesce.asString(nomeProduto);
    }

    public void setNomeProduto(String nomeProduto) {
        this.nomeProduto = nomeProduto;
    }

    public String getConcentracao() {
        return Coalesce.asString(concentracao);
    }

    public void setConcentracao(String concentracao) {
        this.concentracao = concentracao;
    }

    public Long getNumeroLista() {
        return numeroLista;
    }

    public void setNumeroLista(Long numeroLista) {
        this.numeroLista = numeroLista;
    }

    public Date getDataUltimaDispensacao() {
        return dataUltimaDispensacao;
    }

    public void setDataUltimaDispensacao(Date dataUltimaDispensacao) {
        this.dataUltimaDispensacao = dataUltimaDispensacao;
    }

    public Long getNumLicencaFuncionamento() {
        return numLicencaFuncionamento;
    }

    public void setNumLicencaFuncionamento(Long numLicencaFuncionamento) {
        this.numLicencaFuncionamento = numLicencaFuncionamento;
    }

    public Long getNumeroAutorizacaoFuncionamento() {
        return numeroAutorizacaoFuncionamento;
    }

    public void setNumeroAutorizacaoFuncionamento(Long numeroAutorizacaoFuncionamento) {
        this.numeroAutorizacaoFuncionamento = numeroAutorizacaoFuncionamento;
    }

    public String getDescricaoReceita() {
        return descricaoReceita;
    }

    public void setDescricaoReceita(String descricaoReceita) {
        this.descricaoReceita = descricaoReceita;
    }

    public String getDescricaoSubGrupo() {
        return descricaoSubGrupo;
    }

    public void setDescricaoSubGrupo(String descricaoSubGrupo) {
        this.descricaoSubGrupo = descricaoSubGrupo;
    }

    public String getDescricaoGrupo() {
        return descricaoGrupo;
    }

    public void setDescricaoGrupo(String descricaoGrupo) {
        this.descricaoGrupo = descricaoGrupo;
    }
}
