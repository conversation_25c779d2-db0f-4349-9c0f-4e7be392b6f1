package br.com.ksisolucoes.report.entrada.estoque.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoEtiquetaEmbarqueDTOParam implements Serializable {

    private String numeroPedido;
    private Long quantidadeEtiquetas;
    private Empresa empresa;

    public Empresa getEmpresa() { return empresa; }

    public void setEmpresa(Empresa empresa) { this.empresa = empresa; }

    public String getNumeroPedido() {
        return numeroPedido;
    }

    public void setNumeroPedido(String numeroPedido) {
        this.numeroPedido = numeroPedido;
    }

    public Long getQuantidadeEtiquetas() {
        return quantidadeEtiquetas;
    }

    public void setQuantidadeEtiquetas(Long quantidadeEtiquetas) {
        this.quantidadeEtiquetas = quantidadeEtiquetas;
    }

}
