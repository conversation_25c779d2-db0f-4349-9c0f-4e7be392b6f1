package br.com.ksisolucoes.report.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.RelatorioImpressaoExameApacDTO;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.RelatorioImpressaoExameBpaiDTO;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImprimirLaudoTfdCompletoDTO implements Serializable{

    private List<RelatorioImprimirLaudoTfdDTO> listaLaudo;
    private List<RelatorioImpressaoExameApacDTO> listaApac;
    private List<RelatorioImpressaoExameBpaiDTO> listaBpai;

    public List<RelatorioImprimirLaudoTfdDTO> getListaLaudo() {
        return listaLaudo;
    }

    public void setListaLaudo(List<RelatorioImprimirLaudoTfdDTO> listaLaudo) {
        this.listaLaudo = listaLaudo;
    }

    public List<RelatorioImpressaoExameApacDTO> getListaApac() {
        return listaApac;
    }

    public void setListaApac(List<RelatorioImpressaoExameApacDTO> listaApac) {
        this.listaApac = listaApac;
    }

    public List<RelatorioImpressaoExameBpaiDTO> getListaBpai() {
        return listaBpai;
    }

    public void setListaBpai(List<RelatorioImpressaoExameBpaiDTO> listaBpai) {
        this.listaBpai = listaBpai;
    }

}
