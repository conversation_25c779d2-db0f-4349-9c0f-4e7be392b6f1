package br.com.ksisolucoes.report.prontuario.enfermagem.interfaces.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoReceituarioTelaDTO implements Serializable {

    private String nomeProduto;
    private String posologia;
    private String unidade;
    private String quantidade;
    private String descricaoTipo;
    private Long status;

    public String getNomeProduto() {
        return nomeProduto;
    }

    public void setNomeProduto(String nomeProduto) {
        this.nomeProduto = nomeProduto;
    }

    public String getUnidade() {
        return unidade;
    }

    public void setUnidade(String unidade) {
        this.unidade = unidade;
    }

    public String getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(String quantidade) {
        this.quantidade = quantidade;
    }

    public String getPosologia() {
        return posologia;
    }

    public void setPosologia(String posologia) {
        this.posologia = posologia;
    }

    public String getDescricaoTipo() {
        return descricaoTipo;
    }

    public void setDescricaoTipo(String descricaoTipo) {
        this.descricaoTipo = descricaoTipo;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public String getNomeProdutoComStatus() {
        if (getNomeProduto() != null) {
            if (ReceituarioItem.Status.SUSPENSO.value().equals(getStatus())) {
                return "<span style='background-color:red; color:white; padding:2px;'>"
                        + getNomeProduto()
                        + "</span> - "
                        + "<span style='color:red; font-weight:bold;'>"
                        + Bundle.getStringApplication("rotulo_suspenso").toUpperCase()
                        + "</span>";
            }
            return getNomeProduto();
        }
        return  "";
    }
}
