package br.com.ksisolucoes.report.hospital.interfaces.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.SalaUnidade;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioAgendamentosCirurgicosDTOParam implements Serializable {

    private String paciente;
    private Profissional profissional;
    private SalaUnidade salaUnidade;
    private TipoProcedimento tipoProcedimento;
    private Empresa unidadeOrigem;
    private DatePeriod periodo;
    private Long situacao;
    private Convenio convenio;
    private FormaApresentacao formaApresentacao;
    private Long codigoAgendaGradeAtendimentoHorario;

    public static enum FormaApresentacao {

        GERAL(Bundle.getStringApplication("rotulo_geral")),
        DATA(Bundle.getStringApplication("rotulo_data")),
        CONVENIO(Bundle.getStringApplication("rotulo_convenio")),
        PROFISSIONAL(Bundle.getStringApplication("rotulo_profissional")),
        SALA(Bundle.getStringApplication("rotulo_sala")),
        TIPO_PROCEDIMENTO(Bundle.getStringApplication("rotulo_tipo_procedimento"));

        private String descricao;

        private FormaApresentacao(String descricao) {
            this.descricao = descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_paciente")
    public String getPaciente() {
        return paciente;
    }

    public void setPaciente(String paciente) {
        this.paciente = paciente;
    }

    @DescricaoParametro("rotulo_profissional")
    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    @DescricaoParametro("rotulo_sala")
    public SalaUnidade getSalaUnidade() {
        return salaUnidade;
    }

    public void setSalaUnidade(SalaUnidade salaUnidade) {
        this.salaUnidade = salaUnidade;
    }

    @DescricaoParametro("rotulo_tipo_procedimento")
    public TipoProcedimento getTipoProcedimento() {
        return tipoProcedimento;
    }

    public void setTipoProcedimento(TipoProcedimento tipoProcedimento) {
        this.tipoProcedimento = tipoProcedimento;
    }

    @DescricaoParametro("rotulo_unidade_origem")
    public Empresa getUnidadeOrigem() {
        return unidadeOrigem;
    }

    public void setUnidadeOrigem(Empresa unidadeOrigem) {
        this.unidadeOrigem = unidadeOrigem;
    }

    @DescricaoParametro("rotulo_situacao")
    public String getDescricaoSituacao() {
        String descricaoSituacao = AgendaGradeAtendimentoHorario.getSituacao(this.getSituacao());
        return descricaoSituacao;
    }

    public Long getSituacao() {
        return situacao;
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }

    @DescricaoParametro("rotulo_convenio")
    public Convenio getConvenio() {
        return convenio;
    }

    public void setConvenio(Convenio convenio) {
        this.convenio = convenio;
    }

    public Long getCodigoAgendaGradeAtendimentoHorario() {
        return codigoAgendaGradeAtendimentoHorario;
    }

    public void setCodigoAgendaGradeAtendimentoHorario(Long codigoAgendaGradeAtendimentoHorario) {
        this.codigoAgendaGradeAtendimentoHorario = codigoAgendaGradeAtendimentoHorario;
    }
}
