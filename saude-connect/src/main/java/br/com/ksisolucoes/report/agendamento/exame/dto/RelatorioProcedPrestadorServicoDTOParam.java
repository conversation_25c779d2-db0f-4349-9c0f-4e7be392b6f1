package br.com.ksisolucoes.report.agendamento.exame.dto;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;

import java.io.Serializable;
import java.util.List;

public class RelatorioProcedPrestadorServicoDTOParam implements Serializable {

    private Empresa prestador;
    private String cnesPrestador;
    private ExameProcedimento exameProcedimento;
    private List<TipoExame> tipoExame;
    private RelatorioProcedPrestadorServicoDTOParam.FormaApresentacao formaApresentacao;
    private DatePeriod periodo;
    private Long numeroSolicitacao;
    private UsuarioCadsus usuarioCadsus;
    private TipoRelatorio tipoArquivo;
    private String cnes;

    public enum FormaApresentacao {

        GERAL(Bundle.getStringApplication("rotulo_geral")),
        PACIENTE(Bundle.getStringApplication("rotulo_paciente")),
        ;

        private final String descricao;

        FormaApresentacao(String descricao) {
            this.descricao = descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }



    public Empresa getPrestador() {
        return prestador;
    }

    public void setPrestador(Empresa prestador) {
        this.prestador = prestador;
    }

    @DescricaoParametro("rotulo_prestador")
    public String getCnesPrestador() {
        if (prestador == null) return "";

        return prestador.getDescricaoFormatadaCnesDescricao();
    }

    public void setCnesPrestador(String cnesPrestador) {
        this.cnesPrestador = cnesPrestador;
    }

    @DescricaoParametro("rotulo_tipo_exame")
    public List<TipoExame> getTipoExame() {
        return tipoExame;
    }

    public void setTipoExame(List<TipoExame> tipoExame) {
        this.tipoExame = tipoExame;
    }

    @DescricaoParametro("rotulo_exame_procedimento")
    public ExameProcedimento getExameProcedimento() {
        return exameProcedimento;
    }

    public void setExameProcedimento(ExameProcedimento exameProcedimento) {
        this.exameProcedimento = exameProcedimento;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_numero_solicitacao_abv")
    public Long getNumeroSolicitacao() {
        return numeroSolicitacao;
    }

    public void setNumeroSolicitacao(Long numeroSolicitacao) {
        this.numeroSolicitacao = numeroSolicitacao;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public TipoRelatorio getTipoArquivo() {
        return tipoArquivo;
    }

    public void setTipoArquivo(TipoRelatorio tipoArquivo) {
        this.tipoArquivo = tipoArquivo;
    }

    @DescricaoParametro("rotulo_cnes")
    public String getCnes() {
        return cnes;
    }

    public void setCnes(String cnes) {
        this.cnes = cnes;
    }
}
