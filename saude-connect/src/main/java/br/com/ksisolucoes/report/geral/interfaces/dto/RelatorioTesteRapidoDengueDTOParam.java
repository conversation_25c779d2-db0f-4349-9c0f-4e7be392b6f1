package br.com.ksisolucoes.report.geral.interfaces.dto;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioTesteRapidoDengueDTOParam implements Serializable {

    private Empresa empresa;
    private DatePeriod periodo;
    private FormaApresentacao formaApresentacao;
    private TipoRelatorio tipoRelatorio;

    public RelatorioTesteRapidoDengueDTOParam() {
    }

    public enum FormaApresentacao implements IEnum {

        GERAL(0L, Bundle.getStringApplication("rotulo_geral")),
        UNIDADE(1L, Bundle.getStringApplication("rotulo_unidade")),
        EQUIPE_REFERENCIA(2L, Bundle.getStringApplication("equipe_referencia_completo"));

        private Long value;
        private String descricao;

        private FormaApresentacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    @DescricaoParametro("rotulo_empresa")
    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    public TipoRelatorio getTipoRelatorio() {
        return tipoRelatorio;
    }

    public void setTipoRelatorio(TipoRelatorio tipoRelatorio) {
        this.tipoRelatorio = tipoRelatorio;
    }
}
