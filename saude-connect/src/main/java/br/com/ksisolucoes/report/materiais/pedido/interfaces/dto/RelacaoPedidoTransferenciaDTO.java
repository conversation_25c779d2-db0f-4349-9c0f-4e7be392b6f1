package br.com.ksisolucoes.report.materiais.pedido.interfaces.dto;

import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaItem;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelacaoPedidoTransferenciaDTO implements Serializable{

    private PedidoTransferenciaItem pedidoTransferenciaItem;
    private Double quantidadeEnviada;
    private Double quantidadeNaoAprovada;
    private Double quantidadeSemEstoque;

    public PedidoTransferenciaItem getPedidoTransferenciaItem() {
        return pedidoTransferenciaItem;
    }

    public void setPedidoTransferenciaItem(PedidoTransferenciaItem pedidoTransferenciaItem) {
        this.pedidoTransferenciaItem = pedidoTransferenciaItem;
    }

    public Double getQuantidadeEnviada() {
        return quantidadeEnviada;
    }

    public void setQuantidadeEnviada(Double quantidadeEnviada) {
        this.quantidadeEnviada = quantidadeEnviada;
    }

    public Double getQuantidadeNaoAprovada() {
        return quantidadeNaoAprovada;
    }

    public void setQuantidadeNaoAprovada(Double quantidadeNaoAprovada) {
        this.quantidadeNaoAprovada = quantidadeNaoAprovada;
    }

    public Double getQuantidadeSemEstoque() {
        return quantidadeSemEstoque;
    }

    public void setQuantidadeSemEstoque(Double quantidadeSemEstoque) {
        this.quantidadeSemEstoque = quantidadeSemEstoque;
    }
}
