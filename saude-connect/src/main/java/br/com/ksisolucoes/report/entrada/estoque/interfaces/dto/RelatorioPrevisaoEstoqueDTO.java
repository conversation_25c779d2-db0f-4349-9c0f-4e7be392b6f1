package br.com.ksisolucoes.report.entrada.estoque.interfaces.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.Util;
import java.io.Serializable;

public class RelatorioPrevisaoEstoqueDTO implements Serializable {

    private String codigoEmpresa;
    private String descricaoEmpresa;
    private String codigoProduto;
    private String descricaoProduto;
    private String unidadeProduto;
    private String descricaoTipoDocumento;
    private String flagSiglaTipoDocumento;
    private String flagTipoMovimentoTipoDocumento;
    private Double quantidade;
    private Double quantidadeMedia;
    private Double precoMedio;
    private Double precoCusto;
    private Double precoUnitario;
    private Long codigoGrupo;
    private String descricaoGrupo;
    private Long codigoSubGrupo;
    private String descricaoSubGrupo;
    private String codigoEmpresaDestino;
    private String descricaoEmpresaDestino;
    private Long codigoCentroCusto;
    private String descricaoCentroCusto;
    private Double previsao;
    private Double estoqueFisico;
    private Double estoqueMinimo;

    public String getCodigoEmpresaDestino() {
        return codigoEmpresaDestino;
    }

    public void setCodigoEmpresaDestino(String codigoEmpresaDestino) {
        this.codigoEmpresaDestino = codigoEmpresaDestino;
    }

    public String getDescricaoEmpresaDestino() {
        return descricaoEmpresaDestino;
    }

    public void setDescricaoEmpresaDestino(String descricaoEmpresaDestino) {
        this.descricaoEmpresaDestino = descricaoEmpresaDestino;
    }

    public String getCodigoEmpresa() {
        return this.codigoEmpresa;
    }

    public void setCodigoEmpresa(String codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public Long getCodigoGrupo() {
        return codigoGrupo;
    }

    public void setCodigoGrupo(Long codigoGrupo) {
        this.codigoGrupo = codigoGrupo;
    }

    public String getCodigoProduto() {
        return this.codigoProduto;
    }

    public void setCodigoProduto(String codigoProduto) {
        this.codigoProduto = codigoProduto;
    }

    public Long getCodigoSubGrupo() {
        return this.codigoSubGrupo;
    }

    public void setCodigoSubGrupo(Long codigoSubGrupo) {
        this.codigoSubGrupo = codigoSubGrupo;
    }

    public String getDescricaoEmpresa() {
        return this.descricaoEmpresa;
    }

    public void setDescricaoEmpresa(String descricaoEmpresa) {
        this.descricaoEmpresa = descricaoEmpresa;
    }

    public String getDescricaoGrupo() {
        return descricaoGrupo;
    }

    public void setDescricaoGrupo(String descricaoGrupo) {
        this.descricaoGrupo = descricaoGrupo;
    }

    public String getDescricaoProduto() {
        return this.descricaoProduto;
    }

    public void setDescricaoProduto(String descricaoProduto) {
        this.descricaoProduto = descricaoProduto;
    }

    public String getDescricaoSubGrupo() {
        return this.descricaoSubGrupo;
    }

    public void setDescricaoSubGrupo(String descricaoSubGrupo) {
        this.descricaoSubGrupo = descricaoSubGrupo;
    }

    public String getDescricaoTipoDocumento() {
        return this.descricaoTipoDocumento;
    }

    public void setDescricaoTipoDocumento(String descricaoTipoDocumento) {
        this.descricaoTipoDocumento = descricaoTipoDocumento;
    }

    public String getFlagSiglaTipoDocumento() {
        return this.flagSiglaTipoDocumento;
    }

    public void setFlagSiglaTipoDocumento(String flagSiglaTipoDocumento) {
        this.flagSiglaTipoDocumento = flagSiglaTipoDocumento;
    }

    public String getFlagTipoMovimentoTipoDocumento() {
        return this.flagTipoMovimentoTipoDocumento;
    }

    public void setFlagTipoMovimentoTipoDocumento(String flagTipoMovimentoTipoDocumento) {
        this.flagTipoMovimentoTipoDocumento = flagTipoMovimentoTipoDocumento;
    }

    public Double getPrecoCusto() {
        return this.precoCusto;
    }

    public void setPrecoCusto(Double precoCusto) {
        this.precoCusto = precoCusto;
    }

    public Double getPrecoMedio() {
        return this.precoMedio;
    }

    public void setPrecoMedio(Double precoMedio) {
        this.precoMedio = precoMedio;
    }

    public Double getPrecoUnitario() {
        return this.precoUnitario;
    }

    public void setPrecoUnitario(Double precoUnitario) {
        this.precoUnitario = precoUnitario;
    }

    public Double getQuantidade() {
        return this.quantidade;
    }

    public void setQuantidade(Double quantidade) {
        this.quantidade = quantidade;
    }

    public String getUnidadeProduto() {
        return this.unidadeProduto;
    }

    public void setUnidadeProduto(String unidadeProduto) {
        this.unidadeProduto = unidadeProduto;
    }

    public Long getCodigoCentroCusto() {
        return codigoCentroCusto;
    }

    public void setCodigoCentroCusto(Long codigoCentroCusto) {
        this.codigoCentroCusto = codigoCentroCusto;
    }

    public String getDescricaoCentroCusto() {
        return descricaoCentroCusto;
    }

    public void setDescricaoCentroCusto(String descricaoCentroCusto) {
        this.descricaoCentroCusto = descricaoCentroCusto;
    }

    public String getDescricaoGrupoFormatado() {
        return Util.getDescricaoFormatado(Coalesce.asString(getCodigoGrupo(), ""), Coalesce.asString(this.getDescricaoGrupo()));
    }

    public String getDescricaoSubGrupoFormatado() {
        return Util.getDescricaoFormatado(Coalesce.asString(getCodigoSubGrupo(), ""), Coalesce.asString(this.getDescricaoSubGrupo()));
    }

    public String getDescricaoProdutoFormatado() {
        return Util.getDescricaoFormatado(Coalesce.asString(getCodigoProduto(), ""), Coalesce.asString(this.getDescricaoProduto()));
    }

    public String getDescricaoFormatadoEmpresaDestino() {
        if (getCodigoEmpresaDestino() != null) {
            return Util.getDescricaoFormatado(Coalesce.asString(getCodigoEmpresaDestino(), ""), Coalesce.asString(this.getDescricaoEmpresaDestino()));
        }
        return Bundle.getStringApplication("rotulo_sem_cadastro");
    }

    public String getDescricaoFormatadoCentroCusto() {
        if (getCodigoCentroCusto() != null) {
            return Util.getDescricaoFormatado(getCodigoCentroCusto(), this.getDescricaoCentroCusto());
        }
        return Bundle.getStringApplication("rotulo_nao_definido");
    }

    public Double getPrevisao() {
        return previsao;
    }

    public void setPrevisao(Double previsao) {
        this.previsao = previsao;
    }

    public Double getEstoqueFisico() {
        return estoqueFisico;
    }

    public void setEstoqueFisico(Double estoqueFisico) {
        this.estoqueFisico = estoqueFisico;
    }

    public Double getEstoqueMinimo() {
        return estoqueMinimo;
    }

    public void setEstoqueMinimo(Double estoqueMinimo) {
        this.estoqueMinimo = estoqueMinimo;
    }

    public Double getQuantidadeMedia() {
        return quantidadeMedia;
    }

    public void setQuantidadeMedia(Double quantidadeMedia) {
        this.quantidadeMedia = quantidadeMedia;
    }
}
