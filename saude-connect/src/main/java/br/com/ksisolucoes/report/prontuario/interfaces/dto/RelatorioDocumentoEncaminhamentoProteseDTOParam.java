package br.com.ksisolucoes.report.prontuario.interfaces.dto;

import br.com.ksisolucoes.util.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class RelatorioDocumentoEncaminhamentoProteseDTOParam implements Serializable{
    
    private Long codigoDocumentoAtendimento;
    private boolean assinadoDigitalmente;
    private String dataAssinatura;

    public Long getCodigoDocumentoAtendimento() {
        return codigoDocumentoAtendimento;
    }

    public void setCodigoDocumentoAtendimento(Long codigoDocumentoAtendimento) {
        this.codigoDocumentoAtendimento = codigoDocumentoAtendimento;
    }

    public boolean isAssinadoDigitalmente() {
        return assinadoDigitalmente;
    }

    public void setAssinadoDigitalmente(boolean assinadoDigitalmente) {
        this.assinadoDigitalmente = assinadoDigitalmente;
    }

    public String getDataAssinatura() {
        return Data.formatarDataHora(new Date());
    }

    public void setDataAssinatura(String dataAssinatura) {
        this.dataAssinatura = dataAssinatura;
    }
}
