package br.com.ksisolucoes.report.materiais.judicial.interfaces.dto;

import br.com.ksisolucoes.vo.basico.ProdutoSolicitado;
import br.com.ksisolucoes.vo.basico.ProdutoSolicitadoItem;
import br.com.ksisolucoes.vo.basico.ProdutoSolicitadoMovimento;
import br.com.ksisolucoes.vo.basico.ProdutoSolicitadoMovimentoLote;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoProdutoSolicitadoDispensadoDTO implements Serializable {

    private ProdutoSolicitadoMovimento produtoSolicitadoMovimento;
    private ProdutoSolicitado produtoSolicitado;
    private ProdutoSolicitadoItem produtoSolicitadoItem;
    private Produto produto;
    private List<ProdutoSolicitadoMovimentoLote> produtoSolicitadoMovimentoLoteList;

    public ProdutoSolicitadoMovimento getProdutoSolicitadoMovimento() {
        return produtoSolicitadoMovimento;
    }

    public void setProdutoSolicitadoMovimento(ProdutoSolicitadoMovimento produtoSolicitadoMovimento) {
        this.produtoSolicitadoMovimento = produtoSolicitadoMovimento;
    }

    public ProdutoSolicitado getProdutoSolicitado() {
        return produtoSolicitado;
    }

    public void setProdutoSolicitado(ProdutoSolicitado produtoSolicitado) {
        this.produtoSolicitado = produtoSolicitado;
    }

    public ProdutoSolicitadoItem getProdutoSolicitadoItem() {
        return produtoSolicitadoItem;
    }

    public void setProdutoSolicitadoItem(ProdutoSolicitadoItem produtoSolicitadoItem) {
        this.produtoSolicitadoItem = produtoSolicitadoItem;
    }

    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    public List<ProdutoSolicitadoMovimentoLote> getProdutoSolicitadoMovimentoLoteList() {
        return produtoSolicitadoMovimentoLoteList;
    }

    public void setProdutoSolicitadoMovimentoLoteList(List<ProdutoSolicitadoMovimentoLote> produtoSolicitadoMovimentoLoteList) {
        this.produtoSolicitadoMovimentoLoteList = produtoSolicitadoMovimentoLoteList;
    }
}
