package br.com.ksisolucoes.report.agendamento.exame.dto;

import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExame;
import br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExameItem;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoLaudoExameDTO implements Serializable {
    
    private AtendimentoExame atendimentoExame;
    private UsuarioCadsus usuarioCadsus;
    private TipoExame tipoExame;
    private Profissional profissionalLaudo;
    private Convenio convenio;
    private Atendimento atendimento;
    private List<AtendimentoExameItem> atendimentoExameList;

    public AtendimentoExame getAtendimentoExame() {
        return atendimentoExame;
    }

    public void setAtendimentoExame(AtendimentoExame atendimentoExame) {
        this.atendimentoExame = atendimentoExame;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public TipoExame getTipoExame() {
        return tipoExame;
    }

    public void setTipoExame(TipoExame tipoExame) {
        this.tipoExame = tipoExame;
    }

    public Profissional getProfissionalLaudo() {
        return profissionalLaudo;
    }

    public void setProfissionalLaudo(Profissional profissionalLaudo) {
        this.profissionalLaudo = profissionalLaudo;
    }

    public Convenio getConvenio() {
        return convenio;
    }

    public void setConvenio(Convenio convenio) {
        this.convenio = convenio;
    }

    public Atendimento getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    public List<AtendimentoExameItem> getAtendimentoExameList() {
        return atendimentoExameList;
    }

    public void setAtendimentoExameList(List<AtendimentoExameItem> atendimentoExameList) {
        this.atendimentoExameList = atendimentoExameList;
    }
}
