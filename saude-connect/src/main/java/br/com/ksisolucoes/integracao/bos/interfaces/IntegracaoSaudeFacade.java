/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.integracao.bos.interfaces;

import br.com.celk.provider.ejb.EJBLocation;
import br.com.ksisolucoes.bo.integracao.cnes.dto.IntegracaoCnesDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.integracao.cadsus.ImportacaoXmlCadsus;

import java.io.File;
import java.rmi.RemoteException;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@EJBLocation("br.com.ksisolucoes.integracao.bos.IntegracaoSaudeBO")
public interface IntegracaoSaudeFacade {

    public int gerarArquivoCadsus() throws RemoteException, br.com.ksisolucoes.dao.exception.DAOException, ValidacaoException;

    public void integrarInformacoesTabelasCorporativas(List<String> arquivos) throws RemoteException;

    public void integrarInformacoesCadSus(List<String> arquivos) throws RemoteException;

    public void integrarInformacoesCnes(String driver, String url, String username, String password) throws RemoteException;

    public List<String> getFileList(String path) throws RemoteException, DAOException, ValidacaoException;

    public int atualizarRegistrosAlterados(Date dataControle, Date dataParametro) throws RemoteException,br.com.ksisolucoes.dao.exception.DAOException, ValidacaoException;

    public void integrarInformacoesCnesXML(String xml) throws RemoteException;

    public void importarXMLCadsus(String xml, ImportacaoXmlCadsus importacaoXmlCadsus) throws RemoteException, br.com.ksisolucoes.dao.exception.DAOException, ValidacaoException;

    public ImportacaoXmlCadsus saveImportacaoXmlCadsus(ImportacaoXmlCadsus importacaoXmlCadsus) throws RemoteException, br.com.ksisolucoes.dao.exception.DAOException, ValidacaoException;

    public List<ImportacaoXmlCadsus> consultarImportacaoXMLCadsus() throws RemoteException, br.com.ksisolucoes.dao.exception.DAOException, ValidacaoException;

    public String escreverArquivo(byte[] xml, String nomeArquivo) throws RemoteException, br.com.ksisolucoes.dao.exception.DAOException, ValidacaoException;

    public File baixarArquivoXml(String pathFtp) throws br.com.ksisolucoes.dao.exception.DAOException, ValidacaoException;

    public void enviarCnesProcessoFila(IntegracaoCnesDTO dto) throws DAOException, ValidacaoException;

}
