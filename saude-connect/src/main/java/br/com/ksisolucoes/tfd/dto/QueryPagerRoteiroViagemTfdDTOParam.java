/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.tfd.dto;

import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.frota.Motorista;
import br.com.ksisolucoes.vo.frota.Veiculo;
import br.com.ksisolucoes.vo.basico.Cidade;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryPagerRoteiroViagemTfdDTOParam implements Serializable {

    private OperadorValor<List<Veiculo>> veiculos;
    private OperadorValor<List<Motorista>> motoristas;
    private OperadorValor<List<Cidade>> destinos;
    private DatePeriod periodo;
    private Long numeroRoteiro;
    private String reimpressao;

    public OperadorValor<List<Cidade>> getDestinos() {
        return destinos;
    }

    public void setDestinos(OperadorValor<List<Cidade>> destinos) {
        this.destinos = destinos;
    }

    public OperadorValor<List<Motorista>> getMotoristas() {
        return motoristas;
    }

    public void setMotoristas(OperadorValor<List<Motorista>> motoristas) {
        this.motoristas = motoristas;
    }

    public Long getNumeroRoteiro() {
        return numeroRoteiro;
    }

    public void setNumeroRoteiro(Long numeroRoteiro) {
        this.numeroRoteiro = numeroRoteiro;
    }

    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public String getReimpressao() {
        return reimpressao;
    }

    public void setReimpressao(String reimpressao) {
        this.reimpressao = reimpressao;
    }

    public OperadorValor<List<Veiculo>> getVeiculos() {
        return veiculos;
    }

    public void setVeiculos(OperadorValor<List<Veiculo>> veiculos) {
        this.veiculos = veiculos;
    }
}
