package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.ClassificacaoCids;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class RelatorioAgravosDTO implements Serializable {

    private Empresa unidade;
    private Empresa unidadeReferencia;
    private Cid cid;
    private ClassificacaoCids cidClassificacao;
    private UsuarioCadsus usuarioCadsus;
    private String bairro;
    private RegistroAgravo registroAgravo;
    private String tipoResumo;
    private Date dataLimiteEncerramento;

    public Empresa getUnidade() {
        return unidade;
    }

    public void setUnidade(Empresa unidade) {
        this.unidade = unidade;
    }

    public Cid getCid() {
        return cid;
    }

    public void setCid(Cid cid) {
        this.cid = cid;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public RegistroAgravo getRegistroAgravo() {
        return registroAgravo;
    }

    public void setRegistroAgravo(RegistroAgravo registroAgravo) {
        this.registroAgravo = registroAgravo;
    }

    public String getTipoResumo() {
        return tipoResumo;
    }

    public void setTipoResumo(String tipoResumo) {
        this.tipoResumo = tipoResumo;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public ClassificacaoCids getCidClassificacao() {
        return cidClassificacao;
    }

    public void setCidClassificacao(ClassificacaoCids cidClassificacao) {
        this.cidClassificacao = cidClassificacao;
    }

    public Date getDataLimiteEncerramento() {
        return dataLimiteEncerramento;
    }

    public void setDataLimiteEncerramento(Date dataLimiteEncerramento) {
        this.dataLimiteEncerramento = dataLimiteEncerramento;
    }

    public String getDescricaoGestante(){
        if(getRegistroAgravo() != null && getRegistroAgravo().getGestante() != null){
            if(RepositoryComponentDefault.SIM_LONG.equals(getRegistroAgravo().getGestante())){
                return Bundle.getStringApplication("rotulo_sim");
            }else{
                return Bundle.getStringApplication("rotulo_nao");
            }
        }
        return null;
    }

    public Empresa getUnidadeReferencia() {
        return unidadeReferencia;
    }

    public void setUnidadeReferencia(Empresa unidadeReferencia) {
        this.unidadeReferencia = unidadeReferencia;
    }
}
