package br.com.ksisolucoes.bo.vigilancia.interfaces.dto.roteiro;

import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecaoRoteiro;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by laudecir on 02/06/17.
 */
public class RegistroInspecaoRoteiroDTO implements Serializable {

    private RegistroInspecaoRoteiro registroInspecaoRoteiro;
    private List<RegistroInspecaoItemRoteiroDTO> itens = new ArrayList();

    public RegistroInspecaoRoteiroDTO(RegistroInspecaoRoteiro registroInspecaoRoteiro) {
        this.registroInspecaoRoteiro = registroInspecaoRoteiro;
    }

    public RegistroInspecaoRoteiro getRegistroInspecaoRoteiro() {
        return registroInspecaoRoteiro;
    }

    public void setRegistroInspecaoRoteiro(RegistroInspecaoRoteiro registroInspecaoRoteiro) {
        this.registroInspecaoRoteiro = registroInspecaoRoteiro;
    }

    public List<RegistroInspecaoItemRoteiroDTO> getItens() {
        return itens;
    }

    public void setItens(List<RegistroInspecaoItemRoteiroDTO> itens) {
        this.itens = itens;
    }

    public void add(RegistroInspecaoItemRoteiroDTO itemRoteiroDTO) {
        this.itens.add(itemRoteiroDTO);
    }
}
