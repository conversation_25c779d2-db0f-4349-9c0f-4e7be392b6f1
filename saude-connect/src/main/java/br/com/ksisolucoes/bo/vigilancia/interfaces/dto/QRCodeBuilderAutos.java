package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.jasypt.util.text.BasicTextEncryptor;

import javax.ws.rs.core.UriBuilder;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QRCodeBuilderAutos implements Serializable {

    public static final String PARAM_CHAVE = "gKVdcF";
    public static final String PARAM_CODIGO = "vrdSRn";
    public static final String PARAM_TIPO = "sHkUsN";
    public static final String CRYPTO_PASS = "ZuWqjxY3KZeVc7A8";

    public static enum Tipo {
        INFRACAO,
        INTIMACAO,
        MULTA,
        PENALIDADE,
        TERMO_AJUSTAMENTO_CONDUTA;
    }

    private String chave;
    private Long codigo;
    private Tipo tipo;

    public String getURL() {
        StringBuilder url = new StringBuilder();
        url.append(VigilanciaHelper.getURLQRCodePageAuto());

        BasicTextEncryptor encryptor = new BasicTextEncryptor();
        encryptor.setPassword(CRYPTO_PASS);

        UriBuilder uriBuilder = UriBuilder.fromPath(VigilanciaHelper.getURLQRCodePageAuto())
                .queryParam(PARAM_CHAVE, encryptor.encrypt(this.chave))
                .queryParam(PARAM_CODIGO, encryptor.encrypt(this.codigo.toString()))
                .queryParam(PARAM_TIPO, encryptor.encrypt(this.tipo.toString()));

        return uriBuilder.build().toString();
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public void setTipo(Tipo tipo) {
        this.tipo = tipo;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }


}
