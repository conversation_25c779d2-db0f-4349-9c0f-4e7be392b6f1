package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.covid19.build;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.covid19.SinaisClinicosCovid19DTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.covid19.builder.FormularioTriagemSinaisClinicosCovid19Builder;
import br.com.ksisolucoes.vo.cadsus.FormularioTriagemSinaisClinicosCovid19;
import br.com.ksisolucoes.vo.cadsus.SinaisClinicosCovid19;

import java.util.ArrayList;
import java.util.List;

public class BuildFormularioTriagemSinaisClinicosCovid19 {

    public List<FormularioTriagemSinaisClinicosCovid19> buildSinais(List<Long> codigosSinais) {
        List<FormularioTriagemSinaisClinicosCovid19> sinais = new ArrayList<>();
        if (CollectionUtils.isNotNullEmpty(codigosSinais)) {
            for (Long sinalCovidDTO : codigosSinais) {
                sinais.add(this.buildSinal(sinalCovidDTO));
            }
        }
        return sinais;
    }

    public FormularioTriagemSinaisClinicosCovid19 buildSinal(Long codigoSinal) {
        return new FormularioTriagemSinaisClinicosCovid19Builder()
                .setSinalClinicoCovid19(new SinaisClinicosCovid19(codigoSinal))
                .build();
    }
}
