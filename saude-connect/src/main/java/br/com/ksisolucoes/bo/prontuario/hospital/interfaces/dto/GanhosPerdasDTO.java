package br.com.ksisolucoes.bo.prontuario.hospital.interfaces.dto;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.hospital.BalancoHidrico;
import br.com.ksisolucoes.vo.prontuario.hospital.TipoGanhoPerda;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang.SerializationUtils;

/**
 *
 * <AUTHOR>
 */
public class GanhosPerdasDTO implements Serializable {

    private Date dataLancto;
    private Date horaLancto;
    private Atendimento atendimento;
    private Long tipo = TipoGanhoPerda.Tipo.GANHO.value();
    private List<BalancoHidrico> list = new ArrayList();
    private List<BalancoHidrico> listInativos = new ArrayList();
    private BalancoHidrico balancoHidrico = new BalancoHidrico();

    public BalancoHidrico getBalancoHidrico() {
        return balancoHidrico;
    }

    public void setBalancoHidrico(BalancoHidrico balancoHidrico) {
        this.balancoHidrico = balancoHidrico;
    }

    public Date getDataLancto() {
        return dataLancto;
    }

    public void setDataLancto(Date dataLancto) {
        this.dataLancto = dataLancto;
    }

    public Date getHoraLancto() {
        return horaLancto;
    }

    public void setHoraLancto(Date horaLancto) {
        this.horaLancto = horaLancto;
    }

    public Atendimento getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    public Long getTipo() {
        return tipo;
    }

    public void setTipo(Long tipo) {
        this.tipo = tipo;
    }

    public List<BalancoHidrico> getList() {
        return list;
    }

    public void setList(List<BalancoHidrico> list) {
        this.list = list;
    }

    public void addGanhoPerda() throws ValidacaoException {
        Date dataHoraLancamento = DataUtil.mergeDataHora(dataLancto, horaLancto);
        balancoHidrico.setDataLancamento(dataHoraLancamento);
        balancoHidrico.setAtendimento(atendimento);
        balancoHidrico.setProfissionalLancamento(atendimento.getProfissional());
        list.add(0, (BalancoHidrico) SerializationUtils.clone(balancoHidrico));
    }

    public void removeGanhoPerda(BalancoHidrico balancoHidrico) {
        for (int i = 0; i < list.size(); i++) {
            BalancoHidrico item = list.get(i);
            if (item == balancoHidrico) {
                list.remove(i);
                addInativos(item);
                return;
            }
        }
    }

    public List<BalancoHidrico> getListInativos() {
        return listInativos;
    }

    public void setListInativos(List<BalancoHidrico> listInativos) {
        this.listInativos = listInativos;
    }

    private void addInativos(BalancoHidrico balancoHidrico) {
        balancoHidrico.setAtivo(RepositoryComponentDefault.NAO_LONG);
        balancoHidrico.setDataInativacao(DataUtil.getDataAtual());
        listInativos.add(0, balancoHidrico);
    }
}
