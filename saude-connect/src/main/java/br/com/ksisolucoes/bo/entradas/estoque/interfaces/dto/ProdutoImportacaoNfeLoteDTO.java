package br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.estoque.EstoqueHelper;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.Fabricante;
import br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura;

import java.io.Serializable;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class ProdutoImportacaoNfeLoteDTO implements Serializable {

    private String lote;
    private Double quantidade;
    private Double quantidadeOriginal;
    private Date dataFabricacao;
    private Date dataValidade;
    private LocalizacaoEstrutura localizacaoEstrutura;
    private Empresa empresa;
    private Fabricante fabricante;

    public Fabricante getFabricante() {
        return fabricante;
    }

    public void setFabricante(Fabricante fabricante) {
        this.fabricante = fabricante;
    }

    public String getLote() {
        return lote;
    }

    public void setLote(String lote) {
        this.lote = lote;
    }

    public Double getQuantidade() {
        return Coalesce.asDouble(quantidade);
    }

    public void setQuantidade(String quantidade) {
        this.quantidade = Double.valueOf(Coalesce.asString(quantidade));
    }

    public void setQuantidade(Double quantidade) {
        this.quantidade = quantidade;
    }

    public Date getDataFabricacao() {
        return dataFabricacao;
    }

    public void setDataFabricacao(String dataFabricacao) throws ParseException {
        if (dataFabricacao != null && !dataFabricacao.isEmpty()) {
            DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            this.dataFabricacao = format.parse(dataFabricacao);
        }
    }

    public void setDataFabricacao(Date dataFabricacao) {
        this.dataFabricacao = dataFabricacao;
    }

    public Date getDataValidade() {
        return dataValidade;
    }

    public void setDataValidade(String dataValidade) throws ParseException {
        if (dataValidade != null && !dataValidade.isEmpty()) {
            DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            this.dataValidade = format.parse(dataValidade);
        }
    }

    public void setDataValidade(Date dataValidade) {
        this.dataValidade = dataValidade;
    }

    public LocalizacaoEstrutura getLocalizacaoEstrutura() {
        if(localizacaoEstrutura == null){
            try {
                localizacaoEstrutura = EstoqueHelper.getLocalizacaoEstruturaPadrao();
            } catch (ValidacaoException e) {
                Logger.getLogger(ProdutoImportacaoNfeLoteDTO.class.getName()).log(Level.SEVERE, null, e);
            }
        }
        return localizacaoEstrutura;
    }

    public void setLocalizacaoEstrutura(LocalizacaoEstrutura localizacaoEstrutura) {
        this.localizacaoEstrutura = localizacaoEstrutura;
    }

    public Double getQuantidadeOriginal() {
        return quantidadeOriginal;
    }

    public void setQuantidadeOriginal(Double quantidadeOriginal) {
        this.quantidadeOriginal = quantidadeOriginal;
    }

    public void setQuantidadeOriginal(String quantidadeOriginal) {
        this.quantidadeOriginal = Double.valueOf(Coalesce.asString(quantidadeOriginal));
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }
}
