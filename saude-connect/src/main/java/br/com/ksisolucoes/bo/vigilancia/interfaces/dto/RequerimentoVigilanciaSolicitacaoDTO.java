package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RequerimentoVigilanciaSolicitacaoDTO implements Serializable {

    private RequerimentoVigilancia requerimentoVigilancia;

    public RequerimentoVigilanciaSolicitacaoDTO() {
    }

    public RequerimentoVigilanciaSolicitacaoDTO(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }

    public void setRequerimentoVigilancia(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }
}
