package br.com.ksisolucoes.bo.consorcio.interfaces.dto;

import br.com.ksisolucoes.vo.consorcio.ConsorcioPrestador;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoFormaOrganizacao;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoGrupo;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoSubGrupo;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaDominioConsorcioProcedimentoDTOParam implements Serializable{

    private Long codigo;
    private String descricao;
    private ProcedimentoFormaOrganizacao procedimentoFormaOrganizacao;
    private ProcedimentoSubGrupo procedimentoSubGrupo;
    private ProcedimentoGrupo procedimentoGrupo;
    private String keyword;
    private String propSort;
    private boolean ascending;
    private boolean apenasProcedimentosVinculadosPrestador;
    private ConsorcioPrestador consorcioPrestador;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public ProcedimentoFormaOrganizacao getProcedimentoFormaOrganizacao() {
        return procedimentoFormaOrganizacao;
    }

    public void setProcedimentoFormaOrganizacao(ProcedimentoFormaOrganizacao procedimentoFormaOrganizacao) {
        this.procedimentoFormaOrganizacao = procedimentoFormaOrganizacao;
    }

    public ProcedimentoSubGrupo getProcedimentoSubGrupo() {
        return procedimentoSubGrupo;
    }

    public void setProcedimentoSubGrupo(ProcedimentoSubGrupo procedimentoSubGrupo) {
        this.procedimentoSubGrupo = procedimentoSubGrupo;
    }

    public ProcedimentoGrupo getProcedimentoGrupo() {
        return procedimentoGrupo;
    }

    public void setProcedimentoGrupo(ProcedimentoGrupo procedimentoGrupo) {
        this.procedimentoGrupo = procedimentoGrupo;
    }

    public boolean isApenasProcedimentosVinculadosPrestador() {
        return apenasProcedimentosVinculadosPrestador;
    }

    public void setApenasProcedimentosVinculadosPrestador(boolean apenasProcedimentosVinculadosPrestador) {
        this.apenasProcedimentosVinculadosPrestador = apenasProcedimentosVinculadosPrestador;
    }

    public ConsorcioPrestador getConsorcioPrestador() {
        return consorcioPrestador;
    }

    public void setConsorcioPrestador(ConsorcioPrestador consorcioPrestador) {
        this.consorcioPrestador = consorcioPrestador;
    }
}
