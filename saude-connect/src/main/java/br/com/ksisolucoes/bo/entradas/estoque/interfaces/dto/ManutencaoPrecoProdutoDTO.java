package br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ManutencaoPrecoProdutoDTO implements Serializable {

    private EstoqueEmpresa estoqueEmpresa;
    private Empresa estabelecimento;
    private Produto produto;
    private Double novoPreco;
    private String motivo;

    public EstoqueEmpresa getEstoqueEmpresa() {
        return estoqueEmpresa;
    }

    public void setEstoqueEmpresa(EstoqueEmpresa estoqueEmpresa) {
        this.estoqueEmpresa = estoqueEmpresa;
    }

    public Empresa getEstabelecimento() {
        return estabelecimento;
    }

    public void setEstabelecimento(Empresa estabelecimento) {
        this.estabelecimento = estabelecimento;
    }

    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    public Double getNovoPreco() {
        return novoPreco;
    }

    public void setNovoPreco(Double novoPreco) {
        this.novoPreco = novoPreco;
    }

    public String getMotivo() {
        return motivo;
    }

    public void setMotivo(String motivo) {
        this.motivo = motivo;
    }

}
