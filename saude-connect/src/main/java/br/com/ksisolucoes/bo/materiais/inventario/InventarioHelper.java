/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.materiais.inventario;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.estoque.EstoqueHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.Inventario;
import br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class InventarioHelper {

    public static List<LocalizacaoEstrutura> listaLocalizacaoInventario(Inventario inventario) throws ValidacaoException {
        String utilizaLocalizacaoEstoque = null;
        try {
            utilizaLocalizacaoEstoque = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("utilizaLocalizacaoEstoque");
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage());
        }

        if (RepositoryComponentDefault.NAO.equals(utilizaLocalizacaoEstoque)) {
            List<LocalizacaoEstrutura> localizacaoEstruturaList = new ArrayList<>();
            localizacaoEstruturaList.add(EstoqueHelper.getLocalizacaoEstruturaPadrao());
            return localizacaoEstruturaList;
        }

        if (inventario == null || inventario.getLocalizacaoEstrutura() == null || inventario.getLocalizacaoEstrutura().getDescricaoEstrutura() == null) {
            return null;
        }
        List<LocalizacaoEstrutura> list = LoadManager.getInstance(LocalizacaoEstrutura.class)
                .addProperties(new HQLProperties(LocalizacaoEstrutura.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LocalizacaoEstrutura.PROP_DESCRICAO_ESTRUTURA),
                        BuilderQueryCustom.QueryParameter.LIKE, inventario.getLocalizacaoEstrutura().getDescricaoEstrutura(), BuilderQueryCustom.MatchMode.START))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LocalizacaoEstrutura.PROP_CODIGO),
                        BuilderQueryCustom.QueryParameter.DIFERENTE, inventario.getLocalizacaoEstrutura().getCodigo()))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(list)) {
            return list;
        }
        return null;
    }

}
