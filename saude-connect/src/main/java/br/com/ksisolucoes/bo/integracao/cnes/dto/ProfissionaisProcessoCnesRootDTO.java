package br.com.ksisolucoes.bo.integracao.cnes.dto;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.List;

/**
 * Maicon
 */
@XmlRootElement(name = "ImportarXMLCNES")
@XmlAccessorType(XmlAccessType.FIELD)
public class ProfissionaisProcessoCnesRootDTO implements Serializable {

    @XmlElement(name = "IDENTIFICACA<PERSON>")
    private List<ProfissionaisProcessoCnesIdentificacaoDTO> dadosProfissionaisList;

    public List<ProfissionaisProcessoCnesIdentificacaoDTO> getDadosProfissionaisList() {
        return dadosProfissionaisList;
    }

    public void setDadosProfissionaisList(List<ProfissionaisProcessoCnesIdentificacaoDTO> dadosProfissionaisList) {
        this.dadosProfissionaisList = dadosProfissionaisList;
    }
}
