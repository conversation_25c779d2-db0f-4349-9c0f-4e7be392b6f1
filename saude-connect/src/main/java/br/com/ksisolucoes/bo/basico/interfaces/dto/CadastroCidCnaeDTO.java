package br.com.ksisolucoes.bo.basico.interfaces.dto;

import br.com.ksisolucoes.vo.basico.CidCerest;
import br.com.ksisolucoes.vo.basico.CidCerestCnae;


import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by jonas on 21/03/18.
 */
public class CadastroCidCnaeDTO implements Serializable {


    private CidCerest cidCerest;
    private CidCerestCnae cidCerestCnae;
    private List<CidCerestCnae> cidCerestCnaeList = new ArrayList<>();

    public CidCerest getCidCerest() {
        return cidCerest;
    }

    public void setCidCerest(CidCerest cidCerest) {
        this.cidCerest = cidCerest;
    }

    public CidCerestCnae getCidCerestCnae() {
        return cidCerestCnae;
    }

    public void setCidCerestCnae(CidCerestCnae cidCerestCnae) {
        this.cidCerestCnae = cidCerestCnae;
    }

    public List<CidCerestCnae> getCidCerestCnaeList() {
        return cidCerestCnaeList;
    }

    public void setCidCerestCnaeList(List<CidCerestCnae> cidCerestCnaeList) {
        this.cidCerestCnaeList = cidCerestCnaeList;
    }
}
