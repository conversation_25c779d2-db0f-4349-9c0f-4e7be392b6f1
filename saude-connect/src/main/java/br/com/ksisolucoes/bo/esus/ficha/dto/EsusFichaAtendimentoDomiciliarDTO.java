package br.com.ksisolucoes.bo.esus.ficha.dto;

import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendDomiciliar;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendDomiciliarItem;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendDomiciliarItemProcedimento;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class EsusFichaAtendimentoDomiciliarDTO implements Serializable {

    private List<EsusFichaAtendDomiciliar> esusFichaAtendDomiciliar;
    private List<EsusFichaAtendDomiciliarItem> lstItem;
    private List<EsusFichaAtendDomiciliarItemProcedimento> lstItemProcedimento;

    public List<EsusFichaAtendDomiciliar> getEsusFichaAtendDomiciliar() {
        return esusFichaAtendDomiciliar;
    }

    public void setEsusFichaAtendDomiciliar(List<EsusFichaAtendDomiciliar> esusFichaAtendDomiciliar) {
        this.esusFichaAtendDomiciliar = esusFichaAtendDomiciliar;
    }

    public List<EsusFichaAtendDomiciliarItem> getLstItem() {
        return lstItem;
    }

    public void setLstItem(List<EsusFichaAtendDomiciliarItem> lstItem) {
        this.lstItem = lstItem;
    }

    public List<EsusFichaAtendDomiciliarItemProcedimento> getLstItemProcedimento() {
        return lstItemProcedimento;
    }

    public void setLstItemProcedimento(List<EsusFichaAtendDomiciliarItemProcedimento> lstItemProcedimento) {
        this.lstItemProcedimento = lstItemProcedimento;
    }
}
