package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.TalonarioReceitaA;
import br.com.ksisolucoes.vo.vigilancia.TalonarioReceitaTalidomida;

import java.io.Serializable;
import java.util.List;

/**
 * Silvio
 */

public class TalonarioReceitaTalidomidaDTO implements Serializable {

    private TalonarioReceitaA talonarioReceitaA;
    private List<TalonarioReceitaTalidomida> receitaTalidomidaList;

    private Long nroTaloes;
    private Long nroReceitasTaloes;
    private Long numeracaoInicial;
    private Long numeracaoFinal;

    public TalonarioReceitaA getTalonarioReceitaA() {
        return talonarioReceitaA;
    }

    public void setTalonarioReceitaA(TalonarioReceitaA talonarioReceitaA) {
        this.talonarioReceitaA = talonarioReceitaA;
    }

    public List<TalonarioReceitaTalidomida> getReceitaTalidomidaList() {
        return receitaTalidomidaList;
    }

    public void setReceitaTalidomidaList(List<TalonarioReceitaTalidomida> receitaTalidomidaList) {
        this.receitaTalidomidaList = receitaTalidomidaList;
    }

    public Long getNroTaloes() {
        return nroTaloes;
    }

    public void setNroTaloes(Long nroTaloes) {
        this.nroTaloes = nroTaloes;
    }

    public Long getNroReceitasTaloes() {
        return nroReceitasTaloes;
    }

    public void setNroReceitasTaloes(Long nroReceitasTaloes) {
        this.nroReceitasTaloes = nroReceitasTaloes;
    }

    public Long getNumeracaoInicial() {
        return numeracaoInicial;
    }

    public void setNumeracaoInicial(Long numeracaoInicial) {
        this.numeracaoInicial = numeracaoInicial;
    }

    public Long getNumeracaoFinal() {
        return numeracaoFinal;
    }

    public void setNumeracaoFinal(Long numeracaoFinal) {
        this.numeracaoFinal = numeracaoFinal;
    }
}

