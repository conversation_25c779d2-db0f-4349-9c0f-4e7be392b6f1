package br.com.ksisolucoes.bo.entradas.devolucao.interfaces.dto;

import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.vo.entradas.estoque.CodigoBarrasProduto;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoGrupoEstoqueItemDTO;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItemComponente;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItemKit;
import br.com.ksisolucoes.vo.saidas.devolucao.DevolucaoMedicamentoItem;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class DevolucaoMedicamentoItemDTO implements Serializable{
    
    private DevolucaoMedicamentoItem devolucaoMedicamentoItem;
    private List<MovimentoGrupoEstoqueItemDTO> dtosLotes;
    private List<CodigoBarrasProduto> codigoBarrasProdutoList;
    private List<ReceituarioItem> lstReceituarioItem = new ArrayList<ReceituarioItem>();
    private List<ReceituarioItemComponente> lstReceituarioItemComponente = new ArrayList<ReceituarioItemComponente>();
    private List<ReceituarioItemKit> lstReceituarioItemKit = new ArrayList<ReceituarioItemKit>();

    public DevolucaoMedicamentoItem getDevolucaoMedicamentoItem() {
        return devolucaoMedicamentoItem;
    }

    public void setDevolucaoMedicamentoItem(DevolucaoMedicamentoItem devolucaoMedicamentoItem) {
        this.devolucaoMedicamentoItem = devolucaoMedicamentoItem;
    }

    public List<MovimentoGrupoEstoqueItemDTO> getDtosLotes() {
        return dtosLotes;
    }

    public void setDtosLotes(List<MovimentoGrupoEstoqueItemDTO> dtosLotes) {
        this.dtosLotes = dtosLotes;
    }

    public String getDescricaoLote() {
        String descricaoLote = "";
        if(CollectionUtils.isNotNullEmpty(getDtosLotes())){
            for (int i = 0; i < getDtosLotes().size(); i++) {
                MovimentoGrupoEstoqueItemDTO dto = getDtosLotes().get(i);

                descricaoLote += dto.getGrupoEstoque();
                if(i+1 < getDtosLotes().size()){
                    descricaoLote += " - ";
                }
            }
        }
        return descricaoLote;
    }

    public List<CodigoBarrasProduto> getCodigoBarrasProdutoList() {
        return codigoBarrasProdutoList;
    }

    public void setCodigoBarrasProdutoList(List<CodigoBarrasProduto> codigoBarrasProdutoList) {
        this.codigoBarrasProdutoList = codigoBarrasProdutoList;
    }

    public List<ReceituarioItem> getLstReceituarioItem() {
        return lstReceituarioItem;
    }

    public void setLstReceituarioItem(List<ReceituarioItem> lstReceituarioItem) {
        this.lstReceituarioItem = lstReceituarioItem;
    }

    public List<ReceituarioItemComponente> getLstReceituarioItemComponente() {
        return lstReceituarioItemComponente;
    }

    public void setLstReceituarioItemComponente(List<ReceituarioItemComponente> lstReceituarioItemComponente) {
        this.lstReceituarioItemComponente = lstReceituarioItemComponente;
    }

    public List<ReceituarioItemKit> getLstReceituarioItemKit() {
        return lstReceituarioItemKit;
    }

    public void setLstReceituarioItemKit(List<ReceituarioItemKit> lstReceituarioItemKit) {
        this.lstReceituarioItemKit = lstReceituarioItemKit;
    }
}
