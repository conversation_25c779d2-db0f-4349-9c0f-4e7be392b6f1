/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.AtividadeTerapeutica;
import br.com.ksisolucoes.vo.prontuario.DrogaUso;
import br.com.ksisolucoes.vo.prontuario.FichaAcolhimento;
import br.com.ksisolucoes.vo.prontuario.RelacaoFamiliarAcolhimento;
import br.com.ksisolucoes.vo.prontuario.Sintoma;
import br.com.ksisolucoes.vo.prontuario.UsoMedicacao;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class FichaAcolhimentoDTO implements Serializable{
  
    private FichaAcolhimento fichaAcolhimento;
    private String horaEntrada;
    private String horaSaida;
    private List<UsoMedicacao> lstUsoMedicacao = new ArrayList<UsoMedicacao>();
    private List<RelacaoFamiliarAcolhimento> lstFamiliarAco = new ArrayList<RelacaoFamiliarAcolhimento>();
    private List<DrogaUso> lstDroga = new ArrayList<DrogaUso>();
    private List<AtividadeTerapeutica> lstAtividade = new ArrayList<AtividadeTerapeutica>();
    private List<Sintoma> lstSintoma = new ArrayList<Sintoma>();

    public FichaAcolhimento getFichaAcolhimento() {
        return fichaAcolhimento;
    }

    public void setFichaAcolhimento(FichaAcolhimento fichaAcolhimento) {
        this.fichaAcolhimento = fichaAcolhimento;
    }

    public List<UsoMedicacao> getLstUsoMedicacao() {
        return lstUsoMedicacao;
    }

    public void setLstUsoMedicacao(List<UsoMedicacao> lstUsoMedicacao) {
        this.lstUsoMedicacao = lstUsoMedicacao;
    }

    public List<RelacaoFamiliarAcolhimento> getLstFamiliarAco() {
        return lstFamiliarAco;
    }

    public void setLstFamiliarAco(List<RelacaoFamiliarAcolhimento> lstFamiliarAco) {
        this.lstFamiliarAco = lstFamiliarAco;
    }

    public List<DrogaUso> getLstDroga() {
        return lstDroga;
    }

    public void setLstDroga(List<DrogaUso> lstDroga) {
        this.lstDroga = lstDroga;
    }

    public List<AtividadeTerapeutica> getLstAtividade() {
        return lstAtividade;
    }

    public void setLstAtividade(List<AtividadeTerapeutica> lstAtividade) {
        this.lstAtividade = lstAtividade;
    }

    public List<Sintoma> getLstSintoma() {
        return lstSintoma;
    }

    public void setLstSintoma(List<Sintoma> lstSintoma) {
        this.lstSintoma = lstSintoma;
    }

    public String getHoraEntrada() {
        return horaEntrada;
    }

    public void setHoraEntrada(String horaEntrada) {
        this.horaEntrada = horaEntrada;
    }

    public String getHoraSaida() {
        return horaSaida;
    }

    public void setHoraSaida(String horaSaida) {
        this.horaSaida = horaSaida;
    }
}
