/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.basico.dto;

import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class AutenticacaoUsuarioDTO implements Serializable{
    private Usuario usuario;
    private ValidacaoException exception;

    public Usuario getUsuario() throws ValidacaoException{
        if(exception != null){
            throw exception;
        }

        return usuario;
    }

    public void setException(ValidacaoException exception) {
        this.exception = exception;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

}
