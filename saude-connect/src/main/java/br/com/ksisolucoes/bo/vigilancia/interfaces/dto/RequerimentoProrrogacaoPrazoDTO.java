package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.EloRequerimentoVigilanciaSetorVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaFiscal;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProrrogacaoPrazo;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProrrogacaoPrazoItem;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RequerimentoProrrogacaoPrazoDTO implements Serializable {
    
    private RequerimentoProrrogacaoPrazo requerimentoProrrogacaoPrazo;
    private TipoSolicitacao tipoSolicitacao;
    private List<RequerimentoProrrogacaoPrazoItem> requerimentoProrrogacaoPrazoItemList = new ArrayList<>();
    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoExcluidoDTOList = new ArrayList<>();
    private List<EloRequerimentoVigilanciaSetorVigilancia> eloRequerimentoVigilanciaSetorVigilanciaList;
    private List<EloRequerimentoVigilanciaSetorVigilancia> eloRequerimentoVigilanciaSetorVigilanciaExcluirList;
    private List<RequerimentoVigilanciaFiscal> requerimentoVigilanciaFiscalList;
    private List<RequerimentoVigilanciaFiscal> requerimentoVigilanciaFiscalListExcluir;

    public RequerimentoProrrogacaoPrazoDTO() {
    }

    public RequerimentoProrrogacaoPrazoDTO(RequerimentoProrrogacaoPrazo requerimentoProrrogacaoPrazo) {
        this.requerimentoProrrogacaoPrazo = requerimentoProrrogacaoPrazo;
    }

    public RequerimentoProrrogacaoPrazo getRequerimentoProrrogacaoPrazo() {
        return requerimentoProrrogacaoPrazo;
    }

    public void setRequerimentoProrrogacaoPrazo(RequerimentoProrrogacaoPrazo requerimentoProrrogacaoPrazo) {
        this.requerimentoProrrogacaoPrazo = requerimentoProrrogacaoPrazo;
    }

    public TipoSolicitacao getTipoSolicitacao() {
        return tipoSolicitacao;
    }

    public void setTipoSolicitacao(TipoSolicitacao tipoSolicitacao) {
        this.tipoSolicitacao = tipoSolicitacao;
    }

    public List<RequerimentoProrrogacaoPrazoItem> getRequerimentoProrrogacaoPrazoItemList() {
        return requerimentoProrrogacaoPrazoItemList;
    }

    public void setRequerimentoProrrogacaoPrazoItemList(List<RequerimentoProrrogacaoPrazoItem> requerimentoProrrogacaoPrazoItemList) {
        this.requerimentoProrrogacaoPrazoItemList = requerimentoProrrogacaoPrazoItemList;
    }

    public List<RequerimentoVigilanciaAnexoDTO> getRequerimentoVigilanciaAnexoDTOList() {
        return requerimentoVigilanciaAnexoDTOList;
    }

    public void setRequerimentoVigilanciaAnexoDTOList(List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList) {
        this.requerimentoVigilanciaAnexoDTOList = requerimentoVigilanciaAnexoDTOList;
    }

    public List<RequerimentoVigilanciaAnexoDTO> getRequerimentoVigilanciaAnexoExcluidoDTOList() {
        return requerimentoVigilanciaAnexoExcluidoDTOList;
    }

    public void setRequerimentoVigilanciaAnexoExcluidoDTOList(List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoExcluidoDTOList) {
        this.requerimentoVigilanciaAnexoExcluidoDTOList = requerimentoVigilanciaAnexoExcluidoDTOList;
    }

    public List<EloRequerimentoVigilanciaSetorVigilancia> getEloRequerimentoVigilanciaSetorVigilanciaList() {
        return eloRequerimentoVigilanciaSetorVigilanciaList;
    }

    public void setEloRequerimentoVigilanciaSetorVigilanciaList(List<EloRequerimentoVigilanciaSetorVigilancia> eloRequerimentoVigilanciaSetorVigilanciaList) {
        this.eloRequerimentoVigilanciaSetorVigilanciaList = eloRequerimentoVigilanciaSetorVigilanciaList;
    }

    public List<EloRequerimentoVigilanciaSetorVigilancia> getEloRequerimentoVigilanciaSetorVigilanciaExcluirList() {
        return eloRequerimentoVigilanciaSetorVigilanciaExcluirList;
    }

    public void setEloRequerimentoVigilanciaSetorVigilanciaExcluirList(List<EloRequerimentoVigilanciaSetorVigilancia> eloRequerimentoVigilanciaSetorVigilanciaExcluirList) {
        this.eloRequerimentoVigilanciaSetorVigilanciaExcluirList = eloRequerimentoVigilanciaSetorVigilanciaExcluirList;
    }

    public List<RequerimentoVigilanciaFiscal> getRequerimentoVigilanciaFiscalList() {
        return requerimentoVigilanciaFiscalList;
    }

    public void setRequerimentoVigilanciaFiscalList(List<RequerimentoVigilanciaFiscal> requerimentoVigilanciaFiscalList) {
        this.requerimentoVigilanciaFiscalList = requerimentoVigilanciaFiscalList;
    }

    public List<RequerimentoVigilanciaFiscal> getRequerimentoVigilanciaFiscalListExcluir() {
        return requerimentoVigilanciaFiscalListExcluir;
    }

    public void setRequerimentoVigilanciaFiscalListExcluir(List<RequerimentoVigilanciaFiscal> requerimentoVigilanciaFiscalListExcluir) {
        this.requerimentoVigilanciaFiscalListExcluir = requerimentoVigilanciaFiscalListExcluir;
    }
}
