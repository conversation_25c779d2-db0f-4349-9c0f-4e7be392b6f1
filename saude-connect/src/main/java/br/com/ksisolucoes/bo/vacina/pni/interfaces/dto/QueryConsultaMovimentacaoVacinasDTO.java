package br.com.ksisolucoes.bo.vacina.pni.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.vacina.pni.MovimentacaoVacinasItem;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class QueryConsultaMovimentacaoVacinasDTO implements Serializable {

    private MovimentacaoVacinasItem movimentacaoVacinasItem;
    private Empresa empresa;

    private Long codigoApresentacao;
    private Long codigoProdutor;
    private Long codigoProdutoPni;

    public MovimentacaoVacinasItem getMovimentacaoVacinasItem() {
        return movimentacaoVacinasItem;
    }

    public void setMovimentacaoVacinasItem(MovimentacaoVacinasItem movimentacaoVacinasItem) {
        this.movimentacaoVacinasItem = movimentacaoVacinasItem;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Long getCodigoApresentacao() {
        return codigoApresentacao;
    }

    public void setCodigoApresentacao(Long codigoApresentacao) {
        this.codigoApresentacao = codigoApresentacao;
    }

    public Long getCodigoProdutor() {
        return codigoProdutor;
    }

    public void setCodigoProdutor(Long codigoProdutor) {
        this.codigoProdutor = codigoProdutor;
    }

    public Long getCodigoProdutoPni() {
        return codigoProdutoPni;
    }

    public void setCodigoProdutoPni(Long codigoProdutoPni) {
        this.codigoProdutoPni = codigoProdutoPni;
    }
}
