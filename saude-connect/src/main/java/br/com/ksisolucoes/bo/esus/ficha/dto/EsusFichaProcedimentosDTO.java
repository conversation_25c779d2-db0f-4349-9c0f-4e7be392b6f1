package br.com.ksisolucoes.bo.esus.ficha.dto;

import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaProcedimento;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaProcedimentoItem;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaProcedimentoItemSigtap;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class EsusFichaProcedimentosDTO implements Serializable {

    private List<EsusFichaProcedimento> lstFicha;
    private List<EsusFichaProcedimentoItem> lstItem;
    private List<EsusFichaProcedimentoItemSigtap> lstItemProcedimento = new ArrayList<>();

    public List<EsusFichaProcedimento> getLstFicha() {
        return lstFicha;
    }

    public void setLstFicha(List<EsusFichaProcedimento> lstFicha) {
        this.lstFicha = lstFicha;
    }

    public List<EsusFichaProcedimentoItem> getLstItem() {
        return lstItem;
    }

    public void setLstItem(List<EsusFichaProcedimentoItem> lstItem) {
        this.lstItem = lstItem;
    }

    public List<EsusFichaProcedimentoItemSigtap> getLstItemProcedimento() {
        return lstItemProcedimento;
    }

    public void setLstItemProcedimento(List<EsusFichaProcedimentoItemSigtap> lstItemProcedimento) {
        this.lstItemProcedimento = lstItemProcedimento;
    }

}
