package br.com.ksisolucoes.bo.entradas.estoque.produto;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.entradas.estoque.produto.dto.ProdutoBrasindiceDTOparam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaProdutoBrasindiceTreeProvider extends CommandQuery<QueryConsultaProdutoBrasindiceTreeProvider> {

    private ProdutoBrasindiceDTOparam param;
    private List<QueryConsultaProdutoBrasindiceTreeProviderDTO> result;

    public QueryConsultaProdutoBrasindiceTreeProvider(ProdutoBrasindiceDTOparam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {

//        hql.addToSelect("produtoBrasindiceItem", new HQLProperties(ProdutoBrasindiceItem.class, "produtoBrasindiceItem").getProperties());
//        hql.addToSelect("produtoBrasindice", new HQLProperties(ProdutoBrasindice.class, "produtoBrasindiceItem.produtoBrasindice").getProperties());
        hql.addToSelect("produtoBrasindiceItem.codigo", "produtoBrasindiceItem.codigo");
        hql.addToSelect("produtoBrasindiceItem.preco", "produtoBrasindiceItem.preco");
        hql.addToSelect("produtoBrasindiceItem.tipoPreco", "produtoBrasindiceItem.tipoPreco");
        hql.addToSelect("produtoBrasindiceItem.versaoBrasindice", "produtoBrasindiceItem.versaoBrasindice");
        hql.addToSelect("produtoBrasindiceItem.dataInicioVigencia", "produtoBrasindiceItem.dataInicioVigencia");

        hql.addToSelect("produtoBrasindice.codigo", "produtoBrasindiceItem.produtoBrasindice.codigo");
        hql.addToSelect("produtoBrasindice.laboratorio", "produtoBrasindiceItem.produtoBrasindice.laboratorio");
        hql.addToSelect("produtoBrasindice.descricaoLaboratorio", "produtoBrasindiceItem.produtoBrasindice.descricaoLaboratorio");
        hql.addToSelect("produtoBrasindice.medicamento", "produtoBrasindiceItem.produtoBrasindice.medicamento");
        hql.addToSelect("produtoBrasindice.descricaoMedicamento", "produtoBrasindiceItem.produtoBrasindice.descricaoMedicamento");
        hql.addToSelect("produtoBrasindice.apresentacao", "produtoBrasindiceItem.produtoBrasindice.apresentacao");
        hql.addToSelect("produtoBrasindice.descricaoApresentacao", "produtoBrasindiceItem.produtoBrasindice.descricaoApresentacao");
        hql.addToSelect("produtoBrasindice.codigoTiss", "produtoBrasindiceItem.produtoBrasindice.codigoTiss");
        hql.addToSelect("produtoBrasindice.codigoTuss", "produtoBrasindiceItem.produtoBrasindice.codigoTuss");
        hql.addToSelect("produtoBrasindice.keyword", "produtoBrasindiceItem.produtoBrasindice.keyword");

        hql.setTypeSelect(QueryConsultaProdutoBrasindiceTreeProviderDTO.class.getName());

        hql.addToFrom("ProdutoBrasindiceItem produtoBrasindiceItem"
                + " left join produtoBrasindiceItem.produtoBrasindice produtoBrasindice");

        hql.addToOrder("produtoBrasindice asc");
        hql.addToOrder("produtoBrasindiceItem.versaoBrasindice desc");
        hql.addToOrder("produtoBrasindiceItem.dataInicioVigencia desc");

        hql.addToWhereWhithAnd("produtoBrasindiceItem.dataInicioVigencia is not null");

        if (RepositoryComponentDefault.SIM_LONG.equals(param.getVinculoSistema())) {
            hql.addToWhereWhithAnd("exists(select 1 from EloProdutoBrasindice elo where elo.produtoBrasindice = produtoBrasindice)");
        }

        if (param.getProduto() != null) {
            hql.addToFrom("EloProdutoBrasindice epb "
                    + " left join epb.produto p");

            hql.addToWhereWhithAnd("epb.produtoBrasindice = produtoBrasindice");
            hql.addToWhereWhithAnd("epb.produto = ", param.getProduto());
        }

        if (param != null) {
            hql.addToWhereWhithAnd(hql.getConsultaLiked("produtoBrasindice.keyword", param.getKeyword()));
            hql.addToWhereWhithAnd(hql.getConsultaLiked("produtoBrasindice.codigoTiss", param.getTiss()));
            hql.addToWhereWhithAnd(hql.getConsultaLiked("produtoBrasindice.codigoTuss", param.getTuss()));
        }

    }

    public List<QueryConsultaProdutoBrasindiceTreeProviderDTO> getResult() {
        return this.result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

}
