package br.com.ksisolucoes.bo.consorcio.interfaces.dto;

import br.com.ksisolucoes.vo.consorcio.ConsorcioProcedimento;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaPrestadoresProcedimentosDTOParam implements Serializable{

    private List<ConsorcioProcedimento> procedimentos;
    private String utilizarEditalConsorcio;

    public List<ConsorcioProcedimento> getProcedimentos() {
        return procedimentos;
    }

    public void setProcedimentos(List<ConsorcioProcedimento> procedimentos) {
        this.procedimentos = procedimentos;
    }

    public String getUtilizarEditalConsorcio() {
        return utilizarEditalConsorcio;
    }

    public void setUtilizarEditalConsorcio(String utilizarEditalConsorcio) {
        this.utilizarEditalConsorcio = utilizarEditalConsorcio;
    }
}
