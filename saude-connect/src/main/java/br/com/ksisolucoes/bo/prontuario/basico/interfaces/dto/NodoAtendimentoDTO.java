package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.basico.EloNodoTipoExame;
import br.com.ksisolucoes.vo.prontuario.grupos.EloTipoAtendimentoGrupoAtendimentoCbo;
import br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> <PERSON> em: Jun 11, 2013
 */
public class NodoAtendimentoDTO implements Serializable {

    private NodesAtendimentoRef nodo;
    private String className;
    private String sexo;
    private Long permiteHistorico;
    private Long apenasMunicipio;
    private Long permiteCorrecao;
    private Long exibirAtendimento;
    private Long permiteAtendimentoParalelo;
    private Long informarProfissional;
    private Long informarEstabelecimentoOrigem;
    private Long flagPermiteReclassificacao;
    private String visivelUsuariosTemporarios;
    private GrupoAtendimentoCbo grupoProfissional;
    private List<EloTipoAtendimentoGrupoAtendimentoCbo> grupo;
    private List<Convenio> convenios;
    private List<EloNodoTipoExame> lstEloNodoTipoExame;

    public Long getInformarEstabelecimentoOrigem() {
        return informarEstabelecimentoOrigem;
    }

    public void setInformarEstabelecimentoOrigem(Long informarEstabelecimentoOrigem) {
        this.informarEstabelecimentoOrigem = informarEstabelecimentoOrigem;
    }

    public List<EloNodoTipoExame> getLstEloNodoTipoExame() {
        if (lstEloNodoTipoExame == null) {
            lstEloNodoTipoExame = new ArrayList<EloNodoTipoExame>();
        }
        return lstEloNodoTipoExame;
    }

    public void setLstEloNodoTipoExame(List<EloNodoTipoExame> lstEloNodoTipoExame) {
        this.lstEloNodoTipoExame = lstEloNodoTipoExame;
    }

    public Long getPermiteAtendimentoParalelo() {
        return permiteAtendimentoParalelo;
    }

    public void setPermiteAtendimentoParalelo(Long permiteAtendimentoParalelo) {
        this.permiteAtendimentoParalelo = permiteAtendimentoParalelo;
    }

    public Long getFlagPermiteReclassificacao() {
        return flagPermiteReclassificacao;
    }

    public Long getPermiteHistorico() {
        return permiteHistorico;
    }

    public void setPermiteHistorico(Long permiteHistorico) {
        this.permiteHistorico = permiteHistorico;
    }

    public String getFlagPermiteReclassificacaoDescricao() {
        if (RepositoryComponentDefault.SIM_LONG.equals(getFlagPermiteReclassificacao())) {
            return Bundle.getStringApplication("rotulo_sim");
        }
        return Bundle.getStringApplication("rotulo_nao");
    }

    public void setFlagPermiteReclassificacao(Long flagPermiteReclassificacao) {
        this.flagPermiteReclassificacao = flagPermiteReclassificacao;
    }

    public void setPermiteCorrecao(Long permiteCorrecao) {
        this.permiteCorrecao = permiteCorrecao;
    }

    public Long getPermiteCorrecao() {
        return permiteCorrecao;
    }

    public List<EloTipoAtendimentoGrupoAtendimentoCbo> getGrupo() {
        return grupo;
    }

    public void setGrupo(List<EloTipoAtendimentoGrupoAtendimentoCbo> grupo) {
        this.grupo = grupo;
    }

    public List<Convenio> getConvenios() {
        return convenios;
    }

    public void setConvenios(List<Convenio> convenios) {
        this.convenios = convenios;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public Long getApenasMunicipio() {
        return apenasMunicipio;
    }

    public void setApenasMunicipio(Long apenasMunicipio) {
        this.apenasMunicipio = apenasMunicipio;
    }
       
    public String getVisivelUsuariosTemporarios() {
        return visivelUsuariosTemporarios;
    }

    public Long getInformarProfissional() {
        return informarProfissional;
    }

    public void setInformarProfissional(Long informarProfissional) {
        this.informarProfissional = informarProfissional;
    }

    public void setVisivelUsuariosTemporarios(String visivelUsuariosTemporarios) {
        this.visivelUsuariosTemporarios = visivelUsuariosTemporarios;
    }

    public Long getExibirAtendimento() {
        return exibirAtendimento;
    }

    public void setExibirAtendimento(Long exibirAtendimento) {
        this.exibirAtendimento = exibirAtendimento;
    }

    public NodesAtendimentoRef getNodo() {
        return nodo;
    }

    public void setNodo(NodesAtendimentoRef nodo) {
        this.nodo = nodo;
    }
    
    public String getApenasMunicipioFormatado() {
        if (RepositoryComponentDefault.SIM_LONG.equals(getApenasMunicipio())) {
            return Bundle.getStringApplication("rotulo_sim");
        } else {
            return Bundle.getStringApplication("rotulo_nao");
        }
    }

    public GrupoAtendimentoCbo getGrupoProfissional() {
        return grupoProfissional;
    }

    public void setGrupoProfissional(GrupoAtendimentoCbo grupoProfissional) {
        this.grupoProfissional = grupoProfissional;
    }

    public String getSexoFormatado() {
        if (RepositoryComponentDefault.SEXO_FEMININO.equals(getSexo())) {
            return Bundle.getStringApplication("rotulo_feminino");
        } else if (RepositoryComponentDefault.SEXO_MASCULINO.equals(getSexo())) {
            return Bundle.getStringApplication("rotulo_masculino");
        } else if (RepositoryComponentDefault.SEXO_INDIFERENTE.equals(getSexo())) {
            return Bundle.getStringApplication("rotulo_ambos");
        }
        return null;
    }

    public String getVisivelUsuariosTemporariosFormatado() {
        if (RepositoryComponentDefault.SIM.equals(getVisivelUsuariosTemporarios())) {
            return Bundle.getStringApplication("rotulo_sim");
        } else if (RepositoryComponentDefault.NAO.equals(getVisivelUsuariosTemporarios())) {
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getPermiteAtendimentoParaleloFormatado() {
        if (RepositoryComponentDefault.SIM_LONG.equals(getPermiteAtendimentoParalelo())) {
            return Bundle.getStringApplication("rotulo_sim");
        } else if (RepositoryComponentDefault.NAO_LONG.equals(getPermiteAtendimentoParalelo())) {
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getPermiteCorrecaoFormatado() {
        if (RepositoryComponentDefault.SIM_LONG.equals(getPermiteCorrecao())) {
            return Bundle.getStringApplication("rotulo_sim");
        } else if (RepositoryComponentDefault.NAO_LONG.equals(getPermiteCorrecao())) {
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getExibirAtendimentoFormatado() {
        if (RepositoryComponentDefault.SIM_LONG.equals(getExibirAtendimento())) {
            return Bundle.getStringApplication("rotulo_sim");
        } else if (RepositoryComponentDefault.NAO_LONG.equals(getExibirAtendimento())) {
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getInformarProfissionalFormatado() {
        if (RepositoryComponentDefault.SIM_LONG.equals(getInformarProfissional())) {
            return Bundle.getStringApplication("rotulo_sim");
        } else if (RepositoryComponentDefault.NAO_LONG.equals(getInformarProfissional())) {
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }
    
    @Override
    public int hashCode() {
        int hash = 3;
        hash = 13 * hash + (this.nodo != null ? this.nodo.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final NodoAtendimentoDTO other = (NodoAtendimentoDTO) obj;
        if (this.nodo != other.nodo) {
            return false;
        }
        return true;
    }

}
