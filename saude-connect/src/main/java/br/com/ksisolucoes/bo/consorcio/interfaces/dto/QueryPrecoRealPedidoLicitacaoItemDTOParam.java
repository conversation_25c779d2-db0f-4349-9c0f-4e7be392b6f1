package br.com.ksisolucoes.bo.consorcio.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.Licitacao;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryPrecoRealPedidoLicitacaoItemDTOParam implements Serializable {

    private Empresa empresa;
    private Produto produto;
    private Licitacao licitacao;

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    public Licitacao getLicitacao() {
        return licitacao;
    }

    public void setLicitacao(Licitacao licitacao) {
        this.licitacao = licitacao;
    }
}
