package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.RelatorioInspecao;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;

import java.io.Serializable;

/**
 * Created by sulivan on 05/06/17.
 */
public class RelatorioInspecaoSanitariaDTOParam implements Serializable {

    private RequerimentoVigilancia requerimentoVigilancia;
    private RelatorioInspecao relatorioInspecao;

    public RelatorioInspecao getRelatorioInspecao() {
        return relatorioInspecao;
    }

    public void setRelatorioInspecao(RelatorioInspecao relatorioInspecao) {
        this.relatorioInspecao = relatorioInspecao;
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }

    public void setRequerimentoVigilancia(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }
}
