package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.basico.ConfiguracaoOrdenacaoConsultaAtendimento;
import java.io.Serializable;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class ConfiguracaoOrdenacaoConsultaAtendimentoDTO implements Serializable {
    
    private ConfiguracaoOrdenacaoConsultaAtendimento configuracaoOrdenacao;

    public ConfiguracaoOrdenacaoConsultaAtendimento getConfiguracaoOrdenacao() {
        return configuracaoOrdenacao;
    }

    public void setConfiguracaoOrdenacao(ConfiguracaoOrdenacaoConsultaAtendimento configuracaoOrdenacao) {
        this.configuracaoOrdenacao = configuracaoOrdenacao;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 97 * hash + Objects.hashCode(configuracaoOrdenacao.getEmpresa());
        hash = 97 * hash + Objects.hashCode(configuracaoOrdenacao.getCodigoColuna());
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ConfiguracaoOrdenacaoConsultaAtendimentoDTO other = (ConfiguracaoOrdenacaoConsultaAtendimentoDTO) obj;
        if (!Objects.equals(configuracaoOrdenacao.getEmpresa(), other.getConfiguracaoOrdenacao().getEmpresa())) {
            return false;
        }
        if (!Objects.equals(configuracaoOrdenacao.getCodigoColuna(), other.getConfiguracaoOrdenacao().getCodigoColuna())) {
            return false;
        }
        return true;
    }
    
}
