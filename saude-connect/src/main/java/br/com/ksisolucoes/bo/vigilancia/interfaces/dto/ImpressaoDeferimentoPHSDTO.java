package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaFiscal;
import br.com.ksisolucoes.vo.vigilancia.TipoProjetoRequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecer;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoDeferimentoPHSDTO implements Serializable {

    private String inscricoesImobiliarias;
    private String responsaveisTecnicos;
    private String descricaoTipoProjeto;
    private String descricaoObjetoAnalise;
    private String nomePessoa;
    private String nomeFantasia;
    private String endereco;
    private String bairro;
    private String cep;
    private String complemento;
    private String fone;
    private String atividadePrincipal;
    private String cpfCnpj;
    private String enderecoObra;
    private String descricaoRodape;
    private RequerimentoProjetoHidrossanitarioParecer requerimentoProjetoHidrossanitarioParecer;
    private RequerimentoVigilancia requerimentoVigilancia;
    private List<TipoProjetoRequerimentoVigilancia> tipoProjetoRequerimentoVigilanciaList;
    private List<RequerimentoVigilanciaFiscal> requerimentoVigilanciaFiscalList;
    private List<Profissional> fiscais;

    private List<InstalacaoHidrossanitariaEdificacaoDTO> instalacaoHidrossanitariaEdificacaoDTOList = new ArrayList<>();

    public String getInscricoesImobiliarias() {
        return inscricoesImobiliarias;
    }

    public void setInscricoesImobiliarias(String inscricoesImobiliarias) {
        this.inscricoesImobiliarias = inscricoesImobiliarias;
    }

    public String getResponsaveisTecnicos() {
        return responsaveisTecnicos;
    }

    public void setResponsaveisTecnicos(String responsaveisTecnicos) {
        this.responsaveisTecnicos = responsaveisTecnicos;
    }

    public String getNomePessoa() {
        return nomePessoa;
    }

    public void setNomePessoa(String nomePessoa) {
        this.nomePessoa = nomePessoa;
    }

    public String getNomeFantasia() {
        return nomeFantasia;
    }

    public void setNomeFantasia(String nomeFantasia) {
        this.nomeFantasia = nomeFantasia;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getFone() {
        return fone;
    }

    public void setFone(String fone) {
        this.fone = fone;
    }

    public String getAtividadePrincipal() {
        return atividadePrincipal;
    }

    public void setAtividadePrincipal(String atividadePrincipal) {
        this.atividadePrincipal = atividadePrincipal;
    }

    public String getCpfCnpj() {
        return cpfCnpj;
    }

    public void setCpfCnpj(String cpfCnpj) {
        this.cpfCnpj = cpfCnpj;
    }

    public RequerimentoProjetoHidrossanitarioParecer getRequerimentoProjetoHidrossanitarioParecer() {
        return requerimentoProjetoHidrossanitarioParecer;
    }

    public void setRequerimentoProjetoHidrossanitarioParecer(RequerimentoProjetoHidrossanitarioParecer requerimentoProjetoHidrossanitarioParecer) {
        this.requerimentoProjetoHidrossanitarioParecer = requerimentoProjetoHidrossanitarioParecer;
    }

    public List<RequerimentoVigilanciaFiscal> getRequerimentoVigilanciaFiscalList() {
        if (requerimentoVigilanciaFiscalList == null) {
            requerimentoVigilanciaFiscalList = new ArrayList<>();
        }
        return requerimentoVigilanciaFiscalList;
    }

    public void setRequerimentoVigilanciaFiscalList(List<RequerimentoVigilanciaFiscal> requerimentoVigilanciaFiscalList) {
        this.requerimentoVigilanciaFiscalList = requerimentoVigilanciaFiscalList;
    }

    public List<TipoProjetoRequerimentoVigilancia> getTipoProjetoRequerimentoVigilanciaList() {
        if(CollectionUtils.isEmpty(tipoProjetoRequerimentoVigilanciaList)){
            tipoProjetoRequerimentoVigilanciaList = new ArrayList<>();
        }
        return tipoProjetoRequerimentoVigilanciaList;
    }

    public void setTipoProjetoRequerimentoVigilanciaList(List<TipoProjetoRequerimentoVigilancia> tipoProjetoRequerimentoVigilanciaList) {
        this.tipoProjetoRequerimentoVigilanciaList = tipoProjetoRequerimentoVigilanciaList;
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }

    public void setRequerimentoVigilancia(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }

    public String getDescricaoTipoProjeto() {
        return descricaoTipoProjeto;
    }

    public void setDescricaoTipoProjeto(String descricaoTipoProjeto) {
        this.descricaoTipoProjeto = descricaoTipoProjeto;
    }

    public List<InstalacaoHidrossanitariaEdificacaoDTO> getInstalacaoHidrossanitariaEdificacaoDTOList() {
        return instalacaoHidrossanitariaEdificacaoDTOList;
    }

    public void setInstalacaoHidrossanitariaEdificacaoDTOList(List<InstalacaoHidrossanitariaEdificacaoDTO> instalacaoHidrossanitariaEdificacaoDTOList) {
        this.instalacaoHidrossanitariaEdificacaoDTOList = instalacaoHidrossanitariaEdificacaoDTOList;
    }


    public String getDescricaoObjetoAnalise() {
        return descricaoObjetoAnalise;
    }

    public void setDescricaoObjetoAnalise(String descricaoObjetoAnalise) {
        this.descricaoObjetoAnalise = descricaoObjetoAnalise;
    }

    public String getEnderecoObra() {
        return enderecoObra;
    }

    public void setEnderecoObra(String enderecoObra) {
        this.enderecoObra = enderecoObra;
    }

    public List<Profissional> getFiscais() {
        return fiscais;
    }

    public void setFiscais(List<Profissional> fiscais) {
        this.fiscais = fiscais;
    }

    public static class InstalacaoHidrossanitariaEdificacaoDTO implements Serializable {

        private String tipo;
        private String descricao;

        public String getTipo() {
            return tipo;
        }

        public void setTipo(String tipo) {
            this.tipo = tipo;
        }

        public String getDescricao() {
            return descricao;
        }

        public void setDescricao(String descricao) {
            this.descricao = descricao;
        }
    }

    public String getDescricaoRodape() {
        return descricaoRodape;
    }

    public void setDescricaoRodape(String descricaoRodape) {
        this.descricaoRodape = descricaoRodape;
    }
}
