package br.com.ksisolucoes.bo.hospital.interfaces.dto;

import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.vo.basico.Empresa;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaOcupacaoDiariaDTO implements Serializable {
    
    public static final String PROP_EMPRESA = "empresa";
    public static final String PROP_LEITOS_ATIVOS = "leitosAtivos";
    public static final String PROP_LEITOS_DISPONIVEIS = "leitosDisponiveis";
    public static final String PROP_LEITOS_OCUPADOS = "leitosOcupados";
    public static final String PROP_TAXA_OCUPACAO = "taxaOcupacao";
    public static final String PROP_MEDIA_PERMANENCIA = "mediaPermanencia";
    
    
    private Empresa empresa;
    private Long leitosAtivos;
    private Long leitosDisponiveis;
    private Long leitosOcupados;
    private Long taxaOcupacao;
    private Long mediaPermanencia;
    private Long tempoTotal;
    private Long numeroRegistros;

    public Long getTempoTotal() {
        return tempoTotal;
    }

    public void setTempoTotal(Long tempoTotal) {
        this.tempoTotal = tempoTotal;
    }

    public Long getNumeroRegistros() {
        return numeroRegistros;
    }

    public void setNumeroRegistros(Long numeroRegistros) {
        this.numeroRegistros = numeroRegistros;
    }
    
    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa descricaoSetor) {
        this.empresa = descricaoSetor;
    }

    public Long getLeitosAtivos() {
        return leitosAtivos;
    }

    public void setLeitosAtivos(Long leitosAtivos) {
        this.leitosAtivos = leitosAtivos;
    }

    public Long getLeitosDisponiveis() {
        return leitosDisponiveis;
    }

    public void setLeitosDisponiveis(Long leitosDisponiveis) {
        this.leitosDisponiveis = leitosDisponiveis;
    }

    public Long getLeitosOcupados() {
        return leitosOcupados;
    }

    public void setLeitosOcupados(Long leitosOcupados) {
        this.leitosOcupados = leitosOcupados;
    }

    public Long getTaxaOcupacao() {
        return taxaOcupacao;
    }

    public void setTaxaOcupacao(Long taxaOcupacao) {
        this.taxaOcupacao = taxaOcupacao;
    }

    public Long getMediaPermanencia() {
        return mediaPermanencia;
    }

    public void setMediaPermanencia(Long mediaPermanencia) {
        this.mediaPermanencia = mediaPermanencia;
    }
    
    public String descricaoTaxaOcupacao(){
        return calcularTaxaOcupacao() +" %";
    }
    
    public Double calcularTaxaOcupacao(){
        if(getLeitosAtivos() != null && getLeitosOcupados() != null && getLeitosAtivos() != 0){
            return new Dinheiro(getLeitosOcupados().longValue()).dividir(getLeitosAtivos().longValue()).multiplicar(100, 2).doubleValue();
        }
        
        return 0D;
    }
    
    public String descricaoMediaPermanencia(){
        return calcularMediaPermanencia() + " Dias";
    }
    
    public Long calcularMediaPermanencia(){
        Long retorno = 0L;
        if(getNumeroRegistros() > 0L){
            retorno = new Dinheiro(getTempoTotal().longValue()).dividir(getNumeroRegistros().longValue(), 0).longValue();
            if(retorno == 0L){
                return retorno;
            }            
            return new Dinheiro(retorno).dividir(86400).longValue();
        }
        return retorno;
    }
}