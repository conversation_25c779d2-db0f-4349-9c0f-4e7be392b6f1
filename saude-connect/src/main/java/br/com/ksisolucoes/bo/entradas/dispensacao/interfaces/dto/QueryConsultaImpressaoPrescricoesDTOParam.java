package br.com.ksisolucoes.bo.entradas.dispensacao.interfaces.dto;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaImpressaoPrescricoesDTOParam implements Serializable {

    private Long codigoReceituario;
    private String nomePaciente;
    private Empresa empresa;
    private Empresa empresaDispensacao;
    private boolean visualizarApenasPrescricaoEstabelecimento;
    private DatePeriod periodo;
    private Long tipoData;
    private Long impressao;
    private List<Long> lstStatus;
    private Date dataValidadePrescricao;
    private TipoReceita tipoReceita;
    private String propSort;
    private boolean ascending;

    public enum TipoData implements IEnum {

        DATA_CADASTRO(0L, Bundle.getStringApplication("rotulo_data_cadastro")),
        DATA_RECEITUARIO(1L, Bundle.getStringApplication("rotulo_data_prescricao"));

        private TipoData(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        private final Long value;
        private final String descricao;

        @Override
        public Object value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }

        public static TipoData valueOf(Long value) {
            for (TipoData tipoData : TipoData.values()) {
                if (tipoData.value().equals(value)) {
                    return tipoData;
                }
            }
            return null;
        }

    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public Long getTipoData() {
        return tipoData;
    }

    public void setTipoData(Long tipoData) {
        this.tipoData = tipoData;
    }

    public Long getImpressao() {
        return impressao;
    }

    public void setImpressao(Long impressao) {
        this.impressao = impressao;
    }

    public List<Long> getLstStatus() {
        return lstStatus;
    }

    public void setLstStatus(List<Long> lstStatus) {
        this.lstStatus = lstStatus;
    }

    public Date getDataValidadePrescricao() {
        return dataValidadePrescricao;
    }

    public void setDataValidadePrescricao(Date dataValidadePrescricao) {
        this.dataValidadePrescricao = dataValidadePrescricao;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

    public Long getCodigoReceituario() {
        return codigoReceituario;
    }

    public void setCodigoReceituario(Long codigoReceituario) {
        this.codigoReceituario = codigoReceituario;
    }

    public TipoReceita getTipoReceita() {
        return tipoReceita;
    }

    public void setTipoReceita(TipoReceita tipoReceita) {
        this.tipoReceita = tipoReceita;
    }

    public Empresa getEmpresaDispensacao() {
        return empresaDispensacao;
    }

    public void setEmpresaDispensacao(Empresa empresaDispensacao) {
        this.empresaDispensacao = empresaDispensacao;
    }

    public boolean isVisualizarApenasPrescricaoEstabelecimento() {
        return visualizarApenasPrescricaoEstabelecimento;
    }

    public void setVisualizarApenasPrescricaoEstabelecimento(boolean visualizarApenasPrescricaoEstabelecimento) {
        this.visualizarApenasPrescricaoEstabelecimento = visualizarApenasPrescricaoEstabelecimento;
    }
}
