package br.com.ksisolucoes.bo.materiais.compras.interfaces.dto;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.vo.entradas.estoque.AutorizacaoFornecimento;
import br.com.ksisolucoes.vo.entradas.estoque.AutorizacaoFornecimentoItem;
import br.com.ksisolucoes.vo.entradas.estoque.OrdemCompra;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AutorizacaoFornecimentoDTO implements Serializable{
    
    private OrdemCompra ordemCompra;
    private List<AutorizacaoFornecimentoItem> autorizacaoFornecimentoItemList = new ArrayList<AutorizacaoFornecimentoItem>();
    private AutorizacaoFornecimento autorizacaoFornecimento;
    private String numeroAF;

    public OrdemCompra getOrdemCompra() {
        return ordemCompra;
    }

    public void setOrdemCompra(OrdemCompra ordemCompra) {
        this.ordemCompra = ordemCompra;
    }

    public AutorizacaoFornecimento getAutorizacaoFornecimento() {
        return autorizacaoFornecimento;
    }

    public void setAutorizacaoFornecimento(AutorizacaoFornecimento autorizacaoFornecimento) {
        this.autorizacaoFornecimento = autorizacaoFornecimento;
    }

    public List<AutorizacaoFornecimentoItem> getAutorizacaoFornecimentoItemList() {
        if(CollectionUtils.isAllEmpty(autorizacaoFornecimentoItemList)){
            autorizacaoFornecimentoItemList = new ArrayList<>();
        }
        return autorizacaoFornecimentoItemList;
    }

    public void setAutorizacaoFornecimentoItemList(List<AutorizacaoFornecimentoItem> autorizacaoFornecimentoItemList) {
        this.autorizacaoFornecimentoItemList = autorizacaoFornecimentoItemList;
    }

    public String getNumeroAF() {
        return numeroAF;
    }

    public void setNumeroAF(String numeroAF) {
        this.numeroAF = numeroAF;
    }
}
