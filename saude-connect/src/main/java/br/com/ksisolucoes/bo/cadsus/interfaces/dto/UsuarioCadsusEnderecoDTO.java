package br.com.ksisolucoes.bo.cadsus.interfaces.dto;

import br.com.ksisolucoes.bo.dto.MensagemAnexoDTO;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDocumento;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEndereco;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEsus;
import br.com.ksisolucoes.vo.prontuario.basico.ConvenioPaciente;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class UsuarioCadsusEnderecoDTO implements Serializable {

    public static final String PROP_USUARIO_CADSUS_ENDERECO = "usuarioCadsusEndereco";
    public static final String PROP_NUMERO_PRONTUARIO = "numeroProntuario";

    private UsuarioCadsusEndereco usuarioCadsusEndereco;
    private List<UsuarioCadsusDocumento> documentos;
    private String numeroProntuario;
    private List<UsuarioCadsusCns> cartoes;
    private boolean validarDocumentosObrigatorios = true;
    private boolean recemNascido;
    private List<ConvenioPaciente> lstConvenioPaciente;
    private UsuarioCadsusEsus usuarioCadsusEsus;
    private UsuarioCadsus usuarioCadsus;
    private List<Long> tipoDocumentos;
    private List<AcompanhanteUsuarioCadsusHospitalDTO> acompanhanteUsuarioCadsusHospitalDTOList;
    private List<AcompanhanteUsuarioCadsusHospitalDTO> acompanhanteUsuarioCadsusHospitalRemovidosDTOList;
    private MensagemAnexoDTO anexoDTO;
    private boolean cadastro;
    private String motivoNovoEndereco;

    public boolean isValidarDocumentosObrigatorios() {
        return validarDocumentosObrigatorios;
    }

    public void setValidarDocumentosObrigatorios(boolean validarDocumentosObrigatorios) {
        this.validarDocumentosObrigatorios = validarDocumentosObrigatorios;
    }

    public boolean isRecemNascido() {
        return recemNascido;
    }

    public void setRecemNascido(boolean recemNascido) {
        this.recemNascido = recemNascido;
    }

    public List<UsuarioCadsusCns> getCartoes() {
        return cartoes;
    }

    public void setCartoes(List<UsuarioCadsusCns> cartoes) {
        this.cartoes = cartoes;
    }

    public List<UsuarioCadsusDocumento> getDocumentos() {
        return documentos;
    }

    public void setDocumentos(List<UsuarioCadsusDocumento> documentos) {
        this.documentos = documentos;
    }

    public String getNumeroProntuario() {
        return numeroProntuario;
    }

    public void setNumeroProntuario(String numeroProntuario) {
        this.numeroProntuario = numeroProntuario;
    }

    public UsuarioCadsusEndereco getUsuarioCadsusEndereco() {
        return usuarioCadsusEndereco;
    }

    public void setUsuarioCadsusEndereco(UsuarioCadsusEndereco usuarioCadsusEndereco) {
        this.usuarioCadsusEndereco = usuarioCadsusEndereco;
    }

    public List<ConvenioPaciente> getLstConvenioPaciente() {
        return lstConvenioPaciente;
    }

    public void setLstConvenioPaciente(List<ConvenioPaciente> lstConvenioPaciente) {
        this.lstConvenioPaciente = lstConvenioPaciente;
    }

    public UsuarioCadsusEsus getUsuarioCadsusEsus() {
        return usuarioCadsusEsus;
    }

    public void setUsuarioCadsusEsus(UsuarioCadsusEsus usuarioCadsusEsus) {
        this.usuarioCadsusEsus = usuarioCadsusEsus;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public List<Long> getTipoDocumentos() {
        return tipoDocumentos;
    }

    public void setTipoDocumentos(List<Long> tipoDocumentos) {
        this.tipoDocumentos = tipoDocumentos;
    }

    public List<AcompanhanteUsuarioCadsusHospitalDTO> getAcompanhanteUsuarioCadsusHospitalDTOList() {
        return acompanhanteUsuarioCadsusHospitalDTOList;
    }

    public void setAcompanhanteUsuarioCadsusHospitalDTOList(List<AcompanhanteUsuarioCadsusHospitalDTO> acompanhanteUsuarioCadsusHospitalDTOList) {
        this.acompanhanteUsuarioCadsusHospitalDTOList = acompanhanteUsuarioCadsusHospitalDTOList;
    }

    public List<AcompanhanteUsuarioCadsusHospitalDTO> getAcompanhanteUsuarioCadsusHospitalRemovidosDTOList() {
        return acompanhanteUsuarioCadsusHospitalRemovidosDTOList;
    }

    public void setAcompanhanteUsuarioCadsusHospitalRemovidosDTOList(List<AcompanhanteUsuarioCadsusHospitalDTO> acompanhanteUsuarioCadsusHospitalRemovidosDTOList) {
        this.acompanhanteUsuarioCadsusHospitalRemovidosDTOList = acompanhanteUsuarioCadsusHospitalRemovidosDTOList;
    }

    public MensagemAnexoDTO getAnexoDTO() {
        return anexoDTO;
    }

    public void setAnexoDTO(MensagemAnexoDTO anexoDTO) {
        this.anexoDTO = anexoDTO;
    }

    public boolean isCadastro() {
        return cadastro;
    }

    public void setCadastro(boolean cadastro) {
        this.cadastro = cadastro;
    }

    public String getMotivoNovoEndereco() {
        return motivoNovoEndereco;
    }

    public void setMotivoNovoEndereco(String motivoNovoEndereco) {
        this.motivoNovoEndereco = motivoNovoEndereco;
    }
}
