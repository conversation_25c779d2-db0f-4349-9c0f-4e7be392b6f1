package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoAutoExternoDTO implements Serializable {

    public static enum Tipo {
        INFRACAO,
        INTIMACAO,
        MULTA,
        PENALIDADE;
    }

    private Long codigo;
    private Tipo tipo;
    private String numero;
    private boolean recebido;

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public Tipo getTipo() {
        return tipo;
    }

    public void setTipo(Tipo tipo) {
        this.tipo = tipo;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public boolean isRecebido() {
        return recebido;
    }

    public void setRecebido(boolean recebido) {
        this.recebido = recebido;
    }
}
