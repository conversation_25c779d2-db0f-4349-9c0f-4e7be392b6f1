package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoOcorrencia;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ProcessoAdministrativoDTO implements Serializable {
    private ProcessoAdministrativo processoAdministrativo;
    private List<ProcessoAdministrativoOcorrencia> processoAdministrativoOcorrencia = new ArrayList();
//    private List<ProcessoAdministrativoDecisaoDTO> decisaoDTOList = new ArrayList();
//    private List<ProcessoAdministrativoParecerDTO> parecerDTOList = new ArrayList();
//    private List<RequerimentoVigilanciaAnexoDTO> anexoDTOList =  new ArrayList();

    public ProcessoAdministrativoDTO(ProcessoAdministrativo processoAdministrativo) {
        this.processoAdministrativo = processoAdministrativo;
    }

    public ProcessoAdministrativo getProcessoAdministrativo() {
        return processoAdministrativo;
    }

//    public void setProcessoAdministrativo(ProcessoAdministrativo processoAdministrativo) {
//        this.processoAdministrativo = processoAdministrativo;
//    }
//
//    public List<ProcessoAdministrativoDecisaoDTO> getDecisaoDTOList() {
//        return decisaoDTOList;
//    }
//
//    public void setDecisaoDTOList(List<ProcessoAdministrativoDecisaoDTO> decisaoDTOList) {
//        this.decisaoDTOList = decisaoDTOList;
//    }
//
//    public List<ProcessoAdministrativoParecerDTO> getParecerDTOList() {
//        return parecerDTOList;
//    }
//
//    public void setParecerDTOList(List<ProcessoAdministrativoParecerDTO> parecerDTOList) {
//        this.parecerDTOList = parecerDTOList;
//    }
//
//    public List<RequerimentoVigilanciaAnexoDTO> getAnexoDTOList() {
//        return anexoDTOList;
//    }
//
//    public void setAnexoDTOList(List<RequerimentoVigilanciaAnexoDTO> anexoDTOList) {
//        this.anexoDTOList = anexoDTOList;
//    }

    public List<ProcessoAdministrativoOcorrencia> getProcessoAdministrativoOcorrencia() {
        return processoAdministrativoOcorrencia;
    }

    public void setProcessoAdministrativoOcorrencia(List<ProcessoAdministrativoOcorrencia> processoAdministrativoOcorrencia) {
        this.processoAdministrativoOcorrencia = processoAdministrativoOcorrencia;
    }
}