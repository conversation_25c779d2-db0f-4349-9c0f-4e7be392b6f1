package br.com.ksisolucoes.bo.materiais.horus.interfaces.dto;

import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class ConsultaDadosEnviadosHorusDTOParam implements Serializable{
    
    private Long tipoSincronizacao;
    private Long status;
    private Empresa Estabelecimento;
    private Produto produto;
    private UsuarioCadsus paciente;
    private Date competencia;
    private SincronizacaoHorusProcesso sincronizacaoHorusProcesso;
    private String campoOrdenacao;
    private boolean isAscending;
    
    public ConsultaDadosEnviadosHorusDTOParam(SincronizacaoHorusProcesso sincronizacaoHorusProcesso) {
        this.sincronizacaoHorusProcesso = sincronizacaoHorusProcesso;
    }
    public ConsultaDadosEnviadosHorusDTOParam(){
      
    }
    public Long getTipoSincronizacao() {
        return tipoSincronizacao;
    }

    public void setTipoSincronizacao(Long tipoSincronizacao) {
        this.tipoSincronizacao = tipoSincronizacao;
    }

    public Empresa getEstabelecimento() {
        return Estabelecimento;
    }

    public void setEstabelecimento(Empresa Estabelecimento) {
        this.Estabelecimento = Estabelecimento;
    }

    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    public UsuarioCadsus getPaciente() {
        return paciente;
    }

    public void setPaciente(UsuarioCadsus paciente) {
        this.paciente = paciente;
    }

    public Date getCompetencia() {
        return competencia;
    }

    public void setCompetencia(Date competencia) {
        this.competencia = competencia;
    }

    public SincronizacaoHorusProcesso getSincronizacaoHorusProcesso() {
        return sincronizacaoHorusProcesso;
    }

    public void setSincronizacaoHorusProcesso(SincronizacaoHorusProcesso sincronizacaoHorusProcesso) {
        this.sincronizacaoHorusProcesso = sincronizacaoHorusProcesso;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public String getCampoOrdenacao() {
        return campoOrdenacao;
    }

    public void setCampoOrdenacao(String campoOrdenacao) {
        this.campoOrdenacao = campoOrdenacao;
    }

    public boolean isAscending() {
        return isAscending;
    }

    public void setIsAscending(boolean isAscending) {
        this.isAscending = isAscending;
    }
}