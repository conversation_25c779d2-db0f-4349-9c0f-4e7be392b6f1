package br.com.ksisolucoes.bo.integracao.cnes.dto;

import br.com.ksisolucoes.xml.util.DateAdapter;
import br.com.ksisolucoes.xml.util.LongAdapter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * Created by sulivan on 16/06/17.
 */
@XmlAccessorType(XmlAccessType.FIELD)
public class CnesProcessoEquipesDadosDTO implements Serializable {

    public CnesProcessoEquipesDadosDTO() {
    }

    @XmlAttribute(name = "COD_MUN")
    private String codigoMunicipio;
    @XmlAttribute(name = "COD_AREA")
    private String codigoArea;
    @XmlAttribute(name = "SEQ_EQUIPE")
    @XmlJavaTypeAdapter(LongAdapter.class)
    private Long sequencialEquipe;
    @XmlAttribute(name = "DS_AREA")
    private String descricaoArea;
    @XmlAttribute(name = "UNIDADE_ID")
    private String unidadeId;
    @XmlAttribute(name = "TP_EQUIPE")
    private String tipoEquipe;
    @XmlAttribute(name = "DS_EQUIPE")
    private String descricaoEquipe;
    @XmlAttribute(name = "NM_REFERENCIA")
    private String nomeReferencia;
    @XmlAttribute(name = "CD_SEGMENTO")
    private String codigoSegmento;
    @XmlAttribute(name = "DS_SEGMENTO")
    private String descricaoSegmento;
    @XmlAttribute(name = "TP_SEGMENTO")
    private String tipoSegmento;
    @XmlAttribute(name = "TP_POP_ASSIST_QUILOMB")
    private String tipoPopulacaoAssistidaQuilombola;
    @XmlAttribute(name = "TP_POP_ASSIST_ASSENT")
    private String tipoPopulacaoAssistidaAssentado;
    @XmlAttribute(name = "TP_POP_ASSIST_GERAL")
    private String tipoPopulacaoAssistidaGeral;
    @XmlAttribute(name = "TP_POP_ASSIST_ESCOLA")
    private String tipoPopulacaoAssistidaEscola;
    @XmlAttribute(name = "TP_POP_ASSIST_PRONASCI")
    private String tipoPopulacaoAssistidaPronasci;
    @XmlAttribute(name = "DT_ATIVACAO")
    @XmlJavaTypeAdapter(DateAdapter.class)
    private Date dataAtivacao;
    @XmlAttribute(name = "DT_DESATIVACAO")
    @XmlJavaTypeAdapter(DateAdapter.class)
    private Date dataDesativacao;
    @XmlAttribute(name = "CD_TIPO_DESATIV")
    private String codigoTipoDesativacao;
    @XmlAttribute(name = "CD_MOTIVO_DESATIV")
    private String codigoMotivoDesativacao;
    @XmlAttribute(name = "USUARIO")
    private String usuario;
    @XmlAttribute(name = "CO_EQUIPE")
    private String codigoEquipe;
    @XmlElement(name = "PROF_EQUIPE")
    private CnesProcessoEquipeProfissionalDTO equipeProfissional;

    public String getCodigoMunicipio() {
        return codigoMunicipio;
    }

    public void setCodigoMunicipio(String codigoMunicipio) {
        this.codigoMunicipio = codigoMunicipio;
    }

    public String getCodigoArea() {
        return codigoArea;
    }

    public void setCodigoArea(String codigoArea) {
        this.codigoArea = codigoArea;
    }

    public Long getSequencialEquipe() {
        return sequencialEquipe;
    }

    public void setSequencialEquipe(Long sequencialEquipe) {
        this.sequencialEquipe = sequencialEquipe;
    }

    public String getDescricaoArea() {
        return descricaoArea;
    }

    public void setDescricaoArea(String descricaoArea) {
        this.descricaoArea = descricaoArea;
    }

    public String getUnidadeId() {
        return unidadeId;
    }

    public void setUnidadeId(String unidadeId) {
        this.unidadeId = unidadeId;
    }

    public String getTipoEquipe() {
        return tipoEquipe;
    }

    public void setTipoEquipe(String tipoEquipe) {
        this.tipoEquipe = tipoEquipe;
    }

    public String getDescricaoEquipe() {
        return descricaoEquipe;
    }

    public void setDescricaoEquipe(String descricaoEquipe) {
        this.descricaoEquipe = descricaoEquipe;
    }

    public String getNomeReferencia() {
        return nomeReferencia;
    }

    public void setNomeReferencia(String nomeReferencia) {
        this.nomeReferencia = nomeReferencia;
    }

    public String getCodigoSegmento() {
        return codigoSegmento;
    }

    public void setCodigoSegmento(String codigoSegmento) {
        this.codigoSegmento = codigoSegmento;
    }

    public String getDescricaoSegmento() {
        return descricaoSegmento;
    }

    public void setDescricaoSegmento(String descricaoSegmento) {
        this.descricaoSegmento = descricaoSegmento;
    }

    public String getTipoSegmento() {
        return tipoSegmento;
    }

    public void setTipoSegmento(String tipoSegmento) {
        this.tipoSegmento = tipoSegmento;
    }

    public String getTipoPopulacaoAssistidaQuilombola() {
        return tipoPopulacaoAssistidaQuilombola;
    }

    public void setTipoPopulacaoAssistidaQuilombola(String tipoPopulacaoAssistidaQuilombola) {
        this.tipoPopulacaoAssistidaQuilombola = tipoPopulacaoAssistidaQuilombola;
    }

    public String getTipoPopulacaoAssistidaAssentado() {
        return tipoPopulacaoAssistidaAssentado;
    }

    public void setTipoPopulacaoAssistidaAssentado(String tipoPopulacaoAssistidaAssentado) {
        this.tipoPopulacaoAssistidaAssentado = tipoPopulacaoAssistidaAssentado;
    }

    public String getTipoPopulacaoAssistidaGeral() {
        return tipoPopulacaoAssistidaGeral;
    }

    public void setTipoPopulacaoAssistidaGeral(String tipoPopulacaoAssistidaGeral) {
        this.tipoPopulacaoAssistidaGeral = tipoPopulacaoAssistidaGeral;
    }

    public String getTipoPopulacaoAssistidaEscola() {
        return tipoPopulacaoAssistidaEscola;
    }

    public void setTipoPopulacaoAssistidaEscola(String tipoPopulacaoAssistidaEscola) {
        this.tipoPopulacaoAssistidaEscola = tipoPopulacaoAssistidaEscola;
    }

    public String getTipoPopulacaoAssistidaPronasci() {
        return tipoPopulacaoAssistidaPronasci;
    }

    public void setTipoPopulacaoAssistidaPronasci(String tipoPopulacaoAssistidaPronasci) {
        this.tipoPopulacaoAssistidaPronasci = tipoPopulacaoAssistidaPronasci;
    }

    public Date getDataAtivacao() {
        return dataAtivacao;
    }

    public void setDataAtivacao(Date dataAtivacao) {
        this.dataAtivacao = dataAtivacao;
    }

    public Date getDataDesativacao() {
        return dataDesativacao;
    }

    public void setDataDesativacao(Date dataDesativacao) {
        this.dataDesativacao = dataDesativacao;
    }

    public String getCodigoTipoDesativacao() {
        return codigoTipoDesativacao;
    }

    public void setCodigoTipoDesativacao(String codigoTipoDesativacao) {
        this.codigoTipoDesativacao = codigoTipoDesativacao;
    }

    public String getCodigoMotivoDesativacao() {
        return codigoMotivoDesativacao;
    }

    public void setCodigoMotivoDesativacao(String codigoMotivoDesativacao) {
        this.codigoMotivoDesativacao = codigoMotivoDesativacao;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getCodigoEquipe() {
        return codigoEquipe;
    }

    public void setCodigoEquipe(String codigoEquipe) {
        this.codigoEquipe = codigoEquipe;
    }

    public CnesProcessoEquipeProfissionalDTO getEquipeProfissional() {
        return equipeProfissional;
    }

    public void setEquipeProfissional(CnesProcessoEquipeProfissionalDTO equipeProfissional) {
        this.equipeProfissional = equipeProfissional;
    }

    @Override
    public int hashCode() {
        return Objects.hash(codigoEquipe);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CnesProcessoEquipesDadosDTO objeto = (CnesProcessoEquipesDadosDTO) o;
        return codigoEquipe == objeto.codigoEquipe;
    }
}