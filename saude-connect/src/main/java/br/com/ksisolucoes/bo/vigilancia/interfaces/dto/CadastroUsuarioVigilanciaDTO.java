package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.externo.UsuarioVigilancia;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class CadastroUsuarioVigilanciaDTO implements Serializable {
    private UsuarioVigilancia usuarioVigilancia;
    private String cpf;
    private String confirmacaoSenha;
    private String url;

    public CadastroUsuarioVigilanciaDTO() {
        setUsuarioVigilancia(new UsuarioVigilancia());
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public UsuarioVigilancia getUsuarioVigilancia() {
        return usuarioVigilancia;
    }

    public void setUsuarioVigilancia(UsuarioVigilancia usuarioVigilancia) {
        this.usuarioVigilancia = usuarioVigilancia;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }


    public String getConfirmacaoSenha() {
        return confirmacaoSenha;
    }

    public void setConfirmacaoSenha(String confirmacaoSenha) {
        this.confirmacaoSenha = confirmacaoSenha;
    }
}
