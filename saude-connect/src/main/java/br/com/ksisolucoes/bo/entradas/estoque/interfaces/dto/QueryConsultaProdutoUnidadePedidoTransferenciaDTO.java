/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto;

import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaProdutoUnidadePedidoTransferenciaDTO implements Serializable {

    public static final String PROP_PRODUTO = "produto";
    public static final String PROP_QUANTIDADE_PADRAO_DISPENSASAO = "quantidadePadraoDispensacao";
    public static final String PROP_QUANTIDADE_CONSUMO = "quantidadeConsumo";
    public static final String PROP_ESTOQUE_FISICO = "estoqueFisico";
    public static final String PROP_DISPONIVEL = "disponivel";
    public static final String PROP_ESTOQUE_DISPONIVEL_EMPRESA_SESSAO = "estoqueDisponivelEmpresaSessao";

    private Produto produto;
    private Double quantidadePadraoDispensacao;
    private Double quantidadeConsumo;
    private Double estoqueFisico;
    private Double disponivel;

    private Double estoqueFisicoEmpresaSessao;
    private Double estoqueEncomendadoEmpresaSessao;
    private Double estoqueReservadoEmpresaSessao;

    public Double getDisponivel() {
        return Coalesce.asDouble(disponivel);
    }

    public void setDisponivel(Double disponivel) {
        this.disponivel = disponivel;
    }

    public Double getEstoqueEncomendadoEmpresaSessao() {
        return Coalesce.asDouble(estoqueEncomendadoEmpresaSessao);
    }

    public void setEstoqueEncomendadoEmpresaSessao(Double estoqueEncomendadoEmpresaSessao) {
        this.estoqueEncomendadoEmpresaSessao = estoqueEncomendadoEmpresaSessao;
    }

    public Double getEstoqueFisico() {
        return Coalesce.asDouble(estoqueFisico);
    }

    public void setEstoqueFisico(Double estoqueFisico) {
        this.estoqueFisico = estoqueFisico;
    }

    public Double getEstoqueFisicoEmpresaSessao() {
        return Coalesce.asDouble(estoqueFisicoEmpresaSessao);
    }

    public void setEstoqueFisicoEmpresaSessao(Double estoqueFisicoEmpresaSessao) {
        this.estoqueFisicoEmpresaSessao = estoqueFisicoEmpresaSessao;
    }

    public Double getEstoqueReservadoEmpresaSessao() {
        return Coalesce.asDouble(estoqueReservadoEmpresaSessao);
    }

    public void setEstoqueReservadoEmpresaSessao(Double estoqueReservadoEmpresaSessao) {
        this.estoqueReservadoEmpresaSessao = estoqueReservadoEmpresaSessao;
    }

    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    public Double getQuantidadeConsumo() {
        return Coalesce.asDouble(quantidadeConsumo);
    }

    public void setQuantidadeConsumo(Double quantidadeConsumo) {
        this.quantidadeConsumo = quantidadeConsumo;
    }

    public Double getQuantidadePadraoDispensacao() {
        return Coalesce.asDouble(quantidadePadraoDispensacao);
    }

    public void setQuantidadePadraoDispensacao(Double quantidadePadraoDispensacao) {
        this.quantidadePadraoDispensacao = quantidadePadraoDispensacao;
    }

    public Double getEstoqueDisponivelEmpresaSessao(){
        return EstoqueEmpresa.getCalculoEstoqueDisponivel(estoqueFisicoEmpresaSessao, estoqueEncomendadoEmpresaSessao, estoqueReservadoEmpresaSessao);
    }

}
