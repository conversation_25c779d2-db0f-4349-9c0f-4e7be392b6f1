package br.com.ksisolucoes.agendamento.exame.dto;

import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.agendamento.TipoAtendimentoAgenda;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CadastroNovaAgendaDTO implements Serializable {
    
    private Date dataAgenda;
    private Date horaInicio;
    private TipoAtendimentoAgenda tipoAtendimentoAgenda;
    private Long tempoMedio;
    private Long reabrirAgenda;
    private Long quantidadeAtendimento;
    private String motivo;
    private List<AgendaGradeAtendimentoHorario> agendaGradeAtendimentoHorarioList;

    public Date getDataAgenda() {
        return dataAgenda;
    }

    public void setDataAgenda(Date dataAgenda) {
        this.dataAgenda = dataAgenda;
    }

    public Date getHoraInicio() {
        return horaInicio;
    }

    public void setHoraInicio(Date horaInicio) {
        this.horaInicio = horaInicio;
    }

    public TipoAtendimentoAgenda getTipoAtendimentoAgenda() {
        return tipoAtendimentoAgenda;
    }

    public void setTipoAtendimentoAgenda(TipoAtendimentoAgenda tipoAtendimentoAgenda) {
        this.tipoAtendimentoAgenda = tipoAtendimentoAgenda;
    }

    public Long getTempoMedio() {
        return tempoMedio;
    }

    public void setTempoMedio(Long tempoMedio) {
        this.tempoMedio = tempoMedio;
    }
    
    public Long getReabrirAgenda() {
        return reabrirAgenda;
    }

    public void setReabrirAgenda(Long reabrirAgenda) {
        this.reabrirAgenda = reabrirAgenda;
    }

    public String getMotivo() {
        return motivo;
    }

    public void setMotivo(String motivo) {
        this.motivo = motivo;
    }

    public List<AgendaGradeAtendimentoHorario> getAgendaGradeAtendimentoHorarioList() {
        return agendaGradeAtendimentoHorarioList;
    }

    public void setAgendaGradeAtendimentoHorarioList(List<AgendaGradeAtendimentoHorario> agendaGradeAtendimentoHorarioList) {
        this.agendaGradeAtendimentoHorarioList = agendaGradeAtendimentoHorarioList;
    }

    public Long getQuantidadeAtendimento() {
        return quantidadeAtendimento;
    }

    public void setQuantidadeAtendimento(Long quantidadeAtendimento) {
        this.quantidadeAtendimento = quantidadeAtendimento;
    }
    
}
