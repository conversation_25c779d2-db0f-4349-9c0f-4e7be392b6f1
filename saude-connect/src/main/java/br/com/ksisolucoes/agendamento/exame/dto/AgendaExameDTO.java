package br.com.ksisolucoes.agendamento.exame.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeHorario;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class AgendaExameDTO implements Serializable {
    
    private Date data;
    private Date horaInicial;
    private Long vagasDisponiveis;
    private AgendaGradeHorario agendaGradeHorario;

    private AgendaExameDTO() {
    }

    public static AgendaExameDTO build() {
        return new AgendaExameDTO();
    }

    public Date getData() {
        return data;
    }

    public AgendaExameDTO setData(Date data) {
        this.data = data;
        return this;
    }

    public Date getHoraInicial() {
        return horaInicial;
    }

    public AgendaExameDTO setHoraInicial(Date horaInicial) {
        this.horaInicial = horaInicial;
        return this;
    }

    public Long getVagasDisponiveis() {
        return vagasDisponiveis;
    }

    public AgendaExameDTO setVagasDisponiveis(Long vagasDisponiveis) {
        this.vagasDisponiveis = vagasDisponiveis;
        return this;
    }
    
    public AgendaGradeHorario getAgendaGradeHorario() {
        return agendaGradeHorario;
    }

    public AgendaExameDTO setAgendaGradeHorario(AgendaGradeHorario agendaGradeHorario) {
        this.agendaGradeHorario = agendaGradeHorario;
        return this;
    }

    public String getDiaSemanaAbv() {
        return new SimpleDateFormat("EEE", Bundle.getLocale()).format(getData());
    }

    public String getDescricaoHoraInicial(){
        if(getHoraInicial() != null){
            return new SimpleDateFormat("HH:mm", Bundle.getLocale()).format(getHoraInicial());
        }
        return "";
    }

}
