/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.agendamento.exame.dto;

import br.com.ksisolucoes.util.DTOParamConfigureDefault;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd.StatusLaudoTfd;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SolicitacaoAgendamentoDTOParam implements Serializable{

    private List<TipoProcedimento> tipoProcedimento;
    private List<Empresa> empresa;
    private List<UsuarioCadsus> paciente;
    private DatePeriod periodo;
    private List<Long> situacaoList;
    private String tipoData;
    private boolean validarDataConfirmacao;
    private Empresa unidadeOrigem;
    private Long numeroSolicitacao;
    private List<Long> tipoAgendamentos;
    private List<Long> tipoConsulta;
    private List<Long> prioridade;
    private LaudoTfd.StatusLaudoTfd statusLaudo;
    private List<Long> parecerPedido;
    private String flagTfd;
    private String numeroPedido;
    private String nomePaciente;
    private Date dataNascimento;
    private String sortProp;
    private boolean ascending;
    private Long codigoPaciente;
    private String cnsPaciente;
    private Long situacaoAgendamento;
    private Empresa localAgendamento;
    private Cidade cidade;
    private Long tipoFila;

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public List<Long> getParecerPedido() {
        return parecerPedido;
    }

    public void setParecerPedido(List<Long> parecerPedido) {
        this.parecerPedido = parecerPedido;
    }

    public String getFlagTfd() {
        return flagTfd;
    }

    public void setFlagTfd(String flagTfd) {
        this.flagTfd = flagTfd;
    }

    public StatusLaudoTfd getStatusLaudo() {
        return statusLaudo;
    }

    public void setStatusLaudo(StatusLaudoTfd statusLaudo) {
        this.statusLaudo = statusLaudo;
    }

    private DTOParamConfigureDefault configureParam;

    public DTOParamConfigureDefault getConfigureParam() {
        if(configureParam == null){
            configureParam = new DTOParamConfigureDefault();
        }
        return configureParam;
    }

    public Long getNumeroSolicitacao() {
        return numeroSolicitacao;
    }

    public void setNumeroSolicitacao(Long numeroSolicitacao) {
        this.numeroSolicitacao = numeroSolicitacao;
    }

    public List<Empresa> getEmpresa() {
        return empresa;
    }

    public void setEmpresa(List<Empresa> empresa) {
        this.empresa = empresa;
    }

    public List<UsuarioCadsus> getPaciente() {
        return paciente;
    }

    public void setPaciente(List<UsuarioCadsus> paciente) {
        this.paciente = paciente;
    }

    public Long getCodigoPaciente() {
        return codigoPaciente;
    }

    public void setCodigoPaciente(Long codigoPaciente) {
        this.codigoPaciente = codigoPaciente;
    }

    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public List<Long> getSituacaoList() {
        return situacaoList;
    }

    public void setSituacaoList(List<Long> situacaoList) {
        this.situacaoList = situacaoList;
    }

    public String getTipoData() {
        return tipoData;
    }

    public void setTipoData(String tipoData) {
        this.tipoData = tipoData;
    }

    public List<TipoProcedimento> getTipoProcedimento() {
        return tipoProcedimento;
    }

    public void setTipoProcedimento(List<TipoProcedimento> tipoProcedimento) {
        this.tipoProcedimento = tipoProcedimento;
    }

    public boolean isValidarDataConfirmacao() {
        return validarDataConfirmacao;
    }

    public void setValidarDataConfirmacao(boolean validarDataConfirmacao) {
        this.validarDataConfirmacao = validarDataConfirmacao;
    }

    public List<Long> getTipoAgendamentos() {
        return tipoAgendamentos;
    }

    public void setTipoAgendamentos(List<Long> tipoAgendamentos) {
        this.tipoAgendamentos = tipoAgendamentos;
    }

    public List<Long> getTipoConsulta() {
        return tipoConsulta;
    }

    public void setTipoConsulta(List<Long> tipoConsulta) {
        this.tipoConsulta = tipoConsulta;
    }

    public List<Long> getPrioridade() {
        return prioridade;
    }

    public void setPrioridade(List<Long> prioridade) {
        this.prioridade = prioridade;
    }

    public String getNumeroPedido() {
        return numeroPedido;
    }

    public void setNumeroPedido(String numeroPedido) {
        this.numeroPedido = numeroPedido;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public String getSortProp() {
        return sortProp;
    }

    public void setSortProp(String sortProp) {
        this.sortProp = sortProp;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

    public String getCnsPaciente() {
        return cnsPaciente;
    }

    public void setCnsPaciente(String cnsPaciente) {
        this.cnsPaciente = cnsPaciente;
    }

    public Long getSituacaoAgendamento() {
        return situacaoAgendamento;
    }

    public void setSituacaoAgendamento(Long situacaoAgendamento) {
        this.situacaoAgendamento = situacaoAgendamento;
    }

    public Empresa getLocalAgendamento() {
        return localAgendamento;
    }

    public void setLocalAgendamento(Empresa localAgendamento) {
        this.localAgendamento = localAgendamento;
    }

    public Cidade getCidade() {
        return cidade;
    }

    public void setCidade(Cidade cidade) {
        this.cidade = cidade;
    }

    public Long getTipoFila() {
        return tipoFila;
    }

    public void setTipoFila(Long tipoFila) {
        this.tipoFila = tipoFila;
    }

    public void setConfigureParam(DTOParamConfigureDefault configureParam) {
        this.configureParam = configureParam;
    }

    public Empresa getUnidadeOrigem() {
        return unidadeOrigem;
    }

    public void setUnidadeOrigem(Empresa unidadeOrigem) {
        this.unidadeOrigem = unidadeOrigem;
    }
}
