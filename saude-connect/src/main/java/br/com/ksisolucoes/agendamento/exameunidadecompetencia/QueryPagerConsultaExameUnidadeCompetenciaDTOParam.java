/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.agendamento.exameunidadecompetencia;

import br.com.ksisolucoes.vo.basico.Empresa;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryPagerConsultaExameUnidadeCompetenciaDTOParam implements Serializable {

    private List<Empresa> unidade;
    private Date competencia;

    public Date getCompetencia() {
        return competencia;
    }

    public void setCompetencia(Date competencia) {
        this.competencia = competencia;
    }

    public List<Empresa> getUnidade() {
        return unidade;
    }

    public void setUnidade(List<Empresa> unidade) {
        this.unidade = unidade;
    }

}
