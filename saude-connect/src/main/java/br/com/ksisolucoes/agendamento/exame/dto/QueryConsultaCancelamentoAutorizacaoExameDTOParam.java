/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.agendamento.exame.dto;

import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaCancelamentoAutorizacaoExameDTOParam implements Serializable {

    private Long codigoExame;
    private String nomePaciente;
    private Empresa unidade;
    private TipoExame tipoExame;
    private DatePeriod periodo;
    private String campoOrdenacao;
    private String tipoOrdenacao;

    public Long getCodigoExame() {
        return codigoExame;
    }

    public void setCodigoExame(Long codigoExame) {
        this.codigoExame = codigoExame;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public TipoExame getTipoExame() {
        return tipoExame;
    }

    public void setTipoExame(TipoExame tipoExame) {
        this.tipoExame = tipoExame;
    }

    public Empresa getUnidade() {
        return unidade;
    }

    public void setUnidade(Empresa unidade) {
        this.unidade = unidade;
    }

    public String getCampoOrdenacao(){
        return this.campoOrdenacao;
    }
    
    public void setCampoOrdenacao(String campoOrdenacao) {
        this.campoOrdenacao = campoOrdenacao;
    }

    public String getTipoOrdenacao() {
        return tipoOrdenacao;
    }
    
    public void setTipoOrdenacao(String tipoOrdenacao) {
        this.tipoOrdenacao = tipoOrdenacao;
    }

}
