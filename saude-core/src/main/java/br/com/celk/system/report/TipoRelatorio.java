package br.com.celk.system.report;

import br.com.ksisolucoes.enums.IEnum;

/**
 *
 * <AUTHOR>
 */
public enum TipoRelatorio implements IEnum{

    PDF(1, ".pdf"),
    HTML(2,".html"),
    XLS(3,".xls"),
    CS<PERSON>(4,".csv"),
    TXT(5,".txt"),
    RTF(6,".rtf"),
    XLS2(7,".xls"),
    PDF_TEMPLATE(8,".pdf"),
    ;
    
    private final String ext;
    private final long value;

    TipoRelatorio(long value, String ext) {
        this.ext = ext;
        this.value = value;
    }

    @Override
    public Long value() {
        return value;
    }

    @Override
    public String descricao() {
        return ext;
    }
    public static TipoRelatorio byValue(Long codigo){
        for (TipoRelatorio tipo: values()) {
            if(codigo.equals(tipo.value())){
                return tipo;
            }
        }
        return null;
    }
}
