package br.com.celk.integracao.fcm.core;

import de.bytefish.fcmjava.model.topics.Topic;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
public class FCMMessage {

    @NotNull(message = "Implemente o tópico da mensagem")
    private Topic topic;
    @NotNull(message = "Implemente os dados do tópico")
    private FCMMessageDTO data;

    public Topic getTopic() {
        return topic;
    }

    public void setTopic(Topic topic) {
        this.topic = topic;
    }

    public FCMMessageDTO getData() {
        return data;
    }

    public void setData(FCMMessageDTO data) {
        this.data = data;
    }
}
