/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.command;

import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang.SerializationUtils;
import org.hibernate.Session;

import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Reflection;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

/**
 *
 * <AUTHOR>
 */
public class VerifyChangesVO extends CommandQuery<VerifyChangesVO> {

    private CodigoManager vo;
    private String[] properties;
    private boolean changed;
    private boolean notYetExists = true;
    private List<String> exceptThisProperties;

    public VerifyChangesVO(CodigoManager vo, String... properties) {
        this.vo = vo;
        this.properties = properties;
    }

    public VerifyChangesVO(CodigoManager vo, String[] exceptThisProperties, String... properties) {
        this.vo = vo;
        this.properties = properties;
        this.exceptThisProperties = Arrays.asList(exceptThisProperties);
    }

    public VerifyChangesVO(CodigoManager vo) {
        this.vo = vo;
        if (this.vo != null) {
            this.properties = new HQLProperties(this.vo.getClass()).getProperties();
        }
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (this.vo == null) {
            throw new ValidacaoException("O VO no pode ser nulo para verificar as diferenas!");
        }

        if (this.vo.getCodigoManager() == null) {
            throw new ValidacaoException("A ID do VO no pode ser nula para verificar as diferenas!");
        }

        try {
            CodigoManager newInstance = (CodigoManager) SerializationUtils.clone((Serializable) vo);

            ReloadVO reloadVO = new ReloadVO(newInstance, true, ReloadVO.always(), properties);
            reloadVO.start();

            newInstance = reloadVO.getNewInstance();

            if (newInstance != null) {
                notYetExists = false;
            }

            for (String property : properties) {
                if (this.exceptThisProperties != null) {
                    if (this.exceptThisProperties.contains(property)) {
                        continue;
                    }
                }

                Object currentValue = Reflection.getValueByPattern(vo, property);
                Object currentValueDB = Reflection.getValueByPattern(newInstance, property);

                if ((currentValue == null && currentValueDB != null) || (currentValue != null && currentValueDB == null)) {
                    changed = true;
                    break;
                }

                if ((currentValue != null && currentValueDB != null) && (!currentValue.equals(currentValueDB))) {
                    changed = true;
                    break;
                }
            }
        } catch (NoSuchMethodException ex) {
            throw new DAOException(ex);
        } catch (IllegalAccessException ex) {
            throw new DAOException(ex);
        } catch (InvocationTargetException ex) {
            throw new DAOException(ex);
        }
    }

    public boolean isChanged() {
        return changed;
    }

    public boolean isNotYetExists() {
        return notYetExists;
    }
}
