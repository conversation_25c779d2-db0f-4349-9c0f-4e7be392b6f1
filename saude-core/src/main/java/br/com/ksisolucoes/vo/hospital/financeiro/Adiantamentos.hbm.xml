<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.hospital.financeiro"  >
    <class name="Adiantamentos" table="adiantamentos" >
        <id
            column="cd_adiantamentos"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />
        
        <many-to-one class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"
                     name="usuarioCadsus" not-null="true">
            <column name="cd_usuario_cadsus" />
        </many-to-one>
        
        <property 
            name="dataCadastro"
            column="dt_cadastro"
            not-null="true"
            type="timestamp"
        />
        
        <property 
            name="dataAdiantamento"
            column="dt_adiantamento"
            not-null="true"
            type="timestamp"
        />
        
        <property
            name="valorAdiantamento"
            column="vl_adiantamento"
            not-null="true"
            type="java.lang.Double"
        />
        
        <property
            name="valorPago"
            column="vl_pago"
            not-null="false"
            type="java.lang.Double"
        />
        
        <property
            column="observacao"
            name="observacao"
            not-null="false"
            length="512"
            type="java.lang.String"
        />
        
        <property
            column="motivo"
            name="motivo"
            not-null="false"
            length="512"
            type="java.lang.String"
        />
        
        <property 
            name="status"
            column="status"
            type="java.lang.Long"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuario"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario_cancelamento"
            name="usuarioCancelamento"
            not-null="false"
        />
        
        <property 
            name="dataCancelamento"
            column="dt_cancelamento"
            not-null="false"
            type="timestamp"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.hospital.financeiro.FormaPagamento"
            column="cd_forma_pagamento"
            name="formaPagamento"
            not-null="true"
        />
        
    </class>
</hibernate-mapping>
