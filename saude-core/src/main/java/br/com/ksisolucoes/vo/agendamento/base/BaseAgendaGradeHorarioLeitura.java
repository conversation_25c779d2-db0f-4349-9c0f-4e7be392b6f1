package br.com.ksisolucoes.vo.agendamento.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the agenda_grade_horario table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="agenda_grade_horario"
 */

public abstract class BaseAgendaGradeHorarioLeitura extends BaseRootVO implements Serializable {

	public static String REF = "AgendaGradeHorarioLeitura";
	public static final String PROP_STATUS = "status";
	public static final String PROP_AGENDA_GRADE_ATENDIMENTO = "agendaGradeAtendimento";
	public static final String PROP_HORA = "hora";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO = "agendaGradeAtendimentoHorario";


	// constructors
	public BaseAgendaGradeHorarioLeitura () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAgendaGradeHorarioLeitura (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAgendaGradeHorarioLeitura (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento agendaGradeAtendimento,
		java.util.Date hora,
		java.lang.Long status) {

		this.setCodigo(codigo);
		this.setAgendaGradeAtendimento(agendaGradeAtendimento);
		this.setHora(hora);
		this.setStatus(status);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date hora;
	private java.lang.Long status;

	// one to one
	private br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario;

	// many to one
	private br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento agendaGradeAtendimento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_agenda_horario"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: hora
	 */
	public java.util.Date getHora () {
		return getPropertyValue(this, hora, PROP_HORA); 
	}

	/**
	 * Set the value related to the column: hora
	 * @param hora the hora value
	 */
	public void setHora (java.util.Date hora) {
//        java.util.Date horaOld = this.hora;
		this.hora = hora;
//        this.getPropertyChangeSupport().firePropertyChange ("hora", horaOld, hora);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: agendaGradeAtendimentoHorario
	 */
	public br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario getAgendaGradeAtendimentoHorario () {
		return getPropertyValue(this, agendaGradeAtendimentoHorario, PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO); 
	}

	/**
	 * Set the value related to the column: agendaGradeAtendimentoHorario
	 * @param agendaGradeAtendimentoHorario the agendaGradeAtendimentoHorario value
	 */
	public void setAgendaGradeAtendimentoHorario (br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario) {
//        br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorarioOld = this.agendaGradeAtendimentoHorario;
		this.agendaGradeAtendimentoHorario = agendaGradeAtendimentoHorario;
//        this.getPropertyChangeSupport().firePropertyChange ("agendaGradeAtendimentoHorario", agendaGradeAtendimentoHorarioOld, agendaGradeAtendimentoHorario);
	}



	/**
	 * Return the value associated with the column: cd_ag_gra_atendimento
	 */
	public br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento getAgendaGradeAtendimento () {
		return getPropertyValue(this, agendaGradeAtendimento, PROP_AGENDA_GRADE_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_ag_gra_atendimento
	 * @param agendaGradeAtendimento the cd_ag_gra_atendimento value
	 */
	public void setAgendaGradeAtendimento (br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento agendaGradeAtendimento) {
//        br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento agendaGradeAtendimentoOld = this.agendaGradeAtendimento;
		this.agendaGradeAtendimento = agendaGradeAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("agendaGradeAtendimento", agendaGradeAtendimentoOld, agendaGradeAtendimento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.agendamento.AgendaGradeHorarioLeitura)) return false;
		else {
			br.com.ksisolucoes.vo.agendamento.AgendaGradeHorarioLeitura agendaGradeHorarioLeitura = (br.com.ksisolucoes.vo.agendamento.AgendaGradeHorarioLeitura) obj;
			if (null == this.getCodigo() || null == agendaGradeHorarioLeitura.getCodigo()) return false;
			else return (this.getCodigo().equals(agendaGradeHorarioLeitura.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}