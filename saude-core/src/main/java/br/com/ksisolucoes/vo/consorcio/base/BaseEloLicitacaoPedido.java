package br.com.ksisolucoes.vo.consorcio.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the elo_licitacao_pedido table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="elo_licitacao_pedido"
 */

public abstract class BaseEloLicitacaoPedido extends BaseRootVO implements Serializable {

	public static String REF = "EloLicitacaoPedido";
	public static final String PROP_PEDIDO_LICITACAO_ITEM = "pedidoLicitacaoItem";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_LICITACAO_ITEM = "licitacaoItem";


	// constructors
	public BaseEloLicitacaoPedido () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEloLicitacaoPedido (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseEloLicitacaoPedido (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.consorcio.PedidoLicitacaoItem pedidoLicitacaoItem,
		br.com.ksisolucoes.vo.consorcio.LicitacaoItem licitacaoItem) {

		this.setCodigo(codigo);
		this.setPedidoLicitacaoItem(pedidoLicitacaoItem);
		this.setLicitacaoItem(licitacaoItem);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.consorcio.PedidoLicitacaoItem pedidoLicitacaoItem;
	private br.com.ksisolucoes.vo.consorcio.LicitacaoItem licitacaoItem;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_elo_lic_ped"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_ped_lic_it
	 */
	public br.com.ksisolucoes.vo.consorcio.PedidoLicitacaoItem getPedidoLicitacaoItem () {
		return getPropertyValue(this, pedidoLicitacaoItem, PROP_PEDIDO_LICITACAO_ITEM); 
	}

	/**
	 * Set the value related to the column: cd_ped_lic_it
	 * @param pedidoLicitacaoItem the cd_ped_lic_it value
	 */
	public void setPedidoLicitacaoItem (br.com.ksisolucoes.vo.consorcio.PedidoLicitacaoItem pedidoLicitacaoItem) {
//        br.com.ksisolucoes.vo.consorcio.PedidoLicitacaoItem pedidoLicitacaoItemOld = this.pedidoLicitacaoItem;
		this.pedidoLicitacaoItem = pedidoLicitacaoItem;
//        this.getPropertyChangeSupport().firePropertyChange ("pedidoLicitacaoItem", pedidoLicitacaoItemOld, pedidoLicitacaoItem);
	}



	/**
	 * Return the value associated with the column: cd_lic_item
	 */
	public br.com.ksisolucoes.vo.consorcio.LicitacaoItem getLicitacaoItem () {
		return getPropertyValue(this, licitacaoItem, PROP_LICITACAO_ITEM); 
	}

	/**
	 * Set the value related to the column: cd_lic_item
	 * @param licitacaoItem the cd_lic_item value
	 */
	public void setLicitacaoItem (br.com.ksisolucoes.vo.consorcio.LicitacaoItem licitacaoItem) {
//        br.com.ksisolucoes.vo.consorcio.LicitacaoItem licitacaoItemOld = this.licitacaoItem;
		this.licitacaoItem = licitacaoItem;
//        this.getPropertyChangeSupport().firePropertyChange ("licitacaoItem", licitacaoItemOld, licitacaoItem);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.consorcio.EloLicitacaoPedido)) return false;
		else {
			br.com.ksisolucoes.vo.consorcio.EloLicitacaoPedido eloLicitacaoPedido = (br.com.ksisolucoes.vo.consorcio.EloLicitacaoPedido) obj;
			if (null == this.getCodigo() || null == eloLicitacaoPedido.getCodigo()) return false;
			else return (this.getCodigo().equals(eloLicitacaoPedido.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}