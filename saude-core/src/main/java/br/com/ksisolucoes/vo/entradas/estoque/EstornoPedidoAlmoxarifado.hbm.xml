<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.entradas.estoque"  >
    <class name="EstornoPedidoAlmoxarifado" table="estorno_pedido_almoxarifado">
        <id name="codigo" type="java.lang.Long" column="cd_estorno_pedido_almoxarifado" >
            <generator class="sequence">
                <param name="sequence">seq_estorno_pedido_almoxarifado</param>
            </generator>
        </id>
        <version column="version" name="version" type="long" />
        <many-to-one class="br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaItemLote" not-null="true"  column="cod_pedido_transf_item_lote" name="pedidoTransferenciaItemLote"/>
        <many-to-one class="br.com.ksisolucoes.vo.entradas.estoque.Produto" column="cod_produto" name="produto"/>
        <property column="qtd_solicitada" name="quantidadeSolicitada" type="java.lang.Double" not-null="true"/>
        <property column="qtd_recebida" name="quantidadeRecebida" type="java.lang.Double" not-null="true"/>
        <property name="observacao" column="observacao" not-null="true" type="java.lang.String"/>
        <many-to-one name="usuario" class="br.com.ksisolucoes.vo.controle.Usuario" column="cd_usuario" not-null="true" />
        <property name="dataCadastro" column="dt_cadastro" type="timestamp" not-null="true"/>
    </class>
</hibernate-mapping>