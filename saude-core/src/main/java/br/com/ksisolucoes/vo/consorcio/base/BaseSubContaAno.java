package br.com.ksisolucoes.vo.consorcio.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the subconta_ano table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="subconta_ano"
 */

public abstract class BaseSubContaAno extends BaseRootVO implements Serializable {

	public static String REF = "SubContaAno";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_USUARIO = "dataUsuario";
	public static final String PROP_SUB_CONTA = "subConta";
	public static final String PROP_VALOR_RESERVADO = "valorReservado";
	public static final String PROP_ANO = "ano";
	public static final String PROP_SALDO_ATUAL = "saldoAtual";


	// constructors
	public BaseSubContaAno () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseSubContaAno (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseSubContaAno (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.consorcio.SubConta subConta,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.Long ano,
		java.lang.Double saldoAtual,
		java.lang.Double valorReservado,
		java.util.Date dataUsuario) {

		this.setCodigo(codigo);
		this.setSubConta(subConta);
		this.setUsuario(usuario);
		this.setAno(ano);
		this.setSaldoAtual(saldoAtual);
		this.setValorReservado(valorReservado);
		this.setDataUsuario(dataUsuario);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long ano;
	private java.lang.Double saldoAtual;
	private java.lang.Double valorReservado;
	private java.util.Date dataUsuario;

	// many to one
	private br.com.ksisolucoes.vo.consorcio.SubConta subConta;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_subconta_ano"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ano
	 */
	public java.lang.Long getAno () {
		return getPropertyValue(this, ano, PROP_ANO); 
	}

	/**
	 * Set the value related to the column: ano
	 * @param ano the ano value
	 */
	public void setAno (java.lang.Long ano) {
//        java.lang.Long anoOld = this.ano;
		this.ano = ano;
//        this.getPropertyChangeSupport().firePropertyChange ("ano", anoOld, ano);
	}



	/**
	 * Return the value associated with the column: saldo_atual
	 */
	public java.lang.Double getSaldoAtual () {
		return getPropertyValue(this, saldoAtual, PROP_SALDO_ATUAL); 
	}

	/**
	 * Set the value related to the column: saldo_atual
	 * @param saldoAtual the saldo_atual value
	 */
	public void setSaldoAtual (java.lang.Double saldoAtual) {
//        java.lang.Double saldoAtualOld = this.saldoAtual;
		this.saldoAtual = saldoAtual;
//        this.getPropertyChangeSupport().firePropertyChange ("saldoAtual", saldoAtualOld, saldoAtual);
	}



	/**
	 * Return the value associated with the column: valor_reservado
	 */
	public java.lang.Double getValorReservado () {
		return getPropertyValue(this, valorReservado, PROP_VALOR_RESERVADO); 
	}

	/**
	 * Set the value related to the column: valor_reservado
	 * @param valorReservado the valor_reservado value
	 */
	public void setValorReservado (java.lang.Double valorReservado) {
//        java.lang.Double valorReservadoOld = this.valorReservado;
		this.valorReservado = valorReservado;
//        this.getPropertyChangeSupport().firePropertyChange ("valorReservado", valorReservadoOld, valorReservado);
	}



	/**
	 * Return the value associated with the column: dt_usuario
	 */
	public java.util.Date getDataUsuario () {
		return getPropertyValue(this, dataUsuario, PROP_DATA_USUARIO); 
	}

	/**
	 * Set the value related to the column: dt_usuario
	 * @param dataUsuario the dt_usuario value
	 */
	public void setDataUsuario (java.util.Date dataUsuario) {
//        java.util.Date dataUsuarioOld = this.dataUsuario;
		this.dataUsuario = dataUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuario", dataUsuarioOld, dataUsuario);
	}



	/**
	 * Return the value associated with the column: cd_subconta
	 */
	public br.com.ksisolucoes.vo.consorcio.SubConta getSubConta () {
		return getPropertyValue(this, subConta, PROP_SUB_CONTA); 
	}

	/**
	 * Set the value related to the column: cd_subconta
	 * @param subConta the cd_subconta value
	 */
	public void setSubConta (br.com.ksisolucoes.vo.consorcio.SubConta subConta) {
//        br.com.ksisolucoes.vo.consorcio.SubConta subContaOld = this.subConta;
		this.subConta = subConta;
//        this.getPropertyChangeSupport().firePropertyChange ("subConta", subContaOld, subConta);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.consorcio.SubContaAno)) return false;
		else {
			br.com.ksisolucoes.vo.consorcio.SubContaAno subContaAno = (br.com.ksisolucoes.vo.consorcio.SubContaAno) obj;
			if (null == this.getCodigo() || null == subContaAno.getCodigo()) return false;
			else return (this.getCodigo().equals(subContaAno.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}