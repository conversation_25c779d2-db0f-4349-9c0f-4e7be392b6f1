
package br.com.ksisolucoes.vo.vigilancia.financeiro;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.financeiro.base.BaseVigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.io.Serializable;
import java.text.NumberFormat;


public class VigilanciaFinanceiro extends BaseVigilanciaFinanceiro implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public VigilanciaFinanceiro () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public VigilanciaFinanceiro (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public VigilanciaFinanceiro (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo anexoBoleto,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.integracao.boleto.Boleto boleto,
		java.lang.Long status,
		java.util.Date dataEmissao,
		java.lang.Double valor) {

		super (
			codigo,
			anexoBoleto,
			usuario,
			boleto,
			status,
			dataEmissao,
			valor);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

	public enum Status implements IEnum {

		EMITIDO(0L, Bundle.getStringApplication("rotulo_emitido")),
		AGUARDANDO_PAGAMENTO(1L, Bundle.getStringApplication("rotulo_aguardando_pagamento")),
		PAGO(2L, Bundle.getStringApplication("rotulo_pago")),
		CANCELADO(3L, Bundle.getStringApplication("rotulo_cancelado")),;

		private Long value;
		private String descricao;

		private Status(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static Status valeuOf(Long value) {
			if (value != null) {
				for (Status status : Status.values()) {
					if (status.value().equals(value)) {
						return status;
					}
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}

	}

	public String getNumeracaoFormatado() {
		return VigilanciaHelper.formatarProtocolo(getNumeracao());
	}

	public String getQuantidadeTaxaFormatado() {
		String qtdTaxaFormatado = null;
		if (getQuantidadeTaxa() != null) {
			Double totalQtd = new Dinheiro(getQuantidadeTaxa())
					.somar(Coalesce.asDouble(getQuantidadeTaxaAlvara()))
					.doubleValue();
			NumberFormat nf = NumberFormat.getInstance();
			nf.setMaximumFractionDigits(4);
			qtdTaxaFormatado = nf.format(totalQtd);
		}
		return qtdTaxaFormatado;
	}

	public String getDescricaoSituacao() {
		Status status = Status.valeuOf(getStatus());
		if (status != null) {
			return status.descricao();
		}
		return null;
	}

	public String getDescricaoComplementar() {
		if(RepositoryComponentDefault.SIM_LONG.equals(getFlagComplementar())){
			return "Sim";
		} else {
			return "Não";
		}
	}

	public String getDescricaoProcessoVinculado(){
    	if(this.getRequerimentoVigilancia() != null) {
    		return "Requerimento/Protocolo Nº " + this.getRequerimentoVigilancia().getProtocoloFormatado();
		} else if(this.getAutoMulta()  != null) {
    		return "Auto de Multa Nº " + this.getAutoMulta().getNumeroFormatado();
		} else if(this.getAutoPenalidade()  != null) {
			return "Auto de Penalidade Nº " + this.getAutoPenalidade().getNumeroFormatado();
		}
		return "";
	}
}