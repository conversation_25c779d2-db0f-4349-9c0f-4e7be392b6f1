package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseFatorRisco;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class FatorRisco extends BaseFatorRisco implements CodigoManager {
	private static final long serialVersionUID = 1L;

	public static enum Situacao implements IEnum<Situacao> {

		ATIVO(0L, Bundle.getStringApplication("rotulo_ativo")),
		INATIVO(1L, Bundle.getStringApplication("rotulo_inativo"));

		private Long value;
		private String descricao;

		private Situacao(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static Situacao valueOf(Long value) {
			for (Situacao situacao : Situacao.values()) {
				if (situacao.value().equals(value)) {
					return situacao;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return this.value;
		}

		@Override
		public String descricao() {
			return this.descricao;
		}
	}

/*[CONSTRUCTOR MARKER BEGIN]*/
	public FatorRisco () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public FatorRisco (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public FatorRisco (
		java.lang.Long codigo,
		java.lang.String descricao,
		java.lang.Long situacao,
		java.util.Date dataCadastro,
		java.util.Date dataAlteracao) {

		super (
			codigo,
			descricao,
			situacao,
			dataCadastro,
			dataAlteracao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

	public String getSituacaoFormatado() {
		Situacao situacao = Situacao.valueOf(getSituacao());
		if (situacao != null && situacao.descricao() != null) {
			return situacao.descricao();
		}

		return "";
	}
}