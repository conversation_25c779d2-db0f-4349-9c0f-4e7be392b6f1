package br.com.ksisolucoes.vo.controle.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;


public abstract class BaseProgramaCampoPK extends BaseRootVO implements Serializable {

	protected int hashCode = Integer.MIN_VALUE;

	public static String PROP_CODIGO = "codigo";
	public static String PROP_PROGRAMA = "programa";

	private java.lang.Long codigo;
	private br.com.ksisolucoes.vo.controle.Programa programa;


	public BaseProgramaCampoPK () {}
	
	public BaseProgramaCampoPK (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Programa programa) {

		this.setCodigo(codigo);
		this.setPrograma(programa);
	}


	/**
	 * Return the value associated with the column: sequencia
	 */
	public java.lang.Long getCodigo () {
		return getPropertyValue(this, codigo, PROP_CODIGO); 
	}

	/**
	 * Set the value related to the column: sequencia
	 * @param codigo the sequencia value
	 */
	public void setCodigo (java.lang.Long codigo) {
//        java.lang.Long codigoOld = this.codigo;
		this.codigo = codigo;
//        this.getPropertyChangeSupport().firePropertyChange ("codigo", codigoOld, codigo);
	}



	/**
	 * Return the value associated with the column: cd_programa
	 */
	public br.com.ksisolucoes.vo.controle.Programa getPrograma () {
		return getPropertyValue(this, programa, PROP_PROGRAMA); 
	}

	/**
	 * Set the value related to the column: cd_programa
	 * @param programa the cd_programa value
	 */
	public void setPrograma (br.com.ksisolucoes.vo.controle.Programa programa) {
//        br.com.ksisolucoes.vo.controle.Programa programaOld = this.programa;
		this.programa = programa;
//        this.getPropertyChangeSupport().firePropertyChange ("programa", programaOld, programa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.controle.ProgramaCampoPK)) return false;
		else {
			br.com.ksisolucoes.vo.controle.ProgramaCampoPK mObj = (br.com.ksisolucoes.vo.controle.ProgramaCampoPK) obj;
			if (null != this.getCodigo() && null != mObj.getCodigo()) {
				if (!this.getCodigo().equals(mObj.getCodigo())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getPrograma() && null != mObj.getPrograma()) {
				if (!this.getPrograma().equals(mObj.getPrograma())) {
					return false;
				}
			}
			else {
				return false;
			}
			return true;
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			StringBuilder sb = new StringBuilder();
			if (null != this.getCodigo()) {
				sb.append(this.getCodigo().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getPrograma()) {
				sb.append(this.getPrograma().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			this.hashCode = sb.toString().hashCode();
		}
		return this.hashCode;
	}

    private java.beans.PropertyChangeSupport propertyChangeSupport;

    protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
        if( this.propertyChangeSupport == null ) {
            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
        }
        return this.propertyChangeSupport;
    }

    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.addPropertyChangeListener(l);
    }

    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
		propertyChangeSupport.addPropertyChangeListener(propertyName, listener);
    }

    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.removePropertyChangeListener(l);
    }
}