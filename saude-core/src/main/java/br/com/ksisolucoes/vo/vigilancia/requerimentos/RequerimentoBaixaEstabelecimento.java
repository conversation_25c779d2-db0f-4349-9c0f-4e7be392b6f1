package br.com.ksisolucoes.vo.vigilancia.requerimentos;

import java.io.Serializable;

import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoBaixaEstabelecimento;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class RequerimentoBaixaEstabelecimento extends BaseRequerimentoBaixaEstabelecimento implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequerimentoBaixaEstabelecimento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequerimentoBaixaEstabelecimento (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequerimentoBaixaEstabelecimento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia) {

		super (
			codigo,
			requerimentoVigilancia);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}