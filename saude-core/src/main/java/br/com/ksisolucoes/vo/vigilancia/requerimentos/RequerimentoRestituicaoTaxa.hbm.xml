<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.requerimentos">
    <class name="RequerimentoRestituicaoTaxa" table="requerimento_restituicao_taxa">
        <id
                column="cd_requerimento_restituicao_taxa"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="sequence">
                <param name="sequence">seq_requerimento_restituicao_taxa</param>
            </generator>
        </id>
        <version column="version" name="version" type="long"/>

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia"
                column="cd_req_vigilancia"
                name="requerimentoVigilancia"
                not-null="true"
        />

        <property
                column="nr_processo"
                name="numeroProcesso"
                type="java.lang.String"
                not-null="false"
        />

        <property
                column="motivo_restituicao"
                name="motivoRestituicao"
                not-null="false"
                type="java.lang.String"
        />
        
        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia"
                column="cd_atividades_vigilancia"
                name="atividadesVigilancia"
                not-null="false"
        />

    </class>
</hibernate-mapping>
