package br.com.ksisolucoes.vo.materiais.smar.rme;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

public class RequisicaoMateriaisEstoque {
    private String codigoUnidadeOrcamentaria;
    private String data;
    private int idAlmoxarifado;
    private String observacao;
    private int tipoRme;
    private List<Item> itens;

    public String getCodigoUnidadeOrcamentaria() {
        return codigoUnidadeOrcamentaria;
    }

    public void setCodigoUnidadeOrcamentaria(String codigoUnidadeOrcamentaria) {
        this.codigoUnidadeOrcamentaria = codigoUnidadeOrcamentaria;
    }

    public String getData() {
        return data;
    }

    public void setData(LocalDate data) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String dataFormatada = data.format(formatter);
        this.data = dataFormatada;
    }

    public void setData(String data) {
        this.data = data;
    }

    public int getIdAlmoxarifado() {
        return idAlmoxarifado;
    }

    public void setIdAlmoxarifado(int idAlmoxarifado) {
        this.idAlmoxarifado = idAlmoxarifado;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public int getTipoRme() {
        return tipoRme;
    }

    public void setTipoRme(int tipoRme) {
        this.tipoRme = tipoRme;
    }

    public List<Item> getItens() {
        return itens;
    }

    public void setItens(List<Item> itens) {
        this.itens = itens;
    }
}
