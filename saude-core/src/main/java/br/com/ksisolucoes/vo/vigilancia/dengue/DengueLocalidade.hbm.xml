<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
	
<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.dengue"  >
    <class name="DengueLocalidade" table="dengue_localidade">
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_dengue_localidade"
        >
            <generator class="sequence">
                <param name="sequence">seq_dengue_localidade</param>
            </generator>

        </id> 
        <version column="version" name="version" type="long" />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.basico.Cidade"
            column="cod_cid"
            name="cidade"
            not-null="true"
        />
        
        <property 
            name="dataRegistro"
            column="dt_registro"
            type="date"
            not-null="true"
        />
        
        <property
            name="localidade"
            column="localidade"
            type="java.lang.String"
            not-null="true"
            length="80"
        />
        
        <property
            name="categoria"
            column="categoria"
            type="java.lang.String"
            not-null="true"
            length="50"
        />
        
        <property
            column="zona"
            name="zona"
            not-null="true"
            type="java.lang.Long"
        />
        
        <property
            column="situacao"
            name="situacao"
            not-null="true"
            type="java.lang.Long"
        />
        
        <property 
            name="dataInativacao"
            column="dt_inativacao"
            type="date"
            not-null="false"
        />
        
        <property
            name="motivoInativacao"
            column="motivo_inativacao"
            type="java.lang.String"
            not-null="false"
            length="250"
        />

        <property 
            name="dataCadastro"
            column="dt_cadastro"
            type="timestamp"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuarioCadastro"
            not-null="true"
        />
    </class>
</hibernate-mapping>