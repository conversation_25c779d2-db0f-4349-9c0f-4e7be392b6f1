package br.com.ksisolucoes.vo.vigilancia.investigacao;

import br.com.ksisolucoes.enums.IEnum;

public class InvestigacaoAgravoDoencaTrabalhoPneumoconiosesEnum {

    public enum SituacaoMercadoTrabalhoEnum implements IEnum {
        REGISTRADO_CARTEIRA_ASSINADA(1L, "Empregado registrado com carteira assinada"),
        NAO_REGISTRADO(2L, "Empregado não registrado"),
        AUTONOMO(3L, "Autônomo/conta própria"),
        SERVIDOR_PUBLICO(4L, "Servidor público estatuário"),
        SERVIDOR_PUBLICO_CELETISTA(5L, "Servidor público celetista"),
        APOSENTADO(6L, "Aposentado"),
        DESEMPREGADO(7L, "Desempregado"),
        TRABALHO_TEMPORARIO(8L, "Trabalho temporário"),
        COOPERATIVADO(9L, "Cooperativado"),
        TRABALHADOR_AVULSO(10L, "Trabalhador avulso"),
        EMPREGADOR(11L, "Empregador"),
        OUTROS(12L, "Outros"),
        IGNORADO(99L, "Ignorado");

        private Long value;
        private String descricao;

        SituacaoMercadoTrabalhoEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static SituacaoMercadoTrabalhoEnum valueOf(Long value) {
            for (SituacaoMercadoTrabalhoEnum v : SituacaoMercadoTrabalhoEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum TempoTrabalhoEnum implements IEnum{
        HORA(1L, "Hora"),
        DIA(2L, "Dia"),
        MES(3L, "Mês"),
        ANO(4L, "Ano");

        private Long value;
        private String descricao;

        TempoTrabalhoEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static TempoTrabalhoEnum valueOf(Long value) {
            for (TempoTrabalhoEnum v : TempoTrabalhoEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum SimNaoIgnoradoEnum implements IEnum {
        SIM(1L, "Sim"),
        NAO(2L, "Não"),
        NAO_SE_APLICA(3L, "Não se aplica"),
        IGNORADO(9L, "Ignorado");

        private Long value;
        private String descricao;

        SimNaoIgnoradoEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static SimNaoIgnoradoEnum valueOf(Long value) {
            for (SimNaoIgnoradoEnum v : SimNaoIgnoradoEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }

        public static IEnum[] getSimNaoIgonorado() {
            IEnum[] arr = {SimNaoIgnoradoEnum.SIM, SimNaoIgnoradoEnum.NAO, SimNaoIgnoradoEnum.IGNORADO};
            return arr;
        }
        public static IEnum[] getSimNao() {
            IEnum[] arr = {SimNaoIgnoradoEnum.SIM, SimNaoIgnoradoEnum.NAO};
            return arr;
        }
    }

    public enum RegimeTratamentoEnum implements IEnum {
        HOSPITALAR(1L, "Hospitalar"),
        AMBULATORIAL(2L, "Ambulatorial");

        private Long value;
        private String descricao;

        RegimeTratamentoEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static RegimeTratamentoEnum valueOf(Long value) {
            for (RegimeTratamentoEnum v : RegimeTratamentoEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum HabitoFumarEnum implements IEnum {
        SIM(1L, "Sim"),
        NAO(2L, "Não"),
        EX_FUMANTE(3L, "Ex-fumante"),
        IGNORADO(9L, "Ignorado");

        private Long value;
        private String descricao;

        HabitoFumarEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static HabitoFumarEnum valueOf(Long value) {
            for (HabitoFumarEnum v : HabitoFumarEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum AvaliacaoFuncionalEnum implements IEnum {
        NORMAL(1L, "Normal"),
        ALTERADA(2L, "Alterada");

        private Long value;
        private String descricao;

        AvaliacaoFuncionalEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static AvaliacaoFuncionalEnum valueOf(Long value) {
            for (AvaliacaoFuncionalEnum v : AvaliacaoFuncionalEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum EvolucaoCasoEnum implements IEnum{
        CURA(1L, "Cura"),
        CURA_NAO_CONFIRMADA(2L, "Cura não confirmada"),
        INCAPACIDADE_TEMPORARIA(3L, "Incapacidade Temporária"),
        INCAPACIDADE_PERMANENTE_PARCIAL(4L, "Incapacidade Permanente Parcial"),
        INCAPACIDADE_PERMANENTE_TOTAL(5L, "Incapacidade Permanente Total"),
        OBITO_DOENCAS_RELACIONADAS_TRABALHO(6L, "Óbito por doenças relacionada ao trabalho"),
        OBITO_OUTRA_CAUSA(7L, "Óbito por outra causa"),
        OUTRO(8L, "Outro"),
        IGNORADO(9L, "Ignorado");

        private Long value;
        private String descricao;

        EvolucaoCasoEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static EvolucaoCasoEnum valueOf(Long value) {
            for (EvolucaoCasoEnum v : EvolucaoCasoEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

}
