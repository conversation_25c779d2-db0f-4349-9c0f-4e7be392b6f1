<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia"  >
    <class name="VeiculoEstabelecimento" table="veiculo_estabelecimento">
        <id
            column="cd_veiculo"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.Estabelecimento"
            column="cd_estabelecimento"
            not-null="true"
            name="estabelecimento"
        />
                      
        <property
            column="placa"
            name="placa"
            not-null="true"
            type="java.lang.String"
        />
		
        <property
            column="tipo_veiculo"
            name="tipoVeiculo"
            not-null="true"
            type="java.lang.String"
        />
		
        <property
            column="especificacao"
            name="especificacao"
            not-null="true"
            type="java.lang.String"
        />
		
        <property
            column="restricoes"
            name="restricoes"
            type="java.lang.String"
        />

        <property
            column="renavam"
            name="renavam"
            length="20"
            type="java.lang.String"
        />
        
        <property
            column="licenca_transporte"
            name="licencaTransporte"
            not-null="true"
            type="java.lang.Long"
        />

        <property
                column="situacao"
                name="situacao"
                type="java.lang.Long"
        />

        <property
                column="refrigerado"
                name="refrigerado"
                not-null="false"
                type="java.lang.Long"
        />

    </class>
</hibernate-mapping>
