package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the investigacao_agr_aci_trabalho_transtorno_mental table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class table="investigacao_agr_aci_trabalho_transtorno_mental"
 */

public abstract class BaseInvestigacaoAgravoAcidenteTrabalhoTranstornoMental extends BaseRootVO implements Serializable {

    public static String REF = "InvestigacaoAgravoAcidenteTrabalhoTranstornoMental";
    public static final String PROP_DATA_INVESTIGACAO = "dataInvestigacao";
    public static final String PROP_PACIENTE_ENCAMINHADO_CAPES = "pacienteEncaminhadoCapes";
    public static final String PROP_TEMPO_MERCADO_TRABALHO_UM = "tempoMercadoTrabalhoUm";
    public static final String PROP_CONDUTA_GERAL_PROTECAO_INDIVIDUAL = "condutaGeralProtecaoIndividual";
    public static final String PROP_USUARIO_ENCERRAMENTO = "usuarioEncerramento";
    public static final String PROP_CONDUTA_GERAL_MUDANCA_ORGANIZACAO_TRABALHO = "condutaGeralMudancaOrganizacaoTrabalho";
    public static final String PROP_FLAG_INFORMACOES_COMPLEMENTARES = "flagInformacoesComplementares";
    public static final String PROP_TEMPO_EXPOSICAO_TABACO_UNIDADE_MEDIDA = "tempoExposicaoTabacoUnidadeMedida";
    public static final String PROP_CONDUTA_GERAL_PROTECAO_COLETIVA = "condutaGeralProtecaoColetiva";
    public static final String PROP_TEMPO_EXPOSICAO_TABACO = "tempoExposicaoTabaco";
    public static final String PROP_DATA_ENCERRAMENTO = "dataEncerramento";
    public static final String PROP_OBSERVACAO = "observacao";
    public static final String PROP_TEMPO_EXPOSICAO_AGENTE_RISCO_UNIDADE_MEDIDA = "tempoExposicaoAgenteRiscoUnidadeMedida";
    public static final String PROP_CONDUTA_GERAL_AFASTAMENTO_SITUACAO_DESGASTE_MENTAL = "condutaGeralAfastamentoSituacaoDesgasteMental";
    public static final String PROP_DATA_OBITO = "dataObito";
    public static final String PROP_CONDUTA_GERAL_NENHUM = "condutaGeralNenhum";
    public static final String PROP_REGIME_TRATAMENTO = "regimeTratamento";
    public static final String PROP_HABITOS_FUMAR = "habitosFumar";
    public static final String PROP_HABITOS_DROGAS_PSICOATIVAS = "habitosDrogasPsicoativas";
    public static final String PROP_CODIGO = "codigo";
    public static final String PROP_TEMPO_MERCADO_TRABALHO = "tempoMercadoTrabalho";
    public static final String PROP_HABITOS_PSICOFARMACOS = "habitosPsicofarmacos";
    public static final String PROP_REGISTRO_AGRAVO = "registroAgravo";
    public static final String PROP_CONDUTA_GERAL_OUTROS = "condutaGeralOutros";
    public static final String PROP_OCUPACAO_CBO = "ocupacaoCbo";
    public static final String PROP_TEMPO_EXPOSICAO_AGENTE_RISCO = "tempoExposicaoAgenteRisco";
    public static final String PROP_CONDUTA_GERAL_AFASTAMENTO_LOCAL_TRABALHO = "condutaGeralAfastamentoLocalTrabalho";
    public static final String PROP_OUTROS_TRABALHADORES_MESMA_DOENCA_LOCAL_TRABALHO = "outrosTrabalhadoresMesmaDoencaLocalTrabalho";
    public static final String PROP_CID_DIAGNOSTICO_ESPECIFICO = "cidDiagnosticoEspecifico";
    public static final String PROP_EVOLUCAO_CASO = "evolucaoCaso";
    public static final String PROP_SITUACAO_MERCADO_TRABALHO = "situacaoMercadoTrabalho";
    public static final String PROP_EMITIDA_C_A_T = "emitidaCAT";
    public static final String PROP_HABITOS_ALCOOL = "habitosAlcool";
    public static final String PROP_EMPREGADOR_EMPRESA_TERCEIRIZADA = "empregadorEmpresaTerceirizada";
    public static final String PROP_EMPRESA_CONTRATANTE = "empresaContratante";


    // constructors
    public BaseInvestigacaoAgravoAcidenteTrabalhoTranstornoMental() {
        initialize();
    }

    /**
     * Constructor for primary key
     */
    public BaseInvestigacaoAgravoAcidenteTrabalhoTranstornoMental(java.lang.Long codigo) {
        this.setCodigo(codigo);
        initialize();
    }

    /**
     * Constructor for required fields
     */
    public BaseInvestigacaoAgravoAcidenteTrabalhoTranstornoMental(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
            java.lang.String flagInformacoesComplementares) {

        this.setCodigo(codigo);
        this.setRegistroAgravo(registroAgravo);
        this.setFlagInformacoesComplementares(flagInformacoesComplementares);
        initialize();
    }

    protected void initialize() {
    }


    private int hashCode = Integer.MIN_VALUE;

    // primary key
    private java.lang.Long codigo;

    // fields
    private java.lang.String flagInformacoesComplementares;
    private java.util.Date dataInvestigacao;
    private java.lang.String observacao;
    private java.util.Date dataEncerramento;
    private java.lang.Long situacaoMercadoTrabalho;
    private java.lang.String tempoMercadoTrabalho;
    private java.lang.Long tempoMercadoTrabalhoUm;
    private java.lang.Long empregadorEmpresaTerceirizada;
    private java.lang.String tempoExposicaoAgenteRisco;
    private java.lang.Long tempoExposicaoAgenteRiscoUnidadeMedida;
    private java.lang.Long regimeTratamento;
    private java.lang.Long habitosAlcool;
    private java.lang.Long habitosPsicofarmacos;
    private java.lang.Long habitosDrogasPsicoativas;
    private java.lang.Long habitosFumar;
    private java.lang.String tempoExposicaoTabaco;
    private java.lang.Long tempoExposicaoTabacoUnidadeMedida;
    private java.lang.Long condutaGeralAfastamentoSituacaoDesgasteMental;
    private java.lang.Long condutaGeralProtecaoIndividual;
    private java.lang.Long condutaGeralMudancaOrganizacaoTrabalho;
    private java.lang.Long condutaGeralNenhum;
    private java.lang.Long condutaGeralProtecaoColetiva;
    private java.lang.Long condutaGeralAfastamentoLocalTrabalho;
    private java.lang.String condutaGeralOutros;
    private java.lang.Long outrosTrabalhadoresMesmaDoencaLocalTrabalho;
    private java.lang.Long pacienteEncaminhadoCapes;
    private java.lang.Long evolucaoCaso;
    private java.util.Date dataObito;
    private java.lang.Long emitidaCAT;

    // many to one
    private br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo;
    private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo;
    private br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento;
    private br.com.ksisolucoes.vo.basico.Empresa empresaContratante;
    private br.com.ksisolucoes.vo.prontuario.basico.Cid cidDiagnosticoEspecifico;


    /**
     * Return the unique identifier of this class
     *
     * @hibernate.id generator-class="sequence"
     * column="cd_invest_agr_acidente_trabalho_transtorno_mental"
     */
    public java.lang.Long getCodigo() {
        return getPropertyValue(this, codigo, "codigo");
    }

    /**
     * Set the unique identifier of this class
     *
     * @param codigo the new ID
     */
    public void setCodigo(java.lang.Long codigo) {
        this.codigo = codigo;
        this.hashCode = Integer.MIN_VALUE;
    }


    /**
     * Return the value associated with the column: flag_informacoes_complementares
     */
    public java.lang.String getFlagInformacoesComplementares() {
        return getPropertyValue(this, flagInformacoesComplementares, PROP_FLAG_INFORMACOES_COMPLEMENTARES);
    }

    /**
     * Set the value related to the column: flag_informacoes_complementares
     *
     * @param flagInformacoesComplementares the flag_informacoes_complementares value
     */
    public void setFlagInformacoesComplementares(java.lang.String flagInformacoesComplementares) {
//        java.lang.String flagInformacoesComplementaresOld = this.flagInformacoesComplementares;
        this.flagInformacoesComplementares = flagInformacoesComplementares;
//        this.getPropertyChangeSupport().firePropertyChange ("flagInformacoesComplementares", flagInformacoesComplementaresOld, flagInformacoesComplementares);
    }


    /**
     * Return the value associated with the column: dt_investigacao
     */
    public java.util.Date getDataInvestigacao() {
        return getPropertyValue(this, dataInvestigacao, PROP_DATA_INVESTIGACAO);
    }

    /**
     * Set the value related to the column: dt_investigacao
     *
     * @param dataInvestigacao the dt_investigacao value
     */
    public void setDataInvestigacao(java.util.Date dataInvestigacao) {
//        java.util.Date dataInvestigacaoOld = this.dataInvestigacao;
        this.dataInvestigacao = dataInvestigacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInvestigacao", dataInvestigacaoOld, dataInvestigacao);
    }


    /**
     * Return the value associated with the column: observacao
     */
    public java.lang.String getObservacao() {
        return getPropertyValue(this, observacao, PROP_OBSERVACAO);
    }

    /**
     * Set the value related to the column: observacao
     *
     * @param observacao the observacao value
     */
    public void setObservacao(java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
        this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
    }


    /**
     * Return the value associated with the column: dt_encerramento
     */
    public java.util.Date getDataEncerramento() {
        return getPropertyValue(this, dataEncerramento, PROP_DATA_ENCERRAMENTO);
    }

    /**
     * Set the value related to the column: dt_encerramento
     *
     * @param dataEncerramento the dt_encerramento value
     */
    public void setDataEncerramento(java.util.Date dataEncerramento) {
//        java.util.Date dataEncerramentoOld = this.dataEncerramento;
        this.dataEncerramento = dataEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEncerramento", dataEncerramentoOld, dataEncerramento);
    }


    /**
     * Return the value associated with the column: situacao_mercado_trabalho
     */
    public java.lang.Long getSituacaoMercadoTrabalho() {
        return getPropertyValue(this, situacaoMercadoTrabalho, PROP_SITUACAO_MERCADO_TRABALHO);
    }

    /**
     * Set the value related to the column: situacao_mercado_trabalho
     *
     * @param situacaoMercadoTrabalho the situacao_mercado_trabalho value
     */
    public void setSituacaoMercadoTrabalho(java.lang.Long situacaoMercadoTrabalho) {
//        java.lang.Long situacaoMercadoTrabalhoOld = this.situacaoMercadoTrabalho;
        this.situacaoMercadoTrabalho = situacaoMercadoTrabalho;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoMercadoTrabalho", situacaoMercadoTrabalhoOld, situacaoMercadoTrabalho);
    }


    /**
     * Return the value associated with the column: tempo_mercado_trabalho
     */
    public java.lang.String getTempoMercadoTrabalho() {
        return getPropertyValue(this, tempoMercadoTrabalho, PROP_TEMPO_MERCADO_TRABALHO);
    }

    /**
     * Set the value related to the column: tempo_mercado_trabalho
     *
     * @param tempoMercadoTrabalho the tempo_mercado_trabalho value
     */
    public void setTempoMercadoTrabalho(java.lang.String tempoMercadoTrabalho) {
//        java.lang.String tempoMercadoTrabalhoOld = this.tempoMercadoTrabalho;
        this.tempoMercadoTrabalho = tempoMercadoTrabalho;
//        this.getPropertyChangeSupport().firePropertyChange ("tempoMercadoTrabalho", tempoMercadoTrabalhoOld, tempoMercadoTrabalho);
    }


    /**
     * Return the value associated with the column: tempo_mercado_trabalho_um
     */
    public java.lang.Long getTempoMercadoTrabalhoUm() {
        return getPropertyValue(this, tempoMercadoTrabalhoUm, PROP_TEMPO_MERCADO_TRABALHO_UM);
    }

    /**
     * Set the value related to the column: tempo_mercado_trabalho_um
     *
     * @param tempoMercadoTrabalhoUm the tempo_mercado_trabalho_um value
     */
    public void setTempoMercadoTrabalhoUm(java.lang.Long tempoMercadoTrabalhoUm) {
//        java.lang.Long tempoMercadoTrabalhoUmOld = this.tempoMercadoTrabalhoUm;
        this.tempoMercadoTrabalhoUm = tempoMercadoTrabalhoUm;
//        this.getPropertyChangeSupport().firePropertyChange ("tempoMercadoTrabalhoUm", tempoMercadoTrabalhoUmOld, tempoMercadoTrabalhoUm);
    }


    /**
     * Return the value associated with the column: empregador_empresa_terceirizada
     */
    public java.lang.Long getEmpregadorEmpresaTerceirizada() {
        return getPropertyValue(this, empregadorEmpresaTerceirizada, PROP_EMPREGADOR_EMPRESA_TERCEIRIZADA);
    }

    /**
     * Set the value related to the column: empregador_empresa_terceirizada
     *
     * @param empregadorEmpresaTerceirizada the empregador_empresa_terceirizada value
     */
    public void setEmpregadorEmpresaTerceirizada(java.lang.Long empregadorEmpresaTerceirizada) {
//        java.lang.Long empregadorEmpresaTerceirizadaOld = this.empregadorEmpresaTerceirizada;
        this.empregadorEmpresaTerceirizada = empregadorEmpresaTerceirizada;
//        this.getPropertyChangeSupport().firePropertyChange ("empregadorEmpresaTerceirizada", empregadorEmpresaTerceirizadaOld, empregadorEmpresaTerceirizada);
    }


    /**
     * Return the value associated with the column: tempo_exposicao_agente_risco
     */
    public java.lang.String getTempoExposicaoAgenteRisco() {
        return getPropertyValue(this, tempoExposicaoAgenteRisco, PROP_TEMPO_EXPOSICAO_AGENTE_RISCO);
    }

    /**
     * Set the value related to the column: tempo_exposicao_agente_risco
     *
     * @param tempoExposicaoAgenteRisco the tempo_exposicao_agente_risco value
     */
    public void setTempoExposicaoAgenteRisco(java.lang.String tempoExposicaoAgenteRisco) {
//        java.lang.String tempoExposicaoAgenteRiscoOld = this.tempoExposicaoAgenteRisco;
        this.tempoExposicaoAgenteRisco = tempoExposicaoAgenteRisco;
//        this.getPropertyChangeSupport().firePropertyChange ("tempoExposicaoAgenteRisco", tempoExposicaoAgenteRiscoOld, tempoExposicaoAgenteRisco);
    }


    /**
     * Return the value associated with the column: tempo_exposicao_agente_risco_um
     */
    public java.lang.Long getTempoExposicaoAgenteRiscoUnidadeMedida() {
        return getPropertyValue(this, tempoExposicaoAgenteRiscoUnidadeMedida, PROP_TEMPO_EXPOSICAO_AGENTE_RISCO_UNIDADE_MEDIDA);
    }

    /**
     * Set the value related to the column: tempo_exposicao_agente_risco_um
     *
     * @param tempoExposicaoAgenteRiscoUnidadeMedida the tempo_exposicao_agente_risco_um value
     */
    public void setTempoExposicaoAgenteRiscoUnidadeMedida(java.lang.Long tempoExposicaoAgenteRiscoUnidadeMedida) {
//        java.lang.Long tempoExposicaoAgenteRiscoUnidadeMedidaOld = this.tempoExposicaoAgenteRiscoUnidadeMedida;
        this.tempoExposicaoAgenteRiscoUnidadeMedida = tempoExposicaoAgenteRiscoUnidadeMedida;
//        this.getPropertyChangeSupport().firePropertyChange ("tempoExposicaoAgenteRiscoUnidadeMedida", tempoExposicaoAgenteRiscoUnidadeMedidaOld, tempoExposicaoAgenteRiscoUnidadeMedida);
    }


    /**
     * Return the value associated with the column: regime_tratamento
     */
    public java.lang.Long getRegimeTratamento() {
        return getPropertyValue(this, regimeTratamento, PROP_REGIME_TRATAMENTO);
    }

    /**
     * Set the value related to the column: regime_tratamento
     *
     * @param regimeTratamento the regime_tratamento value
     */
    public void setRegimeTratamento(java.lang.Long regimeTratamento) {
//        java.lang.Long regimeTratamentoOld = this.regimeTratamento;
        this.regimeTratamento = regimeTratamento;
//        this.getPropertyChangeSupport().firePropertyChange ("regimeTratamento", regimeTratamentoOld, regimeTratamento);
    }


    /**
     * Return the value associated with the column: habitos_alcool
     */
    public java.lang.Long getHabitosAlcool() {
        return getPropertyValue(this, habitosAlcool, PROP_HABITOS_ALCOOL);
    }

    /**
     * Set the value related to the column: habitos_alcool
     *
     * @param habitosAlcool the habitos_alcool value
     */
    public void setHabitosAlcool(java.lang.Long habitosAlcool) {
//        java.lang.Long habitosAlcoolOld = this.habitosAlcool;
        this.habitosAlcool = habitosAlcool;
//        this.getPropertyChangeSupport().firePropertyChange ("habitosAlcool", habitosAlcoolOld, habitosAlcool);
    }


    /**
     * Return the value associated with the column: habitos_psicofarmacos
     */
    public java.lang.Long getHabitosPsicofarmacos() {
        return getPropertyValue(this, habitosPsicofarmacos, PROP_HABITOS_PSICOFARMACOS);
    }

    /**
     * Set the value related to the column: habitos_psicofarmacos
     *
     * @param habitosPsicofarmacos the habitos_psicofarmacos value
     */
    public void setHabitosPsicofarmacos(java.lang.Long habitosPsicofarmacos) {
//        java.lang.Long habitosPsicofarmacosOld = this.habitosPsicofarmacos;
        this.habitosPsicofarmacos = habitosPsicofarmacos;
//        this.getPropertyChangeSupport().firePropertyChange ("habitosPsicofarmacos", habitosPsicofarmacosOld, habitosPsicofarmacos);
    }


    /**
     * Return the value associated with the column: habitos_drogas_psicoativas
     */
    public java.lang.Long getHabitosDrogasPsicoativas() {
        return getPropertyValue(this, habitosDrogasPsicoativas, PROP_HABITOS_DROGAS_PSICOATIVAS);
    }

    /**
     * Set the value related to the column: habitos_drogas_psicoativas
     *
     * @param habitosDrogasPsicoativas the habitos_drogas_psicoativas value
     */
    public void setHabitosDrogasPsicoativas(java.lang.Long habitosDrogasPsicoativas) {
//        java.lang.Long habitosDrogasPsicoativasOld = this.habitosDrogasPsicoativas;
        this.habitosDrogasPsicoativas = habitosDrogasPsicoativas;
//        this.getPropertyChangeSupport().firePropertyChange ("habitosDrogasPsicoativas", habitosDrogasPsicoativasOld, habitosDrogasPsicoativas);
    }


    /**
     * Return the value associated with the column: habitos_fumar
     */
    public java.lang.Long getHabitosFumar() {
        return getPropertyValue(this, habitosFumar, PROP_HABITOS_FUMAR);
    }

    /**
     * Set the value related to the column: habitos_fumar
     *
     * @param habitosFumar the habitos_fumar value
     */
    public void setHabitosFumar(java.lang.Long habitosFumar) {
//        java.lang.Long habitosFumarOld = this.habitosFumar;
        this.habitosFumar = habitosFumar;
//        this.getPropertyChangeSupport().firePropertyChange ("habitosFumar", habitosFumarOld, habitosFumar);
    }


    /**
     * Return the value associated with the column: tempo_exposicao_tabaco
     */
    public java.lang.String getTempoExposicaoTabaco() {
        return getPropertyValue(this, tempoExposicaoTabaco, PROP_TEMPO_EXPOSICAO_TABACO);
    }

    /**
     * Set the value related to the column: tempo_exposicao_tabaco
     *
     * @param tempoExposicaoTabaco the tempo_exposicao_tabaco value
     */
    public void setTempoExposicaoTabaco(java.lang.String tempoExposicaoTabaco) {
//        java.lang.String tempoExposicaoTabacoOld = this.tempoExposicaoTabaco;
        this.tempoExposicaoTabaco = tempoExposicaoTabaco;
//        this.getPropertyChangeSupport().firePropertyChange ("tempoExposicaoTabaco", tempoExposicaoTabacoOld, tempoExposicaoTabaco);
    }


    /**
     * Return the value associated with the column: tempo_exposicao_tabaco_um
     */
    public java.lang.Long getTempoExposicaoTabacoUnidadeMedida() {
        return getPropertyValue(this, tempoExposicaoTabacoUnidadeMedida, PROP_TEMPO_EXPOSICAO_TABACO_UNIDADE_MEDIDA);
    }

    /**
     * Set the value related to the column: tempo_exposicao_tabaco_um
     *
     * @param tempoExposicaoTabacoUnidadeMedida the tempo_exposicao_tabaco_um value
     */
    public void setTempoExposicaoTabacoUnidadeMedida(java.lang.Long tempoExposicaoTabacoUnidadeMedida) {
//        java.lang.Long tempoExposicaoTabacoUnidadeMedidaOld = this.tempoExposicaoTabacoUnidadeMedida;
        this.tempoExposicaoTabacoUnidadeMedida = tempoExposicaoTabacoUnidadeMedida;
//        this.getPropertyChangeSupport().firePropertyChange ("tempoExposicaoTabacoUnidadeMedida", tempoExposicaoTabacoUnidadeMedidaOld, tempoExposicaoTabacoUnidadeMedida);
    }


    /**
     * Return the value associated with the column: conduta_geral_afastamento_situacao_desgaste_mental
     */
    public java.lang.Long getCondutaGeralAfastamentoSituacaoDesgasteMental() {
        return getPropertyValue(this, condutaGeralAfastamentoSituacaoDesgasteMental, PROP_CONDUTA_GERAL_AFASTAMENTO_SITUACAO_DESGASTE_MENTAL);
    }

    /**
     * Set the value related to the column: conduta_geral_afastamento_situacao_desgaste_mental
     *
     * @param condutaGeralAfastamentoSituacaoDesgasteMental the conduta_geral_afastamento_situacao_desgaste_mental value
     */
    public void setCondutaGeralAfastamentoSituacaoDesgasteMental(java.lang.Long condutaGeralAfastamentoSituacaoDesgasteMental) {
//        java.lang.Long condutaGeralAfastamentoSituacaoDesgasteMentalOld = this.condutaGeralAfastamentoSituacaoDesgasteMental;
        this.condutaGeralAfastamentoSituacaoDesgasteMental = condutaGeralAfastamentoSituacaoDesgasteMental;
//        this.getPropertyChangeSupport().firePropertyChange ("condutaGeralAfastamentoSituacaoDesgasteMental", condutaGeralAfastamentoSituacaoDesgasteMentalOld, condutaGeralAfastamentoSituacaoDesgasteMental);
    }


    /**
     * Return the value associated with the column: conduta_geral_protecao_individual
     */
    public java.lang.Long getCondutaGeralProtecaoIndividual() {
        return getPropertyValue(this, condutaGeralProtecaoIndividual, PROP_CONDUTA_GERAL_PROTECAO_INDIVIDUAL);
    }

    /**
     * Set the value related to the column: conduta_geral_protecao_individual
     *
     * @param condutaGeralProtecaoIndividual the conduta_geral_protecao_individual value
     */
    public void setCondutaGeralProtecaoIndividual(java.lang.Long condutaGeralProtecaoIndividual) {
//        java.lang.Long condutaGeralProtecaoIndividualOld = this.condutaGeralProtecaoIndividual;
        this.condutaGeralProtecaoIndividual = condutaGeralProtecaoIndividual;
//        this.getPropertyChangeSupport().firePropertyChange ("condutaGeralProtecaoIndividual", condutaGeralProtecaoIndividualOld, condutaGeralProtecaoIndividual);
    }


    /**
     * Return the value associated with the column: conduta_geral_mudanca_organizacao_trabalho
     */
    public java.lang.Long getCondutaGeralMudancaOrganizacaoTrabalho() {
        return getPropertyValue(this, condutaGeralMudancaOrganizacaoTrabalho, PROP_CONDUTA_GERAL_MUDANCA_ORGANIZACAO_TRABALHO);
    }

    /**
     * Set the value related to the column: conduta_geral_mudanca_organizacao_trabalho
     *
     * @param condutaGeralMudancaOrganizacaoTrabalho the conduta_geral_mudanca_organizacao_trabalho value
     */
    public void setCondutaGeralMudancaOrganizacaoTrabalho(java.lang.Long condutaGeralMudancaOrganizacaoTrabalho) {
//        java.lang.Long condutaGeralMudancaOrganizacaoTrabalhoOld = this.condutaGeralMudancaOrganizacaoTrabalho;
        this.condutaGeralMudancaOrganizacaoTrabalho = condutaGeralMudancaOrganizacaoTrabalho;
//        this.getPropertyChangeSupport().firePropertyChange ("condutaGeralMudancaOrganizacaoTrabalho", condutaGeralMudancaOrganizacaoTrabalhoOld, condutaGeralMudancaOrganizacaoTrabalho);
    }


    /**
     * Return the value associated with the column: conduta_geral_nenhum
     */
    public java.lang.Long getCondutaGeralNenhum() {
        return getPropertyValue(this, condutaGeralNenhum, PROP_CONDUTA_GERAL_NENHUM);
    }

    /**
     * Set the value related to the column: conduta_geral_nenhum
     *
     * @param condutaGeralNenhum the conduta_geral_nenhum value
     */
    public void setCondutaGeralNenhum(java.lang.Long condutaGeralNenhum) {
//        java.lang.Long condutaGeralNenhumOld = this.condutaGeralNenhum;
        this.condutaGeralNenhum = condutaGeralNenhum;
//        this.getPropertyChangeSupport().firePropertyChange ("condutaGeralNenhum", condutaGeralNenhumOld, condutaGeralNenhum);
    }


    /**
     * Return the value associated with the column: conduta_geral_protecao_coletiva
     */
    public java.lang.Long getCondutaGeralProtecaoColetiva() {
        return getPropertyValue(this, condutaGeralProtecaoColetiva, PROP_CONDUTA_GERAL_PROTECAO_COLETIVA);
    }

    /**
     * Set the value related to the column: conduta_geral_protecao_coletiva
     *
     * @param condutaGeralProtecaoColetiva the conduta_geral_protecao_coletiva value
     */
    public void setCondutaGeralProtecaoColetiva(java.lang.Long condutaGeralProtecaoColetiva) {
//        java.lang.Long condutaGeralProtecaoColetivaOld = this.condutaGeralProtecaoColetiva;
        this.condutaGeralProtecaoColetiva = condutaGeralProtecaoColetiva;
//        this.getPropertyChangeSupport().firePropertyChange ("condutaGeralProtecaoColetiva", condutaGeralProtecaoColetivaOld, condutaGeralProtecaoColetiva);
    }


    /**
     * Return the value associated with the column: conduta_geral_afastamento_local_trabalho
     */
    public java.lang.Long getCondutaGeralAfastamentoLocalTrabalho() {
        return getPropertyValue(this, condutaGeralAfastamentoLocalTrabalho, PROP_CONDUTA_GERAL_AFASTAMENTO_LOCAL_TRABALHO);
    }

    /**
     * Set the value related to the column: conduta_geral_afastamento_local_trabalho
     *
     * @param condutaGeralAfastamentoLocalTrabalho the conduta_geral_afastamento_local_trabalho value
     */
    public void setCondutaGeralAfastamentoLocalTrabalho(java.lang.Long condutaGeralAfastamentoLocalTrabalho) {
//        java.lang.Long condutaGeralAfastamentoLocalTrabalhoOld = this.condutaGeralAfastamentoLocalTrabalho;
        this.condutaGeralAfastamentoLocalTrabalho = condutaGeralAfastamentoLocalTrabalho;
//        this.getPropertyChangeSupport().firePropertyChange ("condutaGeralAfastamentoLocalTrabalho", condutaGeralAfastamentoLocalTrabalhoOld, condutaGeralAfastamentoLocalTrabalho);
    }


    /**
     * Return the value associated with the column: conduta_geral_outros
     */
    public java.lang.String getCondutaGeralOutros() {
        return getPropertyValue(this, condutaGeralOutros, PROP_CONDUTA_GERAL_OUTROS);
    }

    /**
     * Set the value related to the column: conduta_geral_outros
     *
     * @param condutaGeralOutros the conduta_geral_outros value
     */
    public void setCondutaGeralOutros(java.lang.String condutaGeralOutros) {
//        java.lang.String condutaGeralOutrosOld = this.condutaGeralOutros;
        this.condutaGeralOutros = condutaGeralOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("condutaGeralOutros", condutaGeralOutrosOld, condutaGeralOutros);
    }


    /**
     * Return the value associated with the column: outros_trabalhores_mesma_doenca_local_trabalho
     */
    public java.lang.Long getOutrosTrabalhadoresMesmaDoencaLocalTrabalho() {
        return getPropertyValue(this, outrosTrabalhadoresMesmaDoencaLocalTrabalho, PROP_OUTROS_TRABALHADORES_MESMA_DOENCA_LOCAL_TRABALHO);
    }

    /**
     * Set the value related to the column: outros_trabalhores_mesma_doenca_local_trabalho
     *
     * @param outrosTrabalhadoresMesmaDoencaLocalTrabalho the outros_trabalhores_mesma_doenca_local_trabalho value
     */
    public void setOutrosTrabalhadoresMesmaDoencaLocalTrabalho(java.lang.Long outrosTrabalhadoresMesmaDoencaLocalTrabalho) {
//        java.lang.Long outrosTrabalhadoresMesmaDoencaLocalTrabalhoOld = this.outrosTrabalhadoresMesmaDoencaLocalTrabalho;
        this.outrosTrabalhadoresMesmaDoencaLocalTrabalho = outrosTrabalhadoresMesmaDoencaLocalTrabalho;
//        this.getPropertyChangeSupport().firePropertyChange ("outrosTrabalhadoresMesmaDoencaLocalTrabalho", outrosTrabalhadoresMesmaDoencaLocalTrabalhoOld, outrosTrabalhadoresMesmaDoencaLocalTrabalho);
    }


    /**
     * Return the value associated with the column: paciente_encaminhado_capes
     */
    public java.lang.Long getPacienteEncaminhadoCapes() {
        return getPropertyValue(this, pacienteEncaminhadoCapes, PROP_PACIENTE_ENCAMINHADO_CAPES);
    }

    /**
     * Set the value related to the column: paciente_encaminhado_capes
     *
     * @param pacienteEncaminhadoCapes the paciente_encaminhado_capes value
     */
    public void setPacienteEncaminhadoCapes(java.lang.Long pacienteEncaminhadoCapes) {
//        java.lang.Long pacienteEncaminhadoCapesOld = this.pacienteEncaminhadoCapes;
        this.pacienteEncaminhadoCapes = pacienteEncaminhadoCapes;
//        this.getPropertyChangeSupport().firePropertyChange ("pacienteEncaminhadoCapes", pacienteEncaminhadoCapesOld, pacienteEncaminhadoCapes);
    }


    /**
     * Return the value associated with the column: evolucao_caso
     */
    public java.lang.Long getEvolucaoCaso() {
        return getPropertyValue(this, evolucaoCaso, PROP_EVOLUCAO_CASO);
    }

    /**
     * Set the value related to the column: evolucao_caso
     *
     * @param evolucaoCaso the evolucao_caso value
     */
    public void setEvolucaoCaso(java.lang.Long evolucaoCaso) {
//        java.lang.Long evolucaoCasoOld = this.evolucaoCaso;
        this.evolucaoCaso = evolucaoCaso;
//        this.getPropertyChangeSupport().firePropertyChange ("evolucaoCaso", evolucaoCasoOld, evolucaoCaso);
    }


    /**
     * Return the value associated with the column: dt_obito
     */
    public java.util.Date getDataObito() {
        return getPropertyValue(this, dataObito, PROP_DATA_OBITO);
    }

    /**
     * Set the value related to the column: dt_obito
     *
     * @param dataObito the dt_obito value
     */
    public void setDataObito(java.util.Date dataObito) {
//        java.util.Date dataObitoOld = this.dataObito;
        this.dataObito = dataObito;
//        this.getPropertyChangeSupport().firePropertyChange ("dataObito", dataObitoOld, dataObito);
    }


    /**
     * Return the value associated with the column: emitida_cat
     */
    public java.lang.Long getEmitidaCAT() {
        return getPropertyValue(this, emitidaCAT, PROP_EMITIDA_C_A_T);
    }

    /**
     * Set the value related to the column: emitida_cat
     *
     * @param emitidaCAT the emitida_cat value
     */
    public void setEmitidaCAT(java.lang.Long emitidaCAT) {
//        java.lang.Long emitidaCATOld = this.emitidaCAT;
        this.emitidaCAT = emitidaCAT;
//        this.getPropertyChangeSupport().firePropertyChange ("emitidaCAT", emitidaCATOld, emitidaCAT);
    }


    /**
     * Return the value associated with the column: cd_registro_agravo
     */
    public br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo getRegistroAgravo() {
        return getPropertyValue(this, registroAgravo, PROP_REGISTRO_AGRAVO);
    }

    /**
     * Set the value related to the column: cd_registro_agravo
     *
     * @param registroAgravo the cd_registro_agravo value
     */
    public void setRegistroAgravo(br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo) {
//        br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravoOld = this.registroAgravo;
        this.registroAgravo = registroAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("registroAgravo", registroAgravoOld, registroAgravo);
    }


    /**
     * Return the value associated with the column: ocupacao_cbo
     */
    public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getOcupacaoCbo() {
        return getPropertyValue(this, ocupacaoCbo, PROP_OCUPACAO_CBO);
    }

    /**
     * Set the value related to the column: ocupacao_cbo
     *
     * @param ocupacaoCbo the ocupacao_cbo value
     */
    public void setOcupacaoCbo(br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCboOld = this.ocupacaoCbo;
        this.ocupacaoCbo = ocupacaoCbo;
//        this.getPropertyChangeSupport().firePropertyChange ("ocupacaoCbo", ocupacaoCboOld, ocupacaoCbo);
    }


    /**
     * Return the value associated with the column: cd_usuario_encerramento
     */
    public br.com.ksisolucoes.vo.controle.Usuario getUsuarioEncerramento() {
        return getPropertyValue(this, usuarioEncerramento, PROP_USUARIO_ENCERRAMENTO);
    }

    /**
     * Set the value related to the column: cd_usuario_encerramento
     *
     * @param usuarioEncerramento the cd_usuario_encerramento value
     */
    public void setUsuarioEncerramento(br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramentoOld = this.usuarioEncerramento;
        this.usuarioEncerramento = usuarioEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioEncerramento", usuarioEncerramentoOld, usuarioEncerramento);
    }


    /**
     * Return the value associated with the column: cd_empresa_contratante
     */
    public br.com.ksisolucoes.vo.basico.Empresa getEmpresaContratante() {
        return getPropertyValue(this, empresaContratante, PROP_EMPRESA_CONTRATANTE);
    }

    /**
     * Set the value related to the column: cd_empresa_contratante
     *
     * @param empresaContratante the cd_empresa_contratante value
     */
    public void setEmpresaContratante(br.com.ksisolucoes.vo.basico.Empresa empresaContratante) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaContratanteOld = this.empresaContratante;
        this.empresaContratante = empresaContratante;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaContratante", empresaContratanteOld, empresaContratante);
    }


    /**
     * Return the value associated with the column: cd_diagnostico_especifico_cid
     */
    public br.com.ksisolucoes.vo.prontuario.basico.Cid getCidDiagnosticoEspecifico() {
        return getPropertyValue(this, cidDiagnosticoEspecifico, PROP_CID_DIAGNOSTICO_ESPECIFICO);
    }

    /**
     * Set the value related to the column: cd_diagnostico_especifico_cid
     *
     * @param cidDiagnosticoEspecifico the cd_diagnostico_especifico_cid value
     */
    public void setCidDiagnosticoEspecifico(br.com.ksisolucoes.vo.prontuario.basico.Cid cidDiagnosticoEspecifico) {
//        br.com.ksisolucoes.vo.prontuario.basico.Cid cidDiagnosticoEspecificoOld = this.cidDiagnosticoEspecifico;
        this.cidDiagnosticoEspecifico = cidDiagnosticoEspecifico;
//        this.getPropertyChangeSupport().firePropertyChange ("cidDiagnosticoEspecifico", cidDiagnosticoEspecificoOld, cidDiagnosticoEspecifico);
    }


    public boolean equals(Object obj) {
        if (null == obj) return false;
        if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoAcidenteTrabalhoTranstornoMental)) return false;
        else {
            br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoAcidenteTrabalhoTranstornoMental investigacaoAgravoAcidenteTrabalhoTranstornoMental = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoAcidenteTrabalhoTranstornoMental) obj;
            if (null == this.getCodigo() || null == investigacaoAgravoAcidenteTrabalhoTranstornoMental.getCodigo()) return false;
            else return (this.getCodigo().equals(investigacaoAgravoAcidenteTrabalhoTranstornoMental.getCodigo()));
        }
    }

    public int hashCode() {
        if (Integer.MIN_VALUE == this.hashCode) {
            if (null == this.getCodigo()) return super.hashCode();
            else {
                String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
                this.hashCode = hashStr.hashCode();
            }
        }
        return this.hashCode;
    }


    public String toString() {
        return super.toString();
    }

    private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
        if (this.retornoValidacao == null) {
            this.retornoValidacao = new RetornoValidacao();
        }
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
        this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}