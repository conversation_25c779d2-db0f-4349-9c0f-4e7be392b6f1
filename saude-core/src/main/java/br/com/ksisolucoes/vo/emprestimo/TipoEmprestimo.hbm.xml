<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.emprestimo"  >
    <class name="TipoEmprestimo" table="tipo_emprestimo">
            <id
                    name="codigo"
                    type="java.lang.Long"
                    column="cd_tp_emprestimo"
            >
            <generator class="assigned" />
            </id> <version column="version" name="version" type="long" />
            
            <property
                    name="descricaoTipoEmprestimo"
                    column="ds_tp_emprestimo"
                    type="java.lang.String"
                    not-null="true"
                    length="50"
            />
            
            <property
                    name="tipoEmprestimo"
                    column="tp_emprestimo"
                    type="java.lang.Long"
                    not-null="true"
            />
            
            <property
                    name="tipoSignatario"
                    column="tp_signatario"
                    type="java.lang.Long"
                    not-null="true"
            />

            <many-to-one
                    name="tipoDocumentoEstoque"
                    class="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                    column="cod_tip_doc_estoque"
                    not-null="true"
            />

            <many-to-one
                    name="tipoDocumentoEstorno"
                    class="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                    column="cod_tip_doc_estorno"
                    not-null="true"
            />
            
    </class>
</hibernate-mapping>