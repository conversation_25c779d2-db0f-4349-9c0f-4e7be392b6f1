<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="ExameCotaPpi" table="exame_cota_ppi" >
         
		<id
			name="codigo"
			type="java.lang.Long"
			column="cd_exa_cota_ppi"
		>
			<generator class="assigned"/>
		</id> <version column="version" name="version" type="long" />
          
        <property
            name="tetoFisico"
            column="teto_fisico"
            type="java.lang.Double"
            not-null="true"
        />

        <property
            name="tetoFinanceiro"
            column="teto_financeiro"
            type="java.lang.Double"
            not-null="true"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.TipoExame"
            column="cd_tp_exame"
            name="tipoExame"
            not-null="true"
        />
        
        <property
            name="tipoTeto"
            column="tp_teto"
            type="java.lang.Long"
            not-null="true"
        />

        <property
            name="recursoProprio"
            column="recurso_proprio"
            type="java.lang.Double"
            not-null="false"
        />

    </class>
</hibernate-mapping>
