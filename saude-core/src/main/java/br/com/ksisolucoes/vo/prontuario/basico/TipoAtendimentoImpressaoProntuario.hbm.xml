<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="TipoAtendimentoImpressaoProntuario" table="tipo_atendimento_impressao_prontuario" >
        
        <id
            name="codigo"
            type="java.lang.Long"  
            column="cd_tp_atend_impr_pront"
        > 
            <generator class="assigned"/>
        </id>
        
        <version column="version" name="version" type="long" />
        
        <many-to-one
            name="tipoAtendimento"
            class="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"
            column="cd_tp_atendimento"
        />

        <property 
            name="tipoRegistro"
            column="tipo_registro"
            type="java.lang.Long"
            not-null="true"
         />
         
    </class>
</hibernate-mapping>