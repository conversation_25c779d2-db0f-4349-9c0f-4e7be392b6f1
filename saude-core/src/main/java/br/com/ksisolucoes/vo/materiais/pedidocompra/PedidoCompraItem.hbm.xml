<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.materiais.pedidocompra">
    <class name="PedidoCompraItem" table="pedido_compra_item">
        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_pedido_compra_item"
        >
            <generator class="assigned"/>
        </id>
        <version column="version" name="version" type="long"/>

        <many-to-one
                name="pedidoCompra"
                class="br.com.ksisolucoes.vo.materiais.pedidocompra.PedidoCompra"
                column="cd_pedido_compra"
                not-null="true"
        />

        <many-to-one
                name="produto"
                class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
                column="cod_pro"
                not-null="true"
        />

        <property
                name="quantidadeItem"
                column="qtd_item"
                type="java.math.BigDecimal"
                not-null="false"
        />

        <property
                name="observacao"
                column="observacao"
                type="java.lang.String"
                not-null="false"
                length="512"
        />

        <property
                name="status"
                column="status"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                name="quantidadeRecebida"
                column="qtd_recebida"
                type="java.math.BigDecimal"
                not-null="false"
        />

        <many-to-one
                name="usuario"
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario"
        />

        <property
                name="dataUsuario"
                column="dt_usuario"
                type="timestamp"
                not-null="true"
        />

        <property
                name="dataCancelamento"
                column="dt_cancelamento"
                type="timestamp"
                not-null="false"
        />

        <many-to-one
                name="usuarioCancelamento"
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usu_can"
        />

        <property
                name="quantidadeCancelada"
                column="qtd_cancelada"
                type="java.math.BigDecimal"
                not-null="false"
        />

    </class>
</hibernate-mapping>