<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.cadsus"  >
    <class name="VisitaDomiciliarMotivo" table="visita_domiciliar_motivo">

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_visita_motivo"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.VisitaDomiciliar"
            column="cd_visita"
            name="visita"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.MotivoVisitaDomiciliar"
            column="cd_motivo_visita"
            name="motivoVisitaDomicilar"
            not-null="true"
        />

    </class>
</hibernate-mapping>