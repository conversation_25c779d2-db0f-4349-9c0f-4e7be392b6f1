package br.com.ksisolucoes.vo.service.sms;

import java.io.Serializable;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.service.sms.base.BaseSmsResposta;



public class SmsResposta extends BaseSmsResposta implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public SmsResposta () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public SmsResposta (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public SmsResposta (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.service.sms.SmsMensagem smsMensagem,
		java.util.Date dataCadastro,
		java.util.Date dataResposta,
		java.lang.String mensagem) {

		super (
			codigo,
			smsMensagem,
			dataCadastro,
			dataResposta,
			mensagem);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}