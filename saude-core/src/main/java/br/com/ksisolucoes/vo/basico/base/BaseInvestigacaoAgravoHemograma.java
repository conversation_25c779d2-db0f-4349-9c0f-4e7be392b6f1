package br.com.ksisolucoes.vo.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the investigacao_agravo_hemograma table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agravo_hemograma"
 */

public abstract class BaseInvestigacaoAgravoHemograma extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoHemograma";
	public static final String PROP_DATA_COLETA = "dataColeta";
	public static final String PROP_LEUCOCITOS = "leucocitos";
	public static final String PROP_HEMACIAS = "hemacias";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_HEMOGLOBINA = "hemoglobina";
	public static final String PROP_NEUTROFILOS = "neutrofilos";
	public static final String PROP_HEMATOCRITO = "hematocrito";
	public static final String PROP_EOSINOFITOS = "eosinofitos";
	public static final String PROP_INVESTIGACAO_AGRAVO = "investigacaoAgravo";
	public static final String PROP_BASTOES = "bastoes";
	public static final String PROP_LINFOCITOS = "linfocitos";
	public static final String PROP_PLAQUETAS = "plaquetas";
	public static final String PROP_MONOCITOS = "monocitos";


	// constructors
	public BaseInvestigacaoAgravoHemograma () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoHemograma (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoAgravoHemograma (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.InvestigacaoAgravo investigacaoAgravo,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setInvestigacaoAgravo(investigacaoAgravo);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataColeta;
	private java.lang.String hemacias;
	private java.lang.String hemoglobina;
	private java.lang.String hematocrito;
	private java.lang.String plaquetas;
	private java.lang.String bastoes;
	private java.lang.String neutrofilos;
	private java.lang.String linfocitos;
	private java.lang.String eosinofitos;
	private java.lang.String leucocitos;
	private java.lang.String monocitos;
	private java.util.Date dataCadastro;

	// many to one
	private br.com.ksisolucoes.vo.basico.InvestigacaoAgravo investigacaoAgravo;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_investigacao_agravo_hemograma"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_coleta
	 */
	public java.util.Date getDataColeta () {
		return getPropertyValue(this, dataColeta, PROP_DATA_COLETA); 
	}

	/**
	 * Set the value related to the column: dt_coleta
	 * @param dataColeta the dt_coleta value
	 */
	public void setDataColeta (java.util.Date dataColeta) {
//        java.util.Date dataColetaOld = this.dataColeta;
		this.dataColeta = dataColeta;
//        this.getPropertyChangeSupport().firePropertyChange ("dataColeta", dataColetaOld, dataColeta);
	}



	/**
	 * Return the value associated with the column: hemacias
	 */
	public java.lang.String getHemacias () {
		return getPropertyValue(this, hemacias, PROP_HEMACIAS); 
	}

	/**
	 * Set the value related to the column: hemacias
	 * @param hemacias the hemacias value
	 */
	public void setHemacias (java.lang.String hemacias) {
//        java.lang.String hemaciasOld = this.hemacias;
		this.hemacias = hemacias;
//        this.getPropertyChangeSupport().firePropertyChange ("hemacias", hemaciasOld, hemacias);
	}



	/**
	 * Return the value associated with the column: hemoglobina
	 */
	public java.lang.String getHemoglobina () {
		return getPropertyValue(this, hemoglobina, PROP_HEMOGLOBINA); 
	}

	/**
	 * Set the value related to the column: hemoglobina
	 * @param hemoglobina the hemoglobina value
	 */
	public void setHemoglobina (java.lang.String hemoglobina) {
//        java.lang.String hemoglobinaOld = this.hemoglobina;
		this.hemoglobina = hemoglobina;
//        this.getPropertyChangeSupport().firePropertyChange ("hemoglobina", hemoglobinaOld, hemoglobina);
	}



	/**
	 * Return the value associated with the column: hematocrito
	 */
	public java.lang.String getHematocrito () {
		return getPropertyValue(this, hematocrito, PROP_HEMATOCRITO); 
	}

	/**
	 * Set the value related to the column: hematocrito
	 * @param hematocrito the hematocrito value
	 */
	public void setHematocrito (java.lang.String hematocrito) {
//        java.lang.String hematocritoOld = this.hematocrito;
		this.hematocrito = hematocrito;
//        this.getPropertyChangeSupport().firePropertyChange ("hematocrito", hematocritoOld, hematocrito);
	}



	/**
	 * Return the value associated with the column: plaquetas
	 */
	public java.lang.String getPlaquetas () {
		return getPropertyValue(this, plaquetas, PROP_PLAQUETAS); 
	}

	/**
	 * Set the value related to the column: plaquetas
	 * @param plaquetas the plaquetas value
	 */
	public void setPlaquetas (java.lang.String plaquetas) {
//        java.lang.String plaquetasOld = this.plaquetas;
		this.plaquetas = plaquetas;
//        this.getPropertyChangeSupport().firePropertyChange ("plaquetas", plaquetasOld, plaquetas);
	}



	/**
	 * Return the value associated with the column: bastoes
	 */
	public java.lang.String getBastoes () {
		return getPropertyValue(this, bastoes, PROP_BASTOES); 
	}

	/**
	 * Set the value related to the column: bastoes
	 * @param bastoes the bastoes value
	 */
	public void setBastoes (java.lang.String bastoes) {
//        java.lang.String bastoesOld = this.bastoes;
		this.bastoes = bastoes;
//        this.getPropertyChangeSupport().firePropertyChange ("bastoes", bastoesOld, bastoes);
	}



	/**
	 * Return the value associated with the column: neutrofilos
	 */
	public java.lang.String getNeutrofilos () {
		return getPropertyValue(this, neutrofilos, PROP_NEUTROFILOS); 
	}

	/**
	 * Set the value related to the column: neutrofilos
	 * @param neutrofilos the neutrofilos value
	 */
	public void setNeutrofilos (java.lang.String neutrofilos) {
//        java.lang.String neutrofilosOld = this.neutrofilos;
		this.neutrofilos = neutrofilos;
//        this.getPropertyChangeSupport().firePropertyChange ("neutrofilos", neutrofilosOld, neutrofilos);
	}



	/**
	 * Return the value associated with the column: linfocitos
	 */
	public java.lang.String getLinfocitos () {
		return getPropertyValue(this, linfocitos, PROP_LINFOCITOS); 
	}

	/**
	 * Set the value related to the column: linfocitos
	 * @param linfocitos the linfocitos value
	 */
	public void setLinfocitos (java.lang.String linfocitos) {
//        java.lang.String linfocitosOld = this.linfocitos;
		this.linfocitos = linfocitos;
//        this.getPropertyChangeSupport().firePropertyChange ("linfocitos", linfocitosOld, linfocitos);
	}



	/**
	 * Return the value associated with the column: eosinofitos
	 */
	public java.lang.String getEosinofitos () {
		return getPropertyValue(this, eosinofitos, PROP_EOSINOFITOS); 
	}

	/**
	 * Set the value related to the column: eosinofitos
	 * @param eosinofitos the eosinofitos value
	 */
	public void setEosinofitos (java.lang.String eosinofitos) {
//        java.lang.String eosinofitosOld = this.eosinofitos;
		this.eosinofitos = eosinofitos;
//        this.getPropertyChangeSupport().firePropertyChange ("eosinofitos", eosinofitosOld, eosinofitos);
	}



	/**
	 * Return the value associated with the column: leucocitos
	 */
	public java.lang.String getLeucocitos () {
		return getPropertyValue(this, leucocitos, PROP_LEUCOCITOS); 
	}

	/**
	 * Set the value related to the column: leucocitos
	 * @param leucocitos the leucocitos value
	 */
	public void setLeucocitos (java.lang.String leucocitos) {
//        java.lang.String leucocitosOld = this.leucocitos;
		this.leucocitos = leucocitos;
//        this.getPropertyChangeSupport().firePropertyChange ("leucocitos", leucocitosOld, leucocitos);
	}



	/**
	 * Return the value associated with the column: monocitos
	 */
	public java.lang.String getMonocitos () {
		return getPropertyValue(this, monocitos, PROP_MONOCITOS); 
	}

	/**
	 * Set the value related to the column: monocitos
	 * @param monocitos the monocitos value
	 */
	public void setMonocitos (java.lang.String monocitos) {
//        java.lang.String monocitosOld = this.monocitos;
		this.monocitos = monocitos;
//        this.getPropertyChangeSupport().firePropertyChange ("monocitos", monocitosOld, monocitos);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: cd_investigacao_agravo
	 */
	public br.com.ksisolucoes.vo.basico.InvestigacaoAgravo getInvestigacaoAgravo () {
		return getPropertyValue(this, investigacaoAgravo, PROP_INVESTIGACAO_AGRAVO); 
	}

	/**
	 * Set the value related to the column: cd_investigacao_agravo
	 * @param investigacaoAgravo the cd_investigacao_agravo value
	 */
	public void setInvestigacaoAgravo (br.com.ksisolucoes.vo.basico.InvestigacaoAgravo investigacaoAgravo) {
//        br.com.ksisolucoes.vo.basico.InvestigacaoAgravo investigacaoAgravoOld = this.investigacaoAgravo;
		this.investigacaoAgravo = investigacaoAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("investigacaoAgravo", investigacaoAgravoOld, investigacaoAgravo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.InvestigacaoAgravoHemograma)) return false;
		else {
			br.com.ksisolucoes.vo.basico.InvestigacaoAgravoHemograma investigacaoAgravoHemograma = (br.com.ksisolucoes.vo.basico.InvestigacaoAgravoHemograma) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoHemograma.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoHemograma.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}