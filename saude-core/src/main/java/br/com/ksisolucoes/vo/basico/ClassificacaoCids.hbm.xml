<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.basico"  >
	<class 
		name="ClassificacaoCids"
		table="cid_classificacao"
	>
		<id
			name="codigo"
			type="java.lang.Long"
			column="cd_classificacao"
		>
			<generator class="assigned">
			</generator>
		</id> <version column="version" name="version" type="long" />

		<property
			name="descricao"
			column="descricao"
			type="java.lang.String"
			not-null="true"
			length="150" 
		/>

		<many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.Cid"
					 name="cidAgrupador">
			<column name="cd_cid_agrupador" />
		</many-to-one>
		
		<many-to-one
				name="fichaInvestigacaoAgravo"
				class="br.com.ksisolucoes.vo.basico.FichaInvestigacaoAgravo"
				not-null="false"
		>
			<column name="cd_ficha_investigacao_agravo"/>
		</many-to-one>

		<property
				name="permiteNotificacaoConcomitante"
				column="flag_notificacao_concomitante"
				type="java.lang.Long"
				not-null="true"
		/>
		
		<property
                name="prazoEncerramento"
                column="prazo_encerramento"
                type="java.lang.Long"
                not-null="false"
        />

	</class>
</hibernate-mapping>