package br.com.ksisolucoes.vo.vacina.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the vac_calendario table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="vac_calendario"
 */

public abstract class BaseVacinaCalendario extends BaseRootVO implements Serializable {

	public static String REF = "VacinaCalendario";
	public static final String PROP_FLAG_ESPECIAL = "flagEspecial";
	public static final String PROP_FLAG_GESTANTE = "flagGestante";
	public static final String PROP_INTERVALO_MINIMO_DOSE = "intervaloMinimoDose";
	public static final String PROP_FLAG_APLICAR_ANTES_LIMITE = "flagAplicarAntesLimite";
	public static final String PROP_OPCIONAL = "opcional";
	public static final String PROP_FLAG_IDADE = "flagIdade";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_INTERVALO_MAXIMO_DOSE = "intervaloMaximoDose";
	public static final String PROP_FLAG_APLICAR_APOS_INTERVALO = "flagAplicarAposIntervalo";
	public static final String PROP_DOENCAS_EVITADAS = "doencasEvitadas";
	public static final String PROP_FLAG_PERMITE_REAPLICAR = "flagPermiteReaplicar";
	public static final String PROP_CALENDARIO = "calendario";
	public static final String PROP_DOSE = "dose";
	public static final String PROP_FLAG_IDADE_LIMITE = "flagIdadeLimite";
	public static final String PROP_STATUS = "status";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_FLAG_APLICAR_APOS_LIMITE = "flagAplicarAposLimite";
	public static final String PROP_FLAG_PERMITE_APLICAR_N_VEZES = "flagPermiteAplicarNVezes";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_FLAG_APLICAR_ANTES_INTERVALO = "flagAplicarAntesIntervalo";
	public static final String PROP_TIPO_VACINA = "tipoVacina";
	public static final String PROP_FLAG_APRAZAR = "flagAprazar";
	public static final String PROP_DESCRICAO_IDADE = "descricaoIdade";
	public static final String PROP_IDADE = "idade";
	public static final String PROP_FLAG_SEXO = "flagSexo";
	public static final String PROP_IDADE_LIMITE = "idadeLimite";


	// constructors
	public BaseVacinaCalendario () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseVacinaCalendario (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseVacinaCalendario (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vacina.TipoVacina tipoVacina,
		br.com.ksisolucoes.vo.vacina.Calendario calendario,
		java.lang.Long idade,
		java.lang.Long opcional,
		java.lang.Long dose,
		java.lang.Long status,
		java.lang.Long flagIdade,
		java.lang.Long flagPermiteReaplicar,
		java.lang.Long flagEspecial,
		java.lang.Long flagAprazar,
		java.lang.Long flagGestante,
		java.lang.Long flagSexo,
		java.lang.Long flagAplicarAposLimite,
		java.lang.Long flagAplicarAntesLimite,
		java.lang.Long flagAplicarAntesIntervalo,
		java.lang.Long flagAplicarAposIntervalo) {

		this.setCodigo(codigo);
		this.setTipoVacina(tipoVacina);
		this.setCalendario(calendario);
		this.setIdade(idade);
		this.setOpcional(opcional);
		this.setDose(dose);
		this.setStatus(status);
		this.setFlagIdade(flagIdade);
		this.setFlagPermiteReaplicar(flagPermiteReaplicar);
		this.setFlagEspecial(flagEspecial);
		this.setFlagAprazar(flagAprazar);
		this.setFlagGestante(flagGestante);
		this.setFlagSexo(flagSexo);
		this.setFlagAplicarAposLimite(flagAplicarAposLimite);
		this.setFlagAplicarAntesLimite(flagAplicarAntesLimite);
		this.setFlagAplicarAntesIntervalo(flagAplicarAntesIntervalo);
		this.setFlagAplicarAposIntervalo(flagAplicarAposIntervalo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long idade;
	private java.lang.String doencasEvitadas;
	private java.lang.String observacao;
	private java.lang.Long opcional;
	private java.lang.Long dose;
	private java.lang.Long status;
	private java.util.Date dataCancelamento;
	private java.lang.Long flagIdade;
	private java.lang.Long flagPermiteReaplicar;
	private java.lang.Long flagEspecial;
	private java.lang.Long flagAprazar;
	private java.lang.Long flagGestante;
	private java.lang.Long flagSexo;
	private java.lang.Long idadeLimite;
	private java.lang.Long flagIdadeLimite;
	private java.lang.String descricaoIdade;
	private java.lang.Long flagAplicarAposLimite;
	private java.lang.Long flagPermiteAplicarNVezes;
	private java.lang.Long intervaloMinimoDose;
	private java.lang.Long flagAplicarAntesLimite;
	private java.lang.Long flagAplicarAntesIntervalo;
	private java.lang.Long flagAplicarAposIntervalo;
	private java.lang.Long intervaloMaximoDose;

	// many to one
	private br.com.ksisolucoes.vo.vacina.TipoVacina tipoVacina;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;
	private br.com.ksisolucoes.vo.vacina.Calendario calendario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_calendario"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: idade
	 */
	public java.lang.Long getIdade () {
		return getPropertyValue(this, idade, PROP_IDADE); 
	}

	/**
	 * Set the value related to the column: idade
	 * @param idade the idade value
	 */
	public void setIdade (java.lang.Long idade) {
//        java.lang.Long idadeOld = this.idade;
		this.idade = idade;
//        this.getPropertyChangeSupport().firePropertyChange ("idade", idadeOld, idade);
	}



	/**
	 * Return the value associated with the column: doencas_evitadas
	 */
	public java.lang.String getDoencasEvitadas () {
		return getPropertyValue(this, doencasEvitadas, PROP_DOENCAS_EVITADAS); 
	}

	/**
	 * Set the value related to the column: doencas_evitadas
	 * @param doencasEvitadas the doencas_evitadas value
	 */
	public void setDoencasEvitadas (java.lang.String doencasEvitadas) {
//        java.lang.String doencasEvitadasOld = this.doencasEvitadas;
		this.doencasEvitadas = doencasEvitadas;
//        this.getPropertyChangeSupport().firePropertyChange ("doencasEvitadas", doencasEvitadasOld, doencasEvitadas);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: opcional
	 */
	public java.lang.Long getOpcional () {
		return getPropertyValue(this, opcional, PROP_OPCIONAL); 
	}

	/**
	 * Set the value related to the column: opcional
	 * @param opcional the opcional value
	 */
	public void setOpcional (java.lang.Long opcional) {
//        java.lang.Long opcionalOld = this.opcional;
		this.opcional = opcional;
//        this.getPropertyChangeSupport().firePropertyChange ("opcional", opcionalOld, opcional);
	}



	/**
	 * Return the value associated with the column: cd_doses
	 */
	public java.lang.Long getDose () {
		return getPropertyValue(this, dose, PROP_DOSE); 
	}

	/**
	 * Set the value related to the column: cd_doses
	 * @param dose the cd_doses value
	 */
	public void setDose (java.lang.Long dose) {
//        java.lang.Long doseOld = this.dose;
		this.dose = dose;
//        this.getPropertyChangeSupport().firePropertyChange ("dose", doseOld, dose);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: flag_idade
	 */
	public java.lang.Long getFlagIdade () {
		return getPropertyValue(this, flagIdade, PROP_FLAG_IDADE); 
	}

	/**
	 * Set the value related to the column: flag_idade
	 * @param flagIdade the flag_idade value
	 */
	public void setFlagIdade (java.lang.Long flagIdade) {
//        java.lang.Long flagIdadeOld = this.flagIdade;
		this.flagIdade = flagIdade;
//        this.getPropertyChangeSupport().firePropertyChange ("flagIdade", flagIdadeOld, flagIdade);
	}



	/**
	 * Return the value associated with the column: flag_permite_reaplicar
	 */
	public java.lang.Long getFlagPermiteReaplicar () {
		return getPropertyValue(this, flagPermiteReaplicar, PROP_FLAG_PERMITE_REAPLICAR); 
	}

	/**
	 * Set the value related to the column: flag_permite_reaplicar
	 * @param flagPermiteReaplicar the flag_permite_reaplicar value
	 */
	public void setFlagPermiteReaplicar (java.lang.Long flagPermiteReaplicar) {
//        java.lang.Long flagPermiteReaplicarOld = this.flagPermiteReaplicar;
		this.flagPermiteReaplicar = flagPermiteReaplicar;
//        this.getPropertyChangeSupport().firePropertyChange ("flagPermiteReaplicar", flagPermiteReaplicarOld, flagPermiteReaplicar);
	}



	/**
	 * Return the value associated with the column: flag_especial
	 */
	public java.lang.Long getFlagEspecial () {
		return getPropertyValue(this, flagEspecial, PROP_FLAG_ESPECIAL); 
	}

	/**
	 * Set the value related to the column: flag_especial
	 * @param flagEspecial the flag_especial value
	 */
	public void setFlagEspecial (java.lang.Long flagEspecial) {
//        java.lang.Long flagEspecialOld = this.flagEspecial;
		this.flagEspecial = flagEspecial;
//        this.getPropertyChangeSupport().firePropertyChange ("flagEspecial", flagEspecialOld, flagEspecial);
	}



	/**
	 * Return the value associated with the column: flag_aprazar
	 */
	public java.lang.Long getFlagAprazar () {
		return getPropertyValue(this, flagAprazar, PROP_FLAG_APRAZAR); 
	}

	/**
	 * Set the value related to the column: flag_aprazar
	 * @param flagAprazar the flag_aprazar value
	 */
	public void setFlagAprazar (java.lang.Long flagAprazar) {
//        java.lang.Long flagAprazarOld = this.flagAprazar;
		this.flagAprazar = flagAprazar;
//        this.getPropertyChangeSupport().firePropertyChange ("flagAprazar", flagAprazarOld, flagAprazar);
	}



	/**
	 * Return the value associated with the column: flag_gestante
	 */
	public java.lang.Long getFlagGestante () {
		return getPropertyValue(this, flagGestante, PROP_FLAG_GESTANTE); 
	}

	/**
	 * Set the value related to the column: flag_gestante
	 * @param flagGestante the flag_gestante value
	 */
	public void setFlagGestante (java.lang.Long flagGestante) {
//        java.lang.Long flagGestanteOld = this.flagGestante;
		this.flagGestante = flagGestante;
//        this.getPropertyChangeSupport().firePropertyChange ("flagGestante", flagGestanteOld, flagGestante);
	}



	/**
	 * Return the value associated with the column: flag_sexo
	 */
	public java.lang.Long getFlagSexo () {
		return getPropertyValue(this, flagSexo, PROP_FLAG_SEXO); 
	}

	/**
	 * Set the value related to the column: flag_sexo
	 * @param flagSexo the flag_sexo value
	 */
	public void setFlagSexo (java.lang.Long flagSexo) {
//        java.lang.Long flagSexoOld = this.flagSexo;
		this.flagSexo = flagSexo;
//        this.getPropertyChangeSupport().firePropertyChange ("flagSexo", flagSexoOld, flagSexo);
	}



	/**
	 * Return the value associated with the column: idade_limite
	 */
	public java.lang.Long getIdadeLimite () {
		return getPropertyValue(this, idadeLimite, PROP_IDADE_LIMITE); 
	}

	/**
	 * Set the value related to the column: idade_limite
	 * @param idadeLimite the idade_limite value
	 */
	public void setIdadeLimite (java.lang.Long idadeLimite) {
//        java.lang.Long idadeLimiteOld = this.idadeLimite;
		this.idadeLimite = idadeLimite;
//        this.getPropertyChangeSupport().firePropertyChange ("idadeLimite", idadeLimiteOld, idadeLimite);
	}



	/**
	 * Return the value associated with the column: flag_idade_limite
	 */
	public java.lang.Long getFlagIdadeLimite () {
		return getPropertyValue(this, flagIdadeLimite, PROP_FLAG_IDADE_LIMITE); 
	}

	/**
	 * Set the value related to the column: flag_idade_limite
	 * @param flagIdadeLimite the flag_idade_limite value
	 */
	public void setFlagIdadeLimite (java.lang.Long flagIdadeLimite) {
//        java.lang.Long flagIdadeLimiteOld = this.flagIdadeLimite;
		this.flagIdadeLimite = flagIdadeLimite;
//        this.getPropertyChangeSupport().firePropertyChange ("flagIdadeLimite", flagIdadeLimiteOld, flagIdadeLimite);
	}



	/**
	 * Return the value associated with the column: ds_idade
	 */
	public java.lang.String getDescricaoIdade () {
		return getPropertyValue(this, descricaoIdade, PROP_DESCRICAO_IDADE); 
	}

	/**
	 * Set the value related to the column: ds_idade
	 * @param descricaoIdade the ds_idade value
	 */
	public void setDescricaoIdade (java.lang.String descricaoIdade) {
//        java.lang.String descricaoIdadeOld = this.descricaoIdade;
		this.descricaoIdade = descricaoIdade;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoIdade", descricaoIdadeOld, descricaoIdade);
	}



	/**
	 * Return the value associated with the column: flag_aplicar_apos_limite
	 */
	public java.lang.Long getFlagAplicarAposLimite () {
		return getPropertyValue(this, flagAplicarAposLimite, PROP_FLAG_APLICAR_APOS_LIMITE); 
	}

	/**
	 * Set the value related to the column: flag_aplicar_apos_limite
	 * @param flagAplicarAposLimite the flag_aplicar_apos_limite value
	 */
	public void setFlagAplicarAposLimite (java.lang.Long flagAplicarAposLimite) {
//        java.lang.Long flagAplicarAposLimiteOld = this.flagAplicarAposLimite;
		this.flagAplicarAposLimite = flagAplicarAposLimite;
//        this.getPropertyChangeSupport().firePropertyChange ("flagAplicarAposLimite", flagAplicarAposLimiteOld, flagAplicarAposLimite);
	}



	/**
	 * Return the value associated with the column: flag_permite_aplicar_n_vezes
	 */
	public java.lang.Long getFlagPermiteAplicarNVezes () {
		return getPropertyValue(this, flagPermiteAplicarNVezes, PROP_FLAG_PERMITE_APLICAR_N_VEZES); 
	}

	/**
	 * Set the value related to the column: flag_permite_aplicar_n_vezes
	 * @param flagPermiteAplicarNVezes the flag_permite_aplicar_n_vezes value
	 */
	public void setFlagPermiteAplicarNVezes (java.lang.Long flagPermiteAplicarNVezes) {
//        java.lang.Long flagPermiteAplicarNVezesOld = this.flagPermiteAplicarNVezes;
		this.flagPermiteAplicarNVezes = flagPermiteAplicarNVezes;
//        this.getPropertyChangeSupport().firePropertyChange ("flagPermiteAplicarNVezes", flagPermiteAplicarNVezesOld, flagPermiteAplicarNVezes);
	}



	/**
	 * Return the value associated with the column: intervalo_minimo_dose
	 */
	public java.lang.Long getIntervaloMinimoDose () {
		return getPropertyValue(this, intervaloMinimoDose, PROP_INTERVALO_MINIMO_DOSE); 
	}

	/**
	 * Set the value related to the column: intervalo_minimo_dose
	 * @param intervaloMinimoDose the intervalo_minimo_dose value
	 */
	public void setIntervaloMinimoDose (java.lang.Long intervaloMinimoDose) {
//        java.lang.Long intervaloMinimoDoseOld = this.intervaloMinimoDose;
		this.intervaloMinimoDose = intervaloMinimoDose;
//        this.getPropertyChangeSupport().firePropertyChange ("intervaloMinimoDose", intervaloMinimoDoseOld, intervaloMinimoDose);
	}



	/**
	 * Return the value associated with the column: flag_aplicar_antes_limite
	 */
	public java.lang.Long getFlagAplicarAntesLimite () {
		return getPropertyValue(this, flagAplicarAntesLimite, PROP_FLAG_APLICAR_ANTES_LIMITE); 
	}

	/**
	 * Set the value related to the column: flag_aplicar_antes_limite
	 * @param flagAplicarAntesLimite the flag_aplicar_antes_limite value
	 */
	public void setFlagAplicarAntesLimite (java.lang.Long flagAplicarAntesLimite) {
//        java.lang.Long flagAplicarAntesLimiteOld = this.flagAplicarAntesLimite;
		this.flagAplicarAntesLimite = flagAplicarAntesLimite;
//        this.getPropertyChangeSupport().firePropertyChange ("flagAplicarAntesLimite", flagAplicarAntesLimiteOld, flagAplicarAntesLimite);
	}



	/**
	 * Return the value associated with the column: flag_aplicar_antes_intervalo
	 */
	public java.lang.Long getFlagAplicarAntesIntervalo () {
		return getPropertyValue(this, flagAplicarAntesIntervalo, PROP_FLAG_APLICAR_ANTES_INTERVALO); 
	}

	/**
	 * Set the value related to the column: flag_aplicar_antes_intervalo
	 * @param flagAplicarAntesIntervalo the flag_aplicar_antes_intervalo value
	 */
	public void setFlagAplicarAntesIntervalo (java.lang.Long flagAplicarAntesIntervalo) {
//        java.lang.Long flagAplicarAntesIntervaloOld = this.flagAplicarAntesIntervalo;
		this.flagAplicarAntesIntervalo = flagAplicarAntesIntervalo;
//        this.getPropertyChangeSupport().firePropertyChange ("flagAplicarAntesIntervalo", flagAplicarAntesIntervaloOld, flagAplicarAntesIntervalo);
	}



	/**
	 * Return the value associated with the column: flag_aplicar_apos_intervalo
	 */
	public java.lang.Long getFlagAplicarAposIntervalo () {
		return getPropertyValue(this, flagAplicarAposIntervalo, PROP_FLAG_APLICAR_APOS_INTERVALO); 
	}

	/**
	 * Set the value related to the column: flag_aplicar_apos_intervalo
	 * @param flagAplicarAposIntervalo the flag_aplicar_apos_intervalo value
	 */
	public void setFlagAplicarAposIntervalo (java.lang.Long flagAplicarAposIntervalo) {
//        java.lang.Long flagAplicarAposIntervaloOld = this.flagAplicarAposIntervalo;
		this.flagAplicarAposIntervalo = flagAplicarAposIntervalo;
//        this.getPropertyChangeSupport().firePropertyChange ("flagAplicarAposIntervalo", flagAplicarAposIntervaloOld, flagAplicarAposIntervalo);
	}



	/**
	 * Return the value associated with the column: intervalo_maximo_dose
	 */
	public java.lang.Long getIntervaloMaximoDose () {
		return getPropertyValue(this, intervaloMaximoDose, PROP_INTERVALO_MAXIMO_DOSE); 
	}

	/**
	 * Set the value related to the column: intervalo_maximo_dose
	 * @param intervaloMaximoDose the intervalo_maximo_dose value
	 */
	public void setIntervaloMaximoDose (java.lang.Long intervaloMaximoDose) {
//        java.lang.Long intervaloMaximoDoseOld = this.intervaloMaximoDose;
		this.intervaloMaximoDose = intervaloMaximoDose;
//        this.getPropertyChangeSupport().firePropertyChange ("intervaloMaximoDose", intervaloMaximoDoseOld, intervaloMaximoDose);
	}



	/**
	 * Return the value associated with the column: cd_vacina
	 */
	public br.com.ksisolucoes.vo.vacina.TipoVacina getTipoVacina () {
		return getPropertyValue(this, tipoVacina, PROP_TIPO_VACINA); 
	}

	/**
	 * Set the value related to the column: cd_vacina
	 * @param tipoVacina the cd_vacina value
	 */
	public void setTipoVacina (br.com.ksisolucoes.vo.vacina.TipoVacina tipoVacina) {
//        br.com.ksisolucoes.vo.vacina.TipoVacina tipoVacinaOld = this.tipoVacina;
		this.tipoVacina = tipoVacina;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoVacina", tipoVacinaOld, tipoVacina);
	}



	/**
	 * Return the value associated with the column: cd_usuario_canc
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_canc
	 * @param usuarioCancelamento the cd_usuario_canc value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}



	/**
	 * Return the value associated with the column: cd_calendario_vacinacao
	 */
	public br.com.ksisolucoes.vo.vacina.Calendario getCalendario () {
		return getPropertyValue(this, calendario, PROP_CALENDARIO); 
	}

	/**
	 * Set the value related to the column: cd_calendario_vacinacao
	 * @param calendario the cd_calendario_vacinacao value
	 */
	public void setCalendario (br.com.ksisolucoes.vo.vacina.Calendario calendario) {
//        br.com.ksisolucoes.vo.vacina.Calendario calendarioOld = this.calendario;
		this.calendario = calendario;
//        this.getPropertyChangeSupport().firePropertyChange ("calendario", calendarioOld, calendario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vacina.VacinaCalendario)) return false;
		else {
			br.com.ksisolucoes.vo.vacina.VacinaCalendario vacinaCalendario = (br.com.ksisolucoes.vo.vacina.VacinaCalendario) obj;
			if (null == this.getCodigo() || null == vacinaCalendario.getCodigo()) return false;
			else return (this.getCodigo().equals(vacinaCalendario.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}