<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.siab"  >
    <class name="ArquivoSiab" table="arquivo_siab" >

        <id
            name="codigo" 
            type="java.lang.Long"   
            column="cd_arquivo_siab"  
        > 
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />

        <property
            name="mes"
            not-null="true"
            type="java.lang.Long"
            column="mes"
        >
        </property>
        
        <property
            name="ano"
            not-null="true"
            type="java.lang.Long"
            column="ano"
        >
        </property>
        
        <many-to-one
            class="br.com.ksisolucoes.vo.service.AsyncProcess"
            name="asyncProcess"
        >
            <column name="cd_process"/>
        </many-to-one>

        <property
            name="pathRetorno"
            type="java.lang.String"
            column="path_retorno"
        >
        </property>

    </class>
</hibernate-mapping>
