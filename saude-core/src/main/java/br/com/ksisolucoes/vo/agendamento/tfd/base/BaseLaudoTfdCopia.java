package br.com.ksisolucoes.vo.agendamento.tfd.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the laudo_tfd_copia table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="laudo_tfd_copia"
 */

public abstract class BaseLaudoTfdCopia extends BaseRootVO implements Serializable {

	public static String REF = "LaudoTfdCopia";
	public static final String PROP_JUSTIFICATIVA_TFD = "justificativaTfd";
	public static final String PROP_TRANSPORTE_RECOMENDAVEL = "transporteRecomendavel";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_EXAME_COMPLEMENTAR_REALIZADO = "exameComplementarRealizado";
	public static final String PROP_TRATAMENTO_REALIZADO = "tratamentoRealizado";
	public static final String PROP_PROCEDIMENTO = "procedimento";
	public static final String PROP_LAUDO_TFD = "laudoTfd";
	public static final String PROP_FLAG_URGENTE = "flagUrgente";
	public static final String PROP_JUSTIFICATIVA_ACOMPANHANTE = "justificativaAcompanhante";
	public static final String PROP_OBSERVACAO_URGENTE = "observacaoUrgente";
	public static final String PROP_HISTORICO_DOENCA = "historicoDoenca";
	public static final String PROP_CARATER_ATENDIMENTO = "caraterAtendimento";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_EXAME_FISICO = "exameFisico";
	public static final String PROP_PROCEDIMENTO_TRATAMENTO_SOLICITADO = "procedimentoTratamentoSolicitado";
	public static final String PROP_NOME_PROFISSIONAL = "nomeProfissional";
	public static final String PROP_CID = "cid";
	public static final String PROP_PROFISSIONAL = "profissional";
	public static final String PROP_DATA_LAUDO = "dataLaudo";
	public static final String PROP_DIAGNOSTICO_PROVAVEL = "diagnosticoProvavel";
	public static final String PROP_TIPO_PROCEDIMENTO = "tipoProcedimento";
	public static final String PROP_DATA_COPIA = "dataCopia";
	public static final String PROP_JUSTIFICATIVA_TRANSPORTE = "justificativaTransporte";


	// constructors
	public BaseLaudoTfdCopia () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseLaudoTfdCopia (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataLaudo;
	private java.util.Date dataCopia;
	private java.lang.Long caraterAtendimento;
	private java.lang.String historicoDoenca;
	private java.lang.String exameFisico;
	private java.lang.String diagnosticoProvavel;
	private java.lang.String exameComplementarRealizado;
	private java.lang.String tratamentoRealizado;
	private java.lang.String procedimentoTratamentoSolicitado;
	private java.lang.String justificativaTfd;
	private java.lang.String justificativaAcompanhante;
	private java.lang.Long transporteRecomendavel;
	private java.lang.String justificativaTransporte;
	private java.lang.Long flagUrgente;
	private java.lang.String observacaoUrgente;
	private java.lang.String nomeProfissional;

	// many to one
	private br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd laudoTfd;
	private br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimento;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissional;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.prontuario.basico.Cid cid;
	private br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_laudo_copia"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_laudo
	 */
	public java.util.Date getDataLaudo () {
		return getPropertyValue(this, dataLaudo, PROP_DATA_LAUDO); 
	}

	/**
	 * Set the value related to the column: dt_laudo
	 * @param dataLaudo the dt_laudo value
	 */
	public void setDataLaudo (java.util.Date dataLaudo) {
//        java.util.Date dataLaudoOld = this.dataLaudo;
		this.dataLaudo = dataLaudo;
//        this.getPropertyChangeSupport().firePropertyChange ("dataLaudo", dataLaudoOld, dataLaudo);
	}



	/**
	 * Return the value associated with the column: dt_copia
	 */
	public java.util.Date getDataCopia () {
		return getPropertyValue(this, dataCopia, PROP_DATA_COPIA); 
	}

	/**
	 * Set the value related to the column: dt_copia
	 * @param dataCopia the dt_copia value
	 */
	public void setDataCopia (java.util.Date dataCopia) {
//        java.util.Date dataCopiaOld = this.dataCopia;
		this.dataCopia = dataCopia;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCopia", dataCopiaOld, dataCopia);
	}



	/**
	 * Return the value associated with the column: carater_atendimento
	 */
	public java.lang.Long getCaraterAtendimento () {
		return getPropertyValue(this, caraterAtendimento, PROP_CARATER_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: carater_atendimento
	 * @param caraterAtendimento the carater_atendimento value
	 */
	public void setCaraterAtendimento (java.lang.Long caraterAtendimento) {
//        java.lang.Long caraterAtendimentoOld = this.caraterAtendimento;
		this.caraterAtendimento = caraterAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("caraterAtendimento", caraterAtendimentoOld, caraterAtendimento);
	}



	/**
	 * Return the value associated with the column: historico_doenca
	 */
	public java.lang.String getHistoricoDoenca () {
		return getPropertyValue(this, historicoDoenca, PROP_HISTORICO_DOENCA); 
	}

	/**
	 * Set the value related to the column: historico_doenca
	 * @param historicoDoenca the historico_doenca value
	 */
	public void setHistoricoDoenca (java.lang.String historicoDoenca) {
//        java.lang.String historicoDoencaOld = this.historicoDoenca;
		this.historicoDoenca = historicoDoenca;
//        this.getPropertyChangeSupport().firePropertyChange ("historicoDoenca", historicoDoencaOld, historicoDoenca);
	}



	/**
	 * Return the value associated with the column: exame_fisico
	 */
	public java.lang.String getExameFisico () {
		return getPropertyValue(this, exameFisico, PROP_EXAME_FISICO); 
	}

	/**
	 * Set the value related to the column: exame_fisico
	 * @param exameFisico the exame_fisico value
	 */
	public void setExameFisico (java.lang.String exameFisico) {
//        java.lang.String exameFisicoOld = this.exameFisico;
		this.exameFisico = exameFisico;
//        this.getPropertyChangeSupport().firePropertyChange ("exameFisico", exameFisicoOld, exameFisico);
	}



	/**
	 * Return the value associated with the column: diagnostico_provavel
	 */
	public java.lang.String getDiagnosticoProvavel () {
		return getPropertyValue(this, diagnosticoProvavel, PROP_DIAGNOSTICO_PROVAVEL); 
	}

	/**
	 * Set the value related to the column: diagnostico_provavel
	 * @param diagnosticoProvavel the diagnostico_provavel value
	 */
	public void setDiagnosticoProvavel (java.lang.String diagnosticoProvavel) {
//        java.lang.String diagnosticoProvavelOld = this.diagnosticoProvavel;
		this.diagnosticoProvavel = diagnosticoProvavel;
//        this.getPropertyChangeSupport().firePropertyChange ("diagnosticoProvavel", diagnosticoProvavelOld, diagnosticoProvavel);
	}



	/**
	 * Return the value associated with the column: exame_complementar_realizado
	 */
	public java.lang.String getExameComplementarRealizado () {
		return getPropertyValue(this, exameComplementarRealizado, PROP_EXAME_COMPLEMENTAR_REALIZADO); 
	}

	/**
	 * Set the value related to the column: exame_complementar_realizado
	 * @param exameComplementarRealizado the exame_complementar_realizado value
	 */
	public void setExameComplementarRealizado (java.lang.String exameComplementarRealizado) {
//        java.lang.String exameComplementarRealizadoOld = this.exameComplementarRealizado;
		this.exameComplementarRealizado = exameComplementarRealizado;
//        this.getPropertyChangeSupport().firePropertyChange ("exameComplementarRealizado", exameComplementarRealizadoOld, exameComplementarRealizado);
	}



	/**
	 * Return the value associated with the column: tratamento_realizado
	 */
	public java.lang.String getTratamentoRealizado () {
		return getPropertyValue(this, tratamentoRealizado, PROP_TRATAMENTO_REALIZADO); 
	}

	/**
	 * Set the value related to the column: tratamento_realizado
	 * @param tratamentoRealizado the tratamento_realizado value
	 */
	public void setTratamentoRealizado (java.lang.String tratamentoRealizado) {
//        java.lang.String tratamentoRealizadoOld = this.tratamentoRealizado;
		this.tratamentoRealizado = tratamentoRealizado;
//        this.getPropertyChangeSupport().firePropertyChange ("tratamentoRealizado", tratamentoRealizadoOld, tratamentoRealizado);
	}



	/**
	 * Return the value associated with the column: proc_tratamento_solicitado
	 */
	public java.lang.String getProcedimentoTratamentoSolicitado () {
		return getPropertyValue(this, procedimentoTratamentoSolicitado, PROP_PROCEDIMENTO_TRATAMENTO_SOLICITADO); 
	}

	/**
	 * Set the value related to the column: proc_tratamento_solicitado
	 * @param procedimentoTratamentoSolicitado the proc_tratamento_solicitado value
	 */
	public void setProcedimentoTratamentoSolicitado (java.lang.String procedimentoTratamentoSolicitado) {
//        java.lang.String procedimentoTratamentoSolicitadoOld = this.procedimentoTratamentoSolicitado;
		this.procedimentoTratamentoSolicitado = procedimentoTratamentoSolicitado;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimentoTratamentoSolicitado", procedimentoTratamentoSolicitadoOld, procedimentoTratamentoSolicitado);
	}



	/**
	 * Return the value associated with the column: justificativa_tfd
	 */
	public java.lang.String getJustificativaTfd () {
		return getPropertyValue(this, justificativaTfd, PROP_JUSTIFICATIVA_TFD); 
	}

	/**
	 * Set the value related to the column: justificativa_tfd
	 * @param justificativaTfd the justificativa_tfd value
	 */
	public void setJustificativaTfd (java.lang.String justificativaTfd) {
//        java.lang.String justificativaTfdOld = this.justificativaTfd;
		this.justificativaTfd = justificativaTfd;
//        this.getPropertyChangeSupport().firePropertyChange ("justificativaTfd", justificativaTfdOld, justificativaTfd);
	}



	/**
	 * Return the value associated with the column: justificativa_acompanhante
	 */
	public java.lang.String getJustificativaAcompanhante () {
		return getPropertyValue(this, justificativaAcompanhante, PROP_JUSTIFICATIVA_ACOMPANHANTE); 
	}

	/**
	 * Set the value related to the column: justificativa_acompanhante
	 * @param justificativaAcompanhante the justificativa_acompanhante value
	 */
	public void setJustificativaAcompanhante (java.lang.String justificativaAcompanhante) {
//        java.lang.String justificativaAcompanhanteOld = this.justificativaAcompanhante;
		this.justificativaAcompanhante = justificativaAcompanhante;
//        this.getPropertyChangeSupport().firePropertyChange ("justificativaAcompanhante", justificativaAcompanhanteOld, justificativaAcompanhante);
	}



	/**
	 * Return the value associated with the column: transporte_recomendavel
	 */
	public java.lang.Long getTransporteRecomendavel () {
		return getPropertyValue(this, transporteRecomendavel, PROP_TRANSPORTE_RECOMENDAVEL); 
	}

	/**
	 * Set the value related to the column: transporte_recomendavel
	 * @param transporteRecomendavel the transporte_recomendavel value
	 */
	public void setTransporteRecomendavel (java.lang.Long transporteRecomendavel) {
//        java.lang.Long transporteRecomendavelOld = this.transporteRecomendavel;
		this.transporteRecomendavel = transporteRecomendavel;
//        this.getPropertyChangeSupport().firePropertyChange ("transporteRecomendavel", transporteRecomendavelOld, transporteRecomendavel);
	}



	/**
	 * Return the value associated with the column: justificativa_transporte
	 */
	public java.lang.String getJustificativaTransporte () {
		return getPropertyValue(this, justificativaTransporte, PROP_JUSTIFICATIVA_TRANSPORTE); 
	}

	/**
	 * Set the value related to the column: justificativa_transporte
	 * @param justificativaTransporte the justificativa_transporte value
	 */
	public void setJustificativaTransporte (java.lang.String justificativaTransporte) {
//        java.lang.String justificativaTransporteOld = this.justificativaTransporte;
		this.justificativaTransporte = justificativaTransporte;
//        this.getPropertyChangeSupport().firePropertyChange ("justificativaTransporte", justificativaTransporteOld, justificativaTransporte);
	}



	/**
	 * Return the value associated with the column: flag_urgente
	 */
	public java.lang.Long getFlagUrgente () {
		return getPropertyValue(this, flagUrgente, PROP_FLAG_URGENTE); 
	}

	/**
	 * Set the value related to the column: flag_urgente
	 * @param flagUrgente the flag_urgente value
	 */
	public void setFlagUrgente (java.lang.Long flagUrgente) {
//        java.lang.Long flagUrgenteOld = this.flagUrgente;
		this.flagUrgente = flagUrgente;
//        this.getPropertyChangeSupport().firePropertyChange ("flagUrgente", flagUrgenteOld, flagUrgente);
	}



	/**
	 * Return the value associated with the column: obs_urgente
	 */
	public java.lang.String getObservacaoUrgente () {
		return getPropertyValue(this, observacaoUrgente, PROP_OBSERVACAO_URGENTE); 
	}

	/**
	 * Set the value related to the column: obs_urgente
	 * @param observacaoUrgente the obs_urgente value
	 */
	public void setObservacaoUrgente (java.lang.String observacaoUrgente) {
//        java.lang.String observacaoUrgenteOld = this.observacaoUrgente;
		this.observacaoUrgente = observacaoUrgente;
//        this.getPropertyChangeSupport().firePropertyChange ("observacaoUrgente", observacaoUrgenteOld, observacaoUrgente);
	}



	/**
	 * Return the value associated with the column: nome_profissional
	 */
	public java.lang.String getNomeProfissional () {
		return getPropertyValue(this, nomeProfissional, PROP_NOME_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: nome_profissional
	 * @param nomeProfissional the nome_profissional value
	 */
	public void setNomeProfissional (java.lang.String nomeProfissional) {
//        java.lang.String nomeProfissionalOld = this.nomeProfissional;
		this.nomeProfissional = nomeProfissional;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeProfissional", nomeProfissionalOld, nomeProfissional);
	}



	/**
	 * Return the value associated with the column: cd_laudo_tfd
	 */
	public br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd getLaudoTfd () {
		return getPropertyValue(this, laudoTfd, PROP_LAUDO_TFD); 
	}

	/**
	 * Set the value related to the column: cd_laudo_tfd
	 * @param laudoTfd the cd_laudo_tfd value
	 */
	public void setLaudoTfd (br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd laudoTfd) {
//        br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd laudoTfdOld = this.laudoTfd;
		this.laudoTfd = laudoTfd;
//        this.getPropertyChangeSupport().firePropertyChange ("laudoTfd", laudoTfdOld, laudoTfd);
	}



	/**
	 * Return the value associated with the column: cd_tp_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento getTipoProcedimento () {
		return getPropertyValue(this, tipoProcedimento, PROP_TIPO_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_tp_procedimento
	 * @param tipoProcedimento the cd_tp_procedimento value
	 */
	public void setTipoProcedimento (br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimentoOld = this.tipoProcedimento;
		this.tipoProcedimento = tipoProcedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoProcedimento", tipoProcedimentoOld, tipoProcedimento);
	}



	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissional () {
		return getPropertyValue(this, profissional, PROP_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissional the cd_profissional value
	 */
	public void setProfissional (br.com.ksisolucoes.vo.cadsus.Profissional profissional) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalOld = this.profissional;
		this.profissional = profissional;
//        this.getPropertyChangeSupport().firePropertyChange ("profissional", profissionalOld, profissional);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_cid
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Cid getCid () {
		return getPropertyValue(this, cid, PROP_CID); 
	}

	/**
	 * Set the value related to the column: cd_cid
	 * @param cid the cd_cid value
	 */
	public void setCid (br.com.ksisolucoes.vo.prontuario.basico.Cid cid) {
//        br.com.ksisolucoes.vo.prontuario.basico.Cid cidOld = this.cid;
		this.cid = cid;
//        this.getPropertyChangeSupport().firePropertyChange ("cid", cidOld, cid);
	}



	/**
	 * Return the value associated with the column: cd_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento getProcedimento () {
		return getPropertyValue(this, procedimento, PROP_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_procedimento
	 * @param procedimento the cd_procedimento value
	 */
	public void setProcedimento (br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoOld = this.procedimento;
		this.procedimento = procedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimento", procedimentoOld, procedimento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfdCopia)) return false;
		else {
			br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfdCopia laudoTfdCopia = (br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfdCopia) obj;
			if (null == this.getCodigo() || null == laudoTfdCopia.getCodigo()) return false;
			else return (this.getCodigo().equals(laudoTfdCopia.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}