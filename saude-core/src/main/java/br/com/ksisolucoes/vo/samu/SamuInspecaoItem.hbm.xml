<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.samu"  >
    <class name="SamuInspecaoItem" table="samu_inspecao_item" >
        <id
            column="cd_samu_inspecao_item"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />    
        
        <many-to-one class="br.com.ksisolucoes.vo.samu.SamuInspecao" 
                     not-null="true"
                     name="samuInspecao">
            <column name="cd_samu_inspecao" />
        </many-to-one>
        
        <many-to-one class="br.com.ksisolucoes.vo.samu.SamuChecagem" 
                             not-null="true"
                             name="samuChecagem">
            <column name="cd_samu_checagem" />
        </many-to-one>
        
        <property
            column="ds_item_inspecao"
            name="descricao"
            not-null="true"
            type="java.lang.String"
        />
        
        <many-to-one class="br.com.ksisolucoes.vo.entradas.estoque.Unidade" 
                     not-null="false"
                     name="unidade">
            <column name="cod_uni" />
        </many-to-one>
        
        <property 
            name="quantidade"
            column="qtdade"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            name="quantidadeChecada"
            column="qtdade_checada"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            column="observacao"
            name="observacao"
            not-null="false"
            type="java.lang.String"
        />
        
        <property
            name="dataCadastro"
            column="dt_cadastro"
            type="timestamp"
            not-null="true"
        />
        
        <many-to-one class="br.com.ksisolucoes.vo.controle.Usuario" 
                     not-null="true"
                     name="usuario">
            <column name="cd_usuario" />
        </many-to-one>
        
        <property
            name="dataUsuario"
            column="dt_usuario"
            type="timestamp"
            not-null="true"
        />
    </class>
</hibernate-mapping>