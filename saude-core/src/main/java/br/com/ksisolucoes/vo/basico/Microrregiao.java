package br.com.ksisolucoes.vo.basico;

import br.com.ksisolucoes.vo.basico.base.BaseMicrorregiao;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;

import java.io.Serializable;


public class Microrregiao extends BaseMicrorregiao implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public Microrregiao () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public Microrregiao (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public Microrregiao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.RegionalSaude regionalSaude,
		java.lang.String descricao) {

		super (
			codigo,
			regionalSaude,
			descricao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

	@Override
	public String getDescricaoVO() {
		return getDescricao();
	}

	@Override
	public String getIdentificador() {
		return getCodigo().toString();
	}
}