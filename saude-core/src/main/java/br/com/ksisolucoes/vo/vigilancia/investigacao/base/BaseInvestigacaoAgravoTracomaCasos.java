package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the investigacao_agr_tracoma_casos table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_tracoma_casos"
 */

public abstract class BaseInvestigacaoAgravoTracomaCasos extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoTracomaCasos";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_INVESTIGACAO_AGRAVO_TRACOMA = "investigacaoAgravoTracoma";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_NUMERO_CASO = "numeroCaso";
	public static final String PROP_FORMA_CLINICA_T_I = "formaClinicaTI";
	public static final String PROP_FORMA_CLINICA_T_F = "formaClinicaTF";
	public static final String PROP_ENCAMINHADO_CIRURGIA = "encaminhadoCirurgia";
	public static final String PROP_USUARIO_ZONA = "usuarioZona";
	public static final String PROP_FORMA_CLINICA_T_T = "formaClinicaTT";
	public static final String PROP_FORMA_CLINICA_T_S = "formaClinicaTS";
	public static final String PROP_FORMA_CLINICA_C_O = "formaClinicaCO";


	// constructors
	public BaseInvestigacaoAgravoTracomaCasos () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoTracomaCasos (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoAgravoTracomaCasos (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoTracoma investigacaoAgravoTracoma,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {

		this.setCodigo(codigo);
		this.setInvestigacaoAgravoTracoma(investigacaoAgravoTracoma);
		this.setUsuarioCadsus(usuarioCadsus);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String numeroCaso;
	private java.lang.Long usuarioZona;
	private java.lang.Long formaClinicaTF;
	private java.lang.Long formaClinicaTI;
	private java.lang.Long formaClinicaTS;
	private java.lang.Long formaClinicaTT;
	private java.lang.Long formaClinicaCO;
	private java.lang.Long encaminhadoCirurgia;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoTracoma investigacaoAgravoTracoma;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="codigo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nro_caso
	 */
	public java.lang.String getNumeroCaso () {
		return getPropertyValue(this, numeroCaso, PROP_NUMERO_CASO); 
	}

	/**
	 * Set the value related to the column: nro_caso
	 * @param numeroCaso the nro_caso value
	 */
	public void setNumeroCaso (java.lang.String numeroCaso) {
//        java.lang.String numeroCasoOld = this.numeroCaso;
		this.numeroCaso = numeroCaso;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroCaso", numeroCasoOld, numeroCaso);
	}



	/**
	 * Return the value associated with the column: usuario_zona
	 */
	public java.lang.Long getUsuarioZona () {
		return getPropertyValue(this, usuarioZona, PROP_USUARIO_ZONA); 
	}

	/**
	 * Set the value related to the column: usuario_zona
	 * @param usuarioZona the usuario_zona value
	 */
	public void setUsuarioZona (java.lang.Long usuarioZona) {
//        java.lang.Long usuarioZonaOld = this.usuarioZona;
		this.usuarioZona = usuarioZona;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioZona", usuarioZonaOld, usuarioZona);
	}



	/**
	 * Return the value associated with the column: forma_clinica_tf
	 */
	public java.lang.Long getFormaClinicaTF () {
		return getPropertyValue(this, formaClinicaTF, PROP_FORMA_CLINICA_T_F); 
	}

	/**
	 * Set the value related to the column: forma_clinica_tf
	 * @param formaClinicaTF the forma_clinica_tf value
	 */
	public void setFormaClinicaTF (java.lang.Long formaClinicaTF) {
//        java.lang.Long formaClinicaTFOld = this.formaClinicaTF;
		this.formaClinicaTF = formaClinicaTF;
//        this.getPropertyChangeSupport().firePropertyChange ("formaClinicaTF", formaClinicaTFOld, formaClinicaTF);
	}



	/**
	 * Return the value associated with the column: forma_clinica_ti
	 */
	public java.lang.Long getFormaClinicaTI () {
		return getPropertyValue(this, formaClinicaTI, PROP_FORMA_CLINICA_T_I); 
	}

	/**
	 * Set the value related to the column: forma_clinica_ti
	 * @param formaClinicaTI the forma_clinica_ti value
	 */
	public void setFormaClinicaTI (java.lang.Long formaClinicaTI) {
//        java.lang.Long formaClinicaTIOld = this.formaClinicaTI;
		this.formaClinicaTI = formaClinicaTI;
//        this.getPropertyChangeSupport().firePropertyChange ("formaClinicaTI", formaClinicaTIOld, formaClinicaTI);
	}



	/**
	 * Return the value associated with the column: forma_clinica_ts
	 */
	public java.lang.Long getFormaClinicaTS () {
		return getPropertyValue(this, formaClinicaTS, PROP_FORMA_CLINICA_T_S); 
	}

	/**
	 * Set the value related to the column: forma_clinica_ts
	 * @param formaClinicaTS the forma_clinica_ts value
	 */
	public void setFormaClinicaTS (java.lang.Long formaClinicaTS) {
//        java.lang.Long formaClinicaTSOld = this.formaClinicaTS;
		this.formaClinicaTS = formaClinicaTS;
//        this.getPropertyChangeSupport().firePropertyChange ("formaClinicaTS", formaClinicaTSOld, formaClinicaTS);
	}



	/**
	 * Return the value associated with the column: forma_clinica_tt
	 */
	public java.lang.Long getFormaClinicaTT () {
		return getPropertyValue(this, formaClinicaTT, PROP_FORMA_CLINICA_T_T); 
	}

	/**
	 * Set the value related to the column: forma_clinica_tt
	 * @param formaClinicaTT the forma_clinica_tt value
	 */
	public void setFormaClinicaTT (java.lang.Long formaClinicaTT) {
//        java.lang.Long formaClinicaTTOld = this.formaClinicaTT;
		this.formaClinicaTT = formaClinicaTT;
//        this.getPropertyChangeSupport().firePropertyChange ("formaClinicaTT", formaClinicaTTOld, formaClinicaTT);
	}



	/**
	 * Return the value associated with the column: forma_clinica_co
	 */
	public java.lang.Long getFormaClinicaCO () {
		return getPropertyValue(this, formaClinicaCO, PROP_FORMA_CLINICA_C_O); 
	}

	/**
	 * Set the value related to the column: forma_clinica_co
	 * @param formaClinicaCO the forma_clinica_co value
	 */
	public void setFormaClinicaCO (java.lang.Long formaClinicaCO) {
//        java.lang.Long formaClinicaCOOld = this.formaClinicaCO;
		this.formaClinicaCO = formaClinicaCO;
//        this.getPropertyChangeSupport().firePropertyChange ("formaClinicaCO", formaClinicaCOOld, formaClinicaCO);
	}



	/**
	 * Return the value associated with the column: encaminhado_cirurgia
	 */
	public java.lang.Long getEncaminhadoCirurgia () {
		return getPropertyValue(this, encaminhadoCirurgia, PROP_ENCAMINHADO_CIRURGIA); 
	}

	/**
	 * Set the value related to the column: encaminhado_cirurgia
	 * @param encaminhadoCirurgia the encaminhado_cirurgia value
	 */
	public void setEncaminhadoCirurgia (java.lang.Long encaminhadoCirurgia) {
//        java.lang.Long encaminhadoCirurgiaOld = this.encaminhadoCirurgia;
		this.encaminhadoCirurgia = encaminhadoCirurgia;
//        this.getPropertyChangeSupport().firePropertyChange ("encaminhadoCirurgia", encaminhadoCirurgiaOld, encaminhadoCirurgia);
	}



	/**
	 * Return the value associated with the column: cd_investigacao_agr_tracoma
	 */
	public br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoTracoma getInvestigacaoAgravoTracoma () {
		return getPropertyValue(this, investigacaoAgravoTracoma, PROP_INVESTIGACAO_AGRAVO_TRACOMA); 
	}

	/**
	 * Set the value related to the column: cd_investigacao_agr_tracoma
	 * @param investigacaoAgravoTracoma the cd_investigacao_agr_tracoma value
	 */
	public void setInvestigacaoAgravoTracoma (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoTracoma investigacaoAgravoTracoma) {
//        br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoTracoma investigacaoAgravoTracomaOld = this.investigacaoAgravoTracoma;
		this.investigacaoAgravoTracoma = investigacaoAgravoTracoma;
//        this.getPropertyChangeSupport().firePropertyChange ("investigacaoAgravoTracoma", investigacaoAgravoTracomaOld, investigacaoAgravoTracoma);
	}



	/**
	 * Return the value associated with the column: cd_usuario_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usuario_cadsus
	 * @param usuarioCadsus the cd_usuario_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoTracomaCasos)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoTracomaCasos investigacaoAgravoTracomaCasos = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoTracomaCasos) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoTracomaCasos.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoTracomaCasos.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}