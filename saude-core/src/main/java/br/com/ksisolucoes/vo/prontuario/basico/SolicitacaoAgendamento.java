package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseSolicitacaoAgendamento;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

public class SolicitacaoAgendamento extends BaseSolicitacaoAgendamento implements CodigoManager {

    private static final long serialVersionUID = 1L;

    public static final Long STATUS_REGULACAO_PENDENTE = 0L;
    public static final Long STATUS_FILA_ESPERA = 1L;
    public static final Long STATUS_AGENDADO = 2L;
    public static final Long STATUS_REGULACAO_NEGADO = 3L;
    public static final Long STATUS_REGULACAO_DEVOLVIDO = 4L;
    public static final Long STATUS_REGULACAO_AGENDADO = 5L;
    public static final Long STATUS_CANCELADO = 6L;
    public static final Long STATUS_AGENDADO_FORA_REDE = 7L;
    public static final Long STATUS_AGUARDANDO_ANALISE = 8L;
    public static final Long STATUS_DEVOLVIDO = 9L;
    public static final Long STATUS_AGUARDANDO_AUTORIZACAO = 10L;
    public static final Long STATUS_FILA_ESPERA_PRESTADOR = 11L;
    public static final Long STATUS_BLOQUEADO = 12L;
    public static final Long STATUS_REGULACAO_APROVADO = 13L;

    public static final Long TIPO_CONSULTA_NORMAL = 0L;
    public static final Long TIPO_CONSULTA_RETORNO = 1L;

    public static final Long TIPO_FILA_NORMAL = 1L;
    public static final Long TIPO_FILA_REGULACAO = 2L;

    public static final Long PRIORIDADE_URGENTE = 0L;
    public static final Long PRIORIDADE_BREVIDADE = 1L;
    public static final Long PRIORIDADE_ELETIVO = 2L;
    public static final Long PRIORIDADE_POUCO_URGENTE = 3L;
    public static final Long PRIORIDADE_NAO_URGENTE = 4L;
    public static final Long SEM_PRIORIDADE = 5L;

    public static final String PROP_DESCRICAO_PRIORIDADE = "descricaoPrioridade";
    public static final String PROP_DESCRICAO_TIPO_CONSULTA = "descricaoTipoConsulta";
    public static final String PROP_DESCRICAO_SITUACAO = "descricaoSituacao";
    public static final String PROP_DESCRICAO_SITUACAO_COM_PRIORIDADE = "descricaoPrioridadeComSolicitacao";
    public static final String PROP_SOLICITAR_PRIORIDADE_FORMATADO = "solicitarPrioridadeFormatadoData";
    public static final List<Long> STATUS_REGULACAO = Arrays.asList(STATUS_REGULACAO_PENDENTE, STATUS_REGULACAO_NEGADO, STATUS_REGULACAO_DEVOLVIDO, STATUS_REGULACAO_AGENDADO);
    public static final List<Long> STATUS_PENDENTES = Arrays.asList(STATUS_AGUARDANDO_ANALISE, STATUS_AGUARDANDO_AUTORIZACAO, STATUS_FILA_ESPERA, STATUS_FILA_ESPERA_PRESTADOR, STATUS_REGULACAO_PENDENTE);

    private boolean gerarSolicitacaoPrioridade = false;
    private boolean validarExisteOutraSolicitacao = true;
    private boolean solicitacaoQuebrada = false;
    private Empresa origemSolicitacao;


//private AutoCompleteConsultaEmpresa autoCompleteOrigemSolicitacao;

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public SolicitacaoAgendamento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public SolicitacaoAgendamento (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public SolicitacaoAgendamento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimento,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		br.com.ksisolucoes.vo.cadsus.Profissional profissional,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento,
		java.util.Date dataSolicitacao,
		java.util.Date dataCadastro,
		java.lang.Long status,
		java.lang.Long flagEnviarRegulacao,
		java.lang.Long flagReenviadoRegulacao) {

		super (
			codigo,
			tipoProcedimento,
			empresa,
			profissional,
			usuarioCadsus,
			usuario,
			procedimento,
			dataSolicitacao,
			dataCadastro,
			status,
			flagEnviarRegulacao,
			flagReenviadoRegulacao);
	}

    public static String getDescricaoTipoConsulta(Long tipoConsulta) {
        if (TIPO_CONSULTA_NORMAL.equals(tipoConsulta)) {
            return Bundle.getStringApplication("rotulo_primeira_vez");
        } else if (TIPO_CONSULTA_RETORNO.equals(tipoConsulta)) {
            return Bundle.getStringApplication("rotulo_retorno");
        }
        return "";
    }

    public static String getDescricaoSituacao(Long status) {
        if (SolicitacaoAgendamento.STATUS_REGULACAO_PENDENTE.equals(status)) {
            return Bundle.getStringApplication("rotulo_pendente");
        } else if (SolicitacaoAgendamento.STATUS_FILA_ESPERA.equals(status)) {
            return Bundle.getStringApplication("rotulo_aguardando_agendamento");
        } else if (SolicitacaoAgendamento.STATUS_AGENDADO.equals(status)) {
            return Bundle.getStringApplication("rotulo_agendado");
        } else if (SolicitacaoAgendamento.STATUS_REGULACAO_NEGADO.equals(status)) {
            return Bundle.getStringApplication("rotulo_negado_regulacao");
        } else if (SolicitacaoAgendamento.STATUS_REGULACAO_AGENDADO.equals(status)) {
            return Bundle.getStringApplication("rotulo_agendado");
        } else if (SolicitacaoAgendamento.STATUS_REGULACAO_DEVOLVIDO.equals(status)) {
            return Bundle.getStringApplication("rotulo_devolvido_regulacao");
        } else if (SolicitacaoAgendamento.STATUS_CANCELADO.equals(status)) {
            return Bundle.getStringApplication("rotulo_cancelado");
        } else if (SolicitacaoAgendamento.STATUS_AGENDADO_FORA_REDE.equals(status)) {
            return Bundle.getStringApplication("rotulo_agendado_fora_rede");
        } else if (SolicitacaoAgendamento.STATUS_DEVOLVIDO.equals(status)) {
            return Bundle.getStringApplication("rotulo_devolvido");
        } else if (SolicitacaoAgendamento.STATUS_AGUARDANDO_ANALISE.equals(status)) {
            return Bundle.getStringApplication("rotulo_aguardando_analise");
        } else if (SolicitacaoAgendamento.STATUS_AGUARDANDO_AUTORIZACAO.equals(status)) {
            return Bundle.getStringApplication("rotulo_aguardando_autorizacao");
        } else if (SolicitacaoAgendamento.STATUS_FILA_ESPERA_PRESTADOR.equals(status)) {
            return Bundle.getStringApplication("rotulo_fila_espera_prestador");
        }
        return "";
    }

    public static String getDescricaoSituacaoAbreviada(Long status) {
        if (SolicitacaoAgendamento.STATUS_REGULACAO_PENDENTE.equals(status)) {
            return Bundle.getStringApplication("rotulo_pendente_abv");
        } else if (SolicitacaoAgendamento.STATUS_FILA_ESPERA.equals(status)) {
            return Bundle.getStringApplication("rotulo_aguardando_agendamento_abv");
        } else if (SolicitacaoAgendamento.STATUS_AGENDADO.equals(status)) {
            return Bundle.getStringApplication("rotulo_agendado_abv");
        } else if (SolicitacaoAgendamento.STATUS_REGULACAO_AGENDADO.equals(status)) {
            return Bundle.getStringApplication("rotulo_agendado_abv");
        } else if (SolicitacaoAgendamento.STATUS_REGULACAO_NEGADO.equals(status)) {
            return Bundle.getStringApplication("rotulo_negado_regulamentacao_abv");
        } else if (SolicitacaoAgendamento.STATUS_REGULACAO_DEVOLVIDO.equals(status)) {
            return Bundle.getStringApplication("rotulo_devolvido_regulamentacao_abv");
        } else if (SolicitacaoAgendamento.STATUS_CANCELADO.equals(status)) {
            return Bundle.getStringApplication("rotulo_cancelado_abv");
        } else if (SolicitacaoAgendamento.STATUS_AGENDADO_FORA_REDE.equals(status)) {
            return Bundle.getStringApplication("rotulo_agendado_fora_rede_abv");
        } else if (SolicitacaoAgendamento.STATUS_DEVOLVIDO.equals(status)) {
            return Bundle.getStringApplication("rotulo_devolvido_abv");
        } else if (SolicitacaoAgendamento.STATUS_AGUARDANDO_ANALISE.equals(status)) {
            return Bundle.getStringApplication("rotulo_aguardando_analise_abv");
        } else if (SolicitacaoAgendamento.STATUS_AGUARDANDO_AUTORIZACAO.equals(status)) {
            return Bundle.getStringApplication("rotulo_aguardando_autorizacao_abv");
        } else if (SolicitacaoAgendamento.STATUS_FILA_ESPERA_PRESTADOR.equals(status)) {
            return Bundle.getStringApplication("rotulo_fila_espera_prestador_abv");
        } else {
            return "";
        }
    }

    public static String getDescricaoPrioridade(Long prioridade) {
        String descricaoPrioridade = getDescricaoPrioridadeClassificacao(prioridade);

        if (descricaoPrioridade != null) {
            return descricaoPrioridade;
        }

        if (SolicitacaoAgendamento.PRIORIDADE_URGENTE.equals(prioridade)) {
            return Bundle.getStringApplication("rotulo_urgente");
        } else if (SolicitacaoAgendamento.PRIORIDADE_BREVIDADE.equals(prioridade)) {
            return Bundle.getStringApplication("rotulo_brevidade");
        } else {
            return Bundle.getStringApplication("rotulo_eletivo");
        }
    }

    public static String getDescricaoPrioridadeComSolicitacao(Long prioridade, Long SolicitarPrioridade) {
        String descricaoPrioridade = getDescricaoPrioridadeClassificacao(prioridade);

        if (descricaoPrioridade != null) {
            return descricaoPrioridade;
        }

        if (SolicitacaoAgendamento.PRIORIDADE_URGENTE.equals(prioridade)) {
            return Bundle.getStringApplication("rotulo_urgente");
        } else if (SolicitacaoAgendamento.PRIORIDADE_ELETIVO.equals(prioridade) && SolicitarPrioridade.equals(RepositoryComponentDefault.SIM_LONG)) {
            return Bundle.getStringApplication("rotulo_prioridade_solicitada");
        } else if (SolicitacaoAgendamento.PRIORIDADE_BREVIDADE.equals(prioridade)) {
            return Bundle.getStringApplication("rotulo_brevidade");
        } else {
            return Bundle.getStringApplication("rotulo_eletivo");
        }

    }

    public static String getDescricaoPrioridadeResumo(Long prioridade, Long solicitarPrioridade) {
        String descricaoPrioridade = getDescricaoPrioridadeClassificacao(prioridade);

        if (descricaoPrioridade != null) {
            return descricaoPrioridade;
        }
        if (SolicitacaoAgendamento.PRIORIDADE_URGENTE.equals(prioridade)) {
            return Bundle.getStringApplication("rotulo_urgente");
        } else if (SolicitacaoAgendamento.PRIORIDADE_BREVIDADE.equals(prioridade)) {
            return Bundle.getStringApplication("rotulo_brevidade");
        } else if (SolicitacaoAgendamento.PRIORIDADE_ELETIVO.equals(prioridade) && solicitarPrioridade.equals(RepositoryComponentDefault.NAO_LONG)) {
            return Bundle.getStringApplication("rotulo_eletivo");
        } else if (RepositoryComponentDefault.SIM_LONG.equals(solicitarPrioridade)) {
            return Bundle.getStringApplication("rotulo_prioridade_solicitada");
        } else {
            return Bundle.getStringApplication("rotulo_bloqueado");
        }
    }

    public static String getDescricaoAcamado(Long acamado) {
        if (RepositoryComponentDefault.NAO_LONG.equals(acamado))
            return RepositoryComponentDefault.SimNaoLong.NAO.descricao();
        else if (RepositoryComponentDefault.SIM_LONG.equals(acamado))
            return RepositoryComponentDefault.SimNaoLong.SIM.descricao();
        return null;
    }

    public static String getDescricaoPrioridadeClassificacao(Long prioridade) {
        Long habilitaClassificacaoRiscoEncaminhamentoEspecialista;
        String habilitaClassificacaoRiscoExame;
        try {
            habilitaClassificacaoRiscoEncaminhamentoEspecialista = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("habilitaClassificaçãoRiscoEncaminhamentoEspecialista");
            habilitaClassificacaoRiscoExame = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("habilitaClassificacaoRiscoExame");
        } catch (DAOException e) {
            throw new RuntimeException(e);
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(habilitaClassificacaoRiscoEncaminhamentoEspecialista) ||
                RepositoryComponentDefault.SIM.equals(habilitaClassificacaoRiscoExame)) {
            if (SolicitacaoAgendamento.PRIORIDADE_URGENTE.equals(prioridade)) {
                return Bundle.getStringApplication("rotulo_emergencia");
            } else if (SolicitacaoAgendamento.PRIORIDADE_BREVIDADE.equals(prioridade)) {
                return Bundle.getStringApplication("rotulo_muito_urgente");
            } else if (SolicitacaoAgendamento.PRIORIDADE_ELETIVO.equals(prioridade)) {
                return Bundle.getStringApplication("rotulo_urgente");
            } else if (SolicitacaoAgendamento.PRIORIDADE_POUCO_URGENTE.equals(prioridade)) {
                return Bundle.getStringApplication("rotulo_pouco_urgente");
            } else if (SolicitacaoAgendamento.PRIORIDADE_NAO_URGENTE.equals(prioridade)) {
                return Bundle.getStringApplication("rotulo_nao_urgente");
            } else {
                return Bundle.getStringApplication("rotulo_nao_classificado");
            }
        }
        return null;
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public String getDescricaoTipoConsulta() {
        return getDescricaoTipoConsulta(getTipoConsulta());
    }

    public String getDescricaoSituacao() {
        return getDescricaoSituacao(this.getStatus());
    }

    public String getDescricaoPrioridade() {
        return getDescricaoPrioridade(getPrioridade());
    }

    public String getDescricaoPrioridadeComSolicitacao() {
        return getDescricaoPrioridadeComSolicitacao(getPrioridade(), getSolicitarPrioridade());
    }

    public String getDescricaoPrioridadeResumo() {
        return getDescricaoPrioridadeResumo(getPrioridade(), getSolicitarPrioridade());
    }

    public boolean isGerarSolicitacaoPrioridade() {
        return gerarSolicitacaoPrioridade;
    }

    public void setGerarSolicitacaoPrioridade(boolean gerarSolicitacaoPrioridade) {
        this.gerarSolicitacaoPrioridade = gerarSolicitacaoPrioridade;
    }

    public boolean isValidarExisteOutraSolicitacao() {
        return validarExisteOutraSolicitacao;
    }

    public void setValidarExisteOutraSolicitacao(boolean validarExisteOutraSolicitacao) {
        this.validarExisteOutraSolicitacao = validarExisteOutraSolicitacao;
    }

    public boolean isSolicitacaoQuebrada() {
        return solicitacaoQuebrada;
    }

    public void setSolicitacaoQuebrada(boolean solicitacaoQuebrada) {
        this.solicitacaoQuebrada = solicitacaoQuebrada;
    }

    public String getDataHoraContatoDevolucao() {
        return Data.formatarDataHora(getDataContatoDevolucao());
    }

    public String getSolicitarPrioridadeFormatadoData() {
        String descricaoPrioridade = getDescricaoPrioridadeClassificacao(getPrioridade());

        if (descricaoPrioridade != null) {
            return descricaoPrioridade;
        }

        if (getSolicitarPrioridade() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getSolicitarPrioridade()) && RepositoryComponentDefault.NAO_LONG.equals(getFlagAvaliacaoAprovado()) && RepositoryComponentDefault.NAO_LONG.equals(getFlagBloqueado())) {
                return Bundle.getStringApplication("rotulo_prioridade_solicitada");
            }
        }
        if (getFlagDevolvido() != null && getFlagDevolvido().equals(RepositoryComponentDefault.SIM_LONG)) {
            return Bundle.getStringApplication("rotulo_devolvido");
        } else if (getFlagBloqueado() != null && getFlagBloqueado().equals(RepositoryComponentDefault.SIM_LONG)) {
            return Bundle.getStringApplication("rotulo_bloqueado_dia", Data.formatar(getDiaAteBloqueio()));
        } else if (RepositoryComponentDefault.SIM_LONG.equals(getFlagAvaliacaoAprovado()) && SolicitacaoAgendamento.PRIORIDADE_BREVIDADE.equals(getPrioridade())) {
            return Bundle.getStringApplication("rotulo_atendida");
        } else if (SolicitacaoAgendamento.PRIORIDADE_BREVIDADE.equals(getPrioridade())) {
            return Bundle.getStringApplication("rotulo_brevidade");
        } else if (SolicitacaoAgendamento.PRIORIDADE_ELETIVO.equals(getPrioridade())) {
            return Bundle.getStringApplication("rotulo_eletivo");
        } else {
            return Bundle.getStringApplication("rotulo_urgente");
        }
    }

    public String getDescricaoAcamado() {
        return getDescricaoAcamado(getAcamado());
    }

    public String getTipoFilaFormatado() {
        if (getTipoFila() != null) {
            if (SolicitacaoAgendamento.TIPO_FILA_NORMAL == getTipoFila().longValue())
                return Bundle.getStringApplication("rotulo_normal");
        }
        if (SolicitacaoAgendamento.TIPO_FILA_REGULACAO == getTipoFila().longValue()) {
            return Bundle.getStringApplication("rotulo_regulacao");
        }
        return "";
    }

    public String getDescricaoFlagEnviarRegulacao() {
        if (RepositoryComponentDefault.NAO_LONG.equals(getFlagEnviarRegulacao()))
            return RepositoryComponentDefault.SimNaoLong.NAO.descricao();
        else if (RepositoryComponentDefault.SIM_LONG.equals(getFlagEnviarRegulacao()))
            return RepositoryComponentDefault.SimNaoLong.SIM.descricao();
        return null;
    }

    public String getDescricaoReenviadoRegulacao() {
        ReenviadoRegulacao reenviadoRegulacao = ReenviadoRegulacao.valueOf(getStatus());
        if (reenviadoRegulacao != null && reenviadoRegulacao.descricao != null) {
            return reenviadoRegulacao.descricao();
        }
        return "";
    }

    public boolean isTipoFilaRegulacao() {
        return TIPO_FILA_REGULACAO.equals(this.getTipoFila());
    }

    public boolean isTipoConsultaRetorno() {
        return TIPO_CONSULTA_RETORNO.equals(this.getTipoConsulta());
    }

    public boolean isPermiteEdicao() {
        return Stream.of(STATUS_FILA_ESPERA,
                STATUS_REGULACAO_PENDENTE,
                STATUS_REGULACAO_NEGADO,
                STATUS_REGULACAO_DEVOLVIDO,
                STATUS_DEVOLVIDO,
                STATUS_AGUARDANDO_AUTORIZACAO,
                STATUS_FILA_ESPERA_PRESTADOR,
                STATUS_AGUARDANDO_ANALISE).anyMatch(status -> status.equals(getStatus()));
    }

    public boolean isPermiteAgendamento() {
        return Stream.of(STATUS_FILA_ESPERA,
                STATUS_FILA_ESPERA_PRESTADOR).anyMatch(status -> status.equals(getStatus()));

    }

    public boolean isStatusRegulacaoDevolvido() {
        return STATUS_REGULACAO_DEVOLVIDO.equals(getStatus());
    }

    public boolean isStatusDevolvido() {
        return STATUS_DEVOLVIDO.equals(getStatus());
    }

    public boolean isStatusRegulacaoPendente() {
        return STATUS_REGULACAO_PENDENTE.equals(getStatus());
    }

    public List<Long> getCodigosUnidadeExecutanteSolicitante() {
        List<Long> codigos = new ArrayList<>();
        if (this.getUnidadeOrigem() != null) {
            codigos.add(this.getUnidadeOrigem().getCodigo());
        }
        if (this.getEmpresa() != null) {
            codigos.add(this.getEmpresa().getCodigo());
        }
        return codigos;
    }

    public String getDescricaoJustificativaPriorizacao() {
        if (this.getJustificativaPriorizacao() != null) {
            return this.getJustificativaPriorizacao().getJustificativa();
        }
        return "";
    }

    public enum SituacaoContato implements IEnum {

        NAO_CONTACTADO(0L, null),
        CONTACTADO(1L, null),
        CONTATO_NAO_NECESSARIO(2L, null),
        CONTACTADO_VIA_SMS(3L, null),
        ;


        private final Long value;
        private final String descricao;

        SituacaoContato(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static SituacaoContato valeuOf(Long value) {
            for (SituacaoContato situacaoContato : SituacaoContato.values()) {
                if (situacaoContato.value().equals(value)) {
                    return situacaoContato;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    public enum ReenviadoRegulacao implements IEnum {
        DEVOLUCAO(0L, Bundle.getStringApplication("rotulo_devolucao")),
        JA_REGULADA(1L, Bundle.getStringApplication("rotulo_ja_regulada"));

        private final Long value;
        private final String descricao;

        ReenviadoRegulacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static ReenviadoRegulacao valueOf(Long value) {
            for (ReenviadoRegulacao reenviadoRegulacao : ReenviadoRegulacao.values()) {
                if (reenviadoRegulacao.value().equals(value)) {
                    return reenviadoRegulacao;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

    public enum TipoFinanciamento implements IEnum {

        PPI(0L, Bundle.getStringApplication("rotulo_ppi")),
        PROJETO(1L, Bundle.getStringApplication("rotulo_projeto")),
        TSC(2L, Bundle.getStringApplication("rotulo_tsc")),
        ;

        private Long value;
        private String descricao;

        TipoFinanciamento(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static TipoFinanciamento valueOf(Long value) {
            for (TipoFinanciamento tipoFinanciamento : TipoFinanciamento.values()) {
                if (tipoFinanciamento.value().equals(value)) {
                    return tipoFinanciamento;
                }
            }
            return null;
        }
    }

}
