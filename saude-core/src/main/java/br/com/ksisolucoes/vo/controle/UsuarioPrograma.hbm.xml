<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.controle"  >
	<class
		name="UsuarioPrograma"
		table="usuario_programa"
	>
		<composite-id name="id" class="UsuarioProgramaPK">
		
			<key-many-to-one 
				name="usuario"
				class="br.com.ksisolucoes.vo.controle.Usuario"
				column="cd_usuario"
			/>
			
			<key-many-to-one 
				name="programa"
				class="br.com.ksisolucoes.vo.controle.Programa"
				column="cd_programa"
			/>
		
		</composite-id> <version column="version" name="version" type="long" />
		
		<property 
			name="icone"
			column="icone"
			type="java.lang.String"
			length="50"
		/>
		
		<property 
			name="nome"
			column="nome"
			type="java.lang.String"
			length="50"
		/>
		
		<property 
			name="indice"
			column="indice"
			type="java.lang.Long"
		/>

	</class>
</hibernate-mapping>