<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.consorcio"  >
    <class name="ConsorcioProvisaoOrcamentaria"  table="consorcio_provisao_orcamentaria" >
        
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_provisao"
        >
            <generator class="assigned"/>
        </id> 
        
        <version column="version" name="version" type="long" />

        <many-to-one  
            class="br.com.ksisolucoes.vo.controle.Usuario"
            name="usuario"
            column="cd_usuario"
            not-null="true"
         		/>
        
        <many-to-one  
            class="br.com.ksisolucoes.vo.basico.Empresa"
            name="empresaConsorcio"
            column="empresa_consorcio"
            not-null="true"
         		/>
        
        <many-to-one  
            class="br.com.ksisolucoes.vo.consorcio.SubConta"
            name="subConta"
            column="cd_subconta"
            not-null="false"
         		/>
        
        <property
            name="anoBase"
            column="ano_base"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property
            name="valor"
            column="valor"
            type="java.lang.Double"
            not-null="true"
        />
        
        <property
            name="valorExtra"
            column="valor_extra"
            type="java.lang.Double"
            not-null="true"
        />
        
        <property
            name="dataUsuario"
            column="dt_usuario"
            type="java.util.Date"
            not-null="true"
        />
        
    </class>
</hibernate-mapping>