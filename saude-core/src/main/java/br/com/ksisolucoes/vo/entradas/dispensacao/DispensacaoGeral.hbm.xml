<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.entradas.dispensacao"  >
    <class name="DispensacaoGeral" table="dispensacao_geral">
        
        <id
            name="codigo"
            type="java.lang.Long" 
            column="codigo"
        > 
            <generator class="assigned"/>
        </id> 
        
        <version column="version" name="version" type="long" />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem"
            name="dispensacaoMedicamentoItem"
            column="cd_dis_med_item"
            not-null="false"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.basico.ProdutoSolicitadoMovimento"
            name="produtoSolicitadoMovimento"
            column="cd_prod_sol_mov"
            not-null="false"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
            column="cod_pro"
            name="produto"
            not-null="true"
        />
        
        <property
            column="data"
            name="data"
            not-null="true"
            type="java.util.Date"
        />
        
        <property
            column="quantidade"
            name="quantidade"
            not-null="true"
            type="java.lang.Double"
        />
        
        <property
            column="tp_dispensacao"
            name="tipoDispensacao"
            not-null="true"
            type="java.lang.Long"
        />
        
        <property
            column="dt_prox_dispensacao"
            name="dataProximaDispensacao"
            not-null="false"
            type="java.util.Date"
        />
        
        <property
            column="vl_total"
            name="valorTotal"
            not-null="false"
            type="java.lang.Double"
        />
        
    </class>
</hibernate-mapping>
