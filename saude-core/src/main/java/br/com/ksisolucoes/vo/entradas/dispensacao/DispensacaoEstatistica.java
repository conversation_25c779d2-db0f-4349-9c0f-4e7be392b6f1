package br.com.ksisolucoes.vo.entradas.dispensacao;

import java.io.Serializable;

import br.com.ksisolucoes.vo.entradas.dispensacao.base.BaseDispensacaoEstatistica;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class DispensacaoEstatistica extends BaseDispensacaoEstatistica implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public DispensacaoEstatistica () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public DispensacaoEstatistica (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}