package br.com.ksisolucoes.vo.consorcio;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.EnumUtil;
import br.com.ksisolucoes.vo.consorcio.base.BaseFundoConsorcio;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;


public class FundoConsorcio extends BaseFundoConsorcio implements CodigoManager {
    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public FundoConsorcio() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public FundoConsorcio(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public FundoConsorcio(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.controle.Usuario usuarioAlteracao,
            br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
            java.lang.String descricao,
            java.lang.Long situacao,
            java.util.Date dataCadastro,
            java.util.Date dataAlteracao) {

        super(
                codigo,
                usuarioAlteracao,
                usuarioCadastro,
                descricao,
                situacao,
                dataCadastro,
                dataAlteracao);
    }

    /*[CONSTRUCTOR MARKER END]*/

    public enum Situacao implements IEnum {
        ATIVO(1L, Bundle.getStringApplication("rotulo_ativo")),
        INATIVO(0L, Bundle.getStringApplication("rotulo_inativo"));

        private Long value;
        private String descricao;

        private Situacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Situacao valeuOf(Long value) {
            for (Situacao status : Situacao.values()) {
                if (status.value().equals(value)) {
                    return status;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        @Override
        public String toString() {
            return this.descricao;
        }

    }

    public String getSituacaoFormatada() {
        return new EnumUtil().resolveDescricao(FundoConsorcio.Situacao.values(), getSituacao());
    }

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}