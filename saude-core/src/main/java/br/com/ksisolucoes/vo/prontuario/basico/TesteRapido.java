package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEsus;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseTesteRapido;

import java.io.Serializable;



public class TesteRapido extends BaseTesteRapido implements CodigoManager {
	private static final long serialVersionUID = 1L;
        
        public static final String PROP_DESCRICAO_TIPO_TESTE = "descricaoTipoTeste";
        public static final String PROP_DESCRICAO_ORIENTACAO_SEXUAL = "descricaoOrientacaoSexual";
        public static final String PROP_DESCRICAO_TIPO_CONTATO = "descricaoTipoContato";
        public static final String PROP_DESCRICAO_SABENDO_SERVICO = "descricaoSabendoServico";
        public static final String PROP_DESCRICAO_TIPO_PARCEIRO = "descricaoTipoParceiro";
        public static final String PROP_DESCRICAO_NUMERO_PARCEIRO = "descricaoNumeroParceiro";
        public static final String PROP_DESCRICAO_CAMISINHA_PARCEIRO_FIXO = "descricaoCamisinhaParceiroFixo";
        public static final String PROP_DESCRICAO_CAMISINHA_PARCEIRO_EVENTUAL = "descricaoCamisinhaParceiroEventual";
        
        public static final Long TIPO_TESTE_OUTROS = 0L;
        public static final Long TIPO_TESTE_GRAVIDEZ = 1L;
        public static final Long TIPO_TESTE_COVID_19 = 2L;
        public static final Long TIPO_TESTE_TUBERCULOSE = 4L;
        public static final Long TIPO_TESTE_DENGUE = 5L;
        public static final Long TIPO_TESTE_INFLUENZA = 6L;
        public static final Long TIPO_TESTE_HANSENIASE = 7L;
        public static final Long TIPO_TESTE_HIV_SIFILIS = 8L;
        public static final Long TIPO_TESTE_AIDS_AVANCADO = 9L;
        public static final Long TIPO_TESTE_COVID_MAIS_INFLUENZA_AB = 10L;

        public enum OrientacaoSexual implements IEnum {
            HETEROSEXUAL(1L, UsuarioCadsusEsus.OrientacaoSexual.HETEROSEXUAL.value(), Bundle.getStringApplication("rotulo_heterossexual")),
            HOMOSSEXUAL(2L, UsuarioCadsusEsus.OrientacaoSexual.OUTRO.value(), Bundle.getStringApplication("rotulo_homossexual")),
            BISSEXUAL(4L, UsuarioCadsusEsus.OrientacaoSexual.BISSEXUAL.value(), Bundle.getStringApplication("rotulo_bissexual")),
            TRAVESTI(5L, UsuarioCadsusEsus.IdentidadeGenero.TRAVESTI.value(), Bundle.getStringApplication("rotulo_travesti")),
            TRANSSEXUAL(6L, null, Bundle.getStringApplication("rotulo_transsexual")),
            OUTRO(7L, UsuarioCadsusEsus.OrientacaoSexual.OUTRO.value(), Bundle.getStringApplication("rotulo_outro"))
            ;

            private final Long value;
            private final Long valueEsus;
            private final String descricao;

            OrientacaoSexual(Long value, Long valueEsus, String descricao) {
                this.value = value;
                this.valueEsus = valueEsus;
                this.descricao = descricao;
            }

            public static OrientacaoSexual valeuOf(Long value) {
                for (OrientacaoSexual orientacaoSexual : OrientacaoSexual.values()) {
                    if (orientacaoSexual.value().equals(value)) {
                        return orientacaoSexual;
                    }
                }
                return null;
            }

            @Override
            public Long value() {
                return value;
            }

            public Long valueEsus() {
                return valueEsus;
            }

            @Override
            public String descricao() {
                return descricao;
            }

        }

        public enum TipoContato implements IEnum {
            TELEFONE(1L, Bundle.getStringApplication("rotulo_telefone")),
            CORREIO(2L, Bundle.getStringApplication("rotulo_correio")),
            EMAIL(3L, Bundle.getStringApplication("rotulo_email")),
            VISITA(4L, Bundle.getStringApplication("rotulo_visita")),
            OUTRO(5L, Bundle.getStringApplication("rotulo_outro"))
            ;

            private final Long value;
            private final String descricao;

            TipoContato(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            public static TipoContato valeuOf(Long value) {
                for (TipoContato tipoContato : TipoContato.values()) {
                    if (tipoContato.value().equals(value)) {
                        return tipoContato;
                    }
                }
                return null;
            }

            @Override
            public Long value() {
                return value;
            }

            @Override
            public String descricao() {
                return descricao;
            }

        }
        
        public enum SabendoServico implements IEnum {
            MATERIAL_DIVULGACAO(1L, Bundle.getStringApplication("rotulo_material_divulgacao")),
            BANCO_SANGUE(2L, Bundle.getStringApplication("rotulo_banco_sangue")),
            SERVICO_PROFISSIONAL_SAUDE(3L, Bundle.getStringApplication("rotulo_servico_profissional_saude")),
            AMIGOS_USUARIOS(4L, Bundle.getStringApplication("rotulo_amigos_usuarios")),
            JORNAIS_RADIO_TV(5L, Bundle.getStringApplication("rotulo_jornais_radio_tv")),
            SERVICO_INFORMACAO_TELEFONICA(6L, Bundle.getStringApplication("rotulo_servico_informacao_telefonica")),
            ESCOLA(7L, Bundle.getStringApplication("rotulo_escola")),
            OUTRO(8L, Bundle.getStringApplication("rotulo_outro")),
            ;

            private final Long value;
            private final String descricao;

            SabendoServico(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            public static SabendoServico valeuOf(Long value) {
                for (SabendoServico sabendoServico : SabendoServico.values()) {
                    if (sabendoServico.value().equals(value)) {
                        return sabendoServico;
                    }
                }
                return null;
            }

            @Override
            public Long value() {
                return value;
            }

            @Override
            public String descricao() {
                return descricao;
            }

        }
        
        public enum TipoParceiro implements IEnum {
            NAO_TEVE_RELACOES_SEXUAIS(1L, Bundle.getStringApplication("rotulo_nao_teve_relacoes_sexuais")),
            SO_HOMENS(2L, Bundle.getStringApplication("rotulo_so_homens")),
            SO_MULHERES(3L, Bundle.getStringApplication("rotulo_so_mulheres")),
            HOMENS_MULHERES(4L, Bundle.getStringApplication("rotulo_homens_mulheres")),
            TRAVESTIS_TRANSEXUAIS(5L, Bundle.getStringApplication("rotulo_travestis_transexuais")),
            NAO_INFORMADO(6L, Bundle.getStringApplication("rotulo_nao_informado"))
            ;

            private final Long value;
            private final String descricao;

            TipoParceiro(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            public static TipoParceiro valeuOf(Long value) {
                for (TipoParceiro tipoParceiro : TipoParceiro.values()) {
                    if (tipoParceiro.value().equals(value)) {
                        return tipoParceiro;
                    }
                }
                return null;
            }

            @Override
            public Long value() {
                return value;
            }

            @Override
            public String descricao() {
                return descricao;
            }

        }
        
        public enum NumeroParceiro implements IEnum {
            NENHUM(1L, Bundle.getStringApplication("rotulo_nenhum")),
            UM(2L, Bundle.getStringApplication("rotulo_um")),
            DOIS(3L, Bundle.getStringApplication("rotulo_dois")),
            TRES_A_CINCO(4L, Bundle.getStringApplication("rotulo_tres_a_cinco")),
            SEIS_A_DEZ(5L, Bundle.getStringApplication("rotulo_seis_a_dez")),
            ONZE_A_VINTE(6L, Bundle.getStringApplication("rotulo_onze_a_vinte")),
            VINTE_UM_A_CINQUENTA(7L, Bundle.getStringApplication("rotulo_vinte_um_a_cinquenta")),
            CINQUENTA_UM_A_CEM(8L, Bundle.getStringApplication("rotulo_cinquenta_um_a_cem")),
            MAIS_DE_CEM(9L, Bundle.getStringApplication("rotulo_mais_de_cem")),
            NAO_INFORMADO(10L, Bundle.getStringApplication("rotulo_nao_informado"))
            ;

            private final Long value;
            private final String descricao;

            NumeroParceiro(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            public static NumeroParceiro valeuOf(Long value) {
                for (NumeroParceiro numeroParceiro : NumeroParceiro.values()) {
                    if (numeroParceiro.value().equals(value)) {
                        return numeroParceiro;
                    }
                }
                return null;
            }

            @Override
            public Long value() {
                return value;
            }

            @Override
            public String descricao() {
                return descricao;
            }

        }
        
        public enum CamisinhaParceiroFixo implements IEnum {
            NUNCA(1L, Bundle.getStringApplication("rotulo_nunca")),
            SEMPRE(2L, Bundle.getStringApplication("rotulo_sempre")),
            AS_VEZES(3L, Bundle.getStringApplication("rotulo_as_vezes")),
            NAO_TEM_PARCEIRO_FIXO(4L, Bundle.getStringApplication("rotulo_nao_tem_parceiro_fixo"))
            ;

            private final Long value;
            private final String descricao;

            CamisinhaParceiroFixo(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            public static CamisinhaParceiroFixo valeuOf(Long value) {
                for (CamisinhaParceiroFixo camisinhaParceiroFixo : CamisinhaParceiroFixo.values()) {
                    if (camisinhaParceiroFixo.value().equals(value)) {
                        return camisinhaParceiroFixo;
                    }
                }
                return null;
            }

            @Override
            public Long value() {
                return value;
            }

            @Override
            public String descricao() {
                return descricao;
            }

        }
        
        public enum CamisinhaParceiroEventual implements IEnum {
            NUNCA(1L, Bundle.getStringApplication("rotulo_nunca")),
            SEMPRE(2L, Bundle.getStringApplication("rotulo_sempre")),
            AS_VEZES(3L, Bundle.getStringApplication("rotulo_as_vezes")),
            NAO_TEM_PARCEIRO_EVENTUAL(4L, Bundle.getStringApplication("rotulo_nao_tem_parceiro_eventual"))
            ;

            private final Long value;
            private final String descricao;

            CamisinhaParceiroEventual(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            public static CamisinhaParceiroEventual valeuOf(Long value) {
                for (CamisinhaParceiroEventual camisinhaParceiroEventual : CamisinhaParceiroEventual.values()) {
                    if (camisinhaParceiroEventual.value().equals(value)) {
                        return camisinhaParceiroEventual;
                    }
                }
                return null;
            }

            @Override
            public Long value() {
                return value;
            }

            @Override
            public String descricao() {
                return descricao;
            }

        }
        
/*[CONSTRUCTOR MARKER BEGIN]*/
	public TesteRapido () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public TesteRapido (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public TesteRapido (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento,
		java.lang.Long permiteContato,
		java.lang.Long primeiroTesteHiv,
		java.lang.Long comoFicouSabendoServico,
		java.lang.Long tipoParceiro,
		java.lang.Long numeroParceiro,
		java.lang.Long teveDst,
		java.lang.Long usoCamisinhaParceiroFixo,
		java.lang.Long usoCamisinhaParceiroEventual) {

		super (
			codigo,
			atendimento,
			permiteContato,
			primeiroTesteHiv,
			comoFicouSabendoServico,
			tipoParceiro,
			numeroParceiro,
			teveDst,
			usoCamisinhaParceiroFixo,
			usoCamisinhaParceiroEventual);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDescricaoOrientacaoSexual(){
        OrientacaoSexual orientacaoSexual = OrientacaoSexual.valeuOf(getOrientacaoSexual());
        if (orientacaoSexual != null && orientacaoSexual.descricao != null) {
            return orientacaoSexual.descricao();
        }
        return "";
    }
    
    public String getDescricaoTipoContato(){
        TipoContato tipoContato = TipoContato.valeuOf(getTipoContato());
        if (tipoContato != null && tipoContato.descricao != null) {
            return tipoContato.descricao();
        }
        return "";
    }
    
    public String getDescricaoSabendoServico(){
        SabendoServico sabendoServico = SabendoServico.valeuOf(getComoFicouSabendoServico());
        if (sabendoServico != null && sabendoServico.descricao != null) {
            return sabendoServico.descricao();
        }
        return "";
    }
    
    public String getDescricaoTipoParceiro(){
        TipoParceiro tipoParceiro = TipoParceiro.valeuOf(getTipoParceiro());
        if (tipoParceiro != null && tipoParceiro.descricao != null) {
            return tipoParceiro.descricao();
        }
        return "";
    }
    
    public String getDescricaoNumeroParceiro(){
        NumeroParceiro numeroParceiro = NumeroParceiro.valeuOf(getNumeroParceiro());
        if (numeroParceiro != null && numeroParceiro.descricao != null) {
            return numeroParceiro.descricao();
        }
        return "";
    }
    
    public String getDescricaoCamisinhaParceiroFixo(){
        CamisinhaParceiroFixo camisinhaParceiroFixo = CamisinhaParceiroFixo.valeuOf(getUsoCamisinhaParceiroFixo());
        if (camisinhaParceiroFixo != null && camisinhaParceiroFixo.descricao != null) {
            return camisinhaParceiroFixo.descricao();
        }
        return "";
    }
    
    public String getDescricaoCamisinhaParceiroEventual(){
        CamisinhaParceiroEventual camisinhaParceiroEventual = CamisinhaParceiroEventual.valeuOf(getUsoCamisinhaParceiroEventual());
        if (camisinhaParceiroEventual != null && camisinhaParceiroEventual.descricao != null) {
            return camisinhaParceiroEventual.descricao();
        }
        return "";
    }
    
}