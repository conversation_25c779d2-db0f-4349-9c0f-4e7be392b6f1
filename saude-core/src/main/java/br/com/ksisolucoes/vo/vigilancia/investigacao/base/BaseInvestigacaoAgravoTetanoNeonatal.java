package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the investigacao_agr_tetano_neonatal table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_tetano_neonatal"
 */

public abstract class BaseInvestigacaoAgravoTetanoNeonatal extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoTetanoNeonatal";
	public static final String PROP_LOCAL_RESIDENCIA = "localResidencia";
	public static final String PROP_LOCAL_PROVAVEL_INFECCAO = "localProvavelInfeccao";
	public static final String PROP_NUM_CONSULTAS_PRE_NATAL = "numConsultasPreNatal";
	public static final String PROP_SINAIS_SINTOMAS_RIGIDEZ_NUCA = "sinaisSintomasRigidezNuca";
	public static final String PROP_ESCOLARIDADE_MAE = "escolaridadeMae";
	public static final String PROP_BAIRRO_LOCAL_INFECCAO = "bairroLocalInfeccao";
	public static final String PROP_SINAIS_SINTOMAS_PROCESSO_INFLAMATORIO = "sinaisSintomasProcessoInflamatorio";
	public static final String PROP_SINAIS_SINTOMAS_OUTROS = "sinaisSintomasOutros";
	public static final String PROP_DATA_ENCERRAMENTO = "dataEncerramento";
	public static final String PROP_IDADE_MAE = "idadeMae";
	public static final String PROP_CASO_AUTOCTONE = "casoAutoctone";
	public static final String PROP_DATA_ULTIMO_REFORCO = "dataUltimoReforco";
	public static final String PROP_DATA_OBITO = "dataObito";
	public static final String PROP_MEDIDAS_CONTROLE_ORIENTACAO_PARTURIENTES = "medidasControleOrientacaoParturientes";
	public static final String PROP_HOSPITAL = "hospital";
	public static final String PROP_CIDADE_LOCAL_INFECCAO = "cidadeLocalInfeccao";
	public static final String PROP_OUTRO_LOCAL_PARTO = "outroLocalParto";
	public static final String PROP_DATA3_DOSE = "data3Dose";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_PARTO_ATENDIDO_OUTRO = "partoAtendidoOutro";
	public static final String PROP_SINAIS_SINTOMAS_RIGIDEZ_ABDOMINAL = "sinaisSintomasRigidezAbdominal";
	public static final String PROP_SINAIS_SINTOMAS_OPISTOTONO = "sinaisSintomasOpistotono";
	public static final String PROP_CLASSIFICACAO_FINAL = "classificacaoFinal";
	public static final String PROP_SINAIS_SINTOMAS_CHORO_EXCESSIVO = "sinaisSintomasChoroExcessivo";
	public static final String PROP_EVOLUCAO_CASO = "evolucaoCaso";
	public static final String PROP_DATA_TRISMO = "dataTrismo";
	public static final String PROP_ORIGEM_CASO = "origemCaso";
	public static final String PROP_LOCAL_INFECCAO_UNIDADE = "localInfeccaoUnidade";
	public static final String PROP_MEDIDAS_CONTROLE_BUSCA_ATIVA = "medidasControleBuscaAtiva";
	public static final String PROP_SINAIS_SINTOMAS_RIGIDEZ_MEMBROS = "sinaisSintomasRigidezMembros";
	public static final String PROP_DATA_INVESTIGACAO = "dataInvestigacao";
	public static final String PROP_MEDIDAS_CONTROLE_OUTROS = "medidasControleOutros";
	public static final String PROP_MEDIDAS_CONTROLE_CADASTRO_PARTEIRAS = "medidasControleCadastroParteiras";
	public static final String PROP_MEDIDAS_CONTROLE_DIVULGACAO_PROBLEMA = "medidasControleDivulgacaoProblema";
	public static final String PROP_USUARIO_ENCERRAMENTO = "usuarioEncerramento";
	public static final String PROP_FLAG_INFORMACOES_COMPLEMENTARES = "flagInformacoesComplementares";
	public static final String PROP_DATA1_DOSE = "data1Dose";
	public static final String PROP_SINAIS_SINTOMAS_CRISES_CONTRATURAS = "sinaisSintomasCrisesContraturas";
	public static final String PROP_DISTRITO_LOCAL_INFECCAO = "distritoLocalInfeccao";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_ANTECEDENTES_VACINAIS = "antecedentesVacinais";
	public static final String PROP_SINAIS_SINTOMAS_CONTRATURA_LABIAL = "sinaisSintomasContraturaLabial";
	public static final String PROP_DATA2_DOSE = "data2Dose";
	public static final String PROP_SINAIS_SINTOMAS_DIFICULDADE_MAMAR = "sinaisSintomasDificuldadeMamar";
	public static final String PROP_OUTRO_LOCAL_RESIDENCIA = "outroLocalResidencia";
	public static final String PROP_SUGOU_APOS_NASCIMENTO = "sugouAposNascimento";
	public static final String PROP_LOCAL_PROVAVEL_INFECCAO_OUTRO = "localProvavelInfeccaoOutro";
	public static final String PROP_HOSPITALIZACAO = "hospitalizacao";
	public static final String PROP_REGISTRO_AGRAVO = "registroAgravo";
	public static final String PROP_PARTO_ATENDIDO = "partoAtendido";
	public static final String PROP_OCUPACAO_CBO = "ocupacaoCbo";
	public static final String PROP_NUM_GESTACAO = "numGestacao";
	public static final String PROP_MEDIDAS_CONTROLE_ESQUEMA_VACINAL_MAE = "medidasControleEsquemaVacinalMae";
	public static final String PROP_PAIS_LOCAL_INFECCAO = "paisLocalInfeccao";
	public static final String PROP_SINAIS_SINTOMAS_TRISMO = "sinaisSintomasTrismo";
	public static final String PROP_LOCAL_PARTO = "localParto";
	public static final String PROP_DATA_INTERNACAO = "dataInternacao";
	public static final String PROP_MEDIDAS_CONTROLE_ANALISE_CV = "medidasControleAnaliseCv";


	// constructors
	public BaseInvestigacaoAgravoTetanoNeonatal () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoTetanoNeonatal (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoAgravoTetanoNeonatal (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		java.lang.String flagInformacoesComplementares) {

		this.setCodigo(codigo);
		this.setRegistroAgravo(registroAgravo);
		this.setFlagInformacoesComplementares(flagInformacoesComplementares);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String flagInformacoesComplementares;
	private java.util.Date dataInvestigacao;
	private java.lang.Long numConsultasPreNatal;
	private java.lang.Long antecedentesVacinais;
	private java.util.Date data1Dose;
	private java.util.Date data2Dose;
	private java.util.Date data3Dose;
	private java.util.Date dataUltimoReforco;
	private java.lang.String idadeMae;
	private java.lang.Long numGestacao;
	private java.lang.Long escolaridadeMae;
	private java.lang.Long localParto;
	private java.lang.String outroLocalParto;
	private java.lang.Long partoAtendido;
	private java.lang.String partoAtendidoOutro;
	private java.lang.Long sugouAposNascimento;
	private java.lang.Long sinaisSintomasDificuldadeMamar;
	private java.lang.Long sinaisSintomasChoroExcessivo;
	private java.lang.Long sinaisSintomasProcessoInflamatorio;
	private java.lang.Long sinaisSintomasCrisesContraturas;
	private java.lang.Long sinaisSintomasTrismo;
	private java.lang.Long sinaisSintomasContraturaLabial;
	private java.lang.Long sinaisSintomasOpistotono;
	private java.lang.Long sinaisSintomasRigidezNuca;
	private java.lang.Long sinaisSintomasRigidezAbdominal;
	private java.lang.Long sinaisSintomasRigidezMembros;
	private java.lang.String sinaisSintomasOutros;
	private java.util.Date dataTrismo;
	private java.lang.Long origemCaso;
	private java.lang.Long localResidencia;
	private java.lang.String outroLocalResidencia;
	private java.lang.Long hospitalizacao;
	private java.util.Date dataInternacao;
	private java.lang.Long medidasControleEsquemaVacinalMae;
	private java.lang.Long medidasControleBuscaAtiva;
	private java.lang.Long medidasControleAnaliseCv;
	private java.lang.Long medidasControleCadastroParteiras;
	private java.lang.Long medidasControleOrientacaoParturientes;
	private java.lang.Long medidasControleDivulgacaoProblema;
	private java.lang.String medidasControleOutros;
	private java.lang.Long localProvavelInfeccao;
	private java.lang.String localProvavelInfeccaoOutro;
	private java.lang.Long casoAutoctone;
	private java.lang.String distritoLocalInfeccao;
	private java.lang.String bairroLocalInfeccao;
	private java.lang.Long classificacaoFinal;
	private java.lang.Long evolucaoCaso;
	private java.util.Date dataObito;
	private java.lang.String observacao;
	private java.util.Date dataEncerramento;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo;
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo;
	private br.com.ksisolucoes.vo.basico.Empresa hospital;
	private br.com.ksisolucoes.vo.basico.Empresa localInfeccaoUnidade;
	private br.com.ksisolucoes.vo.basico.Cidade cidadeLocalInfeccao;
	private br.com.ksisolucoes.vo.basico.Pais paisLocalInfeccao;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_invest_agr_tetano_neonatal"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: flag_informacoes_complementares
	 */
	public java.lang.String getFlagInformacoesComplementares () {
		return getPropertyValue(this, flagInformacoesComplementares, PROP_FLAG_INFORMACOES_COMPLEMENTARES); 
	}

	/**
	 * Set the value related to the column: flag_informacoes_complementares
	 * @param flagInformacoesComplementares the flag_informacoes_complementares value
	 */
	public void setFlagInformacoesComplementares (java.lang.String flagInformacoesComplementares) {
//        java.lang.String flagInformacoesComplementaresOld = this.flagInformacoesComplementares;
		this.flagInformacoesComplementares = flagInformacoesComplementares;
//        this.getPropertyChangeSupport().firePropertyChange ("flagInformacoesComplementares", flagInformacoesComplementaresOld, flagInformacoesComplementares);
	}



	/**
	 * Return the value associated with the column: dt_investigacao
	 */
	public java.util.Date getDataInvestigacao () {
		return getPropertyValue(this, dataInvestigacao, PROP_DATA_INVESTIGACAO); 
	}

	/**
	 * Set the value related to the column: dt_investigacao
	 * @param dataInvestigacao the dt_investigacao value
	 */
	public void setDataInvestigacao (java.util.Date dataInvestigacao) {
//        java.util.Date dataInvestigacaoOld = this.dataInvestigacao;
		this.dataInvestigacao = dataInvestigacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInvestigacao", dataInvestigacaoOld, dataInvestigacao);
	}



	/**
	 * Return the value associated with the column: num_consultas_pre_natal
	 */
	public java.lang.Long getNumConsultasPreNatal () {
		return getPropertyValue(this, numConsultasPreNatal, PROP_NUM_CONSULTAS_PRE_NATAL); 
	}

	/**
	 * Set the value related to the column: num_consultas_pre_natal
	 * @param numConsultasPreNatal the num_consultas_pre_natal value
	 */
	public void setNumConsultasPreNatal (java.lang.Long numConsultasPreNatal) {
//        java.lang.Long numConsultasPreNatalOld = this.numConsultasPreNatal;
		this.numConsultasPreNatal = numConsultasPreNatal;
//        this.getPropertyChangeSupport().firePropertyChange ("numConsultasPreNatal", numConsultasPreNatalOld, numConsultasPreNatal);
	}



	/**
	 * Return the value associated with the column: antecedentes_vacinais
	 */
	public java.lang.Long getAntecedentesVacinais () {
		return getPropertyValue(this, antecedentesVacinais, PROP_ANTECEDENTES_VACINAIS); 
	}

	/**
	 * Set the value related to the column: antecedentes_vacinais
	 * @param antecedentesVacinais the antecedentes_vacinais value
	 */
	public void setAntecedentesVacinais (java.lang.Long antecedentesVacinais) {
//        java.lang.Long antecedentesVacinaisOld = this.antecedentesVacinais;
		this.antecedentesVacinais = antecedentesVacinais;
//        this.getPropertyChangeSupport().firePropertyChange ("antecedentesVacinais", antecedentesVacinaisOld, antecedentesVacinais);
	}



	/**
	 * Return the value associated with the column: dt_1_dose
	 */
	public java.util.Date getData1Dose () {
		return getPropertyValue(this, data1Dose, PROP_DATA1_DOSE); 
	}

	/**
	 * Set the value related to the column: dt_1_dose
	 * @param data1Dose the dt_1_dose value
	 */
	public void setData1Dose (java.util.Date data1Dose) {
//        java.util.Date data1DoseOld = this.data1Dose;
		this.data1Dose = data1Dose;
//        this.getPropertyChangeSupport().firePropertyChange ("data1Dose", data1DoseOld, data1Dose);
	}



	/**
	 * Return the value associated with the column: dt_2_dose
	 */
	public java.util.Date getData2Dose () {
		return getPropertyValue(this, data2Dose, PROP_DATA2_DOSE); 
	}

	/**
	 * Set the value related to the column: dt_2_dose
	 * @param data2Dose the dt_2_dose value
	 */
	public void setData2Dose (java.util.Date data2Dose) {
//        java.util.Date data2DoseOld = this.data2Dose;
		this.data2Dose = data2Dose;
//        this.getPropertyChangeSupport().firePropertyChange ("data2Dose", data2DoseOld, data2Dose);
	}



	/**
	 * Return the value associated with the column: dt_3_dose
	 */
	public java.util.Date getData3Dose () {
		return getPropertyValue(this, data3Dose, PROP_DATA3_DOSE); 
	}

	/**
	 * Set the value related to the column: dt_3_dose
	 * @param data3Dose the dt_3_dose value
	 */
	public void setData3Dose (java.util.Date data3Dose) {
//        java.util.Date data3DoseOld = this.data3Dose;
		this.data3Dose = data3Dose;
//        this.getPropertyChangeSupport().firePropertyChange ("data3Dose", data3DoseOld, data3Dose);
	}



	/**
	 * Return the value associated with the column: dt_ultimo_reforco
	 */
	public java.util.Date getDataUltimoReforco () {
		return getPropertyValue(this, dataUltimoReforco, PROP_DATA_ULTIMO_REFORCO); 
	}

	/**
	 * Set the value related to the column: dt_ultimo_reforco
	 * @param dataUltimoReforco the dt_ultimo_reforco value
	 */
	public void setDataUltimoReforco (java.util.Date dataUltimoReforco) {
//        java.util.Date dataUltimoReforcoOld = this.dataUltimoReforco;
		this.dataUltimoReforco = dataUltimoReforco;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUltimoReforco", dataUltimoReforcoOld, dataUltimoReforco);
	}



	/**
	 * Return the value associated with the column: idade_mae
	 */
	public java.lang.String getIdadeMae () {
		return getPropertyValue(this, idadeMae, PROP_IDADE_MAE); 
	}

	/**
	 * Set the value related to the column: idade_mae
	 * @param idadeMae the idade_mae value
	 */
	public void setIdadeMae (java.lang.String idadeMae) {
//        java.lang.String idadeMaeOld = this.idadeMae;
		this.idadeMae = idadeMae;
//        this.getPropertyChangeSupport().firePropertyChange ("idadeMae", idadeMaeOld, idadeMae);
	}



	/**
	 * Return the value associated with the column: num_gestacao
	 */
	public java.lang.Long getNumGestacao () {
		return getPropertyValue(this, numGestacao, PROP_NUM_GESTACAO); 
	}

	/**
	 * Set the value related to the column: num_gestacao
	 * @param numGestacao the num_gestacao value
	 */
	public void setNumGestacao (java.lang.Long numGestacao) {
//        java.lang.Long numGestacaoOld = this.numGestacao;
		this.numGestacao = numGestacao;
//        this.getPropertyChangeSupport().firePropertyChange ("numGestacao", numGestacaoOld, numGestacao);
	}



	/**
	 * Return the value associated with the column: escolaridade_mae
	 */
	public java.lang.Long getEscolaridadeMae () {
		return getPropertyValue(this, escolaridadeMae, PROP_ESCOLARIDADE_MAE); 
	}

	/**
	 * Set the value related to the column: escolaridade_mae
	 * @param escolaridadeMae the escolaridade_mae value
	 */
	public void setEscolaridadeMae (java.lang.Long escolaridadeMae) {
//        java.lang.Long escolaridadeMaeOld = this.escolaridadeMae;
		this.escolaridadeMae = escolaridadeMae;
//        this.getPropertyChangeSupport().firePropertyChange ("escolaridadeMae", escolaridadeMaeOld, escolaridadeMae);
	}



	/**
	 * Return the value associated with the column: local_parto
	 */
	public java.lang.Long getLocalParto () {
		return getPropertyValue(this, localParto, PROP_LOCAL_PARTO); 
	}

	/**
	 * Set the value related to the column: local_parto
	 * @param localParto the local_parto value
	 */
	public void setLocalParto (java.lang.Long localParto) {
//        java.lang.Long localPartoOld = this.localParto;
		this.localParto = localParto;
//        this.getPropertyChangeSupport().firePropertyChange ("localParto", localPartoOld, localParto);
	}



	/**
	 * Return the value associated with the column: outro_local_parto
	 */
	public java.lang.String getOutroLocalParto () {
		return getPropertyValue(this, outroLocalParto, PROP_OUTRO_LOCAL_PARTO); 
	}

	/**
	 * Set the value related to the column: outro_local_parto
	 * @param outroLocalParto the outro_local_parto value
	 */
	public void setOutroLocalParto (java.lang.String outroLocalParto) {
//        java.lang.String outroLocalPartoOld = this.outroLocalParto;
		this.outroLocalParto = outroLocalParto;
//        this.getPropertyChangeSupport().firePropertyChange ("outroLocalParto", outroLocalPartoOld, outroLocalParto);
	}



	/**
	 * Return the value associated with the column: parto_atendido
	 */
	public java.lang.Long getPartoAtendido () {
		return getPropertyValue(this, partoAtendido, PROP_PARTO_ATENDIDO); 
	}

	/**
	 * Set the value related to the column: parto_atendido
	 * @param partoAtendido the parto_atendido value
	 */
	public void setPartoAtendido (java.lang.Long partoAtendido) {
//        java.lang.Long partoAtendidoOld = this.partoAtendido;
		this.partoAtendido = partoAtendido;
//        this.getPropertyChangeSupport().firePropertyChange ("partoAtendido", partoAtendidoOld, partoAtendido);
	}



	/**
	 * Return the value associated with the column: outro_parto_atendido
	 */
	public java.lang.String getPartoAtendidoOutro () {
		return getPropertyValue(this, partoAtendidoOutro, PROP_PARTO_ATENDIDO_OUTRO); 
	}

	/**
	 * Set the value related to the column: outro_parto_atendido
	 * @param partoAtendidoOutro the outro_parto_atendido value
	 */
	public void setPartoAtendidoOutro (java.lang.String partoAtendidoOutro) {
//        java.lang.String partoAtendidoOutroOld = this.partoAtendidoOutro;
		this.partoAtendidoOutro = partoAtendidoOutro;
//        this.getPropertyChangeSupport().firePropertyChange ("partoAtendidoOutro", partoAtendidoOutroOld, partoAtendidoOutro);
	}



	/**
	 * Return the value associated with the column: sugou_apos_nascimento
	 */
	public java.lang.Long getSugouAposNascimento () {
		return getPropertyValue(this, sugouAposNascimento, PROP_SUGOU_APOS_NASCIMENTO); 
	}

	/**
	 * Set the value related to the column: sugou_apos_nascimento
	 * @param sugouAposNascimento the sugou_apos_nascimento value
	 */
	public void setSugouAposNascimento (java.lang.Long sugouAposNascimento) {
//        java.lang.Long sugouAposNascimentoOld = this.sugouAposNascimento;
		this.sugouAposNascimento = sugouAposNascimento;
//        this.getPropertyChangeSupport().firePropertyChange ("sugouAposNascimento", sugouAposNascimentoOld, sugouAposNascimento);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_dificuldade_mamar
	 */
	public java.lang.Long getSinaisSintomasDificuldadeMamar () {
		return getPropertyValue(this, sinaisSintomasDificuldadeMamar, PROP_SINAIS_SINTOMAS_DIFICULDADE_MAMAR); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_dificuldade_mamar
	 * @param sinaisSintomasDificuldadeMamar the sinais_sintomas_dificuldade_mamar value
	 */
	public void setSinaisSintomasDificuldadeMamar (java.lang.Long sinaisSintomasDificuldadeMamar) {
//        java.lang.Long sinaisSintomasDificuldadeMamarOld = this.sinaisSintomasDificuldadeMamar;
		this.sinaisSintomasDificuldadeMamar = sinaisSintomasDificuldadeMamar;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasDificuldadeMamar", sinaisSintomasDificuldadeMamarOld, sinaisSintomasDificuldadeMamar);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_choro_excessivo
	 */
	public java.lang.Long getSinaisSintomasChoroExcessivo () {
		return getPropertyValue(this, sinaisSintomasChoroExcessivo, PROP_SINAIS_SINTOMAS_CHORO_EXCESSIVO); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_choro_excessivo
	 * @param sinaisSintomasChoroExcessivo the sinais_sintomas_choro_excessivo value
	 */
	public void setSinaisSintomasChoroExcessivo (java.lang.Long sinaisSintomasChoroExcessivo) {
//        java.lang.Long sinaisSintomasChoroExcessivoOld = this.sinaisSintomasChoroExcessivo;
		this.sinaisSintomasChoroExcessivo = sinaisSintomasChoroExcessivo;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasChoroExcessivo", sinaisSintomasChoroExcessivoOld, sinaisSintomasChoroExcessivo);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_processo_inflamatorio
	 */
	public java.lang.Long getSinaisSintomasProcessoInflamatorio () {
		return getPropertyValue(this, sinaisSintomasProcessoInflamatorio, PROP_SINAIS_SINTOMAS_PROCESSO_INFLAMATORIO); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_processo_inflamatorio
	 * @param sinaisSintomasProcessoInflamatorio the sinais_sintomas_processo_inflamatorio value
	 */
	public void setSinaisSintomasProcessoInflamatorio (java.lang.Long sinaisSintomasProcessoInflamatorio) {
//        java.lang.Long sinaisSintomasProcessoInflamatorioOld = this.sinaisSintomasProcessoInflamatorio;
		this.sinaisSintomasProcessoInflamatorio = sinaisSintomasProcessoInflamatorio;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasProcessoInflamatorio", sinaisSintomasProcessoInflamatorioOld, sinaisSintomasProcessoInflamatorio);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_crises_contraturas
	 */
	public java.lang.Long getSinaisSintomasCrisesContraturas () {
		return getPropertyValue(this, sinaisSintomasCrisesContraturas, PROP_SINAIS_SINTOMAS_CRISES_CONTRATURAS); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_crises_contraturas
	 * @param sinaisSintomasCrisesContraturas the sinais_sintomas_crises_contraturas value
	 */
	public void setSinaisSintomasCrisesContraturas (java.lang.Long sinaisSintomasCrisesContraturas) {
//        java.lang.Long sinaisSintomasCrisesContraturasOld = this.sinaisSintomasCrisesContraturas;
		this.sinaisSintomasCrisesContraturas = sinaisSintomasCrisesContraturas;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasCrisesContraturas", sinaisSintomasCrisesContraturasOld, sinaisSintomasCrisesContraturas);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_trismo
	 */
	public java.lang.Long getSinaisSintomasTrismo () {
		return getPropertyValue(this, sinaisSintomasTrismo, PROP_SINAIS_SINTOMAS_TRISMO); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_trismo
	 * @param sinaisSintomasTrismo the sinais_sintomas_trismo value
	 */
	public void setSinaisSintomasTrismo (java.lang.Long sinaisSintomasTrismo) {
//        java.lang.Long sinaisSintomasTrismoOld = this.sinaisSintomasTrismo;
		this.sinaisSintomasTrismo = sinaisSintomasTrismo;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasTrismo", sinaisSintomasTrismoOld, sinaisSintomasTrismo);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_contratura_labial
	 */
	public java.lang.Long getSinaisSintomasContraturaLabial () {
		return getPropertyValue(this, sinaisSintomasContraturaLabial, PROP_SINAIS_SINTOMAS_CONTRATURA_LABIAL); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_contratura_labial
	 * @param sinaisSintomasContraturaLabial the sinais_sintomas_contratura_labial value
	 */
	public void setSinaisSintomasContraturaLabial (java.lang.Long sinaisSintomasContraturaLabial) {
//        java.lang.Long sinaisSintomasContraturaLabialOld = this.sinaisSintomasContraturaLabial;
		this.sinaisSintomasContraturaLabial = sinaisSintomasContraturaLabial;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasContraturaLabial", sinaisSintomasContraturaLabialOld, sinaisSintomasContraturaLabial);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_opistotono
	 */
	public java.lang.Long getSinaisSintomasOpistotono () {
		return getPropertyValue(this, sinaisSintomasOpistotono, PROP_SINAIS_SINTOMAS_OPISTOTONO); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_opistotono
	 * @param sinaisSintomasOpistotono the sinais_sintomas_opistotono value
	 */
	public void setSinaisSintomasOpistotono (java.lang.Long sinaisSintomasOpistotono) {
//        java.lang.Long sinaisSintomasOpistotonoOld = this.sinaisSintomasOpistotono;
		this.sinaisSintomasOpistotono = sinaisSintomasOpistotono;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasOpistotono", sinaisSintomasOpistotonoOld, sinaisSintomasOpistotono);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_rigidez_nuca
	 */
	public java.lang.Long getSinaisSintomasRigidezNuca () {
		return getPropertyValue(this, sinaisSintomasRigidezNuca, PROP_SINAIS_SINTOMAS_RIGIDEZ_NUCA); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_rigidez_nuca
	 * @param sinaisSintomasRigidezNuca the sinais_sintomas_rigidez_nuca value
	 */
	public void setSinaisSintomasRigidezNuca (java.lang.Long sinaisSintomasRigidezNuca) {
//        java.lang.Long sinaisSintomasRigidezNucaOld = this.sinaisSintomasRigidezNuca;
		this.sinaisSintomasRigidezNuca = sinaisSintomasRigidezNuca;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasRigidezNuca", sinaisSintomasRigidezNucaOld, sinaisSintomasRigidezNuca);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_rigidez_abdominal
	 */
	public java.lang.Long getSinaisSintomasRigidezAbdominal () {
		return getPropertyValue(this, sinaisSintomasRigidezAbdominal, PROP_SINAIS_SINTOMAS_RIGIDEZ_ABDOMINAL); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_rigidez_abdominal
	 * @param sinaisSintomasRigidezAbdominal the sinais_sintomas_rigidez_abdominal value
	 */
	public void setSinaisSintomasRigidezAbdominal (java.lang.Long sinaisSintomasRigidezAbdominal) {
//        java.lang.Long sinaisSintomasRigidezAbdominalOld = this.sinaisSintomasRigidezAbdominal;
		this.sinaisSintomasRigidezAbdominal = sinaisSintomasRigidezAbdominal;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasRigidezAbdominal", sinaisSintomasRigidezAbdominalOld, sinaisSintomasRigidezAbdominal);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_rigidez_membros
	 */
	public java.lang.Long getSinaisSintomasRigidezMembros () {
		return getPropertyValue(this, sinaisSintomasRigidezMembros, PROP_SINAIS_SINTOMAS_RIGIDEZ_MEMBROS); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_rigidez_membros
	 * @param sinaisSintomasRigidezMembros the sinais_sintomas_rigidez_membros value
	 */
	public void setSinaisSintomasRigidezMembros (java.lang.Long sinaisSintomasRigidezMembros) {
//        java.lang.Long sinaisSintomasRigidezMembrosOld = this.sinaisSintomasRigidezMembros;
		this.sinaisSintomasRigidezMembros = sinaisSintomasRigidezMembros;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasRigidezMembros", sinaisSintomasRigidezMembrosOld, sinaisSintomasRigidezMembros);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_outros
	 */
	public java.lang.String getSinaisSintomasOutros () {
		return getPropertyValue(this, sinaisSintomasOutros, PROP_SINAIS_SINTOMAS_OUTROS); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_outros
	 * @param sinaisSintomasOutros the sinais_sintomas_outros value
	 */
	public void setSinaisSintomasOutros (java.lang.String sinaisSintomasOutros) {
//        java.lang.String sinaisSintomasOutrosOld = this.sinaisSintomasOutros;
		this.sinaisSintomasOutros = sinaisSintomasOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasOutros", sinaisSintomasOutrosOld, sinaisSintomasOutros);
	}



	/**
	 * Return the value associated with the column: dt_trismo
	 */
	public java.util.Date getDataTrismo () {
		return getPropertyValue(this, dataTrismo, PROP_DATA_TRISMO); 
	}

	/**
	 * Set the value related to the column: dt_trismo
	 * @param dataTrismo the dt_trismo value
	 */
	public void setDataTrismo (java.util.Date dataTrismo) {
//        java.util.Date dataTrismoOld = this.dataTrismo;
		this.dataTrismo = dataTrismo;
//        this.getPropertyChangeSupport().firePropertyChange ("dataTrismo", dataTrismoOld, dataTrismo);
	}



	/**
	 * Return the value associated with the column: origem_caso
	 */
	public java.lang.Long getOrigemCaso () {
		return getPropertyValue(this, origemCaso, PROP_ORIGEM_CASO); 
	}

	/**
	 * Set the value related to the column: origem_caso
	 * @param origemCaso the origem_caso value
	 */
	public void setOrigemCaso (java.lang.Long origemCaso) {
//        java.lang.Long origemCasoOld = this.origemCaso;
		this.origemCaso = origemCaso;
//        this.getPropertyChangeSupport().firePropertyChange ("origemCaso", origemCasoOld, origemCaso);
	}



	/**
	 * Return the value associated with the column: local_residencia
	 */
	public java.lang.Long getLocalResidencia () {
		return getPropertyValue(this, localResidencia, PROP_LOCAL_RESIDENCIA); 
	}

	/**
	 * Set the value related to the column: local_residencia
	 * @param localResidencia the local_residencia value
	 */
	public void setLocalResidencia (java.lang.Long localResidencia) {
//        java.lang.Long localResidenciaOld = this.localResidencia;
		this.localResidencia = localResidencia;
//        this.getPropertyChangeSupport().firePropertyChange ("localResidencia", localResidenciaOld, localResidencia);
	}



	/**
	 * Return the value associated with the column: outro_local_residencia
	 */
	public java.lang.String getOutroLocalResidencia () {
		return getPropertyValue(this, outroLocalResidencia, PROP_OUTRO_LOCAL_RESIDENCIA); 
	}

	/**
	 * Set the value related to the column: outro_local_residencia
	 * @param outroLocalResidencia the outro_local_residencia value
	 */
	public void setOutroLocalResidencia (java.lang.String outroLocalResidencia) {
//        java.lang.String outroLocalResidenciaOld = this.outroLocalResidencia;
		this.outroLocalResidencia = outroLocalResidencia;
//        this.getPropertyChangeSupport().firePropertyChange ("outroLocalResidencia", outroLocalResidenciaOld, outroLocalResidencia);
	}



	/**
	 * Return the value associated with the column: hospitalizacao
	 */
	public java.lang.Long getHospitalizacao () {
		return getPropertyValue(this, hospitalizacao, PROP_HOSPITALIZACAO); 
	}

	/**
	 * Set the value related to the column: hospitalizacao
	 * @param hospitalizacao the hospitalizacao value
	 */
	public void setHospitalizacao (java.lang.Long hospitalizacao) {
//        java.lang.Long hospitalizacaoOld = this.hospitalizacao;
		this.hospitalizacao = hospitalizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("hospitalizacao", hospitalizacaoOld, hospitalizacao);
	}



	/**
	 * Return the value associated with the column: dt_internacao
	 */
	public java.util.Date getDataInternacao () {
		return getPropertyValue(this, dataInternacao, PROP_DATA_INTERNACAO); 
	}

	/**
	 * Set the value related to the column: dt_internacao
	 * @param dataInternacao the dt_internacao value
	 */
	public void setDataInternacao (java.util.Date dataInternacao) {
//        java.util.Date dataInternacaoOld = this.dataInternacao;
		this.dataInternacao = dataInternacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInternacao", dataInternacaoOld, dataInternacao);
	}



	/**
	 * Return the value associated with the column: medidas_controle_esquema_vacinal_mae
	 */
	public java.lang.Long getMedidasControleEsquemaVacinalMae () {
		return getPropertyValue(this, medidasControleEsquemaVacinalMae, PROP_MEDIDAS_CONTROLE_ESQUEMA_VACINAL_MAE); 
	}

	/**
	 * Set the value related to the column: medidas_controle_esquema_vacinal_mae
	 * @param medidasControleEsquemaVacinalMae the medidas_controle_esquema_vacinal_mae value
	 */
	public void setMedidasControleEsquemaVacinalMae (java.lang.Long medidasControleEsquemaVacinalMae) {
//        java.lang.Long medidasControleEsquemaVacinalMaeOld = this.medidasControleEsquemaVacinalMae;
		this.medidasControleEsquemaVacinalMae = medidasControleEsquemaVacinalMae;
//        this.getPropertyChangeSupport().firePropertyChange ("medidasControleEsquemaVacinalMae", medidasControleEsquemaVacinalMaeOld, medidasControleEsquemaVacinalMae);
	}



	/**
	 * Return the value associated with the column: medidas_controle_busca_ativa
	 */
	public java.lang.Long getMedidasControleBuscaAtiva () {
		return getPropertyValue(this, medidasControleBuscaAtiva, PROP_MEDIDAS_CONTROLE_BUSCA_ATIVA); 
	}

	/**
	 * Set the value related to the column: medidas_controle_busca_ativa
	 * @param medidasControleBuscaAtiva the medidas_controle_busca_ativa value
	 */
	public void setMedidasControleBuscaAtiva (java.lang.Long medidasControleBuscaAtiva) {
//        java.lang.Long medidasControleBuscaAtivaOld = this.medidasControleBuscaAtiva;
		this.medidasControleBuscaAtiva = medidasControleBuscaAtiva;
//        this.getPropertyChangeSupport().firePropertyChange ("medidasControleBuscaAtiva", medidasControleBuscaAtivaOld, medidasControleBuscaAtiva);
	}



	/**
	 * Return the value associated with the column: medidas_controle_analise_cv
	 */
	public java.lang.Long getMedidasControleAnaliseCv () {
		return getPropertyValue(this, medidasControleAnaliseCv, PROP_MEDIDAS_CONTROLE_ANALISE_CV); 
	}

	/**
	 * Set the value related to the column: medidas_controle_analise_cv
	 * @param medidasControleAnaliseCv the medidas_controle_analise_cv value
	 */
	public void setMedidasControleAnaliseCv (java.lang.Long medidasControleAnaliseCv) {
//        java.lang.Long medidasControleAnaliseCvOld = this.medidasControleAnaliseCv;
		this.medidasControleAnaliseCv = medidasControleAnaliseCv;
//        this.getPropertyChangeSupport().firePropertyChange ("medidasControleAnaliseCv", medidasControleAnaliseCvOld, medidasControleAnaliseCv);
	}



	/**
	 * Return the value associated with the column: medidas_controle_cadastro_parteiras
	 */
	public java.lang.Long getMedidasControleCadastroParteiras () {
		return getPropertyValue(this, medidasControleCadastroParteiras, PROP_MEDIDAS_CONTROLE_CADASTRO_PARTEIRAS); 
	}

	/**
	 * Set the value related to the column: medidas_controle_cadastro_parteiras
	 * @param medidasControleCadastroParteiras the medidas_controle_cadastro_parteiras value
	 */
	public void setMedidasControleCadastroParteiras (java.lang.Long medidasControleCadastroParteiras) {
//        java.lang.Long medidasControleCadastroParteirasOld = this.medidasControleCadastroParteiras;
		this.medidasControleCadastroParteiras = medidasControleCadastroParteiras;
//        this.getPropertyChangeSupport().firePropertyChange ("medidasControleCadastroParteiras", medidasControleCadastroParteirasOld, medidasControleCadastroParteiras);
	}



	/**
	 * Return the value associated with the column: medidas_controle_orientacao_parturientes
	 */
	public java.lang.Long getMedidasControleOrientacaoParturientes () {
		return getPropertyValue(this, medidasControleOrientacaoParturientes, PROP_MEDIDAS_CONTROLE_ORIENTACAO_PARTURIENTES); 
	}

	/**
	 * Set the value related to the column: medidas_controle_orientacao_parturientes
	 * @param medidasControleOrientacaoParturientes the medidas_controle_orientacao_parturientes value
	 */
	public void setMedidasControleOrientacaoParturientes (java.lang.Long medidasControleOrientacaoParturientes) {
//        java.lang.Long medidasControleOrientacaoParturientesOld = this.medidasControleOrientacaoParturientes;
		this.medidasControleOrientacaoParturientes = medidasControleOrientacaoParturientes;
//        this.getPropertyChangeSupport().firePropertyChange ("medidasControleOrientacaoParturientes", medidasControleOrientacaoParturientesOld, medidasControleOrientacaoParturientes);
	}



	/**
	 * Return the value associated with the column: medidas_controle_divulgacao_problema
	 */
	public java.lang.Long getMedidasControleDivulgacaoProblema () {
		return getPropertyValue(this, medidasControleDivulgacaoProblema, PROP_MEDIDAS_CONTROLE_DIVULGACAO_PROBLEMA); 
	}

	/**
	 * Set the value related to the column: medidas_controle_divulgacao_problema
	 * @param medidasControleDivulgacaoProblema the medidas_controle_divulgacao_problema value
	 */
	public void setMedidasControleDivulgacaoProblema (java.lang.Long medidasControleDivulgacaoProblema) {
//        java.lang.Long medidasControleDivulgacaoProblemaOld = this.medidasControleDivulgacaoProblema;
		this.medidasControleDivulgacaoProblema = medidasControleDivulgacaoProblema;
//        this.getPropertyChangeSupport().firePropertyChange ("medidasControleDivulgacaoProblema", medidasControleDivulgacaoProblemaOld, medidasControleDivulgacaoProblema);
	}



	/**
	 * Return the value associated with the column: medidas_controle_outros
	 */
	public java.lang.String getMedidasControleOutros () {
		return getPropertyValue(this, medidasControleOutros, PROP_MEDIDAS_CONTROLE_OUTROS); 
	}

	/**
	 * Set the value related to the column: medidas_controle_outros
	 * @param medidasControleOutros the medidas_controle_outros value
	 */
	public void setMedidasControleOutros (java.lang.String medidasControleOutros) {
//        java.lang.String medidasControleOutrosOld = this.medidasControleOutros;
		this.medidasControleOutros = medidasControleOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("medidasControleOutros", medidasControleOutrosOld, medidasControleOutros);
	}



	/**
	 * Return the value associated with the column: local_provavel_infeccao
	 */
	public java.lang.Long getLocalProvavelInfeccao () {
		return getPropertyValue(this, localProvavelInfeccao, PROP_LOCAL_PROVAVEL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: local_provavel_infeccao
	 * @param localProvavelInfeccao the local_provavel_infeccao value
	 */
	public void setLocalProvavelInfeccao (java.lang.Long localProvavelInfeccao) {
//        java.lang.Long localProvavelInfeccaoOld = this.localProvavelInfeccao;
		this.localProvavelInfeccao = localProvavelInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("localProvavelInfeccao", localProvavelInfeccaoOld, localProvavelInfeccao);
	}



	/**
	 * Return the value associated with the column: local_provavel_infeccao_outros
	 */
	public java.lang.String getLocalProvavelInfeccaoOutro () {
		return getPropertyValue(this, localProvavelInfeccaoOutro, PROP_LOCAL_PROVAVEL_INFECCAO_OUTRO); 
	}

	/**
	 * Set the value related to the column: local_provavel_infeccao_outros
	 * @param localProvavelInfeccaoOutro the local_provavel_infeccao_outros value
	 */
	public void setLocalProvavelInfeccaoOutro (java.lang.String localProvavelInfeccaoOutro) {
//        java.lang.String localProvavelInfeccaoOutroOld = this.localProvavelInfeccaoOutro;
		this.localProvavelInfeccaoOutro = localProvavelInfeccaoOutro;
//        this.getPropertyChangeSupport().firePropertyChange ("localProvavelInfeccaoOutro", localProvavelInfeccaoOutroOld, localProvavelInfeccaoOutro);
	}



	/**
	 * Return the value associated with the column: caso_autoctone
	 */
	public java.lang.Long getCasoAutoctone () {
		return getPropertyValue(this, casoAutoctone, PROP_CASO_AUTOCTONE); 
	}

	/**
	 * Set the value related to the column: caso_autoctone
	 * @param casoAutoctone the caso_autoctone value
	 */
	public void setCasoAutoctone (java.lang.Long casoAutoctone) {
//        java.lang.Long casoAutoctoneOld = this.casoAutoctone;
		this.casoAutoctone = casoAutoctone;
//        this.getPropertyChangeSupport().firePropertyChange ("casoAutoctone", casoAutoctoneOld, casoAutoctone);
	}



	/**
	 * Return the value associated with the column: str_distrito_infeccao
	 */
	public java.lang.String getDistritoLocalInfeccao () {
		return getPropertyValue(this, distritoLocalInfeccao, PROP_DISTRITO_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: str_distrito_infeccao
	 * @param distritoLocalInfeccao the str_distrito_infeccao value
	 */
	public void setDistritoLocalInfeccao (java.lang.String distritoLocalInfeccao) {
//        java.lang.String distritoLocalInfeccaoOld = this.distritoLocalInfeccao;
		this.distritoLocalInfeccao = distritoLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("distritoLocalInfeccao", distritoLocalInfeccaoOld, distritoLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: str_bairro_infeccao
	 */
	public java.lang.String getBairroLocalInfeccao () {
		return getPropertyValue(this, bairroLocalInfeccao, PROP_BAIRRO_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: str_bairro_infeccao
	 * @param bairroLocalInfeccao the str_bairro_infeccao value
	 */
	public void setBairroLocalInfeccao (java.lang.String bairroLocalInfeccao) {
//        java.lang.String bairroLocalInfeccaoOld = this.bairroLocalInfeccao;
		this.bairroLocalInfeccao = bairroLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("bairroLocalInfeccao", bairroLocalInfeccaoOld, bairroLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: classificacao_final
	 */
	public java.lang.Long getClassificacaoFinal () {
		return getPropertyValue(this, classificacaoFinal, PROP_CLASSIFICACAO_FINAL); 
	}

	/**
	 * Set the value related to the column: classificacao_final
	 * @param classificacaoFinal the classificacao_final value
	 */
	public void setClassificacaoFinal (java.lang.Long classificacaoFinal) {
//        java.lang.Long classificacaoFinalOld = this.classificacaoFinal;
		this.classificacaoFinal = classificacaoFinal;
//        this.getPropertyChangeSupport().firePropertyChange ("classificacaoFinal", classificacaoFinalOld, classificacaoFinal);
	}



	/**
	 * Return the value associated with the column: evolucao_caso
	 */
	public java.lang.Long getEvolucaoCaso () {
		return getPropertyValue(this, evolucaoCaso, PROP_EVOLUCAO_CASO); 
	}

	/**
	 * Set the value related to the column: evolucao_caso
	 * @param evolucaoCaso the evolucao_caso value
	 */
	public void setEvolucaoCaso (java.lang.Long evolucaoCaso) {
//        java.lang.Long evolucaoCasoOld = this.evolucaoCaso;
		this.evolucaoCaso = evolucaoCaso;
//        this.getPropertyChangeSupport().firePropertyChange ("evolucaoCaso", evolucaoCasoOld, evolucaoCaso);
	}



	/**
	 * Return the value associated with the column: dt_obito
	 */
	public java.util.Date getDataObito () {
		return getPropertyValue(this, dataObito, PROP_DATA_OBITO); 
	}

	/**
	 * Set the value related to the column: dt_obito
	 * @param dataObito the dt_obito value
	 */
	public void setDataObito (java.util.Date dataObito) {
//        java.util.Date dataObitoOld = this.dataObito;
		this.dataObito = dataObito;
//        this.getPropertyChangeSupport().firePropertyChange ("dataObito", dataObitoOld, dataObito);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: dt_encerramento
	 */
	public java.util.Date getDataEncerramento () {
		return getPropertyValue(this, dataEncerramento, PROP_DATA_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_encerramento
	 * @param dataEncerramento the dt_encerramento value
	 */
	public void setDataEncerramento (java.util.Date dataEncerramento) {
//        java.util.Date dataEncerramentoOld = this.dataEncerramento;
		this.dataEncerramento = dataEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEncerramento", dataEncerramentoOld, dataEncerramento);
	}



	/**
	 * Return the value associated with the column: cd_registro_agravo
	 */
	public br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo getRegistroAgravo () {
		return getPropertyValue(this, registroAgravo, PROP_REGISTRO_AGRAVO); 
	}

	/**
	 * Set the value related to the column: cd_registro_agravo
	 * @param registroAgravo the cd_registro_agravo value
	 */
	public void setRegistroAgravo (br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo) {
//        br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravoOld = this.registroAgravo;
		this.registroAgravo = registroAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("registroAgravo", registroAgravoOld, registroAgravo);
	}



	/**
	 * Return the value associated with the column: ocupacao_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getOcupacaoCbo () {
		return getPropertyValue(this, ocupacaoCbo, PROP_OCUPACAO_CBO); 
	}

	/**
	 * Set the value related to the column: ocupacao_cbo
	 * @param ocupacaoCbo the ocupacao_cbo value
	 */
	public void setOcupacaoCbo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCboOld = this.ocupacaoCbo;
		this.ocupacaoCbo = ocupacaoCbo;
//        this.getPropertyChangeSupport().firePropertyChange ("ocupacaoCbo", ocupacaoCboOld, ocupacaoCbo);
	}



	/**
	 * Return the value associated with the column: unidade_hospital
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getHospital () {
		return getPropertyValue(this, hospital, PROP_HOSPITAL); 
	}

	/**
	 * Set the value related to the column: unidade_hospital
	 * @param hospital the unidade_hospital value
	 */
	public void setHospital (br.com.ksisolucoes.vo.basico.Empresa hospital) {
//        br.com.ksisolucoes.vo.basico.Empresa hospitalOld = this.hospital;
		this.hospital = hospital;
//        this.getPropertyChangeSupport().firePropertyChange ("hospital", hospitalOld, hospital);
	}



	/**
	 * Return the value associated with the column: local_infeccao_unidade
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getLocalInfeccaoUnidade () {
		return getPropertyValue(this, localInfeccaoUnidade, PROP_LOCAL_INFECCAO_UNIDADE); 
	}

	/**
	 * Set the value related to the column: local_infeccao_unidade
	 * @param localInfeccaoUnidade the local_infeccao_unidade value
	 */
	public void setLocalInfeccaoUnidade (br.com.ksisolucoes.vo.basico.Empresa localInfeccaoUnidade) {
//        br.com.ksisolucoes.vo.basico.Empresa localInfeccaoUnidadeOld = this.localInfeccaoUnidade;
		this.localInfeccaoUnidade = localInfeccaoUnidade;
//        this.getPropertyChangeSupport().firePropertyChange ("localInfeccaoUnidade", localInfeccaoUnidadeOld, localInfeccaoUnidade);
	}



	/**
	 * Return the value associated with the column: cd_cidade_infeccao
	 */
	public br.com.ksisolucoes.vo.basico.Cidade getCidadeLocalInfeccao () {
		return getPropertyValue(this, cidadeLocalInfeccao, PROP_CIDADE_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: cd_cidade_infeccao
	 * @param cidadeLocalInfeccao the cd_cidade_infeccao value
	 */
	public void setCidadeLocalInfeccao (br.com.ksisolucoes.vo.basico.Cidade cidadeLocalInfeccao) {
//        br.com.ksisolucoes.vo.basico.Cidade cidadeLocalInfeccaoOld = this.cidadeLocalInfeccao;
		this.cidadeLocalInfeccao = cidadeLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("cidadeLocalInfeccao", cidadeLocalInfeccaoOld, cidadeLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: cd_pais_infeccao
	 */
	public br.com.ksisolucoes.vo.basico.Pais getPaisLocalInfeccao () {
		return getPropertyValue(this, paisLocalInfeccao, PROP_PAIS_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: cd_pais_infeccao
	 * @param paisLocalInfeccao the cd_pais_infeccao value
	 */
	public void setPaisLocalInfeccao (br.com.ksisolucoes.vo.basico.Pais paisLocalInfeccao) {
//        br.com.ksisolucoes.vo.basico.Pais paisLocalInfeccaoOld = this.paisLocalInfeccao;
		this.paisLocalInfeccao = paisLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("paisLocalInfeccao", paisLocalInfeccaoOld, paisLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: cd_usuario_encerramento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioEncerramento () {
		return getPropertyValue(this, usuarioEncerramento, PROP_USUARIO_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_encerramento
	 * @param usuarioEncerramento the cd_usuario_encerramento value
	 */
	public void setUsuarioEncerramento (br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramentoOld = this.usuarioEncerramento;
		this.usuarioEncerramento = usuarioEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioEncerramento", usuarioEncerramentoOld, usuarioEncerramento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoTetanoNeonatal)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoTetanoNeonatal investigacaoAgravoTetanoNeonatal = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoTetanoNeonatal) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoTetanoNeonatal.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoTetanoNeonatal.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}