<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.basico"  >
    <class
        name="EmpresaBpa"
        table="empresa_bpa"
    >
        <id
            name="codigo"
            type="java.lang.Long" 
            column="empresa"
        >
            <generator class="assigned"/>
        </id> 
        <version column="version" name="version" type="long" />
 
        <property
            name="geraBpa"
            column="gera_bpa"
            type="java.lang.String"
            length="1"
        /> 
        
        <property
            name="sequenceLoteBpa"
            column="seq_lote_bpa"
            type="java.lang.Long"
        />

        <one-to-one name="empresa" 
                    class="br.com.ksisolucoes.vo.basico.Empresa"
                    constrained="true">
        </one-to-one>        

        <property
            name="origemDados"
            type="java.lang.Long"
            column="origem_dados"
        />
                                                        		
    </class>
</hibernate-mapping>
