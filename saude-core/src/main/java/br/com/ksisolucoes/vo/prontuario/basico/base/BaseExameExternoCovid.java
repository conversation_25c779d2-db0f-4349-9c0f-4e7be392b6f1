package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the exame_externo_covid table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="exame_externo_covid"
 */

public abstract class BaseExameExternoCovid extends BaseRootVO implements Serializable {

	public static String REF = "ExameExternoCovid";
	public static final String PROP_DATA_EXAME = "dataExame";
	public static final String PROP_DESCRICAO_EXAME = "descricaoExame";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_DATA_NASCIMENTO = "dataNascimento";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_NOME_MAE = "nomeMae";
	public static final String PROP_TELEFONE = "telefone";
	public static final String PROP_CPF_PACIENTE = "cpfPaciente";
	public static final String PROP_ENDERECO_PACIENTE = "enderecoPaciente";
	public static final String PROP_SEXO = "sexo";
	public static final String PROP_ESTADO_PACIENTE = "estadoPaciente";
	public static final String PROP_RESULTADO = "resultado";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_CEP_PACIENTE = "cepPaciente";
	public static final String PROP_NOME_PACIENTE = "nomePaciente";
	public static final String PROP_MUNICIPIO_PACIENTE = "municipioPaciente";
	public static final String PROP_RACA_COR = "racaCor";
	public static final String PROP_BAIRRO_PACIENTE = "bairroPaciente";


	// constructors
	public BaseExameExternoCovid () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseExameExternoCovid (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseExameExternoCovid (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.String nomePaciente,
		java.lang.String municipioPaciente,
		java.util.Date dataNascimento,
		java.lang.String telefone,
		java.lang.String descricaoExame,
		java.lang.String resultado,
		java.util.Date dataExame) {

		this.setCodigo(codigo);
		this.setUsuario(usuario);
		this.setNomePaciente(nomePaciente);
		this.setMunicipioPaciente(municipioPaciente);
		this.setDataNascimento(dataNascimento);
		this.setTelefone(telefone);
		this.setDescricaoExame(descricaoExame);
		this.setResultado(resultado);
		this.setDataExame(dataExame);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String nomePaciente;
	private java.lang.String municipioPaciente;
	private java.lang.String nomeMae;
	private java.lang.String racaCor;
	private java.lang.String sexo;
	private java.lang.String estadoPaciente;
	private java.lang.String bairroPaciente;
	private java.lang.String enderecoPaciente;
	private java.lang.String cepPaciente;
	private java.lang.String cpfPaciente;
	private java.util.Date dataNascimento;
	private java.lang.String telefone;
	private java.lang.String descricaoExame;
	private java.lang.String resultado;
	private java.util.Date dataExame;

	// many to one
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.basico.Empresa empresa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_exame_externo_covid"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nome_paciente
	 */
	public java.lang.String getNomePaciente () {
		return getPropertyValue(this, nomePaciente, PROP_NOME_PACIENTE); 
	}

	/**
	 * Set the value related to the column: nome_paciente
	 * @param nomePaciente the nome_paciente value
	 */
	public void setNomePaciente (java.lang.String nomePaciente) {
//        java.lang.String nomePacienteOld = this.nomePaciente;
		this.nomePaciente = nomePaciente;
//        this.getPropertyChangeSupport().firePropertyChange ("nomePaciente", nomePacienteOld, nomePaciente);
	}



	/**
	 * Return the value associated with the column: municipio_paciente
	 */
	public java.lang.String getMunicipioPaciente () {
		return getPropertyValue(this, municipioPaciente, PROP_MUNICIPIO_PACIENTE); 
	}

	/**
	 * Set the value related to the column: municipio_paciente
	 * @param municipioPaciente the municipio_paciente value
	 */
	public void setMunicipioPaciente (java.lang.String municipioPaciente) {
//        java.lang.String municipioPacienteOld = this.municipioPaciente;
		this.municipioPaciente = municipioPaciente;
//        this.getPropertyChangeSupport().firePropertyChange ("municipioPaciente", municipioPacienteOld, municipioPaciente);
	}



	/**
	 * Return the value associated with the column: nome_mae
	 */
	public java.lang.String getNomeMae () {
		return getPropertyValue(this, nomeMae, PROP_NOME_MAE); 
	}

	/**
	 * Set the value related to the column: nome_mae
	 * @param nomeMae the nome_mae value
	 */
	public void setNomeMae (java.lang.String nomeMae) {
//        java.lang.String nomeMaeOld = this.nomeMae;
		this.nomeMae = nomeMae;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeMae", nomeMaeOld, nomeMae);
	}



	/**
	 * Return the value associated with the column: raca_cor
	 */
	public java.lang.String getRacaCor () {
		return getPropertyValue(this, racaCor, PROP_RACA_COR); 
	}

	/**
	 * Set the value related to the column: raca_cor
	 * @param racaCor the raca_cor value
	 */
	public void setRacaCor (java.lang.String racaCor) {
//        java.lang.String racaCorOld = this.racaCor;
		this.racaCor = racaCor;
//        this.getPropertyChangeSupport().firePropertyChange ("racaCor", racaCorOld, racaCor);
	}



	/**
	 * Return the value associated with the column: sexo
	 */
	public java.lang.String getSexo () {
		return getPropertyValue(this, sexo, PROP_SEXO); 
	}

	/**
	 * Set the value related to the column: sexo
	 * @param sexo the sexo value
	 */
	public void setSexo (java.lang.String sexo) {
//        java.lang.String sexoOld = this.sexo;
		this.sexo = sexo;
//        this.getPropertyChangeSupport().firePropertyChange ("sexo", sexoOld, sexo);
	}



	/**
	 * Return the value associated with the column: estado_paciente
	 */
	public java.lang.String getEstadoPaciente () {
		return getPropertyValue(this, estadoPaciente, PROP_ESTADO_PACIENTE); 
	}

	/**
	 * Set the value related to the column: estado_paciente
	 * @param estadoPaciente the estado_paciente value
	 */
	public void setEstadoPaciente (java.lang.String estadoPaciente) {
//        java.lang.String estadoPacienteOld = this.estadoPaciente;
		this.estadoPaciente = estadoPaciente;
//        this.getPropertyChangeSupport().firePropertyChange ("estadoPaciente", estadoPacienteOld, estadoPaciente);
	}



	/**
	 * Return the value associated with the column: bairro_paciente
	 */
	public java.lang.String getBairroPaciente () {
		return getPropertyValue(this, bairroPaciente, PROP_BAIRRO_PACIENTE); 
	}

	/**
	 * Set the value related to the column: bairro_paciente
	 * @param bairroPaciente the bairro_paciente value
	 */
	public void setBairroPaciente (java.lang.String bairroPaciente) {
//        java.lang.String bairroPacienteOld = this.bairroPaciente;
		this.bairroPaciente = bairroPaciente;
//        this.getPropertyChangeSupport().firePropertyChange ("bairroPaciente", bairroPacienteOld, bairroPaciente);
	}



	/**
	 * Return the value associated with the column: endereco_paciente
	 */
	public java.lang.String getEnderecoPaciente () {
		return getPropertyValue(this, enderecoPaciente, PROP_ENDERECO_PACIENTE); 
	}

	/**
	 * Set the value related to the column: endereco_paciente
	 * @param enderecoPaciente the endereco_paciente value
	 */
	public void setEnderecoPaciente (java.lang.String enderecoPaciente) {
//        java.lang.String enderecoPacienteOld = this.enderecoPaciente;
		this.enderecoPaciente = enderecoPaciente;
//        this.getPropertyChangeSupport().firePropertyChange ("enderecoPaciente", enderecoPacienteOld, enderecoPaciente);
	}



	/**
	 * Return the value associated with the column: cep_paciente
	 */
	public java.lang.String getCepPaciente () {
		return getPropertyValue(this, cepPaciente, PROP_CEP_PACIENTE); 
	}

	/**
	 * Set the value related to the column: cep_paciente
	 * @param cepPaciente the cep_paciente value
	 */
	public void setCepPaciente (java.lang.String cepPaciente) {
//        java.lang.String cepPacienteOld = this.cepPaciente;
		this.cepPaciente = cepPaciente;
//        this.getPropertyChangeSupport().firePropertyChange ("cepPaciente", cepPacienteOld, cepPaciente);
	}



	/**
	 * Return the value associated with the column: cpf_paciente
	 */
	public java.lang.String getCpfPaciente () {
		return getPropertyValue(this, cpfPaciente, PROP_CPF_PACIENTE); 
	}

	/**
	 * Set the value related to the column: cpf_paciente
	 * @param cpfPaciente the cpf_paciente value
	 */
	public void setCpfPaciente (java.lang.String cpfPaciente) {
//        java.lang.String cpfPacienteOld = this.cpfPaciente;
		this.cpfPaciente = cpfPaciente;
//        this.getPropertyChangeSupport().firePropertyChange ("cpfPaciente", cpfPacienteOld, cpfPaciente);
	}



	/**
	 * Return the value associated with the column: data_nascimento
	 */
	public java.util.Date getDataNascimento () {
		return getPropertyValue(this, dataNascimento, PROP_DATA_NASCIMENTO); 
	}

	/**
	 * Set the value related to the column: data_nascimento
	 * @param dataNascimento the data_nascimento value
	 */
	public void setDataNascimento (java.util.Date dataNascimento) {
//        java.util.Date dataNascimentoOld = this.dataNascimento;
		this.dataNascimento = dataNascimento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataNascimento", dataNascimentoOld, dataNascimento);
	}



	/**
	 * Return the value associated with the column: telefone
	 */
	public java.lang.String getTelefone () {
		return getPropertyValue(this, telefone, PROP_TELEFONE); 
	}

	/**
	 * Set the value related to the column: telefone
	 * @param telefone the telefone value
	 */
	public void setTelefone (java.lang.String telefone) {
//        java.lang.String telefoneOld = this.telefone;
		this.telefone = telefone;
//        this.getPropertyChangeSupport().firePropertyChange ("telefone", telefoneOld, telefone);
	}



	/**
	 * Return the value associated with the column: descricao_exame
	 */
	public java.lang.String getDescricaoExame () {
		return getPropertyValue(this, descricaoExame, PROP_DESCRICAO_EXAME); 
	}

	/**
	 * Set the value related to the column: descricao_exame
	 * @param descricaoExame the descricao_exame value
	 */
	public void setDescricaoExame (java.lang.String descricaoExame) {
//        java.lang.String descricaoExameOld = this.descricaoExame;
		this.descricaoExame = descricaoExame;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoExame", descricaoExameOld, descricaoExame);
	}



	/**
	 * Return the value associated with the column: resultado
	 */
	public java.lang.String getResultado () {
		return getPropertyValue(this, resultado, PROP_RESULTADO); 
	}

	/**
	 * Set the value related to the column: resultado
	 * @param resultado the resultado value
	 */
	public void setResultado (java.lang.String resultado) {
//        java.lang.String resultadoOld = this.resultado;
		this.resultado = resultado;
//        this.getPropertyChangeSupport().firePropertyChange ("resultado", resultadoOld, resultado);
	}



	/**
	 * Return the value associated with the column: data_exame
	 */
	public java.util.Date getDataExame () {
		return getPropertyValue(this, dataExame, PROP_DATA_EXAME); 
	}

	/**
	 * Set the value related to the column: data_exame
	 * @param dataExame the data_exame value
	 */
	public void setDataExame (java.util.Date dataExame) {
//        java.util.Date dataExameOld = this.dataExame;
		this.dataExame = dataExame;
//        this.getPropertyChangeSupport().firePropertyChange ("dataExame", dataExameOld, dataExame);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.ExameExternoCovid)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.ExameExternoCovid exameExternoCovid = (br.com.ksisolucoes.vo.prontuario.basico.ExameExternoCovid) obj;
			if (null == this.getCodigo() || null == exameExternoCovid.getCodigo()) return false;
			else return (this.getCodigo().equals(exameExternoCovid.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}