package br.com.ksisolucoes.vo.financeiro.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;



/**
 * This class has been automatically generated by Hibernate Synchronizer.
 * For more information or documentation, visit The Hibernate Synchronizer page
 * at http://www.binamics.com/hibernatesync or contact <PERSON> at <EMAIL>.
 *
 * Este objeto est relacionado com a tabela tipo_duplicata.
 * No modifique esta classe, pois, sincronizaes com a 
 * base sobrescrevero as alteraes.
 *
 * @hibernate.class
 *  table="tipo_duplicata"
 */
public abstract class BaseTipoDuplicata  extends BaseRootVO implements Serializable, ValidacaoExceptionInterface {

	public static String PROP_FLAG_GERA_MANUAL = "flagGeraManual";
	public static String PROP_FLAG_GERA_COMISSAO = "flagGeraComissao";
	public static String PROP_FLAG_RELATORIO = "flagRelatorio";
	public static String PROP_CODIGO = "codigo";
	public static String PROP_DESCRICAO = "descricao";

	public RetornoValidacao retornoValidacao = new RetornoValidacao();
    protected java.beans.PropertyChangeSupport propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);

	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.String  codigo;

	// fields
	private java.lang.String flagRelatorio;
	private java.lang.String descricao;
	private java.lang.String flagGeraManual;
	private java.lang.String flagGeraComissao;


	// construtores
	public BaseTipoDuplicata () {}

	/**
	 * Construtor para a chave primria.
	 */
	public BaseTipoDuplicata (java.lang.String codigo) {
		this.setCodigo(codigo);
	}

	/**
	 * Construtor para os atributos requeridos.
	 */
	public BaseTipoDuplicata (
		java.lang.String codigo,
		java.lang.String flagRelatorio,
		java.lang.String descricao,
		java.lang.String flagGeraManual,
		java.lang.String flagGeraComissao) {

		this.setCodigo(codigo);
		this.setFlagRelatorio(flagRelatorio);
		this.setDescricao(descricao);
		this.setFlagGeraManual(flagGeraManual);
		this.setFlagGeraComissao(flagGeraComissao);
	}



	/**
	 * Retorna o identificador nico da classe.
	 *
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cod_tip_duplicata"
     *
	 * @return codigo
	 */
	public java.lang.String getCodigo() {
		return codigo;
	}

	/**
	 * Seta o identificador nico da classe.
	 *
	 * @param codigo para o aributo codigo
	 */
	public void setCodigo(java.lang.String codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}


    /**
     * Retorna o valor do atributo flagRelatorio
     *
     * @return flagRelatorio
     */
	public java.lang.String getFlagRelatorio() {
		return flagRelatorio;
	}

	/**
	 * Setar um valor para o atributo: flagRelatorio.
     *
	 * @param flagRelatorio valor para o atributo flagRelatorio.
	 */
	public void setFlagRelatorio(java.lang.String flagRelatorio) {
        java.lang.String flagRelatorioOld = this.flagRelatorio;
		this.flagRelatorio = flagRelatorio;
        propertyChangeSupport.firePropertyChange ("flagRelatorio", flagRelatorioOld, flagRelatorio);
	}


    /**
     * Retorna o valor do atributo descricao
     *
     * @return descricao
     */
	public java.lang.String getDescricao() {
		return descricao;
	}

	/**
	 * Setar um valor para o atributo: descricao.
     *
	 * @param descricao valor para o atributo descricao.
	 */
	public void setDescricao(java.lang.String descricao) {
        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
        propertyChangeSupport.firePropertyChange ("descricao", descricaoOld, descricao);
	}


    /**
     * Retorna o valor do atributo flagGeraManual
     *
     * @return flagGeraManual
     */
	public java.lang.String getFlagGeraManual() {
		return flagGeraManual;
	}

	/**
	 * Setar um valor para o atributo: flagGeraManual.
     *
	 * @param flagGeraManual valor para o atributo flagGeraManual.
	 */
	public void setFlagGeraManual(java.lang.String flagGeraManual) {
        java.lang.String flagGeraManualOld = this.flagGeraManual;
		this.flagGeraManual = flagGeraManual;
        propertyChangeSupport.firePropertyChange ("flagGeraManual", flagGeraManualOld, flagGeraManual);
	}


    /**
     * Retorna o valor do atributo flagGeraComissao
     *
     * @return flagGeraComissao
     */
	public java.lang.String getFlagGeraComissao() {
		return flagGeraComissao;
	}

	/**
	 * Setar um valor para o atributo: flagGeraComissao.
     *
	 * @param flagGeraComissao valor para o atributo flagGeraComissao.
	 */
	public void setFlagGeraComissao(java.lang.String flagGeraComissao) {
        java.lang.String flagGeraComissaoOld = this.flagGeraComissao;
		this.flagGeraComissao = flagGeraComissao;
        propertyChangeSupport.firePropertyChange ("flagGeraComissao", flagGeraComissaoOld, flagGeraComissao);
	}


	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.financeiro.base.BaseTipoDuplicata)) return false;
		else {
			br.com.ksisolucoes.vo.financeiro.base.BaseTipoDuplicata mObj = (br.com.ksisolucoes.vo.financeiro.base.BaseTipoDuplicata) obj;
			if (null == this.getCodigo() || null == mObj.getCodigo()) return false;
			else return (this.getCodigo().equals(mObj.getCodigo()));
		}
	}


	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


    /**
     * Adds a PropertyChangeListener to the listener list.
     * @param l The listener to add.
     */
    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {

        propertyChangeSupport.addPropertyChangeListener(l);
    }

    /**
     * Removes a PropertyChangeListener from the listener list.
     * @param l The listener to remove.
     */
    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {

        propertyChangeSupport.removePropertyChangeListener(l);
    }

    /* (non-Javadoc)
     * @see br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface#getRetornoValidacao()
     */
    public RetornoValidacao getRetornoValidacao() {
        return this.retornoValidacao;
    }

   public String toString() {
       StringBuffer s = new StringBuffer();
   // primary key
       s.append("codigo: " + codigo + ".\n");

   // fields
       s.append("flagRelatorio: " + flagRelatorio + ".\n");
       s.append("descricao: " + descricao + ".\n");
       s.append("flagGeraManual: " + flagGeraManual + ".\n");
       s.append("flagGeraComissao: " + flagGeraComissao + ".\n");

       return s.toString();
   }


}