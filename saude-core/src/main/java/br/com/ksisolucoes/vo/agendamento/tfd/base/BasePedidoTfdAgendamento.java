package br.com.ksisolucoes.vo.agendamento.tfd.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the pedido_tfd_agendamento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="pedido_tfd_agendamento"
 */

public abstract class BasePedidoTfdAgendamento extends BaseRootVO implements Serializable {

	public static String REF = "PedidoTfdAgendamento";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_STATUS = "status";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_USUARIO = "dataUsuario";
	public static final String PROP_PEDIDO_TFD = "pedidoTfd";
	public static final String PROP_DATA_AGENDAMENTO = "dataAgendamento";
	public static final String PROP_LOCAL_AGENDAMENTO = "localAgendamento";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_SOLICITACAO_AGENDAMENTO = "solicitacaoAgendamento";


	// constructors
	public BasePedidoTfdAgendamento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BasePedidoTfdAgendamento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataAgendamento;
	private java.lang.Long status;
	private java.lang.String observacao;
	private java.util.Date dataUsuario;

	// many to one
	private br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd pedidoTfd;
	private br.com.ksisolucoes.vo.basico.Empresa localAgendamento;
	private br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento solicitacaoAgendamento;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_ped_tfd_agendamento"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_agendamento
	 */
	public java.util.Date getDataAgendamento () {
		return getPropertyValue(this, dataAgendamento, PROP_DATA_AGENDAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_agendamento
	 * @param dataAgendamento the dt_agendamento value
	 */
	public void setDataAgendamento (java.util.Date dataAgendamento) {
//        java.util.Date dataAgendamentoOld = this.dataAgendamento;
		this.dataAgendamento = dataAgendamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAgendamento", dataAgendamentoOld, dataAgendamento);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: dt_usuario
	 */
	public java.util.Date getDataUsuario () {
		return getPropertyValue(this, dataUsuario, PROP_DATA_USUARIO); 
	}

	/**
	 * Set the value related to the column: dt_usuario
	 * @param dataUsuario the dt_usuario value
	 */
	public void setDataUsuario (java.util.Date dataUsuario) {
//        java.util.Date dataUsuarioOld = this.dataUsuario;
		this.dataUsuario = dataUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuario", dataUsuarioOld, dataUsuario);
	}



	/**
	 * Return the value associated with the column: cd_pedido_tfd
	 */
	public br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd getPedidoTfd () {
		return getPropertyValue(this, pedidoTfd, PROP_PEDIDO_TFD); 
	}

	/**
	 * Set the value related to the column: cd_pedido_tfd
	 * @param pedidoTfd the cd_pedido_tfd value
	 */
	public void setPedidoTfd (br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd pedidoTfd) {
//        br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd pedidoTfdOld = this.pedidoTfd;
		this.pedidoTfd = pedidoTfd;
//        this.getPropertyChangeSupport().firePropertyChange ("pedidoTfd", pedidoTfdOld, pedidoTfd);
	}



	/**
	 * Return the value associated with the column: local_agendamento
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getLocalAgendamento () {
		return getPropertyValue(this, localAgendamento, PROP_LOCAL_AGENDAMENTO); 
	}

	/**
	 * Set the value related to the column: local_agendamento
	 * @param localAgendamento the local_agendamento value
	 */
	public void setLocalAgendamento (br.com.ksisolucoes.vo.basico.Empresa localAgendamento) {
//        br.com.ksisolucoes.vo.basico.Empresa localAgendamentoOld = this.localAgendamento;
		this.localAgendamento = localAgendamento;
//        this.getPropertyChangeSupport().firePropertyChange ("localAgendamento", localAgendamentoOld, localAgendamento);
	}



	/**
	 * Return the value associated with the column: cd_solicitacao
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento getSolicitacaoAgendamento () {
		return getPropertyValue(this, solicitacaoAgendamento, PROP_SOLICITACAO_AGENDAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_solicitacao
	 * @param solicitacaoAgendamento the cd_solicitacao value
	 */
	public void setSolicitacaoAgendamento (br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento solicitacaoAgendamento) {
//        br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento solicitacaoAgendamentoOld = this.solicitacaoAgendamento;
		this.solicitacaoAgendamento = solicitacaoAgendamento;
//        this.getPropertyChangeSupport().firePropertyChange ("solicitacaoAgendamento", solicitacaoAgendamentoOld, solicitacaoAgendamento);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfdAgendamento)) return false;
		else {
			br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfdAgendamento pedidoTfdAgendamento = (br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfdAgendamento) obj;
			if (null == this.getCodigo() || null == pedidoTfdAgendamento.getCodigo()) return false;
			else return (this.getCodigo().equals(pedidoTfdAgendamento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}