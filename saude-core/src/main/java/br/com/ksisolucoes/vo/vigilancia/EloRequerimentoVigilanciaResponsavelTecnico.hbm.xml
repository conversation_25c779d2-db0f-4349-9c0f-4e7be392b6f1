<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia">
    <class name="EloRequerimentoVigilanciaResponsavelTecnico" table="elo_req_vigi_resp_tec">
        <id
                column="cd_elo_req_vigi_resp_tec"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="sequence">
                <param name="sequence">seq_elo_req_vigi_resp_tec</param>
            </generator>
        </id>
        <version column="version" name="version" type="long"/>

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia"
                column="cd_req_vigilancia"
                not-null="true"
                name="requerimentoVigilancia"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnico"
                column="cd_resp_tecnico"
                not-null="true"
                name="responsavelTecnico"
        />

    </class>
</hibernate-mapping>
