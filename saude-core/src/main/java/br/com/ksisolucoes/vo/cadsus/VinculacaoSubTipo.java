package br.com.ksisolucoes.vo.cadsus;

import java.io.Serializable;

import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.vo.cadsus.base.BaseVinculacaoSubTipo;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;



public class VinculacaoSubTipo extends BaseVinculacaoSubTipo implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public VinculacaoSubTipo () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public VinculacaoSubTipo (br.com.ksisolucoes.vo.cadsus.VinculacaoSubTipoPK id) {
		super(id);
	}

	/**
	 * Constructor for required fields
	 */
	public VinculacaoSubTipo (
		br.com.ksisolucoes.vo.cadsus.VinculacaoSubTipoPK id,
		java.lang.String descricao) {

		super (
			id,
			descricao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setId( (br.com.ksisolucoes.vo.cadsus.VinculacaoSubTipoPK)key );
    }

    public Serializable getCodigoManager() {
        return this.getId();
    }

    public String getDescricaoVO() {
        return this.getDescricao();
    }

    public String getIdentificador() {
        return this.getId().getCodigo();
    }

    public String getDescricaoFormatado() {
        return Util.getDescricaoFormatado( getId().getCodigo(), Coalesce.asString(this.getDescricao() ) );
    }
}