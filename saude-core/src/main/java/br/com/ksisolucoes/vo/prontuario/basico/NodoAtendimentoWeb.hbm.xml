<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="NodoAtendimentoWeb" table="nodo_atendimento_web" >
    
        <id
            column="cd_nodo_atendimento"
            name="codigo"
            type="java.lang.Long"
            >
            <generator class="assigned" />
        </id> <version column="version" name="version" type="long" />   
                 
         <many-to-one  
            class="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"
            name="tipoAtendimento"
            column="cd_tp_atendimento"
        />

        <property
            name="classeNodo"
            column="classe_nodo"
            type="java.lang.String"
         /> 
  
        <property
            name="ordem"
            column="ordem"
            type="java.lang.Long"
         />

		<property
            name="apenasMunicipio"
            column="apenas_municipio"
            type="java.lang.Long"
            not-null="true"
         />

		<property
            name="sexo"
            column="sexo"
            type="java.lang.String"
            not-null="true"
         />

        <property
                name="permiteHistorico"
                column="flag_permite_historico_geral"
                type="java.lang.Long"
                not-null="true"
        />

		<property
            name="visivelUsuariosTemporarios"
            column="visivel_usuarios_temp"
            type="java.lang.String"
            not-null="true"
         />

        <property
         	name="permiteCorrecao"
         	column="permite_correcao"
         	type="java.lang.Long"
         />

        <property
         	name="exibirAtendimento"
         	column="exibir_atendimento"
         	type="java.lang.Long"
         />

        <property
         	name="permiteAtendimentoParalelo"
         	column="permite_atend_paralelo"
         	type="java.lang.Long"
         />
        
         <property
             column="flag_informar_profissional"
             name="informarProfissional"
             type="java.lang.Long"
         /> 
         
         <property
             column="flag_informar_estab_origem"
             name="informarEstabelecimentoOrigem"
             type="java.lang.Long"
         /> 
                
         <many-to-one  
            class="br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo"
            name="grupoAtendimentoCbo"
            column="cd_grupo_atend_cbo"
        />

        <property
                column="flag_permite_reclassificacao"
                name="flagPermiteReclassificacao"
                not-null="true"
                type="java.lang.Long"
        />
    </class>
</hibernate-mapping>
