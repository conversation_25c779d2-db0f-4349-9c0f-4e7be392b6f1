package br.com.ksisolucoes.vo.materiais.bnafar.estoque.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the bnafar_pos_estoque table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="bnafar_pos_estoque"
 */

public abstract class BaseBnafarPosEstoque extends BaseRootVO implements Serializable {

	public static String REF = "BnafarPosEstoque";
	public static final String PROP_DATA_VALIDADE = "dataValidade";
	public static final String PROP_DATA_ULTIMO_ENVIO = "dataUltimoEnvio";
	public static final String PROP_TIPO_ESTABELECIMENTO = "tipoEstabelecimento";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_FABRICANTE = "fabricante";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_GRUPO_ESTOQUE = "grupoEstoque";
	public static final String PROP_BNAFAR_POS_ESTOQUE_ELO = "bnafarPosEstoqueElo";
	public static final String PROP_ORIGEM = "origem";
	public static final String PROP_QUANTIDADE = "quantidade";
	public static final String PROP_DATA_POS_ESTOQUE = "dataPosEstoque";
	public static final String PROP_PRODUTO_ORIGEM = "produtoOrigem";
	public static final String PROP_STATUS_REGISTRO = "statusRegistro";
	public static final String PROP_CNES = "cnes";


	// constructors
	public BaseBnafarPosEstoque () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseBnafarPosEstoque (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseBnafarPosEstoque (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.entradas.estoque.Produto produtoOrigem,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.String cnes,
		java.lang.String tipoEstabelecimento,
		java.lang.Long origem,
		java.util.Date dataPosEstoque,
		java.lang.String grupoEstoque,
		java.util.Date dataValidade,
		java.lang.Double quantidade,
		java.lang.Long statusRegistro,
		java.util.Date dataCadastro,
		java.util.Date dataUltimoEnvio) {

		this.setCodigo(codigo);
		this.setProdutoOrigem(produtoOrigem);
		this.setUsuario(usuario);
		this.setCnes(cnes);
		this.setTipoEstabelecimento(tipoEstabelecimento);
		this.setOrigem(origem);
		this.setDataPosEstoque(dataPosEstoque);
		this.setGrupoEstoque(grupoEstoque);
		this.setDataValidade(dataValidade);
		this.setQuantidade(quantidade);
		this.setStatusRegistro(statusRegistro);
		this.setDataCadastro(dataCadastro);
		this.setDataUltimoEnvio(dataUltimoEnvio);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String cnes;
	private java.lang.String tipoEstabelecimento;
	private java.lang.Long origem;
	private java.util.Date dataPosEstoque;
	private java.lang.String grupoEstoque;
	private java.util.Date dataValidade;
	private java.lang.Double quantidade;
	private java.lang.Long statusRegistro;
	private java.util.Date dataCadastro;
	private java.util.Date dataUltimoEnvio;

	// many to one
	private br.com.ksisolucoes.vo.entradas.estoque.Produto produtoOrigem;
	private br.com.ksisolucoes.vo.entradas.estoque.Fabricante fabricante;
	private br.com.ksisolucoes.vo.materiais.bnafar.estoque.BnafarPosEstoqueElo bnafarPosEstoqueElo;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_bnafar_pos_estoque"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cnes
	 */
	public java.lang.String getCnes () {
		return getPropertyValue(this, cnes, PROP_CNES); 
	}

	/**
	 * Set the value related to the column: cnes
	 * @param cnes the cnes value
	 */
	public void setCnes (java.lang.String cnes) {
//        java.lang.String cnesOld = this.cnes;
		this.cnes = cnes;
//        this.getPropertyChangeSupport().firePropertyChange ("cnes", cnesOld, cnes);
	}



	/**
	 * Return the value associated with the column: tp_estabelecimento
	 */
	public java.lang.String getTipoEstabelecimento () {
		return getPropertyValue(this, tipoEstabelecimento, PROP_TIPO_ESTABELECIMENTO); 
	}

	/**
	 * Set the value related to the column: tp_estabelecimento
	 * @param tipoEstabelecimento the tp_estabelecimento value
	 */
	public void setTipoEstabelecimento (java.lang.String tipoEstabelecimento) {
//        java.lang.String tipoEstabelecimentoOld = this.tipoEstabelecimento;
		this.tipoEstabelecimento = tipoEstabelecimento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoEstabelecimento", tipoEstabelecimentoOld, tipoEstabelecimento);
	}



	/**
	 * Return the value associated with the column: cd_origem
	 */
	public java.lang.Long getOrigem () {
		return getPropertyValue(this, origem, PROP_ORIGEM); 
	}

	/**
	 * Set the value related to the column: cd_origem
	 * @param origem the cd_origem value
	 */
	public void setOrigem (java.lang.Long origem) {
//        java.lang.Long origemOld = this.origem;
		this.origem = origem;
//        this.getPropertyChangeSupport().firePropertyChange ("origem", origemOld, origem);
	}



	/**
	 * Return the value associated with the column: dt_pos_estoque
	 */
	public java.util.Date getDataPosEstoque () {
		return getPropertyValue(this, dataPosEstoque, PROP_DATA_POS_ESTOQUE); 
	}

	/**
	 * Set the value related to the column: dt_pos_estoque
	 * @param dataPosEstoque the dt_pos_estoque value
	 */
	public void setDataPosEstoque (java.util.Date dataPosEstoque) {
//        java.util.Date dataPosEstoqueOld = this.dataPosEstoque;
		this.dataPosEstoque = dataPosEstoque;
//        this.getPropertyChangeSupport().firePropertyChange ("dataPosEstoque", dataPosEstoqueOld, dataPosEstoque);
	}



	/**
	 * Return the value associated with the column: grupo_estoque
	 */
	public java.lang.String getGrupoEstoque () {
		return getPropertyValue(this, grupoEstoque, PROP_GRUPO_ESTOQUE); 
	}

	/**
	 * Set the value related to the column: grupo_estoque
	 * @param grupoEstoque the grupo_estoque value
	 */
	public void setGrupoEstoque (java.lang.String grupoEstoque) {
//        java.lang.String grupoEstoqueOld = this.grupoEstoque;
		this.grupoEstoque = grupoEstoque;
//        this.getPropertyChangeSupport().firePropertyChange ("grupoEstoque", grupoEstoqueOld, grupoEstoque);
	}



	/**
	 * Return the value associated with the column: dt_validade
	 */
	public java.util.Date getDataValidade () {
		return getPropertyValue(this, dataValidade, PROP_DATA_VALIDADE); 
	}

	/**
	 * Set the value related to the column: dt_validade
	 * @param dataValidade the dt_validade value
	 */
	public void setDataValidade (java.util.Date dataValidade) {
//        java.util.Date dataValidadeOld = this.dataValidade;
		this.dataValidade = dataValidade;
//        this.getPropertyChangeSupport().firePropertyChange ("dataValidade", dataValidadeOld, dataValidade);
	}



	/**
	 * Return the value associated with the column: quantidade
	 */
	public java.lang.Double getQuantidade () {
		return getPropertyValue(this, quantidade, PROP_QUANTIDADE); 
	}

	/**
	 * Set the value related to the column: quantidade
	 * @param quantidade the quantidade value
	 */
	public void setQuantidade (java.lang.Double quantidade) {
//        java.lang.Double quantidadeOld = this.quantidade;
		this.quantidade = quantidade;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidade", quantidadeOld, quantidade);
	}



	/**
	 * Return the value associated with the column: flag_status_registro
	 */
	public java.lang.Long getStatusRegistro () {
		return getPropertyValue(this, statusRegistro, PROP_STATUS_REGISTRO); 
	}

	/**
	 * Set the value related to the column: flag_status_registro
	 * @param statusRegistro the flag_status_registro value
	 */
	public void setStatusRegistro (java.lang.Long statusRegistro) {
//        java.lang.Long statusRegistroOld = this.statusRegistro;
		this.statusRegistro = statusRegistro;
//        this.getPropertyChangeSupport().firePropertyChange ("statusRegistro", statusRegistroOld, statusRegistro);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_ultimo_envio
	 */
	public java.util.Date getDataUltimoEnvio () {
		return getPropertyValue(this, dataUltimoEnvio, PROP_DATA_ULTIMO_ENVIO); 
	}

	/**
	 * Set the value related to the column: dt_ultimo_envio
	 * @param dataUltimoEnvio the dt_ultimo_envio value
	 */
	public void setDataUltimoEnvio (java.util.Date dataUltimoEnvio) {
//        java.util.Date dataUltimoEnvioOld = this.dataUltimoEnvio;
		this.dataUltimoEnvio = dataUltimoEnvio;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUltimoEnvio", dataUltimoEnvioOld, dataUltimoEnvio);
	}



	/**
	 * Return the value associated with the column: cd_produto_origem
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Produto getProdutoOrigem () {
		return getPropertyValue(this, produtoOrigem, PROP_PRODUTO_ORIGEM); 
	}

	/**
	 * Set the value related to the column: cd_produto_origem
	 * @param produtoOrigem the cd_produto_origem value
	 */
	public void setProdutoOrigem (br.com.ksisolucoes.vo.entradas.estoque.Produto produtoOrigem) {
//        br.com.ksisolucoes.vo.entradas.estoque.Produto produtoOrigemOld = this.produtoOrigem;
		this.produtoOrigem = produtoOrigem;
//        this.getPropertyChangeSupport().firePropertyChange ("produtoOrigem", produtoOrigemOld, produtoOrigem);
	}



	/**
	 * Return the value associated with the column: cd_fabricante
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Fabricante getFabricante () {
		return getPropertyValue(this, fabricante, PROP_FABRICANTE); 
	}

	/**
	 * Set the value related to the column: cd_fabricante
	 * @param fabricante the cd_fabricante value
	 */
	public void setFabricante (br.com.ksisolucoes.vo.entradas.estoque.Fabricante fabricante) {
//        br.com.ksisolucoes.vo.entradas.estoque.Fabricante fabricanteOld = this.fabricante;
		this.fabricante = fabricante;
//        this.getPropertyChangeSupport().firePropertyChange ("fabricante", fabricanteOld, fabricante);
	}



	/**
	 * Return the value associated with the column: cd_bnafar_pos_estoque_elo
	 */
	public br.com.ksisolucoes.vo.materiais.bnafar.estoque.BnafarPosEstoqueElo getBnafarPosEstoqueElo () {
		return getPropertyValue(this, bnafarPosEstoqueElo, PROP_BNAFAR_POS_ESTOQUE_ELO); 
	}

	/**
	 * Set the value related to the column: cd_bnafar_pos_estoque_elo
	 * @param bnafarPosEstoqueElo the cd_bnafar_pos_estoque_elo value
	 */
	public void setBnafarPosEstoqueElo (br.com.ksisolucoes.vo.materiais.bnafar.estoque.BnafarPosEstoqueElo bnafarPosEstoqueElo) {
//        br.com.ksisolucoes.vo.materiais.bnafar.estoque.BnafarPosEstoqueElo bnafarPosEstoqueEloOld = this.bnafarPosEstoqueElo;
		this.bnafarPosEstoqueElo = bnafarPosEstoqueElo;
//        this.getPropertyChangeSupport().firePropertyChange ("bnafarPosEstoqueElo", bnafarPosEstoqueEloOld, bnafarPosEstoqueElo);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.materiais.bnafar.estoque.BnafarPosEstoque)) return false;
		else {
			br.com.ksisolucoes.vo.materiais.bnafar.estoque.BnafarPosEstoque bnafarPosEstoque = (br.com.ksisolucoes.vo.materiais.bnafar.estoque.BnafarPosEstoque) obj;
			if (null == this.getCodigo() || null == bnafarPosEstoque.getCodigo()) return false;
			else return (this.getCodigo().equals(bnafarPosEstoque.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}