<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.investigacao">

    <class name="InvestigacaoAgravoAcidenteAnimalPeconhento" table="investigacao_agr_acidente_animal_peconhento">

        <id name="codigo"
            type="java.lang.Long"
            column="cd_invest_agr_acidente_animal_peconhento" >
            <generator class="sequence">
                <param name="sequence">seq_investigacao_agr_acidente_animal_peconhento</param>
            </generator>
        </id>

        <version column="version" name="version" type="long"/>

        <property
            name="flagInformacoesComplementares"
            column="flag_informacoes_complementares"
            type="java.lang.String"
            not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo"
                name="registroAgravo"
                not-null="true">
            <column name="cd_registro_agravo"/>
        </many-to-one>

        <property
                name="dataInvestigacao"
                column="dt_investigacao"
                type="java.util.Date"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"
                name="ocupacaoCbo" not-null="false">
            <column name="ocupacao_cbo"/>
        </many-to-one>

        <!-- CASO Autoctone -->
        <property
                name="casoAutoctone"
                column="caso_autoctone"
                type="java.lang.Long"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Cidade"
                name="cidadeLocalAcidente" not-null="false">
            <column name="cd_cidade_acidente"/>
        </many-to-one>
        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Pais"
                name="paisLocalAcidente" not-null="false">
            <column name="cd_pais_acidente"/>
        </many-to-one>
        <property
                name="distritoLocalAcidente"
                column="str_distrito_acidente"
                type="java.lang.String"
        />
        <property
                name="bairroLocalAcidente"
                column="str_bairro_acidente"
                type="java.lang.String"
        />

        <!-- OBS -->
        <property
            name="observacao"
            column="observacao"
            type="java.lang.String"
        />

        <!-- encerramento -->
        <property
            name="dataEncerramento"
            column="dt_encerramento"
            type="java.util.Date"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                name="usuarioEncerramento" not-null="false">
            <column name="cd_usuario_encerramento"/>
        </many-to-one>


        <!-- antecedentes epidemiologicos -->
        <property
                name="dataAcidente"
                column="data_acidente"
                type="java.util.Date"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Cidade"
                name="cidadeLocalOcorrencia" not-null="false">
            <column name="cidade_ocorrencia"/>
        </many-to-one>

        <property
                name="localidadeOcorrencia"
                column="str_localidade_ocorrencia"
                type="java.lang.String"
        />

        <property
                name="zonaOcorrencia"
                column="zona_ocorrencia"
                type="java.lang.Long"
        />
        <property
                name="tempoDecorridoPicadaAtendimento"
                column="tempo_decorrido_picada_atendimento"
                type="java.lang.Long"
        />
        <property
                name="localPicadaCorpo"
                column="local_picada_corpo"
                type="java.lang.Long"
        />


        <!-- Dados clinicos -->
        <!-- Tipo Manifestação local -->
        <property
                name="manifestacaoLocal"
                column="manifestacoes_locais"
                type="java.lang.Long"
        />
        <property
                name="tipoManifestacaoLocalDor"
                column="tp_man_local_dor"
                type="java.lang.Long"
        />
        <property
                name="tipoManifestacaoLocalEdema"
                column="tp_man_local_edema"
                type="java.lang.Long"
        />
        <property
                name="tipoManifestacaoLocalEquimose"
                column="tp_man_local_equimose"
                type="java.lang.Long"
        />
        <property
                name="tipoManifestacaoLocalNecrose"
                column="tp_man_local_necrose"
                type="java.lang.Long"
        />
        <property
                name="tipoManifestacaoLocalOutros"
                column="tp_man_local_outros"
                type="java.lang.Long"
        />
        <property
                name="tipoManifestacaoLocalOutrosStr"
                column="tp_man_local_outros_str"
                type="java.lang.String"
        />

        <!-- Manifestações sistêmicas -->
        <property
                name="manifestacaoSistemica"
                column="manifestacoes_sistemicas"
                type="java.lang.Long"
        />
        <property
                name="tipoManifestacaoSistemicaNeuroparalitica"
                column="tp_man_sistemicas_neuroparalitica"
                type="java.lang.Long"
        />
        <property
                name="tipoManifestacaoSistemicaHemorragica"
                column="tp_man_sistemicas_hemorragica"
                type="java.lang.Long"
        />
        <property
                name="tipoManifestacaoSistemicaVagais"
                column="tp_man_sistemicas_vagais"
                type="java.lang.Long"
        />
        <property
                name="tipoManifestacaoSistemicaHemolitica"
                column="tp_man_sistemicas_miolitica_hemolitica"
                type="java.lang.Long"
        />
        <property
                name="tipoManifestacaoSistemicaRenais"
                column="tp_man_sistemicas_renais"
                type="java.lang.Long"
        />
        <property
                name="tipoManifestacaoSistemicaOutros"
                column="tp_man_sistemicas_outros"
                type="java.lang.Long"
        />
        <property
                name="tipoManifestacaoSistemicaOutrosStr"
                column="tp_man_sistemicas_outros_str"
                type="java.lang.String"
        />
        <property
                name="tempoCoagulacao"
                column="tempo_coagulacao"
                type="java.lang.Long"
        />


        <!-- Dados Acidente -->
        <property
                name="tipoAcidente"
                column="tipo_acidente"
                type="java.lang.Long"
        />
        <property
                name="tipoAcidenteOutrosStr"
                column="tp_acidente_outros_str"
                type="java.lang.String"
        />
        <property
                name="tipoAcidenteSerpente"
                column="tp_acidente_serpente"
                type="java.lang.Long"
        />
        <property
                name="tipoAcidenteAranha"
                column="tp_acidente_aranha"
                type="java.lang.Long"
        />
        <property
                name="tipoAcidenteLagarta"
                column="tp_acidente_lagarta"
                type="java.lang.Long"
        />



        <!-- Tratamento -->
        <property
                name="classificacaoCaso"
                column="classificacao_caso"
                type="java.lang.Long"
        />
        <property
                name="soroterapia"
                column="soroterapia"
                type="java.lang.Long"
        />
        <property
                name="nrAmpolasAntibotropico"
                column="nr_ampolas_antibotropico"
                type="java.lang.Long"
        />
        <property
                name="nrAmpolasAntibotropicoLaquetico"
                column="nr_ampolas_antibotropico_laquetico"
                type="java.lang.Long"
        />
        <property
                name="nrAmpolasAntibotropicoCrolatico"
                column="nr_ampolas_antibotropico_crolatico"
                type="java.lang.Long"
        />
        <property
                name="nrAmpolasAnticrolatico"
                column="nr_ampolas_anticrolatico"
                type="java.lang.Long"
        />
        <property
                name="nrAmpolasAntielapidico"
                column="nr_ampolas_antielapidico"
                type="java.lang.Long"
        />
        <property
                name="nrAmpolasAntiescorpianico"
                column="nr_ampolas_antiescorpianico"
                type="java.lang.Long"
        />
        <property
                name="nrAmpolasAntiaracnidico"
                column="nr_ampolas_antiaracnidico"
                type="java.lang.Long"
        />
        <property
                name="nrAmpolasAntilixiscelico"
                column="nr_ampolas_antilixiscelico"
                type="java.lang.Long"
        />
        <property
                name="nrAmpolasAntilonomico"
                column="nr_ampolas_antilonomico"
                type="java.lang.Long"
        />

        <!-- Complicacoes Locais -->
        <property
                name="complicoesLocais"
                column="complicacoes_locais"
                type="java.lang.Long"
        />
        <property
                name="tipoComplicacaoLocalInfeccaoSecundaria"
                column="tp_comp_local_infeccao_secundaria"
                type="java.lang.Long"
        />
        <property
                name="tipoComplicacaoLocalNecroseExtensa"
                column="tp_comp_local_necrose_extensa"
                type="java.lang.Long"
        />
        <property
                name="tipoComplicacaoLocalSindromeCompartimental"
                column="tp_comp_local_sindrome_compartimental"
                type="java.lang.Long"
        />
        <property
                name="tipoComplicacaoLocalDeficitFuncional"
                column="tp_comp_local_deficit_funcional"
                type="java.lang.Long"
        />
        <property
                name="tipoComplicacaoLocalAmputacao"
                column="tp_comp_local_amputacao"
                type="java.lang.Long"
        />

        <!-- Complicacoes Sistêmicas -->
        <property
                name="complicacoesSistemicas"
                column="complicacoes_sistemicas"
                type="java.lang.Long"
        />
        <property
                name="tipoComplicacaoSistemicaInsuficienciaRenal"
                column="tp_comp_sistemica_insuficiencia_renal"
                type="java.lang.Long"
        />
        <property
                name="tipoComplicacaoSistemicaInsuficienciaRespiratoria"
                column="tp_comp_sistemica_insuficiencia_respiratoria"
                type="java.lang.Long"
        />
        <property
                name="tipoComplicacaoSistemicaSepticemia"
                column="tp_comp_sistemica_septicemia"
                type="java.lang.Long"
        />
        <property
                name="tipoComplicacaoSistemicaChoque"
                column="tp_comp_sistemica_choque"
                type="java.lang.Long"
        />

        <!-- conclusao -->
        <property
                name="acidenteRelacionadoTrabalho"
                column="acidente_relacionado_trabalho"
                type="java.lang.Long"
        />
        <property
                name="evolucaoCaso"
                column="evolucao_caso"
                type="java.lang.Long"
        />
        <property
                name="dataObito"
                column="data_obito"
                type="java.util.Date"
        />

    </class>
</hibernate-mapping>