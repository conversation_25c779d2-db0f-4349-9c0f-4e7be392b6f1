<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario"  >
    <class name="TipoSintoma" table="tipo_sintoma">
        <id
            column="cd_tipo_sintoma"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />

        <property
            column="armazena_sugestao"
            name="armazenaSugestao"
            not-null="true"
            type="java.lang.Long"
		 />        
        
        <property
            column="ds_tipo_sintoma"
            name="descricao"
            not-null="true"
            type="java.lang.String"
		 />        
        
        <property
            column="dica_resposta"
            name="dicaResposta"
            type="java.lang.String"
		 />        
    </class>
</hibernate-mapping>
