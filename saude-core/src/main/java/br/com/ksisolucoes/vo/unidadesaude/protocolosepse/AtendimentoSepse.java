package br.com.ksisolucoes.vo.unidadesaude.protocolosepse;

import java.io.Serializable;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.unidadesaude.protocolosepse.base.BaseAtendimentoSepse;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class AtendimentoSepse extends BaseAtendimentoSepse implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public AtendimentoSepse () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public AtendimentoSepse (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public AtendimentoSepse (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {

		super (
			codigo,
			atendimento);
	}

/*[CONSTRUCTOR MARKER END]*/

	public enum HipoteseSepse implements IEnum {
		DESCARTADA(0l, Bundle.getStringApplication("rotulo_descartada")),
		MANTIDA(1l, Bundle.getStringApplication("rotulo_mantida"));

		private Long value;
		private String descricao;

		HipoteseSepse(Long value,String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}
	}

	public enum NewsFinal implements IEnum {
		RISCO_BAIXO(0l, Bundle.getStringApplication("rotulo_risco_baixo_sepse")),
		RISCO_MEDIO(1l, Bundle.getStringApplication("rotulo_risco_medio_sepse")),
		RISCO_ALTO(2l, Bundle.getStringApplication("rotulo_risco_alto_sepse"));

		private Long value;
		private String descricao;

		NewsFinal(Long value,String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}
	}

	public enum Desfecho implements IEnum {
		ALTA_MELHORADA(0l, Bundle.getStringApplication("rotulo_alta_melhorada")),
		OBSERV_NO_EIXO(1l, Bundle.getStringApplication("rotulo_observ_no_eixo")),
		EVASAO(2l, Bundle.getStringApplication("rotulo_evasao")),
		OBITO(3l, Bundle.getStringApplication("rotulo_obito"));

		private Long value;
		private String descricao;

		Desfecho(Long value,String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}
	}

	public enum Status implements IEnum {
		EM_ANDAMENTO(0L, Bundle.getStringApplication("rotulo_em_andamento")),
		CONCLUIDO(1L, Bundle.getStringApplication("rotulo_concluido")),
		CANCELADO(2L, Bundle.getStringApplication("rotulo_cancelado"));

		private Long value;
		private String descricao;

		Status(Long value,String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}
	}

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}