package br.com.ksisolucoes.vo.vigilancia.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the talonario_receita_a table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="talonario_receita_a"
 */

public abstract class BaseTalonarioReceitaA extends BaseRootVO implements Serializable {

	public static String REF = "TalonarioReceitaA";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_STATUS = "status";
	public static final String PROP_NUMERACAO_INICIAL = "numeracaoInicial";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_ESTABELECIMENTO = "estabelecimento";
	public static final String PROP_VIGILANCIA_PROFISSIONAL = "vigilanciaProfissional";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_DATA_ENTRADA = "dataEntrada";
	public static final String PROP_NUMERACAO_FINAL = "numeracaoFinal";


	// constructors
	public BaseTalonarioReceitaA () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseTalonarioReceitaA (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseTalonarioReceitaA (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento,
		java.util.Date dataEntrada,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setUsuario(usuario);
		this.setEstabelecimento(estabelecimento);
		this.setDataEntrada(dataEntrada);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long numeracaoInicial;
	private java.lang.Long numeracaoFinal;
	private java.util.Date dataEntrada;
	private java.util.Date dataCadastro;
	private java.lang.Long status;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissional vigilanciaProfissional;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_talonario_receita_a"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nro_inicial
	 */
	public java.lang.Long getNumeracaoInicial () {
		return getPropertyValue(this, numeracaoInicial, PROP_NUMERACAO_INICIAL); 
	}

	/**
	 * Set the value related to the column: nro_inicial
	 * @param numeracaoInicial the nro_inicial value
	 */
	public void setNumeracaoInicial (java.lang.Long numeracaoInicial) {
//        java.lang.Long numeracaoInicialOld = this.numeracaoInicial;
		this.numeracaoInicial = numeracaoInicial;
//        this.getPropertyChangeSupport().firePropertyChange ("numeracaoInicial", numeracaoInicialOld, numeracaoInicial);
	}



	/**
	 * Return the value associated with the column: nro_final
	 */
	public java.lang.Long getNumeracaoFinal () {
		return getPropertyValue(this, numeracaoFinal, PROP_NUMERACAO_FINAL); 
	}

	/**
	 * Set the value related to the column: nro_final
	 * @param numeracaoFinal the nro_final value
	 */
	public void setNumeracaoFinal (java.lang.Long numeracaoFinal) {
//        java.lang.Long numeracaoFinalOld = this.numeracaoFinal;
		this.numeracaoFinal = numeracaoFinal;
//        this.getPropertyChangeSupport().firePropertyChange ("numeracaoFinal", numeracaoFinalOld, numeracaoFinal);
	}



	/**
	 * Return the value associated with the column: dt_entrada
	 */
	public java.util.Date getDataEntrada () {
		return getPropertyValue(this, dataEntrada, PROP_DATA_ENTRADA); 
	}

	/**
	 * Set the value related to the column: dt_entrada
	 * @param dataEntrada the dt_entrada value
	 */
	public void setDataEntrada (java.util.Date dataEntrada) {
//        java.util.Date dataEntradaOld = this.dataEntrada;
		this.dataEntrada = dataEntrada;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEntrada", dataEntradaOld, dataEntrada);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: cd_vigilancia_profissional
	 */
	public br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissional getVigilanciaProfissional () {
		return getPropertyValue(this, vigilanciaProfissional, PROP_VIGILANCIA_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_vigilancia_profissional
	 * @param vigilanciaProfissional the cd_vigilancia_profissional value
	 */
	public void setVigilanciaProfissional (br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissional vigilanciaProfissional) {
//        br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissional vigilanciaProfissionalOld = this.vigilanciaProfissional;
		this.vigilanciaProfissional = vigilanciaProfissional;
//        this.getPropertyChangeSupport().firePropertyChange ("vigilanciaProfissional", vigilanciaProfissionalOld, vigilanciaProfissional);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_estabelecimento
	 */
	public br.com.ksisolucoes.vo.vigilancia.Estabelecimento getEstabelecimento () {
		return getPropertyValue(this, estabelecimento, PROP_ESTABELECIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_estabelecimento
	 * @param estabelecimento the cd_estabelecimento value
	 */
	public void setEstabelecimento (br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento) {
//        br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimentoOld = this.estabelecimento;
		this.estabelecimento = estabelecimento;
//        this.getPropertyChangeSupport().firePropertyChange ("estabelecimento", estabelecimentoOld, estabelecimento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.TalonarioReceitaA)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.TalonarioReceitaA talonarioReceitaA = (br.com.ksisolucoes.vo.vigilancia.TalonarioReceitaA) obj;
			if (null == this.getCodigo() || null == talonarioReceitaA.getCodigo()) return false;
			else return (this.getCodigo().equals(talonarioReceitaA.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}