package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the investigacao_agr_microcefalia table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_microcefalia"
 */

public abstract class BaseInvestigacaoAgravoMicrocefalia extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoMicrocefalia";
	public static final String PROP_OBITO_NEONATAL_PRECOCE = "obitoNeonatalPrecoce";
	public static final String PROP_MAE_RESULTADO_SIFILIS = "maeResultadoSifilis";
	public static final String PROP_MICROCEFALIA_APENAS = "microcefaliaApenas";
	public static final String PROP_FILHO_RESULTADO_HERPES = "filhoResultadoHerpes";
	public static final String PROP_MAE_RESULTADO_IGG_ZIKA = "maeResultadoIggZika";
	public static final String PROP_TIPO_ALTERACAO_CONGENITA_IGNORADO = "tipoAlteracaoCongenitaIgnorado";
	public static final String PROP_MICROCEFALIA_ALTERACAO_CONGENITA = "microcefaliaAlteracaoCongenita";
	public static final String PROP_FILHO_RESULTADO_SIFILIS = "filhoResultadoSifilis";
	public static final String PROP_DATA_EXAME_TOMOGRAFIA_COMPUTADORIZADA = "dataExameTomografiaComputadorizada";
	public static final String PROP_DEFICIENCIA_NEUROLOGICA = "deficienciaNeurologica";
	public static final String PROP_DESCRICAO_EXAME_TOMOGRAFIA_COMPUTADORIZADA = "descricaoExameTomografiaComputadorizada";
	public static final String PROP_DESCRICAO_EXAME_ULTRASSONOGRAFIA_TRANSFONTANELA = "descricaoExameUltrassonografiaTransfontanela";
	public static final String PROP_FILHO_RESULTADO_TESTE_RAPIDO_IGG_ZIKA = "filhoResultadoTesteRapidoIggZika";
	public static final String PROP_NUMERO_DECLARACAO_NASCIDO_VIVO = "numeroDeclaracaoNascidoVivo";
	public static final String PROP_DETECCAO_ALTERACAO_CONGENITA = "deteccaoAlteracaoCongenita";
	public static final String PROP_MAE_RESULTADO_TESTE_RAPIDO_IGM_ZIKA = "maeResultadoTesteRapidoIgmZika";
	public static final String PROP_CIRCUNFERENCIA_CRANIANA = "circunferenciaCraniana";
	public static final String PROP_EXAME_TOMOGRAFIA_COMPUTADORIZADA = "exameTomografiaComputadorizada";
	public static final String PROP_DATA_OBITO = "dataObito";
	public static final String PROP_MAE_RESULTADO_TOXOPLASMOSE = "maeResultadoToxoplasmose";
	public static final String PROP_MAE_DATA_PROVAVEL_INICIO_SINTOMAS = "maeDataProvavelInicioSintomas";
	public static final String PROP_FILHO_RESULTADO_IGG_ZIKA = "filhoResultadoIggZika";
	public static final String PROP_FILHO_RESULTADO_TESTE_RAPIDO_IGM_ZIKA = "filhoResultadoTesteRapidoIgmZika";
	public static final String PROP_PESO_RECEM_NASCIDO = "pesoRecemNascido";
	public static final String PROP_DEFICIENCIA_VISUAL = "deficienciaVisual";
	public static final String PROP_MAE_SINAL_SINTOMA_DOR_ARTICULACAO = "maeSinalSintomaDorArticulacao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_NUMERO_DECLARACAO_OBITO = "numeroDeclaracaoObito";
	public static final String PROP_MAE_SINAL_SINTOMA_HIPERTROFIA_GANGLIONAR = "maeSinalSintomaHipertrofiaGanglionar";
	public static final String PROP_FILHO_RESULTADO_CITOMEGALOVIRUS = "filhoResultadoCitomegalovirus";
	public static final String PROP_DATA_MEDICAO_PERIMETRO_CEFALICO = "dataMedicaoPerimetroCefalico";
	public static final String PROP_MAE_EXAME_CITOMEGALOVIRUS = "maeExameCitomegalovirus";
	public static final String PROP_MAE_HISTORICO_MALFORMACAO_CONGENITA = "maeHistoricoMalformacaoCongenita";
	public static final String PROP_FILHO_RESULTADO_TOXOPLASMOSE = "filhoResultadoToxoplasmose";
	public static final String PROP_DATA_INVESTIGACAO = "dataInvestigacao";
	public static final String PROP_MAE_RESULTADO_TESTE_RAPIDO_IGG_ZIKA = "maeResultadoTesteRapidoIggZika";
	public static final String PROP_FILHO_RESULTADO_PCR_ZIKA = "filhoResultadoPcrZika";
	public static final String PROP_DESCRICAO_EXAME_RESSONANACIA_MAGNETICA = "descricaoExameRessonanaciaMagnetica";
	public static final String PROP_MAE_REALIZOU_EXAME_LABORATORIAL = "maeRealizouExameLaboratorial";
	public static final String PROP_USUARIO_ENCERRAMENTO = "usuarioEncerramento";
	public static final String PROP_EXAME_RESSONANCIA_MAGNETICA = "exameRessonanciaMagnetica";
	public static final String PROP_MAE_RESULTADO_PCR_ZIKA = "maeResultadoPcrZika";
	public static final String PROP_FLAG_INFORMACOES_COMPLEMENTARES = "flagInformacoesComplementares";
	public static final String PROP_IDADE_GESTACIONAL_IDENTIFICACAO = "idadeGestacionalIdentificacao";
	public static final String PROP_DEFICIENCIA_AUDITIVA = "deficienciaAuditiva";
	public static final String PROP_DESCRICAO_EXAME_ULTRASSONOGRAFIA = "descricaoExameUltrassonografia";
	public static final String PROP_EXAME_ULTRASSONOGRAFIA = "exameUltrassonografia";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_MAE_SINAL_SINTOMA_ACOMETIMENTO_NEUROLOGICO = "maeSinalSintomaAcometimentoNeurologico";
	public static final String PROP_OBITO = "obito";
	public static final String PROP_MAE_SINAL_SINTOMA_DOR_MUSCULAR = "maeSinalSintomaDorMuscular";
	public static final String PROP_ESTABELECIMENTO_SAUDE_PARTO = "estabelecimentoSaudeParto";
	public static final String PROP_MAE_EXANTEMA_DURANTE_GESTACAO = "maeExantemaDuranteGestacao";
	public static final String PROP_MAE_SINAL_SINTOMA_HIPEREMIA_CONJUNTIVAL = "maeSinalSintomaHiperemiaConjuntival";
	public static final String PROP_DATA_EXAME_ULTRASSONOGRAFIA = "dataExameUltrassonografia";
	public static final String PROP_MICROCEFALIA_ALTERACAO_S_N_C = "microcefaliaAlteracaoSNC";
	public static final String PROP_NOME_RECEM_NASCIDO = "nomeRecemNascido";
	public static final String PROP_CLASSIFICACAO_NASCIDO_VIVO_NATIMORTO = "classificacaoNascidoVivoNatimorto";
	public static final String PROP_MAE_SINAL_SINTOMA_EDEMA_ARTICULACAO = "maeSinalSintomaEdemaArticulacao";
	public static final String PROP_MAE_RESULTADO_CITOMEGALOVIRUS = "maeResultadoCitomegalovirus";
	public static final String PROP_MAE_RESULTADO_IGM_ZIKA = "maeResultadoIgmZika";
	public static final String PROP_FILHO_EXAME_LABORATORIAL = "filhoExameLaboratorial";
	public static final String PROP_REGISTRO_AGRAVO = "registroAgravo";
	public static final String PROP_MAE_FEBRE_DURANTE_GESTACAO = "maeFebreDuranteGestacao";
	public static final String PROP_COMPRIMENTO_RECEM_NASCIDO = "comprimentoRecemNascido";
	public static final String PROP_TIPO_INVESTIGACAO = "tipoInvestigacao";
	public static final String PROP_DATA_EXAME_RESSONANCIA_MAGNETICA = "dataExameRessonanciaMagnetica";
	public static final String PROP_ALTERACOES_CONGENITAS_SEM_MICROCEFALIA = "alteracoesCongenitasSemMicrocefalia";
	public static final String PROP_TIPO_GRAVIDEZ = "tipoGravidez";
	public static final String PROP_FILHO_RESULTADO_IGM_ZIKA = "filhoResultadoIgmZika";
	public static final String PROP_DATA_EXAME_ULTRASSONOGRAFIA_TRANSFONTANELA = "dataExameUltrassonografiaTransfontanela";
	public static final String PROP_EXAME_ULTRASSONOGRAFIA_TRANSFONTANELA = "exameUltrassonografiaTransfontanela";
	public static final String PROP_DATA_NASCIMENTO_RECEM_NASCIDO = "dataNascimentoRecemNascido";
	public static final String PROP_MAE_SINAL_SINTOMA_PRURIDO = "maeSinalSintomaPrurido";
	public static final String PROP_MAE_HISTORICO_INFECCAO_ARBOBIRUS = "maeHistoricoInfeccaoArbobirus";
	public static final String PROP_MAE_EXAME_HERPES = "maeExameHerpes";
	public static final String PROP_SEXO_RECEM_NASCIDO = "sexoRecemNascido";
	public static final String PROP_MAE_SINAL_SINTOMA_CEFALEIA = "maeSinalSintomaCefaleia";
	public static final String PROP_MAE_RESULTADO_HERPES = "maeResultadoHerpes";
	public static final String PROP_PERIMETRO_CEFALICO = "perimetroCefalico";


	// constructors
	public BaseInvestigacaoAgravoMicrocefalia () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoMicrocefalia (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoAgravoMicrocefalia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		java.lang.String flagInformacoesComplementares) {

		this.setCodigo(codigo);
		this.setRegistroAgravo(registroAgravo);
		this.setFlagInformacoesComplementares(flagInformacoesComplementares);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String flagInformacoesComplementares;
	private java.util.Date dataInvestigacao;
	private java.lang.Long tipoInvestigacao;
	private java.lang.String nomeRecemNascido;
	private java.lang.Long sexoRecemNascido;
	private java.util.Date dataNascimentoRecemNascido;
	private java.lang.String pesoRecemNascido;
	private java.lang.String comprimentoRecemNascido;
	private java.lang.String numeroDeclaracaoNascidoVivo;
	private java.lang.Long tipoAlteracaoCongenitaIgnorado;
	private java.lang.Long microcefaliaApenas;
	private java.lang.Long microcefaliaAlteracaoSNC;
	private java.lang.Long microcefaliaAlteracaoCongenita;
	private java.lang.Long alteracoesCongenitasSemMicrocefalia;
	private java.lang.Long deficienciaNeurologica;
	private java.lang.Long deficienciaAuditiva;
	private java.lang.Long deficienciaVisual;
	private java.lang.Long deteccaoAlteracaoCongenita;
	private java.lang.String idadeGestacionalIdentificacao;
	private java.lang.Long tipoGravidez;
	private java.lang.Long classificacaoNascidoVivoNatimorto;
	private java.lang.Double perimetroCefalico;
	private java.util.Date dataMedicaoPerimetroCefalico;
	private java.lang.Double circunferenciaCraniana;
	private java.util.Date maeDataProvavelInicioSintomas;
	private java.lang.Long maeFebreDuranteGestacao;
	private java.lang.Long maeExantemaDuranteGestacao;
	private java.lang.Long maeSinalSintomaPrurido;
	private java.lang.Long maeSinalSintomaHiperemiaConjuntival;
	private java.lang.Long maeSinalSintomaDorArticulacao;
	private java.lang.Long maeSinalSintomaDorMuscular;
	private java.lang.Long maeSinalSintomaEdemaArticulacao;
	private java.lang.Long maeSinalSintomaCefaleia;
	private java.lang.Long maeSinalSintomaHipertrofiaGanglionar;
	private java.lang.Long maeSinalSintomaAcometimentoNeurologico;
	private java.lang.Long maeRealizouExameLaboratorial;
	private java.lang.Long maeResultadoSifilis;
	private java.lang.Long maeResultadoToxoplasmose;
	private java.lang.Long maeHistoricoInfeccaoArbobirus;
	private java.lang.Long maeHistoricoMalformacaoCongenita;
	private java.lang.Long maeExameCitomegalovirus;
	private java.lang.Long maeResultadoCitomegalovirus;
	private java.lang.Long maeExameHerpes;
	private java.lang.Long maeResultadoHerpes;
	private java.lang.Long maeResultadoIggZika;
	private java.lang.Long maeResultadoTesteRapidoIggZika;
	private java.lang.Long maeResultadoIgmZika;
	private java.lang.Long maeResultadoTesteRapidoIgmZika;
	private java.lang.Long maeResultadoPcrZika;
	private java.lang.Long filhoExameLaboratorial;
	private java.lang.Long filhoResultadoSifilis;
	private java.lang.Long filhoResultadoToxoplasmose;
	private java.lang.Long filhoResultadoCitomegalovirus;
	private java.lang.Long filhoResultadoHerpes;
	private java.lang.Long filhoResultadoIggZika;
	private java.lang.Long filhoResultadoTesteRapidoIggZika;
	private java.lang.Long filhoResultadoIgmZika;
	private java.lang.Long filhoResultadoTesteRapidoIgmZika;
	private java.lang.Long filhoResultadoPcrZika;
	private java.lang.Long exameUltrassonografia;
	private java.util.Date dataExameUltrassonografia;
	private java.lang.String descricaoExameUltrassonografia;
	private java.lang.Long exameUltrassonografiaTransfontanela;
	private java.util.Date dataExameUltrassonografiaTransfontanela;
	private java.lang.String descricaoExameUltrassonografiaTransfontanela;
	private java.lang.Long exameTomografiaComputadorizada;
	private java.util.Date dataExameTomografiaComputadorizada;
	private java.lang.String descricaoExameTomografiaComputadorizada;
	private java.lang.Long exameRessonanciaMagnetica;
	private java.util.Date dataExameRessonanciaMagnetica;
	private java.lang.String descricaoExameRessonanaciaMagnetica;
	private java.lang.Long obito;
	private java.lang.String numeroDeclaracaoObito;
	private java.util.Date dataObito;
	private java.lang.Long obitoNeonatalPrecoce;
	private java.lang.String observacao;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo;
	private br.com.ksisolucoes.vo.basico.Empresa estabelecimentoSaudeParto;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="codigo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: flag_informacoes_complementares
	 */
	public java.lang.String getFlagInformacoesComplementares () {
		return getPropertyValue(this, flagInformacoesComplementares, PROP_FLAG_INFORMACOES_COMPLEMENTARES); 
	}

	/**
	 * Set the value related to the column: flag_informacoes_complementares
	 * @param flagInformacoesComplementares the flag_informacoes_complementares value
	 */
	public void setFlagInformacoesComplementares (java.lang.String flagInformacoesComplementares) {
//        java.lang.String flagInformacoesComplementaresOld = this.flagInformacoesComplementares;
		this.flagInformacoesComplementares = flagInformacoesComplementares;
//        this.getPropertyChangeSupport().firePropertyChange ("flagInformacoesComplementares", flagInformacoesComplementaresOld, flagInformacoesComplementares);
	}



	/**
	 * Return the value associated with the column: dt_investigacao
	 */
	public java.util.Date getDataInvestigacao () {
		return getPropertyValue(this, dataInvestigacao, PROP_DATA_INVESTIGACAO); 
	}

	/**
	 * Set the value related to the column: dt_investigacao
	 * @param dataInvestigacao the dt_investigacao value
	 */
	public void setDataInvestigacao (java.util.Date dataInvestigacao) {
//        java.util.Date dataInvestigacaoOld = this.dataInvestigacao;
		this.dataInvestigacao = dataInvestigacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInvestigacao", dataInvestigacaoOld, dataInvestigacao);
	}



	/**
	 * Return the value associated with the column: tipo_investigacao
	 */
	public java.lang.Long getTipoInvestigacao () {
		return getPropertyValue(this, tipoInvestigacao, PROP_TIPO_INVESTIGACAO); 
	}

	/**
	 * Set the value related to the column: tipo_investigacao
	 * @param tipoInvestigacao the tipo_investigacao value
	 */
	public void setTipoInvestigacao (java.lang.Long tipoInvestigacao) {
//        java.lang.Long tipoInvestigacaoOld = this.tipoInvestigacao;
		this.tipoInvestigacao = tipoInvestigacao;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoInvestigacao", tipoInvestigacaoOld, tipoInvestigacao);
	}



	/**
	 * Return the value associated with the column: nome_recem_nascido
	 */
	public java.lang.String getNomeRecemNascido () {
		return getPropertyValue(this, nomeRecemNascido, PROP_NOME_RECEM_NASCIDO); 
	}

	/**
	 * Set the value related to the column: nome_recem_nascido
	 * @param nomeRecemNascido the nome_recem_nascido value
	 */
	public void setNomeRecemNascido (java.lang.String nomeRecemNascido) {
//        java.lang.String nomeRecemNascidoOld = this.nomeRecemNascido;
		this.nomeRecemNascido = nomeRecemNascido;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeRecemNascido", nomeRecemNascidoOld, nomeRecemNascido);
	}



	/**
	 * Return the value associated with the column: sexo_recem_nascido
	 */
	public java.lang.Long getSexoRecemNascido () {
		return getPropertyValue(this, sexoRecemNascido, PROP_SEXO_RECEM_NASCIDO); 
	}

	/**
	 * Set the value related to the column: sexo_recem_nascido
	 * @param sexoRecemNascido the sexo_recem_nascido value
	 */
	public void setSexoRecemNascido (java.lang.Long sexoRecemNascido) {
//        java.lang.Long sexoRecemNascidoOld = this.sexoRecemNascido;
		this.sexoRecemNascido = sexoRecemNascido;
//        this.getPropertyChangeSupport().firePropertyChange ("sexoRecemNascido", sexoRecemNascidoOld, sexoRecemNascido);
	}



	/**
	 * Return the value associated with the column: dt_nascimento_recem_nascido
	 */
	public java.util.Date getDataNascimentoRecemNascido () {
		return getPropertyValue(this, dataNascimentoRecemNascido, PROP_DATA_NASCIMENTO_RECEM_NASCIDO); 
	}

	/**
	 * Set the value related to the column: dt_nascimento_recem_nascido
	 * @param dataNascimentoRecemNascido the dt_nascimento_recem_nascido value
	 */
	public void setDataNascimentoRecemNascido (java.util.Date dataNascimentoRecemNascido) {
//        java.util.Date dataNascimentoRecemNascidoOld = this.dataNascimentoRecemNascido;
		this.dataNascimentoRecemNascido = dataNascimentoRecemNascido;
//        this.getPropertyChangeSupport().firePropertyChange ("dataNascimentoRecemNascido", dataNascimentoRecemNascidoOld, dataNascimentoRecemNascido);
	}



	/**
	 * Return the value associated with the column: peso_recem_nascido
	 */
	public java.lang.String getPesoRecemNascido () {
		return getPropertyValue(this, pesoRecemNascido, PROP_PESO_RECEM_NASCIDO); 
	}

	/**
	 * Set the value related to the column: peso_recem_nascido
	 * @param pesoRecemNascido the peso_recem_nascido value
	 */
	public void setPesoRecemNascido (java.lang.String pesoRecemNascido) {
//        java.lang.String pesoRecemNascidoOld = this.pesoRecemNascido;
		this.pesoRecemNascido = pesoRecemNascido;
//        this.getPropertyChangeSupport().firePropertyChange ("pesoRecemNascido", pesoRecemNascidoOld, pesoRecemNascido);
	}



	/**
	 * Return the value associated with the column: comprimento_recem_nascido
	 */
	public java.lang.String getComprimentoRecemNascido () {
		return getPropertyValue(this, comprimentoRecemNascido, PROP_COMPRIMENTO_RECEM_NASCIDO); 
	}

	/**
	 * Set the value related to the column: comprimento_recem_nascido
	 * @param comprimentoRecemNascido the comprimento_recem_nascido value
	 */
	public void setComprimentoRecemNascido (java.lang.String comprimentoRecemNascido) {
//        java.lang.String comprimentoRecemNascidoOld = this.comprimentoRecemNascido;
		this.comprimentoRecemNascido = comprimentoRecemNascido;
//        this.getPropertyChangeSupport().firePropertyChange ("comprimentoRecemNascido", comprimentoRecemNascidoOld, comprimentoRecemNascido);
	}



	/**
	 * Return the value associated with the column: nr_declaracao_nascido_vivo
	 */
	public java.lang.String getNumeroDeclaracaoNascidoVivo () {
		return getPropertyValue(this, numeroDeclaracaoNascidoVivo, PROP_NUMERO_DECLARACAO_NASCIDO_VIVO); 
	}

	/**
	 * Set the value related to the column: nr_declaracao_nascido_vivo
	 * @param numeroDeclaracaoNascidoVivo the nr_declaracao_nascido_vivo value
	 */
	public void setNumeroDeclaracaoNascidoVivo (java.lang.String numeroDeclaracaoNascidoVivo) {
//        java.lang.String numeroDeclaracaoNascidoVivoOld = this.numeroDeclaracaoNascidoVivo;
		this.numeroDeclaracaoNascidoVivo = numeroDeclaracaoNascidoVivo;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDeclaracaoNascidoVivo", numeroDeclaracaoNascidoVivoOld, numeroDeclaracaoNascidoVivo);
	}



	/**
	 * Return the value associated with the column: tipo_alteracao_congenita_ignorado
	 */
	public java.lang.Long getTipoAlteracaoCongenitaIgnorado () {
		return getPropertyValue(this, tipoAlteracaoCongenitaIgnorado, PROP_TIPO_ALTERACAO_CONGENITA_IGNORADO); 
	}

	/**
	 * Set the value related to the column: tipo_alteracao_congenita_ignorado
	 * @param tipoAlteracaoCongenitaIgnorado the tipo_alteracao_congenita_ignorado value
	 */
	public void setTipoAlteracaoCongenitaIgnorado (java.lang.Long tipoAlteracaoCongenitaIgnorado) {
//        java.lang.Long tipoAlteracaoCongenitaIgnoradoOld = this.tipoAlteracaoCongenitaIgnorado;
		this.tipoAlteracaoCongenitaIgnorado = tipoAlteracaoCongenitaIgnorado;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAlteracaoCongenitaIgnorado", tipoAlteracaoCongenitaIgnoradoOld, tipoAlteracaoCongenitaIgnorado);
	}



	/**
	 * Return the value associated with the column: microcefalia_apenas
	 */
	public java.lang.Long getMicrocefaliaApenas () {
		return getPropertyValue(this, microcefaliaApenas, PROP_MICROCEFALIA_APENAS); 
	}

	/**
	 * Set the value related to the column: microcefalia_apenas
	 * @param microcefaliaApenas the microcefalia_apenas value
	 */
	public void setMicrocefaliaApenas (java.lang.Long microcefaliaApenas) {
//        java.lang.Long microcefaliaApenasOld = this.microcefaliaApenas;
		this.microcefaliaApenas = microcefaliaApenas;
//        this.getPropertyChangeSupport().firePropertyChange ("microcefaliaApenas", microcefaliaApenasOld, microcefaliaApenas);
	}



	/**
	 * Return the value associated with the column: microcefalia_alteracao_snc
	 */
	public java.lang.Long getMicrocefaliaAlteracaoSNC () {
		return getPropertyValue(this, microcefaliaAlteracaoSNC, PROP_MICROCEFALIA_ALTERACAO_S_N_C); 
	}

	/**
	 * Set the value related to the column: microcefalia_alteracao_snc
	 * @param microcefaliaAlteracaoSNC the microcefalia_alteracao_snc value
	 */
	public void setMicrocefaliaAlteracaoSNC (java.lang.Long microcefaliaAlteracaoSNC) {
//        java.lang.Long microcefaliaAlteracaoSNCOld = this.microcefaliaAlteracaoSNC;
		this.microcefaliaAlteracaoSNC = microcefaliaAlteracaoSNC;
//        this.getPropertyChangeSupport().firePropertyChange ("microcefaliaAlteracaoSNC", microcefaliaAlteracaoSNCOld, microcefaliaAlteracaoSNC);
	}



	/**
	 * Return the value associated with the column: microcefalia_alteracao_congenita
	 */
	public java.lang.Long getMicrocefaliaAlteracaoCongenita () {
		return getPropertyValue(this, microcefaliaAlteracaoCongenita, PROP_MICROCEFALIA_ALTERACAO_CONGENITA); 
	}

	/**
	 * Set the value related to the column: microcefalia_alteracao_congenita
	 * @param microcefaliaAlteracaoCongenita the microcefalia_alteracao_congenita value
	 */
	public void setMicrocefaliaAlteracaoCongenita (java.lang.Long microcefaliaAlteracaoCongenita) {
//        java.lang.Long microcefaliaAlteracaoCongenitaOld = this.microcefaliaAlteracaoCongenita;
		this.microcefaliaAlteracaoCongenita = microcefaliaAlteracaoCongenita;
//        this.getPropertyChangeSupport().firePropertyChange ("microcefaliaAlteracaoCongenita", microcefaliaAlteracaoCongenitaOld, microcefaliaAlteracaoCongenita);
	}



	/**
	 * Return the value associated with the column: alteracoes_congenitas_sem_microcefalia
	 */
	public java.lang.Long getAlteracoesCongenitasSemMicrocefalia () {
		return getPropertyValue(this, alteracoesCongenitasSemMicrocefalia, PROP_ALTERACOES_CONGENITAS_SEM_MICROCEFALIA); 
	}

	/**
	 * Set the value related to the column: alteracoes_congenitas_sem_microcefalia
	 * @param alteracoesCongenitasSemMicrocefalia the alteracoes_congenitas_sem_microcefalia value
	 */
	public void setAlteracoesCongenitasSemMicrocefalia (java.lang.Long alteracoesCongenitasSemMicrocefalia) {
//        java.lang.Long alteracoesCongenitasSemMicrocefaliaOld = this.alteracoesCongenitasSemMicrocefalia;
		this.alteracoesCongenitasSemMicrocefalia = alteracoesCongenitasSemMicrocefalia;
//        this.getPropertyChangeSupport().firePropertyChange ("alteracoesCongenitasSemMicrocefalia", alteracoesCongenitasSemMicrocefaliaOld, alteracoesCongenitasSemMicrocefalia);
	}



	/**
	 * Return the value associated with the column: deficiencia_neurologica
	 */
	public java.lang.Long getDeficienciaNeurologica () {
		return getPropertyValue(this, deficienciaNeurologica, PROP_DEFICIENCIA_NEUROLOGICA); 
	}

	/**
	 * Set the value related to the column: deficiencia_neurologica
	 * @param deficienciaNeurologica the deficiencia_neurologica value
	 */
	public void setDeficienciaNeurologica (java.lang.Long deficienciaNeurologica) {
//        java.lang.Long deficienciaNeurologicaOld = this.deficienciaNeurologica;
		this.deficienciaNeurologica = deficienciaNeurologica;
//        this.getPropertyChangeSupport().firePropertyChange ("deficienciaNeurologica", deficienciaNeurologicaOld, deficienciaNeurologica);
	}



	/**
	 * Return the value associated with the column: deficiencia_auditiva
	 */
	public java.lang.Long getDeficienciaAuditiva () {
		return getPropertyValue(this, deficienciaAuditiva, PROP_DEFICIENCIA_AUDITIVA); 
	}

	/**
	 * Set the value related to the column: deficiencia_auditiva
	 * @param deficienciaAuditiva the deficiencia_auditiva value
	 */
	public void setDeficienciaAuditiva (java.lang.Long deficienciaAuditiva) {
//        java.lang.Long deficienciaAuditivaOld = this.deficienciaAuditiva;
		this.deficienciaAuditiva = deficienciaAuditiva;
//        this.getPropertyChangeSupport().firePropertyChange ("deficienciaAuditiva", deficienciaAuditivaOld, deficienciaAuditiva);
	}



	/**
	 * Return the value associated with the column: deficiencia_visual
	 */
	public java.lang.Long getDeficienciaVisual () {
		return getPropertyValue(this, deficienciaVisual, PROP_DEFICIENCIA_VISUAL); 
	}

	/**
	 * Set the value related to the column: deficiencia_visual
	 * @param deficienciaVisual the deficiencia_visual value
	 */
	public void setDeficienciaVisual (java.lang.Long deficienciaVisual) {
//        java.lang.Long deficienciaVisualOld = this.deficienciaVisual;
		this.deficienciaVisual = deficienciaVisual;
//        this.getPropertyChangeSupport().firePropertyChange ("deficienciaVisual", deficienciaVisualOld, deficienciaVisual);
	}



	/**
	 * Return the value associated with the column: deteccao_alteracao_congenita
	 */
	public java.lang.Long getDeteccaoAlteracaoCongenita () {
		return getPropertyValue(this, deteccaoAlteracaoCongenita, PROP_DETECCAO_ALTERACAO_CONGENITA); 
	}

	/**
	 * Set the value related to the column: deteccao_alteracao_congenita
	 * @param deteccaoAlteracaoCongenita the deteccao_alteracao_congenita value
	 */
	public void setDeteccaoAlteracaoCongenita (java.lang.Long deteccaoAlteracaoCongenita) {
//        java.lang.Long deteccaoAlteracaoCongenitaOld = this.deteccaoAlteracaoCongenita;
		this.deteccaoAlteracaoCongenita = deteccaoAlteracaoCongenita;
//        this.getPropertyChangeSupport().firePropertyChange ("deteccaoAlteracaoCongenita", deteccaoAlteracaoCongenitaOld, deteccaoAlteracaoCongenita);
	}



	/**
	 * Return the value associated with the column: idade_gestacional_identificacao
	 */
	public java.lang.String getIdadeGestacionalIdentificacao () {
		return getPropertyValue(this, idadeGestacionalIdentificacao, PROP_IDADE_GESTACIONAL_IDENTIFICACAO); 
	}

	/**
	 * Set the value related to the column: idade_gestacional_identificacao
	 * @param idadeGestacionalIdentificacao the idade_gestacional_identificacao value
	 */
	public void setIdadeGestacionalIdentificacao (java.lang.String idadeGestacionalIdentificacao) {
//        java.lang.String idadeGestacionalIdentificacaoOld = this.idadeGestacionalIdentificacao;
		this.idadeGestacionalIdentificacao = idadeGestacionalIdentificacao;
//        this.getPropertyChangeSupport().firePropertyChange ("idadeGestacionalIdentificacao", idadeGestacionalIdentificacaoOld, idadeGestacionalIdentificacao);
	}



	/**
	 * Return the value associated with the column: tipo_gravidez
	 */
	public java.lang.Long getTipoGravidez () {
		return getPropertyValue(this, tipoGravidez, PROP_TIPO_GRAVIDEZ); 
	}

	/**
	 * Set the value related to the column: tipo_gravidez
	 * @param tipoGravidez the tipo_gravidez value
	 */
	public void setTipoGravidez (java.lang.Long tipoGravidez) {
//        java.lang.Long tipoGravidezOld = this.tipoGravidez;
		this.tipoGravidez = tipoGravidez;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoGravidez", tipoGravidezOld, tipoGravidez);
	}



	/**
	 * Return the value associated with the column: classificacao_nascido_vivo_natimorto
	 */
	public java.lang.Long getClassificacaoNascidoVivoNatimorto () {
		return getPropertyValue(this, classificacaoNascidoVivoNatimorto, PROP_CLASSIFICACAO_NASCIDO_VIVO_NATIMORTO); 
	}

	/**
	 * Set the value related to the column: classificacao_nascido_vivo_natimorto
	 * @param classificacaoNascidoVivoNatimorto the classificacao_nascido_vivo_natimorto value
	 */
	public void setClassificacaoNascidoVivoNatimorto (java.lang.Long classificacaoNascidoVivoNatimorto) {
//        java.lang.Long classificacaoNascidoVivoNatimortoOld = this.classificacaoNascidoVivoNatimorto;
		this.classificacaoNascidoVivoNatimorto = classificacaoNascidoVivoNatimorto;
//        this.getPropertyChangeSupport().firePropertyChange ("classificacaoNascidoVivoNatimorto", classificacaoNascidoVivoNatimortoOld, classificacaoNascidoVivoNatimorto);
	}



	/**
	 * Return the value associated with the column: perimetro_cefalico
	 */
	public java.lang.Double getPerimetroCefalico () {
		return getPropertyValue(this, perimetroCefalico, PROP_PERIMETRO_CEFALICO); 
	}

	/**
	 * Set the value related to the column: perimetro_cefalico
	 * @param perimetroCefalico the perimetro_cefalico value
	 */
	public void setPerimetroCefalico (java.lang.Double perimetroCefalico) {
//        java.lang.Double perimetroCefalicoOld = this.perimetroCefalico;
		this.perimetroCefalico = perimetroCefalico;
//        this.getPropertyChangeSupport().firePropertyChange ("perimetroCefalico", perimetroCefalicoOld, perimetroCefalico);
	}



	/**
	 * Return the value associated with the column: dt_medicao_perimetro_cefalico
	 */
	public java.util.Date getDataMedicaoPerimetroCefalico () {
		return getPropertyValue(this, dataMedicaoPerimetroCefalico, PROP_DATA_MEDICAO_PERIMETRO_CEFALICO); 
	}

	/**
	 * Set the value related to the column: dt_medicao_perimetro_cefalico
	 * @param dataMedicaoPerimetroCefalico the dt_medicao_perimetro_cefalico value
	 */
	public void setDataMedicaoPerimetroCefalico (java.util.Date dataMedicaoPerimetroCefalico) {
//        java.util.Date dataMedicaoPerimetroCefalicoOld = this.dataMedicaoPerimetroCefalico;
		this.dataMedicaoPerimetroCefalico = dataMedicaoPerimetroCefalico;
//        this.getPropertyChangeSupport().firePropertyChange ("dataMedicaoPerimetroCefalico", dataMedicaoPerimetroCefalicoOld, dataMedicaoPerimetroCefalico);
	}



	/**
	 * Return the value associated with the column: circunferencia_craniana
	 */
	public java.lang.Double getCircunferenciaCraniana () {
		return getPropertyValue(this, circunferenciaCraniana, PROP_CIRCUNFERENCIA_CRANIANA); 
	}

	/**
	 * Set the value related to the column: circunferencia_craniana
	 * @param circunferenciaCraniana the circunferencia_craniana value
	 */
	public void setCircunferenciaCraniana (java.lang.Double circunferenciaCraniana) {
//        java.lang.Double circunferenciaCranianaOld = this.circunferenciaCraniana;
		this.circunferenciaCraniana = circunferenciaCraniana;
//        this.getPropertyChangeSupport().firePropertyChange ("circunferenciaCraniana", circunferenciaCranianaOld, circunferenciaCraniana);
	}



	/**
	 * Return the value associated with the column: mae_dt_provavel_inicio_sintomas
	 */
	public java.util.Date getMaeDataProvavelInicioSintomas () {
		return getPropertyValue(this, maeDataProvavelInicioSintomas, PROP_MAE_DATA_PROVAVEL_INICIO_SINTOMAS); 
	}

	/**
	 * Set the value related to the column: mae_dt_provavel_inicio_sintomas
	 * @param maeDataProvavelInicioSintomas the mae_dt_provavel_inicio_sintomas value
	 */
	public void setMaeDataProvavelInicioSintomas (java.util.Date maeDataProvavelInicioSintomas) {
//        java.util.Date maeDataProvavelInicioSintomasOld = this.maeDataProvavelInicioSintomas;
		this.maeDataProvavelInicioSintomas = maeDataProvavelInicioSintomas;
//        this.getPropertyChangeSupport().firePropertyChange ("maeDataProvavelInicioSintomas", maeDataProvavelInicioSintomasOld, maeDataProvavelInicioSintomas);
	}



	/**
	 * Return the value associated with the column: mae_febre_durante_gestacao
	 */
	public java.lang.Long getMaeFebreDuranteGestacao () {
		return getPropertyValue(this, maeFebreDuranteGestacao, PROP_MAE_FEBRE_DURANTE_GESTACAO); 
	}

	/**
	 * Set the value related to the column: mae_febre_durante_gestacao
	 * @param maeFebreDuranteGestacao the mae_febre_durante_gestacao value
	 */
	public void setMaeFebreDuranteGestacao (java.lang.Long maeFebreDuranteGestacao) {
//        java.lang.Long maeFebreDuranteGestacaoOld = this.maeFebreDuranteGestacao;
		this.maeFebreDuranteGestacao = maeFebreDuranteGestacao;
//        this.getPropertyChangeSupport().firePropertyChange ("maeFebreDuranteGestacao", maeFebreDuranteGestacaoOld, maeFebreDuranteGestacao);
	}



	/**
	 * Return the value associated with the column: mae_exantema_durante_gestacao
	 */
	public java.lang.Long getMaeExantemaDuranteGestacao () {
		return getPropertyValue(this, maeExantemaDuranteGestacao, PROP_MAE_EXANTEMA_DURANTE_GESTACAO); 
	}

	/**
	 * Set the value related to the column: mae_exantema_durante_gestacao
	 * @param maeExantemaDuranteGestacao the mae_exantema_durante_gestacao value
	 */
	public void setMaeExantemaDuranteGestacao (java.lang.Long maeExantemaDuranteGestacao) {
//        java.lang.Long maeExantemaDuranteGestacaoOld = this.maeExantemaDuranteGestacao;
		this.maeExantemaDuranteGestacao = maeExantemaDuranteGestacao;
//        this.getPropertyChangeSupport().firePropertyChange ("maeExantemaDuranteGestacao", maeExantemaDuranteGestacaoOld, maeExantemaDuranteGestacao);
	}



	/**
	 * Return the value associated with the column: mae_sinal_sintoma_prurido
	 */
	public java.lang.Long getMaeSinalSintomaPrurido () {
		return getPropertyValue(this, maeSinalSintomaPrurido, PROP_MAE_SINAL_SINTOMA_PRURIDO); 
	}

	/**
	 * Set the value related to the column: mae_sinal_sintoma_prurido
	 * @param maeSinalSintomaPrurido the mae_sinal_sintoma_prurido value
	 */
	public void setMaeSinalSintomaPrurido (java.lang.Long maeSinalSintomaPrurido) {
//        java.lang.Long maeSinalSintomaPruridoOld = this.maeSinalSintomaPrurido;
		this.maeSinalSintomaPrurido = maeSinalSintomaPrurido;
//        this.getPropertyChangeSupport().firePropertyChange ("maeSinalSintomaPrurido", maeSinalSintomaPruridoOld, maeSinalSintomaPrurido);
	}



	/**
	 * Return the value associated with the column: mae_sinal_sintoma_hiperemia_conjuntival
	 */
	public java.lang.Long getMaeSinalSintomaHiperemiaConjuntival () {
		return getPropertyValue(this, maeSinalSintomaHiperemiaConjuntival, PROP_MAE_SINAL_SINTOMA_HIPEREMIA_CONJUNTIVAL); 
	}

	/**
	 * Set the value related to the column: mae_sinal_sintoma_hiperemia_conjuntival
	 * @param maeSinalSintomaHiperemiaConjuntival the mae_sinal_sintoma_hiperemia_conjuntival value
	 */
	public void setMaeSinalSintomaHiperemiaConjuntival (java.lang.Long maeSinalSintomaHiperemiaConjuntival) {
//        java.lang.Long maeSinalSintomaHiperemiaConjuntivalOld = this.maeSinalSintomaHiperemiaConjuntival;
		this.maeSinalSintomaHiperemiaConjuntival = maeSinalSintomaHiperemiaConjuntival;
//        this.getPropertyChangeSupport().firePropertyChange ("maeSinalSintomaHiperemiaConjuntival", maeSinalSintomaHiperemiaConjuntivalOld, maeSinalSintomaHiperemiaConjuntival);
	}



	/**
	 * Return the value associated with the column: mae_sinal_sintoma_dor_articulacao
	 */
	public java.lang.Long getMaeSinalSintomaDorArticulacao () {
		return getPropertyValue(this, maeSinalSintomaDorArticulacao, PROP_MAE_SINAL_SINTOMA_DOR_ARTICULACAO); 
	}

	/**
	 * Set the value related to the column: mae_sinal_sintoma_dor_articulacao
	 * @param maeSinalSintomaDorArticulacao the mae_sinal_sintoma_dor_articulacao value
	 */
	public void setMaeSinalSintomaDorArticulacao (java.lang.Long maeSinalSintomaDorArticulacao) {
//        java.lang.Long maeSinalSintomaDorArticulacaoOld = this.maeSinalSintomaDorArticulacao;
		this.maeSinalSintomaDorArticulacao = maeSinalSintomaDorArticulacao;
//        this.getPropertyChangeSupport().firePropertyChange ("maeSinalSintomaDorArticulacao", maeSinalSintomaDorArticulacaoOld, maeSinalSintomaDorArticulacao);
	}



	/**
	 * Return the value associated with the column: mae_sinal_sintoma_dor_muscular
	 */
	public java.lang.Long getMaeSinalSintomaDorMuscular () {
		return getPropertyValue(this, maeSinalSintomaDorMuscular, PROP_MAE_SINAL_SINTOMA_DOR_MUSCULAR); 
	}

	/**
	 * Set the value related to the column: mae_sinal_sintoma_dor_muscular
	 * @param maeSinalSintomaDorMuscular the mae_sinal_sintoma_dor_muscular value
	 */
	public void setMaeSinalSintomaDorMuscular (java.lang.Long maeSinalSintomaDorMuscular) {
//        java.lang.Long maeSinalSintomaDorMuscularOld = this.maeSinalSintomaDorMuscular;
		this.maeSinalSintomaDorMuscular = maeSinalSintomaDorMuscular;
//        this.getPropertyChangeSupport().firePropertyChange ("maeSinalSintomaDorMuscular", maeSinalSintomaDorMuscularOld, maeSinalSintomaDorMuscular);
	}



	/**
	 * Return the value associated with the column: mae_sinal_sintoma_edema_articulacao
	 */
	public java.lang.Long getMaeSinalSintomaEdemaArticulacao () {
		return getPropertyValue(this, maeSinalSintomaEdemaArticulacao, PROP_MAE_SINAL_SINTOMA_EDEMA_ARTICULACAO); 
	}

	/**
	 * Set the value related to the column: mae_sinal_sintoma_edema_articulacao
	 * @param maeSinalSintomaEdemaArticulacao the mae_sinal_sintoma_edema_articulacao value
	 */
	public void setMaeSinalSintomaEdemaArticulacao (java.lang.Long maeSinalSintomaEdemaArticulacao) {
//        java.lang.Long maeSinalSintomaEdemaArticulacaoOld = this.maeSinalSintomaEdemaArticulacao;
		this.maeSinalSintomaEdemaArticulacao = maeSinalSintomaEdemaArticulacao;
//        this.getPropertyChangeSupport().firePropertyChange ("maeSinalSintomaEdemaArticulacao", maeSinalSintomaEdemaArticulacaoOld, maeSinalSintomaEdemaArticulacao);
	}



	/**
	 * Return the value associated with the column: mae_sinal_sintoma_cefaleia
	 */
	public java.lang.Long getMaeSinalSintomaCefaleia () {
		return getPropertyValue(this, maeSinalSintomaCefaleia, PROP_MAE_SINAL_SINTOMA_CEFALEIA); 
	}

	/**
	 * Set the value related to the column: mae_sinal_sintoma_cefaleia
	 * @param maeSinalSintomaCefaleia the mae_sinal_sintoma_cefaleia value
	 */
	public void setMaeSinalSintomaCefaleia (java.lang.Long maeSinalSintomaCefaleia) {
//        java.lang.Long maeSinalSintomaCefaleiaOld = this.maeSinalSintomaCefaleia;
		this.maeSinalSintomaCefaleia = maeSinalSintomaCefaleia;
//        this.getPropertyChangeSupport().firePropertyChange ("maeSinalSintomaCefaleia", maeSinalSintomaCefaleiaOld, maeSinalSintomaCefaleia);
	}



	/**
	 * Return the value associated with the column: mae_sinal_sintoma_hipertrofia_ganglionar
	 */
	public java.lang.Long getMaeSinalSintomaHipertrofiaGanglionar () {
		return getPropertyValue(this, maeSinalSintomaHipertrofiaGanglionar, PROP_MAE_SINAL_SINTOMA_HIPERTROFIA_GANGLIONAR); 
	}

	/**
	 * Set the value related to the column: mae_sinal_sintoma_hipertrofia_ganglionar
	 * @param maeSinalSintomaHipertrofiaGanglionar the mae_sinal_sintoma_hipertrofia_ganglionar value
	 */
	public void setMaeSinalSintomaHipertrofiaGanglionar (java.lang.Long maeSinalSintomaHipertrofiaGanglionar) {
//        java.lang.Long maeSinalSintomaHipertrofiaGanglionarOld = this.maeSinalSintomaHipertrofiaGanglionar;
		this.maeSinalSintomaHipertrofiaGanglionar = maeSinalSintomaHipertrofiaGanglionar;
//        this.getPropertyChangeSupport().firePropertyChange ("maeSinalSintomaHipertrofiaGanglionar", maeSinalSintomaHipertrofiaGanglionarOld, maeSinalSintomaHipertrofiaGanglionar);
	}



	/**
	 * Return the value associated with the column: mae_sinal_sintoma_acometimento_neurologico
	 */
	public java.lang.Long getMaeSinalSintomaAcometimentoNeurologico () {
		return getPropertyValue(this, maeSinalSintomaAcometimentoNeurologico, PROP_MAE_SINAL_SINTOMA_ACOMETIMENTO_NEUROLOGICO); 
	}

	/**
	 * Set the value related to the column: mae_sinal_sintoma_acometimento_neurologico
	 * @param maeSinalSintomaAcometimentoNeurologico the mae_sinal_sintoma_acometimento_neurologico value
	 */
	public void setMaeSinalSintomaAcometimentoNeurologico (java.lang.Long maeSinalSintomaAcometimentoNeurologico) {
//        java.lang.Long maeSinalSintomaAcometimentoNeurologicoOld = this.maeSinalSintomaAcometimentoNeurologico;
		this.maeSinalSintomaAcometimentoNeurologico = maeSinalSintomaAcometimentoNeurologico;
//        this.getPropertyChangeSupport().firePropertyChange ("maeSinalSintomaAcometimentoNeurologico", maeSinalSintomaAcometimentoNeurologicoOld, maeSinalSintomaAcometimentoNeurologico);
	}



	/**
	 * Return the value associated with the column: mae_realizou_exame_laboratorial
	 */
	public java.lang.Long getMaeRealizouExameLaboratorial () {
		return getPropertyValue(this, maeRealizouExameLaboratorial, PROP_MAE_REALIZOU_EXAME_LABORATORIAL); 
	}

	/**
	 * Set the value related to the column: mae_realizou_exame_laboratorial
	 * @param maeRealizouExameLaboratorial the mae_realizou_exame_laboratorial value
	 */
	public void setMaeRealizouExameLaboratorial (java.lang.Long maeRealizouExameLaboratorial) {
//        java.lang.Long maeRealizouExameLaboratorialOld = this.maeRealizouExameLaboratorial;
		this.maeRealizouExameLaboratorial = maeRealizouExameLaboratorial;
//        this.getPropertyChangeSupport().firePropertyChange ("maeRealizouExameLaboratorial", maeRealizouExameLaboratorialOld, maeRealizouExameLaboratorial);
	}



	/**
	 * Return the value associated with the column: mae_resultado_sifilis
	 */
	public java.lang.Long getMaeResultadoSifilis () {
		return getPropertyValue(this, maeResultadoSifilis, PROP_MAE_RESULTADO_SIFILIS); 
	}

	/**
	 * Set the value related to the column: mae_resultado_sifilis
	 * @param maeResultadoSifilis the mae_resultado_sifilis value
	 */
	public void setMaeResultadoSifilis (java.lang.Long maeResultadoSifilis) {
//        java.lang.Long maeResultadoSifilisOld = this.maeResultadoSifilis;
		this.maeResultadoSifilis = maeResultadoSifilis;
//        this.getPropertyChangeSupport().firePropertyChange ("maeResultadoSifilis", maeResultadoSifilisOld, maeResultadoSifilis);
	}



	/**
	 * Return the value associated with the column: mae_resultado_toxoplasmose
	 */
	public java.lang.Long getMaeResultadoToxoplasmose () {
		return getPropertyValue(this, maeResultadoToxoplasmose, PROP_MAE_RESULTADO_TOXOPLASMOSE); 
	}

	/**
	 * Set the value related to the column: mae_resultado_toxoplasmose
	 * @param maeResultadoToxoplasmose the mae_resultado_toxoplasmose value
	 */
	public void setMaeResultadoToxoplasmose (java.lang.Long maeResultadoToxoplasmose) {
//        java.lang.Long maeResultadoToxoplasmoseOld = this.maeResultadoToxoplasmose;
		this.maeResultadoToxoplasmose = maeResultadoToxoplasmose;
//        this.getPropertyChangeSupport().firePropertyChange ("maeResultadoToxoplasmose", maeResultadoToxoplasmoseOld, maeResultadoToxoplasmose);
	}



	/**
	 * Return the value associated with the column: mae_historico_infeccao_arbobirus
	 */
	public java.lang.Long getMaeHistoricoInfeccaoArbobirus () {
		return getPropertyValue(this, maeHistoricoInfeccaoArbobirus, PROP_MAE_HISTORICO_INFECCAO_ARBOBIRUS); 
	}

	/**
	 * Set the value related to the column: mae_historico_infeccao_arbobirus
	 * @param maeHistoricoInfeccaoArbobirus the mae_historico_infeccao_arbobirus value
	 */
	public void setMaeHistoricoInfeccaoArbobirus (java.lang.Long maeHistoricoInfeccaoArbobirus) {
//        java.lang.Long maeHistoricoInfeccaoArbobirusOld = this.maeHistoricoInfeccaoArbobirus;
		this.maeHistoricoInfeccaoArbobirus = maeHistoricoInfeccaoArbobirus;
//        this.getPropertyChangeSupport().firePropertyChange ("maeHistoricoInfeccaoArbobirus", maeHistoricoInfeccaoArbobirusOld, maeHistoricoInfeccaoArbobirus);
	}



	/**
	 * Return the value associated with the column: mae_historico_malformacao_congenita
	 */
	public java.lang.Long getMaeHistoricoMalformacaoCongenita () {
		return getPropertyValue(this, maeHistoricoMalformacaoCongenita, PROP_MAE_HISTORICO_MALFORMACAO_CONGENITA); 
	}

	/**
	 * Set the value related to the column: mae_historico_malformacao_congenita
	 * @param maeHistoricoMalformacaoCongenita the mae_historico_malformacao_congenita value
	 */
	public void setMaeHistoricoMalformacaoCongenita (java.lang.Long maeHistoricoMalformacaoCongenita) {
//        java.lang.Long maeHistoricoMalformacaoCongenitaOld = this.maeHistoricoMalformacaoCongenita;
		this.maeHistoricoMalformacaoCongenita = maeHistoricoMalformacaoCongenita;
//        this.getPropertyChangeSupport().firePropertyChange ("maeHistoricoMalformacaoCongenita", maeHistoricoMalformacaoCongenitaOld, maeHistoricoMalformacaoCongenita);
	}



	/**
	 * Return the value associated with the column: mae_exame_citomegalovirus
	 */
	public java.lang.Long getMaeExameCitomegalovirus () {
		return getPropertyValue(this, maeExameCitomegalovirus, PROP_MAE_EXAME_CITOMEGALOVIRUS); 
	}

	/**
	 * Set the value related to the column: mae_exame_citomegalovirus
	 * @param maeExameCitomegalovirus the mae_exame_citomegalovirus value
	 */
	public void setMaeExameCitomegalovirus (java.lang.Long maeExameCitomegalovirus) {
//        java.lang.Long maeExameCitomegalovirusOld = this.maeExameCitomegalovirus;
		this.maeExameCitomegalovirus = maeExameCitomegalovirus;
//        this.getPropertyChangeSupport().firePropertyChange ("maeExameCitomegalovirus", maeExameCitomegalovirusOld, maeExameCitomegalovirus);
	}



	/**
	 * Return the value associated with the column: mae_resultado_citomegalovirus
	 */
	public java.lang.Long getMaeResultadoCitomegalovirus () {
		return getPropertyValue(this, maeResultadoCitomegalovirus, PROP_MAE_RESULTADO_CITOMEGALOVIRUS); 
	}

	/**
	 * Set the value related to the column: mae_resultado_citomegalovirus
	 * @param maeResultadoCitomegalovirus the mae_resultado_citomegalovirus value
	 */
	public void setMaeResultadoCitomegalovirus (java.lang.Long maeResultadoCitomegalovirus) {
//        java.lang.Long maeResultadoCitomegalovirusOld = this.maeResultadoCitomegalovirus;
		this.maeResultadoCitomegalovirus = maeResultadoCitomegalovirus;
//        this.getPropertyChangeSupport().firePropertyChange ("maeResultadoCitomegalovirus", maeResultadoCitomegalovirusOld, maeResultadoCitomegalovirus);
	}



	/**
	 * Return the value associated with the column: mae_exame_herpes
	 */
	public java.lang.Long getMaeExameHerpes () {
		return getPropertyValue(this, maeExameHerpes, PROP_MAE_EXAME_HERPES); 
	}

	/**
	 * Set the value related to the column: mae_exame_herpes
	 * @param maeExameHerpes the mae_exame_herpes value
	 */
	public void setMaeExameHerpes (java.lang.Long maeExameHerpes) {
//        java.lang.Long maeExameHerpesOld = this.maeExameHerpes;
		this.maeExameHerpes = maeExameHerpes;
//        this.getPropertyChangeSupport().firePropertyChange ("maeExameHerpes", maeExameHerpesOld, maeExameHerpes);
	}



	/**
	 * Return the value associated with the column: mae_resultado_herpes
	 */
	public java.lang.Long getMaeResultadoHerpes () {
		return getPropertyValue(this, maeResultadoHerpes, PROP_MAE_RESULTADO_HERPES); 
	}

	/**
	 * Set the value related to the column: mae_resultado_herpes
	 * @param maeResultadoHerpes the mae_resultado_herpes value
	 */
	public void setMaeResultadoHerpes (java.lang.Long maeResultadoHerpes) {
//        java.lang.Long maeResultadoHerpesOld = this.maeResultadoHerpes;
		this.maeResultadoHerpes = maeResultadoHerpes;
//        this.getPropertyChangeSupport().firePropertyChange ("maeResultadoHerpes", maeResultadoHerpesOld, maeResultadoHerpes);
	}



	/**
	 * Return the value associated with the column: mae_resultado_igg_zika
	 */
	public java.lang.Long getMaeResultadoIggZika () {
		return getPropertyValue(this, maeResultadoIggZika, PROP_MAE_RESULTADO_IGG_ZIKA); 
	}

	/**
	 * Set the value related to the column: mae_resultado_igg_zika
	 * @param maeResultadoIggZika the mae_resultado_igg_zika value
	 */
	public void setMaeResultadoIggZika (java.lang.Long maeResultadoIggZika) {
//        java.lang.Long maeResultadoIggZikaOld = this.maeResultadoIggZika;
		this.maeResultadoIggZika = maeResultadoIggZika;
//        this.getPropertyChangeSupport().firePropertyChange ("maeResultadoIggZika", maeResultadoIggZikaOld, maeResultadoIggZika);
	}



	/**
	 * Return the value associated with the column: mae_resultado_teste_rapido_igg_zika
	 */
	public java.lang.Long getMaeResultadoTesteRapidoIggZika () {
		return getPropertyValue(this, maeResultadoTesteRapidoIggZika, PROP_MAE_RESULTADO_TESTE_RAPIDO_IGG_ZIKA); 
	}

	/**
	 * Set the value related to the column: mae_resultado_teste_rapido_igg_zika
	 * @param maeResultadoTesteRapidoIggZika the mae_resultado_teste_rapido_igg_zika value
	 */
	public void setMaeResultadoTesteRapidoIggZika (java.lang.Long maeResultadoTesteRapidoIggZika) {
//        java.lang.Long maeResultadoTesteRapidoIggZikaOld = this.maeResultadoTesteRapidoIggZika;
		this.maeResultadoTesteRapidoIggZika = maeResultadoTesteRapidoIggZika;
//        this.getPropertyChangeSupport().firePropertyChange ("maeResultadoTesteRapidoIggZika", maeResultadoTesteRapidoIggZikaOld, maeResultadoTesteRapidoIggZika);
	}



	/**
	 * Return the value associated with the column: mae_resultado_igm_zika
	 */
	public java.lang.Long getMaeResultadoIgmZika () {
		return getPropertyValue(this, maeResultadoIgmZika, PROP_MAE_RESULTADO_IGM_ZIKA); 
	}

	/**
	 * Set the value related to the column: mae_resultado_igm_zika
	 * @param maeResultadoIgmZika the mae_resultado_igm_zika value
	 */
	public void setMaeResultadoIgmZika (java.lang.Long maeResultadoIgmZika) {
//        java.lang.Long maeResultadoIgmZikaOld = this.maeResultadoIgmZika;
		this.maeResultadoIgmZika = maeResultadoIgmZika;
//        this.getPropertyChangeSupport().firePropertyChange ("maeResultadoIgmZika", maeResultadoIgmZikaOld, maeResultadoIgmZika);
	}



	/**
	 * Return the value associated with the column: mae_resultado_teste_rapido_igm_zika
	 */
	public java.lang.Long getMaeResultadoTesteRapidoIgmZika () {
		return getPropertyValue(this, maeResultadoTesteRapidoIgmZika, PROP_MAE_RESULTADO_TESTE_RAPIDO_IGM_ZIKA); 
	}

	/**
	 * Set the value related to the column: mae_resultado_teste_rapido_igm_zika
	 * @param maeResultadoTesteRapidoIgmZika the mae_resultado_teste_rapido_igm_zika value
	 */
	public void setMaeResultadoTesteRapidoIgmZika (java.lang.Long maeResultadoTesteRapidoIgmZika) {
//        java.lang.Long maeResultadoTesteRapidoIgmZikaOld = this.maeResultadoTesteRapidoIgmZika;
		this.maeResultadoTesteRapidoIgmZika = maeResultadoTesteRapidoIgmZika;
//        this.getPropertyChangeSupport().firePropertyChange ("maeResultadoTesteRapidoIgmZika", maeResultadoTesteRapidoIgmZikaOld, maeResultadoTesteRapidoIgmZika);
	}



	/**
	 * Return the value associated with the column: mae_resultado_pcr_zika
	 */
	public java.lang.Long getMaeResultadoPcrZika () {
		return getPropertyValue(this, maeResultadoPcrZika, PROP_MAE_RESULTADO_PCR_ZIKA); 
	}

	/**
	 * Set the value related to the column: mae_resultado_pcr_zika
	 * @param maeResultadoPcrZika the mae_resultado_pcr_zika value
	 */
	public void setMaeResultadoPcrZika (java.lang.Long maeResultadoPcrZika) {
//        java.lang.Long maeResultadoPcrZikaOld = this.maeResultadoPcrZika;
		this.maeResultadoPcrZika = maeResultadoPcrZika;
//        this.getPropertyChangeSupport().firePropertyChange ("maeResultadoPcrZika", maeResultadoPcrZikaOld, maeResultadoPcrZika);
	}



	/**
	 * Return the value associated with the column: filho_exame_laboratorial
	 */
	public java.lang.Long getFilhoExameLaboratorial () {
		return getPropertyValue(this, filhoExameLaboratorial, PROP_FILHO_EXAME_LABORATORIAL); 
	}

	/**
	 * Set the value related to the column: filho_exame_laboratorial
	 * @param filhoExameLaboratorial the filho_exame_laboratorial value
	 */
	public void setFilhoExameLaboratorial (java.lang.Long filhoExameLaboratorial) {
//        java.lang.Long filhoExameLaboratorialOld = this.filhoExameLaboratorial;
		this.filhoExameLaboratorial = filhoExameLaboratorial;
//        this.getPropertyChangeSupport().firePropertyChange ("filhoExameLaboratorial", filhoExameLaboratorialOld, filhoExameLaboratorial);
	}



	/**
	 * Return the value associated with the column: filho_resultado_sifilis
	 */
	public java.lang.Long getFilhoResultadoSifilis () {
		return getPropertyValue(this, filhoResultadoSifilis, PROP_FILHO_RESULTADO_SIFILIS); 
	}

	/**
	 * Set the value related to the column: filho_resultado_sifilis
	 * @param filhoResultadoSifilis the filho_resultado_sifilis value
	 */
	public void setFilhoResultadoSifilis (java.lang.Long filhoResultadoSifilis) {
//        java.lang.Long filhoResultadoSifilisOld = this.filhoResultadoSifilis;
		this.filhoResultadoSifilis = filhoResultadoSifilis;
//        this.getPropertyChangeSupport().firePropertyChange ("filhoResultadoSifilis", filhoResultadoSifilisOld, filhoResultadoSifilis);
	}



	/**
	 * Return the value associated with the column: filho_resultado_toxoplasmose
	 */
	public java.lang.Long getFilhoResultadoToxoplasmose () {
		return getPropertyValue(this, filhoResultadoToxoplasmose, PROP_FILHO_RESULTADO_TOXOPLASMOSE); 
	}

	/**
	 * Set the value related to the column: filho_resultado_toxoplasmose
	 * @param filhoResultadoToxoplasmose the filho_resultado_toxoplasmose value
	 */
	public void setFilhoResultadoToxoplasmose (java.lang.Long filhoResultadoToxoplasmose) {
//        java.lang.Long filhoResultadoToxoplasmoseOld = this.filhoResultadoToxoplasmose;
		this.filhoResultadoToxoplasmose = filhoResultadoToxoplasmose;
//        this.getPropertyChangeSupport().firePropertyChange ("filhoResultadoToxoplasmose", filhoResultadoToxoplasmoseOld, filhoResultadoToxoplasmose);
	}



	/**
	 * Return the value associated with the column: filho_resultado_citomegalovirus
	 */
	public java.lang.Long getFilhoResultadoCitomegalovirus () {
		return getPropertyValue(this, filhoResultadoCitomegalovirus, PROP_FILHO_RESULTADO_CITOMEGALOVIRUS); 
	}

	/**
	 * Set the value related to the column: filho_resultado_citomegalovirus
	 * @param filhoResultadoCitomegalovirus the filho_resultado_citomegalovirus value
	 */
	public void setFilhoResultadoCitomegalovirus (java.lang.Long filhoResultadoCitomegalovirus) {
//        java.lang.Long filhoResultadoCitomegalovirusOld = this.filhoResultadoCitomegalovirus;
		this.filhoResultadoCitomegalovirus = filhoResultadoCitomegalovirus;
//        this.getPropertyChangeSupport().firePropertyChange ("filhoResultadoCitomegalovirus", filhoResultadoCitomegalovirusOld, filhoResultadoCitomegalovirus);
	}



	/**
	 * Return the value associated with the column: filho_resultado_herpes
	 */
	public java.lang.Long getFilhoResultadoHerpes () {
		return getPropertyValue(this, filhoResultadoHerpes, PROP_FILHO_RESULTADO_HERPES); 
	}

	/**
	 * Set the value related to the column: filho_resultado_herpes
	 * @param filhoResultadoHerpes the filho_resultado_herpes value
	 */
	public void setFilhoResultadoHerpes (java.lang.Long filhoResultadoHerpes) {
//        java.lang.Long filhoResultadoHerpesOld = this.filhoResultadoHerpes;
		this.filhoResultadoHerpes = filhoResultadoHerpes;
//        this.getPropertyChangeSupport().firePropertyChange ("filhoResultadoHerpes", filhoResultadoHerpesOld, filhoResultadoHerpes);
	}



	/**
	 * Return the value associated with the column: filho_resultado_igg_zika
	 */
	public java.lang.Long getFilhoResultadoIggZika () {
		return getPropertyValue(this, filhoResultadoIggZika, PROP_FILHO_RESULTADO_IGG_ZIKA); 
	}

	/**
	 * Set the value related to the column: filho_resultado_igg_zika
	 * @param filhoResultadoIggZika the filho_resultado_igg_zika value
	 */
	public void setFilhoResultadoIggZika (java.lang.Long filhoResultadoIggZika) {
//        java.lang.Long filhoResultadoIggZikaOld = this.filhoResultadoIggZika;
		this.filhoResultadoIggZika = filhoResultadoIggZika;
//        this.getPropertyChangeSupport().firePropertyChange ("filhoResultadoIggZika", filhoResultadoIggZikaOld, filhoResultadoIggZika);
	}



	/**
	 * Return the value associated with the column: filho_resultado_teste_rapido_igg_zika
	 */
	public java.lang.Long getFilhoResultadoTesteRapidoIggZika () {
		return getPropertyValue(this, filhoResultadoTesteRapidoIggZika, PROP_FILHO_RESULTADO_TESTE_RAPIDO_IGG_ZIKA); 
	}

	/**
	 * Set the value related to the column: filho_resultado_teste_rapido_igg_zika
	 * @param filhoResultadoTesteRapidoIggZika the filho_resultado_teste_rapido_igg_zika value
	 */
	public void setFilhoResultadoTesteRapidoIggZika (java.lang.Long filhoResultadoTesteRapidoIggZika) {
//        java.lang.Long filhoResultadoTesteRapidoIggZikaOld = this.filhoResultadoTesteRapidoIggZika;
		this.filhoResultadoTesteRapidoIggZika = filhoResultadoTesteRapidoIggZika;
//        this.getPropertyChangeSupport().firePropertyChange ("filhoResultadoTesteRapidoIggZika", filhoResultadoTesteRapidoIggZikaOld, filhoResultadoTesteRapidoIggZika);
	}



	/**
	 * Return the value associated with the column: filho_resultado_igm_zika
	 */
	public java.lang.Long getFilhoResultadoIgmZika () {
		return getPropertyValue(this, filhoResultadoIgmZika, PROP_FILHO_RESULTADO_IGM_ZIKA); 
	}

	/**
	 * Set the value related to the column: filho_resultado_igm_zika
	 * @param filhoResultadoIgmZika the filho_resultado_igm_zika value
	 */
	public void setFilhoResultadoIgmZika (java.lang.Long filhoResultadoIgmZika) {
//        java.lang.Long filhoResultadoIgmZikaOld = this.filhoResultadoIgmZika;
		this.filhoResultadoIgmZika = filhoResultadoIgmZika;
//        this.getPropertyChangeSupport().firePropertyChange ("filhoResultadoIgmZika", filhoResultadoIgmZikaOld, filhoResultadoIgmZika);
	}



	/**
	 * Return the value associated with the column: filho_resultado_teste_rapido_igm_zika
	 */
	public java.lang.Long getFilhoResultadoTesteRapidoIgmZika () {
		return getPropertyValue(this, filhoResultadoTesteRapidoIgmZika, PROP_FILHO_RESULTADO_TESTE_RAPIDO_IGM_ZIKA); 
	}

	/**
	 * Set the value related to the column: filho_resultado_teste_rapido_igm_zika
	 * @param filhoResultadoTesteRapidoIgmZika the filho_resultado_teste_rapido_igm_zika value
	 */
	public void setFilhoResultadoTesteRapidoIgmZika (java.lang.Long filhoResultadoTesteRapidoIgmZika) {
//        java.lang.Long filhoResultadoTesteRapidoIgmZikaOld = this.filhoResultadoTesteRapidoIgmZika;
		this.filhoResultadoTesteRapidoIgmZika = filhoResultadoTesteRapidoIgmZika;
//        this.getPropertyChangeSupport().firePropertyChange ("filhoResultadoTesteRapidoIgmZika", filhoResultadoTesteRapidoIgmZikaOld, filhoResultadoTesteRapidoIgmZika);
	}



	/**
	 * Return the value associated with the column: filho_resultado_pcr_zika
	 */
	public java.lang.Long getFilhoResultadoPcrZika () {
		return getPropertyValue(this, filhoResultadoPcrZika, PROP_FILHO_RESULTADO_PCR_ZIKA); 
	}

	/**
	 * Set the value related to the column: filho_resultado_pcr_zika
	 * @param filhoResultadoPcrZika the filho_resultado_pcr_zika value
	 */
	public void setFilhoResultadoPcrZika (java.lang.Long filhoResultadoPcrZika) {
//        java.lang.Long filhoResultadoPcrZikaOld = this.filhoResultadoPcrZika;
		this.filhoResultadoPcrZika = filhoResultadoPcrZika;
//        this.getPropertyChangeSupport().firePropertyChange ("filhoResultadoPcrZika", filhoResultadoPcrZikaOld, filhoResultadoPcrZika);
	}



	/**
	 * Return the value associated with the column: exame_ultrassonografia
	 */
	public java.lang.Long getExameUltrassonografia () {
		return getPropertyValue(this, exameUltrassonografia, PROP_EXAME_ULTRASSONOGRAFIA); 
	}

	/**
	 * Set the value related to the column: exame_ultrassonografia
	 * @param exameUltrassonografia the exame_ultrassonografia value
	 */
	public void setExameUltrassonografia (java.lang.Long exameUltrassonografia) {
//        java.lang.Long exameUltrassonografiaOld = this.exameUltrassonografia;
		this.exameUltrassonografia = exameUltrassonografia;
//        this.getPropertyChangeSupport().firePropertyChange ("exameUltrassonografia", exameUltrassonografiaOld, exameUltrassonografia);
	}



	/**
	 * Return the value associated with the column: dt_exame_ultrassonografia
	 */
	public java.util.Date getDataExameUltrassonografia () {
		return getPropertyValue(this, dataExameUltrassonografia, PROP_DATA_EXAME_ULTRASSONOGRAFIA); 
	}

	/**
	 * Set the value related to the column: dt_exame_ultrassonografia
	 * @param dataExameUltrassonografia the dt_exame_ultrassonografia value
	 */
	public void setDataExameUltrassonografia (java.util.Date dataExameUltrassonografia) {
//        java.util.Date dataExameUltrassonografiaOld = this.dataExameUltrassonografia;
		this.dataExameUltrassonografia = dataExameUltrassonografia;
//        this.getPropertyChangeSupport().firePropertyChange ("dataExameUltrassonografia", dataExameUltrassonografiaOld, dataExameUltrassonografia);
	}



	/**
	 * Return the value associated with the column: txt_exame_ultrassonografia
	 */
	public java.lang.String getDescricaoExameUltrassonografia () {
		return getPropertyValue(this, descricaoExameUltrassonografia, PROP_DESCRICAO_EXAME_ULTRASSONOGRAFIA); 
	}

	/**
	 * Set the value related to the column: txt_exame_ultrassonografia
	 * @param descricaoExameUltrassonografia the txt_exame_ultrassonografia value
	 */
	public void setDescricaoExameUltrassonografia (java.lang.String descricaoExameUltrassonografia) {
//        java.lang.String descricaoExameUltrassonografiaOld = this.descricaoExameUltrassonografia;
		this.descricaoExameUltrassonografia = descricaoExameUltrassonografia;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoExameUltrassonografia", descricaoExameUltrassonografiaOld, descricaoExameUltrassonografia);
	}



	/**
	 * Return the value associated with the column: exame_ultrassonografia_transfontanela
	 */
	public java.lang.Long getExameUltrassonografiaTransfontanela () {
		return getPropertyValue(this, exameUltrassonografiaTransfontanela, PROP_EXAME_ULTRASSONOGRAFIA_TRANSFONTANELA); 
	}

	/**
	 * Set the value related to the column: exame_ultrassonografia_transfontanela
	 * @param exameUltrassonografiaTransfontanela the exame_ultrassonografia_transfontanela value
	 */
	public void setExameUltrassonografiaTransfontanela (java.lang.Long exameUltrassonografiaTransfontanela) {
//        java.lang.Long exameUltrassonografiaTransfontanelaOld = this.exameUltrassonografiaTransfontanela;
		this.exameUltrassonografiaTransfontanela = exameUltrassonografiaTransfontanela;
//        this.getPropertyChangeSupport().firePropertyChange ("exameUltrassonografiaTransfontanela", exameUltrassonografiaTransfontanelaOld, exameUltrassonografiaTransfontanela);
	}



	/**
	 * Return the value associated with the column: dt_exame_ultrassonografia_transfontanela
	 */
	public java.util.Date getDataExameUltrassonografiaTransfontanela () {
		return getPropertyValue(this, dataExameUltrassonografiaTransfontanela, PROP_DATA_EXAME_ULTRASSONOGRAFIA_TRANSFONTANELA); 
	}

	/**
	 * Set the value related to the column: dt_exame_ultrassonografia_transfontanela
	 * @param dataExameUltrassonografiaTransfontanela the dt_exame_ultrassonografia_transfontanela value
	 */
	public void setDataExameUltrassonografiaTransfontanela (java.util.Date dataExameUltrassonografiaTransfontanela) {
//        java.util.Date dataExameUltrassonografiaTransfontanelaOld = this.dataExameUltrassonografiaTransfontanela;
		this.dataExameUltrassonografiaTransfontanela = dataExameUltrassonografiaTransfontanela;
//        this.getPropertyChangeSupport().firePropertyChange ("dataExameUltrassonografiaTransfontanela", dataExameUltrassonografiaTransfontanelaOld, dataExameUltrassonografiaTransfontanela);
	}



	/**
	 * Return the value associated with the column: txt_exame_ultrassonografia_transfontanela
	 */
	public java.lang.String getDescricaoExameUltrassonografiaTransfontanela () {
		return getPropertyValue(this, descricaoExameUltrassonografiaTransfontanela, PROP_DESCRICAO_EXAME_ULTRASSONOGRAFIA_TRANSFONTANELA); 
	}

	/**
	 * Set the value related to the column: txt_exame_ultrassonografia_transfontanela
	 * @param descricaoExameUltrassonografiaTransfontanela the txt_exame_ultrassonografia_transfontanela value
	 */
	public void setDescricaoExameUltrassonografiaTransfontanela (java.lang.String descricaoExameUltrassonografiaTransfontanela) {
//        java.lang.String descricaoExameUltrassonografiaTransfontanelaOld = this.descricaoExameUltrassonografiaTransfontanela;
		this.descricaoExameUltrassonografiaTransfontanela = descricaoExameUltrassonografiaTransfontanela;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoExameUltrassonografiaTransfontanela", descricaoExameUltrassonografiaTransfontanelaOld, descricaoExameUltrassonografiaTransfontanela);
	}



	/**
	 * Return the value associated with the column: exame_tomografia_computadorizada
	 */
	public java.lang.Long getExameTomografiaComputadorizada () {
		return getPropertyValue(this, exameTomografiaComputadorizada, PROP_EXAME_TOMOGRAFIA_COMPUTADORIZADA); 
	}

	/**
	 * Set the value related to the column: exame_tomografia_computadorizada
	 * @param exameTomografiaComputadorizada the exame_tomografia_computadorizada value
	 */
	public void setExameTomografiaComputadorizada (java.lang.Long exameTomografiaComputadorizada) {
//        java.lang.Long exameTomografiaComputadorizadaOld = this.exameTomografiaComputadorizada;
		this.exameTomografiaComputadorizada = exameTomografiaComputadorizada;
//        this.getPropertyChangeSupport().firePropertyChange ("exameTomografiaComputadorizada", exameTomografiaComputadorizadaOld, exameTomografiaComputadorizada);
	}



	/**
	 * Return the value associated with the column: dt_exame_tomografia_computadorizada
	 */
	public java.util.Date getDataExameTomografiaComputadorizada () {
		return getPropertyValue(this, dataExameTomografiaComputadorizada, PROP_DATA_EXAME_TOMOGRAFIA_COMPUTADORIZADA); 
	}

	/**
	 * Set the value related to the column: dt_exame_tomografia_computadorizada
	 * @param dataExameTomografiaComputadorizada the dt_exame_tomografia_computadorizada value
	 */
	public void setDataExameTomografiaComputadorizada (java.util.Date dataExameTomografiaComputadorizada) {
//        java.util.Date dataExameTomografiaComputadorizadaOld = this.dataExameTomografiaComputadorizada;
		this.dataExameTomografiaComputadorizada = dataExameTomografiaComputadorizada;
//        this.getPropertyChangeSupport().firePropertyChange ("dataExameTomografiaComputadorizada", dataExameTomografiaComputadorizadaOld, dataExameTomografiaComputadorizada);
	}



	/**
	 * Return the value associated with the column: txt_exame_tomografia_computadorizada
	 */
	public java.lang.String getDescricaoExameTomografiaComputadorizada () {
		return getPropertyValue(this, descricaoExameTomografiaComputadorizada, PROP_DESCRICAO_EXAME_TOMOGRAFIA_COMPUTADORIZADA); 
	}

	/**
	 * Set the value related to the column: txt_exame_tomografia_computadorizada
	 * @param descricaoExameTomografiaComputadorizada the txt_exame_tomografia_computadorizada value
	 */
	public void setDescricaoExameTomografiaComputadorizada (java.lang.String descricaoExameTomografiaComputadorizada) {
//        java.lang.String descricaoExameTomografiaComputadorizadaOld = this.descricaoExameTomografiaComputadorizada;
		this.descricaoExameTomografiaComputadorizada = descricaoExameTomografiaComputadorizada;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoExameTomografiaComputadorizada", descricaoExameTomografiaComputadorizadaOld, descricaoExameTomografiaComputadorizada);
	}



	/**
	 * Return the value associated with the column: exame_ressonancia_magnetica
	 */
	public java.lang.Long getExameRessonanciaMagnetica () {
		return getPropertyValue(this, exameRessonanciaMagnetica, PROP_EXAME_RESSONANCIA_MAGNETICA); 
	}

	/**
	 * Set the value related to the column: exame_ressonancia_magnetica
	 * @param exameRessonanciaMagnetica the exame_ressonancia_magnetica value
	 */
	public void setExameRessonanciaMagnetica (java.lang.Long exameRessonanciaMagnetica) {
//        java.lang.Long exameRessonanciaMagneticaOld = this.exameRessonanciaMagnetica;
		this.exameRessonanciaMagnetica = exameRessonanciaMagnetica;
//        this.getPropertyChangeSupport().firePropertyChange ("exameRessonanciaMagnetica", exameRessonanciaMagneticaOld, exameRessonanciaMagnetica);
	}



	/**
	 * Return the value associated with the column: dt_exame_ressonancia_magnetica
	 */
	public java.util.Date getDataExameRessonanciaMagnetica () {
		return getPropertyValue(this, dataExameRessonanciaMagnetica, PROP_DATA_EXAME_RESSONANCIA_MAGNETICA); 
	}

	/**
	 * Set the value related to the column: dt_exame_ressonancia_magnetica
	 * @param dataExameRessonanciaMagnetica the dt_exame_ressonancia_magnetica value
	 */
	public void setDataExameRessonanciaMagnetica (java.util.Date dataExameRessonanciaMagnetica) {
//        java.util.Date dataExameRessonanciaMagneticaOld = this.dataExameRessonanciaMagnetica;
		this.dataExameRessonanciaMagnetica = dataExameRessonanciaMagnetica;
//        this.getPropertyChangeSupport().firePropertyChange ("dataExameRessonanciaMagnetica", dataExameRessonanciaMagneticaOld, dataExameRessonanciaMagnetica);
	}



	/**
	 * Return the value associated with the column: txt_exame_ressonancia_magnetica
	 */
	public java.lang.String getDescricaoExameRessonanaciaMagnetica () {
		return getPropertyValue(this, descricaoExameRessonanaciaMagnetica, PROP_DESCRICAO_EXAME_RESSONANACIA_MAGNETICA); 
	}

	/**
	 * Set the value related to the column: txt_exame_ressonancia_magnetica
	 * @param descricaoExameRessonanaciaMagnetica the txt_exame_ressonancia_magnetica value
	 */
	public void setDescricaoExameRessonanaciaMagnetica (java.lang.String descricaoExameRessonanaciaMagnetica) {
//        java.lang.String descricaoExameRessonanaciaMagneticaOld = this.descricaoExameRessonanaciaMagnetica;
		this.descricaoExameRessonanaciaMagnetica = descricaoExameRessonanaciaMagnetica;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoExameRessonanaciaMagnetica", descricaoExameRessonanaciaMagneticaOld, descricaoExameRessonanaciaMagnetica);
	}



	/**
	 * Return the value associated with the column: obito
	 */
	public java.lang.Long getObito () {
		return getPropertyValue(this, obito, PROP_OBITO); 
	}

	/**
	 * Set the value related to the column: obito
	 * @param obito the obito value
	 */
	public void setObito (java.lang.Long obito) {
//        java.lang.Long obitoOld = this.obito;
		this.obito = obito;
//        this.getPropertyChangeSupport().firePropertyChange ("obito", obitoOld, obito);
	}



	/**
	 * Return the value associated with the column: nr_declaracao_obito
	 */
	public java.lang.String getNumeroDeclaracaoObito () {
		return getPropertyValue(this, numeroDeclaracaoObito, PROP_NUMERO_DECLARACAO_OBITO); 
	}

	/**
	 * Set the value related to the column: nr_declaracao_obito
	 * @param numeroDeclaracaoObito the nr_declaracao_obito value
	 */
	public void setNumeroDeclaracaoObito (java.lang.String numeroDeclaracaoObito) {
//        java.lang.String numeroDeclaracaoObitoOld = this.numeroDeclaracaoObito;
		this.numeroDeclaracaoObito = numeroDeclaracaoObito;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDeclaracaoObito", numeroDeclaracaoObitoOld, numeroDeclaracaoObito);
	}



	/**
	 * Return the value associated with the column: dt_obito
	 */
	public java.util.Date getDataObito () {
		return getPropertyValue(this, dataObito, PROP_DATA_OBITO); 
	}

	/**
	 * Set the value related to the column: dt_obito
	 * @param dataObito the dt_obito value
	 */
	public void setDataObito (java.util.Date dataObito) {
//        java.util.Date dataObitoOld = this.dataObito;
		this.dataObito = dataObito;
//        this.getPropertyChangeSupport().firePropertyChange ("dataObito", dataObitoOld, dataObito);
	}



	/**
	 * Return the value associated with the column: obito_neonatal_precoce
	 */
	public java.lang.Long getObitoNeonatalPrecoce () {
		return getPropertyValue(this, obitoNeonatalPrecoce, PROP_OBITO_NEONATAL_PRECOCE); 
	}

	/**
	 * Set the value related to the column: obito_neonatal_precoce
	 * @param obitoNeonatalPrecoce the obito_neonatal_precoce value
	 */
	public void setObitoNeonatalPrecoce (java.lang.Long obitoNeonatalPrecoce) {
//        java.lang.Long obitoNeonatalPrecoceOld = this.obitoNeonatalPrecoce;
		this.obitoNeonatalPrecoce = obitoNeonatalPrecoce;
//        this.getPropertyChangeSupport().firePropertyChange ("obitoNeonatalPrecoce", obitoNeonatalPrecoceOld, obitoNeonatalPrecoce);
	}



	/**
	 * Return the value associated with the column: txt_observacoes
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: txt_observacoes
	 * @param observacao the txt_observacoes value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: cd_registro_agravo
	 */
	public br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo getRegistroAgravo () {
		return getPropertyValue(this, registroAgravo, PROP_REGISTRO_AGRAVO); 
	}

	/**
	 * Set the value related to the column: cd_registro_agravo
	 * @param registroAgravo the cd_registro_agravo value
	 */
	public void setRegistroAgravo (br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo) {
//        br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravoOld = this.registroAgravo;
		this.registroAgravo = registroAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("registroAgravo", registroAgravoOld, registroAgravo);
	}



	/**
	 * Return the value associated with the column: estabelecimento_saude_parto
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEstabelecimentoSaudeParto () {
		return getPropertyValue(this, estabelecimentoSaudeParto, PROP_ESTABELECIMENTO_SAUDE_PARTO); 
	}

	/**
	 * Set the value related to the column: estabelecimento_saude_parto
	 * @param estabelecimentoSaudeParto the estabelecimento_saude_parto value
	 */
	public void setEstabelecimentoSaudeParto (br.com.ksisolucoes.vo.basico.Empresa estabelecimentoSaudeParto) {
//        br.com.ksisolucoes.vo.basico.Empresa estabelecimentoSaudePartoOld = this.estabelecimentoSaudeParto;
		this.estabelecimentoSaudeParto = estabelecimentoSaudeParto;
//        this.getPropertyChangeSupport().firePropertyChange ("estabelecimentoSaudeParto", estabelecimentoSaudePartoOld, estabelecimentoSaudeParto);
	}



	/**
	 * Return the value associated with the column: cd_usuario_encerramento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioEncerramento () {
		return getPropertyValue(this, usuarioEncerramento, PROP_USUARIO_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_encerramento
	 * @param usuarioEncerramento the cd_usuario_encerramento value
	 */
	public void setUsuarioEncerramento (br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramentoOld = this.usuarioEncerramento;
		this.usuarioEncerramento = usuarioEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioEncerramento", usuarioEncerramentoOld, usuarioEncerramento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoMicrocefalia)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoMicrocefalia investigacaoAgravoMicrocefalia = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoMicrocefalia) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoMicrocefalia.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoMicrocefalia.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}