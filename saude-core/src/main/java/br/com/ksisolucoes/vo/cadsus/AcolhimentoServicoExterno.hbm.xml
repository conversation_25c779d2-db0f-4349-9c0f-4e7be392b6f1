<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.cadsus">

    <class name="AcolhimentoServicoExterno" table="acolhimento_servico_externo">

        <id name="codigo" type="java.lang.Long" column="cd_acolhimento_externo">
            <generator class="assigned"/>
        </id>

        <version column="version" name="version" type="long"/>

        <many-to-one name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus" not-null="true">
            <column name="cd_usu_cadsus"/>
        </many-to-one>

        <property name="dadoAtendimento" column="dados_atendimento" type="java.lang.String"/>

        <property name="dataCadastro" column="dt_cadastro" type="timestamp" not-null="true"/>

        <many-to-one class="br.com.ksisolucoes.vo.controle.Usuario" column="cd_usuario" name="usuario"/>

        <many-to-one class="br.com.ksisolucoes.vo.basico.Empresa" column="cd_empresa" name="empresa"/>
    </class>

</hibernate-mapping>