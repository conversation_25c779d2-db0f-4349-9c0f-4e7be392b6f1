package br.com.ksisolucoes.vo.emprestimo.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the devolucao_emprestimo_elo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="devolucao_emprestimo_elo"
 */

public abstract class BaseDevolucaoEmprestimoElo extends BaseRootVO implements Serializable {

	public static String REF = "DevolucaoEmprestimoElo";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DEVOLUCAO_EMPRESTIMO_ITEM = "devolucaoEmprestimoItem";
	public static final String PROP_LANCAMENTO_EMPRESTIMO_ITEM = "lancamentoEmprestimoItem";
	public static final String PROP_QUANTIDADE = "quantidade";


	// constructors
	public BaseDevolucaoEmprestimoElo () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseDevolucaoEmprestimoElo (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseDevolucaoEmprestimoElo (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.emprestimo.LancamentoEmprestimoItem lancamentoEmprestimoItem,
		br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimoItem devolucaoEmprestimoItem,
		java.lang.Double quantidade) {

		this.setCodigo(codigo);
		this.setLancamentoEmprestimoItem(lancamentoEmprestimoItem);
		this.setDevolucaoEmprestimoItem(devolucaoEmprestimoItem);
		this.setQuantidade(quantidade);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Double quantidade;

	// many to one
	private br.com.ksisolucoes.vo.emprestimo.LancamentoEmprestimoItem lancamentoEmprestimoItem;
	private br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimoItem devolucaoEmprestimoItem;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_dev_emprestimo_elo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: quantidade
	 */
	public java.lang.Double getQuantidade () {
		return getPropertyValue(this, quantidade, PROP_QUANTIDADE); 
	}

	/**
	 * Set the value related to the column: quantidade
	 * @param quantidade the quantidade value
	 */
	public void setQuantidade (java.lang.Double quantidade) {
//        java.lang.Double quantidadeOld = this.quantidade;
		this.quantidade = quantidade;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidade", quantidadeOld, quantidade);
	}



	/**
	 * Return the value associated with the column: cd_emprestimo_item
	 */
	public br.com.ksisolucoes.vo.emprestimo.LancamentoEmprestimoItem getLancamentoEmprestimoItem () {
		return getPropertyValue(this, lancamentoEmprestimoItem, PROP_LANCAMENTO_EMPRESTIMO_ITEM); 
	}

	/**
	 * Set the value related to the column: cd_emprestimo_item
	 * @param lancamentoEmprestimoItem the cd_emprestimo_item value
	 */
	public void setLancamentoEmprestimoItem (br.com.ksisolucoes.vo.emprestimo.LancamentoEmprestimoItem lancamentoEmprestimoItem) {
//        br.com.ksisolucoes.vo.emprestimo.LancamentoEmprestimoItem lancamentoEmprestimoItemOld = this.lancamentoEmprestimoItem;
		this.lancamentoEmprestimoItem = lancamentoEmprestimoItem;
//        this.getPropertyChangeSupport().firePropertyChange ("lancamentoEmprestimoItem", lancamentoEmprestimoItemOld, lancamentoEmprestimoItem);
	}



	/**
	 * Return the value associated with the column: cd_dev_emprestimo_item
	 */
	public br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimoItem getDevolucaoEmprestimoItem () {
		return getPropertyValue(this, devolucaoEmprestimoItem, PROP_DEVOLUCAO_EMPRESTIMO_ITEM); 
	}

	/**
	 * Set the value related to the column: cd_dev_emprestimo_item
	 * @param devolucaoEmprestimoItem the cd_dev_emprestimo_item value
	 */
	public void setDevolucaoEmprestimoItem (br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimoItem devolucaoEmprestimoItem) {
//        br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimoItem devolucaoEmprestimoItemOld = this.devolucaoEmprestimoItem;
		this.devolucaoEmprestimoItem = devolucaoEmprestimoItem;
//        this.getPropertyChangeSupport().firePropertyChange ("devolucaoEmprestimoItem", devolucaoEmprestimoItemOld, devolucaoEmprestimoItem);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimoElo)) return false;
		else {
			br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimoElo devolucaoEmprestimoElo = (br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimoElo) obj;
			if (null == this.getCodigo() || null == devolucaoEmprestimoElo.getCodigo()) return false;
			else return (this.getCodigo().equals(devolucaoEmprestimoElo.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}