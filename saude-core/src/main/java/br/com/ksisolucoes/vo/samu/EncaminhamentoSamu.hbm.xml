<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.samu"  >
    <class name="EncaminhamentoSamu" table="encaminhamento_samu" >
        <id
            column="cd_encaminhamento"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />    
        
        <property
            column="ds_encaminhamento"
            name="descricaoEncaminhamento"
            not-null="true"
            type="java.lang.String"
            length="50"
        />
        
    </class>
</hibernate-mapping>