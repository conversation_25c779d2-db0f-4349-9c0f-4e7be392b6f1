package br.com.ksisolucoes.vo.prontuario.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the tipo_encaminhamento_criterio table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="tipo_encaminhamento_criterio"
 */

public abstract class BaseTipoEncaminhamentoCriterio extends BaseRootVO implements Serializable {

	public static String REF = "TipoEncaminhamentoCriterio";
	public static final String PROP_TIPO_ENCAMINHAMENTO = "tipoEncaminhamento";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_FLAG_OBRIGATORIO = "flagObrigatorio";
	public static final String PROP_DESCRICAO = "descricao";


	// constructors
	public BaseTipoEncaminhamentoCriterio () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseTipoEncaminhamentoCriterio (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseTipoEncaminhamentoCriterio (
		java.lang.Long codigo,
		java.lang.String descricao,
		java.lang.Long flagObrigatorio) {

		this.setCodigo(codigo);
		this.setDescricao(descricao);
		this.setFlagObrigatorio(flagObrigatorio);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.lang.Long flagObrigatorio;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.TipoEncaminhamento tipoEncaminhamento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_tp_enc_criterio"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: descricao
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: descricao
	 * @param descricao the descricao value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: flag_obrigatorio
	 */
	public java.lang.Long getFlagObrigatorio () {
		return getPropertyValue(this, flagObrigatorio, PROP_FLAG_OBRIGATORIO); 
	}

	/**
	 * Set the value related to the column: flag_obrigatorio
	 * @param flagObrigatorio the flag_obrigatorio value
	 */
	public void setFlagObrigatorio (java.lang.Long flagObrigatorio) {
//        java.lang.Long flagObrigatorioOld = this.flagObrigatorio;
		this.flagObrigatorio = flagObrigatorio;
//        this.getPropertyChangeSupport().firePropertyChange ("flagObrigatorio", flagObrigatorioOld, flagObrigatorio);
	}



	/**
	 * Return the value associated with the column: cd_tp_encaminhamento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.TipoEncaminhamento getTipoEncaminhamento () {
		return getPropertyValue(this, tipoEncaminhamento, PROP_TIPO_ENCAMINHAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_tp_encaminhamento
	 * @param tipoEncaminhamento the cd_tp_encaminhamento value
	 */
	public void setTipoEncaminhamento (br.com.ksisolucoes.vo.prontuario.basico.TipoEncaminhamento tipoEncaminhamento) {
//        br.com.ksisolucoes.vo.prontuario.basico.TipoEncaminhamento tipoEncaminhamentoOld = this.tipoEncaminhamento;
		this.tipoEncaminhamento = tipoEncaminhamento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoEncaminhamento", tipoEncaminhamentoOld, tipoEncaminhamento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.TipoEncaminhamentoCriterio)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.TipoEncaminhamentoCriterio tipoEncaminhamentoCriterio = (br.com.ksisolucoes.vo.prontuario.basico.TipoEncaminhamentoCriterio) obj;
			if (null == this.getCodigo() || null == tipoEncaminhamentoCriterio.getCodigo()) return false;
			else return (this.getCodigo().equals(tipoEncaminhamentoCriterio.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}