package br.com.ksisolucoes.vo.prontuario.procedimento;

import br.com.ksisolucoes.associacao.annotations.IdNameSIGTAP;
import br.com.ksisolucoes.vo.prontuario.procedimento.base.BaseTabelaCboSubGrupoPK;

public class TabelaCboSubGrupoPK extends BaseTabelaCboSubGrupoPK {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public TabelaCboSubGrupoPK () {}
	
	public TabelaCboSubGrupoPK (
		br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboGrupo tabelaCboGrupo,
		java.lang.Long codigo) {

		super (
			tabelaCboGrupo,
			codigo);
	}

/*[CONSTRUCTOR MARKER END]*/
    @IdNameSIGTAP("NAO_DEFINIDO")
    @Override
    public Long getCodigo() {
        return super.getCodigo();
    }

    @IdNameSIGTAP
    @Override
    public TabelaCboGrupo getTabelaCboGrupo() {
        return super.getTabelaCboGrupo();
    }


}