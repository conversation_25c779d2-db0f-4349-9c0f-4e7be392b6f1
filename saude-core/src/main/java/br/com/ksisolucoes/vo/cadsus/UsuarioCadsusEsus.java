package br.com.ksisolucoes.vo.cadsus;

import br.com.celk.integracao.IntegracaoRest;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.cadsus.base.BaseUsuarioCadsusEsus;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;

@IntegracaoRest
public class UsuarioCadsusEsus extends BaseUsuarioCadsusEsus implements CodigoManager {

    private static final long serialVersionUID = 1L;
    public static final String PROP_DESCRICAO_PARENTESCO_RESPONSAVEL = "descricaoParentescoResponsavel";
    private boolean mobile;

    public enum ParentescoResponsavel implements IEnum {

        CONJUGE_OU_COMPANHEIRO(137L, Bundle.getStringApplication("rotulo_conjuge_ou_companheiro")),
        FILHO(138L, Bundle.getStringApplication("rotulo_filho")),
        ENTEADO(139L, Bundle.getStringApplication("rotulo_enteado")),
        GENRO_OU_NORA(144L, Bundle.getStringApplication("rotulo_genro_ou_nora")),
        IRMAO_OU_IRMA(143L, Bundle.getStringApplication("rotulo_irmao_ou_irmao")),
        NAO_PARENTE(146L, Bundle.getStringApplication("rotulo_nao_parente")),
        NETO_OU_BISNETO(140L, Bundle.getStringApplication("rotulo_neto_ou_bisneto")),
        OUTRO_PARENTE(145L, Bundle.getStringApplication("rotulo_outro_parente")),
        PAI_OU_MAE(141L, Bundle.getStringApplication("rotulo_pai_ou_mae")),
        SOGRO(142L, Bundle.getStringApplication("rotulo_sogro")),;

        private final Long value;
        private final String descricao;

        private ParentescoResponsavel(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static ParentescoResponsavel valeuOf(Long value) {
            for (ParentescoResponsavel parentescoResponsavel : ParentescoResponsavel.values()) {
                if (parentescoResponsavel.value().equals(value)) {
                    return parentescoResponsavel;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public UsuarioCadsusEsus () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public UsuarioCadsusEsus (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public UsuarioCadsusEsus (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		java.util.Date dataCadastro) {

		super (
			codigo,
			usuarioCadsus,
			dataCadastro);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public enum SituacaoConjugal implements IEnum {

        SOLTEIRO(1L, Bundle.getStringApplication("rotulo_solteiro")),
        CASADO(2L, Bundle.getStringApplication("rotulo_casado")),
        DIVORCIADO(3L, Bundle.getStringApplication("rotulo_divorciado")),
        VIUVO(4L, Bundle.getStringApplication("rotulo_viuvo")),
        OUTRA(5L, Bundle.getStringApplication("rotulo_outra"));

        private final Long value;
        private final String descricao;

        private SituacaoConjugal(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static SituacaoConjugal valueOf(Long value) {
            for (SituacaoConjugal situacaoConjugal : SituacaoConjugal.values()) {
                if (situacaoConjugal.value().equals(value)) {
                    return situacaoConjugal;
                }
            }

            return null;
        }
    }

    public enum NivelEscolaridade implements IEnum {

        CRECHE(51L, Bundle.getStringApplication("rotulo_creche")),
        PRE_ESCOLA(52L, Bundle.getStringApplication("rotulo_pre_escola")),
        CLASSE_ALFABETIZADA(53L, Bundle.getStringApplication("rotulo_classe_alfabetizada")),
        ENSINO_FUNDAMENTAL_1_4(54L, Bundle.getStringApplication("rotulo_ensino_fundamental_1_4")),
        ENSINO_FUNDAMENTAL_5_8(55L, Bundle.getStringApplication("rotulo_ensino_fundamental_5_8")),
        ENSINO_FUNDAMENTAL_COMPLETO(56L, Bundle.getStringApplication("rotulo_ensino_fundamental_completo")),
        ENSINO_FUNDAMENTAL_ESPECIAL(61L, Bundle.getStringApplication("rotulo_ensino_fundamental_especial")),
        ENSINO_FUNDAMENTAL_SUPLETIVO_1_4(58L, Bundle.getStringApplication("rotulo_ensino_fundamental_supletivo_1_4")),
        ENSINO_FUNDAMENTAL_SUPLETIVO_5_8(59L, Bundle.getStringApplication("rotulo_ensino_fundamental_supletivo_5_8")),
        ENSINO_MEDIO(60L, Bundle.getStringApplication("rotulo_ensino_medio_2_ciclo")),
        ENSINO_MEDIO_ESPECIAL(57L, Bundle.getStringApplication("rotulo_ensino_medio_especial")),
        ENSINO_MEDIO_SUPLETIVO(62L, Bundle.getStringApplication("rotulo_ensino_medio_supletivo")),
        SUPERIOR_ESPECIALIZADO(63L, Bundle.getStringApplication("rotulo_superior_especializacao")),
        ALFABETIZACAO_ADULTOS(64L, Bundle.getStringApplication("rotulo_alfabetizacao_adultos")),
        NENHUM(65L, Bundle.getStringApplication("rotulo_nenhum")),;

        private final Long value;
        private final String descricao;

        private NivelEscolaridade(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static NivelEscolaridade valueOf(Long value) {
            for (NivelEscolaridade nivelEscolaridade : NivelEscolaridade.values()) {
                if (nivelEscolaridade.value().equals(value)) {
                    return nivelEscolaridade;
                }
            }

            return null;
        }
    }

    public enum SituacaoMercadoTrabalho implements IEnum {

        EMPREGADOR(66L, Bundle.getStringApplication("rotulo_empregador")),
        ASSALARIADO_COM_CARTEIRA(67L, Bundle.getStringApplication("rotulo_assalariado_com_carteira")),
        ASSALARIADO_SEM_CARTEIRA(68L, Bundle.getStringApplication("rotulo_assalariado_sem_carteira")),
        AUTONOMO_COM_PREVIDENCIA(69L, Bundle.getStringApplication("rotulo_autonomo_com_previdencia")),
        AUTONOMO_SEM_PREVIDENCIA(70L, Bundle.getStringApplication("rotulo_autonomo_sem_previdencia")),
        APOSENTADO(71L, Bundle.getStringApplication("rotulo_aposentado_pensionista")),
        DESEMPREGADO(72L, Bundle.getStringApplication("rotulo_desempregado")),
        NAO_TABALHA(73L, Bundle.getStringApplication("rotulo_nao_trabalha")),
        OUTRO(74L, Bundle.getStringApplication("rotulo_outro")),;

        private final Long value;
        private final String descricao;

        private SituacaoMercadoTrabalho(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static SituacaoMercadoTrabalho valueOf(Long value) {
            for (SituacaoMercadoTrabalho situacaoMercadoTrabalho : SituacaoMercadoTrabalho.values()) {
                if (situacaoMercadoTrabalho.value().equals(value)) {
                    return situacaoMercadoTrabalho;
                }
            }

            return null;
        }
    }

    public enum ComunidadeTradicional implements IEnum {

        AGROEXTRATIVISTAS(1L, Bundle.getStringApplication("rotulo_agroextrativistas"),157L),
        CAATINGUEIROS(2L, Bundle.getStringApplication("rotulo_caatingueiros"),158L),
        CAICARAS(3L, Bundle.getStringApplication("rotulo_caicaras"),159L),
        CERRADO(4L, Bundle.getStringApplication("rotulo_cerrado"),160L),
        CIGANOS(5L, Bundle.getStringApplication("rotulo_ciganos"),161L),
        COMUNIDADES_FUNDO_FECHO_PASTO(6L, Bundle.getStringApplication("rotulo_comunidades_fundo_fecho_pasto"),162L),
        EXTRATIVISTAS(7L, Bundle.getStringApplication("rotulo_extrativistas"),163L),
        FAXINALENSES(8L, Bundle.getStringApplication("rotulo_faxinalenses"),164L),
        GERAIZEROS(9L, Bundle.getStringApplication("rotulo_geraizeros"),165L),
        MARISQUEIROS(10L, Bundle.getStringApplication("rotulo_marisqueiros"),166L),
        PANTANEIROS(11L, Bundle.getStringApplication("rotulo_pantaneiros"),167L),
        PESCADORES_ARTESANAIS(12L, Bundle.getStringApplication("rotulo_pescadores_artesanais"),168L),
        POMERANOS(13L, Bundle.getStringApplication("rotulo_pomeranos"),169L),
        POVOS_INDIGENAS(14L, Bundle.getStringApplication("rotulo_povos_indigenas"),170L),
        POVOS_QUILOMBOLAS(15L, Bundle.getStringApplication("rotulo_povos_quilombolas"),171L),
        QUEBRADEIRAS_COCO_BABACU(16L, Bundle.getStringApplication("rotulo_quebradeiras_coco_babacu"),172L),
        RETIREIROS(17L, Bundle.getStringApplication("rotulo_retireiros"),173L),
        RIBEIRINHOS(18L, Bundle.getStringApplication("rotulo_ribeirinhos"),174L),
        SERINGUEIROS(19L, Bundle.getStringApplication("rotulo_seringueiros"),175L),
        TERREIRO_MATRIZ_AFRICANA(20L, Bundle.getStringApplication("rotulo_terreiro_matriz_africana"),176L),
        VAZANTEIROS(21L, Bundle.getStringApplication("rotulo_vazanteiros"),177L),
        OUTROS(22L, Bundle.getStringApplication("rotulo_outros"),178L),
        POPULACAO_ACAMPADA(23L, Bundle.getStringApplication("rotulo_populacao_acampada"),179L),
        POPULACAO_ANDIROBEIRAS(24L, Bundle.getStringApplication("rotulo_populacao_andirobeiras"),180L),
        POPULACAO_ASSENTADA(25L, Bundle.getStringApplication("rotulo_populacao_assentada"),181L),
        POPULACAO_CAMPONESES(26L, Bundle.getStringApplication("rotulo_populacao_camponeses"),182L),
        POPULACAO_CASTANHEIRAS(27L, Bundle.getStringApplication("rotulo_populacao_castanheiras"),183L),
        POPULACAO_CATADORES_DE_MANGABA(28L, Bundle.getStringApplication("rotulo_populacao_catadores_de_mangaba"),184L),
        POPULACAO_ISQUEIROS(29L, Bundle.getStringApplication("rotulo_populacao_isqueiros"),185L),
        POPULACAO_JANGADEIROS(30L, Bundle.getStringApplication("rotulo_populacao_jangadeiros"),186L),
        POPULACAO_MORROQUIANOS(31L, Bundle.getStringApplication("rotulo_populacao_morroquianos"),187L),
        POPULACAO_ATINGIDAS_POR_BARRAGENS(32L, Bundle.getStringApplication("rotulo_populacao_populações_atingidas_por_barragens"),188L),
        TRABALHADORES_RURAIS_ASSALARIADOS(33L, Bundle.getStringApplication("rotulo_trabalhadores_rurais_assalariados"),189L),
        TRABALHADORES_RURAIS_TEMPORARIOS(34L, Bundle.getStringApplication("rotulo_trabalhadores_rurais_temporários"),190L),
        POPULACAO_VARJEIROS(35L, Bundle.getStringApplication("rotulo_populacao_varjeiros"),191L),
        ;

        private final Long value;
        private final String descricao;
        private final Long codigoEsus;

        private ComunidadeTradicional(Long value, String descricao, Long codigoEsus) {
            this.value = value;
            this.descricao = descricao;
            this.codigoEsus = codigoEsus;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public Long getCodigoEsus() { return codigoEsus; }

        public static ComunidadeTradicional valueOf(Long value) {
            for (ComunidadeTradicional comunidadeTradicional : ComunidadeTradicional.values()) {
                if (comunidadeTradicional.value().equals(value)) {
                    return comunidadeTradicional;
                }
            }

            return null;
        }
    }

    public enum ResponsavelCrianca implements IEnum {

        ADULTO_RESPONSAVEL(1L, Bundle.getStringApplication("rotulo_adulto_responsavel")),
        OUTRA_CRIANCA(2L, Bundle.getStringApplication("rotulo_outra_crianca")),
        ADOLESCENTE(133L, Bundle.getStringApplication("rotulo_adolescente")),
        SOZINHA(3L, Bundle.getStringApplication("rotulo_sozinha")),
        CRECHE(134L, Bundle.getStringApplication("rotulo_creche")),
        OUTRO(4L, Bundle.getStringApplication("rotulo_outro"));

        private final Long value;
        private final String descricao;

        private ResponsavelCrianca(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static ResponsavelCrianca valueOf(Long value) {
            for (ResponsavelCrianca responsavelCrianca : ResponsavelCrianca.values()) {
                if (responsavelCrianca.value().equals(value)) {
                    return responsavelCrianca;
                }
            }

            return null;
        }
    }

    public enum OrientacaoSexual implements IEnum {

        HETEROSEXUAL(5L, 148L, Bundle.getStringApplication("rotulo_heterossexual")),
//        HOMOSSEXUAL(6L, 153L, Bundle.getStringApplication("rotulo_homossexual")), INATIVADO
        LESBICA(7L, 197L, Bundle.getStringApplication("rotulo_lesbica")),
        BISSEXUAL(8L, 154L, Bundle.getStringApplication("rotulo_bissexual")),
        ASSEXUAL(9L, 198L, Bundle.getStringApplication("rotulo_assexual")),
        PANSEXUAL(10L, 199L, Bundle.getStringApplication("rotulo_panssexual")),
        OUTRO(11L, 155L, Bundle.getStringApplication("rotulo_outro")),
        GAY(12L, 196L, Bundle.getStringApplication("rotulo_gay"));

        private final Long value;
        private final Long codigoEsus;
        private final String descricao;

        OrientacaoSexual(Long value, Long codigoEsus, String descricao) {
            this.value = value;
            this.codigoEsus = codigoEsus;
            this.descricao = descricao;
        }

        public static OrientacaoSexual valueOf(Long value) {
            if (value == null) {
                return null;
            }
            for (OrientacaoSexual orientacaoSexual : OrientacaoSexual.values()) {
                if (orientacaoSexual.value().equals(value)) {
                    return orientacaoSexual;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public Long getCodigoEsus() {
            return codigoEsus;
        }
    }

    public enum TempoSituacaoRua implements IEnum {

        MENOS_6_MESES(17L, Bundle.getStringApplication("rotulo_menos_6_meses")),
        ENTRE_6_12_MESES(18L, Bundle.getStringApplication("rotulo_6_12_meses")),
        ENTRE_1_5_ANOS(19L, Bundle.getStringApplication("rotulo_1_5_anos")),
        MAIS_5_ANOS(20L, Bundle.getStringApplication("rotulo_mais_5_anos")),;

        private final Long value;
        private final String descricao;

        private TempoSituacaoRua(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static TempoSituacaoRua valueOf(Long value) {
            for (TempoSituacaoRua tempoSituacaoRua : TempoSituacaoRua.values()) {
                if (tempoSituacaoRua.value().equals(value)) {
                    return tempoSituacaoRua;
                }
            }
            return null;
        }
    }

    public enum ConsideracaoPeso implements IEnum {

        ABAIXO_PESO(21L, Bundle.getStringApplication("rotulo_abaixo_peso")),
        PESO_ADEQUADO(22L, Bundle.getStringApplication("rotulo_peso_adequado")),
        ACIMA_PESO(23L, Bundle.getStringApplication("rotulo_acima_peso")),;

        private final Long value;
        private final String descricao;

        private ConsideracaoPeso(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static ConsideracaoPeso valueOf(Long value) {
            for (ConsideracaoPeso consideracaoPeso : ConsideracaoPeso.values()) {
                if (consideracaoPeso.value().equals(value)) {
                    return consideracaoPeso;
                }
            }
            return null;
        }
    }

    public enum RefeicoesDia implements IEnum {

        UMA_VEZ(34L, Bundle.getStringApplication("rotulo_uma_vez")),
        DUAS_OU_TRES_VEZES(35L, Bundle.getStringApplication("rotulo_duas_ou_tres_vezes")),
        MAIS_DE_TRES_VEZES(36L, Bundle.getStringApplication("rotulo_mais_tres_vezes")),;

        private final Long value;
        private final String descricao;

        private RefeicoesDia(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static RefeicoesDia valueOf(Long value) {
            for (RefeicoesDia refeicoesDia : RefeicoesDia.values()) {
                if (refeicoesDia.value().equals(value)) {
                    return refeicoesDia;
                }
            }
            return null;
        }
    }

    public enum DeficienciaEsus implements IEnum {

        AUDITIVA(12L, Bundle.getStringApplication("rotulo_auditiva")),
        VISUAL(13L, Bundle.getStringApplication("rotulo_visual")),
        INTELECTUAL_OU_COGNITIVA(14L, Bundle.getStringApplication("rotulo_intelectual_cognitiva")),
        FISICA(15L, Bundle.getStringApplication("rotulo_fisica")),
        OUTRA(16L, Bundle.getStringApplication("rotulo_outra"));

        private final Long value;
        private final String descricao;

        private DeficienciaEsus(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static DeficienciaEsus valueOf(Long value) {
            for (DeficienciaEsus deficienciaEsus : DeficienciaEsus.values()) {
                if (deficienciaEsus.value().equals(value)) {
                    return deficienciaEsus;
                }
            }
            return null;
        }

        public Long getValue() {
            return value;
        }
    }

    public enum DoencaCardiacaEsus implements IEnum {

        INSUFICIENCIA_CARDIACA(24L, Bundle.getStringApplication("rotulo_insuficiencia_cardiaca")),
        OUTRO(25L, Bundle.getStringApplication("rotulo_outro")),
        NAO_SABE(26L, Bundle.getStringApplication("rotulo_nao_sabe"));

        private final Long value;
        private final String descricao;

        private DoencaCardiacaEsus(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static DoencaCardiacaEsus valueOf(Long value) {
            for (DoencaCardiacaEsus doencaCardiacaEsus : DoencaCardiacaEsus.values()) {
                if (doencaCardiacaEsus.value().equals(value)) {
                    return doencaCardiacaEsus;
                }
            }
            return null;
        }

        public Long getValue() {
            return value;
        }

    }

    public enum DoencaRespiratoriasEsus implements IEnum {

        ASMA(30L, Bundle.getStringApplication("rotulo_asma")),
        DPOC_ENFISEMA(31L, Bundle.getStringApplication("rotulo_dpoc_enfisema")),
        OUTRO(32L, Bundle.getStringApplication("rotulo_outro")),
        NAO_SABE(33L, Bundle.getStringApplication("rotulo_nao_sabe"));

        private final Long value;
        private final String descricao;

        private DoencaRespiratoriasEsus(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static DoencaRespiratoriasEsus valueOf(Long value) {
            for (DoencaRespiratoriasEsus doencaRespiratoriasEsus : DoencaRespiratoriasEsus.values()) {
                if (doencaRespiratoriasEsus.value().equals(value)) {
                    return doencaRespiratoriasEsus;
                }
            }
            return null;
        }

        public Long getValue() {
            return value;
        }
    }

    public enum DoencaRinsEsus implements IEnum {

        INSUFICIENCIA_RENAL(27L, Bundle.getStringApplication("rotulo_insuficiencia_renal")),
        OUTRO(28L, Bundle.getStringApplication("rotulo_outro")),
        NAO_SABE(29L, Bundle.getStringApplication("rotulo_nao_sabe"));

        private final Long value;
        private final String descricao;

        private DoencaRinsEsus(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static DoencaRinsEsus valueOf(Long value) {
            for (DoencaRinsEsus doencaRinsEsus : DoencaRinsEsus.values()) {
                if (doencaRinsEsus.value().equals(value)) {
                    return doencaRinsEsus;
                }
            }
            return null;
        }


        public Long getValue() {
            return value;
        }
    }

    public enum AcessoHigieneEmRuaEsus implements IEnum {

        BANHO(42L, Bundle.getStringApplication("rotulo_banho")),
        ACESSO_AO_SANITARIO(43L, Bundle.getStringApplication("rotulo_acesso_sanitario")),
        HIGIENE_BUCAL(44L, Bundle.getStringApplication("rotulo_higiene_bucal")),
        OUTROS(45L, Bundle.getStringApplication("rotulo_outros"));

        private final Long value;
        private final String descricao;

        private AcessoHigieneEmRuaEsus(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static AcessoHigieneEmRuaEsus valueOf(Long value) {
            for (AcessoHigieneEmRuaEsus acessoHigieneEmRuaEsus : AcessoHigieneEmRuaEsus.values()) {
                if (acessoHigieneEmRuaEsus.value().equals(value)) {
                    return acessoHigieneEmRuaEsus;
                }
            }
            return null;
        }

        public Long getValue() {
            return value;
        }
    }

    public enum OrigemAlimentacaoEmRuaEsus implements IEnum {

        RESTAURANTE_POPULAR(37L, Bundle.getStringApplication("rotulo_restaurante_popular")),
        DOACAO_GRUPO_RELIGIOSO(38L, Bundle.getStringApplication("rotulo_doacao_grupo_religioso")),
        DOACAO_RESTAURANTE(39L, Bundle.getStringApplication("rotulo_doacao_restaurante")),
        DOACAO_POPULAR(40L, Bundle.getStringApplication("rotulo_doacao_popular")),
        OUTROS(41L, Bundle.getStringApplication("rotulo_outros"));

        private final Long value;
        private final String descricao;

        private OrigemAlimentacaoEmRuaEsus(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static OrigemAlimentacaoEmRuaEsus valueOf(Long value) {
            for (OrigemAlimentacaoEmRuaEsus origemAlimentacaoEmRuaEsus : OrigemAlimentacaoEmRuaEsus.values()) {
                if (origemAlimentacaoEmRuaEsus.value().equals(value)) {
                    return origemAlimentacaoEmRuaEsus;
                }
            }
            return null;
        }

        public Long getValue() {
            return value;
        }
    }

    public enum IdentidadeGenero implements IEnum {

        HOMEM_TRANSGENERO(149L, Bundle.getStringApplication("homem_transgenero")),
        MULHER_TRANSGENERO(150L, Bundle.getStringApplication("mulher_transgenero")),
        TRAVESTI(156L, Bundle.getStringApplication("travesti")),
        HOMEM_CISGENEREO(200L, Bundle.getStringApplication("rotulo_homem_cisgenero")),
        MULHER_CISGENEREO(201L, Bundle.getStringApplication("rotulo_mulher_cisgenero")),
        TRANSGENERO(202L, Bundle.getStringApplication("transgenero")),
        NAO_BINARIO(203L, Bundle.getStringApplication("rotulo_nao_binario")),
        OUTRO(151L, Bundle.getStringApplication("rotulo_outro")),;

        private final Long value;
        private final String descricao;

        private IdentidadeGenero(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static IdentidadeGenero valeuOf(Long value) {
            for (IdentidadeGenero identidadeGenero : IdentidadeGenero.values()) {
                if (identidadeGenero.value().equals(value)) {
                    return identidadeGenero;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public static String getDescricaoNivelEscolaridade(Long nivelEscolaridade) {
        NivelEscolaridade escolaridade = NivelEscolaridade.valueOf(nivelEscolaridade);
        return escolaridade == null ? null : escolaridade.descricao;
    }

    public String getDescricaoNivelEscolaridade() {
        return getDescricaoNivelEscolaridade(getNivelEscolaridade());
    }

    public String getDescricaoSituacaoMercadoTrabalho() {
        SituacaoMercadoTrabalho situacaoMercadoTrabalho = SituacaoMercadoTrabalho.valueOf(getSituacaoMercadoTrabalho());
        if (situacaoMercadoTrabalho != null) {
            return situacaoMercadoTrabalho.descricao();
        }

        return null;
    }

    public String getDescricaoSituacaoConjugal() {
        SituacaoConjugal situacaoConjugal = SituacaoConjugal.valueOf(getSituacaoConjugal());
        if (situacaoConjugal != null) {
            return situacaoConjugal.descricao();
        }

        return null;
    }

    public String getDescricaoParentescoResponsavel() {
        ParentescoResponsavel parentescoResponsavel = ParentescoResponsavel.valeuOf(getParentescoResponsavel());
        if (parentescoResponsavel != null) {
            return parentescoResponsavel.descricao();
        }

        return null;
    }

    public String getDescricaoFrequentaEscola() {
        if (getFrequentaEscola() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getFrequentaEscola())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoResponsavelCrianca() {
        if (getResponsavelCrianca() != null) {
            ResponsavelCrianca responsavelCrianca = ResponsavelCrianca.valueOf(getResponsavelCrianca());
            if (responsavelCrianca != null) {
                return responsavelCrianca.descricao();
            }
        }

        return null;
    }

    public String getDescricaoFrequentaCurandeira() {
        if (getFrequentaCurandeira() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getFrequentaCurandeira())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoParticipaGrupoComunitario() {
        if (getParticipaGrupoComunitario() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getParticipaGrupoComunitario())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoPossuiPlanoSaude() {
        if (getPossuiPlanoSaude() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getPossuiPlanoSaude())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoMembroComunidadeTradicional() {
        if (getMembroComunidadeTradicional() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getMembroComunidadeTradicional())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoOrientacaoSexual() {
        if (getOrientacaoSexual() != null) {
            OrientacaoSexual orientacaoSexual = OrientacaoSexual.valueOf(getOrientacaoSexual());
            if (orientacaoSexual != null) {
                return orientacaoSexual.descricao();
            }
        }

        return null;
    }

    public String getDescricaoIdentidadeGenero() {
        if (getIdentidadeGenero() != null) {
            IdentidadeGenero identidadeGenero = IdentidadeGenero.valeuOf(getIdentidadeGenero());
            if (identidadeGenero != null) {
                return identidadeGenero.descricao();
            }
        }

        return null;
    }

    public String getDescricaoPossuiDeficiencia() {
        if (getPossuiDeficiencia() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getPossuiDeficiencia())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoUtilizaProtese() {
        if (getUtilizaProtese() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getUtilizaProtese())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoEstaGestante() {
        if (getEstaGestante() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getEstaGestante())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoPesoConsiderado() {
        if (getPesoConsiderado() != null) {
            ConsideracaoPeso consideracaoPeso = ConsideracaoPeso.valueOf(getPesoConsiderado());
            if (consideracaoPeso != null) {
                return consideracaoPeso.descricao();
            }
        }

        return null;
    }

    public String getDescricaoFumante() {
        if (getFumante() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getFumante())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoDependenteAlcool() {
        if (getDependenteAlcool() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getDependenteAlcool())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoDependenteDroga() {
        if (getDependenteDroga() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getDependenteDroga())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoTemHipertensao() {
        if (getTemHipertensao() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getTemHipertensao())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoTemDiabetes() {
        if (getTemDiabetes() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getTemDiabetes())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoTeveAvc() {
        if (getTeveAvc() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getTeveAvc())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoTeveInfarto() {
        if (getTeveInfarto() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getTeveInfarto())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoTemHanseniase() {
        if (getTemHanseniase() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getTemHanseniase())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoTemTuberculose() {
        if (getTemTuberculose() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getTemTuberculose())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoTemTeveCancer() {
        if (getTemTeveCancer() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getTemTeveCancer())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoEstaAcamado() {
        if (getEstaAcamado() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getEstaAcamado())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoEstaDomiciliado() {
        if (getEstaDomiciliado() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getEstaDomiciliado())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoFezTratamentoPsiquiatrico() {
        if (getFezTratamentoPsiquiatrico() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getFezTratamentoPsiquiatrico())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoOutrasPraticasIntegrativas() {
        if (getOutrasPraticasIntegrativas() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getOutrasPraticasIntegrativas())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoDoencaRespiratoria() {
        if (getDoencaRespiratoria() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getDoencaRespiratoria())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoInternacaoAno() {
        if (getInternacaoAno() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getInternacaoAno())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoDoencaCardiaca() {
        if (getDoencaCardiaca() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getDoencaCardiaca())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoDoencaRins() {
        if (getDoencaRins() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getDoencaRins())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoUsaPlantasMedicinais() {
        if (getUsaPlantasMedicinais() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getUsaPlantasMedicinais())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoSituacaoRua() {
        if (getSituacaoRua() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getSituacaoRua())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoTempoRua() {
        if (getTempoRua() != null) {
            TempoSituacaoRua tempoSituacaoRua = TempoSituacaoRua.valueOf(getTempoRua());
            if (tempoSituacaoRua != null) {
                return tempoSituacaoRua.descricao();
            }
        }

        return null;
    }

    public String getDescricaoAcompanhadoPorOutraInstituicao() {
        if (getAcompanhadoPorOutraInstituicao() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getAcompanhadoPorOutraInstituicao())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoVisitaFamiliarFrequentemente() {
        if (getVisitaFamiliarFrequentemente() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getVisitaFamiliarFrequentemente())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoRecebeBeneficio() {
        if (getRecebeBeneficio() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getRecebeBeneficio())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoPossuiReferenciaFamiliar() {
        if (getPossuiReferenciaFamiliar() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getPossuiReferenciaFamiliar())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoAcessoHigienePessoal() {
        if (getAcessoHigienePessoal() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getAcessoHigienePessoal())) {
                return Bundle.getStringApplication("rotulo_sim");
            }
            return Bundle.getStringApplication("rotulo_nao");
        }

        return null;
    }

    public String getDescricaoRefeicoesDia() {
        if (getRefeicoesDia() != null) {
            RefeicoesDia refeicoesDia = RefeicoesDia.valueOf(getRefeicoesDia());
            if (refeicoesDia != null) {
                return refeicoesDia.descricao();
            }
        }

        return null;
    }

    public String getDescricaoCondicao() {
        StringBuilder condicao = new StringBuilder();

        if (RepositoryComponentDefault.SIM_LONG.equals(getTemDiabetes())) {
            condicao.append(Bundle.getStringApplication("sigla_diabetico"));
            condicao.append(", ");
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(getTemHipertensao())) {
            condicao.append(Bundle.getStringApplication("sigla_hipertensao"));
            condicao.append(", ");
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(getEstaGestante())) {
            condicao.append(Bundle.getStringApplication("sigla_gestante"));
            condicao.append(", ");
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(getEstaAcamado())) {
            condicao.append(Bundle.getStringApplication("rotulo_acamado"));
            condicao.append(", ");
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(getTemHanseniase())) {
            condicao.append(Bundle.getStringApplication("sigla_hanseniase"));
            condicao.append(", ");
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(getTemTuberculose())) {
            condicao.append(Bundle.getStringApplication("sigla_tuberculose"));
            condicao.append(", ");
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(getFezTratamentoPsiquiatrico())) {
            condicao.append(Bundle.getStringApplication("sigla_sofrimentoPsiquicoGrave"));
            condicao.append(", ");
        }

        if (condicao.length() > 0) {
            condicao.setLength(condicao.length() - 2);
            condicao.append(".");
        }

        return condicao.toString();
    }

    public boolean isMobile() {
        return mobile;
    }

    public void setMobile(boolean mobile){
        this.mobile = mobile;
    }
}
