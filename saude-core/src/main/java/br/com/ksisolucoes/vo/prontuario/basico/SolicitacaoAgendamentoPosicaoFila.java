package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseSolicitacaoAgendamentoPosicaoFila;

import java.io.Serializable;



public class SolicitacaoAgendamentoPosicaoFila extends BaseSolicitacaoAgendamentoPosicaoFila implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public SolicitacaoAgendamentoPosicaoFila () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public SolicitacaoAgendamentoPosicaoFila (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}