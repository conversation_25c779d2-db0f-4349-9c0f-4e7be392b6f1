package br.com.ksisolucoes.vo.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the produto_complementar_prescricao table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="produto_complementar_prescricao"
 */

public abstract class BaseProdutoComplementarPrescricao extends BaseRootVO implements Serializable {

	public static String REF = "ProdutoComplementarPrescricao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_PRODUTO = "produto";
	public static final String PROP_ORDEM = "ordem";


	// constructors
	public BaseProdutoComplementarPrescricao () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseProdutoComplementarPrescricao (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseProdutoComplementarPrescricao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.entradas.estoque.Produto produto,
		java.lang.Long ordem) {

		this.setCodigo(codigo);
		this.setProduto(produto);
		this.setOrdem(ordem);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long ordem;

	// many to one
	private br.com.ksisolucoes.vo.entradas.estoque.Produto produto;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_produto_compl_presc"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ordem
	 */
	public java.lang.Long getOrdem () {
		return getPropertyValue(this, ordem, PROP_ORDEM); 
	}

	/**
	 * Set the value related to the column: ordem
	 * @param ordem the ordem value
	 */
	public void setOrdem (java.lang.Long ordem) {
//        java.lang.Long ordemOld = this.ordem;
		this.ordem = ordem;
//        this.getPropertyChangeSupport().firePropertyChange ("ordem", ordemOld, ordem);
	}



	/**
	 * Return the value associated with the column: cod_pro
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Produto getProduto () {
		return getPropertyValue(this, produto, PROP_PRODUTO); 
	}

	/**
	 * Set the value related to the column: cod_pro
	 * @param produto the cod_pro value
	 */
	public void setProduto (br.com.ksisolucoes.vo.entradas.estoque.Produto produto) {
//        br.com.ksisolucoes.vo.entradas.estoque.Produto produtoOld = this.produto;
		this.produto = produto;
//        this.getPropertyChangeSupport().firePropertyChange ("produto", produtoOld, produto);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.ProdutoComplementarPrescricao)) return false;
		else {
			br.com.ksisolucoes.vo.basico.ProdutoComplementarPrescricao produtoComplementarPrescricao = (br.com.ksisolucoes.vo.basico.ProdutoComplementarPrescricao) obj;
			if (null == this.getCodigo() || null == produtoComplementarPrescricao.getCodigo()) return false;
			else return (this.getCodigo().equals(produtoComplementarPrescricao.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}