package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the antecedentes_pessoais table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="antecedentes_pessoais"
 */

public abstract class BaseAntecedentesPessooais extends BaseRootVO implements Serializable {

	public static String REF = "AntecedentesPessooais";
	public static final String PROP_CONDICAO_SITUACAO_SAUDE = "condicaoSituacaoSaude";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_OUTROS_PROBLEMAS_PESSOAL = "outrosProblemasPessoal";


	// constructors
	public BaseAntecedentesPessooais () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAntecedentesPessooais (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long condicaoSituacaoSaude;
	private java.lang.String outrosProblemasPessoal;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_antecedentes_pessoal"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: condicao_situacao_saude
	 */
	public java.lang.Long getCondicaoSituacaoSaude () {
		return getPropertyValue(this, condicaoSituacaoSaude, PROP_CONDICAO_SITUACAO_SAUDE); 
	}

	/**
	 * Set the value related to the column: condicao_situacao_saude
	 * @param condicaoSituacaoSaude the condicao_situacao_saude value
	 */
	public void setCondicaoSituacaoSaude (java.lang.Long condicaoSituacaoSaude) {
//        java.lang.Long condicaoSituacaoSaudeOld = this.condicaoSituacaoSaude;
		this.condicaoSituacaoSaude = condicaoSituacaoSaude;
//        this.getPropertyChangeSupport().firePropertyChange ("condicaoSituacaoSaude", condicaoSituacaoSaudeOld, condicaoSituacaoSaude);
	}



	/**
	 * Return the value associated with the column: outros_problemas_pessoal
	 */
	public java.lang.String getOutrosProblemasPessoal () {
		return getPropertyValue(this, outrosProblemasPessoal, PROP_OUTROS_PROBLEMAS_PESSOAL); 
	}

	/**
	 * Set the value related to the column: outros_problemas_pessoal
	 * @param outrosProblemasPessoal the outros_problemas_pessoal value
	 */
	public void setOutrosProblemasPessoal (java.lang.String outrosProblemasPessoal) {
//        java.lang.String outrosProblemasPessoalOld = this.outrosProblemasPessoal;
		this.outrosProblemasPessoal = outrosProblemasPessoal;
//        this.getPropertyChangeSupport().firePropertyChange ("outrosProblemasPessoal", outrosProblemasPessoalOld, outrosProblemasPessoal);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.AntecedentesPessooais)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.AntecedentesPessooais antecedentesPessooais = (br.com.ksisolucoes.vo.prontuario.basico.AntecedentesPessooais) obj;
			if (null == this.getCodigo() || null == antecedentesPessooais.getCodigo()) return false;
			else return (this.getCodigo().equals(antecedentesPessooais.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}