package br.com.ksisolucoes.vo.basico;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.basico.base.BaseParametroRnds;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;



public class ParametroRnds extends BaseParametroRnds implements CodigoManager {
	private static final long serialVersionUID = 1L;

	/*[CONSTRUCTOR MARKER BEGIN]*/
	public ParametroRnds () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ParametroRnds (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ParametroRnds (
			java.lang.Long codigo,
			br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo certificadoDigital) {

		super (
				codigo,
				certificadoDigital);
	}

	/*[CONSTRUCTOR MARKER END]*/

	public void setCodigoManager(Serializable key) {
		this.setCodigo( (java.lang.Long)key );
	}

	public Serializable getCodigoManager() {
		return this.getCodigo();
	}

	public enum Ambiente implements IEnum {

		PRODUCAO(1L, Bundle.getStringApplication("rotulo_producao")),
		HOMOLOGACAO(2L, Bundle.getStringApplication("rotulo_homologacao")),
		;

		private final Long value;
		private final String descricao;

		Ambiente(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static Ambiente valueOf(Long value) {
			for (Ambiente ambiente : Ambiente.values()) {
				if (ambiente.value().equals(value)) {
					return ambiente;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}
	}

	public String getCnsSolicitanteSemPonto() {
		return super.getCnsSolicitante().replaceAll("[^0-9]", "");
	}
}