package br.com.ksisolucoes.vo.vigilancia.requerimentos.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the requerimento_prorrogacao_prazo_item table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="requerimento_prorrogacao_prazo_item"
 */

public abstract class BaseRequerimentoProrrogacaoPrazoItem extends BaseRootVO implements Serializable {

	public static String REF = "RequerimentoProrrogacaoPrazoItem";
	public static final String PROP_OBSERVACAO_FISCAL = "observacaoFiscal";
	public static final String PROP_NOVA_DATA_CUMPRIMENTO = "novaDataCumprimento";
	public static final String PROP_STATUS = "status";
	public static final String PROP_PRAZO_ORIGINAL = "prazoOriginal";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_PRAZO = "prazo";
	public static final String PROP_DATA_CUMPRIMENTO_ORIGINAL = "dataCumprimentoOriginal";
	public static final String PROP_MOTIVO = "motivo";
	public static final String PROP_AUTO_INTIMACAO_EXIGENCIA = "autoIntimacaoExigencia";
	public static final String PROP_REQUERIMENTO_PRORROGACAO_PRAZO = "requerimentoProrrogacaoPrazo";


	// constructors
	public BaseRequerimentoProrrogacaoPrazoItem () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRequerimentoProrrogacaoPrazoItem (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRequerimentoProrrogacaoPrazoItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProrrogacaoPrazo requerimentoProrrogacaoPrazo,
		java.lang.String descricao,
		java.lang.String motivo,
		java.lang.Long status) {

		this.setCodigo(codigo);
		this.setRequerimentoProrrogacaoPrazo(requerimentoProrrogacaoPrazo);
		this.setDescricao(descricao);
		this.setMotivo(motivo);
		this.setStatus(status);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.lang.String motivo;
	private java.lang.Long prazo;
	private java.lang.Long status;
	private java.lang.String observacaoFiscal;
	private java.util.Date novaDataCumprimento;
	private java.util.Date dataCumprimentoOriginal;
	private java.lang.Long prazoOriginal;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProrrogacaoPrazo requerimentoProrrogacaoPrazo;
	private br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacaoExigencia autoIntimacaoExigencia;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_requerimento_prorrogacao_prazo_item"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: descricao
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: descricao
	 * @param descricao the descricao value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: motivo
	 */
	public java.lang.String getMotivo () {
		return getPropertyValue(this, motivo, PROP_MOTIVO); 
	}

	/**
	 * Set the value related to the column: motivo
	 * @param motivo the motivo value
	 */
	public void setMotivo (java.lang.String motivo) {
//        java.lang.String motivoOld = this.motivo;
		this.motivo = motivo;
//        this.getPropertyChangeSupport().firePropertyChange ("motivo", motivoOld, motivo);
	}



	/**
	 * Return the value associated with the column: prazo
	 */
	public java.lang.Long getPrazo () {
		return getPropertyValue(this, prazo, PROP_PRAZO); 
	}

	/**
	 * Set the value related to the column: prazo
	 * @param prazo the prazo value
	 */
	public void setPrazo (java.lang.Long prazo) {
//        java.lang.Long prazoOld = this.prazo;
		this.prazo = prazo;
//        this.getPropertyChangeSupport().firePropertyChange ("prazo", prazoOld, prazo);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: ds_observacao_fiscal
	 */
	public java.lang.String getObservacaoFiscal () {
		return getPropertyValue(this, observacaoFiscal, PROP_OBSERVACAO_FISCAL); 
	}

	/**
	 * Set the value related to the column: ds_observacao_fiscal
	 * @param observacaoFiscal the ds_observacao_fiscal value
	 */
	public void setObservacaoFiscal (java.lang.String observacaoFiscal) {
//        java.lang.String observacaoFiscalOld = this.observacaoFiscal;
		this.observacaoFiscal = observacaoFiscal;
//        this.getPropertyChangeSupport().firePropertyChange ("observacaoFiscal", observacaoFiscalOld, observacaoFiscal);
	}



	/**
	 * Return the value associated with the column: nova_data_cumprimento
	 */
	public java.util.Date getNovaDataCumprimento () {
		return getPropertyValue(this, novaDataCumprimento, PROP_NOVA_DATA_CUMPRIMENTO); 
	}

	/**
	 * Set the value related to the column: nova_data_cumprimento
	 * @param novaDataCumprimento the nova_data_cumprimento value
	 */
	public void setNovaDataCumprimento (java.util.Date novaDataCumprimento) {
//        java.util.Date novaDataCumprimentoOld = this.novaDataCumprimento;
		this.novaDataCumprimento = novaDataCumprimento;
//        this.getPropertyChangeSupport().firePropertyChange ("novaDataCumprimento", novaDataCumprimentoOld, novaDataCumprimento);
	}



	/**
	 * Return the value associated with the column: data_cumprimento_original
	 */
	public java.util.Date getDataCumprimentoOriginal () {
		return getPropertyValue(this, dataCumprimentoOriginal, PROP_DATA_CUMPRIMENTO_ORIGINAL); 
	}

	/**
	 * Set the value related to the column: data_cumprimento_original
	 * @param dataCumprimentoOriginal the data_cumprimento_original value
	 */
	public void setDataCumprimentoOriginal (java.util.Date dataCumprimentoOriginal) {
//        java.util.Date dataCumprimentoOriginalOld = this.dataCumprimentoOriginal;
		this.dataCumprimentoOriginal = dataCumprimentoOriginal;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCumprimentoOriginal", dataCumprimentoOriginalOld, dataCumprimentoOriginal);
	}



	/**
	 * Return the value associated with the column: prazo_original
	 */
	public java.lang.Long getPrazoOriginal () {
		return getPropertyValue(this, prazoOriginal, PROP_PRAZO_ORIGINAL); 
	}

	/**
	 * Set the value related to the column: prazo_original
	 * @param prazoOriginal the prazo_original value
	 */
	public void setPrazoOriginal (java.lang.Long prazoOriginal) {
//        java.lang.Long prazoOriginalOld = this.prazoOriginal;
		this.prazoOriginal = prazoOriginal;
//        this.getPropertyChangeSupport().firePropertyChange ("prazoOriginal", prazoOriginalOld, prazoOriginal);
	}



	/**
	 * Return the value associated with the column: cd_requerimento_prorrogacao_prazo
	 */
	public br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProrrogacaoPrazo getRequerimentoProrrogacaoPrazo () {
		return getPropertyValue(this, requerimentoProrrogacaoPrazo, PROP_REQUERIMENTO_PRORROGACAO_PRAZO); 
	}

	/**
	 * Set the value related to the column: cd_requerimento_prorrogacao_prazo
	 * @param requerimentoProrrogacaoPrazo the cd_requerimento_prorrogacao_prazo value
	 */
	public void setRequerimentoProrrogacaoPrazo (br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProrrogacaoPrazo requerimentoProrrogacaoPrazo) {
//        br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProrrogacaoPrazo requerimentoProrrogacaoPrazoOld = this.requerimentoProrrogacaoPrazo;
		this.requerimentoProrrogacaoPrazo = requerimentoProrrogacaoPrazo;
//        this.getPropertyChangeSupport().firePropertyChange ("requerimentoProrrogacaoPrazo", requerimentoProrrogacaoPrazoOld, requerimentoProrrogacaoPrazo);
	}



	/**
	 * Return the value associated with the column: cd_auto_intimacao_exigencia
	 */
	public br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacaoExigencia getAutoIntimacaoExigencia () {
		return getPropertyValue(this, autoIntimacaoExigencia, PROP_AUTO_INTIMACAO_EXIGENCIA); 
	}

	/**
	 * Set the value related to the column: cd_auto_intimacao_exigencia
	 * @param autoIntimacaoExigencia the cd_auto_intimacao_exigencia value
	 */
	public void setAutoIntimacaoExigencia (br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacaoExigencia autoIntimacaoExigencia) {
//        br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacaoExigencia autoIntimacaoExigenciaOld = this.autoIntimacaoExigencia;
		this.autoIntimacaoExigencia = autoIntimacaoExigencia;
//        this.getPropertyChangeSupport().firePropertyChange ("autoIntimacaoExigencia", autoIntimacaoExigenciaOld, autoIntimacaoExigencia);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProrrogacaoPrazoItem)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProrrogacaoPrazoItem requerimentoProrrogacaoPrazoItem = (br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProrrogacaoPrazoItem) obj;
			if (null == this.getCodigo() || null == requerimentoProrrogacaoPrazoItem.getCodigo()) return false;
			else return (this.getCodigo().equals(requerimentoProrrogacaoPrazoItem.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}