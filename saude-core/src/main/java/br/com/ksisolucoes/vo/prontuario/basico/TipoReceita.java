package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseTipoReceita;

import java.io.Serializable;

public class TipoReceita extends BaseTipoReceita implements CodigoManager, PesquisaObjectInterface {

    private static final long serialVersionUID = 1L;
    public static final String PROP_DESCRICAO_FORMATADA = "DescricaoFormatada";
    public static final String PROP_RECEITA_FORMATADO = "receitaFormatado";
    public static final String RECEITA_AMARELA = "A";
    public static final String RECEITA_AZUL = "B";
    public static final String RECEITA_BRANCA = "C";
    public static final String RECEITA_BASICA = "P";
    public static final String RECEITA_MAGISTRAL = "M";
    public static final String RECEITA_PRESCRICAO_ATENDIMENTO = "T";
    public static final String RECEITA_CONTROLADAS = "S";
    public static final String RECEITA_SOLICITACAO_MATERIAIS = "H";
    public static final String RECEITA_PRESCRICAO_OCULOS = "O";
    public static final String RECEITA_ANTIMICROBIANA = "R";
    public static final String RECEITA_RECEITUARIO = "E";
    public static final String RECEITA_BRANCA_C3 = "G";

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public TipoReceita () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public TipoReceita (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public TipoReceita (
		java.lang.Long codigo,
		java.lang.Long diasMaximoTratamento,
		java.lang.Long flagListaReceita) {

		super (
			codigo,
			diasMaximoTratamento,
			flagListaReceita);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDescricaoVO() {
        return getDescricao();
    }

    public String getIdentificador() {
        return Coalesce.asString(getCodigo());
    }

    public String getDescricaoFormatada() {
        return Util.getDescricaoFormatado(
                Coalesce.asString(this.getCodigo()),
                Coalesce.asString(this.getDescricao()));
    }

    public String getReceitaFormatado() {
        if (TipoReceita.RECEITA_AMARELA.equals(getTipoReceita())) {
            return Bundle.getStringApplication("rotulo_receita_amarela_a");
        } else if (TipoReceita.RECEITA_AZUL.equals(getTipoReceita())) {
            return Bundle.getStringApplication("rotulo_receita_azul_b");
        } else if (TipoReceita.RECEITA_BASICA.equals(getTipoReceita())) {
            return Bundle.getStringApplication("rotulo_receita_basica_p");
        } else if (TipoReceita.RECEITA_BRANCA.equals(getTipoReceita())) {
            return Bundle.getStringApplication("rotulo_receita_branca_c");
        } else if (TipoReceita.RECEITA_MAGISTRAL.equals(getTipoReceita())) {
            return Bundle.getStringApplication("rotulo_magistral");
        } else if (TipoReceita.RECEITA_PRESCRICAO_ATENDIMENTO.equals(getTipoReceita())) {
            return Bundle.getStringApplication("rotulo_prescricao_atendimento");
        } else if (TipoReceita.RECEITA_CONTROLADAS.equals(getTipoReceita())) {
            return Bundle.getStringApplication("rotulo_controladas");
        } else if (TipoReceita.RECEITA_SOLICITACAO_MATERIAIS.equals(getTipoReceita())) {
            return Bundle.getStringApplication("rotulo_solicitacao_materiais");
        } else if (TipoReceita.RECEITA_PRESCRICAO_OCULOS.equals(getTipoReceita())) {
            return Bundle.getStringApplication("rotulo_prescricao_oculos");
        } else if (TipoReceita.RECEITA_ANTIMICROBIANA.equals(getTipoReceita())) {
            return Bundle.getStringApplication("rotulo_receituario_antimicrobiana");
        }  else if (TipoReceita.RECEITA_RECEITUARIO.equals(getTipoReceita())) {
            return Bundle.getStringApplication("rotulo_receita_receituario");
        } else if (TipoReceita.RECEITA_BRANCA_C3.equals(getTipoReceita())) {
            return Bundle.getStringApplication("rotulo_receita_branca_c3");
        }
        return null;
    }
}
