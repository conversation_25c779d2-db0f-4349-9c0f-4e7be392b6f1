package br.com.ksisolucoes.vo.consorcio;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.consorcio.base.BasePedidoLicitacao;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;

public class PedidoLicitacao extends BasePedidoLicitacao implements CodigoManager {

    private static final long serialVersionUID = 1L;
    public static final String PROP_DESCRICAO_STATUS = "descricaoStatus";

    public enum StatusPedidoLicitacao implements IEnum<StatusPedidoLicitacao> {

        ABERTO(0L, Bundle.getStringApplication("rotulo_aberto")),
        AGUARDANDO(1L, Bundle.getStringApplication("rotulo_aguardando")),
        LICITADO(2L, Bundle.getStringApplication("rotulo_licitado")),
        PENDENTE(3L, Bundle.getStringApplication("rotulo_pendente")),
        CANCELADO(4L, Bundle.getStringApplication("rotulo_cancelado")),
        LICITACAO_PENDENTE(5L, Bundle.getStringApplication("rotulo_licitacao_pendente")),
        ;
        private Long value;
        private String descricao;

        private StatusPedidoLicitacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        @Override
        public Long value() {
            return value;
        }
    }

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public PedidoLicitacao() {
        super();
    }

    /**
     * Constructor
     * for
     * primary
     * key
     */
    public PedidoLicitacao(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor
     * for
     * required
     * fields
     */
    public PedidoLicitacao(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.basico.Empresa empresa,
            br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
            java.util.Date dataCadastro,
            java.lang.Long status) {

        super(
                codigo,
                empresa,
                usuarioCadastro,
                dataCadastro,
                status);
    }

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDescricaoStatus() {
        if (getStatus().equals(StatusPedidoLicitacao.ABERTO.value())) {
            return StatusPedidoLicitacao.ABERTO.descricao();
        } else if (getStatus().equals(StatusPedidoLicitacao.AGUARDANDO.value())) {
            return StatusPedidoLicitacao.AGUARDANDO.descricao();
        } else if (getStatus().equals(StatusPedidoLicitacao.CANCELADO.value())) {
            return StatusPedidoLicitacao.CANCELADO.descricao();
        } else if (getStatus().equals(StatusPedidoLicitacao.LICITADO.value())) {
            return StatusPedidoLicitacao.LICITADO.descricao();
        } else if (getStatus().equals(StatusPedidoLicitacao.PENDENTE.value())) {
            return StatusPedidoLicitacao.PENDENTE.descricao();
        } else if (getStatus().equals(StatusPedidoLicitacao.LICITACAO_PENDENTE.value())) {
            return StatusPedidoLicitacao.LICITACAO_PENDENTE.descricao();
        }
        return Bundle.getStringApplication("rotulo_desconhecido");
    }
}