<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="RequisicaoDeteccaoDNAProViralHIV1" table="requisicao_deteccao_dna" >
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_requisicao_deteccao_dna"
        >
            <generator class="assigned"/>
        </id>

        <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao"
            name="exameRequisicao"
            column="cd_exame_requisicao"
            not-null="true"
        />

        <property
            name="motivoSolicitacao"
            column="motivo_solicitacao"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="justificativa"
            column="ds_justificativa"
            type="java.lang.String"
            not-null="false"
            length="100"
        />

        <property
            column="dt_cadastro"
            name="dataCadastro"
            type="timestamp"
            not-null="false"
        />

    </class>
</hibernate-mapping>
