package br.com.ksisolucoes.vo.vigilancia;

import java.io.Serializable;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.base.BaseRegistroInspecaoVigilancia;

public class RegistroInspecaoVigilancia extends BaseRegistroInspecaoVigilancia implements CodigoManager {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public RegistroInspecaoVigilancia() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public RegistroInspecaoVigilancia(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public RegistroInspecaoVigilancia(
            java.lang.Long codigo,
            java.lang.Long resultado) {

        super(
                codigo,
                resultado);
    }

    /*[CONSTRUCTOR MARKER END]*/
    public static enum Resultado implements IEnum<Resultado> {

        DEFERIDO(0L, Bundle.getStringApplication("rotulo_deferido")),
        INDEFERIDO(1L, Bundle.getStringApplication("rotulo_indeferido"));
        private Long value;
        private String descricao;

        private Resultado(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Resultado valeuOf(Long value) {
            for (Resultado resultado : Resultado.values()) {
                if (resultado.value().equals(value)) {
                    return resultado;
                }
            }
            return null;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}