package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the investigacao_agr_sars table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_sars"
 */

public abstract class BaseInvestigacaoAgravoSars extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoSars";
	public static final String PROP_PRESENCA_SINAIS_SINTOMAS_VOMITO = "presencaSinaisSintomasVomito";
	public static final String PROP_LOTE2_VACINA_COVID19 = "lote2VacinaCovid19";
	public static final String PROP_OUTRO_VIRUS_RESPIRATORIO_B_M = "outroVirusRespiratorioBM";
	public static final String PROP_PRESENCA_SINAIS_SINTOMAS_DIARREIA = "presencaSinaisSintomasDiarreia";
	public static final String PROP_DATA_TIPO_SOROLOGICA_SARS_COV2 = "dataTipoSorologicaSarsCov2";
	public static final String PROP_POSITIVO_PARA_OUTRO_VIRUS_B_M = "positivoParaOutroVirusBM";
	public static final String PROP_DATA_TOMOGRAFIA = "dataTomografia";
	public static final String PROP_TIPO_AMOSTRA_SOROLOGICA_SARS_COV2 = "tipoAmostraSorologicaSarsCov2";
	public static final String PROP_FATORES_RISCO_IMUNODEFICIENCIA = "fatoresRiscoImunodeficiencia";
	public static final String PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_PARAINFLUENZA2_T_A = "outroVirusRespiratorioQuaisParainfluenza2TA";
	public static final String PROP_RECEBEU_VACINA_COVID19 = "recebeuVacinaCovid19";
	public static final String PROP_DATA_ENCERRAMENTO = "dataEncerramento";
	public static final String PROP_DATA3DOSE = "data3dose";
	public static final String PROP_SE_MENOR6MESES_MAE_RECEBEU_VACINA = "seMenor6mesesMaeRecebeuVacina";
	public static final String PROP_RAIOX_TORAX = "raioxTorax";
	public static final String PROP_FATORES_RISCO_DOENCA_CARDIOVASCULAR_CRONICA = "fatoresRiscoDoencaCardiovascularCronica";
	public static final String PROP_CLASSIFICACAO_FINAL_CASO = "classificacaoFinalCaso";
	public static final String PROP_TIPO_AMOSTRA = "tipoAmostra";
	public static final String PROP_SE_SIM_QUAL_INFLUENZA_T_A = "seSimQualInfluenzaTA";
	public static final String PROP_FATORES_RISCO_PUERPERA = "fatoresRiscoPuerpera";
	public static final String PROP_OUTRO_ASPECTO_TOMOGRAFIA = "outroAspectoTomografia";
	public static final String PROP_DATA_ALTA_OBITO = "dataAltaObito";
	public static final String PROP_DATA_TIPO_AMOSTRA_SOROLOGICA_SARS_COV2 = "dataTipoAmostraSorologicaSarsCov2";
	public static final String PROP_PRESENCA_SINAIS_SINTOMAS_FADIGA = "presencaSinaisSintomasFadiga";
	public static final String PROP_TIPO_TESTE_ANTIGENOS_VIRAIS = "tipoTesteAntigenosVirais";
	public static final String PROP_QUAL_ANTIVIRAL = "qualAntiviral";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_FABRICANTE3DOSE_T_X_T = "fabricante3doseTXT";
	public static final String PROP_POSITIVO_PARA_OUTRO_VIRUS_T_A = "positivoParaOutroVirusTA";
	public static final String PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_PARAINFLUENZA2_B_M = "outroVirusRespiratorioQuaisParainfluenza2BM";
	public static final String PROP_SE_MENOR6MESES_MAE_AMAMENTA_CRIANCA = "seMenor6mesesMaeAmamentaCrianca";
	public static final String PROP_COLETOU_AMOSTRA = "coletouAmostra";
	public static final String PROP_LOTE3DOSE_T_X_T = "lote3doseTXT";
	public static final String PROP_FATORES_RISCO_SINDROME_DOWN = "fatoresRiscoSindromeDown";
	public static final String PROP_FATORES_RISCO_OBESIDADE_I_M_C = "fatoresRiscoObesidadeIMC";
	public static final String PROP_FATORES_RISCO_DIABETES_MELLITUS = "fatoresRiscoDiabetesMellitus";
	public static final String PROP_PRESENCA_SINAIS_SINTOMAS_OUTRO = "presencaSinaisSintomasOutro";
	public static final String PROP_LOTE1_VACINA_COVID19 = "lote1VacinaCovid19";
	public static final String PROP_FATORES_RISCO_ASMA = "fatoresRiscoAsma";
	public static final String PROP_TIPO_SOROLOGICA_SARS_COV2 = "tipoSorologicaSarsCov2";
	public static final String PROP_FATORES_RISCO_DOENCA_HEPATICA_CRONICA = "fatoresRiscoDoencaHepaticaCronica";
	public static final String PROP_USO_ANTIVIRAL_GRIPE = "usoAntiviralGripe";
	public static final String PROP_LABORATORIO_QUE_REALIZOU_TESTE_ANTIGENICO = "laboratorioQueRealizouTesteAntigenico";
	public static final String PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_PARAINFLUENZA3_T_A = "outroVirusRespiratorioQuaisParainfluenza3TA";
	public static final String PROP_OUTRO_INFLUENZA_B_QUAL_LINHAGEM = "outroInfluenzaBQualLinhagem";
	public static final String PROP_UNIDADE_SAUDE_ATENDIMENTO = "unidadeSaudeAtendimento";
	public static final String PROP_FATORES_RISCO_OUTRA_PNEUMOPATIA_CRONICA = "fatoresRiscoOutraPneumopatiaCronica";
	public static final String PROP_PRESENCA_SINAIS_SINTOMAS_PERDA_PALADAR = "presencaSinaisSintomasPerdaPaladar";
	public static final String PROP_PRESENCA_SINAIS_SINTOMAS_DOR_GARGANTA = "presencaSinaisSintomasDorGarganta";
	public static final String PROP_DATA_GRIPE_ULTIMA_CAMPANHA = "dataGripeUltimaCampanha";
	public static final String PROP_OBSERVACOES_ADICIONAIS = "observacoesAdicionais";
	public static final String PROP_DATA_ANTIGENOS_VIRAIS = "dataAntigenosVirais";
	public static final String PROP_OUTRO_TIPO_AMOSTRA_SOROLOGICA_SARS_COV2 = "outroTipoAmostraSorologicaSarsCov2";
	public static final String PROP_DATA_SE_SIM_MAE_RECEBEU_VACINA = "dataSeSimMaeRecebeuVacina";
	public static final String PROP_OUTRO_VIRUS_RESPIRATORIO_T_A = "outroVirusRespiratorioTA";
	public static final String PROP_LABORATORIO_QUE_REALIZOU_BIOLOGIA_MOLECULAR = "laboratorioQueRealizouBiologiaMolecular";
	public static final String PROP_FABRICANTE2DOSE_T_X_T = "fabricante2doseTXT";
	public static final String PROP_CRITERIO_ENCERRAMENTO = "criterioEncerramento";
	public static final String PROP_REGISTRO_AGRAVO = "registroAgravo";
	public static final String PROP_MEMBRO_POVO_COMUNIDADE_TRADICIONAL_T_X_T = "membroPovoComunidadeTradicionalTXT";
	public static final String PROP_FATORES_RISCO_DOENCA_RENAL_CRONICA = "fatoresRiscoDoencaRenalCronica";
	public static final String PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_PARAINFLUENZA3_B_M = "outroVirusRespiratorioQuaisParainfluenza3BM";
	public static final String PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_BOCAVIRUS_B_M = "outroVirusRespiratorioQuaisBocavirusBM";
	public static final String PROP_OUTRO_ANTIVIRAL = "outroAntiviral";
	public static final String PROP_RESULTADO_METODO_BIOLOGIA_MOLECULAR = "resultadoMetodoBiologiaMolecular";
	public static final String PROP_LOTE4DOSE_T_X_T = "lote4doseTXT";
	public static final String PROP_INFLUENZA_B_QUAL_LINHAGEM = "influenzaBQualLinhagem";
	public static final String PROP_FATORES_RISCO_OUTRO = "fatoresRiscoOutro";
	public static final String PROP_DATA_DOSE2_VACINA_COVID19 = "dataDose2VacinaCovid19";
	public static final String PROP_ASPECTO_TOMOGRAFIA = "aspectoTomografia";
	public static final String PROP_OUTRO_TIPO_AMOSTRA = "outroTipoAmostra";
	public static final String PROP_PRESENCA_SINAIS_SINTOMAS_PERDA_OLFATO = "presencaSinaisSintomasPerdaOlfato";
	public static final String PROP_NUMERO_DO = "numeroDo";
	public static final String PROP_AGENTE_ETIOLOGICO_T_A_POSITIVO_INFLUENZA = "agenteEtiologicoTAPositivoInfluenza";
	public static final String PROP_AGENTE_ETIOLOGICO_B_M_POSITIVO_INFLUENZA = "agenteEtiologicoBMPositivoInfluenza";
	public static final String PROP_PRESENCA_SINAIS_SINTOMAS_TOSSE = "presencaSinaisSintomasTosse";
	public static final String PROP_DATA_RAIOX_TORAX = "dataRaioxTorax";
	public static final String PROP_PROFISSIONAL_RESPONSAVEL = "profissionalResponsavel";
	public static final String PROP_OUTRO_PACIENTE_TRABALHA_CONTATO_DIRETO = "outroPacienteTrabalhaContatoDireto";
	public static final String PROP_OUTRO_TIPO_SOROLOGICA_SARS_COV2 = "outroTipoSorologicaSarsCov2";
	public static final String PROP_DATA_ENTRADA_UTI = "dataEntradaUti";
	public static final String PROP_RESULTADO_LG_M = "resultadoLgM";
	public static final String PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_VIRUS_SINCICIAL_RESPIRATORIO_B_M = "outroVirusRespiratorioQuaisVirusSincicialRespiratorioBM";
	public static final String PROP_PRESENCA_SINAIS_SINTOMAS_DESCONFORTO_RESPIRATORIO = "presencaSinaisSintomasDesconfortoRespiratorio";
	public static final String PROP_DD_PACIENTE_TRABALHA_CONTATO_DIRETO = "ddPacienteTrabalhaContatoDireto";
	public static final String PROP_DD_POSSUI_FATORES_RISCO_COMORBIDADES = "ddPossuiFatoresRiscoComorbidades";
	public static final String PROP_DATA_SE_MAIOR_IGUAL6MESES_MENOR_IGUAL8ANOS_DOSE_UNICA = "dataSeMaiorIgual6mesesMenorIgual8anosDoseUnica";
	public static final String PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_SARS_COV2_T_A = "outroVirusRespiratorioQuaisSarsCov2TA";
	public static final String PROP_OUTRO_INFLUENZA_A_QUAL_SUBTIPO = "outroInfluenzaAQualSubtipo";
	public static final String PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_METAPNEUMOVIRUS_B_M = "outroVirusRespiratorioQuaisMetapneumovirusBM";
	public static final String PROP_DATA4DOSE = "data4dose";
	public static final String PROP_OUTRO_RAIOX_TORAX = "outroRaioxTorax";
	public static final String PROP_RESULTADO_LG_A = "resultadoLgA";
	public static final String PROP_DD_GRUPO_CASO_NOSOCOMIAL = "ddGrupoCasoNosocomial";
	public static final String PROP_PRESENCA_SINAIS_SINTOMAS_FEBRE = "presencaSinaisSintomasFebre";
	public static final String PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_ADENOVIRUS_T_A = "outroVirusRespiratorioQuaisAdenovirusTA";
	public static final String PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_PARAINFLUENZA4_B_M = "outroVirusRespiratorioQuaisParainfluenza4BM";
	public static final String PROP_RESULTADO_LG_G = "resultadoLgG";
	public static final String PROP_FATORES_RISCO_DOENCA_NEUROLOGICA_CRONICA = "fatoresRiscoDoencaNeurologicaCronica";
	public static final String PROP_INTERNADO_UTI = "internadoUti";
	public static final String PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_VIRUS_SINCICIAL_RESPIRATORIO_T_A = "outroVirusRespiratorioQuaisVirusSincicialRespiratorioTA";
	public static final String PROP_EVOLUCAO_CASO = "evolucaoCaso";
	public static final String PROP_DATA_RESULTADO_METODO_BIOLOGIA_MOLECULAR = "dataResultadoMetodoBiologiaMolecular";
	public static final String PROP_FATORES_RISCO_DOENCA_HERMATOLOGICA_CRONICA = "fatoresRiscoDoencaHermatologicaCronica";
	public static final String PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_SARS_COV2_B_M = "outroVirusRespiratorioQuaisSarsCov2BM";
	public static final String PROP_RECEBEU_VACINA_GRIPE_ULTIMA_CAMPANHA = "recebeuVacinaGripeUltimaCampanha";
	public static final String PROP_PRESENCA_SINAIS_SINTOMAS_SATURACAO_O295 = "presencaSinaisSintomasSaturacaoO295";
	public static final String PROP_MEMBRO_POVO_COMUNIDADE_TRADICIONAL_D_D = "membroPovoComunidadeTradicionalDD";
	public static final String PROP_DATA_SE_MAIOR_IGUAL6MESES_MENOR_IGUAL8ANOS_DOSE1 = "dataSeMaiorIgual6mesesMenorIgual8anosDose1";
	public static final String PROP_DATA_SE_MAIOR_IGUAL6MESES_MENOR_IGUAL8ANOS_DOSE2 = "dataSeMaiorIgual6mesesMenorIgual8anosDose2";
	public static final String PROP_PRESENCA_SINAIS_SINTOMAS_DOR_ABDOMINAL = "presencaSinaisSintomasDorAbdominal";
	public static final String PROP_HOUVE_INTERNACAO = "houveInternacao";
	public static final String PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_ADENOVIRUS_B_M = "outroVirusRespiratorioQuaisAdenovirusBM";
	public static final String PROP_DATA_INICIO_TRATAMENTO = "dataInicioTratamento";
	public static final String PROP_OUTRO_CLASSIFICACAO_FINAL_CASO = "outroClassificacaoFinalCaso";
	public static final String PROP_FLAG_INFORMACOES_COMPLEMENTARES = "flagInformacoesComplementares";
	public static final String PROP_DATA_COLETA_AMOSTRA = "dataColetaAmostra";
	public static final String PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_PARAINFLUENZA1_T_A = "outroVirusRespiratorioQuaisParainfluenza1TA";
	public static final String PROP_RESULTADO_TESTE_ANTIGENICO = "resultadoTesteAntigenico";
	public static final String PROP_DATA_CASO_NOSOCOMIAL = "dataCasoNosocomial";
	public static final String PROP_USO_SUPORTE_VENTILATORIO = "usoSuporteVentilatorio";
	public static final String PROP_FABRICANTE_VACINA_COVID19 = "fabricanteVacinaCovid19";
	public static final String PROP_NUMERO_REQUISICAO_GAL = "numeroRequisicaoGal";
	public static final String PROP_FABRICANTE4DOSE_T_X_T = "fabricante4doseTXT";
	public static final String PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_PARAINFLUENZA1_B_M = "outroVirusRespiratorioQuaisParainfluenza1BM";
	public static final String PROP_PRESENCA_SINAIS_SINTOMAS_DISPNEIA = "presencaSinaisSintomasDispneia";
	public static final String PROP_DATA_INTERNACAO_SRAG = "dataInternacaoSrag";
	public static final String PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_RINOVIRUS_B_M = "outroVirusRespiratorioQuaisRinovirusBM";
	public static final String PROP_CIDADE_EMPRESA = "cidadeEmpresa";
	public static final String PROP_DATA_DOSE1_VACINA_COVID19 = "dataDose1VacinaCovid19";
	public static final String PROP_SE_SIM_QUAL_INFLUENZA_B_M = "seSimQualInfluenzaBM";
	public static final String PROP_DATA_SAIDA_UTI = "dataSaidaUti";
	public static final String PROP_INFLUENZA_A_QUAL_SUBTIPO = "influenzaAQualSubtipo";


	// constructors
	public BaseInvestigacaoAgravoSars () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoSars (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoAgravoSars (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		java.lang.String flagInformacoesComplementares) {

		this.setCodigo(codigo);
		this.setRegistroAgravo(registroAgravo);
		this.setFlagInformacoesComplementares(flagInformacoesComplementares);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String flagInformacoesComplementares;
	private java.util.Date dataCasoNosocomial;
	private java.lang.String outroPacienteTrabalhaContatoDireto;
	private java.lang.Long presencaSinaisSintomasFebre;
	private java.lang.Long presencaSinaisSintomasTosse;
	private java.lang.Long presencaSinaisSintomasDorGarganta;
	private java.lang.Long presencaSinaisSintomasDispneia;
	private java.lang.Long presencaSinaisSintomasDesconfortoRespiratorio;
	private java.lang.Long presencaSinaisSintomasSaturacaoO295;
	private java.lang.Long presencaSinaisSintomasDiarreia;
	private java.lang.Long presencaSinaisSintomasVomito;
	private java.lang.Long presencaSinaisSintomasDorAbdominal;
	private java.lang.Long presencaSinaisSintomasFadiga;
	private java.lang.Long presencaSinaisSintomasPerdaOlfato;
	private java.lang.Long presencaSinaisSintomasPerdaPaladar;
	private java.lang.String presencaSinaisSintomasOutro;
	private java.lang.Long fatoresRiscoPuerpera;
	private java.lang.Long fatoresRiscoSindromeDown;
	private java.lang.Long fatoresRiscoDiabetesMellitus;
	private java.lang.Long fatoresRiscoImunodeficiencia;
	private java.lang.Long fatoresRiscoDoencaCardiovascularCronica;
	private java.lang.Long fatoresRiscoDoencaHepaticaCronica;
	private java.lang.Long fatoresRiscoDoencaNeurologicaCronica;
	private java.lang.Long fatoresRiscoDoencaRenalCronica;
	private java.lang.Long fatoresRiscoDoencaHermatologicaCronica;
	private java.lang.Long fatoresRiscoAsma;
	private java.lang.Long fatoresRiscoOutraPneumopatiaCronica;
	private java.lang.Long fatoresRiscoObesidadeIMC;
	private java.lang.String fatoresRiscoOutro;
	private java.lang.Long recebeuVacinaCovid19;
	private java.util.Date dataDose1VacinaCovid19;
	private java.util.Date dataDose2VacinaCovid19;
	private java.lang.String fabricanteVacinaCovid19;
	private java.lang.String lote1VacinaCovid19;
	private java.lang.String lote2VacinaCovid19;
	private java.lang.Long recebeuVacinaGripeUltimaCampanha;
	private java.util.Date dataGripeUltimaCampanha;
	private java.lang.Long seMenor6mesesMaeRecebeuVacina;
	private java.util.Date dataSeSimMaeRecebeuVacina;
	private java.lang.Long seMenor6mesesMaeAmamentaCrianca;
	private java.util.Date dataSeMaiorIgual6mesesMenorIgual8anosDoseUnica;
	private java.util.Date dataSeMaiorIgual6mesesMenorIgual8anosDose1;
	private java.util.Date dataSeMaiorIgual6mesesMenorIgual8anosDose2;
	private java.lang.Long usoAntiviralGripe;
	private java.lang.Long qualAntiviral;
	private java.lang.String outroAntiviral;
	private java.util.Date dataInicioTratamento;
	private java.lang.Long houveInternacao;
	private java.util.Date dataInternacaoSrag;
	private java.lang.Long internadoUti;
	private java.util.Date dataEntradaUti;
	private java.util.Date dataSaidaUti;
	private java.lang.Long usoSuporteVentilatorio;
	private java.lang.Long raioxTorax;
	private java.util.Date dataRaioxTorax;
	private java.lang.String outroRaioxTorax;
	private java.lang.Long aspectoTomografia;
	private java.lang.String outroAspectoTomografia;
	private java.util.Date dataTomografia;
	private java.lang.Long coletouAmostra;
	private java.util.Date dataColetaAmostra;
	private java.lang.Long tipoAmostra;
	private java.lang.String outroTipoAmostra;
	private java.lang.String numeroRequisicaoGal;
	private java.lang.Long tipoTesteAntigenosVirais;
	private java.util.Date dataAntigenosVirais;
	private java.lang.Long resultadoTesteAntigenico;
	private java.lang.Long agenteEtiologicoTAPositivoInfluenza;
	private java.lang.Long seSimQualInfluenzaTA;
	private java.lang.Long positivoParaOutroVirusTA;
	private java.lang.Long outroVirusRespiratorioQuaisSarsCov2TA;
	private java.lang.Long outroVirusRespiratorioQuaisVirusSincicialRespiratorioTA;
	private java.lang.Long outroVirusRespiratorioQuaisParainfluenza1TA;
	private java.lang.Long outroVirusRespiratorioQuaisParainfluenza2TA;
	private java.lang.Long outroVirusRespiratorioQuaisParainfluenza3TA;
	private java.lang.Long outroVirusRespiratorioQuaisAdenovirusTA;
	private java.lang.String outroVirusRespiratorioTA;
	private java.lang.Long resultadoMetodoBiologiaMolecular;
	private java.util.Date dataResultadoMetodoBiologiaMolecular;
	private java.lang.Long seSimQualInfluenzaBM;
	private java.lang.Long agenteEtiologicoBMPositivoInfluenza;
	private java.lang.Long influenzaAQualSubtipo;
	private java.lang.String outroInfluenzaAQualSubtipo;
	private java.lang.Long influenzaBQualLinhagem;
	private java.lang.String outroInfluenzaBQualLinhagem;
	private java.lang.Long positivoParaOutroVirusBM;
	private java.lang.Long outroVirusRespiratorioQuaisSarsCov2BM;
	private java.lang.Long outroVirusRespiratorioQuaisVirusSincicialRespiratorioBM;
	private java.lang.Long outroVirusRespiratorioQuaisParainfluenza1BM;
	private java.lang.Long outroVirusRespiratorioQuaisParainfluenza2BM;
	private java.lang.Long outroVirusRespiratorioQuaisParainfluenza3BM;
	private java.lang.Long outroVirusRespiratorioQuaisParainfluenza4BM;
	private java.lang.Long outroVirusRespiratorioQuaisAdenovirusBM;
	private java.lang.Long outroVirusRespiratorioQuaisMetapneumovirusBM;
	private java.lang.Long outroVirusRespiratorioQuaisBocavirusBM;
	private java.lang.Long outroVirusRespiratorioQuaisRinovirusBM;
	private java.lang.String outroVirusRespiratorioBM;
	private java.lang.Long tipoAmostraSorologicaSarsCov2;
	private java.lang.String outroTipoAmostraSorologicaSarsCov2;
	private java.util.Date dataTipoAmostraSorologicaSarsCov2;
	private java.lang.Long tipoSorologicaSarsCov2;
	private java.lang.String outroTipoSorologicaSarsCov2;
	private java.util.Date dataTipoSorologicaSarsCov2;
	private java.lang.Long resultadoLgG;
	private java.lang.Long resultadoLgM;
	private java.lang.Long resultadoLgA;
	private java.lang.Long classificacaoFinalCaso;
	private java.lang.String outroClassificacaoFinalCaso;
	private java.lang.Long criterioEncerramento;
	private java.lang.Long evolucaoCaso;
	private java.util.Date dataAltaObito;
	private java.util.Date dataEncerramento;
	private java.lang.String numeroDo;
	private java.lang.String observacoesAdicionais;
	private java.lang.Long membroPovoComunidadeTradicionalDD;
	private java.lang.String membroPovoComunidadeTradicionalTXT;
	private java.util.Date data3dose;
	private java.util.Date data4dose;
	private java.lang.String fabricante2doseTXT;
	private java.lang.String fabricante3doseTXT;
	private java.lang.String fabricante4doseTXT;
	private java.lang.String lote3doseTXT;
	private java.lang.String lote4doseTXT;
	private java.lang.Long ddGrupoCasoNosocomial;
	private java.lang.Long ddPacienteTrabalhaContatoDireto;
	private java.lang.Long ddPossuiFatoresRiscoComorbidades;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo;
	private br.com.ksisolucoes.vo.basico.Cidade cidadeEmpresa;
	private br.com.ksisolucoes.vo.basico.Empresa unidadeSaudeAtendimento;
	private br.com.ksisolucoes.vo.basico.Empresa laboratorioQueRealizouTesteAntigenico;
	private br.com.ksisolucoes.vo.basico.Empresa laboratorioQueRealizouBiologiaMolecular;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissionalResponsavel;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_invest_agr_sars"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: flag_informacoes_complementares
	 */
	public java.lang.String getFlagInformacoesComplementares () {
		return getPropertyValue(this, flagInformacoesComplementares, PROP_FLAG_INFORMACOES_COMPLEMENTARES); 
	}

	/**
	 * Set the value related to the column: flag_informacoes_complementares
	 * @param flagInformacoesComplementares the flag_informacoes_complementares value
	 */
	public void setFlagInformacoesComplementares (java.lang.String flagInformacoesComplementares) {
//        java.lang.String flagInformacoesComplementaresOld = this.flagInformacoesComplementares;
		this.flagInformacoesComplementares = flagInformacoesComplementares;
//        this.getPropertyChangeSupport().firePropertyChange ("flagInformacoesComplementares", flagInformacoesComplementaresOld, flagInformacoesComplementares);
	}



	/**
	 * Return the value associated with the column: dt_caso_nosocomial
	 */
	public java.util.Date getDataCasoNosocomial () {
		return getPropertyValue(this, dataCasoNosocomial, PROP_DATA_CASO_NOSOCOMIAL); 
	}

	/**
	 * Set the value related to the column: dt_caso_nosocomial
	 * @param dataCasoNosocomial the dt_caso_nosocomial value
	 */
	public void setDataCasoNosocomial (java.util.Date dataCasoNosocomial) {
//        java.util.Date dataCasoNosocomialOld = this.dataCasoNosocomial;
		this.dataCasoNosocomial = dataCasoNosocomial;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCasoNosocomial", dataCasoNosocomialOld, dataCasoNosocomial);
	}



	/**
	 * Return the value associated with the column: outro_paciente_trabalha_contato_direto
	 */
	public java.lang.String getOutroPacienteTrabalhaContatoDireto () {
		return getPropertyValue(this, outroPacienteTrabalhaContatoDireto, PROP_OUTRO_PACIENTE_TRABALHA_CONTATO_DIRETO); 
	}

	/**
	 * Set the value related to the column: outro_paciente_trabalha_contato_direto
	 * @param outroPacienteTrabalhaContatoDireto the outro_paciente_trabalha_contato_direto value
	 */
	public void setOutroPacienteTrabalhaContatoDireto (java.lang.String outroPacienteTrabalhaContatoDireto) {
//        java.lang.String outroPacienteTrabalhaContatoDiretoOld = this.outroPacienteTrabalhaContatoDireto;
		this.outroPacienteTrabalhaContatoDireto = outroPacienteTrabalhaContatoDireto;
//        this.getPropertyChangeSupport().firePropertyChange ("outroPacienteTrabalhaContatoDireto", outroPacienteTrabalhaContatoDiretoOld, outroPacienteTrabalhaContatoDireto);
	}



	/**
	 * Return the value associated with the column: presenca_sinais_sintomas_febre
	 */
	public java.lang.Long getPresencaSinaisSintomasFebre () {
		return getPropertyValue(this, presencaSinaisSintomasFebre, PROP_PRESENCA_SINAIS_SINTOMAS_FEBRE); 
	}

	/**
	 * Set the value related to the column: presenca_sinais_sintomas_febre
	 * @param presencaSinaisSintomasFebre the presenca_sinais_sintomas_febre value
	 */
	public void setPresencaSinaisSintomasFebre (java.lang.Long presencaSinaisSintomasFebre) {
//        java.lang.Long presencaSinaisSintomasFebreOld = this.presencaSinaisSintomasFebre;
		this.presencaSinaisSintomasFebre = presencaSinaisSintomasFebre;
//        this.getPropertyChangeSupport().firePropertyChange ("presencaSinaisSintomasFebre", presencaSinaisSintomasFebreOld, presencaSinaisSintomasFebre);
	}



	/**
	 * Return the value associated with the column: presenca_sinais_sintomas_Tosse
	 */
	public java.lang.Long getPresencaSinaisSintomasTosse () {
		return getPropertyValue(this, presencaSinaisSintomasTosse, PROP_PRESENCA_SINAIS_SINTOMAS_TOSSE); 
	}

	/**
	 * Set the value related to the column: presenca_sinais_sintomas_Tosse
	 * @param presencaSinaisSintomasTosse the presenca_sinais_sintomas_Tosse value
	 */
	public void setPresencaSinaisSintomasTosse (java.lang.Long presencaSinaisSintomasTosse) {
//        java.lang.Long presencaSinaisSintomasTosseOld = this.presencaSinaisSintomasTosse;
		this.presencaSinaisSintomasTosse = presencaSinaisSintomasTosse;
//        this.getPropertyChangeSupport().firePropertyChange ("presencaSinaisSintomasTosse", presencaSinaisSintomasTosseOld, presencaSinaisSintomasTosse);
	}



	/**
	 * Return the value associated with the column: presenca_sinais_sintomas_dor_garganta
	 */
	public java.lang.Long getPresencaSinaisSintomasDorGarganta () {
		return getPropertyValue(this, presencaSinaisSintomasDorGarganta, PROP_PRESENCA_SINAIS_SINTOMAS_DOR_GARGANTA); 
	}

	/**
	 * Set the value related to the column: presenca_sinais_sintomas_dor_garganta
	 * @param presencaSinaisSintomasDorGarganta the presenca_sinais_sintomas_dor_garganta value
	 */
	public void setPresencaSinaisSintomasDorGarganta (java.lang.Long presencaSinaisSintomasDorGarganta) {
//        java.lang.Long presencaSinaisSintomasDorGargantaOld = this.presencaSinaisSintomasDorGarganta;
		this.presencaSinaisSintomasDorGarganta = presencaSinaisSintomasDorGarganta;
//        this.getPropertyChangeSupport().firePropertyChange ("presencaSinaisSintomasDorGarganta", presencaSinaisSintomasDorGargantaOld, presencaSinaisSintomasDorGarganta);
	}



	/**
	 * Return the value associated with the column: presenca_sinais_sintomas_dispneia
	 */
	public java.lang.Long getPresencaSinaisSintomasDispneia () {
		return getPropertyValue(this, presencaSinaisSintomasDispneia, PROP_PRESENCA_SINAIS_SINTOMAS_DISPNEIA); 
	}

	/**
	 * Set the value related to the column: presenca_sinais_sintomas_dispneia
	 * @param presencaSinaisSintomasDispneia the presenca_sinais_sintomas_dispneia value
	 */
	public void setPresencaSinaisSintomasDispneia (java.lang.Long presencaSinaisSintomasDispneia) {
//        java.lang.Long presencaSinaisSintomasDispneiaOld = this.presencaSinaisSintomasDispneia;
		this.presencaSinaisSintomasDispneia = presencaSinaisSintomasDispneia;
//        this.getPropertyChangeSupport().firePropertyChange ("presencaSinaisSintomasDispneia", presencaSinaisSintomasDispneiaOld, presencaSinaisSintomasDispneia);
	}



	/**
	 * Return the value associated with the column: presenca_sinais_sintomas_desconforto_respiratorio
	 */
	public java.lang.Long getPresencaSinaisSintomasDesconfortoRespiratorio () {
		return getPropertyValue(this, presencaSinaisSintomasDesconfortoRespiratorio, PROP_PRESENCA_SINAIS_SINTOMAS_DESCONFORTO_RESPIRATORIO); 
	}

	/**
	 * Set the value related to the column: presenca_sinais_sintomas_desconforto_respiratorio
	 * @param presencaSinaisSintomasDesconfortoRespiratorio the presenca_sinais_sintomas_desconforto_respiratorio value
	 */
	public void setPresencaSinaisSintomasDesconfortoRespiratorio (java.lang.Long presencaSinaisSintomasDesconfortoRespiratorio) {
//        java.lang.Long presencaSinaisSintomasDesconfortoRespiratorioOld = this.presencaSinaisSintomasDesconfortoRespiratorio;
		this.presencaSinaisSintomasDesconfortoRespiratorio = presencaSinaisSintomasDesconfortoRespiratorio;
//        this.getPropertyChangeSupport().firePropertyChange ("presencaSinaisSintomasDesconfortoRespiratorio", presencaSinaisSintomasDesconfortoRespiratorioOld, presencaSinaisSintomasDesconfortoRespiratorio);
	}



	/**
	 * Return the value associated with the column: presenca_sinais_sintomas_saturacao_o2_95
	 */
	public java.lang.Long getPresencaSinaisSintomasSaturacaoO295 () {
		return getPropertyValue(this, presencaSinaisSintomasSaturacaoO295, PROP_PRESENCA_SINAIS_SINTOMAS_SATURACAO_O295); 
	}

	/**
	 * Set the value related to the column: presenca_sinais_sintomas_saturacao_o2_95
	 * @param presencaSinaisSintomasSaturacaoO295 the presenca_sinais_sintomas_saturacao_o2_95 value
	 */
	public void setPresencaSinaisSintomasSaturacaoO295 (java.lang.Long presencaSinaisSintomasSaturacaoO295) {
//        java.lang.Long presencaSinaisSintomasSaturacaoO295Old = this.presencaSinaisSintomasSaturacaoO295;
		this.presencaSinaisSintomasSaturacaoO295 = presencaSinaisSintomasSaturacaoO295;
//        this.getPropertyChangeSupport().firePropertyChange ("presencaSinaisSintomasSaturacaoO295", presencaSinaisSintomasSaturacaoO295Old, presencaSinaisSintomasSaturacaoO295);
	}



	/**
	 * Return the value associated with the column: presenca_sinais_sintomas_diarreia
	 */
	public java.lang.Long getPresencaSinaisSintomasDiarreia () {
		return getPropertyValue(this, presencaSinaisSintomasDiarreia, PROP_PRESENCA_SINAIS_SINTOMAS_DIARREIA); 
	}

	/**
	 * Set the value related to the column: presenca_sinais_sintomas_diarreia
	 * @param presencaSinaisSintomasDiarreia the presenca_sinais_sintomas_diarreia value
	 */
	public void setPresencaSinaisSintomasDiarreia (java.lang.Long presencaSinaisSintomasDiarreia) {
//        java.lang.Long presencaSinaisSintomasDiarreiaOld = this.presencaSinaisSintomasDiarreia;
		this.presencaSinaisSintomasDiarreia = presencaSinaisSintomasDiarreia;
//        this.getPropertyChangeSupport().firePropertyChange ("presencaSinaisSintomasDiarreia", presencaSinaisSintomasDiarreiaOld, presencaSinaisSintomasDiarreia);
	}



	/**
	 * Return the value associated with the column: presenca_sinais_sintomas_vomito
	 */
	public java.lang.Long getPresencaSinaisSintomasVomito () {
		return getPropertyValue(this, presencaSinaisSintomasVomito, PROP_PRESENCA_SINAIS_SINTOMAS_VOMITO); 
	}

	/**
	 * Set the value related to the column: presenca_sinais_sintomas_vomito
	 * @param presencaSinaisSintomasVomito the presenca_sinais_sintomas_vomito value
	 */
	public void setPresencaSinaisSintomasVomito (java.lang.Long presencaSinaisSintomasVomito) {
//        java.lang.Long presencaSinaisSintomasVomitoOld = this.presencaSinaisSintomasVomito;
		this.presencaSinaisSintomasVomito = presencaSinaisSintomasVomito;
//        this.getPropertyChangeSupport().firePropertyChange ("presencaSinaisSintomasVomito", presencaSinaisSintomasVomitoOld, presencaSinaisSintomasVomito);
	}



	/**
	 * Return the value associated with the column: presenca_sinais_sintomas_dor_abdominal
	 */
	public java.lang.Long getPresencaSinaisSintomasDorAbdominal () {
		return getPropertyValue(this, presencaSinaisSintomasDorAbdominal, PROP_PRESENCA_SINAIS_SINTOMAS_DOR_ABDOMINAL); 
	}

	/**
	 * Set the value related to the column: presenca_sinais_sintomas_dor_abdominal
	 * @param presencaSinaisSintomasDorAbdominal the presenca_sinais_sintomas_dor_abdominal value
	 */
	public void setPresencaSinaisSintomasDorAbdominal (java.lang.Long presencaSinaisSintomasDorAbdominal) {
//        java.lang.Long presencaSinaisSintomasDorAbdominalOld = this.presencaSinaisSintomasDorAbdominal;
		this.presencaSinaisSintomasDorAbdominal = presencaSinaisSintomasDorAbdominal;
//        this.getPropertyChangeSupport().firePropertyChange ("presencaSinaisSintomasDorAbdominal", presencaSinaisSintomasDorAbdominalOld, presencaSinaisSintomasDorAbdominal);
	}



	/**
	 * Return the value associated with the column: presenca_sinais_sintomas_fadiga
	 */
	public java.lang.Long getPresencaSinaisSintomasFadiga () {
		return getPropertyValue(this, presencaSinaisSintomasFadiga, PROP_PRESENCA_SINAIS_SINTOMAS_FADIGA); 
	}

	/**
	 * Set the value related to the column: presenca_sinais_sintomas_fadiga
	 * @param presencaSinaisSintomasFadiga the presenca_sinais_sintomas_fadiga value
	 */
	public void setPresencaSinaisSintomasFadiga (java.lang.Long presencaSinaisSintomasFadiga) {
//        java.lang.Long presencaSinaisSintomasFadigaOld = this.presencaSinaisSintomasFadiga;
		this.presencaSinaisSintomasFadiga = presencaSinaisSintomasFadiga;
//        this.getPropertyChangeSupport().firePropertyChange ("presencaSinaisSintomasFadiga", presencaSinaisSintomasFadigaOld, presencaSinaisSintomasFadiga);
	}



	/**
	 * Return the value associated with the column: presenca_sinais_sintomas_perda_olfato
	 */
	public java.lang.Long getPresencaSinaisSintomasPerdaOlfato () {
		return getPropertyValue(this, presencaSinaisSintomasPerdaOlfato, PROP_PRESENCA_SINAIS_SINTOMAS_PERDA_OLFATO); 
	}

	/**
	 * Set the value related to the column: presenca_sinais_sintomas_perda_olfato
	 * @param presencaSinaisSintomasPerdaOlfato the presenca_sinais_sintomas_perda_olfato value
	 */
	public void setPresencaSinaisSintomasPerdaOlfato (java.lang.Long presencaSinaisSintomasPerdaOlfato) {
//        java.lang.Long presencaSinaisSintomasPerdaOlfatoOld = this.presencaSinaisSintomasPerdaOlfato;
		this.presencaSinaisSintomasPerdaOlfato = presencaSinaisSintomasPerdaOlfato;
//        this.getPropertyChangeSupport().firePropertyChange ("presencaSinaisSintomasPerdaOlfato", presencaSinaisSintomasPerdaOlfatoOld, presencaSinaisSintomasPerdaOlfato);
	}



	/**
	 * Return the value associated with the column: presenca_sinais_sintomas_perda_paladar
	 */
	public java.lang.Long getPresencaSinaisSintomasPerdaPaladar () {
		return getPropertyValue(this, presencaSinaisSintomasPerdaPaladar, PROP_PRESENCA_SINAIS_SINTOMAS_PERDA_PALADAR); 
	}

	/**
	 * Set the value related to the column: presenca_sinais_sintomas_perda_paladar
	 * @param presencaSinaisSintomasPerdaPaladar the presenca_sinais_sintomas_perda_paladar value
	 */
	public void setPresencaSinaisSintomasPerdaPaladar (java.lang.Long presencaSinaisSintomasPerdaPaladar) {
//        java.lang.Long presencaSinaisSintomasPerdaPaladarOld = this.presencaSinaisSintomasPerdaPaladar;
		this.presencaSinaisSintomasPerdaPaladar = presencaSinaisSintomasPerdaPaladar;
//        this.getPropertyChangeSupport().firePropertyChange ("presencaSinaisSintomasPerdaPaladar", presencaSinaisSintomasPerdaPaladarOld, presencaSinaisSintomasPerdaPaladar);
	}



	/**
	 * Return the value associated with the column: presenca_sinais_sintomas_outro
	 */
	public java.lang.String getPresencaSinaisSintomasOutro () {
		return getPropertyValue(this, presencaSinaisSintomasOutro, PROP_PRESENCA_SINAIS_SINTOMAS_OUTRO); 
	}

	/**
	 * Set the value related to the column: presenca_sinais_sintomas_outro
	 * @param presencaSinaisSintomasOutro the presenca_sinais_sintomas_outro value
	 */
	public void setPresencaSinaisSintomasOutro (java.lang.String presencaSinaisSintomasOutro) {
//        java.lang.String presencaSinaisSintomasOutroOld = this.presencaSinaisSintomasOutro;
		this.presencaSinaisSintomasOutro = presencaSinaisSintomasOutro;
//        this.getPropertyChangeSupport().firePropertyChange ("presencaSinaisSintomasOutro", presencaSinaisSintomasOutroOld, presencaSinaisSintomasOutro);
	}



	/**
	 * Return the value associated with the column: fatores_risco_puerpera
	 */
	public java.lang.Long getFatoresRiscoPuerpera () {
		return getPropertyValue(this, fatoresRiscoPuerpera, PROP_FATORES_RISCO_PUERPERA); 
	}

	/**
	 * Set the value related to the column: fatores_risco_puerpera
	 * @param fatoresRiscoPuerpera the fatores_risco_puerpera value
	 */
	public void setFatoresRiscoPuerpera (java.lang.Long fatoresRiscoPuerpera) {
//        java.lang.Long fatoresRiscoPuerperaOld = this.fatoresRiscoPuerpera;
		this.fatoresRiscoPuerpera = fatoresRiscoPuerpera;
//        this.getPropertyChangeSupport().firePropertyChange ("fatoresRiscoPuerpera", fatoresRiscoPuerperaOld, fatoresRiscoPuerpera);
	}



	/**
	 * Return the value associated with the column: fatores_risco_sindrome_down
	 */
	public java.lang.Long getFatoresRiscoSindromeDown () {
		return getPropertyValue(this, fatoresRiscoSindromeDown, PROP_FATORES_RISCO_SINDROME_DOWN); 
	}

	/**
	 * Set the value related to the column: fatores_risco_sindrome_down
	 * @param fatoresRiscoSindromeDown the fatores_risco_sindrome_down value
	 */
	public void setFatoresRiscoSindromeDown (java.lang.Long fatoresRiscoSindromeDown) {
//        java.lang.Long fatoresRiscoSindromeDownOld = this.fatoresRiscoSindromeDown;
		this.fatoresRiscoSindromeDown = fatoresRiscoSindromeDown;
//        this.getPropertyChangeSupport().firePropertyChange ("fatoresRiscoSindromeDown", fatoresRiscoSindromeDownOld, fatoresRiscoSindromeDown);
	}



	/**
	 * Return the value associated with the column: fatores_risco_diabetes_mellitus
	 */
	public java.lang.Long getFatoresRiscoDiabetesMellitus () {
		return getPropertyValue(this, fatoresRiscoDiabetesMellitus, PROP_FATORES_RISCO_DIABETES_MELLITUS); 
	}

	/**
	 * Set the value related to the column: fatores_risco_diabetes_mellitus
	 * @param fatoresRiscoDiabetesMellitus the fatores_risco_diabetes_mellitus value
	 */
	public void setFatoresRiscoDiabetesMellitus (java.lang.Long fatoresRiscoDiabetesMellitus) {
//        java.lang.Long fatoresRiscoDiabetesMellitusOld = this.fatoresRiscoDiabetesMellitus;
		this.fatoresRiscoDiabetesMellitus = fatoresRiscoDiabetesMellitus;
//        this.getPropertyChangeSupport().firePropertyChange ("fatoresRiscoDiabetesMellitus", fatoresRiscoDiabetesMellitusOld, fatoresRiscoDiabetesMellitus);
	}



	/**
	 * Return the value associated with the column: fatores_risco_imunodeficiencia
	 */
	public java.lang.Long getFatoresRiscoImunodeficiencia () {
		return getPropertyValue(this, fatoresRiscoImunodeficiencia, PROP_FATORES_RISCO_IMUNODEFICIENCIA); 
	}

	/**
	 * Set the value related to the column: fatores_risco_imunodeficiencia
	 * @param fatoresRiscoImunodeficiencia the fatores_risco_imunodeficiencia value
	 */
	public void setFatoresRiscoImunodeficiencia (java.lang.Long fatoresRiscoImunodeficiencia) {
//        java.lang.Long fatoresRiscoImunodeficienciaOld = this.fatoresRiscoImunodeficiencia;
		this.fatoresRiscoImunodeficiencia = fatoresRiscoImunodeficiencia;
//        this.getPropertyChangeSupport().firePropertyChange ("fatoresRiscoImunodeficiencia", fatoresRiscoImunodeficienciaOld, fatoresRiscoImunodeficiencia);
	}



	/**
	 * Return the value associated with the column: fatores_risco_doenca_cardiovascular_cronica
	 */
	public java.lang.Long getFatoresRiscoDoencaCardiovascularCronica () {
		return getPropertyValue(this, fatoresRiscoDoencaCardiovascularCronica, PROP_FATORES_RISCO_DOENCA_CARDIOVASCULAR_CRONICA); 
	}

	/**
	 * Set the value related to the column: fatores_risco_doenca_cardiovascular_cronica
	 * @param fatoresRiscoDoencaCardiovascularCronica the fatores_risco_doenca_cardiovascular_cronica value
	 */
	public void setFatoresRiscoDoencaCardiovascularCronica (java.lang.Long fatoresRiscoDoencaCardiovascularCronica) {
//        java.lang.Long fatoresRiscoDoencaCardiovascularCronicaOld = this.fatoresRiscoDoencaCardiovascularCronica;
		this.fatoresRiscoDoencaCardiovascularCronica = fatoresRiscoDoencaCardiovascularCronica;
//        this.getPropertyChangeSupport().firePropertyChange ("fatoresRiscoDoencaCardiovascularCronica", fatoresRiscoDoencaCardiovascularCronicaOld, fatoresRiscoDoencaCardiovascularCronica);
	}



	/**
	 * Return the value associated with the column: fatores_risco_doenca_hepatica_cronica
	 */
	public java.lang.Long getFatoresRiscoDoencaHepaticaCronica () {
		return getPropertyValue(this, fatoresRiscoDoencaHepaticaCronica, PROP_FATORES_RISCO_DOENCA_HEPATICA_CRONICA); 
	}

	/**
	 * Set the value related to the column: fatores_risco_doenca_hepatica_cronica
	 * @param fatoresRiscoDoencaHepaticaCronica the fatores_risco_doenca_hepatica_cronica value
	 */
	public void setFatoresRiscoDoencaHepaticaCronica (java.lang.Long fatoresRiscoDoencaHepaticaCronica) {
//        java.lang.Long fatoresRiscoDoencaHepaticaCronicaOld = this.fatoresRiscoDoencaHepaticaCronica;
		this.fatoresRiscoDoencaHepaticaCronica = fatoresRiscoDoencaHepaticaCronica;
//        this.getPropertyChangeSupport().firePropertyChange ("fatoresRiscoDoencaHepaticaCronica", fatoresRiscoDoencaHepaticaCronicaOld, fatoresRiscoDoencaHepaticaCronica);
	}



	/**
	 * Return the value associated with the column: fatores_risco_doenca_neurologica_cronica
	 */
	public java.lang.Long getFatoresRiscoDoencaNeurologicaCronica () {
		return getPropertyValue(this, fatoresRiscoDoencaNeurologicaCronica, PROP_FATORES_RISCO_DOENCA_NEUROLOGICA_CRONICA); 
	}

	/**
	 * Set the value related to the column: fatores_risco_doenca_neurologica_cronica
	 * @param fatoresRiscoDoencaNeurologicaCronica the fatores_risco_doenca_neurologica_cronica value
	 */
	public void setFatoresRiscoDoencaNeurologicaCronica (java.lang.Long fatoresRiscoDoencaNeurologicaCronica) {
//        java.lang.Long fatoresRiscoDoencaNeurologicaCronicaOld = this.fatoresRiscoDoencaNeurologicaCronica;
		this.fatoresRiscoDoencaNeurologicaCronica = fatoresRiscoDoencaNeurologicaCronica;
//        this.getPropertyChangeSupport().firePropertyChange ("fatoresRiscoDoencaNeurologicaCronica", fatoresRiscoDoencaNeurologicaCronicaOld, fatoresRiscoDoencaNeurologicaCronica);
	}



	/**
	 * Return the value associated with the column: fatores_risco_doenca_renal_cronica
	 */
	public java.lang.Long getFatoresRiscoDoencaRenalCronica () {
		return getPropertyValue(this, fatoresRiscoDoencaRenalCronica, PROP_FATORES_RISCO_DOENCA_RENAL_CRONICA); 
	}

	/**
	 * Set the value related to the column: fatores_risco_doenca_renal_cronica
	 * @param fatoresRiscoDoencaRenalCronica the fatores_risco_doenca_renal_cronica value
	 */
	public void setFatoresRiscoDoencaRenalCronica (java.lang.Long fatoresRiscoDoencaRenalCronica) {
//        java.lang.Long fatoresRiscoDoencaRenalCronicaOld = this.fatoresRiscoDoencaRenalCronica;
		this.fatoresRiscoDoencaRenalCronica = fatoresRiscoDoencaRenalCronica;
//        this.getPropertyChangeSupport().firePropertyChange ("fatoresRiscoDoencaRenalCronica", fatoresRiscoDoencaRenalCronicaOld, fatoresRiscoDoencaRenalCronica);
	}



	/**
	 * Return the value associated with the column: fatores_risco_doenca_hermatologica_cronica
	 */
	public java.lang.Long getFatoresRiscoDoencaHermatologicaCronica () {
		return getPropertyValue(this, fatoresRiscoDoencaHermatologicaCronica, PROP_FATORES_RISCO_DOENCA_HERMATOLOGICA_CRONICA); 
	}

	/**
	 * Set the value related to the column: fatores_risco_doenca_hermatologica_cronica
	 * @param fatoresRiscoDoencaHermatologicaCronica the fatores_risco_doenca_hermatologica_cronica value
	 */
	public void setFatoresRiscoDoencaHermatologicaCronica (java.lang.Long fatoresRiscoDoencaHermatologicaCronica) {
//        java.lang.Long fatoresRiscoDoencaHermatologicaCronicaOld = this.fatoresRiscoDoencaHermatologicaCronica;
		this.fatoresRiscoDoencaHermatologicaCronica = fatoresRiscoDoencaHermatologicaCronica;
//        this.getPropertyChangeSupport().firePropertyChange ("fatoresRiscoDoencaHermatologicaCronica", fatoresRiscoDoencaHermatologicaCronicaOld, fatoresRiscoDoencaHermatologicaCronica);
	}



	/**
	 * Return the value associated with the column: fatores_risco_asma
	 */
	public java.lang.Long getFatoresRiscoAsma () {
		return getPropertyValue(this, fatoresRiscoAsma, PROP_FATORES_RISCO_ASMA); 
	}

	/**
	 * Set the value related to the column: fatores_risco_asma
	 * @param fatoresRiscoAsma the fatores_risco_asma value
	 */
	public void setFatoresRiscoAsma (java.lang.Long fatoresRiscoAsma) {
//        java.lang.Long fatoresRiscoAsmaOld = this.fatoresRiscoAsma;
		this.fatoresRiscoAsma = fatoresRiscoAsma;
//        this.getPropertyChangeSupport().firePropertyChange ("fatoresRiscoAsma", fatoresRiscoAsmaOld, fatoresRiscoAsma);
	}



	/**
	 * Return the value associated with the column: fatores_risco_outra_pneumopatia_cronica
	 */
	public java.lang.Long getFatoresRiscoOutraPneumopatiaCronica () {
		return getPropertyValue(this, fatoresRiscoOutraPneumopatiaCronica, PROP_FATORES_RISCO_OUTRA_PNEUMOPATIA_CRONICA); 
	}

	/**
	 * Set the value related to the column: fatores_risco_outra_pneumopatia_cronica
	 * @param fatoresRiscoOutraPneumopatiaCronica the fatores_risco_outra_pneumopatia_cronica value
	 */
	public void setFatoresRiscoOutraPneumopatiaCronica (java.lang.Long fatoresRiscoOutraPneumopatiaCronica) {
//        java.lang.Long fatoresRiscoOutraPneumopatiaCronicaOld = this.fatoresRiscoOutraPneumopatiaCronica;
		this.fatoresRiscoOutraPneumopatiaCronica = fatoresRiscoOutraPneumopatiaCronica;
//        this.getPropertyChangeSupport().firePropertyChange ("fatoresRiscoOutraPneumopatiaCronica", fatoresRiscoOutraPneumopatiaCronicaOld, fatoresRiscoOutraPneumopatiaCronica);
	}



	/**
	 * Return the value associated with the column: fatores_risco_obesidade_imc
	 */
	public java.lang.Long getFatoresRiscoObesidadeIMC () {
		return getPropertyValue(this, fatoresRiscoObesidadeIMC, PROP_FATORES_RISCO_OBESIDADE_I_M_C); 
	}

	/**
	 * Set the value related to the column: fatores_risco_obesidade_imc
	 * @param fatoresRiscoObesidadeIMC the fatores_risco_obesidade_imc value
	 */
	public void setFatoresRiscoObesidadeIMC (java.lang.Long fatoresRiscoObesidadeIMC) {
//        java.lang.Long fatoresRiscoObesidadeIMCOld = this.fatoresRiscoObesidadeIMC;
		this.fatoresRiscoObesidadeIMC = fatoresRiscoObesidadeIMC;
//        this.getPropertyChangeSupport().firePropertyChange ("fatoresRiscoObesidadeIMC", fatoresRiscoObesidadeIMCOld, fatoresRiscoObesidadeIMC);
	}



	/**
	 * Return the value associated with the column: fatores_risco_outro
	 */
	public java.lang.String getFatoresRiscoOutro () {
		return getPropertyValue(this, fatoresRiscoOutro, PROP_FATORES_RISCO_OUTRO); 
	}

	/**
	 * Set the value related to the column: fatores_risco_outro
	 * @param fatoresRiscoOutro the fatores_risco_outro value
	 */
	public void setFatoresRiscoOutro (java.lang.String fatoresRiscoOutro) {
//        java.lang.String fatoresRiscoOutroOld = this.fatoresRiscoOutro;
		this.fatoresRiscoOutro = fatoresRiscoOutro;
//        this.getPropertyChangeSupport().firePropertyChange ("fatoresRiscoOutro", fatoresRiscoOutroOld, fatoresRiscoOutro);
	}



	/**
	 * Return the value associated with the column: recebeu_vacina_covid19
	 */
	public java.lang.Long getRecebeuVacinaCovid19 () {
		return getPropertyValue(this, recebeuVacinaCovid19, PROP_RECEBEU_VACINA_COVID19); 
	}

	/**
	 * Set the value related to the column: recebeu_vacina_covid19
	 * @param recebeuVacinaCovid19 the recebeu_vacina_covid19 value
	 */
	public void setRecebeuVacinaCovid19 (java.lang.Long recebeuVacinaCovid19) {
//        java.lang.Long recebeuVacinaCovid19Old = this.recebeuVacinaCovid19;
		this.recebeuVacinaCovid19 = recebeuVacinaCovid19;
//        this.getPropertyChangeSupport().firePropertyChange ("recebeuVacinaCovid19", recebeuVacinaCovid19Old, recebeuVacinaCovid19);
	}



	/**
	 * Return the value associated with the column: dt_dose1_vacina_covid19
	 */
	public java.util.Date getDataDose1VacinaCovid19 () {
		return getPropertyValue(this, dataDose1VacinaCovid19, PROP_DATA_DOSE1_VACINA_COVID19); 
	}

	/**
	 * Set the value related to the column: dt_dose1_vacina_covid19
	 * @param dataDose1VacinaCovid19 the dt_dose1_vacina_covid19 value
	 */
	public void setDataDose1VacinaCovid19 (java.util.Date dataDose1VacinaCovid19) {
//        java.util.Date dataDose1VacinaCovid19Old = this.dataDose1VacinaCovid19;
		this.dataDose1VacinaCovid19 = dataDose1VacinaCovid19;
//        this.getPropertyChangeSupport().firePropertyChange ("dataDose1VacinaCovid19", dataDose1VacinaCovid19Old, dataDose1VacinaCovid19);
	}



	/**
	 * Return the value associated with the column: dt_dose2_vacina_covid19
	 */
	public java.util.Date getDataDose2VacinaCovid19 () {
		return getPropertyValue(this, dataDose2VacinaCovid19, PROP_DATA_DOSE2_VACINA_COVID19); 
	}

	/**
	 * Set the value related to the column: dt_dose2_vacina_covid19
	 * @param dataDose2VacinaCovid19 the dt_dose2_vacina_covid19 value
	 */
	public void setDataDose2VacinaCovid19 (java.util.Date dataDose2VacinaCovid19) {
//        java.util.Date dataDose2VacinaCovid19Old = this.dataDose2VacinaCovid19;
		this.dataDose2VacinaCovid19 = dataDose2VacinaCovid19;
//        this.getPropertyChangeSupport().firePropertyChange ("dataDose2VacinaCovid19", dataDose2VacinaCovid19Old, dataDose2VacinaCovid19);
	}



	/**
	 * Return the value associated with the column: fabricante_vacina_covid19
	 */
	public java.lang.String getFabricanteVacinaCovid19 () {
		return getPropertyValue(this, fabricanteVacinaCovid19, PROP_FABRICANTE_VACINA_COVID19); 
	}

	/**
	 * Set the value related to the column: fabricante_vacina_covid19
	 * @param fabricanteVacinaCovid19 the fabricante_vacina_covid19 value
	 */
	public void setFabricanteVacinaCovid19 (java.lang.String fabricanteVacinaCovid19) {
//        java.lang.String fabricanteVacinaCovid19Old = this.fabricanteVacinaCovid19;
		this.fabricanteVacinaCovid19 = fabricanteVacinaCovid19;
//        this.getPropertyChangeSupport().firePropertyChange ("fabricanteVacinaCovid19", fabricanteVacinaCovid19Old, fabricanteVacinaCovid19);
	}



	/**
	 * Return the value associated with the column: lote1_da_vacina_covid19
	 */
	public java.lang.String getLote1VacinaCovid19 () {
		return getPropertyValue(this, lote1VacinaCovid19, PROP_LOTE1_VACINA_COVID19); 
	}

	/**
	 * Set the value related to the column: lote1_da_vacina_covid19
	 * @param lote1VacinaCovid19 the lote1_da_vacina_covid19 value
	 */
	public void setLote1VacinaCovid19 (java.lang.String lote1VacinaCovid19) {
//        java.lang.String lote1VacinaCovid19Old = this.lote1VacinaCovid19;
		this.lote1VacinaCovid19 = lote1VacinaCovid19;
//        this.getPropertyChangeSupport().firePropertyChange ("lote1VacinaCovid19", lote1VacinaCovid19Old, lote1VacinaCovid19);
	}



	/**
	 * Return the value associated with the column: lote2_da_vacina_covid19
	 */
	public java.lang.String getLote2VacinaCovid19 () {
		return getPropertyValue(this, lote2VacinaCovid19, PROP_LOTE2_VACINA_COVID19); 
	}

	/**
	 * Set the value related to the column: lote2_da_vacina_covid19
	 * @param lote2VacinaCovid19 the lote2_da_vacina_covid19 value
	 */
	public void setLote2VacinaCovid19 (java.lang.String lote2VacinaCovid19) {
//        java.lang.String lote2VacinaCovid19Old = this.lote2VacinaCovid19;
		this.lote2VacinaCovid19 = lote2VacinaCovid19;
//        this.getPropertyChangeSupport().firePropertyChange ("lote2VacinaCovid19", lote2VacinaCovid19Old, lote2VacinaCovid19);
	}



	/**
	 * Return the value associated with the column: recebeu_vacina_gripe_ultima_campanha
	 */
	public java.lang.Long getRecebeuVacinaGripeUltimaCampanha () {
		return getPropertyValue(this, recebeuVacinaGripeUltimaCampanha, PROP_RECEBEU_VACINA_GRIPE_ULTIMA_CAMPANHA); 
	}

	/**
	 * Set the value related to the column: recebeu_vacina_gripe_ultima_campanha
	 * @param recebeuVacinaGripeUltimaCampanha the recebeu_vacina_gripe_ultima_campanha value
	 */
	public void setRecebeuVacinaGripeUltimaCampanha (java.lang.Long recebeuVacinaGripeUltimaCampanha) {
//        java.lang.Long recebeuVacinaGripeUltimaCampanhaOld = this.recebeuVacinaGripeUltimaCampanha;
		this.recebeuVacinaGripeUltimaCampanha = recebeuVacinaGripeUltimaCampanha;
//        this.getPropertyChangeSupport().firePropertyChange ("recebeuVacinaGripeUltimaCampanha", recebeuVacinaGripeUltimaCampanhaOld, recebeuVacinaGripeUltimaCampanha);
	}



	/**
	 * Return the value associated with the column: dt_gripe_ultima_campanha
	 */
	public java.util.Date getDataGripeUltimaCampanha () {
		return getPropertyValue(this, dataGripeUltimaCampanha, PROP_DATA_GRIPE_ULTIMA_CAMPANHA); 
	}

	/**
	 * Set the value related to the column: dt_gripe_ultima_campanha
	 * @param dataGripeUltimaCampanha the dt_gripe_ultima_campanha value
	 */
	public void setDataGripeUltimaCampanha (java.util.Date dataGripeUltimaCampanha) {
//        java.util.Date dataGripeUltimaCampanhaOld = this.dataGripeUltimaCampanha;
		this.dataGripeUltimaCampanha = dataGripeUltimaCampanha;
//        this.getPropertyChangeSupport().firePropertyChange ("dataGripeUltimaCampanha", dataGripeUltimaCampanhaOld, dataGripeUltimaCampanha);
	}



	/**
	 * Return the value associated with the column: se_menor_6meses_mae_recebeu_vacina
	 */
	public java.lang.Long getSeMenor6mesesMaeRecebeuVacina () {
		return getPropertyValue(this, seMenor6mesesMaeRecebeuVacina, PROP_SE_MENOR6MESES_MAE_RECEBEU_VACINA); 
	}

	/**
	 * Set the value related to the column: se_menor_6meses_mae_recebeu_vacina
	 * @param seMenor6mesesMaeRecebeuVacina the se_menor_6meses_mae_recebeu_vacina value
	 */
	public void setSeMenor6mesesMaeRecebeuVacina (java.lang.Long seMenor6mesesMaeRecebeuVacina) {
//        java.lang.Long seMenor6mesesMaeRecebeuVacinaOld = this.seMenor6mesesMaeRecebeuVacina;
		this.seMenor6mesesMaeRecebeuVacina = seMenor6mesesMaeRecebeuVacina;
//        this.getPropertyChangeSupport().firePropertyChange ("seMenor6mesesMaeRecebeuVacina", seMenor6mesesMaeRecebeuVacinaOld, seMenor6mesesMaeRecebeuVacina);
	}



	/**
	 * Return the value associated with the column: dt_se_sim_mae_recebeu_vacina
	 */
	public java.util.Date getDataSeSimMaeRecebeuVacina () {
		return getPropertyValue(this, dataSeSimMaeRecebeuVacina, PROP_DATA_SE_SIM_MAE_RECEBEU_VACINA); 
	}

	/**
	 * Set the value related to the column: dt_se_sim_mae_recebeu_vacina
	 * @param dataSeSimMaeRecebeuVacina the dt_se_sim_mae_recebeu_vacina value
	 */
	public void setDataSeSimMaeRecebeuVacina (java.util.Date dataSeSimMaeRecebeuVacina) {
//        java.util.Date dataSeSimMaeRecebeuVacinaOld = this.dataSeSimMaeRecebeuVacina;
		this.dataSeSimMaeRecebeuVacina = dataSeSimMaeRecebeuVacina;
//        this.getPropertyChangeSupport().firePropertyChange ("dataSeSimMaeRecebeuVacina", dataSeSimMaeRecebeuVacinaOld, dataSeSimMaeRecebeuVacina);
	}



	/**
	 * Return the value associated with the column: se_menor_6meses_mae_amamenta_crianca
	 */
	public java.lang.Long getSeMenor6mesesMaeAmamentaCrianca () {
		return getPropertyValue(this, seMenor6mesesMaeAmamentaCrianca, PROP_SE_MENOR6MESES_MAE_AMAMENTA_CRIANCA); 
	}

	/**
	 * Set the value related to the column: se_menor_6meses_mae_amamenta_crianca
	 * @param seMenor6mesesMaeAmamentaCrianca the se_menor_6meses_mae_amamenta_crianca value
	 */
	public void setSeMenor6mesesMaeAmamentaCrianca (java.lang.Long seMenor6mesesMaeAmamentaCrianca) {
//        java.lang.Long seMenor6mesesMaeAmamentaCriancaOld = this.seMenor6mesesMaeAmamentaCrianca;
		this.seMenor6mesesMaeAmamentaCrianca = seMenor6mesesMaeAmamentaCrianca;
//        this.getPropertyChangeSupport().firePropertyChange ("seMenor6mesesMaeAmamentaCrianca", seMenor6mesesMaeAmamentaCriancaOld, seMenor6mesesMaeAmamentaCrianca);
	}



	/**
	 * Return the value associated with the column: dt_se_maior_igual_6meses_e_menor_igual_8anos_dose_unica
	 */
	public java.util.Date getDataSeMaiorIgual6mesesMenorIgual8anosDoseUnica () {
		return getPropertyValue(this, dataSeMaiorIgual6mesesMenorIgual8anosDoseUnica, PROP_DATA_SE_MAIOR_IGUAL6MESES_MENOR_IGUAL8ANOS_DOSE_UNICA); 
	}

	/**
	 * Set the value related to the column: dt_se_maior_igual_6meses_e_menor_igual_8anos_dose_unica
	 * @param dataSeMaiorIgual6mesesMenorIgual8anosDoseUnica the dt_se_maior_igual_6meses_e_menor_igual_8anos_dose_unica value
	 */
	public void setDataSeMaiorIgual6mesesMenorIgual8anosDoseUnica (java.util.Date dataSeMaiorIgual6mesesMenorIgual8anosDoseUnica) {
//        java.util.Date dataSeMaiorIgual6mesesMenorIgual8anosDoseUnicaOld = this.dataSeMaiorIgual6mesesMenorIgual8anosDoseUnica;
		this.dataSeMaiorIgual6mesesMenorIgual8anosDoseUnica = dataSeMaiorIgual6mesesMenorIgual8anosDoseUnica;
//        this.getPropertyChangeSupport().firePropertyChange ("dataSeMaiorIgual6mesesMenorIgual8anosDoseUnica", dataSeMaiorIgual6mesesMenorIgual8anosDoseUnicaOld, dataSeMaiorIgual6mesesMenorIgual8anosDoseUnica);
	}



	/**
	 * Return the value associated with the column: dt_se_maior_igual_6meses_e_menor_igual_8anos_dose1
	 */
	public java.util.Date getDataSeMaiorIgual6mesesMenorIgual8anosDose1 () {
		return getPropertyValue(this, dataSeMaiorIgual6mesesMenorIgual8anosDose1, PROP_DATA_SE_MAIOR_IGUAL6MESES_MENOR_IGUAL8ANOS_DOSE1); 
	}

	/**
	 * Set the value related to the column: dt_se_maior_igual_6meses_e_menor_igual_8anos_dose1
	 * @param dataSeMaiorIgual6mesesMenorIgual8anosDose1 the dt_se_maior_igual_6meses_e_menor_igual_8anos_dose1 value
	 */
	public void setDataSeMaiorIgual6mesesMenorIgual8anosDose1 (java.util.Date dataSeMaiorIgual6mesesMenorIgual8anosDose1) {
//        java.util.Date dataSeMaiorIgual6mesesMenorIgual8anosDose1Old = this.dataSeMaiorIgual6mesesMenorIgual8anosDose1;
		this.dataSeMaiorIgual6mesesMenorIgual8anosDose1 = dataSeMaiorIgual6mesesMenorIgual8anosDose1;
//        this.getPropertyChangeSupport().firePropertyChange ("dataSeMaiorIgual6mesesMenorIgual8anosDose1", dataSeMaiorIgual6mesesMenorIgual8anosDose1Old, dataSeMaiorIgual6mesesMenorIgual8anosDose1);
	}



	/**
	 * Return the value associated with the column: dt_se_maior_igual_6meses_e_menor_igual_8anos_dose2
	 */
	public java.util.Date getDataSeMaiorIgual6mesesMenorIgual8anosDose2 () {
		return getPropertyValue(this, dataSeMaiorIgual6mesesMenorIgual8anosDose2, PROP_DATA_SE_MAIOR_IGUAL6MESES_MENOR_IGUAL8ANOS_DOSE2); 
	}

	/**
	 * Set the value related to the column: dt_se_maior_igual_6meses_e_menor_igual_8anos_dose2
	 * @param dataSeMaiorIgual6mesesMenorIgual8anosDose2 the dt_se_maior_igual_6meses_e_menor_igual_8anos_dose2 value
	 */
	public void setDataSeMaiorIgual6mesesMenorIgual8anosDose2 (java.util.Date dataSeMaiorIgual6mesesMenorIgual8anosDose2) {
//        java.util.Date dataSeMaiorIgual6mesesMenorIgual8anosDose2Old = this.dataSeMaiorIgual6mesesMenorIgual8anosDose2;
		this.dataSeMaiorIgual6mesesMenorIgual8anosDose2 = dataSeMaiorIgual6mesesMenorIgual8anosDose2;
//        this.getPropertyChangeSupport().firePropertyChange ("dataSeMaiorIgual6mesesMenorIgual8anosDose2", dataSeMaiorIgual6mesesMenorIgual8anosDose2Old, dataSeMaiorIgual6mesesMenorIgual8anosDose2);
	}



	/**
	 * Return the value associated with the column: uso_antiviral_para_gripe
	 */
	public java.lang.Long getUsoAntiviralGripe () {
		return getPropertyValue(this, usoAntiviralGripe, PROP_USO_ANTIVIRAL_GRIPE); 
	}

	/**
	 * Set the value related to the column: uso_antiviral_para_gripe
	 * @param usoAntiviralGripe the uso_antiviral_para_gripe value
	 */
	public void setUsoAntiviralGripe (java.lang.Long usoAntiviralGripe) {
//        java.lang.Long usoAntiviralGripeOld = this.usoAntiviralGripe;
		this.usoAntiviralGripe = usoAntiviralGripe;
//        this.getPropertyChangeSupport().firePropertyChange ("usoAntiviralGripe", usoAntiviralGripeOld, usoAntiviralGripe);
	}



	/**
	 * Return the value associated with the column: qual_antiviral
	 */
	public java.lang.Long getQualAntiviral () {
		return getPropertyValue(this, qualAntiviral, PROP_QUAL_ANTIVIRAL); 
	}

	/**
	 * Set the value related to the column: qual_antiviral
	 * @param qualAntiviral the qual_antiviral value
	 */
	public void setQualAntiviral (java.lang.Long qualAntiviral) {
//        java.lang.Long qualAntiviralOld = this.qualAntiviral;
		this.qualAntiviral = qualAntiviral;
//        this.getPropertyChangeSupport().firePropertyChange ("qualAntiviral", qualAntiviralOld, qualAntiviral);
	}



	/**
	 * Return the value associated with the column: outro_antiviral
	 */
	public java.lang.String getOutroAntiviral () {
		return getPropertyValue(this, outroAntiviral, PROP_OUTRO_ANTIVIRAL); 
	}

	/**
	 * Set the value related to the column: outro_antiviral
	 * @param outroAntiviral the outro_antiviral value
	 */
	public void setOutroAntiviral (java.lang.String outroAntiviral) {
//        java.lang.String outroAntiviralOld = this.outroAntiviral;
		this.outroAntiviral = outroAntiviral;
//        this.getPropertyChangeSupport().firePropertyChange ("outroAntiviral", outroAntiviralOld, outroAntiviral);
	}



	/**
	 * Return the value associated with the column: dt_inicio_tratamento
	 */
	public java.util.Date getDataInicioTratamento () {
		return getPropertyValue(this, dataInicioTratamento, PROP_DATA_INICIO_TRATAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_inicio_tratamento
	 * @param dataInicioTratamento the dt_inicio_tratamento value
	 */
	public void setDataInicioTratamento (java.util.Date dataInicioTratamento) {
//        java.util.Date dataInicioTratamentoOld = this.dataInicioTratamento;
		this.dataInicioTratamento = dataInicioTratamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInicioTratamento", dataInicioTratamentoOld, dataInicioTratamento);
	}



	/**
	 * Return the value associated with the column: houve_internacao
	 */
	public java.lang.Long getHouveInternacao () {
		return getPropertyValue(this, houveInternacao, PROP_HOUVE_INTERNACAO); 
	}

	/**
	 * Set the value related to the column: houve_internacao
	 * @param houveInternacao the houve_internacao value
	 */
	public void setHouveInternacao (java.lang.Long houveInternacao) {
//        java.lang.Long houveInternacaoOld = this.houveInternacao;
		this.houveInternacao = houveInternacao;
//        this.getPropertyChangeSupport().firePropertyChange ("houveInternacao", houveInternacaoOld, houveInternacao);
	}



	/**
	 * Return the value associated with the column: dt_internacao_srag
	 */
	public java.util.Date getDataInternacaoSrag () {
		return getPropertyValue(this, dataInternacaoSrag, PROP_DATA_INTERNACAO_SRAG); 
	}

	/**
	 * Set the value related to the column: dt_internacao_srag
	 * @param dataInternacaoSrag the dt_internacao_srag value
	 */
	public void setDataInternacaoSrag (java.util.Date dataInternacaoSrag) {
//        java.util.Date dataInternacaoSragOld = this.dataInternacaoSrag;
		this.dataInternacaoSrag = dataInternacaoSrag;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInternacaoSrag", dataInternacaoSragOld, dataInternacaoSrag);
	}



	/**
	 * Return the value associated with the column: internado_uti
	 */
	public java.lang.Long getInternadoUti () {
		return getPropertyValue(this, internadoUti, PROP_INTERNADO_UTI); 
	}

	/**
	 * Set the value related to the column: internado_uti
	 * @param internadoUti the internado_uti value
	 */
	public void setInternadoUti (java.lang.Long internadoUti) {
//        java.lang.Long internadoUtiOld = this.internadoUti;
		this.internadoUti = internadoUti;
//        this.getPropertyChangeSupport().firePropertyChange ("internadoUti", internadoUtiOld, internadoUti);
	}



	/**
	 * Return the value associated with the column: dt_entrada_uti
	 */
	public java.util.Date getDataEntradaUti () {
		return getPropertyValue(this, dataEntradaUti, PROP_DATA_ENTRADA_UTI); 
	}

	/**
	 * Set the value related to the column: dt_entrada_uti
	 * @param dataEntradaUti the dt_entrada_uti value
	 */
	public void setDataEntradaUti (java.util.Date dataEntradaUti) {
//        java.util.Date dataEntradaUtiOld = this.dataEntradaUti;
		this.dataEntradaUti = dataEntradaUti;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEntradaUti", dataEntradaUtiOld, dataEntradaUti);
	}



	/**
	 * Return the value associated with the column: dt_saida_uti
	 */
	public java.util.Date getDataSaidaUti () {
		return getPropertyValue(this, dataSaidaUti, PROP_DATA_SAIDA_UTI); 
	}

	/**
	 * Set the value related to the column: dt_saida_uti
	 * @param dataSaidaUti the dt_saida_uti value
	 */
	public void setDataSaidaUti (java.util.Date dataSaidaUti) {
//        java.util.Date dataSaidaUtiOld = this.dataSaidaUti;
		this.dataSaidaUti = dataSaidaUti;
//        this.getPropertyChangeSupport().firePropertyChange ("dataSaidaUti", dataSaidaUtiOld, dataSaidaUti);
	}



	/**
	 * Return the value associated with the column: uso_suporte_ventilatorio
	 */
	public java.lang.Long getUsoSuporteVentilatorio () {
		return getPropertyValue(this, usoSuporteVentilatorio, PROP_USO_SUPORTE_VENTILATORIO); 
	}

	/**
	 * Set the value related to the column: uso_suporte_ventilatorio
	 * @param usoSuporteVentilatorio the uso_suporte_ventilatorio value
	 */
	public void setUsoSuporteVentilatorio (java.lang.Long usoSuporteVentilatorio) {
//        java.lang.Long usoSuporteVentilatorioOld = this.usoSuporteVentilatorio;
		this.usoSuporteVentilatorio = usoSuporteVentilatorio;
//        this.getPropertyChangeSupport().firePropertyChange ("usoSuporteVentilatorio", usoSuporteVentilatorioOld, usoSuporteVentilatorio);
	}



	/**
	 * Return the value associated with the column: raiox_torax
	 */
	public java.lang.Long getRaioxTorax () {
		return getPropertyValue(this, raioxTorax, PROP_RAIOX_TORAX); 
	}

	/**
	 * Set the value related to the column: raiox_torax
	 * @param raioxTorax the raiox_torax value
	 */
	public void setRaioxTorax (java.lang.Long raioxTorax) {
//        java.lang.Long raioxToraxOld = this.raioxTorax;
		this.raioxTorax = raioxTorax;
//        this.getPropertyChangeSupport().firePropertyChange ("raioxTorax", raioxToraxOld, raioxTorax);
	}



	/**
	 * Return the value associated with the column: dt_raiox_torax
	 */
	public java.util.Date getDataRaioxTorax () {
		return getPropertyValue(this, dataRaioxTorax, PROP_DATA_RAIOX_TORAX); 
	}

	/**
	 * Set the value related to the column: dt_raiox_torax
	 * @param dataRaioxTorax the dt_raiox_torax value
	 */
	public void setDataRaioxTorax (java.util.Date dataRaioxTorax) {
//        java.util.Date dataRaioxToraxOld = this.dataRaioxTorax;
		this.dataRaioxTorax = dataRaioxTorax;
//        this.getPropertyChangeSupport().firePropertyChange ("dataRaioxTorax", dataRaioxToraxOld, dataRaioxTorax);
	}



	/**
	 * Return the value associated with the column: outro_raiox_torax
	 */
	public java.lang.String getOutroRaioxTorax () {
		return getPropertyValue(this, outroRaioxTorax, PROP_OUTRO_RAIOX_TORAX); 
	}

	/**
	 * Set the value related to the column: outro_raiox_torax
	 * @param outroRaioxTorax the outro_raiox_torax value
	 */
	public void setOutroRaioxTorax (java.lang.String outroRaioxTorax) {
//        java.lang.String outroRaioxToraxOld = this.outroRaioxTorax;
		this.outroRaioxTorax = outroRaioxTorax;
//        this.getPropertyChangeSupport().firePropertyChange ("outroRaioxTorax", outroRaioxToraxOld, outroRaioxTorax);
	}



	/**
	 * Return the value associated with the column: aspecto_tomografia
	 */
	public java.lang.Long getAspectoTomografia () {
		return getPropertyValue(this, aspectoTomografia, PROP_ASPECTO_TOMOGRAFIA); 
	}

	/**
	 * Set the value related to the column: aspecto_tomografia
	 * @param aspectoTomografia the aspecto_tomografia value
	 */
	public void setAspectoTomografia (java.lang.Long aspectoTomografia) {
//        java.lang.Long aspectoTomografiaOld = this.aspectoTomografia;
		this.aspectoTomografia = aspectoTomografia;
//        this.getPropertyChangeSupport().firePropertyChange ("aspectoTomografia", aspectoTomografiaOld, aspectoTomografia);
	}



	/**
	 * Return the value associated with the column: outro_aspecto_tomografia
	 */
	public java.lang.String getOutroAspectoTomografia () {
		return getPropertyValue(this, outroAspectoTomografia, PROP_OUTRO_ASPECTO_TOMOGRAFIA); 
	}

	/**
	 * Set the value related to the column: outro_aspecto_tomografia
	 * @param outroAspectoTomografia the outro_aspecto_tomografia value
	 */
	public void setOutroAspectoTomografia (java.lang.String outroAspectoTomografia) {
//        java.lang.String outroAspectoTomografiaOld = this.outroAspectoTomografia;
		this.outroAspectoTomografia = outroAspectoTomografia;
//        this.getPropertyChangeSupport().firePropertyChange ("outroAspectoTomografia", outroAspectoTomografiaOld, outroAspectoTomografia);
	}



	/**
	 * Return the value associated with the column: dt_tomografia
	 */
	public java.util.Date getDataTomografia () {
		return getPropertyValue(this, dataTomografia, PROP_DATA_TOMOGRAFIA); 
	}

	/**
	 * Set the value related to the column: dt_tomografia
	 * @param dataTomografia the dt_tomografia value
	 */
	public void setDataTomografia (java.util.Date dataTomografia) {
//        java.util.Date dataTomografiaOld = this.dataTomografia;
		this.dataTomografia = dataTomografia;
//        this.getPropertyChangeSupport().firePropertyChange ("dataTomografia", dataTomografiaOld, dataTomografia);
	}



	/**
	 * Return the value associated with the column: coletou_amostra
	 */
	public java.lang.Long getColetouAmostra () {
		return getPropertyValue(this, coletouAmostra, PROP_COLETOU_AMOSTRA); 
	}

	/**
	 * Set the value related to the column: coletou_amostra
	 * @param coletouAmostra the coletou_amostra value
	 */
	public void setColetouAmostra (java.lang.Long coletouAmostra) {
//        java.lang.Long coletouAmostraOld = this.coletouAmostra;
		this.coletouAmostra = coletouAmostra;
//        this.getPropertyChangeSupport().firePropertyChange ("coletouAmostra", coletouAmostraOld, coletouAmostra);
	}



	/**
	 * Return the value associated with the column: dt_coleta_amostra
	 */
	public java.util.Date getDataColetaAmostra () {
		return getPropertyValue(this, dataColetaAmostra, PROP_DATA_COLETA_AMOSTRA); 
	}

	/**
	 * Set the value related to the column: dt_coleta_amostra
	 * @param dataColetaAmostra the dt_coleta_amostra value
	 */
	public void setDataColetaAmostra (java.util.Date dataColetaAmostra) {
//        java.util.Date dataColetaAmostraOld = this.dataColetaAmostra;
		this.dataColetaAmostra = dataColetaAmostra;
//        this.getPropertyChangeSupport().firePropertyChange ("dataColetaAmostra", dataColetaAmostraOld, dataColetaAmostra);
	}



	/**
	 * Return the value associated with the column: tipo_amostra
	 */
	public java.lang.Long getTipoAmostra () {
		return getPropertyValue(this, tipoAmostra, PROP_TIPO_AMOSTRA); 
	}

	/**
	 * Set the value related to the column: tipo_amostra
	 * @param tipoAmostra the tipo_amostra value
	 */
	public void setTipoAmostra (java.lang.Long tipoAmostra) {
//        java.lang.Long tipoAmostraOld = this.tipoAmostra;
		this.tipoAmostra = tipoAmostra;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAmostra", tipoAmostraOld, tipoAmostra);
	}



	/**
	 * Return the value associated with the column: outro_tipo_amostra
	 */
	public java.lang.String getOutroTipoAmostra () {
		return getPropertyValue(this, outroTipoAmostra, PROP_OUTRO_TIPO_AMOSTRA); 
	}

	/**
	 * Set the value related to the column: outro_tipo_amostra
	 * @param outroTipoAmostra the outro_tipo_amostra value
	 */
	public void setOutroTipoAmostra (java.lang.String outroTipoAmostra) {
//        java.lang.String outroTipoAmostraOld = this.outroTipoAmostra;
		this.outroTipoAmostra = outroTipoAmostra;
//        this.getPropertyChangeSupport().firePropertyChange ("outroTipoAmostra", outroTipoAmostraOld, outroTipoAmostra);
	}



	/**
	 * Return the value associated with the column: nr_requisicao_gal
	 */
	public java.lang.String getNumeroRequisicaoGal () {
		return getPropertyValue(this, numeroRequisicaoGal, PROP_NUMERO_REQUISICAO_GAL); 
	}

	/**
	 * Set the value related to the column: nr_requisicao_gal
	 * @param numeroRequisicaoGal the nr_requisicao_gal value
	 */
	public void setNumeroRequisicaoGal (java.lang.String numeroRequisicaoGal) {
//        java.lang.String numeroRequisicaoGalOld = this.numeroRequisicaoGal;
		this.numeroRequisicaoGal = numeroRequisicaoGal;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroRequisicaoGal", numeroRequisicaoGalOld, numeroRequisicaoGal);
	}



	/**
	 * Return the value associated with the column: tipo_teste_antigenos_virais
	 */
	public java.lang.Long getTipoTesteAntigenosVirais () {
		return getPropertyValue(this, tipoTesteAntigenosVirais, PROP_TIPO_TESTE_ANTIGENOS_VIRAIS); 
	}

	/**
	 * Set the value related to the column: tipo_teste_antigenos_virais
	 * @param tipoTesteAntigenosVirais the tipo_teste_antigenos_virais value
	 */
	public void setTipoTesteAntigenosVirais (java.lang.Long tipoTesteAntigenosVirais) {
//        java.lang.Long tipoTesteAntigenosViraisOld = this.tipoTesteAntigenosVirais;
		this.tipoTesteAntigenosVirais = tipoTesteAntigenosVirais;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoTesteAntigenosVirais", tipoTesteAntigenosViraisOld, tipoTesteAntigenosVirais);
	}



	/**
	 * Return the value associated with the column: dt_antigenos_virais
	 */
	public java.util.Date getDataAntigenosVirais () {
		return getPropertyValue(this, dataAntigenosVirais, PROP_DATA_ANTIGENOS_VIRAIS); 
	}

	/**
	 * Set the value related to the column: dt_antigenos_virais
	 * @param dataAntigenosVirais the dt_antigenos_virais value
	 */
	public void setDataAntigenosVirais (java.util.Date dataAntigenosVirais) {
//        java.util.Date dataAntigenosViraisOld = this.dataAntigenosVirais;
		this.dataAntigenosVirais = dataAntigenosVirais;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAntigenosVirais", dataAntigenosViraisOld, dataAntigenosVirais);
	}



	/**
	 * Return the value associated with the column: resultado_teste_antigenico
	 */
	public java.lang.Long getResultadoTesteAntigenico () {
		return getPropertyValue(this, resultadoTesteAntigenico, PROP_RESULTADO_TESTE_ANTIGENICO); 
	}

	/**
	 * Set the value related to the column: resultado_teste_antigenico
	 * @param resultadoTesteAntigenico the resultado_teste_antigenico value
	 */
	public void setResultadoTesteAntigenico (java.lang.Long resultadoTesteAntigenico) {
//        java.lang.Long resultadoTesteAntigenicoOld = this.resultadoTesteAntigenico;
		this.resultadoTesteAntigenico = resultadoTesteAntigenico;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoTesteAntigenico", resultadoTesteAntigenicoOld, resultadoTesteAntigenico);
	}



	/**
	 * Return the value associated with the column: agente_etiologico_TA_positivo_influenza
	 */
	public java.lang.Long getAgenteEtiologicoTAPositivoInfluenza () {
		return getPropertyValue(this, agenteEtiologicoTAPositivoInfluenza, PROP_AGENTE_ETIOLOGICO_T_A_POSITIVO_INFLUENZA); 
	}

	/**
	 * Set the value related to the column: agente_etiologico_TA_positivo_influenza
	 * @param agenteEtiologicoTAPositivoInfluenza the agente_etiologico_TA_positivo_influenza value
	 */
	public void setAgenteEtiologicoTAPositivoInfluenza (java.lang.Long agenteEtiologicoTAPositivoInfluenza) {
//        java.lang.Long agenteEtiologicoTAPositivoInfluenzaOld = this.agenteEtiologicoTAPositivoInfluenza;
		this.agenteEtiologicoTAPositivoInfluenza = agenteEtiologicoTAPositivoInfluenza;
//        this.getPropertyChangeSupport().firePropertyChange ("agenteEtiologicoTAPositivoInfluenza", agenteEtiologicoTAPositivoInfluenzaOld, agenteEtiologicoTAPositivoInfluenza);
	}



	/**
	 * Return the value associated with the column: se_sim_qual_influenzaTA
	 */
	public java.lang.Long getSeSimQualInfluenzaTA () {
		return getPropertyValue(this, seSimQualInfluenzaTA, PROP_SE_SIM_QUAL_INFLUENZA_T_A); 
	}

	/**
	 * Set the value related to the column: se_sim_qual_influenzaTA
	 * @param seSimQualInfluenzaTA the se_sim_qual_influenzaTA value
	 */
	public void setSeSimQualInfluenzaTA (java.lang.Long seSimQualInfluenzaTA) {
//        java.lang.Long seSimQualInfluenzaTAOld = this.seSimQualInfluenzaTA;
		this.seSimQualInfluenzaTA = seSimQualInfluenzaTA;
//        this.getPropertyChangeSupport().firePropertyChange ("seSimQualInfluenzaTA", seSimQualInfluenzaTAOld, seSimQualInfluenzaTA);
	}



	/**
	 * Return the value associated with the column: positivo_para_outro_virusTA
	 */
	public java.lang.Long getPositivoParaOutroVirusTA () {
		return getPropertyValue(this, positivoParaOutroVirusTA, PROP_POSITIVO_PARA_OUTRO_VIRUS_T_A); 
	}

	/**
	 * Set the value related to the column: positivo_para_outro_virusTA
	 * @param positivoParaOutroVirusTA the positivo_para_outro_virusTA value
	 */
	public void setPositivoParaOutroVirusTA (java.lang.Long positivoParaOutroVirusTA) {
//        java.lang.Long positivoParaOutroVirusTAOld = this.positivoParaOutroVirusTA;
		this.positivoParaOutroVirusTA = positivoParaOutroVirusTA;
//        this.getPropertyChangeSupport().firePropertyChange ("positivoParaOutroVirusTA", positivoParaOutroVirusTAOld, positivoParaOutroVirusTA);
	}



	/**
	 * Return the value associated with the column: se_outros_virus_respiratorios_sarscov2TA
	 */
	public java.lang.Long getOutroVirusRespiratorioQuaisSarsCov2TA () {
		return getPropertyValue(this, outroVirusRespiratorioQuaisSarsCov2TA, PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_SARS_COV2_T_A); 
	}

	/**
	 * Set the value related to the column: se_outros_virus_respiratorios_sarscov2TA
	 * @param outroVirusRespiratorioQuaisSarsCov2TA the se_outros_virus_respiratorios_sarscov2TA value
	 */
	public void setOutroVirusRespiratorioQuaisSarsCov2TA (java.lang.Long outroVirusRespiratorioQuaisSarsCov2TA) {
//        java.lang.Long outroVirusRespiratorioQuaisSarsCov2TAOld = this.outroVirusRespiratorioQuaisSarsCov2TA;
		this.outroVirusRespiratorioQuaisSarsCov2TA = outroVirusRespiratorioQuaisSarsCov2TA;
//        this.getPropertyChangeSupport().firePropertyChange ("outroVirusRespiratorioQuaisSarsCov2TA", outroVirusRespiratorioQuaisSarsCov2TAOld, outroVirusRespiratorioQuaisSarsCov2TA);
	}



	/**
	 * Return the value associated with the column: se_outros_virus_respiratorios_virus_sincicial_respiratorioTA
	 */
	public java.lang.Long getOutroVirusRespiratorioQuaisVirusSincicialRespiratorioTA () {
		return getPropertyValue(this, outroVirusRespiratorioQuaisVirusSincicialRespiratorioTA, PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_VIRUS_SINCICIAL_RESPIRATORIO_T_A); 
	}

	/**
	 * Set the value related to the column: se_outros_virus_respiratorios_virus_sincicial_respiratorioTA
	 * @param outroVirusRespiratorioQuaisVirusSincicialRespiratorioTA the se_outros_virus_respiratorios_virus_sincicial_respiratorioTA value
	 */
	public void setOutroVirusRespiratorioQuaisVirusSincicialRespiratorioTA (java.lang.Long outroVirusRespiratorioQuaisVirusSincicialRespiratorioTA) {
//        java.lang.Long outroVirusRespiratorioQuaisVirusSincicialRespiratorioTAOld = this.outroVirusRespiratorioQuaisVirusSincicialRespiratorioTA;
		this.outroVirusRespiratorioQuaisVirusSincicialRespiratorioTA = outroVirusRespiratorioQuaisVirusSincicialRespiratorioTA;
//        this.getPropertyChangeSupport().firePropertyChange ("outroVirusRespiratorioQuaisVirusSincicialRespiratorioTA", outroVirusRespiratorioQuaisVirusSincicialRespiratorioTAOld, outroVirusRespiratorioQuaisVirusSincicialRespiratorioTA);
	}



	/**
	 * Return the value associated with the column: se_outros_virus_respiratorios_parainfluenza1TA
	 */
	public java.lang.Long getOutroVirusRespiratorioQuaisParainfluenza1TA () {
		return getPropertyValue(this, outroVirusRespiratorioQuaisParainfluenza1TA, PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_PARAINFLUENZA1_T_A); 
	}

	/**
	 * Set the value related to the column: se_outros_virus_respiratorios_parainfluenza1TA
	 * @param outroVirusRespiratorioQuaisParainfluenza1TA the se_outros_virus_respiratorios_parainfluenza1TA value
	 */
	public void setOutroVirusRespiratorioQuaisParainfluenza1TA (java.lang.Long outroVirusRespiratorioQuaisParainfluenza1TA) {
//        java.lang.Long outroVirusRespiratorioQuaisParainfluenza1TAOld = this.outroVirusRespiratorioQuaisParainfluenza1TA;
		this.outroVirusRespiratorioQuaisParainfluenza1TA = outroVirusRespiratorioQuaisParainfluenza1TA;
//        this.getPropertyChangeSupport().firePropertyChange ("outroVirusRespiratorioQuaisParainfluenza1TA", outroVirusRespiratorioQuaisParainfluenza1TAOld, outroVirusRespiratorioQuaisParainfluenza1TA);
	}



	/**
	 * Return the value associated with the column: se_outros_virus_respiratorios_parainfluenza2TA
	 */
	public java.lang.Long getOutroVirusRespiratorioQuaisParainfluenza2TA () {
		return getPropertyValue(this, outroVirusRespiratorioQuaisParainfluenza2TA, PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_PARAINFLUENZA2_T_A); 
	}

	/**
	 * Set the value related to the column: se_outros_virus_respiratorios_parainfluenza2TA
	 * @param outroVirusRespiratorioQuaisParainfluenza2TA the se_outros_virus_respiratorios_parainfluenza2TA value
	 */
	public void setOutroVirusRespiratorioQuaisParainfluenza2TA (java.lang.Long outroVirusRespiratorioQuaisParainfluenza2TA) {
//        java.lang.Long outroVirusRespiratorioQuaisParainfluenza2TAOld = this.outroVirusRespiratorioQuaisParainfluenza2TA;
		this.outroVirusRespiratorioQuaisParainfluenza2TA = outroVirusRespiratorioQuaisParainfluenza2TA;
//        this.getPropertyChangeSupport().firePropertyChange ("outroVirusRespiratorioQuaisParainfluenza2TA", outroVirusRespiratorioQuaisParainfluenza2TAOld, outroVirusRespiratorioQuaisParainfluenza2TA);
	}



	/**
	 * Return the value associated with the column: se_outros_virus_respiratorios_parainfluenza3TA
	 */
	public java.lang.Long getOutroVirusRespiratorioQuaisParainfluenza3TA () {
		return getPropertyValue(this, outroVirusRespiratorioQuaisParainfluenza3TA, PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_PARAINFLUENZA3_T_A); 
	}

	/**
	 * Set the value related to the column: se_outros_virus_respiratorios_parainfluenza3TA
	 * @param outroVirusRespiratorioQuaisParainfluenza3TA the se_outros_virus_respiratorios_parainfluenza3TA value
	 */
	public void setOutroVirusRespiratorioQuaisParainfluenza3TA (java.lang.Long outroVirusRespiratorioQuaisParainfluenza3TA) {
//        java.lang.Long outroVirusRespiratorioQuaisParainfluenza3TAOld = this.outroVirusRespiratorioQuaisParainfluenza3TA;
		this.outroVirusRespiratorioQuaisParainfluenza3TA = outroVirusRespiratorioQuaisParainfluenza3TA;
//        this.getPropertyChangeSupport().firePropertyChange ("outroVirusRespiratorioQuaisParainfluenza3TA", outroVirusRespiratorioQuaisParainfluenza3TAOld, outroVirusRespiratorioQuaisParainfluenza3TA);
	}



	/**
	 * Return the value associated with the column: se_outros_virus_respiratorios_adenovirusTA
	 */
	public java.lang.Long getOutroVirusRespiratorioQuaisAdenovirusTA () {
		return getPropertyValue(this, outroVirusRespiratorioQuaisAdenovirusTA, PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_ADENOVIRUS_T_A); 
	}

	/**
	 * Set the value related to the column: se_outros_virus_respiratorios_adenovirusTA
	 * @param outroVirusRespiratorioQuaisAdenovirusTA the se_outros_virus_respiratorios_adenovirusTA value
	 */
	public void setOutroVirusRespiratorioQuaisAdenovirusTA (java.lang.Long outroVirusRespiratorioQuaisAdenovirusTA) {
//        java.lang.Long outroVirusRespiratorioQuaisAdenovirusTAOld = this.outroVirusRespiratorioQuaisAdenovirusTA;
		this.outroVirusRespiratorioQuaisAdenovirusTA = outroVirusRespiratorioQuaisAdenovirusTA;
//        this.getPropertyChangeSupport().firePropertyChange ("outroVirusRespiratorioQuaisAdenovirusTA", outroVirusRespiratorioQuaisAdenovirusTAOld, outroVirusRespiratorioQuaisAdenovirusTA);
	}



	/**
	 * Return the value associated with the column: outro_virus_respiratorioTA
	 */
	public java.lang.String getOutroVirusRespiratorioTA () {
		return getPropertyValue(this, outroVirusRespiratorioTA, PROP_OUTRO_VIRUS_RESPIRATORIO_T_A); 
	}

	/**
	 * Set the value related to the column: outro_virus_respiratorioTA
	 * @param outroVirusRespiratorioTA the outro_virus_respiratorioTA value
	 */
	public void setOutroVirusRespiratorioTA (java.lang.String outroVirusRespiratorioTA) {
//        java.lang.String outroVirusRespiratorioTAOld = this.outroVirusRespiratorioTA;
		this.outroVirusRespiratorioTA = outroVirusRespiratorioTA;
//        this.getPropertyChangeSupport().firePropertyChange ("outroVirusRespiratorioTA", outroVirusRespiratorioTAOld, outroVirusRespiratorioTA);
	}



	/**
	 * Return the value associated with the column: resultado_metodo_biologia_molecular
	 */
	public java.lang.Long getResultadoMetodoBiologiaMolecular () {
		return getPropertyValue(this, resultadoMetodoBiologiaMolecular, PROP_RESULTADO_METODO_BIOLOGIA_MOLECULAR); 
	}

	/**
	 * Set the value related to the column: resultado_metodo_biologia_molecular
	 * @param resultadoMetodoBiologiaMolecular the resultado_metodo_biologia_molecular value
	 */
	public void setResultadoMetodoBiologiaMolecular (java.lang.Long resultadoMetodoBiologiaMolecular) {
//        java.lang.Long resultadoMetodoBiologiaMolecularOld = this.resultadoMetodoBiologiaMolecular;
		this.resultadoMetodoBiologiaMolecular = resultadoMetodoBiologiaMolecular;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoMetodoBiologiaMolecular", resultadoMetodoBiologiaMolecularOld, resultadoMetodoBiologiaMolecular);
	}



	/**
	 * Return the value associated with the column: dt_resultado_metodo_biologia_molecular
	 */
	public java.util.Date getDataResultadoMetodoBiologiaMolecular () {
		return getPropertyValue(this, dataResultadoMetodoBiologiaMolecular, PROP_DATA_RESULTADO_METODO_BIOLOGIA_MOLECULAR); 
	}

	/**
	 * Set the value related to the column: dt_resultado_metodo_biologia_molecular
	 * @param dataResultadoMetodoBiologiaMolecular the dt_resultado_metodo_biologia_molecular value
	 */
	public void setDataResultadoMetodoBiologiaMolecular (java.util.Date dataResultadoMetodoBiologiaMolecular) {
//        java.util.Date dataResultadoMetodoBiologiaMolecularOld = this.dataResultadoMetodoBiologiaMolecular;
		this.dataResultadoMetodoBiologiaMolecular = dataResultadoMetodoBiologiaMolecular;
//        this.getPropertyChangeSupport().firePropertyChange ("dataResultadoMetodoBiologiaMolecular", dataResultadoMetodoBiologiaMolecularOld, dataResultadoMetodoBiologiaMolecular);
	}



	/**
	 * Return the value associated with the column: se_sim_qual_influenzaBM
	 */
	public java.lang.Long getSeSimQualInfluenzaBM () {
		return getPropertyValue(this, seSimQualInfluenzaBM, PROP_SE_SIM_QUAL_INFLUENZA_B_M); 
	}

	/**
	 * Set the value related to the column: se_sim_qual_influenzaBM
	 * @param seSimQualInfluenzaBM the se_sim_qual_influenzaBM value
	 */
	public void setSeSimQualInfluenzaBM (java.lang.Long seSimQualInfluenzaBM) {
//        java.lang.Long seSimQualInfluenzaBMOld = this.seSimQualInfluenzaBM;
		this.seSimQualInfluenzaBM = seSimQualInfluenzaBM;
//        this.getPropertyChangeSupport().firePropertyChange ("seSimQualInfluenzaBM", seSimQualInfluenzaBMOld, seSimQualInfluenzaBM);
	}



	/**
	 * Return the value associated with the column: agente_etiologico_BM_positivo_influenza
	 */
	public java.lang.Long getAgenteEtiologicoBMPositivoInfluenza () {
		return getPropertyValue(this, agenteEtiologicoBMPositivoInfluenza, PROP_AGENTE_ETIOLOGICO_B_M_POSITIVO_INFLUENZA); 
	}

	/**
	 * Set the value related to the column: agente_etiologico_BM_positivo_influenza
	 * @param agenteEtiologicoBMPositivoInfluenza the agente_etiologico_BM_positivo_influenza value
	 */
	public void setAgenteEtiologicoBMPositivoInfluenza (java.lang.Long agenteEtiologicoBMPositivoInfluenza) {
//        java.lang.Long agenteEtiologicoBMPositivoInfluenzaOld = this.agenteEtiologicoBMPositivoInfluenza;
		this.agenteEtiologicoBMPositivoInfluenza = agenteEtiologicoBMPositivoInfluenza;
//        this.getPropertyChangeSupport().firePropertyChange ("agenteEtiologicoBMPositivoInfluenza", agenteEtiologicoBMPositivoInfluenzaOld, agenteEtiologicoBMPositivoInfluenza);
	}



	/**
	 * Return the value associated with the column: influenzaA_qual_subtipo
	 */
	public java.lang.Long getInfluenzaAQualSubtipo () {
		return getPropertyValue(this, influenzaAQualSubtipo, PROP_INFLUENZA_A_QUAL_SUBTIPO); 
	}

	/**
	 * Set the value related to the column: influenzaA_qual_subtipo
	 * @param influenzaAQualSubtipo the influenzaA_qual_subtipo value
	 */
	public void setInfluenzaAQualSubtipo (java.lang.Long influenzaAQualSubtipo) {
//        java.lang.Long influenzaAQualSubtipoOld = this.influenzaAQualSubtipo;
		this.influenzaAQualSubtipo = influenzaAQualSubtipo;
//        this.getPropertyChangeSupport().firePropertyChange ("influenzaAQualSubtipo", influenzaAQualSubtipoOld, influenzaAQualSubtipo);
	}



	/**
	 * Return the value associated with the column: outro_influenzaA_qual_subtipo
	 */
	public java.lang.String getOutroInfluenzaAQualSubtipo () {
		return getPropertyValue(this, outroInfluenzaAQualSubtipo, PROP_OUTRO_INFLUENZA_A_QUAL_SUBTIPO); 
	}

	/**
	 * Set the value related to the column: outro_influenzaA_qual_subtipo
	 * @param outroInfluenzaAQualSubtipo the outro_influenzaA_qual_subtipo value
	 */
	public void setOutroInfluenzaAQualSubtipo (java.lang.String outroInfluenzaAQualSubtipo) {
//        java.lang.String outroInfluenzaAQualSubtipoOld = this.outroInfluenzaAQualSubtipo;
		this.outroInfluenzaAQualSubtipo = outroInfluenzaAQualSubtipo;
//        this.getPropertyChangeSupport().firePropertyChange ("outroInfluenzaAQualSubtipo", outroInfluenzaAQualSubtipoOld, outroInfluenzaAQualSubtipo);
	}



	/**
	 * Return the value associated with the column: influenzaB_qual_linhagem
	 */
	public java.lang.Long getInfluenzaBQualLinhagem () {
		return getPropertyValue(this, influenzaBQualLinhagem, PROP_INFLUENZA_B_QUAL_LINHAGEM); 
	}

	/**
	 * Set the value related to the column: influenzaB_qual_linhagem
	 * @param influenzaBQualLinhagem the influenzaB_qual_linhagem value
	 */
	public void setInfluenzaBQualLinhagem (java.lang.Long influenzaBQualLinhagem) {
//        java.lang.Long influenzaBQualLinhagemOld = this.influenzaBQualLinhagem;
		this.influenzaBQualLinhagem = influenzaBQualLinhagem;
//        this.getPropertyChangeSupport().firePropertyChange ("influenzaBQualLinhagem", influenzaBQualLinhagemOld, influenzaBQualLinhagem);
	}



	/**
	 * Return the value associated with the column: outro_influenzaB_qual_linhagem
	 */
	public java.lang.String getOutroInfluenzaBQualLinhagem () {
		return getPropertyValue(this, outroInfluenzaBQualLinhagem, PROP_OUTRO_INFLUENZA_B_QUAL_LINHAGEM); 
	}

	/**
	 * Set the value related to the column: outro_influenzaB_qual_linhagem
	 * @param outroInfluenzaBQualLinhagem the outro_influenzaB_qual_linhagem value
	 */
	public void setOutroInfluenzaBQualLinhagem (java.lang.String outroInfluenzaBQualLinhagem) {
//        java.lang.String outroInfluenzaBQualLinhagemOld = this.outroInfluenzaBQualLinhagem;
		this.outroInfluenzaBQualLinhagem = outroInfluenzaBQualLinhagem;
//        this.getPropertyChangeSupport().firePropertyChange ("outroInfluenzaBQualLinhagem", outroInfluenzaBQualLinhagemOld, outroInfluenzaBQualLinhagem);
	}



	/**
	 * Return the value associated with the column: positivo_para_outro_virusBM
	 */
	public java.lang.Long getPositivoParaOutroVirusBM () {
		return getPropertyValue(this, positivoParaOutroVirusBM, PROP_POSITIVO_PARA_OUTRO_VIRUS_B_M); 
	}

	/**
	 * Set the value related to the column: positivo_para_outro_virusBM
	 * @param positivoParaOutroVirusBM the positivo_para_outro_virusBM value
	 */
	public void setPositivoParaOutroVirusBM (java.lang.Long positivoParaOutroVirusBM) {
//        java.lang.Long positivoParaOutroVirusBMOld = this.positivoParaOutroVirusBM;
		this.positivoParaOutroVirusBM = positivoParaOutroVirusBM;
//        this.getPropertyChangeSupport().firePropertyChange ("positivoParaOutroVirusBM", positivoParaOutroVirusBMOld, positivoParaOutroVirusBM);
	}



	/**
	 * Return the value associated with the column: se_outros_virus_respiratorios_sarscov2BM
	 */
	public java.lang.Long getOutroVirusRespiratorioQuaisSarsCov2BM () {
		return getPropertyValue(this, outroVirusRespiratorioQuaisSarsCov2BM, PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_SARS_COV2_B_M); 
	}

	/**
	 * Set the value related to the column: se_outros_virus_respiratorios_sarscov2BM
	 * @param outroVirusRespiratorioQuaisSarsCov2BM the se_outros_virus_respiratorios_sarscov2BM value
	 */
	public void setOutroVirusRespiratorioQuaisSarsCov2BM (java.lang.Long outroVirusRespiratorioQuaisSarsCov2BM) {
//        java.lang.Long outroVirusRespiratorioQuaisSarsCov2BMOld = this.outroVirusRespiratorioQuaisSarsCov2BM;
		this.outroVirusRespiratorioQuaisSarsCov2BM = outroVirusRespiratorioQuaisSarsCov2BM;
//        this.getPropertyChangeSupport().firePropertyChange ("outroVirusRespiratorioQuaisSarsCov2BM", outroVirusRespiratorioQuaisSarsCov2BMOld, outroVirusRespiratorioQuaisSarsCov2BM);
	}



	/**
	 * Return the value associated with the column: se_outros_virus_respiratorios_virus_sincicial_respiratorioBM
	 */
	public java.lang.Long getOutroVirusRespiratorioQuaisVirusSincicialRespiratorioBM () {
		return getPropertyValue(this, outroVirusRespiratorioQuaisVirusSincicialRespiratorioBM, PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_VIRUS_SINCICIAL_RESPIRATORIO_B_M); 
	}

	/**
	 * Set the value related to the column: se_outros_virus_respiratorios_virus_sincicial_respiratorioBM
	 * @param outroVirusRespiratorioQuaisVirusSincicialRespiratorioBM the se_outros_virus_respiratorios_virus_sincicial_respiratorioBM value
	 */
	public void setOutroVirusRespiratorioQuaisVirusSincicialRespiratorioBM (java.lang.Long outroVirusRespiratorioQuaisVirusSincicialRespiratorioBM) {
//        java.lang.Long outroVirusRespiratorioQuaisVirusSincicialRespiratorioBMOld = this.outroVirusRespiratorioQuaisVirusSincicialRespiratorioBM;
		this.outroVirusRespiratorioQuaisVirusSincicialRespiratorioBM = outroVirusRespiratorioQuaisVirusSincicialRespiratorioBM;
//        this.getPropertyChangeSupport().firePropertyChange ("outroVirusRespiratorioQuaisVirusSincicialRespiratorioBM", outroVirusRespiratorioQuaisVirusSincicialRespiratorioBMOld, outroVirusRespiratorioQuaisVirusSincicialRespiratorioBM);
	}



	/**
	 * Return the value associated with the column: se_outros_virus_respiratorios_parainfluenza1BM
	 */
	public java.lang.Long getOutroVirusRespiratorioQuaisParainfluenza1BM () {
		return getPropertyValue(this, outroVirusRespiratorioQuaisParainfluenza1BM, PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_PARAINFLUENZA1_B_M); 
	}

	/**
	 * Set the value related to the column: se_outros_virus_respiratorios_parainfluenza1BM
	 * @param outroVirusRespiratorioQuaisParainfluenza1BM the se_outros_virus_respiratorios_parainfluenza1BM value
	 */
	public void setOutroVirusRespiratorioQuaisParainfluenza1BM (java.lang.Long outroVirusRespiratorioQuaisParainfluenza1BM) {
//        java.lang.Long outroVirusRespiratorioQuaisParainfluenza1BMOld = this.outroVirusRespiratorioQuaisParainfluenza1BM;
		this.outroVirusRespiratorioQuaisParainfluenza1BM = outroVirusRespiratorioQuaisParainfluenza1BM;
//        this.getPropertyChangeSupport().firePropertyChange ("outroVirusRespiratorioQuaisParainfluenza1BM", outroVirusRespiratorioQuaisParainfluenza1BMOld, outroVirusRespiratorioQuaisParainfluenza1BM);
	}



	/**
	 * Return the value associated with the column: se_outros_virus_respiratorios_parainfluenza2BM
	 */
	public java.lang.Long getOutroVirusRespiratorioQuaisParainfluenza2BM () {
		return getPropertyValue(this, outroVirusRespiratorioQuaisParainfluenza2BM, PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_PARAINFLUENZA2_B_M); 
	}

	/**
	 * Set the value related to the column: se_outros_virus_respiratorios_parainfluenza2BM
	 * @param outroVirusRespiratorioQuaisParainfluenza2BM the se_outros_virus_respiratorios_parainfluenza2BM value
	 */
	public void setOutroVirusRespiratorioQuaisParainfluenza2BM (java.lang.Long outroVirusRespiratorioQuaisParainfluenza2BM) {
//        java.lang.Long outroVirusRespiratorioQuaisParainfluenza2BMOld = this.outroVirusRespiratorioQuaisParainfluenza2BM;
		this.outroVirusRespiratorioQuaisParainfluenza2BM = outroVirusRespiratorioQuaisParainfluenza2BM;
//        this.getPropertyChangeSupport().firePropertyChange ("outroVirusRespiratorioQuaisParainfluenza2BM", outroVirusRespiratorioQuaisParainfluenza2BMOld, outroVirusRespiratorioQuaisParainfluenza2BM);
	}



	/**
	 * Return the value associated with the column: se_outros_virus_respiratorios_parainfluenza3BM
	 */
	public java.lang.Long getOutroVirusRespiratorioQuaisParainfluenza3BM () {
		return getPropertyValue(this, outroVirusRespiratorioQuaisParainfluenza3BM, PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_PARAINFLUENZA3_B_M); 
	}

	/**
	 * Set the value related to the column: se_outros_virus_respiratorios_parainfluenza3BM
	 * @param outroVirusRespiratorioQuaisParainfluenza3BM the se_outros_virus_respiratorios_parainfluenza3BM value
	 */
	public void setOutroVirusRespiratorioQuaisParainfluenza3BM (java.lang.Long outroVirusRespiratorioQuaisParainfluenza3BM) {
//        java.lang.Long outroVirusRespiratorioQuaisParainfluenza3BMOld = this.outroVirusRespiratorioQuaisParainfluenza3BM;
		this.outroVirusRespiratorioQuaisParainfluenza3BM = outroVirusRespiratorioQuaisParainfluenza3BM;
//        this.getPropertyChangeSupport().firePropertyChange ("outroVirusRespiratorioQuaisParainfluenza3BM", outroVirusRespiratorioQuaisParainfluenza3BMOld, outroVirusRespiratorioQuaisParainfluenza3BM);
	}



	/**
	 * Return the value associated with the column: se_outros_virus_respiratorios_parainfluenza4BM
	 */
	public java.lang.Long getOutroVirusRespiratorioQuaisParainfluenza4BM () {
		return getPropertyValue(this, outroVirusRespiratorioQuaisParainfluenza4BM, PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_PARAINFLUENZA4_B_M); 
	}

	/**
	 * Set the value related to the column: se_outros_virus_respiratorios_parainfluenza4BM
	 * @param outroVirusRespiratorioQuaisParainfluenza4BM the se_outros_virus_respiratorios_parainfluenza4BM value
	 */
	public void setOutroVirusRespiratorioQuaisParainfluenza4BM (java.lang.Long outroVirusRespiratorioQuaisParainfluenza4BM) {
//        java.lang.Long outroVirusRespiratorioQuaisParainfluenza4BMOld = this.outroVirusRespiratorioQuaisParainfluenza4BM;
		this.outroVirusRespiratorioQuaisParainfluenza4BM = outroVirusRespiratorioQuaisParainfluenza4BM;
//        this.getPropertyChangeSupport().firePropertyChange ("outroVirusRespiratorioQuaisParainfluenza4BM", outroVirusRespiratorioQuaisParainfluenza4BMOld, outroVirusRespiratorioQuaisParainfluenza4BM);
	}



	/**
	 * Return the value associated with the column: se_outros_virus_respiratorios_adenovirusBM
	 */
	public java.lang.Long getOutroVirusRespiratorioQuaisAdenovirusBM () {
		return getPropertyValue(this, outroVirusRespiratorioQuaisAdenovirusBM, PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_ADENOVIRUS_B_M); 
	}

	/**
	 * Set the value related to the column: se_outros_virus_respiratorios_adenovirusBM
	 * @param outroVirusRespiratorioQuaisAdenovirusBM the se_outros_virus_respiratorios_adenovirusBM value
	 */
	public void setOutroVirusRespiratorioQuaisAdenovirusBM (java.lang.Long outroVirusRespiratorioQuaisAdenovirusBM) {
//        java.lang.Long outroVirusRespiratorioQuaisAdenovirusBMOld = this.outroVirusRespiratorioQuaisAdenovirusBM;
		this.outroVirusRespiratorioQuaisAdenovirusBM = outroVirusRespiratorioQuaisAdenovirusBM;
//        this.getPropertyChangeSupport().firePropertyChange ("outroVirusRespiratorioQuaisAdenovirusBM", outroVirusRespiratorioQuaisAdenovirusBMOld, outroVirusRespiratorioQuaisAdenovirusBM);
	}



	/**
	 * Return the value associated with the column: se_outros_virus_respiratorios_metapneumovirusBM
	 */
	public java.lang.Long getOutroVirusRespiratorioQuaisMetapneumovirusBM () {
		return getPropertyValue(this, outroVirusRespiratorioQuaisMetapneumovirusBM, PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_METAPNEUMOVIRUS_B_M); 
	}

	/**
	 * Set the value related to the column: se_outros_virus_respiratorios_metapneumovirusBM
	 * @param outroVirusRespiratorioQuaisMetapneumovirusBM the se_outros_virus_respiratorios_metapneumovirusBM value
	 */
	public void setOutroVirusRespiratorioQuaisMetapneumovirusBM (java.lang.Long outroVirusRespiratorioQuaisMetapneumovirusBM) {
//        java.lang.Long outroVirusRespiratorioQuaisMetapneumovirusBMOld = this.outroVirusRespiratorioQuaisMetapneumovirusBM;
		this.outroVirusRespiratorioQuaisMetapneumovirusBM = outroVirusRespiratorioQuaisMetapneumovirusBM;
//        this.getPropertyChangeSupport().firePropertyChange ("outroVirusRespiratorioQuaisMetapneumovirusBM", outroVirusRespiratorioQuaisMetapneumovirusBMOld, outroVirusRespiratorioQuaisMetapneumovirusBM);
	}



	/**
	 * Return the value associated with the column: se_outros_virus_respiratorios_bocavirusBM
	 */
	public java.lang.Long getOutroVirusRespiratorioQuaisBocavirusBM () {
		return getPropertyValue(this, outroVirusRespiratorioQuaisBocavirusBM, PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_BOCAVIRUS_B_M); 
	}

	/**
	 * Set the value related to the column: se_outros_virus_respiratorios_bocavirusBM
	 * @param outroVirusRespiratorioQuaisBocavirusBM the se_outros_virus_respiratorios_bocavirusBM value
	 */
	public void setOutroVirusRespiratorioQuaisBocavirusBM (java.lang.Long outroVirusRespiratorioQuaisBocavirusBM) {
//        java.lang.Long outroVirusRespiratorioQuaisBocavirusBMOld = this.outroVirusRespiratorioQuaisBocavirusBM;
		this.outroVirusRespiratorioQuaisBocavirusBM = outroVirusRespiratorioQuaisBocavirusBM;
//        this.getPropertyChangeSupport().firePropertyChange ("outroVirusRespiratorioQuaisBocavirusBM", outroVirusRespiratorioQuaisBocavirusBMOld, outroVirusRespiratorioQuaisBocavirusBM);
	}



	/**
	 * Return the value associated with the column: se_outros_virus_respiratorios_rinovirusBM
	 */
	public java.lang.Long getOutroVirusRespiratorioQuaisRinovirusBM () {
		return getPropertyValue(this, outroVirusRespiratorioQuaisRinovirusBM, PROP_OUTRO_VIRUS_RESPIRATORIO_QUAIS_RINOVIRUS_B_M); 
	}

	/**
	 * Set the value related to the column: se_outros_virus_respiratorios_rinovirusBM
	 * @param outroVirusRespiratorioQuaisRinovirusBM the se_outros_virus_respiratorios_rinovirusBM value
	 */
	public void setOutroVirusRespiratorioQuaisRinovirusBM (java.lang.Long outroVirusRespiratorioQuaisRinovirusBM) {
//        java.lang.Long outroVirusRespiratorioQuaisRinovirusBMOld = this.outroVirusRespiratorioQuaisRinovirusBM;
		this.outroVirusRespiratorioQuaisRinovirusBM = outroVirusRespiratorioQuaisRinovirusBM;
//        this.getPropertyChangeSupport().firePropertyChange ("outroVirusRespiratorioQuaisRinovirusBM", outroVirusRespiratorioQuaisRinovirusBMOld, outroVirusRespiratorioQuaisRinovirusBM);
	}



	/**
	 * Return the value associated with the column: outro_virus_respiratorioBM
	 */
	public java.lang.String getOutroVirusRespiratorioBM () {
		return getPropertyValue(this, outroVirusRespiratorioBM, PROP_OUTRO_VIRUS_RESPIRATORIO_B_M); 
	}

	/**
	 * Set the value related to the column: outro_virus_respiratorioBM
	 * @param outroVirusRespiratorioBM the outro_virus_respiratorioBM value
	 */
	public void setOutroVirusRespiratorioBM (java.lang.String outroVirusRespiratorioBM) {
//        java.lang.String outroVirusRespiratorioBMOld = this.outroVirusRespiratorioBM;
		this.outroVirusRespiratorioBM = outroVirusRespiratorioBM;
//        this.getPropertyChangeSupport().firePropertyChange ("outroVirusRespiratorioBM", outroVirusRespiratorioBMOld, outroVirusRespiratorioBM);
	}



	/**
	 * Return the value associated with the column: tipo_amostra_sorologica_sarscov2
	 */
	public java.lang.Long getTipoAmostraSorologicaSarsCov2 () {
		return getPropertyValue(this, tipoAmostraSorologicaSarsCov2, PROP_TIPO_AMOSTRA_SOROLOGICA_SARS_COV2); 
	}

	/**
	 * Set the value related to the column: tipo_amostra_sorologica_sarscov2
	 * @param tipoAmostraSorologicaSarsCov2 the tipo_amostra_sorologica_sarscov2 value
	 */
	public void setTipoAmostraSorologicaSarsCov2 (java.lang.Long tipoAmostraSorologicaSarsCov2) {
//        java.lang.Long tipoAmostraSorologicaSarsCov2Old = this.tipoAmostraSorologicaSarsCov2;
		this.tipoAmostraSorologicaSarsCov2 = tipoAmostraSorologicaSarsCov2;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAmostraSorologicaSarsCov2", tipoAmostraSorologicaSarsCov2Old, tipoAmostraSorologicaSarsCov2);
	}



	/**
	 * Return the value associated with the column: outro_tipo_amostra_sorologica_sarscov2
	 */
	public java.lang.String getOutroTipoAmostraSorologicaSarsCov2 () {
		return getPropertyValue(this, outroTipoAmostraSorologicaSarsCov2, PROP_OUTRO_TIPO_AMOSTRA_SOROLOGICA_SARS_COV2); 
	}

	/**
	 * Set the value related to the column: outro_tipo_amostra_sorologica_sarscov2
	 * @param outroTipoAmostraSorologicaSarsCov2 the outro_tipo_amostra_sorologica_sarscov2 value
	 */
	public void setOutroTipoAmostraSorologicaSarsCov2 (java.lang.String outroTipoAmostraSorologicaSarsCov2) {
//        java.lang.String outroTipoAmostraSorologicaSarsCov2Old = this.outroTipoAmostraSorologicaSarsCov2;
		this.outroTipoAmostraSorologicaSarsCov2 = outroTipoAmostraSorologicaSarsCov2;
//        this.getPropertyChangeSupport().firePropertyChange ("outroTipoAmostraSorologicaSarsCov2", outroTipoAmostraSorologicaSarsCov2Old, outroTipoAmostraSorologicaSarsCov2);
	}



	/**
	 * Return the value associated with the column: data_tipo_amostra_sorologica_sarscov2
	 */
	public java.util.Date getDataTipoAmostraSorologicaSarsCov2 () {
		return getPropertyValue(this, dataTipoAmostraSorologicaSarsCov2, PROP_DATA_TIPO_AMOSTRA_SOROLOGICA_SARS_COV2); 
	}

	/**
	 * Set the value related to the column: data_tipo_amostra_sorologica_sarscov2
	 * @param dataTipoAmostraSorologicaSarsCov2 the data_tipo_amostra_sorologica_sarscov2 value
	 */
	public void setDataTipoAmostraSorologicaSarsCov2 (java.util.Date dataTipoAmostraSorologicaSarsCov2) {
//        java.util.Date dataTipoAmostraSorologicaSarsCov2Old = this.dataTipoAmostraSorologicaSarsCov2;
		this.dataTipoAmostraSorologicaSarsCov2 = dataTipoAmostraSorologicaSarsCov2;
//        this.getPropertyChangeSupport().firePropertyChange ("dataTipoAmostraSorologicaSarsCov2", dataTipoAmostraSorologicaSarsCov2Old, dataTipoAmostraSorologicaSarsCov2);
	}



	/**
	 * Return the value associated with the column: tipo_sorologica_sarscov2
	 */
	public java.lang.Long getTipoSorologicaSarsCov2 () {
		return getPropertyValue(this, tipoSorologicaSarsCov2, PROP_TIPO_SOROLOGICA_SARS_COV2); 
	}

	/**
	 * Set the value related to the column: tipo_sorologica_sarscov2
	 * @param tipoSorologicaSarsCov2 the tipo_sorologica_sarscov2 value
	 */
	public void setTipoSorologicaSarsCov2 (java.lang.Long tipoSorologicaSarsCov2) {
//        java.lang.Long tipoSorologicaSarsCov2Old = this.tipoSorologicaSarsCov2;
		this.tipoSorologicaSarsCov2 = tipoSorologicaSarsCov2;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoSorologicaSarsCov2", tipoSorologicaSarsCov2Old, tipoSorologicaSarsCov2);
	}



	/**
	 * Return the value associated with the column: outro_tipo_sorologica_sarscov2
	 */
	public java.lang.String getOutroTipoSorologicaSarsCov2 () {
		return getPropertyValue(this, outroTipoSorologicaSarsCov2, PROP_OUTRO_TIPO_SOROLOGICA_SARS_COV2); 
	}

	/**
	 * Set the value related to the column: outro_tipo_sorologica_sarscov2
	 * @param outroTipoSorologicaSarsCov2 the outro_tipo_sorologica_sarscov2 value
	 */
	public void setOutroTipoSorologicaSarsCov2 (java.lang.String outroTipoSorologicaSarsCov2) {
//        java.lang.String outroTipoSorologicaSarsCov2Old = this.outroTipoSorologicaSarsCov2;
		this.outroTipoSorologicaSarsCov2 = outroTipoSorologicaSarsCov2;
//        this.getPropertyChangeSupport().firePropertyChange ("outroTipoSorologicaSarsCov2", outroTipoSorologicaSarsCov2Old, outroTipoSorologicaSarsCov2);
	}



	/**
	 * Return the value associated with the column: data_tipo_sorologica_sarscov2
	 */
	public java.util.Date getDataTipoSorologicaSarsCov2 () {
		return getPropertyValue(this, dataTipoSorologicaSarsCov2, PROP_DATA_TIPO_SOROLOGICA_SARS_COV2); 
	}

	/**
	 * Set the value related to the column: data_tipo_sorologica_sarscov2
	 * @param dataTipoSorologicaSarsCov2 the data_tipo_sorologica_sarscov2 value
	 */
	public void setDataTipoSorologicaSarsCov2 (java.util.Date dataTipoSorologicaSarsCov2) {
//        java.util.Date dataTipoSorologicaSarsCov2Old = this.dataTipoSorologicaSarsCov2;
		this.dataTipoSorologicaSarsCov2 = dataTipoSorologicaSarsCov2;
//        this.getPropertyChangeSupport().firePropertyChange ("dataTipoSorologicaSarsCov2", dataTipoSorologicaSarsCov2Old, dataTipoSorologicaSarsCov2);
	}



	/**
	 * Return the value associated with the column: resultado_lgG
	 */
	public java.lang.Long getResultadoLgG () {
		return getPropertyValue(this, resultadoLgG, PROP_RESULTADO_LG_G); 
	}

	/**
	 * Set the value related to the column: resultado_lgG
	 * @param resultadoLgG the resultado_lgG value
	 */
	public void setResultadoLgG (java.lang.Long resultadoLgG) {
//        java.lang.Long resultadoLgGOld = this.resultadoLgG;
		this.resultadoLgG = resultadoLgG;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoLgG", resultadoLgGOld, resultadoLgG);
	}



	/**
	 * Return the value associated with the column: resultado_lgM
	 */
	public java.lang.Long getResultadoLgM () {
		return getPropertyValue(this, resultadoLgM, PROP_RESULTADO_LG_M); 
	}

	/**
	 * Set the value related to the column: resultado_lgM
	 * @param resultadoLgM the resultado_lgM value
	 */
	public void setResultadoLgM (java.lang.Long resultadoLgM) {
//        java.lang.Long resultadoLgMOld = this.resultadoLgM;
		this.resultadoLgM = resultadoLgM;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoLgM", resultadoLgMOld, resultadoLgM);
	}



	/**
	 * Return the value associated with the column: resultado_lgA
	 */
	public java.lang.Long getResultadoLgA () {
		return getPropertyValue(this, resultadoLgA, PROP_RESULTADO_LG_A); 
	}

	/**
	 * Set the value related to the column: resultado_lgA
	 * @param resultadoLgA the resultado_lgA value
	 */
	public void setResultadoLgA (java.lang.Long resultadoLgA) {
//        java.lang.Long resultadoLgAOld = this.resultadoLgA;
		this.resultadoLgA = resultadoLgA;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoLgA", resultadoLgAOld, resultadoLgA);
	}



	/**
	 * Return the value associated with the column: classificacao_final_caso
	 */
	public java.lang.Long getClassificacaoFinalCaso () {
		return getPropertyValue(this, classificacaoFinalCaso, PROP_CLASSIFICACAO_FINAL_CASO); 
	}

	/**
	 * Set the value related to the column: classificacao_final_caso
	 * @param classificacaoFinalCaso the classificacao_final_caso value
	 */
	public void setClassificacaoFinalCaso (java.lang.Long classificacaoFinalCaso) {
//        java.lang.Long classificacaoFinalCasoOld = this.classificacaoFinalCaso;
		this.classificacaoFinalCaso = classificacaoFinalCaso;
//        this.getPropertyChangeSupport().firePropertyChange ("classificacaoFinalCaso", classificacaoFinalCasoOld, classificacaoFinalCaso);
	}



	/**
	 * Return the value associated with the column: outro_classificacao_final_caso
	 */
	public java.lang.String getOutroClassificacaoFinalCaso () {
		return getPropertyValue(this, outroClassificacaoFinalCaso, PROP_OUTRO_CLASSIFICACAO_FINAL_CASO); 
	}

	/**
	 * Set the value related to the column: outro_classificacao_final_caso
	 * @param outroClassificacaoFinalCaso the outro_classificacao_final_caso value
	 */
	public void setOutroClassificacaoFinalCaso (java.lang.String outroClassificacaoFinalCaso) {
//        java.lang.String outroClassificacaoFinalCasoOld = this.outroClassificacaoFinalCaso;
		this.outroClassificacaoFinalCaso = outroClassificacaoFinalCaso;
//        this.getPropertyChangeSupport().firePropertyChange ("outroClassificacaoFinalCaso", outroClassificacaoFinalCasoOld, outroClassificacaoFinalCaso);
	}



	/**
	 * Return the value associated with the column: criterio_encerramento
	 */
	public java.lang.Long getCriterioEncerramento () {
		return getPropertyValue(this, criterioEncerramento, PROP_CRITERIO_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: criterio_encerramento
	 * @param criterioEncerramento the criterio_encerramento value
	 */
	public void setCriterioEncerramento (java.lang.Long criterioEncerramento) {
//        java.lang.Long criterioEncerramentoOld = this.criterioEncerramento;
		this.criterioEncerramento = criterioEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("criterioEncerramento", criterioEncerramentoOld, criterioEncerramento);
	}



	/**
	 * Return the value associated with the column: evolucao_caso
	 */
	public java.lang.Long getEvolucaoCaso () {
		return getPropertyValue(this, evolucaoCaso, PROP_EVOLUCAO_CASO); 
	}

	/**
	 * Set the value related to the column: evolucao_caso
	 * @param evolucaoCaso the evolucao_caso value
	 */
	public void setEvolucaoCaso (java.lang.Long evolucaoCaso) {
//        java.lang.Long evolucaoCasoOld = this.evolucaoCaso;
		this.evolucaoCaso = evolucaoCaso;
//        this.getPropertyChangeSupport().firePropertyChange ("evolucaoCaso", evolucaoCasoOld, evolucaoCaso);
	}



	/**
	 * Return the value associated with the column: data_alta_obito
	 */
	public java.util.Date getDataAltaObito () {
		return getPropertyValue(this, dataAltaObito, PROP_DATA_ALTA_OBITO); 
	}

	/**
	 * Set the value related to the column: data_alta_obito
	 * @param dataAltaObito the data_alta_obito value
	 */
	public void setDataAltaObito (java.util.Date dataAltaObito) {
//        java.util.Date dataAltaObitoOld = this.dataAltaObito;
		this.dataAltaObito = dataAltaObito;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAltaObito", dataAltaObitoOld, dataAltaObito);
	}



	/**
	 * Return the value associated with the column: data_encerramento
	 */
	public java.util.Date getDataEncerramento () {
		return getPropertyValue(this, dataEncerramento, PROP_DATA_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: data_encerramento
	 * @param dataEncerramento the data_encerramento value
	 */
	public void setDataEncerramento (java.util.Date dataEncerramento) {
//        java.util.Date dataEncerramentoOld = this.dataEncerramento;
		this.dataEncerramento = dataEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEncerramento", dataEncerramentoOld, dataEncerramento);
	}



	/**
	 * Return the value associated with the column: numero_do
	 */
	public java.lang.String getNumeroDo () {
		return getPropertyValue(this, numeroDo, PROP_NUMERO_DO); 
	}

	/**
	 * Set the value related to the column: numero_do
	 * @param numeroDo the numero_do value
	 */
	public void setNumeroDo (java.lang.String numeroDo) {
//        java.lang.String numeroDoOld = this.numeroDo;
		this.numeroDo = numeroDo;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDo", numeroDoOld, numeroDo);
	}



	/**
	 * Return the value associated with the column: observacoes_adicionais
	 */
	public java.lang.String getObservacoesAdicionais () {
		return getPropertyValue(this, observacoesAdicionais, PROP_OBSERVACOES_ADICIONAIS); 
	}

	/**
	 * Set the value related to the column: observacoes_adicionais
	 * @param observacoesAdicionais the observacoes_adicionais value
	 */
	public void setObservacoesAdicionais (java.lang.String observacoesAdicionais) {
//        java.lang.String observacoesAdicionaisOld = this.observacoesAdicionais;
		this.observacoesAdicionais = observacoesAdicionais;
//        this.getPropertyChangeSupport().firePropertyChange ("observacoesAdicionais", observacoesAdicionaisOld, observacoesAdicionais);
	}



	/**
	 * Return the value associated with the column: membro_povo_comunidade_tradicional
	 */
	public java.lang.Long getMembroPovoComunidadeTradicionalDD () {
		return getPropertyValue(this, membroPovoComunidadeTradicionalDD, PROP_MEMBRO_POVO_COMUNIDADE_TRADICIONAL_D_D); 
	}

	/**
	 * Set the value related to the column: membro_povo_comunidade_tradicional
	 * @param membroPovoComunidadeTradicionalDD the membro_povo_comunidade_tradicional value
	 */
	public void setMembroPovoComunidadeTradicionalDD (java.lang.Long membroPovoComunidadeTradicionalDD) {
//        java.lang.Long membroPovoComunidadeTradicionalDDOld = this.membroPovoComunidadeTradicionalDD;
		this.membroPovoComunidadeTradicionalDD = membroPovoComunidadeTradicionalDD;
//        this.getPropertyChangeSupport().firePropertyChange ("membroPovoComunidadeTradicionalDD", membroPovoComunidadeTradicionalDDOld, membroPovoComunidadeTradicionalDD);
	}



	/**
	 * Return the value associated with the column: txt_povo_comunidade_tradicional
	 */
	public java.lang.String getMembroPovoComunidadeTradicionalTXT () {
		return getPropertyValue(this, membroPovoComunidadeTradicionalTXT, PROP_MEMBRO_POVO_COMUNIDADE_TRADICIONAL_T_X_T); 
	}

	/**
	 * Set the value related to the column: txt_povo_comunidade_tradicional
	 * @param membroPovoComunidadeTradicionalTXT the txt_povo_comunidade_tradicional value
	 */
	public void setMembroPovoComunidadeTradicionalTXT (java.lang.String membroPovoComunidadeTradicionalTXT) {
//        java.lang.String membroPovoComunidadeTradicionalTXTOld = this.membroPovoComunidadeTradicionalTXT;
		this.membroPovoComunidadeTradicionalTXT = membroPovoComunidadeTradicionalTXT;
//        this.getPropertyChangeSupport().firePropertyChange ("membroPovoComunidadeTradicionalTXT", membroPovoComunidadeTradicionalTXTOld, membroPovoComunidadeTradicionalTXT);
	}



	/**
	 * Return the value associated with the column: dt_data_3_dose
	 */
	public java.util.Date getData3dose () {
		return getPropertyValue(this, data3dose, PROP_DATA3DOSE); 
	}

	/**
	 * Set the value related to the column: dt_data_3_dose
	 * @param data3dose the dt_data_3_dose value
	 */
	public void setData3dose (java.util.Date data3dose) {
//        java.util.Date data3doseOld = this.data3dose;
		this.data3dose = data3dose;
//        this.getPropertyChangeSupport().firePropertyChange ("data3dose", data3doseOld, data3dose);
	}



	/**
	 * Return the value associated with the column: dt_data_4_dose
	 */
	public java.util.Date getData4dose () {
		return getPropertyValue(this, data4dose, PROP_DATA4DOSE); 
	}

	/**
	 * Set the value related to the column: dt_data_4_dose
	 * @param data4dose the dt_data_4_dose value
	 */
	public void setData4dose (java.util.Date data4dose) {
//        java.util.Date data4doseOld = this.data4dose;
		this.data4dose = data4dose;
//        this.getPropertyChangeSupport().firePropertyChange ("data4dose", data4doseOld, data4dose);
	}



	/**
	 * Return the value associated with the column: txt_fabricante_2_dose
	 */
	public java.lang.String getFabricante2doseTXT () {
		return getPropertyValue(this, fabricante2doseTXT, PROP_FABRICANTE2DOSE_T_X_T); 
	}

	/**
	 * Set the value related to the column: txt_fabricante_2_dose
	 * @param fabricante2doseTXT the txt_fabricante_2_dose value
	 */
	public void setFabricante2doseTXT (java.lang.String fabricante2doseTXT) {
//        java.lang.String fabricante2doseTXTOld = this.fabricante2doseTXT;
		this.fabricante2doseTXT = fabricante2doseTXT;
//        this.getPropertyChangeSupport().firePropertyChange ("fabricante2doseTXT", fabricante2doseTXTOld, fabricante2doseTXT);
	}



	/**
	 * Return the value associated with the column: txt_fabricante_3_dose
	 */
	public java.lang.String getFabricante3doseTXT () {
		return getPropertyValue(this, fabricante3doseTXT, PROP_FABRICANTE3DOSE_T_X_T); 
	}

	/**
	 * Set the value related to the column: txt_fabricante_3_dose
	 * @param fabricante3doseTXT the txt_fabricante_3_dose value
	 */
	public void setFabricante3doseTXT (java.lang.String fabricante3doseTXT) {
//        java.lang.String fabricante3doseTXTOld = this.fabricante3doseTXT;
		this.fabricante3doseTXT = fabricante3doseTXT;
//        this.getPropertyChangeSupport().firePropertyChange ("fabricante3doseTXT", fabricante3doseTXTOld, fabricante3doseTXT);
	}



	/**
	 * Return the value associated with the column: txt_fabricante_4_dose
	 */
	public java.lang.String getFabricante4doseTXT () {
		return getPropertyValue(this, fabricante4doseTXT, PROP_FABRICANTE4DOSE_T_X_T); 
	}

	/**
	 * Set the value related to the column: txt_fabricante_4_dose
	 * @param fabricante4doseTXT the txt_fabricante_4_dose value
	 */
	public void setFabricante4doseTXT (java.lang.String fabricante4doseTXT) {
//        java.lang.String fabricante4doseTXTOld = this.fabricante4doseTXT;
		this.fabricante4doseTXT = fabricante4doseTXT;
//        this.getPropertyChangeSupport().firePropertyChange ("fabricante4doseTXT", fabricante4doseTXTOld, fabricante4doseTXT);
	}



	/**
	 * Return the value associated with the column: txt_lote_3_dose
	 */
	public java.lang.String getLote3doseTXT () {
		return getPropertyValue(this, lote3doseTXT, PROP_LOTE3DOSE_T_X_T); 
	}

	/**
	 * Set the value related to the column: txt_lote_3_dose
	 * @param lote3doseTXT the txt_lote_3_dose value
	 */
	public void setLote3doseTXT (java.lang.String lote3doseTXT) {
//        java.lang.String lote3doseTXTOld = this.lote3doseTXT;
		this.lote3doseTXT = lote3doseTXT;
//        this.getPropertyChangeSupport().firePropertyChange ("lote3doseTXT", lote3doseTXTOld, lote3doseTXT);
	}



	/**
	 * Return the value associated with the column: txt_lote_4_dose
	 */
	public java.lang.String getLote4doseTXT () {
		return getPropertyValue(this, lote4doseTXT, PROP_LOTE4DOSE_T_X_T); 
	}

	/**
	 * Set the value related to the column: txt_lote_4_dose
	 * @param lote4doseTXT the txt_lote_4_dose value
	 */
	public void setLote4doseTXT (java.lang.String lote4doseTXT) {
//        java.lang.String lote4doseTXTOld = this.lote4doseTXT;
		this.lote4doseTXT = lote4doseTXT;
//        this.getPropertyChangeSupport().firePropertyChange ("lote4doseTXT", lote4doseTXTOld, lote4doseTXT);
	}



	/**
	 * Return the value associated with the column: dd_grupo_caso_nosocomial
	 */
	public java.lang.Long getDdGrupoCasoNosocomial () {
		return getPropertyValue(this, ddGrupoCasoNosocomial, PROP_DD_GRUPO_CASO_NOSOCOMIAL); 
	}

	/**
	 * Set the value related to the column: dd_grupo_caso_nosocomial
	 * @param ddGrupoCasoNosocomial the dd_grupo_caso_nosocomial value
	 */
	public void setDdGrupoCasoNosocomial (java.lang.Long ddGrupoCasoNosocomial) {
//        java.lang.Long ddGrupoCasoNosocomialOld = this.ddGrupoCasoNosocomial;
		this.ddGrupoCasoNosocomial = ddGrupoCasoNosocomial;
//        this.getPropertyChangeSupport().firePropertyChange ("ddGrupoCasoNosocomial", ddGrupoCasoNosocomialOld, ddGrupoCasoNosocomial);
	}



	/**
	 * Return the value associated with the column: dd_paciente_trabalha_contato_direto
	 */
	public java.lang.Long getDdPacienteTrabalhaContatoDireto () {
		return getPropertyValue(this, ddPacienteTrabalhaContatoDireto, PROP_DD_PACIENTE_TRABALHA_CONTATO_DIRETO); 
	}

	/**
	 * Set the value related to the column: dd_paciente_trabalha_contato_direto
	 * @param ddPacienteTrabalhaContatoDireto the dd_paciente_trabalha_contato_direto value
	 */
	public void setDdPacienteTrabalhaContatoDireto (java.lang.Long ddPacienteTrabalhaContatoDireto) {
//        java.lang.Long ddPacienteTrabalhaContatoDiretoOld = this.ddPacienteTrabalhaContatoDireto;
		this.ddPacienteTrabalhaContatoDireto = ddPacienteTrabalhaContatoDireto;
//        this.getPropertyChangeSupport().firePropertyChange ("ddPacienteTrabalhaContatoDireto", ddPacienteTrabalhaContatoDiretoOld, ddPacienteTrabalhaContatoDireto);
	}



	/**
	 * Return the value associated with the column: possui_fatores_risco_comorbidades
	 */
	public java.lang.Long getDdPossuiFatoresRiscoComorbidades () {
		return getPropertyValue(this, ddPossuiFatoresRiscoComorbidades, PROP_DD_POSSUI_FATORES_RISCO_COMORBIDADES); 
	}

	/**
	 * Set the value related to the column: possui_fatores_risco_comorbidades
	 * @param ddPossuiFatoresRiscoComorbidades the possui_fatores_risco_comorbidades value
	 */
	public void setDdPossuiFatoresRiscoComorbidades (java.lang.Long ddPossuiFatoresRiscoComorbidades) {
//        java.lang.Long ddPossuiFatoresRiscoComorbidadesOld = this.ddPossuiFatoresRiscoComorbidades;
		this.ddPossuiFatoresRiscoComorbidades = ddPossuiFatoresRiscoComorbidades;
//        this.getPropertyChangeSupport().firePropertyChange ("ddPossuiFatoresRiscoComorbidades", ddPossuiFatoresRiscoComorbidadesOld, ddPossuiFatoresRiscoComorbidades);
	}



	/**
	 * Return the value associated with the column: cd_registro_agravo
	 */
	public br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo getRegistroAgravo () {
		return getPropertyValue(this, registroAgravo, PROP_REGISTRO_AGRAVO); 
	}

	/**
	 * Set the value related to the column: cd_registro_agravo
	 * @param registroAgravo the cd_registro_agravo value
	 */
	public void setRegistroAgravo (br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo) {
//        br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravoOld = this.registroAgravo;
		this.registroAgravo = registroAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("registroAgravo", registroAgravoOld, registroAgravo);
	}



	/**
	 * Return the value associated with the column: cidade_empresa
	 */
	public br.com.ksisolucoes.vo.basico.Cidade getCidadeEmpresa () {
		return getPropertyValue(this, cidadeEmpresa, PROP_CIDADE_EMPRESA); 
	}

	/**
	 * Set the value related to the column: cidade_empresa
	 * @param cidadeEmpresa the cidade_empresa value
	 */
	public void setCidadeEmpresa (br.com.ksisolucoes.vo.basico.Cidade cidadeEmpresa) {
//        br.com.ksisolucoes.vo.basico.Cidade cidadeEmpresaOld = this.cidadeEmpresa;
		this.cidadeEmpresa = cidadeEmpresa;
//        this.getPropertyChangeSupport().firePropertyChange ("cidadeEmpresa", cidadeEmpresaOld, cidadeEmpresa);
	}



	/**
	 * Return the value associated with the column: unidade_saude_atendimento
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getUnidadeSaudeAtendimento () {
		return getPropertyValue(this, unidadeSaudeAtendimento, PROP_UNIDADE_SAUDE_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: unidade_saude_atendimento
	 * @param unidadeSaudeAtendimento the unidade_saude_atendimento value
	 */
	public void setUnidadeSaudeAtendimento (br.com.ksisolucoes.vo.basico.Empresa unidadeSaudeAtendimento) {
//        br.com.ksisolucoes.vo.basico.Empresa unidadeSaudeAtendimentoOld = this.unidadeSaudeAtendimento;
		this.unidadeSaudeAtendimento = unidadeSaudeAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("unidadeSaudeAtendimento", unidadeSaudeAtendimentoOld, unidadeSaudeAtendimento);
	}



	/**
	 * Return the value associated with the column: laboratorio_que_realizou_teste_antigenico
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getLaboratorioQueRealizouTesteAntigenico () {
		return getPropertyValue(this, laboratorioQueRealizouTesteAntigenico, PROP_LABORATORIO_QUE_REALIZOU_TESTE_ANTIGENICO); 
	}

	/**
	 * Set the value related to the column: laboratorio_que_realizou_teste_antigenico
	 * @param laboratorioQueRealizouTesteAntigenico the laboratorio_que_realizou_teste_antigenico value
	 */
	public void setLaboratorioQueRealizouTesteAntigenico (br.com.ksisolucoes.vo.basico.Empresa laboratorioQueRealizouTesteAntigenico) {
//        br.com.ksisolucoes.vo.basico.Empresa laboratorioQueRealizouTesteAntigenicoOld = this.laboratorioQueRealizouTesteAntigenico;
		this.laboratorioQueRealizouTesteAntigenico = laboratorioQueRealizouTesteAntigenico;
//        this.getPropertyChangeSupport().firePropertyChange ("laboratorioQueRealizouTesteAntigenico", laboratorioQueRealizouTesteAntigenicoOld, laboratorioQueRealizouTesteAntigenico);
	}



	/**
	 * Return the value associated with the column: laboratorio_que_realizou_biologia_molecular
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getLaboratorioQueRealizouBiologiaMolecular () {
		return getPropertyValue(this, laboratorioQueRealizouBiologiaMolecular, PROP_LABORATORIO_QUE_REALIZOU_BIOLOGIA_MOLECULAR); 
	}

	/**
	 * Set the value related to the column: laboratorio_que_realizou_biologia_molecular
	 * @param laboratorioQueRealizouBiologiaMolecular the laboratorio_que_realizou_biologia_molecular value
	 */
	public void setLaboratorioQueRealizouBiologiaMolecular (br.com.ksisolucoes.vo.basico.Empresa laboratorioQueRealizouBiologiaMolecular) {
//        br.com.ksisolucoes.vo.basico.Empresa laboratorioQueRealizouBiologiaMolecularOld = this.laboratorioQueRealizouBiologiaMolecular;
		this.laboratorioQueRealizouBiologiaMolecular = laboratorioQueRealizouBiologiaMolecular;
//        this.getPropertyChangeSupport().firePropertyChange ("laboratorioQueRealizouBiologiaMolecular", laboratorioQueRealizouBiologiaMolecularOld, laboratorioQueRealizouBiologiaMolecular);
	}



	/**
	 * Return the value associated with the column: cd_profissional_responsavel
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissionalResponsavel () {
		return getPropertyValue(this, profissionalResponsavel, PROP_PROFISSIONAL_RESPONSAVEL); 
	}

	/**
	 * Set the value related to the column: cd_profissional_responsavel
	 * @param profissionalResponsavel the cd_profissional_responsavel value
	 */
	public void setProfissionalResponsavel (br.com.ksisolucoes.vo.cadsus.Profissional profissionalResponsavel) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalResponsavelOld = this.profissionalResponsavel;
		this.profissionalResponsavel = profissionalResponsavel;
//        this.getPropertyChangeSupport().firePropertyChange ("profissionalResponsavel", profissionalResponsavelOld, profissionalResponsavel);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoSars)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoSars investigacaoAgravoSars = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoSars) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoSars.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoSars.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}