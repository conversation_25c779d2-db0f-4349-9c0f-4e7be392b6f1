<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.controle.web"  >
    <class 
		name="ControleProgramaWebGrupo"
		table="controle_programa_web_grupo"
	>
            
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_ctr_prg_web_grupo"
        /> <version column="version" name="version" type="long" />
        
        <many-to-one
                name="grupo"
                class="br.com.ksisolucoes.vo.controle.Grupo"
                not-null="true"
        >
                <column name="cd_grupo"/>
        </many-to-one>
        
        <many-to-one
                name="programaWeb"
                class="br.com.ksisolucoes.vo.controle.web.ProgramaWeb"
                not-null="true"
        >
                <column name="cd_prg_web"/>
        </many-to-one>

    </class>
</hibernate-mapping>