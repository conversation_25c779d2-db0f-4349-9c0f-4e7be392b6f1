package br.com.ksisolucoes.vo.geral.cnes;

import br.com.ksisolucoes.vo.geral.cnes.base.BaseCnesProcessoHabilitacao;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;



public class CnesProcessoHabilitacao extends BaseCnesProcessoHabilitacao implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public CnesProcessoHabilitacao () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public CnesProcessoHabilitacao (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public CnesProcessoHabilitacao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.geral.cnes.CnesProcessoEmpresa cnesProcessoEmpresa,
		java.lang.String codigoHabilitacao,
		java.lang.String descricaoHabilitacao,
		java.lang.String competenciaInicial,
		java.lang.String competenciaFinal,
		java.lang.Long quantidadeLeitos) {

		super (
			codigo,
			cnesProcessoEmpresa,
			codigoHabilitacao,
			descricaoHabilitacao,
			competenciaInicial,
			competenciaFinal,
			quantidadeLeitos);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}