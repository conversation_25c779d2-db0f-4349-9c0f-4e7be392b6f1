package br.com.ksisolucoes.vo.consorcio.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the licitacao_item_ocorrencia table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="licitacao_item_ocorrencia"
 */

public abstract class BaseLicitacaoItemOcorrencia extends BaseRootVO implements Serializable {

	public static String REF = "LicitacaoItemOcorrencia";
	public static final String PROP_LICITACAO_ITEM = "licitacaoItem";
	public static final String PROP_TIPO = "tipo";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_DESCRICAO_OCORRENCIA = "descricaoOcorrencia";


	// constructors
	public BaseLicitacaoItemOcorrencia () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseLicitacaoItemOcorrencia (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseLicitacaoItemOcorrencia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.consorcio.LicitacaoItem licitacaoItem) {

		this.setCodigo(codigo);
		this.setLicitacaoItem(licitacaoItem);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long tipo;
	private java.util.Date dataCadastro;
	private java.lang.String descricaoOcorrencia;

	// many to one
	private br.com.ksisolucoes.vo.consorcio.LicitacaoItem licitacaoItem;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_licitacao_item_ocorrencia"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: tipo
	 */
	public java.lang.Long getTipo () {
		return getPropertyValue(this, tipo, PROP_TIPO); 
	}

	/**
	 * Set the value related to the column: tipo
	 * @param tipo the tipo value
	 */
	public void setTipo (java.lang.Long tipo) {
//        java.lang.Long tipoOld = this.tipo;
		this.tipo = tipo;
//        this.getPropertyChangeSupport().firePropertyChange ("tipo", tipoOld, tipo);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: ds_ocorrencia
	 */
	public java.lang.String getDescricaoOcorrencia () {
		return getPropertyValue(this, descricaoOcorrencia, PROP_DESCRICAO_OCORRENCIA); 
	}

	/**
	 * Set the value related to the column: ds_ocorrencia
	 * @param descricaoOcorrencia the ds_ocorrencia value
	 */
	public void setDescricaoOcorrencia (java.lang.String descricaoOcorrencia) {
//        java.lang.String descricaoOcorrenciaOld = this.descricaoOcorrencia;
		this.descricaoOcorrencia = descricaoOcorrencia;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoOcorrencia", descricaoOcorrenciaOld, descricaoOcorrencia);
	}



	/**
	 * Return the value associated with the column: cd_lic_item
	 */
	public br.com.ksisolucoes.vo.consorcio.LicitacaoItem getLicitacaoItem () {
		return getPropertyValue(this, licitacaoItem, PROP_LICITACAO_ITEM); 
	}

	/**
	 * Set the value related to the column: cd_lic_item
	 * @param licitacaoItem the cd_lic_item value
	 */
	public void setLicitacaoItem (br.com.ksisolucoes.vo.consorcio.LicitacaoItem licitacaoItem) {
//        br.com.ksisolucoes.vo.consorcio.LicitacaoItem licitacaoItemOld = this.licitacaoItem;
		this.licitacaoItem = licitacaoItem;
//        this.getPropertyChangeSupport().firePropertyChange ("licitacaoItem", licitacaoItemOld, licitacaoItem);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.consorcio.LicitacaoItemOcorrencia)) return false;
		else {
			br.com.ksisolucoes.vo.consorcio.LicitacaoItemOcorrencia licitacaoItemOcorrencia = (br.com.ksisolucoes.vo.consorcio.LicitacaoItemOcorrencia) obj;
			if (null == this.getCodigo() || null == licitacaoItemOcorrencia.getCodigo()) return false;
			else return (this.getCodigo().equals(licitacaoItemOcorrencia.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}