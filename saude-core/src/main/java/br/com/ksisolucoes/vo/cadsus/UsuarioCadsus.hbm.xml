<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.cadsus">
    <class name="UsuarioCadsus" table="usuario_cadsus">
        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_usu_cadsus"
        >
            <generator class="sequence">
                <param name="sequence">seq_usuario_cadsus</param>
            </generator>
        </id>
        <version column="version" name="version" type="long"/>

        <property
                column="nm_usuario"
                length="70"
                name="nome"
                not-null="true"
                type="java.lang.String"
        />

        <property
                column="sg_sexo"
                length="1"
                name="sexo"
                not-null="true"
                type="java.lang.String"
        />

        <property
                column="nm_mae"
                length="70"
                name="nomeMae"
                not-null="true"
                type="java.lang.String"
        />

        <property
                column="nm_pai"
                length="70"
                name="nomePai"
                not-null="false"
                type="java.lang.String"
        />

        <property
                column="email"
                length="100"
                name="email"
                not-null="false"
                type="java.lang.String"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Cidade"
                column="cod_cid_nascimento"
                name="cidadeNascimento"
                not-null="false"
        />

        <many-to-one
                name="roDominioUsuarioCadsus"
                class="br.com.ksisolucoes.vo.cadsus.DominioUsuarioCadsus"
                column="cd_usu_cadsus"
                not-null="false"
                property-ref="usuarioCadsus"
                insert="false"
                update="false"
        />

        <property
                column="cpf"
                length="14"
                name="cpf"
                not-null="false"
                type="java.lang.String"
        />

        <property
                column="rg"
                length="20"
                name="rg"
                not-null="false"
                type="java.lang.String"
        />

        <property
                column="dt_nascimento"
                length="4"
                name="dataNascimento"
                not-null="false"
                type="date"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Pais"
                column="cd_pais_nascimento"
                name="paisNascimento"
                not-null="false"
        />

        <property
                column="st_profissional"
                name="profissional"
                not-null="false"
                type="java.lang.Long"
        />

        <property
                column="st_frequenta_escola"
                length="1"
                name="frequentaEscola"
                not-null="false"
                type="java.lang.String"
        />

        <many-to-one
                class="Raca"
                column="cd_raca"
                name="raca"
                not-null="false"
        />

        <many-to-one
                class="EtniaIndigena"
                column="cd_etnia"
                name="etniaIndigena"
                not-null="false"
        />

        <many-to-one
                class="EstadoCivil"
                column="cd_estado_civil"
                name="estadoCivil"
                not-null="false"
        />

        <property
                column="cd_situacao_familiar"
                name="situacaoFamiliar"
                not-null="false"
                type="java.lang.Long"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"
                column="cd_cbo"
                name="tabelaCbo"
                not-null="false"
        />

        <property
                column="nr_telefone"
                name="telefone"
                not-null="false"
                length="15"
                type="java.lang.String"
        />

        <property
                column="nr_telefone_2"
                name="telefone2"
                not-null="false"
                length="15"
                type="java.lang.String"
        />

        <property
                column="dt_inclusao"
                name="dataInclusao"
                not-null="false"
                type="date"
        />

        <property
                column="dt_preenchimento_form"
                name="dataPreenchimentoForm"
                not-null="false"
                type="date"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Cidade"
                column="cd_municipio_residencia"
                name="municipioResidencia"
                not-null="false"
        />

        <property
                column="st_sem_documento"
                name="semDocumento"
                not-null="false"
                type="java.lang.Long"
        />

        <property
                column="nr_usuario_no_domicilio"
                name="numeroUsuarioDomicilio"
                not-null="false"
                type="java.lang.Long"
        />

        <property
                column="st_vivo"
                name="vivo"
                not-null="false"
                type="java.lang.Long"
        />

        <property
                column="cd_usuario_interno"
                name="usuarioInterno"
                not-null="false"
                length="60"
                type="java.lang.String"
        />

        <property
                column="st_excluido"
                name="excluido"
                not-null="false"
                type="java.lang.Long"
        />

        <property
                column="cd_domicilio_interno"
                name="codigoDomicilioInterno"
                not-null="false"
                length="50"
                type="java.lang.String"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio"
                column="cd_domicilio"
                name="enderecoDomicilio"
                not-null="false"
        />


        <many-to-one
                name="escolaridade"
                column="cd_escolaridade"
                class="br.com.ksisolucoes.vo.cadsus.Escolaridade"
        />

        <property
                column="dt_alteracao"
                name="dataAlteracao"
                not-null="false"
                type="timestamp"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Empresa"
                column="empresa_responsavel"
                name="empresaResponsavel"
                not-null="false"
        />

        <property
                column="dt_inativacao"
                name="dataInativacao"
                not-null="false"
                type="timestamp"
        />

        <property
                column="dt_alteracao_app"
                name="dataAlteracaoApp"
                not-null="false"
                type="timestamp"
        />

        <property
                name="situacao"
                column="situacao"
                type="java.lang.Long"
        />

        <property
                name="dataFixacao"
                column="dt_fixacao"
                type="java.util.Date"
        />

        <property
                name="dataCadastro"
                column="dt_cadastro"
                type="timestamp"
        />

        <property
                name="situacaoAprovacao"
                column="st_aprovacao"
                type="java.lang.Long"
        />

        <property
                name="dataAprovacao"
                column="dt_aprovacao"
                type="timestamp"
        />

        <property
                name="flagDocumento"
                column="flag_documento"
                type="java.lang.String"
        />

        <property
                column="telefone3"
                name="telefone3"
                type="java.lang.String"
                length="15"
        />

        <property
                column="telefone4"
                name="telefone4"
                type="java.lang.String"
                length="15"
        />

        <property
                column="celular"
                name="celular"
                length="15"
                type="java.lang.String"
        />

        <property
                column="externo"
                name="flagExterno"
                type="java.lang.String"
                length="1"
        />

        <property
                column="dt_usuario"
                name="dataUsuario"
                type="java.util.Date"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario"
                name="usuario"
                not-null="true"
        />

        <property
                column="observacao"
                name="observacao"
                type="java.lang.String"
        />

        <property
                column="religiao"
                name="religiao"
                type="java.lang.String"
        />

        <property
                column="local_trabalho"
                name="localTrabalho"
                type="java.lang.String"
        />

        <property
                column="telefone_trabalho"
                name="telefoneTrabalho"
                type="java.lang.String"
        />

        <property
                column="responsavel"
                name="responsavel"
                type="java.lang.String"
        />

        <property
                column="parentesco_responsavel"
                name="parentescoResponsavel"
                type="java.lang.String"
        />

        <property
                column="urgencia_chamar"
                name="urgenciaChamar"
                type="java.lang.String"
        />

        <property
                column="telefone_urgencia"
                name="telefoneUrgencia"
                type="java.lang.String"
        />

        <property
                column="grau_parentesco_urgencia"
                name="grauParentescoUrgencia"
                type="java.lang.String"
        />

        <property
                column="recem_nascido"
                name="recemNascido"
                type="java.lang.String"
                length="1"
        />

        <property
                column="nome_conjuge"
                name="nomeConjuge"
                type="java.lang.String"
        />

        <property
                name="flagSimplificado"
                column="flag_simplificado"
                type="java.lang.Long"
        />

        <property
                name="flagEstrangeiro"
                column="flag_estrangeiro"
                type="java.lang.Long"
        />

        <property
                name="flagNaoPossuiCns"
                column="flag_nao_possui_cns"
                type="java.lang.Long"
        />

        <property
                column="flag_utiliza_nome_social"
                name="utilizaNomeSocial"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                name="chaveBiometria"
                column="chave_biometria"
                type="java.lang.String"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.cadsus.LocalPermanencia"
                column="cd_local_permanencia"
                name="localPermanencia"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
                name="atendimentoOrigem"
        >
            <column name="nr_atendimento_origem"/>
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus"
                column="cd_endereco"
                name="enderecoUsuarioCadsus"
                not-null="false"
        />

        <property
                column="version_all"
                name="versionAll"
                type="java.lang.Long"
        />

        <property
                column="apelido"
                name="apelido"
                type="java.lang.String"
        />

        <property
                column="flag_responsavel_familiar"
                name="flagResponsavelFamiliar"
                type="java.lang.Long"
        />

        <many-to-one
                class="UsuarioCadsus"
                column="cd_usu_cadsus_responsavel"
                name="responsavelFamiliar"
        />

        <property
                column="nacionalidade"
                name="nacionalidade"
                type="java.lang.Long"
        />

        <property
                column="renda_familiar"
                name="rendaFamiliar"
                type="java.lang.Long"
        />

        <property
                column="grupo_vacinacao"
                name="grupoVacinacao"
                type="java.lang.Long"
        />

        <property
                column="reside_desde"
                name="resideDesde"
                type="java.util.Date"
                not-null="false"
        />

        <property
                column="nis"
                name="nis"
                length="20"
                type="java.lang.String"
        />

        <property
                column="flag_recem_nascido_recepcao"
                name="flagRecemNascidoRecepcao"
                type="java.lang.String"
                length="1"
        />

        <property
                column="mot_exclusao"
                name="motivoExclusao"
                not-null="false"
                type="java.lang.Long"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo"
                column="cd_gerenciador_arquivo"
                name="foto"
                not-null="false"
        />

        <property
                column="tipo_sanguineo"
                name="tipoSanguineo"
                type="java.lang.Long"
        />

        <property
                name="profissao"
                column="profissao"
                type="java.lang.String"
                length="50"
                not-null="false"
        />

        <!-- ATUALIZADO VIA TRIGGER PELA TABELA USUARIO_CADSUS_DOMICILIO -->
        <property
                column="prontuario"
                name="prontuario"
                not-null="false"
                type="java.lang.String"
                insert="false"
                update="false"
        />

        <one-to-one
                name="usuarioCadsusEsus"
                class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEsus"
                property-ref="usuarioCadsus"
        />

        <property
                name="referencia"
                column="referencia"
                type="java.lang.String"
                length="10"
        />

        <property
                column="uuid_tablet"
                name="uuidTablet"
                type="java.lang.String"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.EstabelecimentoCerest"
                column="cd_estabelecimento_cerest"
                name="estabelecimentoCerest"
                not-null="false"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario_cad"
                not-null="true"
                name="usuarioCadastro"
        />
        
        <many-to-one
            	class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"
            	column="cd_usu_cadsus_unificado"
            	name="usuarioCadsusUnificado"
                not-null="false"
        />

        <property
                column="flag_unificado"
                name="flagUnificado"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="responsavel_anterior"
                name="responsavelAnterior"
                not-null="false"
                type="java.lang.Long"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Equipe"
                column="cd_equipe"
                name="equipe"
        />

        <property
                column="flag_outras_pop_nomades"
                name="flagOutrasPopulacoesNomades"
                type="java.lang.Long"
        />

<!--        sincronizar com campo nivel_escolaridade do UsuarioCadsusEsus-->
        <property
                column="nivel_escolaridade"
                name="nivelEscolaridade"
                type="java.lang.Long"
        />
        <property
                name="beneficiarioBolsaFamilia"
                column="beneficiario_bolsa_familia"
                type="java.lang.Long"
        />

        <property
                name="appCidadaoAtivo"
                column="app_cidadao_ativo"
                type="boolean"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.EquipeProfissional"
                column="cd_equipe_profissional"
                name="equipeProfissional"
                not-null="false"
        />

        <property
                name="flagNaoPossuiCpf"
                column="flag_nao_possui_cpf"
                type="java.lang.Long"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsusMotivoCPF"
                column="cd_motivo_cpf"
                name="usuarioCadsusMotivoCPF"
                not-null="false"
        />

        <property
                name="flagVisivelProntuario"
                column="flag_visivel_prontuario"
                type="java.lang.Long"
        />
    </class>
</hibernate-mapping>
