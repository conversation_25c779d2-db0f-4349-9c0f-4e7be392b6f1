package br.com.ksisolucoes.vo.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the produto_solicitado_movimento_lote table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="produto_solicitado_movimento_lote"
 */

public abstract class BaseProdutoSolicitadoMovimentoLote extends BaseRootVO implements Serializable {

	public static String REF = "ProdutoSolicitadoMovimentoLote";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_GRUPO_ESTOQUE = "grupoEstoque";
	public static final String PROP_PRODUTO_SOLICITADO_MOVIMENTO = "produtoSolicitadoMovimento";
	public static final String PROP_QUANTIDADE = "quantidade";


	// constructors
	public BaseProdutoSolicitadoMovimentoLote () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseProdutoSolicitadoMovimentoLote (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseProdutoSolicitadoMovimentoLote (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.ProdutoSolicitadoMovimento produtoSolicitadoMovimento,
		br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque grupoEstoque,
		java.lang.Double quantidade) {

		this.setCodigo(codigo);
		this.setProdutoSolicitadoMovimento(produtoSolicitadoMovimento);
		this.setGrupoEstoque(grupoEstoque);
		this.setQuantidade(quantidade);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Double quantidade;

	// many to one
	private br.com.ksisolucoes.vo.basico.ProdutoSolicitadoMovimento produtoSolicitadoMovimento;
	private br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque grupoEstoque;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_produto_solicitado_movimento_lote"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: quantidade
	 */
	public java.lang.Double getQuantidade () {
		return getPropertyValue(this, quantidade, PROP_QUANTIDADE); 
	}

	/**
	 * Set the value related to the column: quantidade
	 * @param quantidade the quantidade value
	 */
	public void setQuantidade (java.lang.Double quantidade) {
//        java.lang.Double quantidadeOld = this.quantidade;
		this.quantidade = quantidade;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidade", quantidadeOld, quantidade);
	}



	/**
	 * Return the value associated with the column: cd_prod_solic_mov
	 */
	public br.com.ksisolucoes.vo.basico.ProdutoSolicitadoMovimento getProdutoSolicitadoMovimento () {
		return getPropertyValue(this, produtoSolicitadoMovimento, PROP_PRODUTO_SOLICITADO_MOVIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_prod_solic_mov
	 * @param produtoSolicitadoMovimento the cd_prod_solic_mov value
	 */
	public void setProdutoSolicitadoMovimento (br.com.ksisolucoes.vo.basico.ProdutoSolicitadoMovimento produtoSolicitadoMovimento) {
//        br.com.ksisolucoes.vo.basico.ProdutoSolicitadoMovimento produtoSolicitadoMovimentoOld = this.produtoSolicitadoMovimento;
		this.produtoSolicitadoMovimento = produtoSolicitadoMovimento;
//        this.getPropertyChangeSupport().firePropertyChange ("produtoSolicitadoMovimento", produtoSolicitadoMovimentoOld, produtoSolicitadoMovimento);
	}



	/**
	 * Return the value associated with the column: cd_localizacao_estrutura
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque getGrupoEstoque () {
		return getPropertyValue(this, grupoEstoque, PROP_GRUPO_ESTOQUE); 
	}

	/**
	 * Set the value related to the column: cd_localizacao_estrutura
	 * @param grupoEstoque the cd_localizacao_estrutura value
	 */
	public void setGrupoEstoque (br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque grupoEstoque) {
//        br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque grupoEstoqueOld = this.grupoEstoque;
		this.grupoEstoque = grupoEstoque;
//        this.getPropertyChangeSupport().firePropertyChange ("grupoEstoque", grupoEstoqueOld, grupoEstoque);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.ProdutoSolicitadoMovimentoLote)) return false;
		else {
			br.com.ksisolucoes.vo.basico.ProdutoSolicitadoMovimentoLote produtoSolicitadoMovimentoLote = (br.com.ksisolucoes.vo.basico.ProdutoSolicitadoMovimentoLote) obj;
			if (null == this.getCodigo() || null == produtoSolicitadoMovimentoLote.getCodigo()) return false;
			else return (this.getCodigo().equals(produtoSolicitadoMovimentoLote.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}