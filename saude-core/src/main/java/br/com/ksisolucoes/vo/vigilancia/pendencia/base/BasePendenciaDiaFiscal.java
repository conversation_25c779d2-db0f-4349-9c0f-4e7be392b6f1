package br.com.ksisolucoes.vo.vigilancia.pendencia.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the pendencia_dia_fiscal table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="pendencia_dia_fiscal"
 */

public abstract class BasePendenciaDiaFiscal extends BaseRootVO implements Serializable {

	public static String REF = "PendenciaDiaFiscal";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_PENDENCIA_DIA = "PendenciaDia";
	public static final String PROP_PROFISSIONAL = "profissional";


	// constructors
	public BasePendenciaDiaFiscal () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BasePendenciaDiaFiscal (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BasePendenciaDiaFiscal (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.Profissional profissional,
		br.com.ksisolucoes.vo.vigilancia.pendencia.PendenciaDia pendenciaDia) {

		this.setCodigo(codigo);
		this.setProfissional(profissional);
		this.setPendenciaDia(pendenciaDia);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.cadsus.Profissional profissional;
	private br.com.ksisolucoes.vo.vigilancia.pendencia.PendenciaDia pendenciaDia;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_pendencia_dia_fiscal"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissional () {
		return getPropertyValue(this, profissional, PROP_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissional the cd_profissional value
	 */
	public void setProfissional (br.com.ksisolucoes.vo.cadsus.Profissional profissional) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalOld = this.profissional;
		this.profissional = profissional;
//        this.getPropertyChangeSupport().firePropertyChange ("profissional", profissionalOld, profissional);
	}



	/**
	 * Return the value associated with the column: cd_pendencia_dia
	 */
	public br.com.ksisolucoes.vo.vigilancia.pendencia.PendenciaDia getPendenciaDia () {
		return getPropertyValue(this, pendenciaDia, PROP_PENDENCIA_DIA); 
	}

	/**
	 * Set the value related to the column: cd_pendencia_dia
	 * @param pendenciaDia the cd_pendencia_dia value
	 */
	public void setPendenciaDia (br.com.ksisolucoes.vo.vigilancia.pendencia.PendenciaDia pendenciaDia) {
//        br.com.ksisolucoes.vo.vigilancia.pendencia.PendenciaDia pendenciaDiaOld = this.pendenciaDia;
		this.pendenciaDia = pendenciaDia;
//        this.getPropertyChangeSupport().firePropertyChange ("pendenciaDia", pendenciaDiaOld, pendenciaDia);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.pendencia.PendenciaDiaFiscal)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.pendencia.PendenciaDiaFiscal pendenciaDiaFiscal = (br.com.ksisolucoes.vo.vigilancia.pendencia.PendenciaDiaFiscal) obj;
			if (null == this.getCodigo() || null == pendenciaDiaFiscal.getCodigo()) return false;
			else return (this.getCodigo().equals(pendenciaDiaFiscal.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}