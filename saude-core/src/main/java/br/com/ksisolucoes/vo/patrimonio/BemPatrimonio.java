package br.com.ksisolucoes.vo.patrimonio;

import java.io.Serializable;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.patrimonio.base.BaseBemPatrimonio;

public class BemPatrimonio extends BaseBemPatrimonio implements CodigoManager {

    private static final long serialVersionUID = 1L;

    private static final String PROP_DESCRICAO_STATUS = "descricaoStatus";

    public enum Status implements IEnum {

        PROP_STATUS_ATIVO(0L, Bundle.getStringApplication("rotulo_status_bem_ativo")),
        PROP_STATUS_CANCELADO(1L, Bundle.getStringApplication("rotulo_status_bem_cancelado")),
        PROP_STATUS_BAIXADO(2L, Bundle.getStringApplication("rotulo_status_bem_baixado"));

        private Long value;
        private String descricao;

        private Status(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Status valueOf(Long value) {
            for (Status status : Status.values()) {
                if (status.value().equals(value)) {
                    return status;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    public String getDescricaoStatus() {
        Status status = Status.valueOf(getStatus());
        if (status != null && status.descricao != null) {
            return status.descricao();
        }
        return "";
    }

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public BemPatrimonio() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public BemPatrimonio(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public BemPatrimonio(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.basico.Empresa empresa,
            br.com.ksisolucoes.vo.patrimonio.BemGrupo bemGrupo,
            java.lang.String descricao,
            java.lang.Long referencia,
            java.lang.Long status,
            java.util.Date dataCadastro) {

        super(
                codigo,
                empresa,
                bemGrupo,
                descricao,
                referencia,
                status,
                dataCadastro);
    }

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

}
