<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.hospital"  >
    <class name="ContaPaciente" table="conta_paciente" >
        <id
            column="cd_conta_paciente"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />    
            
        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao"
                     name="atendimentoInformacao" not-null="false">
            <column name="cd_atend_inf" />
        </many-to-one>
        
        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"
            column="cd_usu_cadsus"
            name="usuarioCadsus"
            not-null="false"
        />
        
        <property
            column="status"
            name="status"
            not-null="true"
            type="java.lang.Long"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usu_fechamento"
            name="usuarioFechamento"
            not-null="false"
        />
        
        <property
            name="dataFechamento"
            column="dt_fechamento"
            not-null="false"
            type="timestamp"
        />
        
        <property
            column="numero_guia_atendimento"
            name="numeroGuiaAtendimento"
            not-null="false"
            type="java.lang.String"
            length="6"
        />
        
        <property
            column="conta_exportada"
            name="contaExportada"
            not-null="true"
            type="java.lang.Long"
        />
        
        <property
            column="competencia_atendimento"
            name="competenciaAtendimento"
            not-null="true"
            type="java.util.Date"
        />
        
        <property
            name="dataAbertura"
            column="data_abertura"
            not-null="false"
            type="timestamp"
        />
        
        <property
            name="sequencia"
            column="sequencia"
            not-null="false"
            type="java.lang.Long"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.Convenio"
            column="cd_convenio"
            name="convenio"
            not-null="false"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente"
            column="cd_conta_principal"
            name="contaPacientePrincipal"
            not-null="true"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAlta"
            column="cd_atendimento_alta"
            name="atendimentoAlta"
            not-null="false"
        />
                
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.Cid"
            column="cd_cid"
            name="cid"
            not-null="false"
        />
        
        <property
            name="liberacaoCriticaCns"
            column="lib_critica_cns"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            name="liberacaoCriticaIdadeMaior"
            column="lib_critica_idade_maior"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            name="liberacaoCriticaIdadeMenor"
            column="lib_critica_idade_menor"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            name="liberacaoCriticaQuantidadeMaxima"
            column="lib_critica_qtd_max"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            name="liberacaoCriticaTelefone"
            column="lib_critica_telefone"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            name="liberacaoCriticaTempoPermanencia"
            column="lib_critica_tmp_perman"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            name="liberacaoCriticaJustificativaCns"
            column="lib_critica_just_cns"
            not-null="false"
            type="java.lang.String"
        />
        
        <property
            column="declaracao_nascido"
            name="declaracaoNascido"
            not-null="false"
            type="java.lang.String"
            length="11"
        />
        
        <property
            column="declaracao_obito"
            name="declaracaoObito"
            not-null="false"
            type="java.lang.String"
            length="11"
        />
        
        <property
            name="flagDeclaracaoObitoRecemNascido"
            column="flag_dec_obito_rn"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            column="flag_atend_cancelado"
            name="flagAtendimentoCancelado"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            column="flag_fechada_automatico"
            name="flagFechadaAutomatico"
            not-null="true"
            type="java.lang.Long"
        />
        
        <property
            column="flag_origem"
            name="flagOrigem"
            not-null="true"
            type="java.lang.Long"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo"
            column="cd_atv_grupo"
            name="atividadeGrupo"
            not-null="false"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.VisitaDomiciliar"
            column="cd_visita"
            name="visitaDomiciliar"
            not-null="false"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.samu.AtendimentoSamu"
            column="cd_atendimento_samu"
            name="atendimentoSamu"
            not-null="false"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento"
            column="nr_dispensacao"
            name="dispensacaoMedicamento"
            not-null="false"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus"
            column="cd_endereco"
            name="enderecoUsuarioCadsus"
            not-null="false"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.frota.RoteiroViagemPassageiro"
            column="cd_roteiro_paciente"
            name="roteiroViagemPassageiro"
            not-null="false"
        />
        
        <property
            column="documento"
            name="documento"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            name="dataGeracao"
            column="dt_geracao"
            not-null="false"
            type="timestamp"
        />

        <property
            name="dataFinal"
            column="dt_final"
            not-null="false"
            type="timestamp"
        />
        
        <many-to-one  
            class="br.com.ksisolucoes.vo.basico.Empresa"
            name="empresa"
            column="empresa"
            not-null="false"
        />
        
        <many-to-one  
            class="br.com.ksisolucoes.vo.cadsus.Profissional"
            name="profissional"
            column="cd_profissional"
            not-null="false"
        />
        
        <property
            column="tp_conta"
            name="tipoConta"
            not-null="false"
            type="java.lang.Long"
        />
        
        <many-to-one  
            class="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"
            name="tipoAtendimento"
            column="cd_tp_atendimento"
            not-null="false"
        />

        <!--<many-to-one-->
            <!--class="br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilancia"-->
            <!--column="cd_lancamento_atividades_vigilancia"-->
            <!--name="lancamentoAtividadesVigilancia"-->
            <!--not-null="false"-->
        <!--/>-->

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.Exame"
            column="cd_exame"
            name="exame"
            not-null="false"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento"
            column="cd_guia"
            name="consorcioGuiaProcedimento"
            not-null="false"
        />

    </class>
</hibernate-mapping>
