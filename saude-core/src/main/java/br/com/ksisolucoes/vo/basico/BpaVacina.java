package br.com.ksisolucoes.vo.basico;

import java.io.Serializable;

import br.com.ksisolucoes.vo.basico.base.BaseBpaVacina;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class BpaVacina extends BaseBpaVacina implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public BpaVacina () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public BpaVacina (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public BpaVacina (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.atendimento.Bpa bpa,
		br.com.ksisolucoes.vo.vacina.VacinaAplicacao vacinaAplicacao) {

		super (
			codigo,
			bpa,
			vacinaAplicacao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}