package br.com.ksisolucoes.vo.vigilancia.requerimentos;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoPedidoDocumento;

import java.io.Serializable;



public class RequerimentoPedidoDocumento extends BaseRequerimentoPedidoDocumento implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequerimentoPedidoDocumento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequerimentoPedidoDocumento (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequerimentoPedidoDocumento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia) {

		super (
			codigo,
			requerimentoVigilancia);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}