package br.com.ksisolucoes.vo.basico;

import br.com.ksisolucoes.vo.basico.base.BaseParametroGemPK;

public class ParametroGemPK extends BaseParametroGemPK {
	private static final long serialVersionUID = 1L;
	public static final String ID_ZERO = "0";

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ParametroGemPK () {}
	
	public ParametroGemPK (
		br.com.ksisolucoes.vo.controle.Modulo modulo,
		java.lang.String parametro,
		java.lang.String nivel,
		java.lang.String identificador) {

		super (
			modulo,
			parametro,
			nivel,
			identificador);
	}
/*[CONSTRUCTOR MARKER END]*/


}