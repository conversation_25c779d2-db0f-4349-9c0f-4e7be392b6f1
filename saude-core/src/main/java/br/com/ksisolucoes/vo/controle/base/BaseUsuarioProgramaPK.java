package br.com.ksisolucoes.vo.controle.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;


public abstract class BaseUsuarioProgramaPK extends BaseRootVO implements Serializable {

	protected int hashCode = Integer.MIN_VALUE;

	public static String PROP_USUARIO = "usuario";
	public static String PROP_PROGRAMA = "programa";

	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.controle.Programa programa;


	public BaseUsuarioProgramaPK () {}
	
	public BaseUsuarioProgramaPK (
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.controle.Programa programa) {

		this.setUsuario(usuario);
		this.setPrograma(programa);
	}


	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_programa
	 */
	public br.com.ksisolucoes.vo.controle.Programa getPrograma () {
		return getPropertyValue(this, programa, PROP_PROGRAMA); 
	}

	/**
	 * Set the value related to the column: cd_programa
	 * @param programa the cd_programa value
	 */
	public void setPrograma (br.com.ksisolucoes.vo.controle.Programa programa) {
//        br.com.ksisolucoes.vo.controle.Programa programaOld = this.programa;
		this.programa = programa;
//        this.getPropertyChangeSupport().firePropertyChange ("programa", programaOld, programa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.controle.UsuarioProgramaPK)) return false;
		else {
			br.com.ksisolucoes.vo.controle.UsuarioProgramaPK mObj = (br.com.ksisolucoes.vo.controle.UsuarioProgramaPK) obj;
			if (null != this.getUsuario() && null != mObj.getUsuario()) {
				if (!this.getUsuario().equals(mObj.getUsuario())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getPrograma() && null != mObj.getPrograma()) {
				if (!this.getPrograma().equals(mObj.getPrograma())) {
					return false;
				}
			}
			else {
				return false;
			}
			return true;
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			StringBuilder sb = new StringBuilder();
			if (null != this.getUsuario()) {
				sb.append(this.getUsuario().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getPrograma()) {
				sb.append(this.getPrograma().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			this.hashCode = sb.toString().hashCode();
		}
		return this.hashCode;
	}

    private java.beans.PropertyChangeSupport propertyChangeSupport;

    protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
        if( this.propertyChangeSupport == null ) {
            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
        }
        return this.propertyChangeSupport;
    }

    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.addPropertyChangeListener(l);
    }

    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
		propertyChangeSupport.addPropertyChangeListener(propertyName, listener);
    }

    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.removePropertyChangeListener(l);
    }
}