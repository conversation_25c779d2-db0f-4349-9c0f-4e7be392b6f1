package br.com.ksisolucoes.vo.entradas.recebimento.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the registro_it_nf_licitacao table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="registro_it_nf_licitacao"
 */

public abstract class BaseRegistroItemNotaFiscalLicitacao extends BaseRootVO implements Serializable {

	public static String REF = "RegistroItemNotaFiscalLicitacao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_LOTE = "lote";
	public static final String PROP_REGISTRO_ITEM_NOTA_FISCAL = "registroItemNotaFiscal";
	public static final String PROP_LICITACAO_ITEM = "licitacaoItem";


	// constructors
	public BaseRegistroItemNotaFiscalLicitacao () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRegistroItemNotaFiscalLicitacao (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRegistroItemNotaFiscalLicitacao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.consorcio.LicitacaoItem licitacaoItem,
		br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal registroItemNotaFiscal,
		java.lang.String lote) {

		this.setCodigo(codigo);
		this.setLicitacaoItem(licitacaoItem);
		this.setRegistroItemNotaFiscal(registroItemNotaFiscal);
		this.setLote(lote);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String lote;

	// many to one
	private br.com.ksisolucoes.vo.consorcio.LicitacaoItem licitacaoItem;
	private br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal registroItemNotaFiscal;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_reg_it_nf_lic"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: lote
	 */
	public java.lang.String getLote () {
		return getPropertyValue(this, lote, PROP_LOTE); 
	}

	/**
	 * Set the value related to the column: lote
	 * @param lote the lote value
	 */
	public void setLote (java.lang.String lote) {
//        java.lang.String loteOld = this.lote;
		this.lote = lote;
//        this.getPropertyChangeSupport().firePropertyChange ("lote", loteOld, lote);
	}



	/**
	 * Return the value associated with the column: cd_lic_item
	 */
	public br.com.ksisolucoes.vo.consorcio.LicitacaoItem getLicitacaoItem () {
		return getPropertyValue(this, licitacaoItem, PROP_LICITACAO_ITEM); 
	}

	/**
	 * Set the value related to the column: cd_lic_item
	 * @param licitacaoItem the cd_lic_item value
	 */
	public void setLicitacaoItem (br.com.ksisolucoes.vo.consorcio.LicitacaoItem licitacaoItem) {
//        br.com.ksisolucoes.vo.consorcio.LicitacaoItem licitacaoItemOld = this.licitacaoItem;
		this.licitacaoItem = licitacaoItem;
//        this.getPropertyChangeSupport().firePropertyChange ("licitacaoItem", licitacaoItemOld, licitacaoItem);
	}



	/**
	 * Return the value associated with the column: cd_reg_it_nf
	 */
	public br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal getRegistroItemNotaFiscal () {
		return getPropertyValue(this, registroItemNotaFiscal, PROP_REGISTRO_ITEM_NOTA_FISCAL); 
	}

	/**
	 * Set the value related to the column: cd_reg_it_nf
	 * @param registroItemNotaFiscal the cd_reg_it_nf value
	 */
	public void setRegistroItemNotaFiscal (br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal registroItemNotaFiscal) {
//        br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal registroItemNotaFiscalOld = this.registroItemNotaFiscal;
		this.registroItemNotaFiscal = registroItemNotaFiscal;
//        this.getPropertyChangeSupport().firePropertyChange ("registroItemNotaFiscal", registroItemNotaFiscalOld, registroItemNotaFiscal);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscalLicitacao)) return false;
		else {
			br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscalLicitacao registroItemNotaFiscalLicitacao = (br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscalLicitacao) obj;
			if (null == this.getCodigo() || null == registroItemNotaFiscalLicitacao.getCodigo()) return false;
			else return (this.getCodigo().equals(registroItemNotaFiscalLicitacao.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}