package br.com.ksisolucoes.vo.basico;

import java.io.Serializable;

import br.com.ksisolucoes.vo.basico.base.BaseCidadeCep;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

public class CidadeCep extends BaseCidadeCep implements CodigoManager {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public CidadeCep() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public CidadeCep(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public CidadeCep(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.basico.Cidade cidade,
            java.lang.String cep) {

        super(
                codigo,
                cidade,
                cep);
    }

    /*[CONSTRUCTOR MARKER END]*/
    @Override
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    @Override
    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}