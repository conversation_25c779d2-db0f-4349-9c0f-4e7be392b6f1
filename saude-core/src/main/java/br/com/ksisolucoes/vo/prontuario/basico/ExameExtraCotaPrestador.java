package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseExameExtraCotaPrestador;



public class ExameExtraCotaPrestador extends BaseExameExtraCotaPrestador implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ExameExtraCotaPrestador () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ExameExtraCotaPrestador (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ExameExtraCotaPrestador (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia examePrestadorCompetencia,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataMovimentacao,
		java.lang.Double valorExtraCota) {

		super (
			codigo,
			examePrestadorCompetencia,
			usuario,
			dataMovimentacao,
			valorExtraCota);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

}