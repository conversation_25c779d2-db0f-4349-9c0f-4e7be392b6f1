<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
	
<hibernate-mapping package="br.com.ksisolucoes.vo.atividadegrupo"  >
    <class 
        name="AtividadeGrupoPaciente"
        table="atividade_grupo_paciente"
    >

        <id
            name="codigo" 
            type="java.lang.Long"   
            column="cd_atv_grupo_paciente"  
        > 
            <generator class="assigned"/>

        </id> 
        <version column="version" name="version" type="long" />			
 
        <many-to-one
            name="atividadeGrupo"
            class="br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo"
        >
            <column name="cd_atv_grupo"/>
        </many-to-one>
        
        <many-to-one
            name="usuarioCadsus"
            class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"
        >
            <column name="cd_usu_cadsus"/>
        </many-to-one>

        <property
            column="situacao"
            name="situacao"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property
            name="cns"
            column="cns"
            type="java.lang.Long"
        />

        <property
            name="cpf"
            column="cpf"
            type="java.lang.String"
        />

        <property
            name="dataNascimento"
            column="dt_nascimento"
            type="java.util.Date"
        />

        <property
            name="avaliacaoAlterada"
            column="avaliacao_alterada"
            type="java.lang.Long"
        />
        
         <property
            column="pad"
            name="pressaoArterialDiastolica"
            type="java.lang.Long"
        />
 
        <property
            column="pas"
            name="pressaoArterialSistolica"
            type="java.lang.Long"
        />
        
        <property
            name="peso"
            column="peso"
            type="java.lang.Double"
        />
        
        <property
            name="altura"
            column="altura"
            type="java.lang.Double"
        />
        
        <property
            name="cessouHabitoFumar"
            column="cessouhabitofumar"
            type="java.lang.Long"
        />
        
        <property
            name="abandonouGrupo"
            column="abandonougrupo"
            type="java.lang.Long"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus"
                name="enderecoUsuarioCadsus"
        >
            <column name="cd_endereco"/>
        </many-to-one>
        
        <property
            name="evolucao"
            column="evolucao"
            type="java.lang.String"
        />

        <many-to-one
                name="profissionalEvolucao"
                class="br.com.ksisolucoes.vo.cadsus.Profissional"
        >
            <column name="cd_profissional_evolucao"/>
        </many-to-one>
        
        <property
            name="sexo"
            column="sexo"
            type="java.lang.String"
        />

        <many-to-one class="br.com.ksisolucoes.vo.basico.IndiceImc"
                     not-null="false"
                     name="indiceImc">
            <column name="cd_indice_imc" />
        </many-to-one>

        <property
            column="gestante"
            name="gestante"
            type="java.lang.Long"
        />

        <property
            name="dum"
            column="dum"
            type="timestamp"
        />

    </class>
</hibernate-mapping>