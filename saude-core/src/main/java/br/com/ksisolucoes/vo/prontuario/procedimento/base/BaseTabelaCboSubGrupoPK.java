package br.com.ksisolucoes.vo.prontuario.procedimento.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;


public abstract class BaseTabelaCboSubGrupoPK extends BaseRootVO implements Serializable {

	protected int hashCode = Integer.MIN_VALUE;

	public static String PROP_TABELA_CBO_GRUPO = "tabelaCboGrupo";
	public static String PROP_CODIGO = "codigo";

	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboGrupo tabelaCboGrupo;
	private java.lang.Long codigo;


	public BaseTabelaCboSubGrupoPK () {}
	
	public BaseTabelaCboSubGrupoPK (
		br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboGrupo tabelaCboGrupo,
		java.lang.Long codigo) {

		this.setTabelaCboGrupo(tabelaCboGrupo);
		this.setCodigo(codigo);
	}


	/**
	 * Return the value associated with the column: cd_grupo_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboGrupo getTabelaCboGrupo () {
		return getPropertyValue(this, tabelaCboGrupo, PROP_TABELA_CBO_GRUPO); 
	}

	/**
	 * Set the value related to the column: cd_grupo_cbo
	 * @param tabelaCboGrupo the cd_grupo_cbo value
	 */
	public void setTabelaCboGrupo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboGrupo tabelaCboGrupo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboGrupo tabelaCboGrupoOld = this.tabelaCboGrupo;
		this.tabelaCboGrupo = tabelaCboGrupo;
//        this.getPropertyChangeSupport().firePropertyChange ("tabelaCboGrupo", tabelaCboGrupoOld, tabelaCboGrupo);
	}



	/**
	 * Return the value associated with the column: cd_subgrupo_cbo
	 */
	public java.lang.Long getCodigo () {
		return getPropertyValue(this, codigo, PROP_CODIGO); 
	}

	/**
	 * Set the value related to the column: cd_subgrupo_cbo
	 * @param codigo the cd_subgrupo_cbo value
	 */
	public void setCodigo (java.lang.Long codigo) {
//        java.lang.Long codigoOld = this.codigo;
		this.codigo = codigo;
//        this.getPropertyChangeSupport().firePropertyChange ("codigo", codigoOld, codigo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboSubGrupoPK)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboSubGrupoPK mObj = (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboSubGrupoPK) obj;
			if (null != this.getTabelaCboGrupo() && null != mObj.getTabelaCboGrupo()) {
				if (!this.getTabelaCboGrupo().equals(mObj.getTabelaCboGrupo())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getCodigo() && null != mObj.getCodigo()) {
				if (!this.getCodigo().equals(mObj.getCodigo())) {
					return false;
				}
			}
			else {
				return false;
			}
			return true;
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			StringBuilder sb = new StringBuilder();
			if (null != this.getTabelaCboGrupo()) {
				sb.append(this.getTabelaCboGrupo().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getCodigo()) {
				sb.append(this.getCodigo().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			this.hashCode = sb.toString().hashCode();
		}
		return this.hashCode;
	}

    private java.beans.PropertyChangeSupport propertyChangeSupport;

    protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
        if( this.propertyChangeSupport == null ) {
            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
        }
        return this.propertyChangeSupport;
    }

    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.addPropertyChangeListener(l);
    }

    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
		propertyChangeSupport.addPropertyChangeListener(propertyName, listener);
    }

    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.removePropertyChangeListener(l);
    }
}