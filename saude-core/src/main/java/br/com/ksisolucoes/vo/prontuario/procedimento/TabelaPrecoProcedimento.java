package br.com.ksisolucoes.vo.prontuario.procedimento;

import java.io.Serializable;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.procedimento.base.BaseTabelaPrecoProcedimento;



public class TabelaPrecoProcedimento extends BaseTabelaPrecoProcedimento implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public TabelaPrecoProcedimento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public TabelaPrecoProcedimento (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public TabelaPrecoProcedimento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento,
		br.com.ksisolucoes.vo.prontuario.basico.Convenio convenio,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.Double preco,
		java.util.Date dataUsuario) {

		super (
			codigo,
			procedimento,
			convenio,
			usuario,
			preco,
			dataUsuario);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}