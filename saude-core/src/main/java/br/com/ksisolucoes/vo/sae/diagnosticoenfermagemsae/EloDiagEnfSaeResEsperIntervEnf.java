package br.com.ksisolucoes.vo.sae.diagnosticoenfermagemsae;

import java.io.Serializable;

import br.com.ksisolucoes.vo.sae.diagnosticoenfermagemsae.base.BaseEloDiagEnfSaeResEsperIntervEnf;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class EloDiagEnfSaeResEsperIntervEnf extends BaseEloDiagEnfSaeResEsperIntervEnf implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EloDiagEnfSaeResEsperIntervEnf () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EloDiagEnfSaeResEsperIntervEnf (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public EloDiagEnfSaeResEsperIntervEnf (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.sae.diagnosticoenfermagemsae.DiagnosticoEnfermagemSae diagnostico,
		br.com.ksisolucoes.vo.prontuario.basico.ResultadoEsperado resultado,
		br.com.ksisolucoes.vo.prontuario.basico.IntervencaoEnfermagem intervencaoEnfermagem) {

		super (
			codigo,
			diagnostico,
			resultado,
			intervencaoEnfermagem);
	}

/*[CONSTRUCTOR MARKER END]*/

	@Override
	public boolean equals (Object obj) {
		if (null == obj) {
			return false;
		}

		if (!(obj instanceof br.com.ksisolucoes.vo.sae.diagnosticoenfermagemsae.EloDiagEnfSaeResEsperIntervEnf)) {
			return false;
		}

		br.com.ksisolucoes.vo.sae.diagnosticoenfermagemsae.EloDiagEnfSaeResEsperIntervEnf eloDiagEnfSaeResEsperIntervEnf = (br.com.ksisolucoes.vo.sae.diagnosticoenfermagemsae.EloDiagEnfSaeResEsperIntervEnf) obj;

		if (this.getCodigo() != null && eloDiagEnfSaeResEsperIntervEnf.getCodigo() != null) {
			return this.getCodigo().equals(eloDiagEnfSaeResEsperIntervEnf.getCodigo())
					&& this.getResultado().equals(eloDiagEnfSaeResEsperIntervEnf.getResultado())
					&& this.getIntervencaoEnfermagem().equals(eloDiagEnfSaeResEsperIntervEnf.getIntervencaoEnfermagem());
		}

		return (this.getCodigo() == null && eloDiagEnfSaeResEsperIntervEnf.getCodigo() == null)
				&& this.getResultado().equals(eloDiagEnfSaeResEsperIntervEnf.getResultado())
				&& this.getIntervencaoEnfermagem().equals(eloDiagEnfSaeResEsperIntervEnf.getIntervencaoEnfermagem());
	}

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}