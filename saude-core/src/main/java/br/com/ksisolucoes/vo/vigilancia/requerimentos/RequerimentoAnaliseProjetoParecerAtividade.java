package br.com.ksisolucoes.vo.vigilancia.requerimentos;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoAnaliseProjetoParecerAtividade;

import java.io.Serializable;



public class RequerimentoAnaliseProjetoParecerAtividade extends BaseRequerimentoAnaliseProjetoParecerAtividade implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequerimentoAnaliseProjetoParecerAtividade () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequerimentoAnaliseProjetoParecerAtividade (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequerimentoAnaliseProjetoParecerAtividade (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAnaliseProjetoParecer requerimentoAnaliseProjetoParecer,
		br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia atividadesVigilancia) {

		super (
			codigo,
			requerimentoAnaliseProjetoParecer,
			atividadesVigilancia);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}