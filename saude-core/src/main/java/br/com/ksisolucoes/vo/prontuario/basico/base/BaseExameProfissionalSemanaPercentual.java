package br.com.ksisolucoes.vo.prontuario.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the exa_prof_semana_percentual table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="exa_prof_semana_percentual"
 */

public abstract class BaseExameProfissionalSemanaPercentual extends BaseRootVO implements Serializable {

	public static String REF = "ExameProfissionalSemanaPercentual";
	public static final String PROP_EMPRESA_PRESTADOR = "empresaPrestador";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_EXAME_PROFISSIONAL_SEMANA = "exameProfissionalSemana";
	public static final String PROP_TETO = "teto";
	public static final String PROP_TETO_UTILIZADO = "tetoUtilizado";


	// constructors
	public BaseExameProfissionalSemanaPercentual () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseExameProfissionalSemanaPercentual (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseExameProfissionalSemanaPercentual (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.exame.ExameProfissionalSemana exameProfissionalSemana,
		br.com.ksisolucoes.vo.basico.Empresa empresaPrestador,
		java.lang.Double teto,
		java.lang.Double tetoUtilizado) {

		this.setCodigo(codigo);
		this.setExameProfissionalSemana(exameProfissionalSemana);
		this.setEmpresaPrestador(empresaPrestador);
		this.setTeto(teto);
		this.setTetoUtilizado(tetoUtilizado);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Double teto;
	private java.lang.Double tetoUtilizado;

	// many to one
	private br.com.ksisolucoes.vo.exame.ExameProfissionalSemana exameProfissionalSemana;
	private br.com.ksisolucoes.vo.basico.Empresa empresaPrestador;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_exa_prof_semana_percentual"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: teto
	 */
	public java.lang.Double getTeto () {
		return getPropertyValue(this, teto, PROP_TETO); 
	}

	/**
	 * Set the value related to the column: teto
	 * @param teto the teto value
	 */
	public void setTeto (java.lang.Double teto) {
//        java.lang.Double tetoOld = this.teto;
		this.teto = teto;
//        this.getPropertyChangeSupport().firePropertyChange ("teto", tetoOld, teto);
	}



	/**
	 * Return the value associated with the column: teto_utilizado
	 */
	public java.lang.Double getTetoUtilizado () {
		return getPropertyValue(this, tetoUtilizado, PROP_TETO_UTILIZADO); 
	}

	/**
	 * Set the value related to the column: teto_utilizado
	 * @param tetoUtilizado the teto_utilizado value
	 */
	public void setTetoUtilizado (java.lang.Double tetoUtilizado) {
//        java.lang.Double tetoUtilizadoOld = this.tetoUtilizado;
		this.tetoUtilizado = tetoUtilizado;
//        this.getPropertyChangeSupport().firePropertyChange ("tetoUtilizado", tetoUtilizadoOld, tetoUtilizado);
	}



	/**
	 * Return the value associated with the column: cd_exame_profissional_semana
	 */
	public br.com.ksisolucoes.vo.exame.ExameProfissionalSemana getExameProfissionalSemana () {
		return getPropertyValue(this, exameProfissionalSemana, PROP_EXAME_PROFISSIONAL_SEMANA); 
	}

	/**
	 * Set the value related to the column: cd_exame_profissional_semana
	 * @param exameProfissionalSemana the cd_exame_profissional_semana value
	 */
	public void setExameProfissionalSemana (br.com.ksisolucoes.vo.exame.ExameProfissionalSemana exameProfissionalSemana) {
//        br.com.ksisolucoes.vo.exame.ExameProfissionalSemana exameProfissionalSemanaOld = this.exameProfissionalSemana;
		this.exameProfissionalSemana = exameProfissionalSemana;
//        this.getPropertyChangeSupport().firePropertyChange ("exameProfissionalSemana", exameProfissionalSemanaOld, exameProfissionalSemana);
	}



	/**
	 * Return the value associated with the column: empresa_prestador
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresaPrestador () {
		return getPropertyValue(this, empresaPrestador, PROP_EMPRESA_PRESTADOR); 
	}

	/**
	 * Set the value related to the column: empresa_prestador
	 * @param empresaPrestador the empresa_prestador value
	 */
	public void setEmpresaPrestador (br.com.ksisolucoes.vo.basico.Empresa empresaPrestador) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaPrestadorOld = this.empresaPrestador;
		this.empresaPrestador = empresaPrestador;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaPrestador", empresaPrestadorOld, empresaPrestador);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.ExameProfissionalSemanaPercentual)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.ExameProfissionalSemanaPercentual exameProfissionalSemanaPercentual = (br.com.ksisolucoes.vo.prontuario.basico.ExameProfissionalSemanaPercentual) obj;
			if (null == this.getCodigo() || null == exameProfissionalSemanaPercentual.getCodigo()) return false;
			else return (this.getCodigo().equals(exameProfissionalSemanaPercentual.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}