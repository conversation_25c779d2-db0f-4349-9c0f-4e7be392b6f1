<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="ExameProfissionalSemanaPercentual" table="exa_prof_semana_percentual" >

        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_exa_prof_semana_percentual"
        >
            <generator class="sequence">
                <param name="sequence">seq_exa_semana_percentual</param>
            </generator>
        </id>
		<version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.exame.ExameProfissionalSemana"
            column="cd_exame_profissional_semana"
            name="exameProfissionalSemana"
            not-null="true"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.basico.Empresa"
            column="empresa_prestador"
            name="empresaPrestador"
            not-null="true"
        />

        <property
            name="teto"
            column="teto"
            type="java.lang.Double"
            not-null="true"
        />

        <property
            name="tetoUtilizado"
            column="teto_utilizado"
            type="java.lang.Double"
            not-null="true"
        />

    </class>
</hibernate-mapping>
