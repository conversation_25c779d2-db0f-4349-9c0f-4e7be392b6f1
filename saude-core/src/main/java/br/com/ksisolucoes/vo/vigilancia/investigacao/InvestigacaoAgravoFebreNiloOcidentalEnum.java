package br.com.ksisolucoes.vo.vigilancia.investigacao;

import br.com.ksisolucoes.enums.IEnum;

public class InvestigacaoAgravoFebreNiloOcidentalEnum {

    public enum SimNaoIgnoradoEnum implements IEnum {
        SIM(1L, "Sim"),
        NAO(2L, "Não"),
        IGNORADO(9L, "Ignorado");

        private Long value;
        private String descricao;

        SimNaoIgnoradoEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static SimNaoIgnoradoEnum valueOf(Long value) {
            for (SimNaoIgnoradoEnum v : SimNaoIgnoradoEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum AspectoLiquorEnum implements IEnum{
        LIMPIDO(1L, "Límpido"),
        PURULENTO(2L, "Purulento"),
        HEMORRAGICO(3L, "Hemorrágico"),
        TURVO(4L, "Turvo"),
        XANTOCROMICO(5L, "Xantocrômico"),
        OUTRO(6L, "Outro"),
        IGNORADO(9L, "Ignorado");

        private Long value;
        private String descricao;

        AspectoLiquorEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static AspectoLiquorEnum valueOf(Long value) {
            for (AspectoLiquorEnum v : AspectoLiquorEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum ResultadoReagenteEnum implements IEnum{
        REAGENTE(1L, "Reagente"),
        NAO_REAGENTE(2L, " Não Reagente"),
        INCONCLUSIVO(3L, "Inconclusivo"),
        NAO_REALIZADO(4L, "Não Realizado");

        private Long value;
        private String descricao;

        ResultadoReagenteEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static ResultadoReagenteEnum valueOf(Long value) {
            for (ResultadoReagenteEnum v : ResultadoReagenteEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum PositivoNegativoEnum implements IEnum{
        POSITIVO(1L, "Positivo"),
        NEGATIVO(2L, "Negativo"),
        INCONCLUSIVO(3L, "Inconclusivo"),
        NAO_RELAIZADO(4L, "Não realizado");

        private Long value;
        private String descricao;

        PositivoNegativoEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static PositivoNegativoEnum valueOf(Long value) {
            for (PositivoNegativoEnum v : PositivoNegativoEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum DetectadoEnum implements IEnum{
        DETECTADO(1L, "Detectado"),
        NAO_DETECTADO(2L, "Não detectado"),
        INCONCLUSIVO(3L, "Inconclusivo"),
        NAO_RELAIZADO(4L, "Não realizado");

        private Long value;
        private String descricao;

        DetectadoEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static DetectadoEnum valueOf(Long value) {
            for (DetectadoEnum v : DetectadoEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum ClassificacaoFinalEnum implements IEnum{
        CONFIRMADO(1L, "Confirmado"),
        DESCARTADO(2L, "Descartado");

        private Long value;
        private String descricao;

        ClassificacaoFinalEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static ClassificacaoFinalEnum valueOf(Long value) {
            for (ClassificacaoFinalEnum v : ClassificacaoFinalEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum CriterioConfirmacaoDescarteEnum implements IEnum{
        LABORATORIO(1L, "Laboratório"),
        VINCULO_EPIDEMIOLOGICO(2L, "Vínculo epidemiológico"),
        CLINICO(3L, "Clínico");

        private Long value;
        private String descricao;

        CriterioConfirmacaoDescarteEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static CriterioConfirmacaoDescarteEnum valueOf(Long value) {
            for (CriterioConfirmacaoDescarteEnum v : CriterioConfirmacaoDescarteEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum EvolucaoCasoEnum implements IEnum{
        CURA(1L, "Cura"),
        OBITO_FNO(2L, "Óbito por FNO"),
        OBITO_OUTRAS_CAUSAS(3L, "Óbito por outras causas"),
        IGNORADO(9L, "Ignorado");

        private Long value;
        private String descricao;

        EvolucaoCasoEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static EvolucaoCasoEnum valueOf(Long value) {
            for (EvolucaoCasoEnum v : EvolucaoCasoEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

}
