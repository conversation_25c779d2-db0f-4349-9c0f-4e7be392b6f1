package br.com.ksisolucoes.vo.samu.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the motivo_ocorrencia_samu table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="motivo_ocorrencia_samu"
 */

public abstract class BaseMotivoOcorrenciaSamu extends BaseRootVO implements Serializable {

	public static String REF = "MotivoOcorrenciaSamu";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DESCRICAO_MOTIVO = "descricaoMotivo";
	public static final String PROP_MOTIVO_OCORRENCIA_TIPO_SAMU = "motivoOcorrenciaTipoSamu";


	// constructors
	public BaseMotivoOcorrenciaSamu () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseMotivoOcorrenciaSamu (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseMotivoOcorrenciaSamu (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.samu.MotivoOcorrenciaTipoSamu motivoOcorrenciaTipoSamu,
		java.lang.String descricaoMotivo) {

		this.setCodigo(codigo);
		this.setMotivoOcorrenciaTipoSamu(motivoOcorrenciaTipoSamu);
		this.setDescricaoMotivo(descricaoMotivo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricaoMotivo;

	// many to one
	private br.com.ksisolucoes.vo.samu.MotivoOcorrenciaTipoSamu motivoOcorrenciaTipoSamu;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_motivo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_motivo
	 */
	public java.lang.String getDescricaoMotivo () {
		return getPropertyValue(this, descricaoMotivo, PROP_DESCRICAO_MOTIVO); 
	}

	/**
	 * Set the value related to the column: ds_motivo
	 * @param descricaoMotivo the ds_motivo value
	 */
	public void setDescricaoMotivo (java.lang.String descricaoMotivo) {
//        java.lang.String descricaoMotivoOld = this.descricaoMotivo;
		this.descricaoMotivo = descricaoMotivo;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoMotivo", descricaoMotivoOld, descricaoMotivo);
	}



	/**
	 * Return the value associated with the column: cd_tipo_ocorrencia
	 */
	public br.com.ksisolucoes.vo.samu.MotivoOcorrenciaTipoSamu getMotivoOcorrenciaTipoSamu () {
		return getPropertyValue(this, motivoOcorrenciaTipoSamu, PROP_MOTIVO_OCORRENCIA_TIPO_SAMU); 
	}

	/**
	 * Set the value related to the column: cd_tipo_ocorrencia
	 * @param motivoOcorrenciaTipoSamu the cd_tipo_ocorrencia value
	 */
	public void setMotivoOcorrenciaTipoSamu (br.com.ksisolucoes.vo.samu.MotivoOcorrenciaTipoSamu motivoOcorrenciaTipoSamu) {
//        br.com.ksisolucoes.vo.samu.MotivoOcorrenciaTipoSamu motivoOcorrenciaTipoSamuOld = this.motivoOcorrenciaTipoSamu;
		this.motivoOcorrenciaTipoSamu = motivoOcorrenciaTipoSamu;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoOcorrenciaTipoSamu", motivoOcorrenciaTipoSamuOld, motivoOcorrenciaTipoSamu);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.samu.MotivoOcorrenciaSamu)) return false;
		else {
			br.com.ksisolucoes.vo.samu.MotivoOcorrenciaSamu motivoOcorrenciaSamu = (br.com.ksisolucoes.vo.samu.MotivoOcorrenciaSamu) obj;
			if (null == this.getCodigo() || null == motivoOcorrenciaSamu.getCodigo()) return false;
			else return (this.getCodigo().equals(motivoOcorrenciaSamu.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}