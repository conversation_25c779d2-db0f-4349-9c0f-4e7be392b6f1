package br.com.ksisolucoes.vo.consorcio;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.consorcio.base.BaseConsorcioGuiaProcedimentoItem;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;

public class ConsorcioGuiaProcedimentoItem extends BaseConsorcioGuiaProcedimentoItem implements CodigoManager {

    private static final long serialVersionUID = 1L;
    
    public static final String PROP_DESCRICAO_STATUS = "descricaoStatus";

    public enum StatusGuiaProcedimentoItem implements IEnum<StatusGuiaProcedimentoItem> {
        ABERTA(0L, Bundle.getStringApplication("rotulo_aberta")),
        EXECUTADO(1L, Bundle.getStringApplication("rotulo_executado")),
        CANCELADA(2L, Bundle.getStringApplication("rotulo_cancelada")),
        REPROVADO(3L, Bundle.getStringApplication("rotulo_reprovado")),
        ;
        
        private final Long value;
        private final String descricao;

        StatusGuiaProcedimentoItem(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }
        
        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    public enum SituacaoSisreg implements IEnum<ConsorcioGuiaProcedimento.StatusGuiaProcedimento> {
        PENDENTE(0L, Bundle.getStringApplication("rotulo_pendente")),
        CONFIRMADA(1L, Bundle.getStringApplication("rotulo_confirmada")),
        NAO_CONFIRMADA(2L, Bundle.getStringApplication("rotulo_nao_confirmada")),
        ;

        private final Long value;
        private final String descricao;

        SituacaoSisreg(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static SituacaoSisreg valueOf(Long value) {
            for (SituacaoSisreg situacaoSisreg : SituacaoSisreg.values()) {
                if (situacaoSisreg.value().equals(value)) {
                    return situacaoSisreg;
                }
            }

            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public ConsorcioGuiaProcedimentoItem () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ConsorcioGuiaProcedimentoItem (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ConsorcioGuiaProcedimentoItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento consorcioGuiaProcedimento,
		br.com.ksisolucoes.vo.consorcio.ConsorcioProcedimento consorcioProcedimento,
		br.com.ksisolucoes.vo.controle.Usuario usuarioSituacaoSisreg,
		java.lang.Long status,
		java.lang.Double valorProcedimento,
		java.lang.Long quantidade,
		java.lang.Long situacaoSisreg) {

		super (
			codigo,
			consorcioGuiaProcedimento,
			consorcioProcedimento,
			usuarioSituacaoSisreg,
			status,
			valorProcedimento,
			quantidade,
			situacaoSisreg);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getDescricaoStatus(){
        if (StatusGuiaProcedimentoItem.ABERTA.value().equals(getStatus())) {
            return StatusGuiaProcedimentoItem.ABERTA.descricao();
        } else if (StatusGuiaProcedimentoItem.CANCELADA.value().equals(getStatus())) {
            return StatusGuiaProcedimentoItem.CANCELADA.descricao();
        } else if (StatusGuiaProcedimentoItem.EXECUTADO.value().equals(getStatus())) {
            return StatusGuiaProcedimentoItem.EXECUTADO.descricao();
        }
        return Bundle.getStringApplication("rotulo_desconhecido");
    }

    public String getSituacaoSisregDescrição() {
        if (getSituacaoSisreg() != null) {
            if (SituacaoSisreg.PENDENTE.value().equals(getSituacaoSisreg())) {
                return SituacaoSisreg.PENDENTE.descricao();
            } else if (SituacaoSisreg.CONFIRMADA.value().equals(getSituacaoSisreg())) {
                return SituacaoSisreg.CONFIRMADA.descricao();
            } else if (SituacaoSisreg.NAO_CONFIRMADA.value().equals(getSituacaoSisreg())) {
                return SituacaoSisreg.NAO_CONFIRMADA.descricao();
            }
        }
        return "";
    }

    public String getSituacaoSisregDescricao() {
        if (this.getSituacaoSisreg() != null) {
            if (ConsorcioGuiaProcedimentoItem.SituacaoSisreg.PENDENTE.value().equals(getSituacaoSisreg())) {
                return ConsorcioGuiaProcedimentoItem.SituacaoSisreg.PENDENTE.descricao();
            } else if (ConsorcioGuiaProcedimentoItem.SituacaoSisreg.CONFIRMADA.value().equals(getSituacaoSisreg())) {
                return ConsorcioGuiaProcedimentoItem.SituacaoSisreg.CONFIRMADA.descricao();
            } else if (ConsorcioGuiaProcedimentoItem.SituacaoSisreg.NAO_CONFIRMADA.value().equals(getSituacaoSisreg())) {
                return ConsorcioGuiaProcedimentoItem.SituacaoSisreg.NAO_CONFIRMADA.descricao();
            }
        }
        return "";
    }

    public String getConsorcioProcedimentoDescricaoVO() {
        if (this.getConsorcioProcedimento() != null && this.getConsorcioProcedimento().getDescricaoVO() != null) {
            return this.getConsorcioProcedimento().getDescricaoVO();
        }
        return "";
    }


}