package br.com.ksisolucoes.vo.esus.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the termo_recusa_cad_individual table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="termo_recusa_cad_individual"
 */

public abstract class BaseTermoRecusaCadastroIndividual extends BaseRootVO implements Serializable {

	public static String REF = "TermoRecusaCadastroIndividual";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_CNS_RESPONSAVEL = "cnsResponsavel";
	public static final String PROP_CELULAR = "celular";
	public static final String PROP_CIDADE_NASCIMENTO = "cidadeNascimento";
	public static final String PROP_DATA_NASCIMENTO = "dataNascimento";
	public static final String PROP_DATA_PREENCHIMENTO = "dataPreenchimento";
	public static final String PROP_ETNIA_INDIGENA = "etniaIndigena";
	public static final String PROP_PORTARIA_NATURALIZACAO = "portariaNaturalizacao";
	public static final String PROP_NOME_PAI = "nomePai";
	public static final String PROP_NOME_MAE = "nomeMae";
	public static final String PROP_NIS = "nis";
	public static final String PROP_PAIS_NASCIMENTO = "paisNascimento";
	public static final String PROP_PROFISSIONAL = "profissional";
	public static final String PROP_MAE_DESCONHECIDO = "maeDesconhecido";
	public static final String PROP_DATA_ENTRADA_BRASIL = "dataEntradaBrasil";
	public static final String PROP_NACIONALIDADE = "nacionalidade";
	public static final String PROP_FLAG_FORA_AREA = "flagForaArea";
	public static final String PROP_CNS = "cns";
	public static final String PROP_EQUIPE_PROFISSIONAL = "equipeProfissional";
	public static final String PROP_EQUIPE_MICRO_AREA = "equipeMicroArea";
	public static final String PROP_NOME_SOCIAL = "nomeSocial";
	public static final String PROP_DATA_NATURALIZADO = "dataNaturalizado";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_TABELA_CBO = "tabelaCbo";
	public static final String PROP_PAI_DESCONHECIDO = "paiDesconhecido";
	public static final String PROP_RESPONSAVEL = "responsavel";
	public static final String PROP_EMAIL = "email";
	public static final String PROP_RACA = "raca";
	public static final String PROP_SEXO = "sexo";
	public static final String PROP_NOME = "nome";


	// constructors
	public BaseTermoRecusaCadastroIndividual () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseTermoRecusaCadastroIndividual (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseTermoRecusaCadastroIndividual (
		java.lang.Long codigo,
		java.util.Date dataPreenchimento) {

		this.setCodigo(codigo);
		this.setDataPreenchimento(dataPreenchimento);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String nome;
	private java.lang.String nomeSocial;
	private java.lang.String sexo;
	private java.util.Date dataNascimento;
	private java.lang.String nomeMae;
	private java.lang.Long maeDesconhecido;
	private java.lang.String nomePai;
	private java.lang.Long paiDesconhecido;
	private java.lang.Long nacionalidade;
	private java.util.Date dataNaturalizado;
	private java.util.Date dataEntradaBrasil;
	private java.lang.String portariaNaturalizacao;
	private java.lang.Long flagForaArea;
	private java.lang.String celular;
	private java.lang.String email;
	private java.lang.String nis;
	private java.lang.Long cns;
	private java.lang.Long cnsResponsavel;
	private java.lang.Long responsavel;
	private java.util.Date dataPreenchimento;

	// many to one
	private br.com.ksisolucoes.vo.basico.Cidade cidadeNascimento;
	private br.com.ksisolucoes.vo.cadsus.Raca raca;
	private br.com.ksisolucoes.vo.cadsus.EtniaIndigena etniaIndigena;
	private br.com.ksisolucoes.vo.basico.Pais paisNascimento;
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissional;
	private br.com.ksisolucoes.vo.basico.EquipeMicroArea equipeMicroArea;
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCbo;
	private br.com.ksisolucoes.vo.basico.EquipeProfissional equipeProfissional;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_termo_rec_cad_ind"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nome
	 */
	public java.lang.String getNome () {
		return getPropertyValue(this, nome, PROP_NOME); 
	}

	/**
	 * Set the value related to the column: nome
	 * @param nome the nome value
	 */
	public void setNome (java.lang.String nome) {
//        java.lang.String nomeOld = this.nome;
		this.nome = nome;
//        this.getPropertyChangeSupport().firePropertyChange ("nome", nomeOld, nome);
	}



	/**
	 * Return the value associated with the column: nm_social
	 */
	public java.lang.String getNomeSocial () {
		return getPropertyValue(this, nomeSocial, PROP_NOME_SOCIAL); 
	}

	/**
	 * Set the value related to the column: nm_social
	 * @param nomeSocial the nm_social value
	 */
	public void setNomeSocial (java.lang.String nomeSocial) {
//        java.lang.String nomeSocialOld = this.nomeSocial;
		this.nomeSocial = nomeSocial;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeSocial", nomeSocialOld, nomeSocial);
	}



	/**
	 * Return the value associated with the column: sexo
	 */
	public java.lang.String getSexo () {
		return getPropertyValue(this, sexo, PROP_SEXO); 
	}

	/**
	 * Set the value related to the column: sexo
	 * @param sexo the sexo value
	 */
	public void setSexo (java.lang.String sexo) {
//        java.lang.String sexoOld = this.sexo;
		this.sexo = sexo;
//        this.getPropertyChangeSupport().firePropertyChange ("sexo", sexoOld, sexo);
	}



	/**
	 * Return the value associated with the column: dt_nascimento
	 */
	public java.util.Date getDataNascimento () {
		return getPropertyValue(this, dataNascimento, PROP_DATA_NASCIMENTO); 
	}

	/**
	 * Set the value related to the column: dt_nascimento
	 * @param dataNascimento the dt_nascimento value
	 */
	public void setDataNascimento (java.util.Date dataNascimento) {
//        java.util.Date dataNascimentoOld = this.dataNascimento;
		this.dataNascimento = dataNascimento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataNascimento", dataNascimentoOld, dataNascimento);
	}



	/**
	 * Return the value associated with the column: nm_mae
	 */
	public java.lang.String getNomeMae () {
		return getPropertyValue(this, nomeMae, PROP_NOME_MAE); 
	}

	/**
	 * Set the value related to the column: nm_mae
	 * @param nomeMae the nm_mae value
	 */
	public void setNomeMae (java.lang.String nomeMae) {
//        java.lang.String nomeMaeOld = this.nomeMae;
		this.nomeMae = nomeMae;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeMae", nomeMaeOld, nomeMae);
	}



	/**
	 * Return the value associated with the column: mae_desconhecido
	 */
	public java.lang.Long getMaeDesconhecido () {
		return getPropertyValue(this, maeDesconhecido, PROP_MAE_DESCONHECIDO); 
	}

	/**
	 * Set the value related to the column: mae_desconhecido
	 * @param maeDesconhecido the mae_desconhecido value
	 */
	public void setMaeDesconhecido (java.lang.Long maeDesconhecido) {
//        java.lang.Long maeDesconhecidoOld = this.maeDesconhecido;
		this.maeDesconhecido = maeDesconhecido;
//        this.getPropertyChangeSupport().firePropertyChange ("maeDesconhecido", maeDesconhecidoOld, maeDesconhecido);
	}



	/**
	 * Return the value associated with the column: nm_pai
	 */
	public java.lang.String getNomePai () {
		return getPropertyValue(this, nomePai, PROP_NOME_PAI); 
	}

	/**
	 * Set the value related to the column: nm_pai
	 * @param nomePai the nm_pai value
	 */
	public void setNomePai (java.lang.String nomePai) {
//        java.lang.String nomePaiOld = this.nomePai;
		this.nomePai = nomePai;
//        this.getPropertyChangeSupport().firePropertyChange ("nomePai", nomePaiOld, nomePai);
	}



	/**
	 * Return the value associated with the column: pai_desconhecido
	 */
	public java.lang.Long getPaiDesconhecido () {
		return getPropertyValue(this, paiDesconhecido, PROP_PAI_DESCONHECIDO); 
	}

	/**
	 * Set the value related to the column: pai_desconhecido
	 * @param paiDesconhecido the pai_desconhecido value
	 */
	public void setPaiDesconhecido (java.lang.Long paiDesconhecido) {
//        java.lang.Long paiDesconhecidoOld = this.paiDesconhecido;
		this.paiDesconhecido = paiDesconhecido;
//        this.getPropertyChangeSupport().firePropertyChange ("paiDesconhecido", paiDesconhecidoOld, paiDesconhecido);
	}



	/**
	 * Return the value associated with the column: nacionalidade
	 */
	public java.lang.Long getNacionalidade () {
		return getPropertyValue(this, nacionalidade, PROP_NACIONALIDADE); 
	}

	/**
	 * Set the value related to the column: nacionalidade
	 * @param nacionalidade the nacionalidade value
	 */
	public void setNacionalidade (java.lang.Long nacionalidade) {
//        java.lang.Long nacionalidadeOld = this.nacionalidade;
		this.nacionalidade = nacionalidade;
//        this.getPropertyChangeSupport().firePropertyChange ("nacionalidade", nacionalidadeOld, nacionalidade);
	}



	/**
	 * Return the value associated with the column: dt_naturalizado
	 */
	public java.util.Date getDataNaturalizado () {
		return getPropertyValue(this, dataNaturalizado, PROP_DATA_NATURALIZADO); 
	}

	/**
	 * Set the value related to the column: dt_naturalizado
	 * @param dataNaturalizado the dt_naturalizado value
	 */
	public void setDataNaturalizado (java.util.Date dataNaturalizado) {
//        java.util.Date dataNaturalizadoOld = this.dataNaturalizado;
		this.dataNaturalizado = dataNaturalizado;
//        this.getPropertyChangeSupport().firePropertyChange ("dataNaturalizado", dataNaturalizadoOld, dataNaturalizado);
	}



	/**
	 * Return the value associated with the column: dt_entrada_brasil
	 */
	public java.util.Date getDataEntradaBrasil () {
		return getPropertyValue(this, dataEntradaBrasil, PROP_DATA_ENTRADA_BRASIL); 
	}

	/**
	 * Set the value related to the column: dt_entrada_brasil
	 * @param dataEntradaBrasil the dt_entrada_brasil value
	 */
	public void setDataEntradaBrasil (java.util.Date dataEntradaBrasil) {
//        java.util.Date dataEntradaBrasilOld = this.dataEntradaBrasil;
		this.dataEntradaBrasil = dataEntradaBrasil;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEntradaBrasil", dataEntradaBrasilOld, dataEntradaBrasil);
	}



	/**
	 * Return the value associated with the column: portaria_naturalizacao
	 */
	public java.lang.String getPortariaNaturalizacao () {
		return getPropertyValue(this, portariaNaturalizacao, PROP_PORTARIA_NATURALIZACAO); 
	}

	/**
	 * Set the value related to the column: portaria_naturalizacao
	 * @param portariaNaturalizacao the portaria_naturalizacao value
	 */
	public void setPortariaNaturalizacao (java.lang.String portariaNaturalizacao) {
//        java.lang.String portariaNaturalizacaoOld = this.portariaNaturalizacao;
		this.portariaNaturalizacao = portariaNaturalizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("portariaNaturalizacao", portariaNaturalizacaoOld, portariaNaturalizacao);
	}



	/**
	 * Return the value associated with the column: flag_fora_area
	 */
	public java.lang.Long getFlagForaArea () {
		return getPropertyValue(this, flagForaArea, PROP_FLAG_FORA_AREA); 
	}

	/**
	 * Set the value related to the column: flag_fora_area
	 * @param flagForaArea the flag_fora_area value
	 */
	public void setFlagForaArea (java.lang.Long flagForaArea) {
//        java.lang.Long flagForaAreaOld = this.flagForaArea;
		this.flagForaArea = flagForaArea;
//        this.getPropertyChangeSupport().firePropertyChange ("flagForaArea", flagForaAreaOld, flagForaArea);
	}



	/**
	 * Return the value associated with the column: celular
	 */
	public java.lang.String getCelular () {
		return getPropertyValue(this, celular, PROP_CELULAR); 
	}

	/**
	 * Set the value related to the column: celular
	 * @param celular the celular value
	 */
	public void setCelular (java.lang.String celular) {
//        java.lang.String celularOld = this.celular;
		this.celular = celular;
//        this.getPropertyChangeSupport().firePropertyChange ("celular", celularOld, celular);
	}



	/**
	 * Return the value associated with the column: email
	 */
	public java.lang.String getEmail () {
		return getPropertyValue(this, email, PROP_EMAIL); 
	}

	/**
	 * Set the value related to the column: email
	 * @param email the email value
	 */
	public void setEmail (java.lang.String email) {
//        java.lang.String emailOld = this.email;
		this.email = email;
//        this.getPropertyChangeSupport().firePropertyChange ("email", emailOld, email);
	}



	/**
	 * Return the value associated with the column: nis
	 */
	public java.lang.String getNis () {
		return getPropertyValue(this, nis, PROP_NIS); 
	}

	/**
	 * Set the value related to the column: nis
	 * @param nis the nis value
	 */
	public void setNis (java.lang.String nis) {
//        java.lang.String nisOld = this.nis;
		this.nis = nis;
//        this.getPropertyChangeSupport().firePropertyChange ("nis", nisOld, nis);
	}



	/**
	 * Return the value associated with the column: cns
	 */
	public java.lang.Long getCns () {
		return getPropertyValue(this, cns, PROP_CNS); 
	}

	/**
	 * Set the value related to the column: cns
	 * @param cns the cns value
	 */
	public void setCns (java.lang.Long cns) {
//        java.lang.Long cnsOld = this.cns;
		this.cns = cns;
//        this.getPropertyChangeSupport().firePropertyChange ("cns", cnsOld, cns);
	}



	/**
	 * Return the value associated with the column: cns_responsavel
	 */
	public java.lang.Long getCnsResponsavel () {
		return getPropertyValue(this, cnsResponsavel, PROP_CNS_RESPONSAVEL); 
	}

	/**
	 * Set the value related to the column: cns_responsavel
	 * @param cnsResponsavel the cns_responsavel value
	 */
	public void setCnsResponsavel (java.lang.Long cnsResponsavel) {
//        java.lang.Long cnsResponsavelOld = this.cnsResponsavel;
		this.cnsResponsavel = cnsResponsavel;
//        this.getPropertyChangeSupport().firePropertyChange ("cnsResponsavel", cnsResponsavelOld, cnsResponsavel);
	}



	/**
	 * Return the value associated with the column: responsavel
	 */
	public java.lang.Long getResponsavel () {
		return getPropertyValue(this, responsavel, PROP_RESPONSAVEL); 
	}

	/**
	 * Set the value related to the column: responsavel
	 * @param responsavel the responsavel value
	 */
	public void setResponsavel (java.lang.Long responsavel) {
//        java.lang.Long responsavelOld = this.responsavel;
		this.responsavel = responsavel;
//        this.getPropertyChangeSupport().firePropertyChange ("responsavel", responsavelOld, responsavel);
	}



	/**
	 * Return the value associated with the column: dt_preenchimento
	 */
	public java.util.Date getDataPreenchimento () {
		return getPropertyValue(this, dataPreenchimento, PROP_DATA_PREENCHIMENTO); 
	}

	/**
	 * Set the value related to the column: dt_preenchimento
	 * @param dataPreenchimento the dt_preenchimento value
	 */
	public void setDataPreenchimento (java.util.Date dataPreenchimento) {
//        java.util.Date dataPreenchimentoOld = this.dataPreenchimento;
		this.dataPreenchimento = dataPreenchimento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataPreenchimento", dataPreenchimentoOld, dataPreenchimento);
	}



	/**
	 * Return the value associated with the column: cod_cid_nascimento
	 */
	public br.com.ksisolucoes.vo.basico.Cidade getCidadeNascimento () {
		return getPropertyValue(this, cidadeNascimento, PROP_CIDADE_NASCIMENTO); 
	}

	/**
	 * Set the value related to the column: cod_cid_nascimento
	 * @param cidadeNascimento the cod_cid_nascimento value
	 */
	public void setCidadeNascimento (br.com.ksisolucoes.vo.basico.Cidade cidadeNascimento) {
//        br.com.ksisolucoes.vo.basico.Cidade cidadeNascimentoOld = this.cidadeNascimento;
		this.cidadeNascimento = cidadeNascimento;
//        this.getPropertyChangeSupport().firePropertyChange ("cidadeNascimento", cidadeNascimentoOld, cidadeNascimento);
	}



	/**
	 * Return the value associated with the column: cd_raca
	 */
	public br.com.ksisolucoes.vo.cadsus.Raca getRaca () {
		return getPropertyValue(this, raca, PROP_RACA); 
	}

	/**
	 * Set the value related to the column: cd_raca
	 * @param raca the cd_raca value
	 */
	public void setRaca (br.com.ksisolucoes.vo.cadsus.Raca raca) {
//        br.com.ksisolucoes.vo.cadsus.Raca racaOld = this.raca;
		this.raca = raca;
//        this.getPropertyChangeSupport().firePropertyChange ("raca", racaOld, raca);
	}



	/**
	 * Return the value associated with the column: cd_etnia
	 */
	public br.com.ksisolucoes.vo.cadsus.EtniaIndigena getEtniaIndigena () {
		return getPropertyValue(this, etniaIndigena, PROP_ETNIA_INDIGENA); 
	}

	/**
	 * Set the value related to the column: cd_etnia
	 * @param etniaIndigena the cd_etnia value
	 */
	public void setEtniaIndigena (br.com.ksisolucoes.vo.cadsus.EtniaIndigena etniaIndigena) {
//        br.com.ksisolucoes.vo.cadsus.EtniaIndigena etniaIndigenaOld = this.etniaIndigena;
		this.etniaIndigena = etniaIndigena;
//        this.getPropertyChangeSupport().firePropertyChange ("etniaIndigena", etniaIndigenaOld, etniaIndigena);
	}



	/**
	 * Return the value associated with the column: cd_pais_nascimento
	 */
	public br.com.ksisolucoes.vo.basico.Pais getPaisNascimento () {
		return getPropertyValue(this, paisNascimento, PROP_PAIS_NASCIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_pais_nascimento
	 * @param paisNascimento the cd_pais_nascimento value
	 */
	public void setPaisNascimento (br.com.ksisolucoes.vo.basico.Pais paisNascimento) {
//        br.com.ksisolucoes.vo.basico.Pais paisNascimentoOld = this.paisNascimento;
		this.paisNascimento = paisNascimento;
//        this.getPropertyChangeSupport().firePropertyChange ("paisNascimento", paisNascimentoOld, paisNascimento);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissional () {
		return getPropertyValue(this, profissional, PROP_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissional the cd_profissional value
	 */
	public void setProfissional (br.com.ksisolucoes.vo.cadsus.Profissional profissional) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalOld = this.profissional;
		this.profissional = profissional;
//        this.getPropertyChangeSupport().firePropertyChange ("profissional", profissionalOld, profissional);
	}



	/**
	 * Return the value associated with the column: cd_eqp_micro_area
	 */
	public br.com.ksisolucoes.vo.basico.EquipeMicroArea getEquipeMicroArea () {
		return getPropertyValue(this, equipeMicroArea, PROP_EQUIPE_MICRO_AREA); 
	}

	/**
	 * Set the value related to the column: cd_eqp_micro_area
	 * @param equipeMicroArea the cd_eqp_micro_area value
	 */
	public void setEquipeMicroArea (br.com.ksisolucoes.vo.basico.EquipeMicroArea equipeMicroArea) {
//        br.com.ksisolucoes.vo.basico.EquipeMicroArea equipeMicroAreaOld = this.equipeMicroArea;
		this.equipeMicroArea = equipeMicroArea;
//        this.getPropertyChangeSupport().firePropertyChange ("equipeMicroArea", equipeMicroAreaOld, equipeMicroArea);
	}



	/**
	 * Return the value associated with the column: cd_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getTabelaCbo () {
		return getPropertyValue(this, tabelaCbo, PROP_TABELA_CBO); 
	}

	/**
	 * Set the value related to the column: cd_cbo
	 * @param tabelaCbo the cd_cbo value
	 */
	public void setTabelaCbo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCboOld = this.tabelaCbo;
		this.tabelaCbo = tabelaCbo;
//        this.getPropertyChangeSupport().firePropertyChange ("tabelaCbo", tabelaCboOld, tabelaCbo);
	}



	/**
	 * Return the value associated with the column: cd_equipe_profissional
	 */
	public br.com.ksisolucoes.vo.basico.EquipeProfissional getEquipeProfissional () {
		return getPropertyValue(this, equipeProfissional, PROP_EQUIPE_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_equipe_profissional
	 * @param equipeProfissional the cd_equipe_profissional value
	 */
	public void setEquipeProfissional (br.com.ksisolucoes.vo.basico.EquipeProfissional equipeProfissional) {
//        br.com.ksisolucoes.vo.basico.EquipeProfissional equipeProfissionalOld = this.equipeProfissional;
		this.equipeProfissional = equipeProfissional;
//        this.getPropertyChangeSupport().firePropertyChange ("equipeProfissional", equipeProfissionalOld, equipeProfissional);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.esus.TermoRecusaCadastroIndividual)) return false;
		else {
			br.com.ksisolucoes.vo.esus.TermoRecusaCadastroIndividual termoRecusaCadastroIndividual = (br.com.ksisolucoes.vo.esus.TermoRecusaCadastroIndividual) obj;
			if (null == this.getCodigo() || null == termoRecusaCadastroIndividual.getCodigo()) return false;
			else return (this.getCodigo().equals(termoRecusaCadastroIndividual.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}