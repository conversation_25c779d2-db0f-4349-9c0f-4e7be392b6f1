package br.com.ksisolucoes.vo.cadsus.cds;

import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.cadsus.cds.base.BaseEsusFichaVacina;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;



public class EsusFichaVacina extends BaseEsusFichaVacina implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EsusFichaVacina () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EsusFichaVacina (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public EsusFichaVacina (
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.vacina.VacinaAplicacao vacinaAplicacao,
            br.com.ksisolucoes.vo.cadsus.Profissional profissional,
            br.com.ksisolucoes.vo.controle.Usuario usuario,
            br.com.ksisolucoes.vo.basico.Empresa empresa,
            br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo cbo,
            java.util.Date dataCadastro,
            java.lang.Long status,
            java.lang.String cnsProfissional,
            java.lang.String cboProfissional,
            java.lang.String cnesEstabelecimento,
            java.lang.String codigoIne,
            java.util.Date dataAplicacao,
            java.util.Date dataAplicacaoFim,
            java.lang.String sexo,
            java.util.Date dtNascimento,
            java.lang.Long localAtendimento,
            java.lang.Long historico) {

		super (
			codigo,
			vacinaAplicacao,
			profissional,
			usuario,
			empresa,
			cbo,
			dataCadastro,
			status,
			cnsProfissional,
			cboProfissional,
			cnesEstabelecimento,
			codigoIne,
			dataAplicacao,
                dataAplicacaoFim,
			sexo,
			dtNascimento,
			localAtendimento,
			historico);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDataAplicacaoFormatada() {
    	return Data.formatar(this.getDataAplicacao());
	}
}