package br.com.ksisolucoes.vo.cadsus.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the visita_territorial table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="visita_territorial"
 */

public abstract class BaseVisitaTerritorial extends BaseRootVO implements Serializable {

	public static String REF = "VisitaTerritorial";
	public static final String PROP_LOGRADOURO = "logradouro";
	public static final String PROP_TIPO_IMOVEL = "tipoImovel";
	public static final String PROP_FORA_AREA = "foraArea";
	public static final String PROP_EQUIPE_MICRO_AREA = "equipeMicroArea";
	public static final String PROP_PONTO_REFERENCIA = "pontoReferencia";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_VISITA_DOMICILIAR = "visitaDomiciliar";
	public static final String PROP_BAIRRO = "bairro";
	public static final String PROP_CEP = "cep";


	// constructors
	public BaseVisitaTerritorial () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseVisitaTerritorial (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseVisitaTerritorial (
		java.lang.Long codigo,
		java.lang.String pontoReferencia) {

		this.setCodigo(codigo);
		this.setPontoReferencia(pontoReferencia);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long tipoImovel;
	private java.lang.String cep;
	private java.lang.String logradouro;
	private java.lang.String bairro;
	private java.lang.String pontoReferencia;
	private java.lang.Long foraArea;

	// many to one
	private br.com.ksisolucoes.vo.cadsus.VisitaDomiciliar visitaDomiciliar;
	private br.com.ksisolucoes.vo.basico.EquipeMicroArea equipeMicroArea;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_visita_territorial"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: tipo_imovel
	 */
	public java.lang.Long getTipoImovel () {
		return getPropertyValue(this, tipoImovel, PROP_TIPO_IMOVEL); 
	}

	/**
	 * Set the value related to the column: tipo_imovel
	 * @param tipoImovel the tipo_imovel value
	 */
	public void setTipoImovel (java.lang.Long tipoImovel) {
//        java.lang.Long tipoImovelOld = this.tipoImovel;
		this.tipoImovel = tipoImovel;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoImovel", tipoImovelOld, tipoImovel);
	}



	/**
	 * Return the value associated with the column: cep
	 */
	public java.lang.String getCep () {
		return getPropertyValue(this, cep, PROP_CEP); 
	}

	/**
	 * Set the value related to the column: cep
	 * @param cep the cep value
	 */
	public void setCep (java.lang.String cep) {
//        java.lang.String cepOld = this.cep;
		this.cep = cep;
//        this.getPropertyChangeSupport().firePropertyChange ("cep", cepOld, cep);
	}



	/**
	 * Return the value associated with the column: logradouro
	 */
	public java.lang.String getLogradouro () {
		return getPropertyValue(this, logradouro, PROP_LOGRADOURO); 
	}

	/**
	 * Set the value related to the column: logradouro
	 * @param logradouro the logradouro value
	 */
	public void setLogradouro (java.lang.String logradouro) {
//        java.lang.String logradouroOld = this.logradouro;
		this.logradouro = logradouro;
//        this.getPropertyChangeSupport().firePropertyChange ("logradouro", logradouroOld, logradouro);
	}



	/**
	 * Return the value associated with the column: bairro
	 */
	public java.lang.String getBairro () {
		return getPropertyValue(this, bairro, PROP_BAIRRO); 
	}

	/**
	 * Set the value related to the column: bairro
	 * @param bairro the bairro value
	 */
	public void setBairro (java.lang.String bairro) {
//        java.lang.String bairroOld = this.bairro;
		this.bairro = bairro;
//        this.getPropertyChangeSupport().firePropertyChange ("bairro", bairroOld, bairro);
	}



	/**
	 * Return the value associated with the column: ponto_ref
	 */
	public java.lang.String getPontoReferencia () {
		return getPropertyValue(this, pontoReferencia, PROP_PONTO_REFERENCIA); 
	}

	/**
	 * Set the value related to the column: ponto_ref
	 * @param pontoReferencia the ponto_ref value
	 */
	public void setPontoReferencia (java.lang.String pontoReferencia) {
//        java.lang.String pontoReferenciaOld = this.pontoReferencia;
		this.pontoReferencia = pontoReferencia;
//        this.getPropertyChangeSupport().firePropertyChange ("pontoReferencia", pontoReferenciaOld, pontoReferencia);
	}



	/**
	 * Return the value associated with the column: fora_area
	 */
	public java.lang.Long getForaArea () {
		return getPropertyValue(this, foraArea, PROP_FORA_AREA); 
	}

	/**
	 * Set the value related to the column: fora_area
	 * @param foraArea the fora_area value
	 */
	public void setForaArea (java.lang.Long foraArea) {
//        java.lang.Long foraAreaOld = this.foraArea;
		this.foraArea = foraArea;
//        this.getPropertyChangeSupport().firePropertyChange ("foraArea", foraAreaOld, foraArea);
	}



	/**
	 * Return the value associated with the column: cd_visita_domiciliar
	 */
	public br.com.ksisolucoes.vo.cadsus.VisitaDomiciliar getVisitaDomiciliar () {
		return getPropertyValue(this, visitaDomiciliar, PROP_VISITA_DOMICILIAR); 
	}

	/**
	 * Set the value related to the column: cd_visita_domiciliar
	 * @param visitaDomiciliar the cd_visita_domiciliar value
	 */
	public void setVisitaDomiciliar (br.com.ksisolucoes.vo.cadsus.VisitaDomiciliar visitaDomiciliar) {
//        br.com.ksisolucoes.vo.cadsus.VisitaDomiciliar visitaDomiciliarOld = this.visitaDomiciliar;
		this.visitaDomiciliar = visitaDomiciliar;
//        this.getPropertyChangeSupport().firePropertyChange ("visitaDomiciliar", visitaDomiciliarOld, visitaDomiciliar);
	}



	/**
	 * Return the value associated with the column: cd_eqp_micro_area
	 */
	public br.com.ksisolucoes.vo.basico.EquipeMicroArea getEquipeMicroArea () {
		return getPropertyValue(this, equipeMicroArea, PROP_EQUIPE_MICRO_AREA); 
	}

	/**
	 * Set the value related to the column: cd_eqp_micro_area
	 * @param equipeMicroArea the cd_eqp_micro_area value
	 */
	public void setEquipeMicroArea (br.com.ksisolucoes.vo.basico.EquipeMicroArea equipeMicroArea) {
//        br.com.ksisolucoes.vo.basico.EquipeMicroArea equipeMicroAreaOld = this.equipeMicroArea;
		this.equipeMicroArea = equipeMicroArea;
//        this.getPropertyChangeSupport().firePropertyChange ("equipeMicroArea", equipeMicroAreaOld, equipeMicroArea);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.cadsus.VisitaTerritorial)) return false;
		else {
			br.com.ksisolucoes.vo.cadsus.VisitaTerritorial visitaTerritorial = (br.com.ksisolucoes.vo.cadsus.VisitaTerritorial) obj;
			if (null == this.getCodigo() || null == visitaTerritorial.getCodigo()) return false;
			else return (this.getCodigo().equals(visitaTerritorial.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}