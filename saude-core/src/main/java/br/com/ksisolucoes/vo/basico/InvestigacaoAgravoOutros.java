package br.com.ksisolucoes.vo.basico;

import br.com.ksisolucoes.vo.basico.base.BaseInvestigacaoAgravoOutros;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;



public class InvestigacaoAgravoOutros extends BaseInvestigacaoAgravoOutros implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public InvestigacaoAgravoOutros () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public InvestigacaoAgravoOutros (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public InvestigacaoAgravoOutros (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.InvestigacaoAgravo investigacaoAgravo) {

		super (
			codigo,
			investigacaoAgravo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}