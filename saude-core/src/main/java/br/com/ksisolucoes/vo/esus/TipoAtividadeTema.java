package br.com.ksisolucoes.vo.esus;

import br.com.celk.integracao.IntegracaoRest;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.esus.base.BaseTipoAtividadeTema;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

@IntegracaoRest
public class TipoAtividadeTema extends BaseTipoAtividadeTema implements CodigoManager {

    private static final long serialVersionUID = 1L;

    public static final List<Long> LST_PNCT = Arrays.asList(CodigoESUSPraticasSaude.PNCT1.value(), CodigoESUSPraticasSaude.PNCT2.value(), CodigoESUSPraticasSaude.PNCT3.value(), CodigoESUSPraticasSaude.PNCT4.value());

    public static enum Tipo implements IEnum<Tipo> {

        REUNIAO(1L, Bundle.getStringApplication("rotulo_reuniao")),
        TEMA_SAUDE(2L, Bundle.getStringApplication("rotulo_tema_saude")),
        PRATICA_SAUDE(3L, Bundle.getStringApplication("rotulo_pratica_saude"));

        private Long value;
        private String descricao;

        private Tipo(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Tipo valeuOf(Long value) {
            for (Tipo tipo : Tipo.values()) {
                if (tipo.value().equals(value)) {
                    return tipo;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    public static enum CodigoESUS implements IEnum {

        QUESTOES_ADMINISTRATIVAS_FUNCIONAMENTO(1L, Bundle.getStringApplication("rotulo_questoes_administrativas_funcionamento")),
        PROCESSO_TRABALHO(2L, Bundle.getStringApplication("rotulo_processo_trabalho")),
        DIAGNOSTICO_MONITORAMENTO_TERRITORIO(3L, Bundle.getStringApplication("rotulo_diagnostico_monitoramento_territorio")),
        PLANEJAMENTO_MONITORAMENTO_ACOES_EQUIPE(4L, Bundle.getStringApplication("rotulo_planejamento_monitoramento_acoes_equipe")),
        DISCUSSAO_CASO_PROJETO_TERAPEUTICO_SINGULAR(5L, Bundle.getStringApplication("rotulo_discussao_caso_projeto_terapeutico_singular")),
        EDUCACAO_PERMANENTE(6L, Bundle.getStringApplication("rotulo_educacao_permanente")),
        OUTROS(7L, Bundle.getStringApplication("rotulo_outros"));

        private CodigoESUS(Long codigo, String descricao) {
            this.codigo = codigo;
            this.descricao = descricao;
        }

        public static CodigoESUS valueOf(Long value) {
            for (CodigoESUS codigoEsus : CodigoESUS.values()) {
                if (codigoEsus.value().equals(value)) {
                    return codigoEsus;
                }
            }
            return null;
        }

        private Long codigo;
        private String descricao;

        @Override
        public Long value() {
            return this.codigo;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return this.descricao;
        }
    }

    public static enum CodigoESUSPraticasSaude implements IEnum {
        APLICACAO_TOPICA_FLUOR(2L, Bundle.getStringApplication("rotulo_aplicacao_topica_fluor")),
        SAUDE_OCULAR(3L, Bundle.getStringApplication("rotulo_saude_ocular")),
        ESCOVACAO_DENTARIA_SUPERVISIONADA(9L, Bundle.getStringApplication("rotulo_escovacao_dentaria_supervisionada")),
        PRATICAS_CORPORAIS_ATIVIDADE_FISICA(11L, Bundle.getStringApplication("rotulo_praticas_corporais_atividade_fisica")),
        OUTRAS(12L, Bundle.getStringApplication("rotulo_outras")),
        ANTROPOMETRIA(20L, Bundle.getStringApplication("rotulo_antropometria")),
        SAUDE_AUDITIVA(22L, Bundle.getStringApplication("rotulo_saude_auditiva")),
        DESENVOLVIMENTO_LINGUAGEM(23L, Bundle.getStringApplication("rotulo_desenvolvimento_linguagem")),
        VERIFICACAO_SITUACAO_VACINAL(24L, Bundle.getStringApplication("rotulo_verificacao_situacao_vacinal")),
        PNCT1(25L, Bundle.getStringApplication("rotulo_pnct1")),
        PNCT2(26L, Bundle.getStringApplication("rotulo_pnct2")),
        PNCT3(27L, Bundle.getStringApplication("rotulo_pnct3")),
        PNCT4(28L, Bundle.getStringApplication("rotulo_pnct4")),
        OUTRO_PROCEDIMENTO_COLETIVO(30L, Bundle.getStringApplication("rotulo_outro_procedimento_coletivo")),;

        private CodigoESUSPraticasSaude(Long codigo, String descricao) {
            this.codigo = codigo;
            this.descricao = descricao;
        }

        public static CodigoESUSPraticasSaude valeuOf(Long value) {
            for (CodigoESUSPraticasSaude codigoEsus : CodigoESUSPraticasSaude.values()) {
                if (codigoEsus.value().equals(value)) {
                    return codigoEsus;
                }
            }
            return null;
        }

        private Long codigo;
        private String descricao;

        @Override
        public Long value() {
            return this.codigo;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return this.descricao;
        }
    }

    public static enum CodigoESUSTemasSaude implements IEnum {

        ALIMENTACAO_SAUDAVEL(1L, Bundle.getStringApplication("rotulo_alimentacao_saudavel")),
        AUTOCUIDADO_PESSOAS_DOENCA_CRONICA(4L, Bundle.getStringApplication("rotulo_autocuidado_pessoas_doenca_cronica")),
        CIDADANIA_DIREITOS_HUMANOS(5L, Bundle.getStringApplication("rotulo_cidadania_direitos_humanos")),
        SAUDE_TRABALHADOR(6L, Bundle.getStringApplication("rotulo_saude_trabalhador")),
        DEPENDECIA_QUIMICA_TABACO_ALCOOL_OUTRAS_DROGAS(7L, Bundle.getStringApplication("rotulo_dependecia_quimica_tabaco_alcool_outras_drogas")),
        ENVELHECIMENTO_CLIMATERIO_ANDROPAUSA_ETC(8L, Bundle.getStringApplication("rotulo_envelhecimento_climaterio_andropausa_etc")),
        PLANTA_MEDICINAIS_FITOTERAPIA(10L, Bundle.getStringApplication("rotulo_planta_medicinais_fitoterapia")),
        PREVENCAO_VIOLENCIA_PROMOCAO_CULTURA_PAZ(13L, Bundle.getStringApplication("rotulo_prevencao_violencia_promocao_cultura_paz")),
        SAUDE_AMBIENTAL(14L, Bundle.getStringApplication("rotulo_saude_ambiental")),
        SAUDE_BUCAL(15L, Bundle.getStringApplication("rotulo_saude_bucal")),
        SAUDE_MENTAL(16L, Bundle.getStringApplication("rotulo_saude_mental")),
        SAUDE_SEXUAL_REPRODUTIVA(17L, Bundle.getStringApplication("rotulo_saude_sexual_reprodutiva")),
        SEMANA_SAUDE_ESCOLA(18L, Bundle.getStringApplication("rotulo_semana_saude_escola")),
        AGRAVO_NEGLIGENCIADO(19L, Bundle.getStringApplication("rotulo_agravo_negligenciado")),
        OUTROS(21L, Bundle.getStringApplication("rotulo_outros")),
        ACOES_COMBATE_AEDES_AEGYPTI(29L, Bundle.getStringApplication("rotulo_acoes_combate_aedes_aegypti")),;

        private CodigoESUSTemasSaude(Long codigo, String descricao) {
            this.codigo = codigo;
            this.descricao = descricao;
        }

        public static CodigoESUSTemasSaude valeuOf(Long value) {
            for (CodigoESUSTemasSaude codigoEsus : CodigoESUSTemasSaude.values()) {
                if (codigoEsus.value().equals(value)) {
                    return codigoEsus;
                }
            }
            return null;
        }

        private Long codigo;
        private String descricao;

        @Override
        public Long value() {
            return this.codigo;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return this.descricao;
        }
    }

    public String getCodigoEsusTemaSaudeDescricao() {
        if (getCodigoEsus() == null) return null;
        return CodigoESUSTemasSaude.valeuOf(getCodigoEsus()).descricao();
    }

    public String getCodigoEsusPraticaSaudeDescricao() {
        return CodigoESUSPraticasSaude.valeuOf(getCodigoEsus()).descricao();
    }

    public String getDescricaoTipo() {
        Tipo tipo = Tipo.valeuOf(getTipo());
        if (tipo != null) {
            return tipo.descricao();
        }
        return null;
    }

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public TipoAtividadeTema () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public TipoAtividadeTema (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public TipoAtividadeTema (
		java.lang.Long codigo,
		java.lang.String descricaoTemaAtividade) {

		super (
			codigo,
			descricaoTemaAtividade);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}
