<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.cadsus"  >
    <class name="UsuarioCadsusExclusao" table="usuario_cadsus_exclusao" >
        <id
            column="cd_usuario_cadsus_exclusao"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />
 
        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"
            column="cd_usuario_cadsus"
            name="usuarioCadsus"
            not-null="true"
        />

        <property
            column="status"
            name="status"
            not-null="true"
            type="java.lang.Long"
        />
        
        <property
            column="dt_cadastro"
            name="dataCadastro"
            not-null="true"
            type="timestamp"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuario"
            not-null="true"
        />
        
        <property
            column="dt_confirmacao"
            name="dataConfirmacao"
            not-null="false"
            type="timestamp"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario_confirmacao"
            name="usuarioConfirmacao"
            not-null="false"
        />
        
        <property
            column="dt_reversao"
            name="dataReversao"
            not-null="false"
            type="timestamp"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario_reversao"
            name="usuarioReversao"
            not-null="false"
        />
        
        <property
            column="dt_unificacao"
            name="dataUnificacao"
            not-null="false"
            type="timestamp"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario_unificacao"
            name="usuarioUnificacao"
            not-null="false"
        />
        
        <property
            column="dt_cancelamento"
            name="dataCancelamento"
            not-null="false"
            type="timestamp"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario_cancelamento"
            name="usuarioCancelamento"
            not-null="false"
        />
        
        <property 
            name="motivoReversao"
            column="motivo_reversao"
            type="java.lang.String"
            not-null="false"
            length="500"
         />
        
        <property
            column="situacao_paciente"
            name="situacaoPaciente"
            not-null="true"
            type="java.lang.Long"
        />
        
        <property
            column="mot_exclusao_paciente"
            name="motivoExclusaoPaciente"
            not-null="true"
            type="java.lang.Long"
        />
        
    </class>
</hibernate-mapping>
