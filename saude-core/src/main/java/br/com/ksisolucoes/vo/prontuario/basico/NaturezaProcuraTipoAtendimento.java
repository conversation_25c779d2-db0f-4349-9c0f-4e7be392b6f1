package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.parametrogem.ParametroPanelConsultaBean;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseNaturezaProcuraTipoAtendimento;

import java.io.Serializable;


@ParametroPanelConsultaBean("br.com.celk.view.unidadesaude.naturezaprocuratipo.autocomplete.AutoCompleteConsultaNaturezaProcuraTipoAtendimento")
public class NaturezaProcuraTipoAtendimento extends BaseNaturezaProcuraTipoAtendimento implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

        public static final String PROP_NATUREZA_TIPO_ATENDIMENTO_DESCRICAO = "naturezaTipoAtendimentoDescricao";
        public static final String PROP_VISIVEL_FORMATADO = "visivelFormatado";

        public static final String TIPO_VAGA_SEMANAL = "S";
        public static final String TIPO_VAGA_MENSAL = "M";

/*[CONSTRUCTOR MARKER BEGIN]*/
	public NaturezaProcuraTipoAtendimento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public NaturezaProcuraTipoAtendimento (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public NaturezaProcuraTipoAtendimento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcura naturezaProcura,
		br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento tipoAtendimento,
		java.lang.Long imprimeFichaPaciente) {

		super (
			codigo,
			naturezaProcura,
			tipoAtendimento,
			imprimeFichaPaciente);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (Long) key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getNaturezaTipoAtendimentoDescricao(){
        return getNaturezaProcura().getDescricao()+" / "+getTipoAtendimento().getDescricao();
    }

	@Override
	public String getDescricaoVO() {
		return getNaturezaTipoAtendimentoDescricao();
	}

	@Override
	public String getIdentificador() {
		return Coalesce.asString(getCodigo());
	}
}