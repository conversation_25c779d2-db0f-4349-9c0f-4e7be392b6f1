package br.com.ksisolucoes.vo.entradas.dispensacao.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the dispensacao_medicamento_item table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="dispensacao_medicamento_item"
 */

public abstract class BaseDispensacaoMedicamentoItem extends BaseRootVO implements Serializable {

	public static String REF = "DispensacaoMedicamentoItem";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_USUARIO_CADSUS_KIT = "usuarioCadsusKit";
	public static final String PROP_PRECO_UNITARIO = "precoUnitario";
	public static final String PROP_DATA_ULTIMA_DISPENSACAO = "dataUltimaDispensacao";
	public static final String PROP_DATA_INTEGRACAO_INOVAMFRI = "dataIntegracaoInovamfri";
	public static final String PROP_ITEM = "item";
	public static final String PROP_RECEITUARIO_ITEM_COMPONENTE = "receituarioItemComponente";
	public static final String PROP_QUANTIDADE_DISPENSADA = "quantidadeDispensada";
	public static final String PROP_CID = "cid";
	public static final String PROP_PRODUTO = "produto";
	public static final String PROP_RECEITUARIO_ITEM = "receituarioItem";
	public static final String PROP_PRECO_MEDIO = "precoMedio";
	public static final String PROP_USUARIO_CADSUS_DESTINO = "usuarioCadsusDestino";
	public static final String PROP_PRECO_CUSTO = "precoCusto";
	public static final String PROP_DATA_PROXIMA_DISPENSACAO = "dataProximaDispensacao";
	public static final String PROP_DISPENSACAO_MEDICAMENTO = "dispensacaoMedicamento";
	public static final String PROP_JUSTIFICATIVA = "justificativa";
	public static final String PROP_QUANTIDADE_DEVOLVIDA = "quantidadeDevolvida";
	public static final String PROP_ULTIMO_PRECO = "ultimoPreco";
	public static final String PROP_STATUS = "status";
	public static final String PROP_QUANTIDADE_PRESCRITA = "quantidadePrescrita";
	public static final String PROP_TIPO = "tipo";
	public static final String PROP_QUANTIDADE_DISPENSADA_ORIGINAL = "quantidadeDispensadaOriginal";
	public static final String PROP_POSOLOGIA = "posologia";
	public static final String PROP_TIPO_USO = "tipoUso";
	public static final String PROP_DATA_VALIDADE_RECEITA = "dataValidadeReceita";


	// constructors
	public BaseDispensacaoMedicamentoItem () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseDispensacaoMedicamentoItem (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseDispensacaoMedicamentoItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.entradas.estoque.Produto produto,
		java.lang.Double quantidadePrescrita,
		java.lang.Double quantidadeDispensada,
		java.lang.Double quantidadeDispensadaOriginal,
		java.lang.Long status,
		java.util.Date dataValidadeReceita) {

		this.setCodigo(codigo);
		this.setProduto(produto);
		this.setQuantidadePrescrita(quantidadePrescrita);
		this.setQuantidadeDispensada(quantidadeDispensada);
		this.setQuantidadeDispensadaOriginal(quantidadeDispensadaOriginal);
		this.setStatus(status);
		this.setDataValidadeReceita(dataValidadeReceita);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long item;
	private java.lang.Double posologia;
	private java.lang.Double quantidadePrescrita;
	private java.lang.Double quantidadeDispensada;
	private java.lang.Double quantidadeDispensadaOriginal;
	private java.lang.Double precoCusto;
	private java.lang.Double precoMedio;
	private java.lang.Long status;
	private java.util.Date dataProximaDispensacao;
	private java.util.Date dataValidadeReceita;
	private java.lang.String justificativa;
	private java.util.Date dataUltimaDispensacao;
	private java.lang.Long tipoUso;
	private java.lang.Double ultimoPreco;
	private java.lang.Double quantidadeDevolvida;
	private java.lang.Double precoUnitario;
	private java.lang.Long tipo;
	private java.util.Date dataIntegracaoInovamfri;

	// many to one
	private br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento dispensacaoMedicamento;
	private br.com.ksisolucoes.vo.entradas.estoque.Produto produto;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusDestino;
	private br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem receituarioItem;
	private br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItemComponente receituarioItemComponente;
	private br.com.ksisolucoes.vo.entradas.estoque.UsuarioCadsusKit usuarioCadsusKit;
	private br.com.ksisolucoes.vo.prontuario.basico.Cid cid;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_dis_med_item"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: item
	 */
	public java.lang.Long getItem () {
		return getPropertyValue(this, item, PROP_ITEM); 
	}

	/**
	 * Set the value related to the column: item
	 * @param item the item value
	 */
	public void setItem (java.lang.Long item) {
		this.item = item;
	}



	/**
	 * Return the value associated with the column: posologia
	 */
	public java.lang.Double getPosologia () {
		return getPropertyValue(this, posologia, PROP_POSOLOGIA); 
	}

	/**
	 * Set the value related to the column: posologia
	 * @param posologia the posologia value
	 */
	public void setPosologia (java.lang.Double posologia) {
		this.posologia = posologia;
	}



	/**
	 * Return the value associated with the column: quantidade_prescrita
	 */
	public java.lang.Double getQuantidadePrescrita () {
		return getPropertyValue(this, quantidadePrescrita, PROP_QUANTIDADE_PRESCRITA); 
	}

	/**
	 * Set the value related to the column: quantidade_prescrita
	 * @param quantidadePrescrita the quantidade_prescrita value
	 */
	public void setQuantidadePrescrita (java.lang.Double quantidadePrescrita) {
		this.quantidadePrescrita = quantidadePrescrita;
	}



	/**
	 * Return the value associated with the column: quantidade_dispensada
	 */
	public java.lang.Double getQuantidadeDispensada () {
		return getPropertyValue(this, quantidadeDispensada, PROP_QUANTIDADE_DISPENSADA); 
	}

	/**
	 * Set the value related to the column: quantidade_dispensada
	 * @param quantidadeDispensada the quantidade_dispensada value
	 */
	public void setQuantidadeDispensada (java.lang.Double quantidadeDispensada) {
		this.quantidadeDispensada = quantidadeDispensada;
	}



	/**
	 * Return the value associated with the column: quantidade_dispensada
	 */
	public java.lang.Double getQuantidadeDispensadaOriginal () {
		return getPropertyValue(this, quantidadeDispensadaOriginal, PROP_QUANTIDADE_DISPENSADA_ORIGINAL); 
	}

	/**
	 * Set the value related to the column: quantidade_dispensada
	 * @param quantidadeDispensadaOriginal the quantidade_dispensada value
	 */
	public void setQuantidadeDispensadaOriginal (java.lang.Double quantidadeDispensadaOriginal) {
		this.quantidadeDispensadaOriginal = quantidadeDispensadaOriginal;
	}



	/**
	 * Return the value associated with the column: preco_custo
	 */
	public java.lang.Double getPrecoCusto () {
		return getPropertyValue(this, precoCusto, PROP_PRECO_CUSTO); 
	}

	/**
	 * Set the value related to the column: preco_custo
	 * @param precoCusto the preco_custo value
	 */
	public void setPrecoCusto (java.lang.Double precoCusto) {
		this.precoCusto = precoCusto;
	}



	/**
	 * Return the value associated with the column: preco_medio
	 */
	public java.lang.Double getPrecoMedio () {
		return getPropertyValue(this, precoMedio, PROP_PRECO_MEDIO); 
	}

	/**
	 * Set the value related to the column: preco_medio
	 * @param precoMedio the preco_medio value
	 */
	public void setPrecoMedio (java.lang.Double precoMedio) {
		this.precoMedio = precoMedio;
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
		this.status = status;
	}



	/**
	 * Return the value associated with the column: dt_prox_dispensacao
	 */
	public java.util.Date getDataProximaDispensacao () {
		return getPropertyValue(this, dataProximaDispensacao, PROP_DATA_PROXIMA_DISPENSACAO); 
	}

	/**
	 * Set the value related to the column: dt_prox_dispensacao
	 * @param dataProximaDispensacao the dt_prox_dispensacao value
	 */
	public void setDataProximaDispensacao (java.util.Date dataProximaDispensacao) {
		this.dataProximaDispensacao = dataProximaDispensacao;
	}



	/**
	 * Return the value associated with the column: dt_validade_receita
	 */
	public java.util.Date getDataValidadeReceita () {
		return getPropertyValue(this, dataValidadeReceita, PROP_DATA_VALIDADE_RECEITA); 
	}

	/**
	 * Set the value related to the column: dt_validade_receita
	 * @param dataValidadeReceita the dt_validade_receita value
	 */
	public void setDataValidadeReceita (java.util.Date dataValidadeReceita) {
		this.dataValidadeReceita = dataValidadeReceita;
	}



	/**
	 * Return the value associated with the column: justificativa
	 */
	public java.lang.String getJustificativa () {
		return getPropertyValue(this, justificativa, PROP_JUSTIFICATIVA); 
	}

	/**
	 * Set the value related to the column: justificativa
	 * @param justificativa the justificativa value
	 */
	public void setJustificativa (java.lang.String justificativa) {
		this.justificativa = justificativa;
	}



	/**
	 * Return the value associated with the column: dt_ultima_dispensacao
	 */
	public java.util.Date getDataUltimaDispensacao () {
		return getPropertyValue(this, dataUltimaDispensacao, PROP_DATA_ULTIMA_DISPENSACAO); 
	}

	/**
	 * Set the value related to the column: dt_ultima_dispensacao
	 * @param dataUltimaDispensacao the dt_ultima_dispensacao value
	 */
	public void setDataUltimaDispensacao (java.util.Date dataUltimaDispensacao) {
		this.dataUltimaDispensacao = dataUltimaDispensacao;
	}



	/**
	 * Return the value associated with the column: tp_uso
	 */
	public java.lang.Long getTipoUso () {
		return getPropertyValue(this, tipoUso, PROP_TIPO_USO); 
	}

	/**
	 * Set the value related to the column: tp_uso
	 * @param tipoUso the tp_uso value
	 */
	public void setTipoUso (java.lang.Long tipoUso) {
		this.tipoUso = tipoUso;
	}



	/**
	 * Return the value associated with the column: ultimo_preco
	 */
	public java.lang.Double getUltimoPreco () {
		return getPropertyValue(this, ultimoPreco, PROP_ULTIMO_PRECO); 
	}

	/**
	 * Set the value related to the column: ultimo_preco
	 * @param ultimoPreco the ultimo_preco value
	 */
	public void setUltimoPreco (java.lang.Double ultimoPreco) {
		this.ultimoPreco = ultimoPreco;
	}



	/**
	 * Return the value associated with the column: quantidade_devolvida
	 */
	public java.lang.Double getQuantidadeDevolvida () {
		return getPropertyValue(this, quantidadeDevolvida, PROP_QUANTIDADE_DEVOLVIDA); 
	}

	/**
	 * Set the value related to the column: quantidade_devolvida
	 * @param quantidadeDevolvida the quantidade_devolvida value
	 */
	public void setQuantidadeDevolvida (java.lang.Double quantidadeDevolvida) {
		this.quantidadeDevolvida = quantidadeDevolvida;
	}



	/**
	 * Return the value associated with the column: preco_unitario
	 */
	public java.lang.Double getPrecoUnitario () {
		return getPropertyValue(this, precoUnitario, PROP_PRECO_UNITARIO); 
	}

	/**
	 * Set the value related to the column: preco_unitario
	 * @param precoUnitario the preco_unitario value
	 */
	public void setPrecoUnitario (java.lang.Double precoUnitario) {
		this.precoUnitario = precoUnitario;
	}



	/**
	 * Return the value associated with the column: tipo
	 */
	public java.lang.Long getTipo () {
		return getPropertyValue(this, tipo, PROP_TIPO); 
	}

	/**
	 * Set the value related to the column: tipo
	 * @param tipo the tipo value
	 */
	public void setTipo (java.lang.Long tipo) {
		this.tipo = tipo;
	}



	/**
	 * Return the value associated with the column: dt_integracao_inovamfri
	 */
	public java.util.Date getDataIntegracaoInovamfri () {
		return getPropertyValue(this, dataIntegracaoInovamfri, PROP_DATA_INTEGRACAO_INOVAMFRI); 
	}

	/**
	 * Set the value related to the column: dt_integracao_inovamfri
	 * @param dataIntegracaoInovamfri the dt_integracao_inovamfri value
	 */
	public void setDataIntegracaoInovamfri (java.util.Date dataIntegracaoInovamfri) {
		this.dataIntegracaoInovamfri = dataIntegracaoInovamfri;
	}



	/**
	 * Return the value associated with the column: nr_dispensacao
	 */
	public br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento getDispensacaoMedicamento () {
		return getPropertyValue(this, dispensacaoMedicamento, PROP_DISPENSACAO_MEDICAMENTO); 
	}

	/**
	 * Set the value related to the column: nr_dispensacao
	 * @param dispensacaoMedicamento the nr_dispensacao value
	 */
	public void setDispensacaoMedicamento (br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento dispensacaoMedicamento) {
		this.dispensacaoMedicamento = dispensacaoMedicamento;
	}



	/**
	 * Return the value associated with the column: cod_pro
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Produto getProduto () {
		return getPropertyValue(this, produto, PROP_PRODUTO); 
	}

	/**
	 * Set the value related to the column: cod_pro
	 * @param produto the cod_pro value
	 */
	public void setProduto (br.com.ksisolucoes.vo.entradas.estoque.Produto produto) {
		this.produto = produto;
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus_destino
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsusDestino () {
		return getPropertyValue(this, usuarioCadsusDestino, PROP_USUARIO_CADSUS_DESTINO); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus_destino
	 * @param usuarioCadsusDestino the cd_usu_cadsus_destino value
	 */
	public void setUsuarioCadsusDestino (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusDestino) {
		this.usuarioCadsusDestino = usuarioCadsusDestino;
	}



	/**
	 * Return the value associated with the column: cd_receiturario_item
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem getReceituarioItem () {
		return getPropertyValue(this, receituarioItem, PROP_RECEITUARIO_ITEM); 
	}

	/**
	 * Set the value related to the column: cd_receiturario_item
	 * @param receituarioItem the cd_receiturario_item value
	 */
	public void setReceituarioItem (br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem receituarioItem) {
		this.receituarioItem = receituarioItem;
	}



	/**
	 * Return the value associated with the column: cd_receituario_componente
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItemComponente getReceituarioItemComponente () {
		return getPropertyValue(this, receituarioItemComponente, PROP_RECEITUARIO_ITEM_COMPONENTE); 
	}

	/**
	 * Set the value related to the column: cd_receituario_componente
	 * @param receituarioItemComponente the cd_receituario_componente value
	 */
	public void setReceituarioItemComponente (br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItemComponente receituarioItemComponente) {
		this.receituarioItemComponente = receituarioItemComponente;
	}



	/**
	 * Return the value associated with the column: cd_usuario_cadsus_kit
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.UsuarioCadsusKit getUsuarioCadsusKit () {
		return getPropertyValue(this, usuarioCadsusKit, PROP_USUARIO_CADSUS_KIT); 
	}

	/**
	 * Set the value related to the column: cd_usuario_cadsus_kit
	 * @param usuarioCadsusKit the cd_usuario_cadsus_kit value
	 */
	public void setUsuarioCadsusKit (br.com.ksisolucoes.vo.entradas.estoque.UsuarioCadsusKit usuarioCadsusKit) {
		this.usuarioCadsusKit = usuarioCadsusKit;
	}



	/**
	 * Return the value associated with the column: cd_cid
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Cid getCid () {
		return getPropertyValue(this, cid, PROP_CID); 
	}

	/**
	 * Set the value related to the column: cd_cid
	 * @param cid the cd_cid value
	 */
	public void setCid (br.com.ksisolucoes.vo.prontuario.basico.Cid cid) {
		this.cid = cid;
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem)) return false;
		else {
			br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem dispensacaoMedicamentoItem = (br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem) obj;
			if (null == this.getCodigo() || null == dispensacaoMedicamentoItem.getCodigo()) return false;
			else return (this.getCodigo().equals(dispensacaoMedicamentoItem.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

}