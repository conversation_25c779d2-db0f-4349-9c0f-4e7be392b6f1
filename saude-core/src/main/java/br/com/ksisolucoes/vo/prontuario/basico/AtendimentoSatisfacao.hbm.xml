<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="AtendimentoSatisfacao" table="atendimento_satisfacao" >

        <id
            name="codigo"
            column="cd_atendimento_satisfacao"
            type="java.lang.Long"
        >
            <generator class="sequence">
                <param name="sequence">seq_atendimento_satisfacao</param>
            </generator>
        </id>
         
        <version column="version" name="version" type="long" />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
                name="atendimentoPrincipal"
                column="nr_atendimento_principal"
        />


        
    </class>
</hibernate-mapping>
