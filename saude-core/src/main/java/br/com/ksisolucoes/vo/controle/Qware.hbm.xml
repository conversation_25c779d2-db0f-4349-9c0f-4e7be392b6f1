<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.controle">
    <class
            name="Qware"
            table="qware"
    >
        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_qware"
        >
            <generator class="sequence">
                <param name="sequence">seq_qware</param>
            </generator>
        </id>
        <version column="version" name="version" type="long"/>
        
        <property
                column="cd_familia"
                name="codigoFamiliar"
                not-null="false"
                type="java.lang.Long"
       />
        
        
        <property
                column="nis"
                name="nis"
                length="20"
                type="java.lang.String"
        />
        
        <property
                column="nm_usuario"
                name="nomeIntegrante"
                length="70"
                not-null="false"
                type="java.lang.String"
        />
        
        <property
                column="dt_ultima_alteracao_pessoa"
                length="4"
                name="dataUltimaAlteracaoPessoa"
                not-null="false"
                type="date"
        />
        
       <property
                column="dt_nascimento"
                length="4"
                name="dataNascimento"
                not-null="false"
                type="date"
        />
        
       <property
                column="sg_sexo"
                length="1"
                name="sexo"
                not-null="false"
                type="java.lang.Long"
        />
        
        
       <property
                column="nm_mae"
                length="70"
                name="nomeMae"
                not-null="false"
                type="java.lang.String"
        />
        
        <property
                column="cpf"
                length="14"
                name="cpf"
                not-null="false"
                type="java.lang.String"
        />
        
        
     	<many-to-one
                class="br.com.ksisolucoes.vo.cadsus.Raca"
                column="cd_raca"
                name="raca"
                not-null="false"
        />
        
        
        <property
                column="inep"
                length="30"
                name="inep"
                not-null="false"
                type="java.lang.String"
        />
        
        <property
                column="num_serie_escolar"
                length="30"
                name="numeroSerieEscolar"
                not-null="false"
                type="java.lang.String"
        />
        
        <property
                column="in_pa"
                length="30"
                name="inPA"
                not-null="false"
                type="java.lang.String"
        />
        
        <property
                column="tipo_integrante"
                length="30"
                name="tipoIntegrante"
                not-null="false"
                type="java.lang.Long"
        />
        
        <many-to-one
                class="br.com.ksisolucoes.vo.controle.ArquivoQware"
                column="cd_arquivo_qware"
                name="arquivoQware"
                not-null="false"
        />
        
       <property
                column="ocorrencia"
                name="ocorrencia"
                not-null="false"
                type="java.lang.Long"
        />
        
        <property
                column="descricao_ocorrencia"
                length="70"
                name="descricaoOcorrencia"
                not-null="false"
                type="java.lang.String"
        />
        
    </class>
</hibernate-mapping>