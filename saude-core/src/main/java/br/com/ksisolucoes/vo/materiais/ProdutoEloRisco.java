package br.com.ksisolucoes.vo.materiais;

import java.io.Serializable;

import br.com.ksisolucoes.vo.materiais.base.BaseProdutoEloRisco;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class ProdutoEloRisco extends BaseProdutoEloRisco implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ProdutoEloRisco () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ProdutoEloRisco (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}