package br.com.ksisolucoes.vo.prontuario.basico.base;

import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the requisicao_multipatogenos_ist table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="requisicao_multipatogenos_ist"
 */

public abstract class BaseRequisicaoMultipatogenosIST   extends BaseRootVO implements Serializable  {

	public static String REF = "RequisicaoMultipatogenosIST";
	public static String PROP_MOTIVO_SOLICITACAO = "motivoSolicitacao";
	public static String PROP_CODIGO = "codigo";
	public static String PROP_DATA_CADASTRO = "dataCadastro";
	public static String PROP_MOTIVOS_OUTROS = "motivosOutros";
	public static String PROP_EXAME_REQUISICAO = "exameRequisicao";


	// constructors
	public BaseRequisicaoMultipatogenosIST () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRequisicaoMultipatogenosIST (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRequisicaoMultipatogenosIST (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao) {

		this.setCodigo(codigo);
		this.setExameRequisicao(exameRequisicao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	 long version;

	// fields
	private java.lang.Long motivoSolicitacao;
	private java.lang.String motivosOutros;
	private java.util.Date dataCadastro;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_req_multipatogenos_ist"
     */
	public java.lang.Long getCodigo () {
		return getPropertyValue(this,  codigo, "codigo");
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}


	/**
	 * Return the value associated with the column: motivo_solicitacao
	 */
	public java.lang.Long getMotivoSolicitacao () {
		return getPropertyValue(this, motivoSolicitacao, PROP_MOTIVO_SOLICITACAO);
	}

	/**
	 * Set the value related to the column: motivo_solicitacao
	 * @param motivoSolicitacao the motivo_solicitacao value
	 */
	public void setMotivoSolicitacao (java.lang.Long motivoSolicitacao) {
		this.motivoSolicitacao = motivoSolicitacao;
	}



	/**
	 * Return the value associated with the column: ds_motivos_outros
	 */
	public java.lang.String getMotivosOutros () {
		return getPropertyValue(this, motivosOutros, PROP_MOTIVOS_OUTROS);
	}

	/**
	 * Set the value related to the column: ds_motivos_outros
	 * @param motivosOutros the ds_motivos_outros value
	 */
	public void setMotivosOutros (java.lang.String motivosOutros) {
		this.motivosOutros = motivosOutros;
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO);
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
		this.dataCadastro = dataCadastro;
	}



	/**
	 * Return the value associated with the column: cd_exame_requisicao
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao getExameRequisicao () {
		return getPropertyValue(this, exameRequisicao, PROP_EXAME_REQUISICAO);
	}

	/**
	 * Set the value related to the column: cd_exame_requisicao
	 * @param exameRequisicao the cd_exame_requisicao value
	 */
	public void setExameRequisicao (br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao) {
		this.exameRequisicao = exameRequisicao;
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.RequisicaoMultipatogenosIST)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.RequisicaoMultipatogenosIST requisicaoMultipatogenosIST = (br.com.ksisolucoes.vo.prontuario.basico.RequisicaoMultipatogenosIST) obj;
			if (null == this.getCodigo() || null == requisicaoMultipatogenosIST.getCodigo()) return false;
			else return (this.getCodigo().equals(requisicaoMultipatogenosIST.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}


}