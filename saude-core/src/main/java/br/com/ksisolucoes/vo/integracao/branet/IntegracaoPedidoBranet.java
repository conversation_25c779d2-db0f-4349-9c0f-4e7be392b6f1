package br.com.ksisolucoes.vo.integracao.branet;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.integracao.branet.base.BaseIntegracaoPedidoBranet;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;


public class IntegracaoPedidoBranet extends BaseIntegracaoPedidoBranet implements CodigoManager {
    private static final long serialVersionUID = 1L;

    public static final String RETORNO_SUCESSO = "S";
    public static final String RETORNO_ERRO = "E";
    public static final String RETORNO_CANCELADO = "C";


    public enum StatusPedido implements IEnum {
        PENDENTE(0L, Bundle.getStringApplication("rotulo_pendente")),
        RECEBIDO(1L, Bundle.getStringApplication("rotulo_recebido")),
        CANCELADO(2L, Bundle.getStringApplication("rotulo_cancelado")),
        ;

        private Long value;
        private String descricao;

        private StatusPedido(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static StatusPedido valueOf(Long value) {
            for (StatusPedido status : StatusPedido.values()) {
                if (status.value().equals(value)) {
                    return status;
                }
            }

            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

    public enum Status implements IEnum {
        SUCESSO(0L, Bundle.getStringApplication("rotulo_sucesso")),
        ERRO(1L, Bundle.getStringApplication("rotulo_erro")),
        CANCELADO(2L, Bundle.getStringApplication("rotulo_cancelado")),
        ;

        private Long value;
        private String descricao;

        private Status(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static IntegracaoPedidoBranet.Status valueOf(Long value) {
            for (IntegracaoPedidoBranet.Status status : IntegracaoPedidoBranet.Status.values()) {
                if (status.value().equals(value)) {
                    return status;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }


    /*[CONSTRUCTOR MARKER BEGIN]*/
    public IntegracaoPedidoBranet() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public IntegracaoPedidoBranet(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public IntegracaoPedidoBranet(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferencia pedidoTransferencia,
            java.util.Date data,
            java.util.Date dataCadastro) {

        super(
                codigo,
                pedidoTransferencia,
                data,
                dataCadastro);
    }

    /*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}