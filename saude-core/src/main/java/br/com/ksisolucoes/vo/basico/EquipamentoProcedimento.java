package br.com.ksisolucoes.vo.basico;

import java.io.Serializable;

import br.com.ksisolucoes.vo.basico.base.BaseEquipamentoProcedimento;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class EquipamentoProcedimento extends BaseEquipamentoProcedimento implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EquipamentoProcedimento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EquipamentoProcedimento (br.com.ksisolucoes.vo.basico.EquipamentoProcedimentoPK id) {
		super(id);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setId( (br.com.ksisolucoes.vo.basico.EquipamentoProcedimentoPK)key );
    }

    public Serializable getCodigoManager() {
        return this.getId();
    }
}