package br.com.ksisolucoes.vo.hospital.financeiro;

import java.io.Serializable;

import br.com.ksisolucoes.vo.hospital.financeiro.base.BaseModeloRecibo;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

public class ModeloRecibo extends BaseModeloRecibo implements CodigoManager {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public ModeloRecibo() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public ModeloRecibo(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public ModeloRecibo(
            java.lang.Long codigo,
            java.lang.String descricao,
            java.lang.String modelo) {

        super(
                codigo,
                descricao,
                modelo);
    }

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}
