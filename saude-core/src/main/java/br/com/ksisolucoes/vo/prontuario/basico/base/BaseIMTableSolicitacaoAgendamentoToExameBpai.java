package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the im_solic_agend_to_exame_bpai table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="im_solic_agend_to_exame_bpai"
 */

public abstract class BaseIMTableSolicitacaoAgendamentoToExameBpai extends BaseRootVO implements Serializable {

	public static String REF = "IMTableSolicitacaoAgendamentoToExameBpai";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_EXAME_BPAI = "exameBpai";
	public static final String PROP_SOLICITACAO_AGENDAMENTO = "solicitacaoAgendamento";


	// constructors
	public BaseIMTableSolicitacaoAgendamentoToExameBpai () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseIMTableSolicitacaoAgendamentoToExameBpai (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseIMTableSolicitacaoAgendamentoToExameBpai (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento solicitacaoAgendamento,
		br.com.ksisolucoes.vo.prontuario.basico.ExameBpai exameBpai) {

		this.setCodigo(codigo);
		this.setSolicitacaoAgendamento(solicitacaoAgendamento);
		this.setExameBpai(exameBpai);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento solicitacaoAgendamento;
	private br.com.ksisolucoes.vo.prontuario.basico.ExameBpai exameBpai;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_im"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_solicitacao
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento getSolicitacaoAgendamento () {
		return getPropertyValue(this, solicitacaoAgendamento, PROP_SOLICITACAO_AGENDAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_solicitacao
	 * @param solicitacaoAgendamento the cd_solicitacao value
	 */
	public void setSolicitacaoAgendamento (br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento solicitacaoAgendamento) {
//        br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento solicitacaoAgendamentoOld = this.solicitacaoAgendamento;
		this.solicitacaoAgendamento = solicitacaoAgendamento;
//        this.getPropertyChangeSupport().firePropertyChange ("solicitacaoAgendamento", solicitacaoAgendamentoOld, solicitacaoAgendamento);
	}



	/**
	 * Return the value associated with the column: cd_exame_bpai
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ExameBpai getExameBpai () {
		return getPropertyValue(this, exameBpai, PROP_EXAME_BPAI); 
	}

	/**
	 * Set the value related to the column: cd_exame_bpai
	 * @param exameBpai the cd_exame_bpai value
	 */
	public void setExameBpai (br.com.ksisolucoes.vo.prontuario.basico.ExameBpai exameBpai) {
//        br.com.ksisolucoes.vo.prontuario.basico.ExameBpai exameBpaiOld = this.exameBpai;
		this.exameBpai = exameBpai;
//        this.getPropertyChangeSupport().firePropertyChange ("exameBpai", exameBpaiOld, exameBpai);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.IMTableSolicitacaoAgendamentoToExameBpai)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.IMTableSolicitacaoAgendamentoToExameBpai iMTableSolicitacaoAgendamentoToExameBpai = (br.com.ksisolucoes.vo.prontuario.basico.IMTableSolicitacaoAgendamentoToExameBpai) obj;
			if (null == this.getCodigo() || null == iMTableSolicitacaoAgendamentoToExameBpai.getCodigo()) return false;
			else return (this.getCodigo().equals(iMTableSolicitacaoAgendamentoToExameBpai.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}