<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.cadsus"  >
<class name="TipoDocumentoUsuario" table="tipo_documento_usuario">

	<id 
		name="codigo"
		column="cd_tipo_documento"
		type="java.lang.Long"
	>
		<generator class="assigned" />
	
	</id> <version column="version" name="version" type="long" />
	
	<property 
		name="descricao"
		column="ds_tipo_documento"
		type="java.lang.String"
	/>
	
	<property 
		name="flagOrgaoEmissor"
		column="fl_orgao_emissor"
		type="java.lang.String"
		length="1"
	/>
	
	<property 
		name="flagCodigoUf"
		column="fl_cd_uf"
		type="java.lang.String"
		length="1"
	/>
	
	<property 
		name="flagNomeCartorio"
		column="fl_nm_cartorio"
		type="java.lang.String"
		length="1"
	/>
	
	<property 
		name="flagNumeroLivro"
		column="fl_nr_livro"
		type="java.lang.String"
		length="1"
	/>
	
	<property 
		name="flagNumeroFolha"
		column="fl_nr_folha"
		type="java.lang.String"
		length="1"
	/>
	
	<property 
		name="flagNumeroTermo"
		column="fl_nr_termo"
		type="java.lang.String"
		length="1"
	/>
	
	<property 
		name="flagDataEmissao"
		column="fl_dt_emissao"
		type="java.lang.String"
		length="1"
	/>
	
	<property 
		name="flagDataChegadaBrasil"
		column="fl_dt_chegada_brasil"
		type="java.lang.String"
		length="1"
	/>
	
	<property 
		name="flagDataNaturalizacao"
		column="fl_dt_naturalizacao"
		type="java.lang.String"
		length="1"
	/>
	
	<property 
		name="flagNumeroDocumento"
		column="fl_nr_documento"
		type="java.lang.String"
		length="1"
	/>
	
	<property 
		name="flagNumeroSerie"
		column="fl_nr_serie"
		type="java.lang.String"
		length="1"
	/>
		
	<property 
		name="flagNumeroZonaEleitoral"
		column="fl_nr_zona_eleitoral"
		type="java.lang.String"
		length="1"
	/>
	
	<property 
		name="flagNumeroSecaoEleitoral"
		column="fl_nr_secao_eleitoral"
		type="java.lang.String"
		length="1"
	/>
	
	<property 
		name="flagNumeroPortaria"
		column="fl_nr_portaria"
		type="java.lang.String"
		length="1"
	/>
	
  </class>
    </hibernate-mapping>
