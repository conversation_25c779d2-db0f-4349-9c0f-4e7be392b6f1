<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.atendimento" >

    <class name="RegistroOrientacao" table="registro_orientacao">

        <id
            column="cd_registro_orientacao"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />    
        
        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.Profissional"
            column="cd_profissional"
            name="profissional"
            not-null="true"
        />
		 
        <property 
            name="descricaoOrientado"
            column="ds_orientado"
            type="java.lang.String"
            not-null="true"
            length="100"
         />
        
        <property 
            name="dataOrientacao"
            column="data_orientacao"
            type="java.util.Date"
            not-null="true"
         />
        
        <property 
            name="orientacao"
            column="orientacao"
            type="java.lang.String"
            not-null="true"
            length="1000"
         />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuario"
            not-null="true"
        />

        <property
            column="dt_cadastro"
            name="dataCadastro"
            type="timestamp"
            not-null="true"
        />
		 
    </class>
</hibernate-mapping>
