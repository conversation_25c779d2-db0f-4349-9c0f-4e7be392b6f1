package br.com.ksisolucoes.vo.prontuario.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the evolucao_prontuario table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="evolucao_prontuario"
 */

public abstract class BaseEvolucaoProntuario extends BaseRootVO implements Serializable {

	public static String REF = "EvolucaoProntuario";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_TABELA_CBO = "tabelaCbo";
	public static final String PROP_AVALIACAO = "avaliacao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_ACESSO_COMPARTILHADO = "acessoCompartilhado";
	public static final String PROP_DATA_REGISTRO = "dataRegistro";
	public static final String PROP_DATA_CONCLUSAO = "dataConclusao";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_PROFISSIONAL = "profissional";
	public static final String PROP_CONDUTA = "conduta";
	public static final String PROP_DATA_HISTORICO = "dataHistorico";
	public static final String PROP_MOTIVO_DESTINO_SAIDA = "motivoDestinoSaida";
	public static final String PROP_ATENDIMENTO = "atendimento";
	public static final String PROP_CARATER_ATENDIMENTO = "caraterAtendimento";
	public static final String PROP_TIPO_REGISTRO = "tipoRegistro";


	// constructors
	public BaseEvolucaoProntuario () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEvolucaoProntuario (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseEvolucaoProntuario (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento,
		br.com.ksisolucoes.vo.prontuario.procedimento.caps.MotivoDestinoSaida motivoDestinoSaida,
		java.util.Date dataRegistro,
		java.util.Date dataConclusao) {

		this.setCodigo(codigo);
		this.setAtendimento(atendimento);
		this.setMotivoDestinoSaida(motivoDestinoSaida);
		this.setDataRegistro(dataRegistro);
		this.setDataConclusao(dataConclusao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataHistorico;
	private java.lang.String descricao;
	private java.lang.String avaliacao;
	private java.util.Date dataRegistro;
	private java.lang.Long acessoCompartilhado;
	private java.util.Date dataConclusao;
	private java.lang.Long caraterAtendimento;
	private java.lang.Long tipoRegistro;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissional;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.prontuario.basico.Conduta conduta;
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCbo;
	private br.com.ksisolucoes.vo.prontuario.procedimento.caps.MotivoDestinoSaida motivoDestinoSaida;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_evolucao_prontuario"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_historico
	 */
	public java.util.Date getDataHistorico () {
		return getPropertyValue(this, dataHistorico, PROP_DATA_HISTORICO); 
	}

	/**
	 * Set the value related to the column: dt_historico
	 * @param dataHistorico the dt_historico value
	 */
	public void setDataHistorico (java.util.Date dataHistorico) {
//        java.util.Date dataHistoricoOld = this.dataHistorico;
		this.dataHistorico = dataHistorico;
//        this.getPropertyChangeSupport().firePropertyChange ("dataHistorico", dataHistoricoOld, dataHistorico);
	}



	/**
	 * Return the value associated with the column: ds_prontuario
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_prontuario
	 * @param descricao the ds_prontuario value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: avaliacao
	 */
	public java.lang.String getAvaliacao () {
		return getPropertyValue(this, avaliacao, PROP_AVALIACAO); 
	}

	/**
	 * Set the value related to the column: avaliacao
	 * @param avaliacao the avaliacao value
	 */
	public void setAvaliacao (java.lang.String avaliacao) {
//        java.lang.String avaliacaoOld = this.avaliacao;
		this.avaliacao = avaliacao;
//        this.getPropertyChangeSupport().firePropertyChange ("avaliacao", avaliacaoOld, avaliacao);
	}



	/**
	 * Return the value associated with the column: dt_registro
	 */
	public java.util.Date getDataRegistro () {
		return getPropertyValue(this, dataRegistro, PROP_DATA_REGISTRO); 
	}

	/**
	 * Set the value related to the column: dt_registro
	 * @param dataRegistro the dt_registro value
	 */
	public void setDataRegistro (java.util.Date dataRegistro) {
//        java.util.Date dataRegistroOld = this.dataRegistro;
		this.dataRegistro = dataRegistro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataRegistro", dataRegistroOld, dataRegistro);
	}



	/**
	 * Return the value associated with the column: acesso_compartilhado
	 */
	public java.lang.Long getAcessoCompartilhado () {
		return getPropertyValue(this, acessoCompartilhado, PROP_ACESSO_COMPARTILHADO); 
	}

	/**
	 * Set the value related to the column: acesso_compartilhado
	 * @param acessoCompartilhado the acesso_compartilhado value
	 */
	public void setAcessoCompartilhado (java.lang.Long acessoCompartilhado) {
//        java.lang.Long acessoCompartilhadoOld = this.acessoCompartilhado;
		this.acessoCompartilhado = acessoCompartilhado;
//        this.getPropertyChangeSupport().firePropertyChange ("acessoCompartilhado", acessoCompartilhadoOld, acessoCompartilhado);
	}

	/**
	 * Return the value associated with the column: tipo_registro
	 */
	public java.lang.Long getTipoRegistro () {
		return getPropertyValue(this, tipoRegistro, PROP_TIPO_REGISTRO);
	}

	/**
	 * Set the value related to the column: tipo_registro
	 * @param tipoRegistro the tipo_registro value
	 */
	public void setTipoRegistro (java.lang.Long tipoRegistro) {
//        java.lang.Long tipoRegistroOld = this.tipoRegistro;
		this.tipoRegistro = tipoRegistro;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoRegistro", tipoRegistroOld, tipoRegistro);
	}



	/**
	 * Return the value associated with the column: dt_conclusao
	 */
	public java.util.Date getDataConclusao () {
		return getPropertyValue(this, dataConclusao, PROP_DATA_CONCLUSAO); 
	}

	/**
	 * Set the value related to the column: dt_conclusao
	 * @param dataConclusao the dt_conclusao value
	 */
	public void setDataConclusao (java.util.Date dataConclusao) {
//        java.util.Date dataConclusaoOld = this.dataConclusao;
		this.dataConclusao = dataConclusao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataConclusao", dataConclusaoOld, dataConclusao);
	}



	/**
	 * Return the value associated with the column: carater_atendimento
	 */
	public java.lang.Long getCaraterAtendimento () {
		return getPropertyValue(this, caraterAtendimento, PROP_CARATER_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: carater_atendimento
	 * @param caraterAtendimento the carater_atendimento value
	 */
	public void setCaraterAtendimento (java.lang.Long caraterAtendimento) {
//        java.lang.Long caraterAtendimentoOld = this.caraterAtendimento;
		this.caraterAtendimento = caraterAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("caraterAtendimento", caraterAtendimentoOld, caraterAtendimento);
	}



	/**
	 * Return the value associated with the column: nr_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimento () {
		return getPropertyValue(this, atendimento, PROP_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: nr_atendimento
	 * @param atendimento the nr_atendimento value
	 */
	public void setAtendimento (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOld = this.atendimento;
		this.atendimento = atendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimento", atendimentoOld, atendimento);
	}



	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissional () {
		return getPropertyValue(this, profissional, PROP_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissional the cd_profissional value
	 */
	public void setProfissional (br.com.ksisolucoes.vo.cadsus.Profissional profissional) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalOld = this.profissional;
		this.profissional = profissional;
//        this.getPropertyChangeSupport().firePropertyChange ("profissional", profissionalOld, profissional);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_conduta
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Conduta getConduta () {
		return getPropertyValue(this, conduta, PROP_CONDUTA); 
	}

	/**
	 * Set the value related to the column: cd_conduta
	 * @param conduta the cd_conduta value
	 */
	public void setConduta (br.com.ksisolucoes.vo.prontuario.basico.Conduta conduta) {
//        br.com.ksisolucoes.vo.prontuario.basico.Conduta condutaOld = this.conduta;
		this.conduta = conduta;
//        this.getPropertyChangeSupport().firePropertyChange ("conduta", condutaOld, conduta);
	}



	/**
	 * Return the value associated with the column: cd_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getTabelaCbo () {
		return getPropertyValue(this, tabelaCbo, PROP_TABELA_CBO); 
	}

	/**
	 * Set the value related to the column: cd_cbo
	 * @param tabelaCbo the cd_cbo value
	 */
	public void setTabelaCbo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCboOld = this.tabelaCbo;
		this.tabelaCbo = tabelaCbo;
//        this.getPropertyChangeSupport().firePropertyChange ("tabelaCbo", tabelaCboOld, tabelaCbo);
	}



	/**
	 * Return the value associated with the column: cd_motivo_destino_saida
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.caps.MotivoDestinoSaida getMotivoDestinoSaida () {
		return getPropertyValue(this, motivoDestinoSaida, PROP_MOTIVO_DESTINO_SAIDA); 
	}

	/**
	 * Set the value related to the column: cd_motivo_destino_saida
	 * @param motivoDestinoSaida the cd_motivo_destino_saida value
	 */
	public void setMotivoDestinoSaida (br.com.ksisolucoes.vo.prontuario.procedimento.caps.MotivoDestinoSaida motivoDestinoSaida) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.caps.MotivoDestinoSaida motivoDestinoSaidaOld = this.motivoDestinoSaida;
		this.motivoDestinoSaida = motivoDestinoSaida;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoDestinoSaida", motivoDestinoSaidaOld, motivoDestinoSaida);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.EvolucaoProntuario)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.EvolucaoProntuario evolucaoProntuario = (br.com.ksisolucoes.vo.prontuario.basico.EvolucaoProntuario) obj;
			if (null == this.getCodigo() || null == evolucaoProntuario.getCodigo()) return false;
			else return (this.getCodigo().equals(evolucaoProntuario.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}