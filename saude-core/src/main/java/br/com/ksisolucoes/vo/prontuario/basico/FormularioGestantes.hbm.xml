<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico">
    <class name="FormularioGestantes" table="formulario_gestantes">

        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_formulario_gestantes"
        >
            <generator class="sequence">
                <param name="sequence">seq_formulario_gestantes</param>
            </generator>
        </id>
        <version column="version" name="version" type="long"/>

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.basico.EstratificacaoRisco"
                name="estratificacaoRisco"
                column="cd_estratificacao_risco"
                not-null="true"
        />

        <property
                name="gestanteNegraIndigena"
                type="java.lang.Long"
                column="gestante_negra_indigena"
        />

        <property
                name="gestanteNaoAlfabetizada"
                type="java.lang.Long"
                column="gestante_nao_alfabetizada"
        />

        <property
                name="gestanteMais40"
                type="java.lang.Long"
                column="gestante_mais_40"
        />

        <property
                name="gestanteHistoricoObito"
                type="java.lang.Long"
                column="gestante_historico_obito"
        />

        <property
                name="hipertensaoArterial"
                type="java.lang.Long"
                column="hipertensao_arterial"
        />

        <property
                name="dependenciaDrogasIlicitas"
                type="java.lang.Long"
                column="dependencia_drogas_ilicitas"
        />

        <property
                name="cardiopatias"
                type="java.lang.Long"
                column="cardiopatias"
        />

        <property
                name="pneumopatias"
                type="java.lang.Long"
                column="pneumopatias"
        />

        <property
                name="nefropatias"
                type="java.lang.Long"
                column="nefropatias"
        />

        <property
                name="diabetes"
                type="java.lang.Long"
                column="diabetes"
        />

        <property
                name="hipertiroidismo"
                type="java.lang.Long"
                column="hipertiroidismo"
        />

        <property
                name="maformacaoUtero"
                type="java.lang.Long"
                column="maformacao_utero"
        />

        <property
                name="epilepsia"
                type="java.lang.Long"
                column="epilepsia"
        />

        <property
                name="hemopatias"
                type="java.lang.Long"
                column="hemopatias"
        />

        <property
                name="doencasInfecciosas"
                type="java.lang.Long"
                column="doencas_infecciosas"
        />

        <property
                name="doencasAutoimunes"
                type="java.lang.Long"
                column="doencas_autoimunes"
        />

        <property
                name="cirurgiaUtero"
                type="java.lang.Long"
                column="cirurgia_utero"
        />

        <property
                name="hipotiroidismo"
                type="java.lang.Long"
                column="hipotiroidismo"
        />

        <property
                name="neoplasias"
                type="java.lang.Long"
                column="neoplasias"
        />

        <property
                name="obesidadeMorbida"
                type="java.lang.Long"
                column="obesidade_morbida"
        />

        <property
                name="cirurgiaBariatrica"
                type="java.lang.Long"
                column="cirurgia_bariatrica"
        />

        <property
                name="psicose"
                type="java.lang.Long"
                column="psicose"
        />

        <property
                name="dependenciaDrogasLicitas"
                type="java.lang.Long"
                column="dependencia_drogas_licitas"
        />

        <property
                name="doencasInfectocontagiosas"
                type="java.lang.Long"
                column="doencas_infectocontagiosas"
        />

        <property
                name="sindromeHipertensiva"
                type="java.lang.Long"
                column="sindrome_hipertensiva"
        />

        <property
                name="gestacaoGemelar"
                type="java.lang.Long"
                column="gestacao_gemelar"
        />

        <property
                name="isoimunizacao"
                type="java.lang.Long"
                column="isoimunizacao"
        />

        <property
                name="diabetesMellitus"
                type="java.lang.Long"
                column="diabetes_mellitus"
        />

        <property
                name="retardoCrescimento"
                type="java.lang.Long"
                column="retardo_crescimento"
        />

        <property
                name="partoPrematuro"
                type="java.lang.Long"
                column="parto_prematuro"
        />

        <property
                name="amniorrexe"
                type="java.lang.Long"
                column="amniorrexe"
        />

        <property
                name="placenta"
                type="java.lang.Long"
                column="placenta"
        />

        <property
                name="sangramentoOrigemUterina"
                type="java.lang.Long"
                column="sangramento_origem_uterina"
        />

        <property
                name="maformacaoFetal"
                type="java.lang.Long"
                column="maformacao_fetal"
        />

        <property
                name="mudancaImc"
                type="java.lang.Long"
                column="mudanca_imc"
        />

        <property
                name="observacao"
                type="java.lang.String"
                column="observacao"
        />

        <property
                name="flagRisco"
                type="java.lang.Long"
                column="flag_risco"
        />
    </class>
</hibernate-mapping>