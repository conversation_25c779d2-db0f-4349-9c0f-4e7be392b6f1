package br.com.ksisolucoes.vo.prontuario.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the formulario_idoso_acs table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="formulario_idoso_acs"
 */

public abstract class BaseFormularioIdosoAcs extends BaseRootVO implements Serializable {

	public static String REF = "FormularioIdosoAcs";
    public static final String PROP_QUARTO_ANDANDO = "quartoAndando";
    public static final String PROP_AJUDA_TAREFAS_DOMESTICAS_LEVES_CAUSA_SAUDE = "ajudaTarefasDomesticasLevesCausaSaude";
    public static final String PROP_COMPRAS_ITENS_PESSOAIS = "comprasItensPessoais";
    public static final String PROP_SAUDE_COMPARADO_OUTRAS_PESSOAS = "saudeComparadoOutrasPessoas";
    public static final String PROP_TOMAR_BANHO = "tomarBanho";
    public static final String PROP_CURVAR_AGACHAR_AJOELHAR = "curvarAgacharAjoelhar";
    public static final String PROP_CARREGAR_OBJETOS = "carregarObjetos";
    public static final String PROP_AJUDA_TOMAR_BANHO = "ajudaTomarBanho";
    public static final String PROP_ELEVAR_BRACOS = "elevarBracos";
    public static final String PROP_SERVICO_DOMESTICO = "servicoDomestico";
    public static final String PROP_OBSERVACAO = "observacao";
    public static final String PROP_ESCREVER = "escrever";
    public static final String PROP_LIDAR_DINHEIRO = "lidarDinheiro";
    public static final String PROP_AJUDA_QUARTO_ANDANDO_CAUSA_SAUDE = "ajudaQuartoAndandoCausaSaude";
    public static final String PROP_TAREFAS_DOMESTICAS_LEVES = "tarefasDomesticasLeves";
    public static final String PROP_AJUDA_QUARTO_ANDANDO = "ajudaQuartoAndando";
    public static final String PROP_AJUDA_LIDAR_DINHEIRO_CAUSA_SAUDE = "ajudaLidarDinheiroCausaSaude";
    public static final String PROP_AJUDA_LIDAR_DINHEIRO = "ajudaLidarDinheiro";
    public static final String PROP_CODIGO = "codigo";
    public static final String PROP_AJUDA_TAREFAS_DOMESTICAS_LEVES = "ajudaTarefasDomesticasLeves";
    public static final String PROP_AJUDA_COMPRAS_CAUSA_SAUDE = "ajudaComprasCausaSaude";
    public static final String PROP_ANDAR = "andar";
    public static final String PROP_AJUDA_TOMAR_BANHO_CAUSA_SAUDE = "ajudaTomarBanhoCausaSaude";
	public static final String PROP_SCORE = "score";
    public static final String PROP_IDADE = "idade";
	public static final String PROP_FLAG_RISCO = "flagRisco";
    public static final String PROP_RECEBE_AJUDA_COMPRAS = "recebeAjudaCompras";
	public static final String PROP_ESTRATIFICACAO_RISCO = "estratificacaoRisco";


	// constructors
	public BaseFormularioIdosoAcs () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseFormularioIdosoAcs (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseFormularioIdosoAcs (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.EstratificacaoRisco estratificacaoRisco) {

		this.setCodigo(codigo);
		this.setEstratificacaoRisco(estratificacaoRisco);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
    private java.lang.Long idade;
    private java.lang.Long saudeComparadoOutrasPessoas;
    private java.lang.Long curvarAgacharAjoelhar;
    private java.lang.Long carregarObjetos;
    private java.lang.Long elevarBracos;
    private java.lang.Long escrever;
    private java.lang.Long andar;
    private java.lang.Long servicoDomestico;
    private java.lang.Long comprasItensPessoais;
    private java.lang.Long recebeAjudaCompras;
    private java.lang.Long ajudaComprasCausaSaude;
    private java.lang.Long lidarDinheiro;
    private java.lang.Long ajudaLidarDinheiro;
    private java.lang.Long ajudaLidarDinheiroCausaSaude;
    private java.lang.Long quartoAndando;
    private java.lang.Long ajudaQuartoAndando;
    private java.lang.Long ajudaQuartoAndandoCausaSaude;
    private java.lang.Long tarefasDomesticasLeves;
    private java.lang.Long ajudaTarefasDomesticasLeves;
    private java.lang.Long ajudaTarefasDomesticasLevesCausaSaude;
    private java.lang.Long tomarBanho;
    private java.lang.Long ajudaTomarBanho;
    private java.lang.Long ajudaTomarBanhoCausaSaude;
	private java.lang.String observacao;
	private java.lang.Long score;
	private java.lang.Long flagRisco;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.EstratificacaoRisco estratificacaoRisco;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_formulario_idoso_acs"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}


    /**
     * Return the value associated with the column: idade
     */
    public java.lang.Long getIdade() {
        return getPropertyValue(this, idade, PROP_IDADE);
    }

    /**
     * Set the value related to the column: idade
     *
     * @param idade the idade value
     */
    public void setIdade(java.lang.Long idade) {
//        java.lang.Long idadeOld = this.idade;
        this.idade = idade;
//        this.getPropertyChangeSupport().firePropertyChange ("idade", idadeOld, idade);
    }


    /**
     * Return the value associated with the column: saude_comparado_outras_pessoas
     */
    public java.lang.Long getSaudeComparadoOutrasPessoas() {
        return getPropertyValue(this, saudeComparadoOutrasPessoas, PROP_SAUDE_COMPARADO_OUTRAS_PESSOAS);
    }

    /**
     * Set the value related to the column: saude_comparado_outras_pessoas
     *
     * @param saudeComparadoOutrasPessoas the saude_comparado_outras_pessoas value
     */
    public void setSaudeComparadoOutrasPessoas(java.lang.Long saudeComparadoOutrasPessoas) {
//        java.lang.Long saudeComparadoOutrasPessoasOld = this.saudeComparadoOutrasPessoas;
        this.saudeComparadoOutrasPessoas = saudeComparadoOutrasPessoas;
//        this.getPropertyChangeSupport().firePropertyChange ("saudeComparadoOutrasPessoas", saudeComparadoOutrasPessoasOld, saudeComparadoOutrasPessoas);
    }


    /**
     * Return the value associated with the column: curvar_agachar_ajoelhar
     */
    public java.lang.Long getCurvarAgacharAjoelhar() {
        return getPropertyValue(this, curvarAgacharAjoelhar, PROP_CURVAR_AGACHAR_AJOELHAR);
    }

    /**
     * Set the value related to the column: curvar_agachar_ajoelhar
     *
     * @param curvarAgacharAjoelhar the curvar_agachar_ajoelhar value
     */
    public void setCurvarAgacharAjoelhar(java.lang.Long curvarAgacharAjoelhar) {
//        java.lang.Long curvarAgacharAjoelharOld = this.curvarAgacharAjoelhar;
        this.curvarAgacharAjoelhar = curvarAgacharAjoelhar;
//        this.getPropertyChangeSupport().firePropertyChange ("curvarAgacharAjoelhar", curvarAgacharAjoelharOld, curvarAgacharAjoelhar);
    }


    /**
     * Return the value associated with the column: carregar_objetos
     */
    public java.lang.Long getCarregarObjetos() {
        return getPropertyValue(this, carregarObjetos, PROP_CARREGAR_OBJETOS);
    }

    /**
     * Set the value related to the column: carregar_objetos
     *
     * @param carregarObjetos the carregar_objetos value
     */
    public void setCarregarObjetos(java.lang.Long carregarObjetos) {
//        java.lang.Long carregarObjetosOld = this.carregarObjetos;
        this.carregarObjetos = carregarObjetos;
//        this.getPropertyChangeSupport().firePropertyChange ("carregarObjetos", carregarObjetosOld, carregarObjetos);
    }


    /**
     * Return the value associated with the column: elevar_bracos
     */
    public java.lang.Long getElevarBracos() {
        return getPropertyValue(this, elevarBracos, PROP_ELEVAR_BRACOS);
    }

    /**
     * Set the value related to the column: elevar_bracos
     *
     * @param elevarBracos the elevar_bracos value
     */
    public void setElevarBracos(java.lang.Long elevarBracos) {
//        java.lang.Long elevarBracosOld = this.elevarBracos;
        this.elevarBracos = elevarBracos;
//        this.getPropertyChangeSupport().firePropertyChange ("elevarBracos", elevarBracosOld, elevarBracos);
    }


    /**
     * Return the value associated with the column: escrever
     */
    public java.lang.Long getEscrever() {
        return getPropertyValue(this, escrever, PROP_ESCREVER);
    }

    /**
     * Set the value related to the column: escrever
     *
     * @param escrever the escrever value
     */
    public void setEscrever(java.lang.Long escrever) {
//        java.lang.Long escreverOld = this.escrever;
        this.escrever = escrever;
//        this.getPropertyChangeSupport().firePropertyChange ("escrever", escreverOld, escrever);
    }


    /**
     * Return the value associated with the column: andar
     */
    public java.lang.Long getAndar() {
        return getPropertyValue(this, andar, PROP_ANDAR);
    }

    /**
     * Set the value related to the column: andar
     *
     * @param andar the andar value
     */
    public void setAndar(java.lang.Long andar) {
//        java.lang.Long andarOld = this.andar;
        this.andar = andar;
//        this.getPropertyChangeSupport().firePropertyChange ("andar", andarOld, andar);
    }


    /**
     * Return the value associated with the column: servico_domestico
     */
    public java.lang.Long getServicoDomestico() {
        return getPropertyValue(this, servicoDomestico, PROP_SERVICO_DOMESTICO);
    }

    /**
     * Set the value related to the column: servico_domestico
     *
     * @param servicoDomestico the servico_domestico value
     */
    public void setServicoDomestico(java.lang.Long servicoDomestico) {
//        java.lang.Long servicoDomesticoOld = this.servicoDomestico;
        this.servicoDomestico = servicoDomestico;
//        this.getPropertyChangeSupport().firePropertyChange ("servicoDomestico", servicoDomesticoOld, servicoDomestico);
    }


    /**
     * Return the value associated with the column: compras_itens_pessoais
     */
    public java.lang.Long getComprasItensPessoais() {
        return getPropertyValue(this, comprasItensPessoais, PROP_COMPRAS_ITENS_PESSOAIS);
    }

    /**
     * Set the value related to the column: compras_itens_pessoais
     *
     * @param comprasItensPessoais the compras_itens_pessoais value
     */
    public void setComprasItensPessoais(java.lang.Long comprasItensPessoais) {
//        java.lang.Long comprasItensPessoaisOld = this.comprasItensPessoais;
        this.comprasItensPessoais = comprasItensPessoais;
//        this.getPropertyChangeSupport().firePropertyChange ("comprasItensPessoais", comprasItensPessoaisOld, comprasItensPessoais);
    }


    /**
     * Return the value associated with the column: recebe_ajuda_compras
     */
    public java.lang.Long getRecebeAjudaCompras() {
        return getPropertyValue(this, recebeAjudaCompras, PROP_RECEBE_AJUDA_COMPRAS);
    }

    /**
     * Set the value related to the column: recebe_ajuda_compras
     *
     * @param recebeAjudaCompras the recebe_ajuda_compras value
     */
    public void setRecebeAjudaCompras(java.lang.Long recebeAjudaCompras) {
//        java.lang.Long recebeAjudaComprasOld = this.recebeAjudaCompras;
        this.recebeAjudaCompras = recebeAjudaCompras;
//        this.getPropertyChangeSupport().firePropertyChange ("recebeAjudaCompras", recebeAjudaComprasOld, recebeAjudaCompras);
    }


    /**
     * Return the value associated with the column: ajuda_compras_causa_saude
     */
    public java.lang.Long getAjudaComprasCausaSaude() {
        return getPropertyValue(this, ajudaComprasCausaSaude, PROP_AJUDA_COMPRAS_CAUSA_SAUDE);
    }

    /**
     * Set the value related to the column: ajuda_compras_causa_saude
     *
     * @param ajudaComprasCausaSaude the ajuda_compras_causa_saude value
     */
    public void setAjudaComprasCausaSaude(java.lang.Long ajudaComprasCausaSaude) {
//        java.lang.Long ajudaComprasCausaSaudeOld = this.ajudaComprasCausaSaude;
        this.ajudaComprasCausaSaude = ajudaComprasCausaSaude;
//        this.getPropertyChangeSupport().firePropertyChange ("ajudaComprasCausaSaude", ajudaComprasCausaSaudeOld, ajudaComprasCausaSaude);
    }


    /**
     * Return the value associated with the column: lidar_dineheiro
     */
    public java.lang.Long getLidarDinheiro() {
        return getPropertyValue(this, lidarDinheiro, PROP_LIDAR_DINHEIRO);
    }

    /**
     * Set the value related to the column: lidar_dineheiro
     *
     * @param lidarDinheiro the lidar_dineheiro value
     */
    public void setLidarDinheiro(java.lang.Long lidarDinheiro) {
//        java.lang.Long lidarDinheiroOld = this.lidarDinheiro;
        this.lidarDinheiro = lidarDinheiro;
//        this.getPropertyChangeSupport().firePropertyChange ("lidarDinheiro", lidarDinheiroOld, lidarDinheiro);
    }


    /**
     * Return the value associated with the column: ajuda_lidar_dinheiro
     */
    public java.lang.Long getAjudaLidarDinheiro() {
        return getPropertyValue(this, ajudaLidarDinheiro, PROP_AJUDA_LIDAR_DINHEIRO);
    }

    /**
     * Set the value related to the column: ajuda_lidar_dinheiro
     *
     * @param ajudaLidarDinheiro the ajuda_lidar_dinheiro value
     */
    public void setAjudaLidarDinheiro(java.lang.Long ajudaLidarDinheiro) {
//        java.lang.Long ajudaLidarDinheiroOld = this.ajudaLidarDinheiro;
        this.ajudaLidarDinheiro = ajudaLidarDinheiro;
//        this.getPropertyChangeSupport().firePropertyChange ("ajudaLidarDinheiro", ajudaLidarDinheiroOld, ajudaLidarDinheiro);
    }


    /**
     * Return the value associated with the column: ajuda_lidar_dinheiro_causa_saude
     */
    public java.lang.Long getAjudaLidarDinheiroCausaSaude() {
        return getPropertyValue(this, ajudaLidarDinheiroCausaSaude, PROP_AJUDA_LIDAR_DINHEIRO_CAUSA_SAUDE);
    }

    /**
     * Set the value related to the column: ajuda_lidar_dinheiro_causa_saude
     *
     * @param ajudaLidarDinheiroCausaSaude the ajuda_lidar_dinheiro_causa_saude value
     */
    public void setAjudaLidarDinheiroCausaSaude(java.lang.Long ajudaLidarDinheiroCausaSaude) {
//        java.lang.Long ajudaLidarDinheiroCausaSaudeOld = this.ajudaLidarDinheiroCausaSaude;
        this.ajudaLidarDinheiroCausaSaude = ajudaLidarDinheiroCausaSaude;
//        this.getPropertyChangeSupport().firePropertyChange ("ajudaLidarDinheiroCausaSaude", ajudaLidarDinheiroCausaSaudeOld, ajudaLidarDinheiroCausaSaude);
    }


    /**
     * Return the value associated with the column: quarto_andando
     */
    public java.lang.Long getQuartoAndando() {
        return getPropertyValue(this, quartoAndando, PROP_QUARTO_ANDANDO);
    }

    /**
     * Set the value related to the column: quarto_andando
     *
     * @param quartoAndando the quarto_andando value
     */
    public void setQuartoAndando(java.lang.Long quartoAndando) {
//        java.lang.Long quartoAndandoOld = this.quartoAndando;
        this.quartoAndando = quartoAndando;
//        this.getPropertyChangeSupport().firePropertyChange ("quartoAndando", quartoAndandoOld, quartoAndando);
    }


    /**
     * Return the value associated with the column: ajuda_quarto_andando
     */
    public java.lang.Long getAjudaQuartoAndando() {
        return getPropertyValue(this, ajudaQuartoAndando, PROP_AJUDA_QUARTO_ANDANDO);
    }

    /**
     * Set the value related to the column: ajuda_quarto_andando
     *
     * @param ajudaQuartoAndando the ajuda_quarto_andando value
     */
    public void setAjudaQuartoAndando(java.lang.Long ajudaQuartoAndando) {
//        java.lang.Long ajudaQuartoAndandoOld = this.ajudaQuartoAndando;
        this.ajudaQuartoAndando = ajudaQuartoAndando;
//        this.getPropertyChangeSupport().firePropertyChange ("ajudaQuartoAndando", ajudaQuartoAndandoOld, ajudaQuartoAndando);
    }


    /**
     * Return the value associated with the column: ajuda_quarto_andando_causa_saude
     */
    public java.lang.Long getAjudaQuartoAndandoCausaSaude() {
        return getPropertyValue(this, ajudaQuartoAndandoCausaSaude, PROP_AJUDA_QUARTO_ANDANDO_CAUSA_SAUDE);
    }

    /**
     * Set the value related to the column: ajuda_quarto_andando_causa_saude
     *
     * @param ajudaQuartoAndandoCausaSaude the ajuda_quarto_andando_causa_saude value
     */
    public void setAjudaQuartoAndandoCausaSaude(java.lang.Long ajudaQuartoAndandoCausaSaude) {
//        java.lang.Long ajudaQuartoAndandoCausaSaudeOld = this.ajudaQuartoAndandoCausaSaude;
        this.ajudaQuartoAndandoCausaSaude = ajudaQuartoAndandoCausaSaude;
//        this.getPropertyChangeSupport().firePropertyChange ("ajudaQuartoAndandoCausaSaude", ajudaQuartoAndandoCausaSaudeOld, ajudaQuartoAndandoCausaSaude);
    }


    /**
     * Return the value associated with the column: tarefas_domesticas_leves
     */
    public java.lang.Long getTarefasDomesticasLeves() {
        return getPropertyValue(this, tarefasDomesticasLeves, PROP_TAREFAS_DOMESTICAS_LEVES);
    }

    /**
     * Set the value related to the column: tarefas_domesticas_leves
     *
     * @param tarefasDomesticasLeves the tarefas_domesticas_leves value
     */
    public void setTarefasDomesticasLeves(java.lang.Long tarefasDomesticasLeves) {
//        java.lang.Long tarefasDomesticasLevesOld = this.tarefasDomesticasLeves;
        this.tarefasDomesticasLeves = tarefasDomesticasLeves;
//        this.getPropertyChangeSupport().firePropertyChange ("tarefasDomesticasLeves", tarefasDomesticasLevesOld, tarefasDomesticasLeves);
    }


    /**
     * Return the value associated with the column: ajuda_tarefas_domesticas_leves
     */
    public java.lang.Long getAjudaTarefasDomesticasLeves() {
        return getPropertyValue(this, ajudaTarefasDomesticasLeves, PROP_AJUDA_TAREFAS_DOMESTICAS_LEVES);
    }

    /**
     * Set the value related to the column: ajuda_tarefas_domesticas_leves
     *
     * @param ajudaTarefasDomesticasLeves the ajuda_tarefas_domesticas_leves value
     */
    public void setAjudaTarefasDomesticasLeves(java.lang.Long ajudaTarefasDomesticasLeves) {
//        java.lang.Long ajudaTarefasDomesticasLevesOld = this.ajudaTarefasDomesticasLeves;
        this.ajudaTarefasDomesticasLeves = ajudaTarefasDomesticasLeves;
//        this.getPropertyChangeSupport().firePropertyChange ("ajudaTarefasDomesticasLeves", ajudaTarefasDomesticasLevesOld, ajudaTarefasDomesticasLeves);
    }


    /**
     * Return the value associated with the column: ajuda_tarefas_domesticas_leves_causa_saude
     */
    public java.lang.Long getAjudaTarefasDomesticasLevesCausaSaude() {
        return getPropertyValue(this, ajudaTarefasDomesticasLevesCausaSaude, PROP_AJUDA_TAREFAS_DOMESTICAS_LEVES_CAUSA_SAUDE);
    }

    /**
     * Set the value related to the column: ajuda_tarefas_domesticas_leves_causa_saude
     *
     * @param ajudaTarefasDomesticasLevesCausaSaude the ajuda_tarefas_domesticas_leves_causa_saude value
     */
    public void setAjudaTarefasDomesticasLevesCausaSaude(java.lang.Long ajudaTarefasDomesticasLevesCausaSaude) {
//        java.lang.Long ajudaTarefasDomesticasLevesCausaSaudeOld = this.ajudaTarefasDomesticasLevesCausaSaude;
        this.ajudaTarefasDomesticasLevesCausaSaude = ajudaTarefasDomesticasLevesCausaSaude;
//        this.getPropertyChangeSupport().firePropertyChange ("ajudaTarefasDomesticasLevesCausaSaude", ajudaTarefasDomesticasLevesCausaSaudeOld, ajudaTarefasDomesticasLevesCausaSaude);
    }


    /**
     * Return the value associated with the column: tomar_banho
     */
    public java.lang.Long getTomarBanho() {
        return getPropertyValue(this, tomarBanho, PROP_TOMAR_BANHO);
    }

    /**
     * Set the value related to the column: tomar_banho
     *
     * @param tomarBanho the tomar_banho value
     */
    public void setTomarBanho(java.lang.Long tomarBanho) {
//        java.lang.Long tomarBanhoOld = this.tomarBanho;
        this.tomarBanho = tomarBanho;
//        this.getPropertyChangeSupport().firePropertyChange ("tomarBanho", tomarBanhoOld, tomarBanho);
    }


    /**
     * Return the value associated with the column: ajuda_tomar_banho
     */
    public java.lang.Long getAjudaTomarBanho() {
        return getPropertyValue(this, ajudaTomarBanho, PROP_AJUDA_TOMAR_BANHO);
    }

    /**
     * Set the value related to the column: ajuda_tomar_banho
     *
     * @param ajudaTomarBanho the ajuda_tomar_banho value
     */
    public void setAjudaTomarBanho(java.lang.Long ajudaTomarBanho) {
//        java.lang.Long ajudaTomarBanhoOld = this.ajudaTomarBanho;
        this.ajudaTomarBanho = ajudaTomarBanho;
//        this.getPropertyChangeSupport().firePropertyChange ("ajudaTomarBanho", ajudaTomarBanhoOld, ajudaTomarBanho);
    }


    /**
     * Return the value associated with the column: ajuda_tomar_banho_causa_saude
     */
    public java.lang.Long getAjudaTomarBanhoCausaSaude() {
        return getPropertyValue(this, ajudaTomarBanhoCausaSaude, PROP_AJUDA_TOMAR_BANHO_CAUSA_SAUDE);
    }

    /**
     * Set the value related to the column: ajuda_tomar_banho_causa_saude
     *
     * @param ajudaTomarBanhoCausaSaude the ajuda_tomar_banho_causa_saude value
     */
    public void setAjudaTomarBanhoCausaSaude(java.lang.Long ajudaTomarBanhoCausaSaude) {
//        java.lang.Long ajudaTomarBanhoCausaSaudeOld = this.ajudaTomarBanhoCausaSaude;
        this.ajudaTomarBanhoCausaSaude = ajudaTomarBanhoCausaSaude;
//        this.getPropertyChangeSupport().firePropertyChange ("ajudaTomarBanhoCausaSaude", ajudaTomarBanhoCausaSaudeOld, ajudaTomarBanhoCausaSaude);
    }



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: score
	 */
	public java.lang.Long getScore () {
		return getPropertyValue(this, score, PROP_SCORE); 
	}

	/**
	 * Set the value related to the column: score
	 * @param score the score value
	 */
	public void setScore (java.lang.Long score) {
//        java.lang.Long scoreOld = this.score;
		this.score = score;
//        this.getPropertyChangeSupport().firePropertyChange ("score", scoreOld, score);
	}



	/**
	 * Return the value associated with the column: flag_risco
	 */
	public java.lang.Long getFlagRisco () {
		return getPropertyValue(this, flagRisco, PROP_FLAG_RISCO); 
	}

	/**
	 * Set the value related to the column: flag_risco
	 * @param flagRisco the flag_risco value
	 */
	public void setFlagRisco (java.lang.Long flagRisco) {
//        java.lang.Long flagRiscoOld = this.flagRisco;
		this.flagRisco = flagRisco;
//        this.getPropertyChangeSupport().firePropertyChange ("flagRisco", flagRiscoOld, flagRisco);
	}



	/**
	 * Return the value associated with the column: cd_estratificacao_risco
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.EstratificacaoRisco getEstratificacaoRisco () {
		return getPropertyValue(this, estratificacaoRisco, PROP_ESTRATIFICACAO_RISCO); 
	}

	/**
	 * Set the value related to the column: cd_estratificacao_risco
	 * @param estratificacaoRisco the cd_estratificacao_risco value
	 */
	public void setEstratificacaoRisco (br.com.ksisolucoes.vo.prontuario.basico.EstratificacaoRisco estratificacaoRisco) {
//        br.com.ksisolucoes.vo.prontuario.basico.EstratificacaoRisco estratificacaoRiscoOld = this.estratificacaoRisco;
		this.estratificacaoRisco = estratificacaoRisco;
//        this.getPropertyChangeSupport().firePropertyChange ("estratificacaoRisco", estratificacaoRiscoOld, estratificacaoRisco);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.FormularioIdosoAcs)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.FormularioIdosoAcs formularioIdosoAcs = (br.com.ksisolucoes.vo.prontuario.basico.FormularioIdosoAcs) obj;
			if (null == this.getCodigo() || null == formularioIdosoAcs.getCodigo()) return false;
			else return (this.getCodigo().equals(formularioIdosoAcs.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}