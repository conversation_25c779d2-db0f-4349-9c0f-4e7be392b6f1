package br.com.ksisolucoes.vo.vigilancia.investigacao;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.base.BaseInvestigacaoAgravoHepatiteViral;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


public class InvestigacaoAgravoHepatiteViral extends BaseInvestigacaoAgravoHepatiteViral implements CodigoManager {
    public InvestigacaoAgravoHepatiteViral() {
        super();
    }

    public InvestigacaoAgravoHepatiteViral(Long codigo) {
        super(codigo);
    }

    public InvestigacaoAgravoHepatiteViral(
            Long codigo,
            RegistroAgravo registroAgravo,
            TabelaCbo tabelaCbo,
            String flagInformacoesComplementares
    ) {
        super(
                codigo,
                registroAgravo,
                tabelaCbo,
                flagInformacoesComplementares
        );
    }

    public static InvestigacaoAgravoHepatiteViral buscaPorRegistroAgravo(RegistroAgravo registroAgravo) {
        InvestigacaoAgravoHepatiteViral investigacao =
                LoadManager.getInstance(InvestigacaoAgravoHepatiteViral.class)
                        .addProperties(new HQLProperties(InvestigacaoAgravoHepatiteViral.class).getProperties())
                        .addProperties(new HQLProperties(
                                RegistroAgravo.class,
                                VOUtils.montarPath(InvestigacaoAgravoHepatiteViral.PROP_REGISTRO_AGRAVO)).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(
                                VOUtils.montarPath(InvestigacaoAgravoHepatiteViral.PROP_REGISTRO_AGRAVO, RegistroAgravo.PROP_CODIGO),
                                registroAgravo.getCodigo()))
                        .start().getVO();
        return investigacao;
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public void setCodigoManager(Serializable key) {
        this.setCodigo((Long) key);
    }

    public List<InvestigacaoAgravoHepatiteViralLocalExposicao> getLocaisExposicaoByIdInvestigacaoHepatiteViral() {
        if (getCodigo() != null) {
            List<InvestigacaoAgravoHepatiteViralLocalExposicao> list =
                    LoadManager.getInstance(InvestigacaoAgravoHepatiteViralLocalExposicao.class)
                            .addProperties(new HQLProperties(InvestigacaoAgravoHepatiteViralLocalExposicao.class).getProperties())
                            .addProperty(VOUtils.montarPath(InvestigacaoAgravoHepatiteViralLocalExposicao.PROP_CIDADE_LOCAL_EXPOSICAO, Cidade.PROP_ESTADO, Estado.PROP_SIGLA))
                            .addProperties(new HQLProperties(
                                    InvestigacaoAgravoHepatiteViral.class,
                                    VOUtils.montarPath(InvestigacaoAgravoHepatiteViralLocalExposicao.PROP_INVESTIGACAO_AGRAVO_HEPATITE_VIRAL)).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(
                                    VOUtils.montarPath(InvestigacaoAgravoHepatiteViralLocalExposicao.PROP_INVESTIGACAO_AGRAVO_HEPATITE_VIRAL, InvestigacaoAgravoHepatiteViral.PROP_CODIGO),
                                    getCodigo()))
                            .start().getList();
            return list;
        }
        return new ArrayList<InvestigacaoAgravoHepatiteViralLocalExposicao>();
    }

    public List<InvestigacaoAgravoHepatiteViralComunicantes> getComunicantesByIdInvestigacaoHepatiteViral() {
        if (getCodigo() != null) {
            List<InvestigacaoAgravoHepatiteViralComunicantes> list =
                    LoadManager.getInstance(InvestigacaoAgravoHepatiteViralComunicantes.class)
                            .addProperties(new HQLProperties(InvestigacaoAgravoHepatiteViralComunicantes.class).getProperties())
                            .addProperties(new HQLProperties(
                                    InvestigacaoAgravoHepatiteViral.class,
                                    VOUtils.montarPath(InvestigacaoAgravoHepatiteViralComunicantes.PROP_INVESTIGACAO_AGRAVO_HEPATITE_VIRAL)).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(
                                    VOUtils.montarPath(InvestigacaoAgravoHepatiteViralComunicantes.PROP_INVESTIGACAO_AGRAVO_HEPATITE_VIRAL, InvestigacaoAgravoHepatiteViral.PROP_CODIGO),
                                    getCodigo()))
                            .start().getList();
            return list;
        }
        return new ArrayList<InvestigacaoAgravoHepatiteViralComunicantes>();
    }


}
