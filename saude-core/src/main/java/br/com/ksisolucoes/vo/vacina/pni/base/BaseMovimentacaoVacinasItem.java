package br.com.ksisolucoes.vo.vacina.pni.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the movimentacao_vacinas_item table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="movimentacao_vacinas_item"
 */

public abstract class BaseMovimentacaoVacinasItem extends BaseRootVO implements Serializable {

	public static String REF = "MovimentacaoVacinasItem";
	public static final String PROP_STATUS = "status";
	public static final String PROP_QUANTIDADE_PERDA_TRANSPORTE = "quantidadePerdaTransporte";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_QUANTIDADE_UTILIZADAS = "quantidadeUtilizadas";
	public static final String PROP_QUANTIDADE_QUEBRADAS = "quantidadeQuebradas";
	public static final String PROP_QUANTIDADE_PERDA_FALTA_ENERGIA = "quantidadePerdaFaltaEnergia";
	public static final String PROP_SALDO_ATUAL = "saldoAtual";
	public static final String PROP_QUANTIDADE_PERDA_VALIDADE_VENCIDA = "quantidadePerdaValidadeVencida";
	public static final String PROP_QUANTIDADE_DISTRIBUIDAS = "quantidadeDistribuidas";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_QUANTIDADE_PERDA_OUTROS_MOTIVOS = "quantidadePerdaOutrosMotivos";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_QUANTIDADE_PERDA_FALHA_EQUIPAMENTO = "quantidadePerdaFalhaEquipamento";
	public static final String PROP_QUANTIDADE_INDISPONIVEL = "quantidadeIndisponivel";
	public static final String PROP_PRODUTO_VACINA = "produtoVacina";
	public static final String PROP_SALDO_INDISPONIVEL_ATUAL = "saldoIndisponivelAtual";
	public static final String PROP_QUANTIDADE_PERDA_PROCEDIMENTO_INADEQUADO = "quantidadePerdaProcedimentoInadequado";
	public static final String PROP_DATA_USUARIO = "dataUsuario";
	public static final String PROP_SALDO_ANTERIOR = "saldoAnterior";
	public static final String PROP_QUANTIDADE_TRANSFERIDAS = "quantidadeTransferidas";
	public static final String PROP_SALDO_INDISPONIVEL_ANTERIOR = "saldoIndisponivelAnterior";
	public static final String PROP_MOVIMENTACAO_VACINAS = "movimentacaoVacinas";
	public static final String PROP_QUANTIDADE_RECEBIDAS = "quantidadeRecebidas";


	// constructors
	public BaseMovimentacaoVacinasItem () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseMovimentacaoVacinasItem (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseMovimentacaoVacinasItem (
		java.lang.Long codigo,
		java.lang.Double saldoAnterior,
		java.lang.Double quantidadeRecebidas,
		java.lang.Double quantidadeDistribuidas,
		java.lang.Double quantidadeUtilizadas,
		java.lang.Double quantidadeTransferidas,
		java.lang.Double quantidadeQuebradas,
		java.lang.Double quantidadePerdaFaltaEnergia,
		java.lang.Double quantidadePerdaFalhaEquipamento,
		java.lang.Double quantidadePerdaValidadeVencida,
		java.lang.Double quantidadePerdaProcedimentoInadequado,
		java.lang.Double quantidadePerdaOutrosMotivos,
		java.lang.Double quantidadePerdaTransporte,
		java.lang.Double saldoAtual,
		java.lang.Double saldoIndisponivelAnterior,
		java.lang.Double saldoIndisponivelAtual,
		java.lang.Long status,
		java.util.Date dataCadastro,
		java.util.Date dataUsuario) {

		this.setCodigo(codigo);
		this.setSaldoAnterior(saldoAnterior);
		this.setQuantidadeRecebidas(quantidadeRecebidas);
		this.setQuantidadeDistribuidas(quantidadeDistribuidas);
		this.setQuantidadeUtilizadas(quantidadeUtilizadas);
		this.setQuantidadeTransferidas(quantidadeTransferidas);
		this.setQuantidadeQuebradas(quantidadeQuebradas);
		this.setQuantidadePerdaFaltaEnergia(quantidadePerdaFaltaEnergia);
		this.setQuantidadePerdaFalhaEquipamento(quantidadePerdaFalhaEquipamento);
		this.setQuantidadePerdaValidadeVencida(quantidadePerdaValidadeVencida);
		this.setQuantidadePerdaProcedimentoInadequado(quantidadePerdaProcedimentoInadequado);
		this.setQuantidadePerdaOutrosMotivos(quantidadePerdaOutrosMotivos);
		this.setQuantidadePerdaTransporte(quantidadePerdaTransporte);
		this.setSaldoAtual(saldoAtual);
		this.setSaldoIndisponivelAnterior(saldoIndisponivelAnterior);
		this.setSaldoIndisponivelAtual(saldoIndisponivelAtual);
		this.setStatus(status);
		this.setDataCadastro(dataCadastro);
		this.setDataUsuario(dataUsuario);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Double saldoAnterior;
	private java.lang.Double quantidadeRecebidas;
	private java.lang.Double quantidadeDistribuidas;
	private java.lang.Double quantidadeUtilizadas;
	private java.lang.Double quantidadeTransferidas;
	private java.lang.Double quantidadeQuebradas;
	private java.lang.Double quantidadePerdaFaltaEnergia;
	private java.lang.Double quantidadePerdaFalhaEquipamento;
	private java.lang.Double quantidadePerdaValidadeVencida;
	private java.lang.Double quantidadePerdaProcedimentoInadequado;
	private java.lang.Double quantidadePerdaOutrosMotivos;
	private java.lang.Double quantidadePerdaTransporte;
	private java.lang.Double saldoAtual;
	private java.lang.Double saldoIndisponivelAnterior;
	private java.lang.Double saldoIndisponivelAtual;
	private java.lang.Long status;
	private java.util.Date dataCadastro;
	private java.util.Date dataUsuario;
	private java.lang.Double quantidadeIndisponivel;

	// many to one
	private br.com.ksisolucoes.vo.vacina.pni.MovimentacaoVacinas movimentacaoVacinas;
	private br.com.ksisolucoes.vo.vacina.ProdutoVacina produtoVacina;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_mov_vac_item"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: saldo_anterior
	 */
	public java.lang.Double getSaldoAnterior () {
		return getPropertyValue(this, saldoAnterior, PROP_SALDO_ANTERIOR); 
	}

	/**
	 * Set the value related to the column: saldo_anterior
	 * @param saldoAnterior the saldo_anterior value
	 */
	public void setSaldoAnterior (java.lang.Double saldoAnterior) {
//        java.lang.Double saldoAnteriorOld = this.saldoAnterior;
		this.saldoAnterior = saldoAnterior;
//        this.getPropertyChangeSupport().firePropertyChange ("saldoAnterior", saldoAnteriorOld, saldoAnterior);
	}



	/**
	 * Return the value associated with the column: qt_recebidas
	 */
	public java.lang.Double getQuantidadeRecebidas () {
		return getPropertyValue(this, quantidadeRecebidas, PROP_QUANTIDADE_RECEBIDAS); 
	}

	/**
	 * Set the value related to the column: qt_recebidas
	 * @param quantidadeRecebidas the qt_recebidas value
	 */
	public void setQuantidadeRecebidas (java.lang.Double quantidadeRecebidas) {
//        java.lang.Double quantidadeRecebidasOld = this.quantidadeRecebidas;
		this.quantidadeRecebidas = quantidadeRecebidas;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeRecebidas", quantidadeRecebidasOld, quantidadeRecebidas);
	}



	/**
	 * Return the value associated with the column: qt_distribuidas
	 */
	public java.lang.Double getQuantidadeDistribuidas () {
		return getPropertyValue(this, quantidadeDistribuidas, PROP_QUANTIDADE_DISTRIBUIDAS); 
	}

	/**
	 * Set the value related to the column: qt_distribuidas
	 * @param quantidadeDistribuidas the qt_distribuidas value
	 */
	public void setQuantidadeDistribuidas (java.lang.Double quantidadeDistribuidas) {
//        java.lang.Double quantidadeDistribuidasOld = this.quantidadeDistribuidas;
		this.quantidadeDistribuidas = quantidadeDistribuidas;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeDistribuidas", quantidadeDistribuidasOld, quantidadeDistribuidas);
	}



	/**
	 * Return the value associated with the column: qt_utilizadas
	 */
	public java.lang.Double getQuantidadeUtilizadas () {
		return getPropertyValue(this, quantidadeUtilizadas, PROP_QUANTIDADE_UTILIZADAS); 
	}

	/**
	 * Set the value related to the column: qt_utilizadas
	 * @param quantidadeUtilizadas the qt_utilizadas value
	 */
	public void setQuantidadeUtilizadas (java.lang.Double quantidadeUtilizadas) {
//        java.lang.Double quantidadeUtilizadasOld = this.quantidadeUtilizadas;
		this.quantidadeUtilizadas = quantidadeUtilizadas;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeUtilizadas", quantidadeUtilizadasOld, quantidadeUtilizadas);
	}



	/**
	 * Return the value associated with the column: qt_transferidas
	 */
	public java.lang.Double getQuantidadeTransferidas () {
		return getPropertyValue(this, quantidadeTransferidas, PROP_QUANTIDADE_TRANSFERIDAS); 
	}

	/**
	 * Set the value related to the column: qt_transferidas
	 * @param quantidadeTransferidas the qt_transferidas value
	 */
	public void setQuantidadeTransferidas (java.lang.Double quantidadeTransferidas) {
//        java.lang.Double quantidadeTransferidasOld = this.quantidadeTransferidas;
		this.quantidadeTransferidas = quantidadeTransferidas;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeTransferidas", quantidadeTransferidasOld, quantidadeTransferidas);
	}



	/**
	 * Return the value associated with the column: qt_quebradas
	 */
	public java.lang.Double getQuantidadeQuebradas () {
		return getPropertyValue(this, quantidadeQuebradas, PROP_QUANTIDADE_QUEBRADAS); 
	}

	/**
	 * Set the value related to the column: qt_quebradas
	 * @param quantidadeQuebradas the qt_quebradas value
	 */
	public void setQuantidadeQuebradas (java.lang.Double quantidadeQuebradas) {
//        java.lang.Double quantidadeQuebradasOld = this.quantidadeQuebradas;
		this.quantidadeQuebradas = quantidadeQuebradas;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeQuebradas", quantidadeQuebradasOld, quantidadeQuebradas);
	}



	/**
	 * Return the value associated with the column: qt_perda_falta_energia
	 */
	public java.lang.Double getQuantidadePerdaFaltaEnergia () {
		return getPropertyValue(this, quantidadePerdaFaltaEnergia, PROP_QUANTIDADE_PERDA_FALTA_ENERGIA); 
	}

	/**
	 * Set the value related to the column: qt_perda_falta_energia
	 * @param quantidadePerdaFaltaEnergia the qt_perda_falta_energia value
	 */
	public void setQuantidadePerdaFaltaEnergia (java.lang.Double quantidadePerdaFaltaEnergia) {
//        java.lang.Double quantidadePerdaFaltaEnergiaOld = this.quantidadePerdaFaltaEnergia;
		this.quantidadePerdaFaltaEnergia = quantidadePerdaFaltaEnergia;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadePerdaFaltaEnergia", quantidadePerdaFaltaEnergiaOld, quantidadePerdaFaltaEnergia);
	}



	/**
	 * Return the value associated with the column: qt_perda_falha_equipamento
	 */
	public java.lang.Double getQuantidadePerdaFalhaEquipamento () {
		return getPropertyValue(this, quantidadePerdaFalhaEquipamento, PROP_QUANTIDADE_PERDA_FALHA_EQUIPAMENTO); 
	}

	/**
	 * Set the value related to the column: qt_perda_falha_equipamento
	 * @param quantidadePerdaFalhaEquipamento the qt_perda_falha_equipamento value
	 */
	public void setQuantidadePerdaFalhaEquipamento (java.lang.Double quantidadePerdaFalhaEquipamento) {
//        java.lang.Double quantidadePerdaFalhaEquipamentoOld = this.quantidadePerdaFalhaEquipamento;
		this.quantidadePerdaFalhaEquipamento = quantidadePerdaFalhaEquipamento;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadePerdaFalhaEquipamento", quantidadePerdaFalhaEquipamentoOld, quantidadePerdaFalhaEquipamento);
	}



	/**
	 * Return the value associated with the column: qt_perda_valid_venc
	 */
	public java.lang.Double getQuantidadePerdaValidadeVencida () {
		return getPropertyValue(this, quantidadePerdaValidadeVencida, PROP_QUANTIDADE_PERDA_VALIDADE_VENCIDA); 
	}

	/**
	 * Set the value related to the column: qt_perda_valid_venc
	 * @param quantidadePerdaValidadeVencida the qt_perda_valid_venc value
	 */
	public void setQuantidadePerdaValidadeVencida (java.lang.Double quantidadePerdaValidadeVencida) {
//        java.lang.Double quantidadePerdaValidadeVencidaOld = this.quantidadePerdaValidadeVencida;
		this.quantidadePerdaValidadeVencida = quantidadePerdaValidadeVencida;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadePerdaValidadeVencida", quantidadePerdaValidadeVencidaOld, quantidadePerdaValidadeVencida);
	}



	/**
	 * Return the value associated with the column: qt_perda_proced_inadequado
	 */
	public java.lang.Double getQuantidadePerdaProcedimentoInadequado () {
		return getPropertyValue(this, quantidadePerdaProcedimentoInadequado, PROP_QUANTIDADE_PERDA_PROCEDIMENTO_INADEQUADO); 
	}

	/**
	 * Set the value related to the column: qt_perda_proced_inadequado
	 * @param quantidadePerdaProcedimentoInadequado the qt_perda_proced_inadequado value
	 */
	public void setQuantidadePerdaProcedimentoInadequado (java.lang.Double quantidadePerdaProcedimentoInadequado) {
//        java.lang.Double quantidadePerdaProcedimentoInadequadoOld = this.quantidadePerdaProcedimentoInadequado;
		this.quantidadePerdaProcedimentoInadequado = quantidadePerdaProcedimentoInadequado;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadePerdaProcedimentoInadequado", quantidadePerdaProcedimentoInadequadoOld, quantidadePerdaProcedimentoInadequado);
	}



	/**
	 * Return the value associated with the column: qt_perda_outros_motivos
	 */
	public java.lang.Double getQuantidadePerdaOutrosMotivos () {
		return getPropertyValue(this, quantidadePerdaOutrosMotivos, PROP_QUANTIDADE_PERDA_OUTROS_MOTIVOS); 
	}

	/**
	 * Set the value related to the column: qt_perda_outros_motivos
	 * @param quantidadePerdaOutrosMotivos the qt_perda_outros_motivos value
	 */
	public void setQuantidadePerdaOutrosMotivos (java.lang.Double quantidadePerdaOutrosMotivos) {
//        java.lang.Double quantidadePerdaOutrosMotivosOld = this.quantidadePerdaOutrosMotivos;
		this.quantidadePerdaOutrosMotivos = quantidadePerdaOutrosMotivos;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadePerdaOutrosMotivos", quantidadePerdaOutrosMotivosOld, quantidadePerdaOutrosMotivos);
	}



	/**
	 * Return the value associated with the column: qt_perda_transporte
	 */
	public java.lang.Double getQuantidadePerdaTransporte () {
		return getPropertyValue(this, quantidadePerdaTransporte, PROP_QUANTIDADE_PERDA_TRANSPORTE); 
	}

	/**
	 * Set the value related to the column: qt_perda_transporte
	 * @param quantidadePerdaTransporte the qt_perda_transporte value
	 */
	public void setQuantidadePerdaTransporte (java.lang.Double quantidadePerdaTransporte) {
//        java.lang.Double quantidadePerdaTransporteOld = this.quantidadePerdaTransporte;
		this.quantidadePerdaTransporte = quantidadePerdaTransporte;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadePerdaTransporte", quantidadePerdaTransporteOld, quantidadePerdaTransporte);
	}



	/**
	 * Return the value associated with the column: saldo_atual
	 */
	public java.lang.Double getSaldoAtual () {
		return getPropertyValue(this, saldoAtual, PROP_SALDO_ATUAL); 
	}

	/**
	 * Set the value related to the column: saldo_atual
	 * @param saldoAtual the saldo_atual value
	 */
	public void setSaldoAtual (java.lang.Double saldoAtual) {
//        java.lang.Double saldoAtualOld = this.saldoAtual;
		this.saldoAtual = saldoAtual;
//        this.getPropertyChangeSupport().firePropertyChange ("saldoAtual", saldoAtualOld, saldoAtual);
	}



	/**
	 * Return the value associated with the column: saldo_indisp_anterior
	 */
	public java.lang.Double getSaldoIndisponivelAnterior () {
		return getPropertyValue(this, saldoIndisponivelAnterior, PROP_SALDO_INDISPONIVEL_ANTERIOR); 
	}

	/**
	 * Set the value related to the column: saldo_indisp_anterior
	 * @param saldoIndisponivelAnterior the saldo_indisp_anterior value
	 */
	public void setSaldoIndisponivelAnterior (java.lang.Double saldoIndisponivelAnterior) {
//        java.lang.Double saldoIndisponivelAnteriorOld = this.saldoIndisponivelAnterior;
		this.saldoIndisponivelAnterior = saldoIndisponivelAnterior;
//        this.getPropertyChangeSupport().firePropertyChange ("saldoIndisponivelAnterior", saldoIndisponivelAnteriorOld, saldoIndisponivelAnterior);
	}



	/**
	 * Return the value associated with the column: saldo_indisp_atual
	 */
	public java.lang.Double getSaldoIndisponivelAtual () {
		return getPropertyValue(this, saldoIndisponivelAtual, PROP_SALDO_INDISPONIVEL_ATUAL); 
	}

	/**
	 * Set the value related to the column: saldo_indisp_atual
	 * @param saldoIndisponivelAtual the saldo_indisp_atual value
	 */
	public void setSaldoIndisponivelAtual (java.lang.Double saldoIndisponivelAtual) {
//        java.lang.Double saldoIndisponivelAtualOld = this.saldoIndisponivelAtual;
		this.saldoIndisponivelAtual = saldoIndisponivelAtual;
//        this.getPropertyChangeSupport().firePropertyChange ("saldoIndisponivelAtual", saldoIndisponivelAtualOld, saldoIndisponivelAtual);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_usuario
	 */
	public java.util.Date getDataUsuario () {
		return getPropertyValue(this, dataUsuario, PROP_DATA_USUARIO); 
	}

	/**
	 * Set the value related to the column: dt_usuario
	 * @param dataUsuario the dt_usuario value
	 */
	public void setDataUsuario (java.util.Date dataUsuario) {
//        java.util.Date dataUsuarioOld = this.dataUsuario;
		this.dataUsuario = dataUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuario", dataUsuarioOld, dataUsuario);
	}



	/**
	 * Return the value associated with the column: qt_indisponivel
	 */
	public java.lang.Double getQuantidadeIndisponivel () {
		return getPropertyValue(this, quantidadeIndisponivel, PROP_QUANTIDADE_INDISPONIVEL); 
	}

	/**
	 * Set the value related to the column: qt_indisponivel
	 * @param quantidadeIndisponivel the qt_indisponivel value
	 */
	public void setQuantidadeIndisponivel (java.lang.Double quantidadeIndisponivel) {
//        java.lang.Double quantidadeIndisponivelOld = this.quantidadeIndisponivel;
		this.quantidadeIndisponivel = quantidadeIndisponivel;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeIndisponivel", quantidadeIndisponivelOld, quantidadeIndisponivel);
	}



	/**
	 * Return the value associated with the column: cd_movimentacao_vac
	 */
	public br.com.ksisolucoes.vo.vacina.pni.MovimentacaoVacinas getMovimentacaoVacinas () {
		return getPropertyValue(this, movimentacaoVacinas, PROP_MOVIMENTACAO_VACINAS); 
	}

	/**
	 * Set the value related to the column: cd_movimentacao_vac
	 * @param movimentacaoVacinas the cd_movimentacao_vac value
	 */
	public void setMovimentacaoVacinas (br.com.ksisolucoes.vo.vacina.pni.MovimentacaoVacinas movimentacaoVacinas) {
//        br.com.ksisolucoes.vo.vacina.pni.MovimentacaoVacinas movimentacaoVacinasOld = this.movimentacaoVacinas;
		this.movimentacaoVacinas = movimentacaoVacinas;
//        this.getPropertyChangeSupport().firePropertyChange ("movimentacaoVacinas", movimentacaoVacinasOld, movimentacaoVacinas);
	}



	/**
	 * Return the value associated with the column: cd_produto_vacina
	 */
	public br.com.ksisolucoes.vo.vacina.ProdutoVacina getProdutoVacina () {
		return getPropertyValue(this, produtoVacina, PROP_PRODUTO_VACINA); 
	}

	/**
	 * Set the value related to the column: cd_produto_vacina
	 * @param produtoVacina the cd_produto_vacina value
	 */
	public void setProdutoVacina (br.com.ksisolucoes.vo.vacina.ProdutoVacina produtoVacina) {
//        br.com.ksisolucoes.vo.vacina.ProdutoVacina produtoVacinaOld = this.produtoVacina;
		this.produtoVacina = produtoVacina;
//        this.getPropertyChangeSupport().firePropertyChange ("produtoVacina", produtoVacinaOld, produtoVacina);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vacina.pni.MovimentacaoVacinasItem)) return false;
		else {
			br.com.ksisolucoes.vo.vacina.pni.MovimentacaoVacinasItem movimentacaoVacinasItem = (br.com.ksisolucoes.vo.vacina.pni.MovimentacaoVacinasItem) obj;
			if (null == this.getCodigo() || null == movimentacaoVacinasItem.getCodigo()) return false;
			else return (this.getCodigo().equals(movimentacaoVacinasItem.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}