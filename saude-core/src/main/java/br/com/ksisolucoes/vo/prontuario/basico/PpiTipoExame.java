package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;
import java.util.List;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.entradas.estoque.OrdemCompraItem;
import br.com.ksisolucoes.vo.prontuario.basico.base.BasePpiExame;
import br.com.ksisolucoes.vo.prontuario.basico.base.BasePpiTipoExame;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

public class PpiTipoExame extends BasePpiTipoExame implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public PpiTipoExame () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public PpiTipoExame (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public PpiTipoExame (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.PpiSecretaria ppiSecretaria,
		br.com.ksisolucoes.vo.prontuario.basico.TipoExame tipoExame,
		java.lang.Double vlPpi,
		java.lang.Double vlUsado,
		java.lang.Long stSituacao,
		java.lang.Boolean agendaSemSaldo) {

		super (
			codigo,
			ppiSecretaria,
			tipoExame,
			vlPpi,
			vlUsado,
			stSituacao,
			agendaSemSaldo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

	public boolean isAgendaSemSaldo() {
		return super.getAgendaSemSaldo();
	}

}