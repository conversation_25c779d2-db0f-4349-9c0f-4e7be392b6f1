package br.com.ksisolucoes.vo.prontuario.hospital.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the tipo_prestador_ipe table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="tipo_prestador_ipe"
 */

public abstract class BaseTipoPrestadorIpe extends BaseRootVO implements Serializable {

	public static String REF = "TipoPrestadorIpe";
	public static final String PROP_TIPO_ATIVIDADE_CONTA_HOSPITALAR = "tipoAtividadeContaHospitalar";
	public static final String PROP_TIPO_ATIVIDADE_ATENDIMENTO_COMPLEMENTAR = "tipoAtividadeAtendimentoComplementar";
	public static final String PROP_TIPO = "tipo";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TIPO_ATIVIDADE_CONTA_AMBULATORIAL = "tipoAtividadeContaAmbulatorial";
	public static final String PROP_TIPO_DOCUMENTO = "tipoDocumento";
	public static final String PROP_TIPO_ATIVIDADE_CONSULTA_MEDICA = "tipoAtividadeConsultaMedica";
	public static final String PROP_TIPO_ATIVIDADE_PRONTO_ATENDIMENTO = "tipoAtividadeProntoAtendimento";
	public static final String PROP_DESCRICAO_TIPO = "descricaoTipo";


	// constructors
	public BaseTipoPrestadorIpe () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseTipoPrestadorIpe (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseTipoPrestadorIpe (
		java.lang.Long codigo,
		java.lang.String tipo,
		java.lang.String descricaoTipo,
		java.lang.Long tipoDocumento,
		java.lang.Long tipoAtividadeConsultaMedica,
		java.lang.Long tipoAtividadeAtendimentoComplementar,
		java.lang.Long tipoAtividadeProntoAtendimento,
		java.lang.Long tipoAtividadeContaHospitalar,
		java.lang.Long tipoAtividadeContaAmbulatorial) {

		this.setCodigo(codigo);
		this.setTipo(tipo);
		this.setDescricaoTipo(descricaoTipo);
		this.setTipoDocumento(tipoDocumento);
		this.setTipoAtividadeConsultaMedica(tipoAtividadeConsultaMedica);
		this.setTipoAtividadeAtendimentoComplementar(tipoAtividadeAtendimentoComplementar);
		this.setTipoAtividadeProntoAtendimento(tipoAtividadeProntoAtendimento);
		this.setTipoAtividadeContaHospitalar(tipoAtividadeContaHospitalar);
		this.setTipoAtividadeContaAmbulatorial(tipoAtividadeContaAmbulatorial);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String tipo;
	private java.lang.String descricaoTipo;
	private java.lang.Long tipoDocumento;
	private java.lang.Long tipoAtividadeConsultaMedica;
	private java.lang.Long tipoAtividadeAtendimentoComplementar;
	private java.lang.Long tipoAtividadeProntoAtendimento;
	private java.lang.Long tipoAtividadeContaHospitalar;
	private java.lang.Long tipoAtividadeContaAmbulatorial;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_tipo_prestador_ipe"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_tipo
	 */
	public java.lang.String getTipo () {
		return getPropertyValue(this, tipo, PROP_TIPO); 
	}

	/**
	 * Set the value related to the column: cd_tipo
	 * @param tipo the cd_tipo value
	 */
	public void setTipo (java.lang.String tipo) {
//        java.lang.String tipoOld = this.tipo;
		this.tipo = tipo;
//        this.getPropertyChangeSupport().firePropertyChange ("tipo", tipoOld, tipo);
	}



	/**
	 * Return the value associated with the column: ds_tipo
	 */
	public java.lang.String getDescricaoTipo () {
		return getPropertyValue(this, descricaoTipo, PROP_DESCRICAO_TIPO); 
	}

	/**
	 * Set the value related to the column: ds_tipo
	 * @param descricaoTipo the ds_tipo value
	 */
	public void setDescricaoTipo (java.lang.String descricaoTipo) {
//        java.lang.String descricaoTipoOld = this.descricaoTipo;
		this.descricaoTipo = descricaoTipo;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoTipo", descricaoTipoOld, descricaoTipo);
	}



	/**
	 * Return the value associated with the column: tipo_documento
	 */
	public java.lang.Long getTipoDocumento () {
		return getPropertyValue(this, tipoDocumento, PROP_TIPO_DOCUMENTO); 
	}

	/**
	 * Set the value related to the column: tipo_documento
	 * @param tipoDocumento the tipo_documento value
	 */
	public void setTipoDocumento (java.lang.Long tipoDocumento) {
//        java.lang.Long tipoDocumentoOld = this.tipoDocumento;
		this.tipoDocumento = tipoDocumento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumento", tipoDocumentoOld, tipoDocumento);
	}



	/**
	 * Return the value associated with the column: tp_ativ_consulta_medica
	 */
	public java.lang.Long getTipoAtividadeConsultaMedica () {
		return getPropertyValue(this, tipoAtividadeConsultaMedica, PROP_TIPO_ATIVIDADE_CONSULTA_MEDICA); 
	}

	/**
	 * Set the value related to the column: tp_ativ_consulta_medica
	 * @param tipoAtividadeConsultaMedica the tp_ativ_consulta_medica value
	 */
	public void setTipoAtividadeConsultaMedica (java.lang.Long tipoAtividadeConsultaMedica) {
//        java.lang.Long tipoAtividadeConsultaMedicaOld = this.tipoAtividadeConsultaMedica;
		this.tipoAtividadeConsultaMedica = tipoAtividadeConsultaMedica;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAtividadeConsultaMedica", tipoAtividadeConsultaMedicaOld, tipoAtividadeConsultaMedica);
	}



	/**
	 * Return the value associated with the column: tp_ativ_atendimento_complementar
	 */
	public java.lang.Long getTipoAtividadeAtendimentoComplementar () {
		return getPropertyValue(this, tipoAtividadeAtendimentoComplementar, PROP_TIPO_ATIVIDADE_ATENDIMENTO_COMPLEMENTAR); 
	}

	/**
	 * Set the value related to the column: tp_ativ_atendimento_complementar
	 * @param tipoAtividadeAtendimentoComplementar the tp_ativ_atendimento_complementar value
	 */
	public void setTipoAtividadeAtendimentoComplementar (java.lang.Long tipoAtividadeAtendimentoComplementar) {
//        java.lang.Long tipoAtividadeAtendimentoComplementarOld = this.tipoAtividadeAtendimentoComplementar;
		this.tipoAtividadeAtendimentoComplementar = tipoAtividadeAtendimentoComplementar;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAtividadeAtendimentoComplementar", tipoAtividadeAtendimentoComplementarOld, tipoAtividadeAtendimentoComplementar);
	}



	/**
	 * Return the value associated with the column: tp_ativ_pronto_atendimento
	 */
	public java.lang.Long getTipoAtividadeProntoAtendimento () {
		return getPropertyValue(this, tipoAtividadeProntoAtendimento, PROP_TIPO_ATIVIDADE_PRONTO_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: tp_ativ_pronto_atendimento
	 * @param tipoAtividadeProntoAtendimento the tp_ativ_pronto_atendimento value
	 */
	public void setTipoAtividadeProntoAtendimento (java.lang.Long tipoAtividadeProntoAtendimento) {
//        java.lang.Long tipoAtividadeProntoAtendimentoOld = this.tipoAtividadeProntoAtendimento;
		this.tipoAtividadeProntoAtendimento = tipoAtividadeProntoAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAtividadeProntoAtendimento", tipoAtividadeProntoAtendimentoOld, tipoAtividadeProntoAtendimento);
	}



	/**
	 * Return the value associated with the column: tp_ativ_conta_hospitalar
	 */
	public java.lang.Long getTipoAtividadeContaHospitalar () {
		return getPropertyValue(this, tipoAtividadeContaHospitalar, PROP_TIPO_ATIVIDADE_CONTA_HOSPITALAR); 
	}

	/**
	 * Set the value related to the column: tp_ativ_conta_hospitalar
	 * @param tipoAtividadeContaHospitalar the tp_ativ_conta_hospitalar value
	 */
	public void setTipoAtividadeContaHospitalar (java.lang.Long tipoAtividadeContaHospitalar) {
//        java.lang.Long tipoAtividadeContaHospitalarOld = this.tipoAtividadeContaHospitalar;
		this.tipoAtividadeContaHospitalar = tipoAtividadeContaHospitalar;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAtividadeContaHospitalar", tipoAtividadeContaHospitalarOld, tipoAtividadeContaHospitalar);
	}



	/**
	 * Return the value associated with the column: tp_ativ_conta_ambulatorial
	 */
	public java.lang.Long getTipoAtividadeContaAmbulatorial () {
		return getPropertyValue(this, tipoAtividadeContaAmbulatorial, PROP_TIPO_ATIVIDADE_CONTA_AMBULATORIAL); 
	}

	/**
	 * Set the value related to the column: tp_ativ_conta_ambulatorial
	 * @param tipoAtividadeContaAmbulatorial the tp_ativ_conta_ambulatorial value
	 */
	public void setTipoAtividadeContaAmbulatorial (java.lang.Long tipoAtividadeContaAmbulatorial) {
//        java.lang.Long tipoAtividadeContaAmbulatorialOld = this.tipoAtividadeContaAmbulatorial;
		this.tipoAtividadeContaAmbulatorial = tipoAtividadeContaAmbulatorial;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAtividadeContaAmbulatorial", tipoAtividadeContaAmbulatorialOld, tipoAtividadeContaAmbulatorial);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.hospital.TipoPrestadorIpe)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.hospital.TipoPrestadorIpe tipoPrestadorIpe = (br.com.ksisolucoes.vo.prontuario.hospital.TipoPrestadorIpe) obj;
			if (null == this.getCodigo() || null == tipoPrestadorIpe.getCodigo()) return false;
			else return (this.getCodigo().equals(tipoPrestadorIpe.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}