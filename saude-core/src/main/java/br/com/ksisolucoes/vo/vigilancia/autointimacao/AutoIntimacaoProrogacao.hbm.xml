<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping 
    package="br.com.ksisolucoes.vo.vigilancia.autointimacao"  >
    <class name="AutoIntimacaoProrogacao" table="auto_intimacao_prorogacao">
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_auto_intimacao_prorog"
        >
            <generator class="assigned" />
        </id>
        <version column="version" name="version" type="long" />
        
        <many-to-one 
            class="br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao"
            name="autoIntimacao" not-null="true">
            <column name="cd_auto_intimacao"/>
        </many-to-one>

        <property
            column="dt_prorogada"
            name="dataProrogada"
            not-null="true"
            type="timestamp"
        />

        <property
            column="prazo"
            name="prazo"
            not-null="false"
            type="java.lang.Long"
            length="3"
        />

        <property
            column="ds_observacao_prazo"
            name="observacaoPrazo"
            type="java.lang.String"
            not-null="false"
        />

        <property
            column="dt_usuario"
            name="dataUsuario"
            not-null="true"
            type="timestamp"
        />

        <many-to-one class="br.com.ksisolucoes.vo.controle.Usuario"
                     name="usuario" not-null="true">
            <column name="cd_usuario"/>
        </many-to-one>

    </class>
</hibernate-mapping>