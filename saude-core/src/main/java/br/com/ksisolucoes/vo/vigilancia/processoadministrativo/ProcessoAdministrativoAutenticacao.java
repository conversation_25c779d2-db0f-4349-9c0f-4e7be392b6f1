package br.com.ksisolucoes.vo.vigilancia.processoadministrativo;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.base.BaseProcessoAdministrativoAutenticacao;

import java.io.Serializable;


public class ProcessoAdministrativoAutenticacao extends BaseProcessoAdministrativoAutenticacao implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ProcessoAdministrativoAutenticacao () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ProcessoAdministrativoAutenticacao (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ProcessoAdministrativoAutenticacao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.String chave,
		java.util.Date dataCadastro,
		java.lang.Long situacao) {

		super (
			codigo,
			usuario,
			chave,
			dataCadastro,
			situacao);
	}

/*[CONSTRUCTOR MARKER END]*/


	public static enum Situacao implements IEnum<Situacao> {

		VALIDO(0L, Bundle.getStringApplication("rotulo_valido")),
		INVALIDO(1L, Bundle.getStringApplication("rotulo_invalido"));

		private Long value;
		private String descricao;

		private Situacao(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static Situacao valueOf(Long value) {
			for (Situacao situacao : Situacao.values()) {
				if (situacao.value().equals(value)) {
					return situacao;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return this.value;
		}

		@Override
		public String descricao() {
			return this.descricao;
		}

	}

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}