<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.agravo"  >
    <class name="IntegracaoAgravoNotificacao" table="integracao_agravo">
        <composite-id name="id" class="IntegracaoAgravoPK">
            <key-many-to-one name="numeroAtendimento" class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento" column="nr_atendimento"/>
            <key-property name="cnes" column="cnes" type="java.lang.String"/>
        </composite-id>

        <version column="version" name="version" type="long" />

        <!-- Somente leitura, utilizado para consultas pela Criteria -->
        <property name="respostaStatus" column="resposta_status" type="java.lang.Long" not-null="false" length="4"/>
        <property name="respostaJson" column="resposta_json" type="java.lang.String" not-null="false"/>
        <property name="tipoRequisicao" column="tp_requisicao" type="java.lang.Long" not-null="false" length="1"/>
        <property name="dataIntegracao" column="dt_integracao" type="timestamp" not-null="true"/>

    </class>
</hibernate-mapping>