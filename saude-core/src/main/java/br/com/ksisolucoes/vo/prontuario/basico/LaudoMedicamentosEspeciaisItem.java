package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;

import br.com.ksisolucoes.vo.prontuario.basico.base.BaseLaudoMedicamentosEspeciaisItem;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class LaudoMedicamentosEspeciaisItem extends BaseLaudoMedicamentosEspeciaisItem implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public LaudoMedicamentosEspeciaisItem () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public LaudoMedicamentosEspeciaisItem (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public LaudoMedicamentosEspeciaisItem (
		java.lang.Long codigo,
		java.lang.Long quantidadeSolicitacao1Mes) {

		super (
			codigo,
			quantidadeSolicitacao1Mes);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}