package br.com.ksisolucoes.vo.vacina.pni;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vacina.pni.base.BaseMovimentacaoVacinasItem;

import java.io.Serializable;


public class MovimentacaoVacinasItem extends BaseMovimentacaoVacinasItem implements CodigoManager {
    private static final long serialVersionUID = 1L;

    public enum Status implements IEnum {
        PENDENTE(1L, Bundle.getStringApplication("rotulo_pendente")),
        CONFIRMADO(2L, Bundle.getStringApplication("rotulo_confirmado")),
        IGNORADO(3L, Bundle.getStringApplication("rotulo_ignorado"));

        private Long value;
        private String descricao;

        private Status(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Status valueOf(Long value) {
            for (Status status : Status.values()) {
                if (status.value().equals(value)) {
                    return status;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public MovimentacaoVacinasItem () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public MovimentacaoVacinasItem (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public MovimentacaoVacinasItem (
		java.lang.Long codigo,
		java.lang.Double saldoAnterior,
		java.lang.Double quantidadeRecebidas,
		java.lang.Double quantidadeDistribuidas,
		java.lang.Double quantidadeUtilizadas,
		java.lang.Double quantidadeTransferidas,
		java.lang.Double quantidadeQuebradas,
		java.lang.Double quantidadePerdaFaltaEnergia,
		java.lang.Double quantidadePerdaFalhaEquipamento,
		java.lang.Double quantidadePerdaValidadeVencida,
		java.lang.Double quantidadePerdaProcedimentoInadequado,
		java.lang.Double quantidadePerdaOutrosMotivos,
		java.lang.Double quantidadePerdaTransporte,
		java.lang.Double saldoAtual,
		java.lang.Double saldoIndisponivelAnterior,
		java.lang.Double saldoIndisponivelAtual,
		java.lang.Long status,
		java.util.Date dataCadastro,
		java.util.Date dataUsuario) {

		super (
			codigo,
			saldoAnterior,
			quantidadeRecebidas,
			quantidadeDistribuidas,
			quantidadeUtilizadas,
			quantidadeTransferidas,
			quantidadeQuebradas,
			quantidadePerdaFaltaEnergia,
			quantidadePerdaFalhaEquipamento,
			quantidadePerdaValidadeVencida,
			quantidadePerdaProcedimentoInadequado,
			quantidadePerdaOutrosMotivos,
			quantidadePerdaTransporte,
			saldoAtual,
			saldoIndisponivelAnterior,
			saldoIndisponivelAtual,
			status,
			dataCadastro,
			dataUsuario);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDescricaoStatus() {
        Status status = Status.valueOf(getStatus());
        if (status != null && status.descricao() != null) {
            return status.descricao();
        }
        return null;
    }

    public Double getTotal() {
		Double total = Coalesce.asDouble(getSaldoAtual()) + Coalesce.asDouble(getSaldoIndisponivelAtual());
		return total;
	}
}