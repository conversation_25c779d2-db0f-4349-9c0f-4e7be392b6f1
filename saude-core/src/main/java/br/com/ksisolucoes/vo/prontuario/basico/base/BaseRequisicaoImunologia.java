package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the requisicao_imunologia table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="requisicao_imunologia"
 */

public abstract class BaseRequisicaoImunologia extends BaseRootVO implements Serializable {

	public static String REF = "RequisicaoImunologia";
	public static final String PROP_CONTROLE_TRATAMENTO = "controleTratamento";
	public static final String PROP_SOMA_OUTROS_EXAMES = "somaOutrosExames";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_NUMERO_NOTIFICACAO = "numeroNotificacao";
	public static final String PROP_GESTANTE = "gestante";
	public static final String PROP_EXAME_REQUISICAO = "exameRequisicao";
	public static final String PROP_SINTOMATICO = "sintomatico";
	public static final String PROP_SOMA_EXAME_PRE_NATAL = "somaExamePreNatal";


	// constructors
	public BaseRequisicaoImunologia () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRequisicaoImunologia (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRequisicaoImunologia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao) {

		this.setCodigo(codigo);
		this.setExameRequisicao(exameRequisicao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String numeroNotificacao;
	private java.lang.Long gestante;
	private java.lang.Long controleTratamento;
	private java.lang.Long sintomatico;
	private java.lang.Long somaExamePreNatal;
	private java.lang.Long somaOutrosExames;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_requisicao_imunologia"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: num_notificacao
	 */
	public java.lang.String getNumeroNotificacao () {
		return getPropertyValue(this, numeroNotificacao, PROP_NUMERO_NOTIFICACAO); 
	}

	/**
	 * Set the value related to the column: num_notificacao
	 * @param numeroNotificacao the num_notificacao value
	 */
	public void setNumeroNotificacao (java.lang.String numeroNotificacao) {
//        java.lang.String numeroNotificacaoOld = this.numeroNotificacao;
		this.numeroNotificacao = numeroNotificacao;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroNotificacao", numeroNotificacaoOld, numeroNotificacao);
	}



	/**
	 * Return the value associated with the column: gestante
	 */
	public java.lang.Long getGestante () {
		return getPropertyValue(this, gestante, PROP_GESTANTE); 
	}

	/**
	 * Set the value related to the column: gestante
	 * @param gestante the gestante value
	 */
	public void setGestante (java.lang.Long gestante) {
//        java.lang.Long gestanteOld = this.gestante;
		this.gestante = gestante;
//        this.getPropertyChangeSupport().firePropertyChange ("gestante", gestanteOld, gestante);
	}



	/**
	 * Return the value associated with the column: controle_tratamento
	 */
	public java.lang.Long getControleTratamento () {
		return getPropertyValue(this, controleTratamento, PROP_CONTROLE_TRATAMENTO); 
	}

	/**
	 * Set the value related to the column: controle_tratamento
	 * @param controleTratamento the controle_tratamento value
	 */
	public void setControleTratamento (java.lang.Long controleTratamento) {
//        java.lang.Long controleTratamentoOld = this.controleTratamento;
		this.controleTratamento = controleTratamento;
//        this.getPropertyChangeSupport().firePropertyChange ("controleTratamento", controleTratamentoOld, controleTratamento);
	}



	/**
	 * Return the value associated with the column: sintomatico
	 */
	public java.lang.Long getSintomatico () {
		return getPropertyValue(this, sintomatico, PROP_SINTOMATICO); 
	}

	/**
	 * Set the value related to the column: sintomatico
	 * @param sintomatico the sintomatico value
	 */
	public void setSintomatico (java.lang.Long sintomatico) {
//        java.lang.Long sintomaticoOld = this.sintomatico;
		this.sintomatico = sintomatico;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomatico", sintomaticoOld, sintomatico);
	}



	/**
	 * Return the value associated with the column: soma_exame_pre_natal
	 */
	public java.lang.Long getSomaExamePreNatal () {
		return getPropertyValue(this, somaExamePreNatal, PROP_SOMA_EXAME_PRE_NATAL); 
	}

	/**
	 * Set the value related to the column: soma_exame_pre_natal
	 * @param somaExamePreNatal the soma_exame_pre_natal value
	 */
	public void setSomaExamePreNatal (java.lang.Long somaExamePreNatal) {
//        java.lang.Long somaExamePreNatalOld = this.somaExamePreNatal;
		this.somaExamePreNatal = somaExamePreNatal;
//        this.getPropertyChangeSupport().firePropertyChange ("somaExamePreNatal", somaExamePreNatalOld, somaExamePreNatal);
	}



	/**
	 * Return the value associated with the column: soma_outros_exames
	 */
	public java.lang.Long getSomaOutrosExames () {
		return getPropertyValue(this, somaOutrosExames, PROP_SOMA_OUTROS_EXAMES); 
	}

	/**
	 * Set the value related to the column: soma_outros_exames
	 * @param somaOutrosExames the soma_outros_exames value
	 */
	public void setSomaOutrosExames (java.lang.Long somaOutrosExames) {
//        java.lang.Long somaOutrosExamesOld = this.somaOutrosExames;
		this.somaOutrosExames = somaOutrosExames;
//        this.getPropertyChangeSupport().firePropertyChange ("somaOutrosExames", somaOutrosExamesOld, somaOutrosExames);
	}



	/**
	 * Return the value associated with the column: cd_exame_requisicao
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao getExameRequisicao () {
		return getPropertyValue(this, exameRequisicao, PROP_EXAME_REQUISICAO); 
	}

	/**
	 * Set the value related to the column: cd_exame_requisicao
	 * @param exameRequisicao the cd_exame_requisicao value
	 */
	public void setExameRequisicao (br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao) {
//        br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicaoOld = this.exameRequisicao;
		this.exameRequisicao = exameRequisicao;
//        this.getPropertyChangeSupport().firePropertyChange ("exameRequisicao", exameRequisicaoOld, exameRequisicao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.RequisicaoImunologia)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.RequisicaoImunologia requisicaoImunologia = (br.com.ksisolucoes.vo.prontuario.basico.RequisicaoImunologia) obj;
			if (null == this.getCodigo() || null == requisicaoImunologia.getCodigo()) return false;
			else return (this.getCodigo().equals(requisicaoImunologia.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}