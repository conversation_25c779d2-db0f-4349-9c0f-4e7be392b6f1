package br.com.ksisolucoes.vo.vigilancia.requerimentos;

import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoVistoriaPBAConformidadeTecnicaResposta;

import java.io.Serializable;



public class RequerimentoVistoriaPBAConformidadeTecnicaResposta extends BaseRequerimentoVistoriaPBAConformidadeTecnicaResposta implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequerimentoVistoriaPBAConformidadeTecnicaResposta () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequerimentoVistoriaPBAConformidadeTecnicaResposta (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequerimentoVistoriaPBAConformidadeTecnicaResposta (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaPBAConformidadeTecnica requerimentoVistoriaPBAConformidadeTecnica,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataResposta,
		java.util.Date dataUsuario) {

		super (
			codigo,
			requerimentoVistoriaPBAConformidadeTecnica,
			usuario,
			dataResposta,
			dataUsuario);
	}

/*[CONSTRUCTOR MARKER END]*/


	public enum Situacao implements IEnum {
		CADASTRADO(0L, Bundle.getStringApplication("rotulo_cadastrado")),
		ENVIADO(1L, Bundle.getStringApplication("rotulo_enviado")),
		;

		private Long value;
		private String descricao;

		private Situacao(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static Situacao valeuOf(Long value) {
			for (Situacao situacao : Situacao.values()) {
				if (situacao.value().equals(value)) {
					return situacao;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}

	}


	public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

	@Override
	public String getDescricaoResposta() {
		return StringUtil.limparHtml(super.getDescricaoResposta());
	}

	@Override
	public void setDescricaoResposta(String descricaoResposta) {
		super.setDescricaoResposta(StringUtil.limparHtml(descricaoResposta));
	}

	public String getDescricaoRespostaSemHtml() {
		return StringUtil.removeHtmlString(getDescricaoResposta());
	}
}