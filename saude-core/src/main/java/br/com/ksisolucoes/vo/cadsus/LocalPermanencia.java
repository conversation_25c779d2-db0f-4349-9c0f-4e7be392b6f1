package br.com.ksisolucoes.vo.cadsus;

import java.io.Serializable;

import br.com.ksisolucoes.vo.cadsus.base.BaseLocalPermanencia;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;

public class LocalPermanencia extends BaseLocalPermanencia implements PesquisaObjectInterface, CodigoManager {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public LocalPermanencia() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public LocalPermanencia(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public LocalPermanencia(
            java.lang.Long codigo,
            java.lang.String descricao) {

        super(
                codigo,
                descricao);
    }

    /*[CONSTRUCTOR MARKER END]*/
    @Override
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    @Override
    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
    public String getDescricaoVO() {
        return this.getDescricao();
    }

    @Override
    public String getIdentificador() {
        return this.getCodigo().toString();
    }
}