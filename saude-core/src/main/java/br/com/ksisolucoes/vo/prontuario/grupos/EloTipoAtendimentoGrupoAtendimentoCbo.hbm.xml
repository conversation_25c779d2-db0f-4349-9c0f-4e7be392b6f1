<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.grupos"  >
    <class name="EloTipoAtendimentoGrupoAtendimentoCbo" table="elo_tipo_atend_grupo_atend_cbo">
        <id
            column="cd_elo"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo"
            column="cd_grupo_atend_cbo"
            name="grupoAtendimentoCbo"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"
            column="cd_tp_atendimento"
            name="tipoAtendimento"
            not-null="true"
        />
        
		<property
			name="exigeEvolucao"
			column="exige_evolucao"
			type="java.lang.Long"
		/>
		
                <property
			name="exigeEncaminhamento"
			column="exige_encaminhamento"
			type="java.lang.Long"
		/>
                 
        <property 
            name="validaPrescricaoInterna"
            column="valida_presc_interna"
            type="java.lang.Long"
        />

        <property 
            name="validaPreNatal"
            column="valida_pre_natal"
            type="java.lang.Long"
            not-null="true"
        />
        
                      
        <property
            column="valida_aih_alta"
            name="validarAihAlta"
            type="java.lang.Long"
        />

    </class>
</hibernate-mapping>
