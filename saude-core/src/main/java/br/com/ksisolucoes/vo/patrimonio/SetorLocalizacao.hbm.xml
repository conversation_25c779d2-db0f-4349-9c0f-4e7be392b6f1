<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
	
<hibernate-mapping package="br.com.ksisolucoes.vo.patrimonio"  >
	<class 
		name="SetorLocalizacao"
		table="setor_localizacao"
	>

        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_localizacao"
        >
                <generator class="assigned"/>
        </id> <version column="version" name="version" type="long" />

		<many-to-one
         	name="setor"
         	class="br.com.ksisolucoes.vo.patrimonio.Setor"
         >
         	<column name="cd_setor"/>
        </many-to-one>

        <property
                name="descricao"
                column="ds_localizacao"
                type="java.lang.String"
                not-null="true"
        />
	</class>
</hibernate-mapping>