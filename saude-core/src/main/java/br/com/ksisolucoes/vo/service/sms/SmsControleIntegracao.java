package br.com.ksisolucoes.vo.service.sms;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.service.sms.base.BaseSmsControleIntegracao;

import java.io.Serializable;

public class SmsControleIntegracao extends BaseSmsControleIntegracao implements CodigoManager {

    private static final long serialVersionUID = 1L;
    public static final String PROP_DESCRICAO_STATUS_SMS = "descricaoStatusSms";
    public static final String PROP_DESCRICAO_TIPO_MENSAGEM = "descricaoTipoMensagem";

    public enum StatusMensagemWhatsAppEnum {
        QUEUED("queued"),
        SENT("sent"),
        FAILED("failed"),
        DELIVERED("delivered"),
        UNDELIVERED("undelivered"),
        READ("read"),
        RESPONDIDO("respondido"),
        ;

        StatusMensagemWhatsAppEnum(String descricao) {
            this.descricao = descricao;
        }

        private final String descricao;

        public String descricao() {
            return descricao;
        }

        public static StatusMensagemWhatsAppEnum getByDescricao(String descricao) {
            for (StatusMensagemWhatsAppEnum statusExame : StatusMensagemWhatsAppEnum.values()) {
                if (statusExame.descricao.equals(descricao)) {
                    return statusExame;
                }
            }
            return null;
        }

    }

    public enum PlataformaMensagem {
        SMS(1L),
        WHATSAPP(2L),
        TWILIO(3L)
        ;

        private PlataformaMensagem(Long value) {
            this.value = value;
        }

        private final Long value;

        public Long value() {
            return value;
        }
    }

    public enum StatusSms implements IEnum {

        ENCAMINHADO(0L, Bundle.getStringApplication("rotulo_encaminhado")),
        ENVIADO(1L, Bundle.getStringApplication("rotulo_enviado")),
        ERRO(2L, Bundle.getStringApplication("rotulo_erro")),
        REENVIAR(3L, Bundle.getStringApplication("rotulo_reenviar")),
        CANCELADO(4L, Bundle.getStringApplication("rotulo_cancelado"));
        private Long value;
        private String descricao;

        private StatusSms(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static StatusSms valueOf(Long value) {
            for (StatusSms statusSms : StatusSms.values()) {
                if (statusSms.value().equals(value)) {
                    return statusSms;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    public enum TipoMensagem implements IEnum {

        AVISO_AGENDAMENTO(0L, Bundle.getStringApplication("rotulo_aviso_agendamento")),
        CONFIRMACAO_AGENDAMENTO(1L, Bundle.getStringApplication("rotulo_confirmacao_agendamento")),
        REAVISO_AGENDAMENTO(2L, Bundle.getStringApplication("rotulo_reaviso_agendamento")),
        CONFIRMACAO_RETORNO(3L, Bundle.getStringApplication("rotulo_confirmacao_retorno")),
        AVISO_AGENDAMENTO_LOCAL(4L, Bundle.getStringApplication("rotulo_aviso_agendamento")),
        CANCELAMENTO_AGENDAMENTO(5L, Bundle.getStringApplication("rotulo_cancelamento_agendamento")),
        REMANEJAMENTO_AGENDAMENTO(6L, Bundle.getStringApplication("rotulo_remanejamento_agendamento")),
        MENSAGEM_SMS(7L, Bundle.getStringApplication("rotulo_mensagem_sms")),
        AVISO_AGENDAMENTO_ROTEIRO_VIAGEM(8L,Bundle.getStringApplication("rotulo_aviso_agendamento_veiculo")),
        INCLUSAO_FILA_ESPERA(9L, Bundle.getStringApplication("rotulo_inclusao_fila_espera")),
        AVISO_RESULTADO_EXAME(10L,Bundle.getStringApplication("rotulo_aviso_resultado_exame"))
        ;

        private Long value;
        private String descricao;

        private TipoMensagem(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static TipoMensagem valueOf(Long value) {
            for (TipoMensagem tipoMensagem : TipoMensagem.values()) {
                if (tipoMensagem.value().equals(value)) {
                    return tipoMensagem;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    public enum StatusResposta implements IEnum {
        AGUARDANDO(0L, Bundle.getStringApplication("rotulo_aguardando")),
        PROCESSADA(1L, Bundle.getStringApplication("rotulo_processada"));

        private Long value;
        private String descricao;

        private StatusResposta(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static StatusResposta valueOf(Long value) {
            for (StatusResposta statusResposta : StatusResposta.values()) {
                if (statusResposta.value().equals(value)) {
                    return statusResposta;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

    public enum TipoErro implements IEnum<TipoErro> {

        ERRO_RESPOSTA(0L, Bundle.getStringApplication("rotulo_resposta")),
        ERRO_ENVIO(1L, Bundle.getStringApplication("rotulo_envio"));

        private Long value;
        private String descricao;

        private TipoErro(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static TipoErro valueOf(Long value) {
            for (TipoErro tipoErro : TipoErro.values()) {
                if (tipoErro.value().equals(value)) {
                    return tipoErro;
                }
            }
            return null;
        }

    }

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public SmsControleIntegracao () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public SmsControleIntegracao (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public SmsControleIntegracao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.service.sms.SmsMensagem smsMensagem,
		java.lang.Long tipoMensagem,
		java.lang.Long statusSms) {

		super (
			codigo,
			smsMensagem,
			tipoMensagem,
			statusSms);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDescricaoStatusSms() {
        StatusSms statusSms = StatusSms.valueOf(getStatusSms());
        if (statusSms != null && statusSms.descricao != null) {
            return statusSms.descricao();
        }
        return "";
    }

    public String getDescricaoTipoMensagem() {
        TipoMensagem tipoMensagem = TipoMensagem.valueOf(getTipoMensagem());
        if (tipoMensagem != null && tipoMensagem.descricao != null) {
            return tipoMensagem.descricao();
        }
        return "";
    }

    public String getDescricaoTipoErro() {
        TipoErro tipoErro = TipoErro.valueOf(getTipoErro());
        if (tipoErro != null && tipoErro.descricao != null) {
            return tipoErro.descricao();
        }
        return "";
    }

    public String getDescricaoStatusResposta() {
        StatusResposta statusResposta = StatusResposta.valueOf(getStatusResposta());
        if (statusResposta != null && statusResposta.descricao != null) {
            return statusResposta.descricao();
        }
        return "";
    }

}
