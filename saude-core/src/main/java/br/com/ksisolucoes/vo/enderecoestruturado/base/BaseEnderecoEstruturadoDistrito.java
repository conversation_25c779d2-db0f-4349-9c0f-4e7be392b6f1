package br.com.ksisolucoes.vo.enderecoestruturado.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the end_estruturado_distrito table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="end_estruturado_distrito"
 */

public abstract class BaseEnderecoEstruturadoDistrito extends BaseRootVO implements Serializable {

	public static String REF = "EnderecoEstruturadoDistrito";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_USUARIO_ALTERACAO = "usuarioAlteracao";
	public static final String PROP_DATA_ALTERACAO = "dataAlteracao";


	// constructors
	public BaseEnderecoEstruturadoDistrito () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEnderecoEstruturadoDistrito (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseEnderecoEstruturadoDistrito (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuarioAlteracao) {

		this.setCodigo(codigo);
		this.setUsuarioAlteracao(usuarioAlteracao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.util.Date dataAlteracao;

	// many to one
	private br.com.ksisolucoes.vo.controle.Usuario usuarioAlteracao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  column="cd_end_estruturado_distrito"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: descricao
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: descricao
	 * @param descricao the descricao value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: dt_alteracao
	 */
	public java.util.Date getDataAlteracao () {
		return getPropertyValue(this, dataAlteracao, PROP_DATA_ALTERACAO); 
	}

	/**
	 * Set the value related to the column: dt_alteracao
	 * @param dataAlteracao the dt_alteracao value
	 */
	public void setDataAlteracao (java.util.Date dataAlteracao) {
//        java.util.Date dataAlteracaoOld = this.dataAlteracao;
		this.dataAlteracao = dataAlteracao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAlteracao", dataAlteracaoOld, dataAlteracao);
	}



	/**
	 * Return the value associated with the column: cd_usuario_alteracao
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioAlteracao () {
		return getPropertyValue(this, usuarioAlteracao, PROP_USUARIO_ALTERACAO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_alteracao
	 * @param usuarioAlteracao the cd_usuario_alteracao value
	 */
	public void setUsuarioAlteracao (br.com.ksisolucoes.vo.controle.Usuario usuarioAlteracao) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioAlteracaoOld = this.usuarioAlteracao;
		this.usuarioAlteracao = usuarioAlteracao;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioAlteracao", usuarioAlteracaoOld, usuarioAlteracao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.enderecoestruturado.EnderecoEstruturadoDistrito)) return false;
		else {
			br.com.ksisolucoes.vo.enderecoestruturado.EnderecoEstruturadoDistrito enderecoEstruturadoDistrito = (br.com.ksisolucoes.vo.enderecoestruturado.EnderecoEstruturadoDistrito) obj;
			if (null == this.getCodigo() || null == enderecoEstruturadoDistrito.getCodigo()) return false;
			else return (this.getCodigo().equals(enderecoEstruturadoDistrito.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}