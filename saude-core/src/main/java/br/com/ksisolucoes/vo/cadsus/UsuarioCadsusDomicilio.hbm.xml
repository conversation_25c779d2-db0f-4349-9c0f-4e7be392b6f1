<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.cadsus"  >
<class name="UsuarioCadsusDomicilio" table="usuario_cadsus_domicilio">

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_usu_cadsus_dom"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />
    
        <many-to-one
            name="usuarioCadsus"
            class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"
            column="cd_usu_cadsus"
        />

        <many-to-one
            name="enderecoDomicilio"
            class="br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio"
            column="cd_domicilio"
        />

    	<many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario"
                name="usuario"
                not-null="true"
            />
    
        <property
            column="status"
            name="status"
            not-null="true"
            type="java.lang.Long"
            />

        <property
            column="dt_cadastro"
            name="dataCadastro"
            not-null="true"
            type="java.util.Date"
            />

        <property
            column="dt_usuario"
            name="dataUsuario"
            not-null="true"
            type="java.util.Date"
            />

    	<many-to-one
                class="br.com.ksisolucoes.vo.siab.SiabOcupacao"
                column="cd_ocupacao"
                name="siabOcupacao"
                not-null="false"
            />
        
        <property
            column="prontuario"
            name="prontuario"
            not-null="false"
            type="java.lang.String"
            length="30"
        />
        
        
        <property
            column="motivo_exclusao"
            name="motivoExclusao"
            not-null="false"
            type="java.lang.Long"
        />

    
  </class>
    </hibernate-mapping>
