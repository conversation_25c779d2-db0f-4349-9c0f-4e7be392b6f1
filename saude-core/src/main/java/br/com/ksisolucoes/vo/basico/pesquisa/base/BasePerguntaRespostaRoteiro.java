package br.com.ksisolucoes.vo.basico.pesquisa.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the pergunta_resposta_roteiro table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="pergunta_resposta_roteiro"
 */

public abstract class BasePerguntaRespostaRoteiro extends BaseRootVO implements Serializable {

	public static String REF = "PerguntaRespostaRoteiro";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_PERGUNTA_ROTEIRO = "perguntaRoteiro";


	// constructors
	public BasePerguntaRespostaRoteiro () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BasePerguntaRespostaRoteiro (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BasePerguntaRespostaRoteiro (
		java.lang.Long codigo,
		java.lang.String descricao) {

		this.setCodigo(codigo);
		this.setDescricao(descricao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;

	// many to one
	private br.com.ksisolucoes.vo.basico.pesquisa.PerguntaRoteiro perguntaRoteiro;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_pergunta_resposta_roteiro"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_resposta
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_resposta
	 * @param descricao the ds_resposta value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: cd_pergunta_roteiro
	 */
	public br.com.ksisolucoes.vo.basico.pesquisa.PerguntaRoteiro getPerguntaRoteiro () {
		return getPropertyValue(this, perguntaRoteiro, PROP_PERGUNTA_ROTEIRO); 
	}

	/**
	 * Set the value related to the column: cd_pergunta_roteiro
	 * @param perguntaRoteiro the cd_pergunta_roteiro value
	 */
	public void setPerguntaRoteiro (br.com.ksisolucoes.vo.basico.pesquisa.PerguntaRoteiro perguntaRoteiro) {
//        br.com.ksisolucoes.vo.basico.pesquisa.PerguntaRoteiro perguntaRoteiroOld = this.perguntaRoteiro;
		this.perguntaRoteiro = perguntaRoteiro;
//        this.getPropertyChangeSupport().firePropertyChange ("perguntaRoteiro", perguntaRoteiroOld, perguntaRoteiro);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.pesquisa.PerguntaRespostaRoteiro)) return false;
		else {
			br.com.ksisolucoes.vo.basico.pesquisa.PerguntaRespostaRoteiro perguntaRespostaRoteiro = (br.com.ksisolucoes.vo.basico.pesquisa.PerguntaRespostaRoteiro) obj;
			if (null == this.getCodigo() || null == perguntaRespostaRoteiro.getCodigo()) return false;
			else return (this.getCodigo().equals(perguntaRespostaRoteiro.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}