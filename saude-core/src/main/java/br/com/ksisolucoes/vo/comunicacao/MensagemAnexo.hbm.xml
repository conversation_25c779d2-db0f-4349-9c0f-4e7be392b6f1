<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.comunicacao"  >
    <class name="MensagemAnexo" table="mensagem_anexo">
        <id
            column="cd_mensagem_anexo"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.comunicacao.Mensagem"
            name="mensagem"
            not-null="true"
        >
            <column name="cd_mensagem" />
        </many-to-one>                
		
        <many-to-one
            class="br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo"
            name="gerenciadorArquivo"
            not-null="true"
        >
            <column name="cd_gerenciador_arquivo" />
        </many-to-one>
                
    </class>
</hibernate-mapping>
