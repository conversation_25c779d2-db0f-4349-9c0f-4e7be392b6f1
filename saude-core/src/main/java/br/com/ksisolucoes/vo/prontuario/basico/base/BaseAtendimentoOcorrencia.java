package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the atendimento_ocorrencia table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="atendimento_ocorrencia"
 */

public abstract class BaseAtendimentoOcorrencia extends BaseRootVO implements Serializable {

	public static String REF = "AtendimentoOcorrencia";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_DATA = "data";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TIPO_OCORRENCIA = "tipoOcorrencia";
	public static final String PROP_DESCRICAO_OCORRENCIA = "descricaoOcorrencia";
	public static final String PROP_ATENDIMENTO = "atendimento";


	// constructors
	public BaseAtendimentoOcorrencia () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAtendimentoOcorrencia (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAtendimentoOcorrencia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {

		this.setCodigo(codigo);
		this.setAtendimento(atendimento);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date data;
	private java.lang.String tipoOcorrencia;
	private java.lang.String descricaoOcorrencia;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  column="cd_atendimento_ocorrencia"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: date
	 */
	public java.util.Date getData () {
		return getPropertyValue(this, data, PROP_DATA); 
	}

	/**
	 * Set the value related to the column: date
	 * @param data the date value
	 */
	public void setData (java.util.Date data) {
//        java.util.Date dataOld = this.data;
		this.data = data;
//        this.getPropertyChangeSupport().firePropertyChange ("data", dataOld, data);
	}



	/**
	 * Return the value associated with the column: tipo_ocorrencia
	 */
	public java.lang.String getTipoOcorrencia () {
		return getPropertyValue(this, tipoOcorrencia, PROP_TIPO_OCORRENCIA); 
	}

	/**
	 * Set the value related to the column: tipo_ocorrencia
	 * @param tipoOcorrencia the tipo_ocorrencia value
	 */
	public void setTipoOcorrencia (java.lang.String tipoOcorrencia) {
//        java.lang.String tipoOcorrenciaOld = this.tipoOcorrencia;
		this.tipoOcorrencia = tipoOcorrencia;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoOcorrencia", tipoOcorrenciaOld, tipoOcorrencia);
	}



	/**
	 * Return the value associated with the column: ds_ocorrencia
	 */
	public java.lang.String getDescricaoOcorrencia () {
		return getPropertyValue(this, descricaoOcorrencia, PROP_DESCRICAO_OCORRENCIA); 
	}

	/**
	 * Set the value related to the column: ds_ocorrencia
	 * @param descricaoOcorrencia the ds_ocorrencia value
	 */
	public void setDescricaoOcorrencia (java.lang.String descricaoOcorrencia) {
//        java.lang.String descricaoOcorrenciaOld = this.descricaoOcorrencia;
		this.descricaoOcorrencia = descricaoOcorrencia;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoOcorrencia", descricaoOcorrenciaOld, descricaoOcorrencia);
	}



	/**
	 * Return the value associated with the column: nr_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimento () {
		return getPropertyValue(this, atendimento, PROP_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: nr_atendimento
	 * @param atendimento the nr_atendimento value
	 */
	public void setAtendimento (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOld = this.atendimento;
		this.atendimento = atendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimento", atendimentoOld, atendimento);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOcorrencia)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOcorrencia atendimentoOcorrencia = (br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOcorrencia) obj;
			if (null == this.getCodigo() || null == atendimentoOcorrencia.getCodigo()) return false;
			else return (this.getCodigo().equals(atendimentoOcorrencia.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}