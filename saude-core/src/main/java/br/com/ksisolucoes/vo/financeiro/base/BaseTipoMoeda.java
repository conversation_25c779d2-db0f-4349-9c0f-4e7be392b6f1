package br.com.ksisolucoes.vo.financeiro.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;



/**
 * This class has been automatically generated by Hibernate Synchronizer.
 * For more information or documentation, visit The Hibernate Synchronizer page
 * at http://www.binamics.com/hibernatesync or contact <PERSON> at <EMAIL>.
 *
 * Este objeto est relacionado com a tabela tipo_moeda.
 * No modifique esta classe, pois, sincronizaes com a 
 * base sobrescrevero as alteraes.
 *
 * @hibernate.class
 *  table="tipo_moeda"
 */
public abstract class BaseTipoMoeda  extends BaseRootVO implements Serializable, ValidacaoExceptionInterface {

	public static String PROP_SIGLA = "sigla";
	public static String PROP_REAJUSTA = "reajusta";
	public static String PROP_CODIGO = "codigo";
	public static String PROP_MENSAL_DIARIO = "mensalDiario";
	public static String PROP_DESCRICAO = "descricao";

	public RetornoValidacao retornoValidacao = new RetornoValidacao();

	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long  codigo;

	// fields
	private java.lang.String descricao;
	private java.lang.String mensalDiario;
	private java.lang.String reajusta;
	private java.lang.String sigla;


	// construtores
	public BaseTipoMoeda () {}

	/**
	 * Construtor para a chave primria.
	 */
	public BaseTipoMoeda (java.lang.Long codigo) {
		this.setCodigo(codigo);
	}

	/**
	 * Construtor para os atributos requeridos.
	 */
	public BaseTipoMoeda (
		java.lang.Long codigo,
		java.lang.String descricao,
		java.lang.String reajusta,
		java.lang.String sigla) {

		this.setCodigo(codigo);
		this.setDescricao(descricao);
		this.setReajusta(reajusta);
		this.setSigla(sigla);
	}



	/**
	 * Retorna o identificador nico da classe.
	 *
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cod_moeda"
     *
	 * @return codigo
	 */
	public java.lang.Long getCodigo() {
		return codigo;
	}

	/**
	 * Seta o identificador nico da classe.
	 *
	 * @param codigo para o aributo codigo
	 */
	public void setCodigo(java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}


    /**
     * Retorna o valor do atributo descricao
     *
     * @return descricao
     */
	public java.lang.String getDescricao() {
		return descricao;
	}

	/**
	 * Setar um valor para o atributo: descricao.
     *
	 * @param descricao valor para o atributo descricao.
	 */
	public void setDescricao(java.lang.String descricao) {
		this.descricao = descricao;
	}


    /**
     * Retorna o valor do atributo mensalDiario
     *
     * @return mensalDiario
     */
	public java.lang.String getMensalDiario() {
		return mensalDiario;
	}

	/**
	 * Setar um valor para o atributo: mensalDiario.
     *
	 * @param mensalDiario valor para o atributo mensalDiario.
	 */
	public void setMensalDiario(java.lang.String mensalDiario) {
		this.mensalDiario = mensalDiario;
	}


    /**
     * Retorna o valor do atributo reajusta
     *
     * @return reajusta
     */
	public java.lang.String getReajusta() {
		return reajusta;
	}

	/**
	 * Setar um valor para o atributo: reajusta.
     *
	 * @param reajusta valor para o atributo reajusta.
	 */
	public void setReajusta(java.lang.String reajusta) {
		this.reajusta = reajusta;
	}


    /**
     * Retorna o valor do atributo sigla
     *
     * @return sigla
     */
	public java.lang.String getSigla() {
		return sigla;
	}

	/**
	 * Setar um valor para o atributo: sigla.
     *
	 * @param sigla valor para o atributo sigla.
	 */
	public void setSigla(java.lang.String sigla) {
		this.sigla = sigla;
	}


	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.financeiro.base.BaseTipoMoeda)) return false;
		else {
			br.com.ksisolucoes.vo.financeiro.base.BaseTipoMoeda mObj = (br.com.ksisolucoes.vo.financeiro.base.BaseTipoMoeda) obj;
			if (null == this.getCodigo() || null == mObj.getCodigo()) return false;
			else return (this.getCodigo().equals(mObj.getCodigo()));
		}
	}


	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}



    /* (non-Javadoc)
     * @see br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface#getRetornoValidacao()
     */
    public RetornoValidacao getRetornoValidacao() {
        return this.retornoValidacao;
    }

   public String toString() {
       StringBuffer s = new StringBuffer();
   // primary key
       s.append("codigo: " + codigo + ".\n");

   // fields
       s.append("descricao: " + descricao + ".\n");
       s.append("mensalDiario: " + mensalDiario + ".\n");
       s.append("reajusta: " + reajusta + ".\n");
       s.append("sigla: " + sigla + ".\n");

       return s.toString();
   }


}