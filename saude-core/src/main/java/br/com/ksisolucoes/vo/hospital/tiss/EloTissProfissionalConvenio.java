package br.com.ksisolucoes.vo.hospital.tiss;

import java.io.Serializable;

import br.com.ksisolucoes.vo.hospital.tiss.base.BaseEloTissProfissionalConvenio;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

public class EloTissProfissionalConvenio extends BaseEloTissProfissionalConvenio implements CodigoManager {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public EloTissProfissionalConvenio() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public EloTissProfissionalConvenio(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public EloTissProfissionalConvenio(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.cadsus.Profissional profissional,
            br.com.ksisolucoes.vo.prontuario.basico.Convenio convenio,
            java.lang.String codigoContratadoExecutante) {

        super(
                codigo,
                profissional,
                convenio,
                codigoContratadoExecutante);
    }

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}
