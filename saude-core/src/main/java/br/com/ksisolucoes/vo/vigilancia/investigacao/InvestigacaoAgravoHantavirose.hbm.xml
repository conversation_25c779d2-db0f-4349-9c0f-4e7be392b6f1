<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.investigacao">

    <class name="InvestigacaoAgravoHantavirose" table="investigacao_agr_hantavirose">

        <id name="codigo"
            type="java.lang.Long"
            column="cd_invest_agr_hantavirose" >
            <generator class="sequence">
                <param name="sequence">seq_investigacao_agr_hantavirose</param>
            </generator>
        </id>

        <version column="version" name="version" type="long"/>

        <property
                name="flagInformacoesComplementares"
                column="flag_informacoes_complementares"
                type="java.lang.String"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo"
                name="registroAgravo"
                column="cd_registro_agravo"
                not-null="true"
        />

        <!-- Investigação Agravo -->
        <property
                name="dataInvestigacao"
                column="dt_investigacao"
                type="java.util.Date"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"
                name="ocupacaoCbo"
                column="ocupacao_cbo"
                not-null="false"
        />


        <!-- Antecedentes epidemiologicos -->
        <property
                name="atividadeTreinamentoMilitar"
                column="atividade_treinamento_militar"
                type="java.lang.Long"
        />
        <property
                name="atividadeDesmatamento"
                column="atividade_desmatamento"
                type="java.lang.Long"
        />
        <property
                name="atividadeLimpeza"
                column="atividade_limpeza"
                type="java.lang.Long"
        />
        <property
                name="atividadeMoagem"
                column="atividade_moagem"
                type="java.lang.Long"
        />
        <property
                name="atividadeDormiu"
                column="atividade_dormiu"
                type="java.lang.Long"
        />
        <property
                name="atividadeTransporte"
                column="atividade_transporte"
                type="java.lang.Long"
        />
        <property
                name="atividadePescaCaca"
                column="atividade_pesca_caca"
                type="java.lang.Long"
        />
        <property
                name="atividadeContatoRato"
                column="atividade_contato_rato"
                type="java.lang.Long"
        />
        <property
                name="atividadeOutras"
                column="atividade_outras"
                type="java.lang.String"
                length="100"
        />

        <!-- DADOS CLÍNICOS -->
        <property
                name="dataPrimeiroAtendimento"
                column="dt_primeiro_atendimento"
                type="java.util.Date"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Empresa"
                column="local_primeiro_atendimento"
                name="localPrimeiroAtendimento"
                not-null="false"
        />

        <property
                name="sinalSintomaFebre"
                column="sinal_sintoma_febre"
                type="java.lang.Long"
        />
        <property
                name="sinalSintomaTosseSeca"
                column="sinal_sintoma_tosse_seca"
                type="java.lang.Long"
        />
        <property
                name="sinalSintomaDispneia"
                column="sinal_sintoma_dispneia"
                type="java.lang.Long"
        />
        <property
                name="sinalSintomaInsuficienciaRespiratoria"
                column="sinal_sintoma_insuficiencia_respiratoria"
                type="java.lang.Long"
        />
        <property
                name="sinalSintomaCefaleia"
                column="sinal_sintoma_cefaleia"
                type="java.lang.Long"
        />
        <property
                name="sinalSintomaMialgiaGeneralizada"
                column="sinal_sintoma_mialgia_generalizada"
                type="java.lang.Long"
        />
        <property
                name="sinalSintomaDorLombar"
                column="sinal_sintoma_dor_lombar"
                type="java.lang.Long"
        />
        <property
                name="sinalSintomaDorAbdominal"
                column="sinal_sintoma_dor_abdominal"
                type="java.lang.Long"
        />
        <property
                name="sinalSintomaHipotensao"
                column="sinal_sintoma_hipotensao"
                type="java.lang.Long"
        />
        <property
                name="sinalSintomaChoque"
                column="sinal_sintoma_choque"
                type="java.lang.Long"
        />
        <property
                name="sinalSintomaNauseasVomito"
                column="sinal_sintoma_nauseas_vomito"
                type="java.lang.Long"
        />
        <property
                name="sinalSintomaDiarreia"
                column="sinal_sintoma_diarreia"
                type="java.lang.Long"
        />
        <property
                name="sinalSintomaDorToracica"
                column="sinal_sintoma_dor_toracica"
                type="java.lang.Long"
        />
        <property
                name="sinalSintomaTontura"
                column="sinal_sintoma_tontura"
                type="java.lang.Long"
        />
        <property
                name="sinalSintomaInsuficienciaCardiaca"
                column="sinal_sintoma_insuficiencia_cardiaca"
                type="java.lang.Long"
        />
        <property
                name="sinalSintomaInsuficienciaRenal"
                column="sinal_sintoma_insuficiencia_renal"
                type="java.lang.Long"
        />
        <property
                name="sinalSintomaNeurologico"
                column="sinal_sintoma_neurologico"
                type="java.lang.Long"
        />
        <property
                name="sinalSintomaAstenia"
                column="sinal_sintoma_astenia"
                type="java.lang.Long"
        />
        <property
                name="sinalSintomaPetequias"
                column="sinal_sintoma_petequias"
                type="java.lang.Long"
        />
        <property
                name="sinalSintomaOutrosHemorragico"
                column="sinal_sintoma_outros_hemorragico"
                type="java.lang.String"
                length="50"
        />
        <property
                name="sinalSintomaOutos"
                column="sinal_sintoma_outros"
                type="java.lang.String"
                length="50"
        />


        <!-- DADOS DO LABORATÓRIO -->
        <property
                name="colheuAmostraSangue"
                column="colheu_amostra_sangue"
                type="java.lang.Long"
        />
        <property
                name="resultadoAHematocrito"
                column="resultado_a_hematocrito"
                type="java.lang.Long"
        />
        <property
                name="resultadoATrombocitopenia"
                column="resultado_a_trombocitopenia"
                type="java.lang.Long"
        />
        <property
                name="resultadoALinfocitoAtipico"
                column="resultado_a_linfocito_atipico"
                type="java.lang.Long"
        />
        <property
                name="resultadoAUreiaCreatina"
                column="resultado_a_ureia_creatina"
                type="java.lang.Long"
        />

        <property
                name="resultadoATGO"
                column="resultado_a_tgo"
                type="java.lang.String"
                length="10"
        />
        <property
                name="resultadoATGP"
                column="resultado_a_tgp"
                type="java.lang.String"
                length="10"
        />

        <property
                name="resultadoBLeucocito"
                column="resultado_b_leucocito"
                type="java.lang.Long"
        />

        <property
                name="radiografiaTorax"
                column="radiografia_torax"
                type="java.lang.Long"
        />
        <property
                name="alteracoesToraxInfiltradoDifuso"
                column="alteracoes_torax_infiltrado_difuso"
                type="java.lang.Long"
        />
        <property
                name="alteracoesToraxInfiltradoLocalizado"
                column="alteracoes_torax_infiltrado_localizado"
                type="java.lang.Long"
        />
        <property
                name="alteracoesToraxDerramePleural"
                column="alteracoes_torax_derrame_pleural"
                type="java.lang.Long"
        />

        <property
                name="exameSorologicoDataColeta"
                column="exame_sorologico_dt_coleta"
                type="java.util.Date"
        />
        <property
                name="exameSorologicoResultado"
                column="exame_sorologico_resultado"
                type="java.lang.Long"
        />

        <property
                name="imunohistoquimica"
                column="imunohistoquimica"
                type="java.lang.Long"
        />

        <property
                name="rtPcrDataColeta"
                column="rt_pcr_dt_coleta"
                type="java.util.Date"
        />
        <property
                name="rtPcrResultado"
                column="rt_pcr_resultado"
                type="java.lang.Long"
        />

        <!-- HOSPITALIZAÇÃO -->
        <property
                name="hospitalizacao"
                column="hospitalizacao"
                type="java.lang.Long"
        />
        <property
                name="dataInternacao"
                column="dt_internacao"
                type="java.util.Date"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Empresa"
                column="unidade_hospital"
                name="hospital"
                not-null="false"
        />

        <property
                name="suporteTerapeuticoRespiradorMecanico"
                column="suporte_terapeutico_respirador_mecanico"
                type="java.lang.Long"
        />
        <property
                name="suporteTerapeuticoMedicamentoAntiviral"
                column="suporte_terapeutico_medicamento_antiviral"
                type="java.lang.Long"
        />
        <property
                name="suporteTerapeuticoCorticoide"
                column="suporte_terapeutico_corticoide"
                type="java.lang.Long"
        />
        <property
                name="suporteTerapeuticoCpapBipap"
                column="suporte_terapeutico_cpap_bipap"
                type="java.lang.Long"
        />
        <property
                name="suporteTerapeuticoDrogasVasoativas"
                column="suporte_terapeutico_drogas_vasoativas"
                type="java.lang.Long"
        />
        <property
                name="suporteTerapeuticoAntibiotico"
                column="suporte_terapeutico_antibiotico"
                type="java.lang.Long"
        />
        <property
                name="suporteTerapeuticoOutros"
                column="suporte_terapeutico_outros"
                type="java.lang.String"
                length="50"
        />

        <!-- CASO AUTOCTONE -->
        <property
                name="casoAutoctone"
                column="caso_autoctone"
                type="java.lang.Long"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Cidade"
                name="cidadeLocalInfeccao"
                column="cd_cidade_acidente"
                not-null="false"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Pais"
                name="paisLocalInfeccao"
                column="cd_pais_acidente"
                not-null="false"
        />
        <property
                name="distritoLocalInfeccao"
                column="str_distrito_acidente"
                type="java.lang.String"
                length="200"
        />
        <property
                name="bairroLocalInfeccao"
                column="str_bairro_acidente"
                type="java.lang.String"
                length="200"
        />


        <property
                name="localInfeccaoCaracteristicaZona"
                column="local_infeccao_caracteristica_zona"
                type="java.lang.Long"
        />
        <property
                name="localInfeccaoTipoAmbiente"
                column="local_infeccao_tipo_ambiente"
                type="java.lang.Long"
        />
        <property
                name="localInfeccaoTipoAmbienteOutro"
                column="local_infeccao_tipo_ambiente_outro"
                type="java.lang.String"
                length="50"
        />
        <property
                name="localInfeccaoLocalizacaoLpiKm"
                column="local_infeccao_localizacao_lpi_km"
                type="java.lang.String"
                length="3"
        />
        <property
                name="localInfeccaoLocalizacaoLpiDirecao"
                column="local_infeccao_localizacao_lpi_direcao"
                type="java.lang.Long"
        />



        <!-- Conclusao -->
        <property
                name="classificacaoFinal"
                column="classificacao_final"
                type="java.lang.Long"
        />
        <property
                name="formaClinica"
                column="forma_clinica"
                type="java.lang.Long"
        />
        <property
                name="criterioDiagnostico"
                column="criterio_diagnostico"
                type="java.lang.Long"
        />
        <property
                name="evolucaoCaso"
                column="evolucao_caso"
                type="java.lang.Long"
        />
        <property
                name="dataObitoAlta"
                column="dt_obito_alta"
                type="java.util.Date"
        />
        <property
                name="obitoAutopsia"
                column="obito_autopsia"
                type="java.lang.Long"
        />
        <property
                name="doencaRelacionadaTrabalho"
                column="doenca_relacionada_trabalho"
                type="java.lang.Long"
        />

        <!-- OBS -->
        <property
                name="observacao"
                column="observacao"
                type="java.lang.String"
                length="5000"
        />

        <!-- Encerramento -->
        <property
                name="dataEncerramento"
                column="dt_encerramento"
                type="java.util.Date"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                name="usuarioEncerramento"
                column="cd_usuario_encerramento"
                not-null="false"
        />

    </class>
</hibernate-mapping>