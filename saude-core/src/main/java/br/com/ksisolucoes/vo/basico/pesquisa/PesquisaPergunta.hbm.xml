<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.basico.pesquisa"  >
    <class name="PesquisaPergunta" table="pesquisa_pergunta">
        <id
            column="cd_pesq_pergunta"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.basico.pesquisa.Pesquisa"
            name="pesquisa"
        >
            <column name="cd_pesquisa" />
        </many-to-one>
       
        <many-to-one
            class="br.com.ksisolucoes.vo.basico.pesquisa.PerguntaPesquisa"
            name="perguntaPesquisa"
        >
            <column name="cd_pergunta" />
        </many-to-one>
        
        <property
            column="version_all"
            name="versionAll"
            not-null="true"
            type="java.lang.Long"
        />
        
    </class>
</hibernate-mapping>
