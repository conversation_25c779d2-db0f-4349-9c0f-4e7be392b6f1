package br.com.ksisolucoes.vo.cadsus.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the usuario_cadsus_esus table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="usuario_cadsus_esus"
 */

public abstract class BaseUsuarioCadsusEsus extends BaseRootVO implements Serializable {

	public static String REF = "UsuarioCadsusEsus";
	public static final String PROP_TEM_TUBERCULOSE = "temTuberculose";
	public static final String PROP_INFORMA_ORIENTACAO_SEXUAL = "informaOrientacaoSexual";
	public static final String PROP_DEPENDENTE_ALCOOL = "dependenteAlcool";
	public static final String PROP_ESTA_ACAMADO = "estaAcamado";
	public static final String PROP_TEM_HIPERTENSAO = "temHipertensao";
	public static final String PROP_RESPIRATORIA_OUTROS = "respiratoriaOutros";
	public static final String PROP_INFORMA_IDENTIDADE_GENERO = "informaIdentidadeGenero";
	public static final String PROP_ACESSO_HIGIENE_PESSOAL = "acessoHigienePessoal";
	public static final String PROP_PROTESE_MEMBROS_SUPERIORES = "proteseMembrosSuperiores";
	public static final String PROP_DOENCA_RINS = "doencaRins";
	public static final String PROP_VERSION_ALL = "versionAll";
	public static final String PROP_MATERNIDADE_REFERENCIA = "maternidadeReferencia";
	public static final String PROP_USA_PLANTAS_MEDICINAIS = "usaPlantasMedicinais";
	public static final String PROP_DATA_NATURALIZADO = "dataNaturalizado";
	public static final String PROP_PROTESE_CADEIRA_RODAS = "proteseCadeiraRodas";
	public static final String PROP_TEVE_INFARTO = "teveInfarto";
	public static final String PROP_SITUACAO_CONJUGAL = "situacaoConjugal";
	public static final String PROP_POSSUI_DEFICIENCIA = "possuiDeficiencia";
	public static final String PROP_COMEU_ALGUNS_ALIMENTOS_QUE_TINHA_DINHEIRO_ACABOU = "comeuAlgunsAlimentosQueTinhaDinheiroAcabou";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_PROTESE_AUDITIVA = "proteseAuditiva";
	public static final String PROP_ALIMENTOS_ACABARAM_ANTES_TER_DINHEIRO_COMPRAR_MAIS = "alimentosAcabaramAntesTerDinheiroComprarMais";
	public static final String PROP_NIVEL_ESCOLARIDADE = "nivelEscolaridade";
	public static final String PROP_RESPONSAVEL_CRIANCA = "responsavelCrianca";
	public static final String PROP_PARENTESCO_RESPONSAVEL = "parentescoResponsavel";
	public static final String PROP_MEMBRO_COMUNIDADE_TRADICIONAL = "membroComunidadeTradicional";
	public static final String PROP_REFEICOES_DIA = "refeicoesDia";
	public static final String PROP_TEVE_AVC = "teveAvc";
	public static final String PROP_REFEICAO_RESTAURANTE_POPULAR = "refeicaoRestaurantePopular";
	public static final String PROP_CONDICAO_SAUDE1 = "condicaoSaude1";
	public static final String PROP_REFEICAO_DOACAO_OUTROS = "refeicaoDoacaoOutros";
	public static final String PROP_CONDICAO_SAUDE2 = "condicaoSaude2";
	public static final String PROP_CONDICAO_SAUDE3 = "condicaoSaude3";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_CARDIACA_NAO_SABE = "cardiacaNaoSabe";
	public static final String PROP_PAI_DESCONHECIDO = "paiDesconhecido";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_DEFICIENCIA_OUTRA = "deficienciaOutra";
	public static final String PROP_DOENCA_CARDIACA = "doencaCardiaca";
	public static final String PROP_PORTARIA_NATURALIZACAO = "portariaNaturalizacao";
	public static final String PROP_REFEICAO_DOACAO_POPULAR = "refeicaoDoacaoPopular";
	public static final String PROP_DEFICIENCIA_FISICA = "deficienciaFisica";
	public static final String PROP_RESPIRATORIA_NAO_SABE = "respiratoriaNaoSabe";
	public static final String PROP_PESO_CONSIDERADO = "pesoConsiderado";
	public static final String PROP_IDENTIDADE_GENERO = "identidadeGenero";
	public static final String PROP_FUMANTE = "fumante";
	public static final String PROP_REFEICAO_DOACAO_RELIGIOSO = "refeicaoDoacaoReligioso";
	public static final String PROP_SITUACAO_MERCADO_TRABALHO = "situacaoMercadoTrabalho";
	public static final String PROP_DESCRICAO_DEFICIENCIA_OUTRA = "descricaoDeficienciaOutra";
	public static final String PROP_DT_ENTRADA_BRASIL = "dtEntradaBrasil";
	public static final String PROP_TEM_HANSENIASE = "temHanseniase";
	public static final String PROP_ORIENTACAO_SEXUAL = "orientacaoSexual";
	public static final String PROP_FLAG_DESNUTRICAO_GRAVE = "flagDesnutricaoGrave";
	public static final String PROP_NUMERO_DO = "numeroDo";
	public static final String PROP_RINS_OUTROS = "rinsOutros";
	public static final String PROP_RESPIRATORIA_EFISEMA = "respiratoriaEfisema";
	public static final String PROP_ESTA_DOMICILIADO = "estaDomiciliado";
	public static final String PROP_HIGIENE_OUTROS = "higieneOutros";
	public static final String PROP_PROTESE_OUTROS = "proteseOutros";
	public static final String PROP_DOENCA_RESPIRATORIA = "doencaRespiratoria";
	public static final String PROP_RINS_NAO_SABE = "rinsNaoSabe";
	public static final String PROP_TEM_TEVE_CANCER = "temTeveCancer";
	public static final String PROP_ESTA_GESTANTE = "estaGestante";
	public static final String PROP_TEMPO_RUA = "tempoRua";
	public static final String PROP_COMUNIDADE_TRADICIONAL = "comunidadeTradicional";
	public static final String PROP_DATA_OBITO = "dataObito";
	public static final String PROP_CARDIACA_INSUFICIENCIA = "cardiacaInsuficiencia";
	public static final String PROP_TEM_DIABETES = "temDiabetes";
	public static final String PROP_DEFICIENCIA_INTELECTUAL = "deficienciaIntelectual";
	public static final String PROP_DEFICIENCIA_VISUAL = "deficienciaVisual";
	public static final String PROP_PROTESE_MEMBROS_INFERIORES = "proteseMembrosInferiores";
	public static final String PROP_POSSUI_REFERENCIA_FAMILIAR = "possuiReferenciaFamiliar";
	public static final String PROP_PARTICIPA_GRUPO_COMUNITARIO = "participaGrupoComunitario";
	public static final String PROP_FLAG_ANALFABETO = "flagAnalfabeto";
	public static final String PROP_MAE_DESCONHECIDO = "maeDesconhecido";
	public static final String PROP_RECEBE_BENEFICIO = "recebeBeneficio";
	public static final String PROP_RESPIRATORIA_ASMA = "respiratoriaAsma";
	public static final String PROP_HIGIENE_BUCAL = "higieneBucal";
	public static final String PROP_POSSUI_PLANO_SAUDE = "possuiPlanoSaude";
	public static final String PROP_INTERNACAO_ANO = "internacaoAno";
	public static final String PROP_QUAIS_PLANTAS = "quaisPlantas";
	public static final String PROP_OUTRAS_PRATICAS_INTEGRATIVAS = "outrasPraticasIntegrativas";
	public static final String PROP_CAUSA_INTERNACAO = "causaInternacao";
	public static final String PROP_DEFICIENCIA_AUDITIVA = "deficienciaAuditiva";
	public static final String PROP_HIGIENE_BANHO = "higieneBanho";
	public static final String PROP_FEZ_TRATAMENTO_PSIQUIATRICO = "fezTratamentoPsiquiatrico";
	public static final String PROP_UTILIZA_PROTESE = "utilizaProtese";
	public static final String PROP_GRAU_PARENTESCO = "grauParentesco";
	public static final String PROP_NOME_OUTRA_INSTITUICAO = "nomeOutraInstituicao";
	public static final String PROP_FLAG_FORA_AREA = "flagForaArea";
	public static final String PROP_SITUACAO_RUA = "situacaoRua";
	public static final String PROP_FREQUENTA_CURANDEIRA = "frequentaCurandeira";
	public static final String PROP_FREQUENTA_ESCOLA = "frequentaEscola";
	public static final String PROP_HIGIENE_SANITARIO = "higieneSanitario";
	public static final String PROP_SOFRIMENTO_PSIQUICO_GRAVE = "sofrimentoPsiquicoGrave";
	public static final String PROP_ACOMPANHADO_POR_OUTRA_INSTITUICAO = "acompanhadoPorOutraInstituicao";
	public static final String PROP_DEPENDENTE_DROGA = "dependenteDroga";
	public static final String PROP_VISITA_FAMILIAR_FREQUENTEMENTE = "visitaFamiliarFrequentemente";
	public static final String PROP_CARDIACA_OUTROS = "cardiacaOutros";
	public static final String PROP_REFEICAO_DOACAO_RESTAURANTE = "refeicaoDoacaoRestaurante";
	public static final String PROP_RINS_INSUFICIENCIA = "rinsInsuficiencia";


	// constructors
	public BaseUsuarioCadsusEsus () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseUsuarioCadsusEsus (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseUsuarioCadsusEsus (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setUsuarioCadsus(usuarioCadsus);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataCadastro;
	private java.lang.Long situacaoConjugal;
	private java.lang.Long frequentaEscola;
	private java.lang.Long nivelEscolaridade;
	private java.lang.Long situacaoMercadoTrabalho;
	private java.lang.Long responsavelCrianca;
	private java.lang.Long frequentaCurandeira;
	private java.lang.Long participaGrupoComunitario;
	private java.lang.Long possuiPlanoSaude;
	private java.lang.Long membroComunidadeTradicional;
	private java.lang.String comunidadeTradicional;
	private java.lang.Long orientacaoSexual;
	private java.lang.Long deficienciaAuditiva;
	private java.lang.Long deficienciaVisual;
	private java.lang.Long deficienciaFisica;
	private java.lang.Long deficienciaIntelectual;
	private java.lang.Long deficienciaOutra;
	private java.lang.String descricaoDeficienciaOutra;
	private java.lang.Long situacaoRua;
	private java.lang.Long tempoRua;
	private java.lang.Long acompanhadoPorOutraInstituicao;
	private java.lang.String nomeOutraInstituicao;
	private java.lang.Long recebeBeneficio;
	private java.lang.Long possuiReferenciaFamiliar;
	private java.lang.Long visitaFamiliarFrequentemente;
	private java.lang.String grauParentesco;
	private java.lang.Long refeicoesDia;
	private java.lang.Long refeicaoRestaurantePopular;
	private java.lang.Long refeicaoDoacaoRestaurante;
	private java.lang.Long refeicaoDoacaoReligioso;
	private java.lang.Long refeicaoDoacaoPopular;
	private java.lang.Long refeicaoDoacaoOutros;
	private java.lang.Long acessoHigienePessoal;
	private java.lang.Long higieneBanho;
	private java.lang.Long higieneSanitario;
	private java.lang.Long higieneBucal;
	private java.lang.Long higieneOutros;
	private java.lang.Long estaGestante;
	private java.lang.String maternidadeReferencia;
	private java.lang.Long pesoConsiderado;
	private java.lang.Long fumante;
	private java.lang.Long dependenteAlcool;
	private java.lang.Long dependenteDroga;
	private java.lang.Long temHipertensao;
	private java.lang.Long temDiabetes;
	private java.lang.Long teveAvc;
	private java.lang.Long teveInfarto;
	private java.lang.Long temHanseniase;
	private java.lang.Long temTuberculose;
	private java.lang.Long temTeveCancer;
	private java.lang.Long internacaoAno;
	private java.lang.String causaInternacao;
	private java.lang.Long fezTratamentoPsiquiatrico;
	private java.lang.Long estaAcamado;
	private java.lang.Long estaDomiciliado;
	private java.lang.Long usaPlantasMedicinais;
	private java.lang.String quaisPlantas;
	private java.lang.Long doencaCardiaca;
	private java.lang.Long cardiacaInsuficiencia;
	private java.lang.Long cardiacaOutros;
	private java.lang.Long cardiacaNaoSabe;
	private java.lang.Long doencaRins;
	private java.lang.Long rinsInsuficiencia;
	private java.lang.Long rinsOutros;
	private java.lang.Long rinsNaoSabe;
	private java.lang.Long doencaRespiratoria;
	private java.lang.Long respiratoriaAsma;
	private java.lang.Long respiratoriaEfisema;
	private java.lang.Long respiratoriaOutros;
	private java.lang.Long respiratoriaNaoSabe;
	private java.lang.Long outrasPraticasIntegrativas;
	private java.lang.String condicaoSaude1;
	private java.lang.String condicaoSaude2;
	private java.lang.String condicaoSaude3;
	private java.lang.Long versionAll;
	private java.lang.Long possuiDeficiencia;
	private java.lang.Long informaOrientacaoSexual;
	private java.lang.Long sofrimentoPsiquicoGrave;
	private java.lang.Long utilizaProtese;
	private java.lang.Long proteseAuditiva;
	private java.lang.Long proteseMembrosSuperiores;
	private java.lang.Long proteseMembrosInferiores;
	private java.lang.Long proteseCadeiraRodas;
	private java.lang.Long proteseOutros;
	private java.lang.Long parentescoResponsavel;
	private java.lang.Long maeDesconhecido;
	private java.lang.Long paiDesconhecido;
	private java.util.Date dataNaturalizado;
	private java.lang.String portariaNaturalizacao;
	private java.util.Date dtEntradaBrasil;
	private java.lang.Long flagForaArea;
	private java.lang.Long identidadeGenero;
	private java.util.Date dataObito;
	private java.lang.String numeroDo;
	private java.lang.Long flagDesnutricaoGrave;
	private java.lang.Long flagAnalfabeto;
	private java.lang.Long informaIdentidadeGenero;
	private java.lang.Long alimentosAcabaramAntesTerDinheiroComprarMais;
	private java.lang.Long comeuAlgunsAlimentosQueTinhaDinheiroAcabou;

	// many to one
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_usu_cadsus_esus"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: situacao_conjugal
	 */
	public java.lang.Long getSituacaoConjugal () {
		return getPropertyValue(this, situacaoConjugal, PROP_SITUACAO_CONJUGAL); 
	}

	/**
	 * Set the value related to the column: situacao_conjugal
	 * @param situacaoConjugal the situacao_conjugal value
	 */
	public void setSituacaoConjugal (java.lang.Long situacaoConjugal) {
//        java.lang.Long situacaoConjugalOld = this.situacaoConjugal;
		this.situacaoConjugal = situacaoConjugal;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoConjugal", situacaoConjugalOld, situacaoConjugal);
	}



	/**
	 * Return the value associated with the column: frequenta_escola
	 */
	public java.lang.Long getFrequentaEscola () {
		return getPropertyValue(this, frequentaEscola, PROP_FREQUENTA_ESCOLA); 
	}

	/**
	 * Set the value related to the column: frequenta_escola
	 * @param frequentaEscola the frequenta_escola value
	 */
	public void setFrequentaEscola (java.lang.Long frequentaEscola) {
//        java.lang.Long frequentaEscolaOld = this.frequentaEscola;
		this.frequentaEscola = frequentaEscola;
//        this.getPropertyChangeSupport().firePropertyChange ("frequentaEscola", frequentaEscolaOld, frequentaEscola);
	}



	/**
	 * Return the value associated with the column: nivel_escolaridade
	 */
	public java.lang.Long getNivelEscolaridade () {
		return getPropertyValue(this, nivelEscolaridade, PROP_NIVEL_ESCOLARIDADE); 
	}

	/**
	 * Set the value related to the column: nivel_escolaridade
	 * @param nivelEscolaridade the nivel_escolaridade value
	 */
	public void setNivelEscolaridade (java.lang.Long nivelEscolaridade) {
//        java.lang.Long nivelEscolaridadeOld = this.nivelEscolaridade;
		this.nivelEscolaridade = nivelEscolaridade;
//        this.getPropertyChangeSupport().firePropertyChange ("nivelEscolaridade", nivelEscolaridadeOld, nivelEscolaridade);
	}



	/**
	 * Return the value associated with the column: situacao_merc_trabalho
	 */
	public java.lang.Long getSituacaoMercadoTrabalho () {
		return getPropertyValue(this, situacaoMercadoTrabalho, PROP_SITUACAO_MERCADO_TRABALHO); 
	}

	/**
	 * Set the value related to the column: situacao_merc_trabalho
	 * @param situacaoMercadoTrabalho the situacao_merc_trabalho value
	 */
	public void setSituacaoMercadoTrabalho (java.lang.Long situacaoMercadoTrabalho) {
//        java.lang.Long situacaoMercadoTrabalhoOld = this.situacaoMercadoTrabalho;
		this.situacaoMercadoTrabalho = situacaoMercadoTrabalho;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoMercadoTrabalho", situacaoMercadoTrabalhoOld, situacaoMercadoTrabalho);
	}



	/**
	 * Return the value associated with the column: responsavel_crianca
	 */
	public java.lang.Long getResponsavelCrianca () {
		return getPropertyValue(this, responsavelCrianca, PROP_RESPONSAVEL_CRIANCA); 
	}

	/**
	 * Set the value related to the column: responsavel_crianca
	 * @param responsavelCrianca the responsavel_crianca value
	 */
	public void setResponsavelCrianca (java.lang.Long responsavelCrianca) {
//        java.lang.Long responsavelCriancaOld = this.responsavelCrianca;
		this.responsavelCrianca = responsavelCrianca;
//        this.getPropertyChangeSupport().firePropertyChange ("responsavelCrianca", responsavelCriancaOld, responsavelCrianca);
	}



	/**
	 * Return the value associated with the column: frequenta_curandeira
	 */
	public java.lang.Long getFrequentaCurandeira () {
		return getPropertyValue(this, frequentaCurandeira, PROP_FREQUENTA_CURANDEIRA); 
	}

	/**
	 * Set the value related to the column: frequenta_curandeira
	 * @param frequentaCurandeira the frequenta_curandeira value
	 */
	public void setFrequentaCurandeira (java.lang.Long frequentaCurandeira) {
//        java.lang.Long frequentaCurandeiraOld = this.frequentaCurandeira;
		this.frequentaCurandeira = frequentaCurandeira;
//        this.getPropertyChangeSupport().firePropertyChange ("frequentaCurandeira", frequentaCurandeiraOld, frequentaCurandeira);
	}



	/**
	 * Return the value associated with the column: participa_grupo_comunitario
	 */
	public java.lang.Long getParticipaGrupoComunitario () {
		return getPropertyValue(this, participaGrupoComunitario, PROP_PARTICIPA_GRUPO_COMUNITARIO); 
	}

	/**
	 * Set the value related to the column: participa_grupo_comunitario
	 * @param participaGrupoComunitario the participa_grupo_comunitario value
	 */
	public void setParticipaGrupoComunitario (java.lang.Long participaGrupoComunitario) {
//        java.lang.Long participaGrupoComunitarioOld = this.participaGrupoComunitario;
		this.participaGrupoComunitario = participaGrupoComunitario;
//        this.getPropertyChangeSupport().firePropertyChange ("participaGrupoComunitario", participaGrupoComunitarioOld, participaGrupoComunitario);
	}



	/**
	 * Return the value associated with the column: possui_plano_saude
	 */
	public java.lang.Long getPossuiPlanoSaude () {
		return getPropertyValue(this, possuiPlanoSaude, PROP_POSSUI_PLANO_SAUDE); 
	}

	/**
	 * Set the value related to the column: possui_plano_saude
	 * @param possuiPlanoSaude the possui_plano_saude value
	 */
	public void setPossuiPlanoSaude (java.lang.Long possuiPlanoSaude) {
//        java.lang.Long possuiPlanoSaudeOld = this.possuiPlanoSaude;
		this.possuiPlanoSaude = possuiPlanoSaude;
//        this.getPropertyChangeSupport().firePropertyChange ("possuiPlanoSaude", possuiPlanoSaudeOld, possuiPlanoSaude);
	}



	/**
	 * Return the value associated with the column: membro_comunidade_tradicional
	 */
	public java.lang.Long getMembroComunidadeTradicional () {
		return getPropertyValue(this, membroComunidadeTradicional, PROP_MEMBRO_COMUNIDADE_TRADICIONAL); 
	}

	/**
	 * Set the value related to the column: membro_comunidade_tradicional
	 * @param membroComunidadeTradicional the membro_comunidade_tradicional value
	 */
	public void setMembroComunidadeTradicional (java.lang.Long membroComunidadeTradicional) {
//        java.lang.Long membroComunidadeTradicionalOld = this.membroComunidadeTradicional;
		this.membroComunidadeTradicional = membroComunidadeTradicional;
//        this.getPropertyChangeSupport().firePropertyChange ("membroComunidadeTradicional", membroComunidadeTradicionalOld, membroComunidadeTradicional);
	}



	/**
	 * Return the value associated with the column: comunidade_tradicional
	 */
	public java.lang.String getComunidadeTradicional () {
		return getPropertyValue(this, comunidadeTradicional, PROP_COMUNIDADE_TRADICIONAL); 
	}

	/**
	 * Set the value related to the column: comunidade_tradicional
	 * @param comunidadeTradicional the comunidade_tradicional value
	 */
	public void setComunidadeTradicional (java.lang.String comunidadeTradicional) {
//        java.lang.String comunidadeTradicionalOld = this.comunidadeTradicional;
		this.comunidadeTradicional = comunidadeTradicional;
//        this.getPropertyChangeSupport().firePropertyChange ("comunidadeTradicional", comunidadeTradicionalOld, comunidadeTradicional);
	}



	/**
	 * Return the value associated with the column: orientacao_sexual
	 */
	public java.lang.Long getOrientacaoSexual () {
		return getPropertyValue(this, orientacaoSexual, PROP_ORIENTACAO_SEXUAL); 
	}

	/**
	 * Set the value related to the column: orientacao_sexual
	 * @param orientacaoSexual the orientacao_sexual value
	 */
	public void setOrientacaoSexual (java.lang.Long orientacaoSexual) {
//        java.lang.Long orientacaoSexualOld = this.orientacaoSexual;
		this.orientacaoSexual = orientacaoSexual;
//        this.getPropertyChangeSupport().firePropertyChange ("orientacaoSexual", orientacaoSexualOld, orientacaoSexual);
	}



	/**
	 * Return the value associated with the column: def_auditiva
	 */
	public java.lang.Long getDeficienciaAuditiva () {
		return getPropertyValue(this, deficienciaAuditiva, PROP_DEFICIENCIA_AUDITIVA); 
	}

	/**
	 * Set the value related to the column: def_auditiva
	 * @param deficienciaAuditiva the def_auditiva value
	 */
	public void setDeficienciaAuditiva (java.lang.Long deficienciaAuditiva) {
//        java.lang.Long deficienciaAuditivaOld = this.deficienciaAuditiva;
		this.deficienciaAuditiva = deficienciaAuditiva;
//        this.getPropertyChangeSupport().firePropertyChange ("deficienciaAuditiva", deficienciaAuditivaOld, deficienciaAuditiva);
	}



	/**
	 * Return the value associated with the column: def_visual
	 */
	public java.lang.Long getDeficienciaVisual () {
		return getPropertyValue(this, deficienciaVisual, PROP_DEFICIENCIA_VISUAL); 
	}

	/**
	 * Set the value related to the column: def_visual
	 * @param deficienciaVisual the def_visual value
	 */
	public void setDeficienciaVisual (java.lang.Long deficienciaVisual) {
//        java.lang.Long deficienciaVisualOld = this.deficienciaVisual;
		this.deficienciaVisual = deficienciaVisual;
//        this.getPropertyChangeSupport().firePropertyChange ("deficienciaVisual", deficienciaVisualOld, deficienciaVisual);
	}



	/**
	 * Return the value associated with the column: def_fisica
	 */
	public java.lang.Long getDeficienciaFisica () {
		return getPropertyValue(this, deficienciaFisica, PROP_DEFICIENCIA_FISICA); 
	}

	/**
	 * Set the value related to the column: def_fisica
	 * @param deficienciaFisica the def_fisica value
	 */
	public void setDeficienciaFisica (java.lang.Long deficienciaFisica) {
//        java.lang.Long deficienciaFisicaOld = this.deficienciaFisica;
		this.deficienciaFisica = deficienciaFisica;
//        this.getPropertyChangeSupport().firePropertyChange ("deficienciaFisica", deficienciaFisicaOld, deficienciaFisica);
	}



	/**
	 * Return the value associated with the column: def_intelectual
	 */
	public java.lang.Long getDeficienciaIntelectual () {
		return getPropertyValue(this, deficienciaIntelectual, PROP_DEFICIENCIA_INTELECTUAL); 
	}

	/**
	 * Set the value related to the column: def_intelectual
	 * @param deficienciaIntelectual the def_intelectual value
	 */
	public void setDeficienciaIntelectual (java.lang.Long deficienciaIntelectual) {
//        java.lang.Long deficienciaIntelectualOld = this.deficienciaIntelectual;
		this.deficienciaIntelectual = deficienciaIntelectual;
//        this.getPropertyChangeSupport().firePropertyChange ("deficienciaIntelectual", deficienciaIntelectualOld, deficienciaIntelectual);
	}



	/**
	 * Return the value associated with the column: def_outra
	 */
	public java.lang.Long getDeficienciaOutra () {
		return getPropertyValue(this, deficienciaOutra, PROP_DEFICIENCIA_OUTRA); 
	}

	/**
	 * Set the value related to the column: def_outra
	 * @param deficienciaOutra the def_outra value
	 */
	public void setDeficienciaOutra (java.lang.Long deficienciaOutra) {
//        java.lang.Long deficienciaOutraOld = this.deficienciaOutra;
		this.deficienciaOutra = deficienciaOutra;
//        this.getPropertyChangeSupport().firePropertyChange ("deficienciaOutra", deficienciaOutraOld, deficienciaOutra);
	}



	/**
	 * Return the value associated with the column: desc_def_outra
	 */
	public java.lang.String getDescricaoDeficienciaOutra () {
		return getPropertyValue(this, descricaoDeficienciaOutra, PROP_DESCRICAO_DEFICIENCIA_OUTRA); 
	}

	/**
	 * Set the value related to the column: desc_def_outra
	 * @param descricaoDeficienciaOutra the desc_def_outra value
	 */
	public void setDescricaoDeficienciaOutra (java.lang.String descricaoDeficienciaOutra) {
//        java.lang.String descricaoDeficienciaOutraOld = this.descricaoDeficienciaOutra;
		this.descricaoDeficienciaOutra = descricaoDeficienciaOutra;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoDeficienciaOutra", descricaoDeficienciaOutraOld, descricaoDeficienciaOutra);
	}



	/**
	 * Return the value associated with the column: situacao_rua
	 */
	public java.lang.Long getSituacaoRua () {
		return getPropertyValue(this, situacaoRua, PROP_SITUACAO_RUA); 
	}

	/**
	 * Set the value related to the column: situacao_rua
	 * @param situacaoRua the situacao_rua value
	 */
	public void setSituacaoRua (java.lang.Long situacaoRua) {
//        java.lang.Long situacaoRuaOld = this.situacaoRua;
		this.situacaoRua = situacaoRua;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRua", situacaoRuaOld, situacaoRua);
	}



	/**
	 * Return the value associated with the column: tempo_rua
	 */
	public java.lang.Long getTempoRua () {
		return getPropertyValue(this, tempoRua, PROP_TEMPO_RUA); 
	}

	/**
	 * Set the value related to the column: tempo_rua
	 * @param tempoRua the tempo_rua value
	 */
	public void setTempoRua (java.lang.Long tempoRua) {
//        java.lang.Long tempoRuaOld = this.tempoRua;
		this.tempoRua = tempoRua;
//        this.getPropertyChangeSupport().firePropertyChange ("tempoRua", tempoRuaOld, tempoRua);
	}



	/**
	 * Return the value associated with the column: acomp_outra_instituicao
	 */
	public java.lang.Long getAcompanhadoPorOutraInstituicao () {
		return getPropertyValue(this, acompanhadoPorOutraInstituicao, PROP_ACOMPANHADO_POR_OUTRA_INSTITUICAO); 
	}

	/**
	 * Set the value related to the column: acomp_outra_instituicao
	 * @param acompanhadoPorOutraInstituicao the acomp_outra_instituicao value
	 */
	public void setAcompanhadoPorOutraInstituicao (java.lang.Long acompanhadoPorOutraInstituicao) {
//        java.lang.Long acompanhadoPorOutraInstituicaoOld = this.acompanhadoPorOutraInstituicao;
		this.acompanhadoPorOutraInstituicao = acompanhadoPorOutraInstituicao;
//        this.getPropertyChangeSupport().firePropertyChange ("acompanhadoPorOutraInstituicao", acompanhadoPorOutraInstituicaoOld, acompanhadoPorOutraInstituicao);
	}



	/**
	 * Return the value associated with the column: nome_outra_instituicao
	 */
	public java.lang.String getNomeOutraInstituicao () {
		return getPropertyValue(this, nomeOutraInstituicao, PROP_NOME_OUTRA_INSTITUICAO); 
	}

	/**
	 * Set the value related to the column: nome_outra_instituicao
	 * @param nomeOutraInstituicao the nome_outra_instituicao value
	 */
	public void setNomeOutraInstituicao (java.lang.String nomeOutraInstituicao) {
//        java.lang.String nomeOutraInstituicaoOld = this.nomeOutraInstituicao;
		this.nomeOutraInstituicao = nomeOutraInstituicao;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeOutraInstituicao", nomeOutraInstituicaoOld, nomeOutraInstituicao);
	}



	/**
	 * Return the value associated with the column: recebe_beneficio
	 */
	public java.lang.Long getRecebeBeneficio () {
		return getPropertyValue(this, recebeBeneficio, PROP_RECEBE_BENEFICIO); 
	}

	/**
	 * Set the value related to the column: recebe_beneficio
	 * @param recebeBeneficio the recebe_beneficio value
	 */
	public void setRecebeBeneficio (java.lang.Long recebeBeneficio) {
//        java.lang.Long recebeBeneficioOld = this.recebeBeneficio;
		this.recebeBeneficio = recebeBeneficio;
//        this.getPropertyChangeSupport().firePropertyChange ("recebeBeneficio", recebeBeneficioOld, recebeBeneficio);
	}



	/**
	 * Return the value associated with the column: possui_ref_familiar
	 */
	public java.lang.Long getPossuiReferenciaFamiliar () {
		return getPropertyValue(this, possuiReferenciaFamiliar, PROP_POSSUI_REFERENCIA_FAMILIAR); 
	}

	/**
	 * Set the value related to the column: possui_ref_familiar
	 * @param possuiReferenciaFamiliar the possui_ref_familiar value
	 */
	public void setPossuiReferenciaFamiliar (java.lang.Long possuiReferenciaFamiliar) {
//        java.lang.Long possuiReferenciaFamiliarOld = this.possuiReferenciaFamiliar;
		this.possuiReferenciaFamiliar = possuiReferenciaFamiliar;
//        this.getPropertyChangeSupport().firePropertyChange ("possuiReferenciaFamiliar", possuiReferenciaFamiliarOld, possuiReferenciaFamiliar);
	}



	/**
	 * Return the value associated with the column: visita_fam_freq
	 */
	public java.lang.Long getVisitaFamiliarFrequentemente () {
		return getPropertyValue(this, visitaFamiliarFrequentemente, PROP_VISITA_FAMILIAR_FREQUENTEMENTE); 
	}

	/**
	 * Set the value related to the column: visita_fam_freq
	 * @param visitaFamiliarFrequentemente the visita_fam_freq value
	 */
	public void setVisitaFamiliarFrequentemente (java.lang.Long visitaFamiliarFrequentemente) {
//        java.lang.Long visitaFamiliarFrequentementeOld = this.visitaFamiliarFrequentemente;
		this.visitaFamiliarFrequentemente = visitaFamiliarFrequentemente;
//        this.getPropertyChangeSupport().firePropertyChange ("visitaFamiliarFrequentemente", visitaFamiliarFrequentementeOld, visitaFamiliarFrequentemente);
	}



	/**
	 * Return the value associated with the column: grau_parentesco
	 */
	public java.lang.String getGrauParentesco () {
		return getPropertyValue(this, grauParentesco, PROP_GRAU_PARENTESCO); 
	}

	/**
	 * Set the value related to the column: grau_parentesco
	 * @param grauParentesco the grau_parentesco value
	 */
	public void setGrauParentesco (java.lang.String grauParentesco) {
//        java.lang.String grauParentescoOld = this.grauParentesco;
		this.grauParentesco = grauParentesco;
//        this.getPropertyChangeSupport().firePropertyChange ("grauParentesco", grauParentescoOld, grauParentesco);
	}



	/**
	 * Return the value associated with the column: refeicoes_dia
	 */
	public java.lang.Long getRefeicoesDia () {
		return getPropertyValue(this, refeicoesDia, PROP_REFEICOES_DIA); 
	}

	/**
	 * Set the value related to the column: refeicoes_dia
	 * @param refeicoesDia the refeicoes_dia value
	 */
	public void setRefeicoesDia (java.lang.Long refeicoesDia) {
//        java.lang.Long refeicoesDiaOld = this.refeicoesDia;
		this.refeicoesDia = refeicoesDia;
//        this.getPropertyChangeSupport().firePropertyChange ("refeicoesDia", refeicoesDiaOld, refeicoesDia);
	}



	/**
	 * Return the value associated with the column: ref_rest_popular
	 */
	public java.lang.Long getRefeicaoRestaurantePopular () {
		return getPropertyValue(this, refeicaoRestaurantePopular, PROP_REFEICAO_RESTAURANTE_POPULAR); 
	}

	/**
	 * Set the value related to the column: ref_rest_popular
	 * @param refeicaoRestaurantePopular the ref_rest_popular value
	 */
	public void setRefeicaoRestaurantePopular (java.lang.Long refeicaoRestaurantePopular) {
//        java.lang.Long refeicaoRestaurantePopularOld = this.refeicaoRestaurantePopular;
		this.refeicaoRestaurantePopular = refeicaoRestaurantePopular;
//        this.getPropertyChangeSupport().firePropertyChange ("refeicaoRestaurantePopular", refeicaoRestaurantePopularOld, refeicaoRestaurantePopular);
	}



	/**
	 * Return the value associated with the column: ref_doacao_rest
	 */
	public java.lang.Long getRefeicaoDoacaoRestaurante () {
		return getPropertyValue(this, refeicaoDoacaoRestaurante, PROP_REFEICAO_DOACAO_RESTAURANTE); 
	}

	/**
	 * Set the value related to the column: ref_doacao_rest
	 * @param refeicaoDoacaoRestaurante the ref_doacao_rest value
	 */
	public void setRefeicaoDoacaoRestaurante (java.lang.Long refeicaoDoacaoRestaurante) {
//        java.lang.Long refeicaoDoacaoRestauranteOld = this.refeicaoDoacaoRestaurante;
		this.refeicaoDoacaoRestaurante = refeicaoDoacaoRestaurante;
//        this.getPropertyChangeSupport().firePropertyChange ("refeicaoDoacaoRestaurante", refeicaoDoacaoRestauranteOld, refeicaoDoacaoRestaurante);
	}



	/**
	 * Return the value associated with the column: ref_doacao_relig
	 */
	public java.lang.Long getRefeicaoDoacaoReligioso () {
		return getPropertyValue(this, refeicaoDoacaoReligioso, PROP_REFEICAO_DOACAO_RELIGIOSO); 
	}

	/**
	 * Set the value related to the column: ref_doacao_relig
	 * @param refeicaoDoacaoReligioso the ref_doacao_relig value
	 */
	public void setRefeicaoDoacaoReligioso (java.lang.Long refeicaoDoacaoReligioso) {
//        java.lang.Long refeicaoDoacaoReligiosoOld = this.refeicaoDoacaoReligioso;
		this.refeicaoDoacaoReligioso = refeicaoDoacaoReligioso;
//        this.getPropertyChangeSupport().firePropertyChange ("refeicaoDoacaoReligioso", refeicaoDoacaoReligiosoOld, refeicaoDoacaoReligioso);
	}



	/**
	 * Return the value associated with the column: ref_doacao_popular
	 */
	public java.lang.Long getRefeicaoDoacaoPopular () {
		return getPropertyValue(this, refeicaoDoacaoPopular, PROP_REFEICAO_DOACAO_POPULAR); 
	}

	/**
	 * Set the value related to the column: ref_doacao_popular
	 * @param refeicaoDoacaoPopular the ref_doacao_popular value
	 */
	public void setRefeicaoDoacaoPopular (java.lang.Long refeicaoDoacaoPopular) {
//        java.lang.Long refeicaoDoacaoPopularOld = this.refeicaoDoacaoPopular;
		this.refeicaoDoacaoPopular = refeicaoDoacaoPopular;
//        this.getPropertyChangeSupport().firePropertyChange ("refeicaoDoacaoPopular", refeicaoDoacaoPopularOld, refeicaoDoacaoPopular);
	}



	/**
	 * Return the value associated with the column: ref_otros
	 */
	public java.lang.Long getRefeicaoDoacaoOutros () {
		return getPropertyValue(this, refeicaoDoacaoOutros, PROP_REFEICAO_DOACAO_OUTROS); 
	}

	/**
	 * Set the value related to the column: ref_otros
	 * @param refeicaoDoacaoOutros the ref_otros value
	 */
	public void setRefeicaoDoacaoOutros (java.lang.Long refeicaoDoacaoOutros) {
//        java.lang.Long refeicaoDoacaoOutrosOld = this.refeicaoDoacaoOutros;
		this.refeicaoDoacaoOutros = refeicaoDoacaoOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("refeicaoDoacaoOutros", refeicaoDoacaoOutrosOld, refeicaoDoacaoOutros);
	}



	/**
	 * Return the value associated with the column: acesso_higiene_pessoal
	 */
	public java.lang.Long getAcessoHigienePessoal () {
		return getPropertyValue(this, acessoHigienePessoal, PROP_ACESSO_HIGIENE_PESSOAL); 
	}

	/**
	 * Set the value related to the column: acesso_higiene_pessoal
	 * @param acessoHigienePessoal the acesso_higiene_pessoal value
	 */
	public void setAcessoHigienePessoal (java.lang.Long acessoHigienePessoal) {
//        java.lang.Long acessoHigienePessoalOld = this.acessoHigienePessoal;
		this.acessoHigienePessoal = acessoHigienePessoal;
//        this.getPropertyChangeSupport().firePropertyChange ("acessoHigienePessoal", acessoHigienePessoalOld, acessoHigienePessoal);
	}



	/**
	 * Return the value associated with the column: hig_banho
	 */
	public java.lang.Long getHigieneBanho () {
		return getPropertyValue(this, higieneBanho, PROP_HIGIENE_BANHO); 
	}

	/**
	 * Set the value related to the column: hig_banho
	 * @param higieneBanho the hig_banho value
	 */
	public void setHigieneBanho (java.lang.Long higieneBanho) {
//        java.lang.Long higieneBanhoOld = this.higieneBanho;
		this.higieneBanho = higieneBanho;
//        this.getPropertyChangeSupport().firePropertyChange ("higieneBanho", higieneBanhoOld, higieneBanho);
	}



	/**
	 * Return the value associated with the column: hig_sanitario
	 */
	public java.lang.Long getHigieneSanitario () {
		return getPropertyValue(this, higieneSanitario, PROP_HIGIENE_SANITARIO); 
	}

	/**
	 * Set the value related to the column: hig_sanitario
	 * @param higieneSanitario the hig_sanitario value
	 */
	public void setHigieneSanitario (java.lang.Long higieneSanitario) {
//        java.lang.Long higieneSanitarioOld = this.higieneSanitario;
		this.higieneSanitario = higieneSanitario;
//        this.getPropertyChangeSupport().firePropertyChange ("higieneSanitario", higieneSanitarioOld, higieneSanitario);
	}



	/**
	 * Return the value associated with the column: hig_bucal
	 */
	public java.lang.Long getHigieneBucal () {
		return getPropertyValue(this, higieneBucal, PROP_HIGIENE_BUCAL); 
	}

	/**
	 * Set the value related to the column: hig_bucal
	 * @param higieneBucal the hig_bucal value
	 */
	public void setHigieneBucal (java.lang.Long higieneBucal) {
//        java.lang.Long higieneBucalOld = this.higieneBucal;
		this.higieneBucal = higieneBucal;
//        this.getPropertyChangeSupport().firePropertyChange ("higieneBucal", higieneBucalOld, higieneBucal);
	}



	/**
	 * Return the value associated with the column: hig_outros
	 */
	public java.lang.Long getHigieneOutros () {
		return getPropertyValue(this, higieneOutros, PROP_HIGIENE_OUTROS); 
	}

	/**
	 * Set the value related to the column: hig_outros
	 * @param higieneOutros the hig_outros value
	 */
	public void setHigieneOutros (java.lang.Long higieneOutros) {
//        java.lang.Long higieneOutrosOld = this.higieneOutros;
		this.higieneOutros = higieneOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("higieneOutros", higieneOutrosOld, higieneOutros);
	}



	/**
	 * Return the value associated with the column: esta_gestante
	 */
	public java.lang.Long getEstaGestante () {
		return getPropertyValue(this, estaGestante, PROP_ESTA_GESTANTE); 
	}

	/**
	 * Set the value related to the column: esta_gestante
	 * @param estaGestante the esta_gestante value
	 */
	public void setEstaGestante (java.lang.Long estaGestante) {
//        java.lang.Long estaGestanteOld = this.estaGestante;
		this.estaGestante = estaGestante;
//        this.getPropertyChangeSupport().firePropertyChange ("estaGestante", estaGestanteOld, estaGestante);
	}



	/**
	 * Return the value associated with the column: matern_referencia
	 */
	public java.lang.String getMaternidadeReferencia () {
		return getPropertyValue(this, maternidadeReferencia, PROP_MATERNIDADE_REFERENCIA); 
	}

	/**
	 * Set the value related to the column: matern_referencia
	 * @param maternidadeReferencia the matern_referencia value
	 */
	public void setMaternidadeReferencia (java.lang.String maternidadeReferencia) {
//        java.lang.String maternidadeReferenciaOld = this.maternidadeReferencia;
		this.maternidadeReferencia = maternidadeReferencia;
//        this.getPropertyChangeSupport().firePropertyChange ("maternidadeReferencia", maternidadeReferenciaOld, maternidadeReferencia);
	}



	/**
	 * Return the value associated with the column: peso_consid
	 */
	public java.lang.Long getPesoConsiderado () {
		return getPropertyValue(this, pesoConsiderado, PROP_PESO_CONSIDERADO); 
	}

	/**
	 * Set the value related to the column: peso_consid
	 * @param pesoConsiderado the peso_consid value
	 */
	public void setPesoConsiderado (java.lang.Long pesoConsiderado) {
//        java.lang.Long pesoConsideradoOld = this.pesoConsiderado;
		this.pesoConsiderado = pesoConsiderado;
//        this.getPropertyChangeSupport().firePropertyChange ("pesoConsiderado", pesoConsideradoOld, pesoConsiderado);
	}



	/**
	 * Return the value associated with the column: fumante
	 */
	public java.lang.Long getFumante () {
		return getPropertyValue(this, fumante, PROP_FUMANTE); 
	}

	/**
	 * Set the value related to the column: fumante
	 * @param fumante the fumante value
	 */
	public void setFumante (java.lang.Long fumante) {
//        java.lang.Long fumanteOld = this.fumante;
		this.fumante = fumante;
//        this.getPropertyChangeSupport().firePropertyChange ("fumante", fumanteOld, fumante);
	}



	/**
	 * Return the value associated with the column: dependente_alcool
	 */
	public java.lang.Long getDependenteAlcool () {
		return getPropertyValue(this, dependenteAlcool, PROP_DEPENDENTE_ALCOOL); 
	}

	/**
	 * Set the value related to the column: dependente_alcool
	 * @param dependenteAlcool the dependente_alcool value
	 */
	public void setDependenteAlcool (java.lang.Long dependenteAlcool) {
//        java.lang.Long dependenteAlcoolOld = this.dependenteAlcool;
		this.dependenteAlcool = dependenteAlcool;
//        this.getPropertyChangeSupport().firePropertyChange ("dependenteAlcool", dependenteAlcoolOld, dependenteAlcool);
	}



	/**
	 * Return the value associated with the column: dependente_droga
	 */
	public java.lang.Long getDependenteDroga () {
		return getPropertyValue(this, dependenteDroga, PROP_DEPENDENTE_DROGA); 
	}

	/**
	 * Set the value related to the column: dependente_droga
	 * @param dependenteDroga the dependente_droga value
	 */
	public void setDependenteDroga (java.lang.Long dependenteDroga) {
//        java.lang.Long dependenteDrogaOld = this.dependenteDroga;
		this.dependenteDroga = dependenteDroga;
//        this.getPropertyChangeSupport().firePropertyChange ("dependenteDroga", dependenteDrogaOld, dependenteDroga);
	}



	/**
	 * Return the value associated with the column: tem_hipertensao
	 */
	public java.lang.Long getTemHipertensao () {
		return getPropertyValue(this, temHipertensao, PROP_TEM_HIPERTENSAO); 
	}

	/**
	 * Set the value related to the column: tem_hipertensao
	 * @param temHipertensao the tem_hipertensao value
	 */
	public void setTemHipertensao (java.lang.Long temHipertensao) {
//        java.lang.Long temHipertensaoOld = this.temHipertensao;
		this.temHipertensao = temHipertensao;
//        this.getPropertyChangeSupport().firePropertyChange ("temHipertensao", temHipertensaoOld, temHipertensao);
	}



	/**
	 * Return the value associated with the column: tem_diabetes
	 */
	public java.lang.Long getTemDiabetes () {
		return getPropertyValue(this, temDiabetes, PROP_TEM_DIABETES); 
	}

	/**
	 * Set the value related to the column: tem_diabetes
	 * @param temDiabetes the tem_diabetes value
	 */
	public void setTemDiabetes (java.lang.Long temDiabetes) {
//        java.lang.Long temDiabetesOld = this.temDiabetes;
		this.temDiabetes = temDiabetes;
//        this.getPropertyChangeSupport().firePropertyChange ("temDiabetes", temDiabetesOld, temDiabetes);
	}



	/**
	 * Return the value associated with the column: teve_avc
	 */
	public java.lang.Long getTeveAvc () {
		return getPropertyValue(this, teveAvc, PROP_TEVE_AVC); 
	}

	/**
	 * Set the value related to the column: teve_avc
	 * @param teveAvc the teve_avc value
	 */
	public void setTeveAvc (java.lang.Long teveAvc) {
//        java.lang.Long teveAvcOld = this.teveAvc;
		this.teveAvc = teveAvc;
//        this.getPropertyChangeSupport().firePropertyChange ("teveAvc", teveAvcOld, teveAvc);
	}



	/**
	 * Return the value associated with the column: teve_infarto
	 */
	public java.lang.Long getTeveInfarto () {
		return getPropertyValue(this, teveInfarto, PROP_TEVE_INFARTO); 
	}

	/**
	 * Set the value related to the column: teve_infarto
	 * @param teveInfarto the teve_infarto value
	 */
	public void setTeveInfarto (java.lang.Long teveInfarto) {
//        java.lang.Long teveInfartoOld = this.teveInfarto;
		this.teveInfarto = teveInfarto;
//        this.getPropertyChangeSupport().firePropertyChange ("teveInfarto", teveInfartoOld, teveInfarto);
	}



	/**
	 * Return the value associated with the column: tem_hanseniase
	 */
	public java.lang.Long getTemHanseniase () {
		return getPropertyValue(this, temHanseniase, PROP_TEM_HANSENIASE); 
	}

	/**
	 * Set the value related to the column: tem_hanseniase
	 * @param temHanseniase the tem_hanseniase value
	 */
	public void setTemHanseniase (java.lang.Long temHanseniase) {
//        java.lang.Long temHanseniaseOld = this.temHanseniase;
		this.temHanseniase = temHanseniase;
//        this.getPropertyChangeSupport().firePropertyChange ("temHanseniase", temHanseniaseOld, temHanseniase);
	}



	/**
	 * Return the value associated with the column: tem_tuberculose
	 */
	public java.lang.Long getTemTuberculose () {
		return getPropertyValue(this, temTuberculose, PROP_TEM_TUBERCULOSE); 
	}

	/**
	 * Set the value related to the column: tem_tuberculose
	 * @param temTuberculose the tem_tuberculose value
	 */
	public void setTemTuberculose (java.lang.Long temTuberculose) {
//        java.lang.Long temTuberculoseOld = this.temTuberculose;
		this.temTuberculose = temTuberculose;
//        this.getPropertyChangeSupport().firePropertyChange ("temTuberculose", temTuberculoseOld, temTuberculose);
	}



	/**
	 * Return the value associated with the column: tem_teve_cancer
	 */
	public java.lang.Long getTemTeveCancer () {
		return getPropertyValue(this, temTeveCancer, PROP_TEM_TEVE_CANCER); 
	}

	/**
	 * Set the value related to the column: tem_teve_cancer
	 * @param temTeveCancer the tem_teve_cancer value
	 */
	public void setTemTeveCancer (java.lang.Long temTeveCancer) {
//        java.lang.Long temTeveCancerOld = this.temTeveCancer;
		this.temTeveCancer = temTeveCancer;
//        this.getPropertyChangeSupport().firePropertyChange ("temTeveCancer", temTeveCancerOld, temTeveCancer);
	}



	/**
	 * Return the value associated with the column: internacao_ano
	 */
	public java.lang.Long getInternacaoAno () {
		return getPropertyValue(this, internacaoAno, PROP_INTERNACAO_ANO); 
	}

	/**
	 * Set the value related to the column: internacao_ano
	 * @param internacaoAno the internacao_ano value
	 */
	public void setInternacaoAno (java.lang.Long internacaoAno) {
//        java.lang.Long internacaoAnoOld = this.internacaoAno;
		this.internacaoAno = internacaoAno;
//        this.getPropertyChangeSupport().firePropertyChange ("internacaoAno", internacaoAnoOld, internacaoAno);
	}



	/**
	 * Return the value associated with the column: causa_internacao
	 */
	public java.lang.String getCausaInternacao () {
		return getPropertyValue(this, causaInternacao, PROP_CAUSA_INTERNACAO); 
	}

	/**
	 * Set the value related to the column: causa_internacao
	 * @param causaInternacao the causa_internacao value
	 */
	public void setCausaInternacao (java.lang.String causaInternacao) {
//        java.lang.String causaInternacaoOld = this.causaInternacao;
		this.causaInternacao = causaInternacao;
//        this.getPropertyChangeSupport().firePropertyChange ("causaInternacao", causaInternacaoOld, causaInternacao);
	}



	/**
	 * Return the value associated with the column: fez_trat_psiquiatra
	 */
	public java.lang.Long getFezTratamentoPsiquiatrico () {
		return getPropertyValue(this, fezTratamentoPsiquiatrico, PROP_FEZ_TRATAMENTO_PSIQUIATRICO); 
	}

	/**
	 * Set the value related to the column: fez_trat_psiquiatra
	 * @param fezTratamentoPsiquiatrico the fez_trat_psiquiatra value
	 */
	public void setFezTratamentoPsiquiatrico (java.lang.Long fezTratamentoPsiquiatrico) {
//        java.lang.Long fezTratamentoPsiquiatricoOld = this.fezTratamentoPsiquiatrico;
		this.fezTratamentoPsiquiatrico = fezTratamentoPsiquiatrico;
//        this.getPropertyChangeSupport().firePropertyChange ("fezTratamentoPsiquiatrico", fezTratamentoPsiquiatricoOld, fezTratamentoPsiquiatrico);
	}



	/**
	 * Return the value associated with the column: esta_acamado
	 */
	public java.lang.Long getEstaAcamado () {
		return getPropertyValue(this, estaAcamado, PROP_ESTA_ACAMADO); 
	}

	/**
	 * Set the value related to the column: esta_acamado
	 * @param estaAcamado the esta_acamado value
	 */
	public void setEstaAcamado (java.lang.Long estaAcamado) {
//        java.lang.Long estaAcamadoOld = this.estaAcamado;
		this.estaAcamado = estaAcamado;
//        this.getPropertyChangeSupport().firePropertyChange ("estaAcamado", estaAcamadoOld, estaAcamado);
	}



	/**
	 * Return the value associated with the column: esta_domiciliado
	 */
	public java.lang.Long getEstaDomiciliado () {
		return getPropertyValue(this, estaDomiciliado, PROP_ESTA_DOMICILIADO); 
	}

	/**
	 * Set the value related to the column: esta_domiciliado
	 * @param estaDomiciliado the esta_domiciliado value
	 */
	public void setEstaDomiciliado (java.lang.Long estaDomiciliado) {
//        java.lang.Long estaDomiciliadoOld = this.estaDomiciliado;
		this.estaDomiciliado = estaDomiciliado;
//        this.getPropertyChangeSupport().firePropertyChange ("estaDomiciliado", estaDomiciliadoOld, estaDomiciliado);
	}



	/**
	 * Return the value associated with the column: usa_plantas_medicinais
	 */
	public java.lang.Long getUsaPlantasMedicinais () {
		return getPropertyValue(this, usaPlantasMedicinais, PROP_USA_PLANTAS_MEDICINAIS); 
	}

	/**
	 * Set the value related to the column: usa_plantas_medicinais
	 * @param usaPlantasMedicinais the usa_plantas_medicinais value
	 */
	public void setUsaPlantasMedicinais (java.lang.Long usaPlantasMedicinais) {
//        java.lang.Long usaPlantasMedicinaisOld = this.usaPlantasMedicinais;
		this.usaPlantasMedicinais = usaPlantasMedicinais;
//        this.getPropertyChangeSupport().firePropertyChange ("usaPlantasMedicinais", usaPlantasMedicinaisOld, usaPlantasMedicinais);
	}



	/**
	 * Return the value associated with the column: quais_plantas
	 */
	public java.lang.String getQuaisPlantas () {
		return getPropertyValue(this, quaisPlantas, PROP_QUAIS_PLANTAS); 
	}

	/**
	 * Set the value related to the column: quais_plantas
	 * @param quaisPlantas the quais_plantas value
	 */
	public void setQuaisPlantas (java.lang.String quaisPlantas) {
//        java.lang.String quaisPlantasOld = this.quaisPlantas;
		this.quaisPlantas = quaisPlantas;
//        this.getPropertyChangeSupport().firePropertyChange ("quaisPlantas", quaisPlantasOld, quaisPlantas);
	}



	/**
	 * Return the value associated with the column: doenca_cardiaca
	 */
	public java.lang.Long getDoencaCardiaca () {
		return getPropertyValue(this, doencaCardiaca, PROP_DOENCA_CARDIACA); 
	}

	/**
	 * Set the value related to the column: doenca_cardiaca
	 * @param doencaCardiaca the doenca_cardiaca value
	 */
	public void setDoencaCardiaca (java.lang.Long doencaCardiaca) {
//        java.lang.Long doencaCardiacaOld = this.doencaCardiaca;
		this.doencaCardiaca = doencaCardiaca;
//        this.getPropertyChangeSupport().firePropertyChange ("doencaCardiaca", doencaCardiacaOld, doencaCardiaca);
	}



	/**
	 * Return the value associated with the column: card_insuficiencia
	 */
	public java.lang.Long getCardiacaInsuficiencia () {
		return getPropertyValue(this, cardiacaInsuficiencia, PROP_CARDIACA_INSUFICIENCIA); 
	}

	/**
	 * Set the value related to the column: card_insuficiencia
	 * @param cardiacaInsuficiencia the card_insuficiencia value
	 */
	public void setCardiacaInsuficiencia (java.lang.Long cardiacaInsuficiencia) {
//        java.lang.Long cardiacaInsuficienciaOld = this.cardiacaInsuficiencia;
		this.cardiacaInsuficiencia = cardiacaInsuficiencia;
//        this.getPropertyChangeSupport().firePropertyChange ("cardiacaInsuficiencia", cardiacaInsuficienciaOld, cardiacaInsuficiencia);
	}



	/**
	 * Return the value associated with the column: card_outros
	 */
	public java.lang.Long getCardiacaOutros () {
		return getPropertyValue(this, cardiacaOutros, PROP_CARDIACA_OUTROS); 
	}

	/**
	 * Set the value related to the column: card_outros
	 * @param cardiacaOutros the card_outros value
	 */
	public void setCardiacaOutros (java.lang.Long cardiacaOutros) {
//        java.lang.Long cardiacaOutrosOld = this.cardiacaOutros;
		this.cardiacaOutros = cardiacaOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("cardiacaOutros", cardiacaOutrosOld, cardiacaOutros);
	}



	/**
	 * Return the value associated with the column: card_nao_sabe
	 */
	public java.lang.Long getCardiacaNaoSabe () {
		return getPropertyValue(this, cardiacaNaoSabe, PROP_CARDIACA_NAO_SABE); 
	}

	/**
	 * Set the value related to the column: card_nao_sabe
	 * @param cardiacaNaoSabe the card_nao_sabe value
	 */
	public void setCardiacaNaoSabe (java.lang.Long cardiacaNaoSabe) {
//        java.lang.Long cardiacaNaoSabeOld = this.cardiacaNaoSabe;
		this.cardiacaNaoSabe = cardiacaNaoSabe;
//        this.getPropertyChangeSupport().firePropertyChange ("cardiacaNaoSabe", cardiacaNaoSabeOld, cardiacaNaoSabe);
	}



	/**
	 * Return the value associated with the column: doenca_rins
	 */
	public java.lang.Long getDoencaRins () {
		return getPropertyValue(this, doencaRins, PROP_DOENCA_RINS); 
	}

	/**
	 * Set the value related to the column: doenca_rins
	 * @param doencaRins the doenca_rins value
	 */
	public void setDoencaRins (java.lang.Long doencaRins) {
//        java.lang.Long doencaRinsOld = this.doencaRins;
		this.doencaRins = doencaRins;
//        this.getPropertyChangeSupport().firePropertyChange ("doencaRins", doencaRinsOld, doencaRins);
	}



	/**
	 * Return the value associated with the column: rins_insuficiencia
	 */
	public java.lang.Long getRinsInsuficiencia () {
		return getPropertyValue(this, rinsInsuficiencia, PROP_RINS_INSUFICIENCIA); 
	}

	/**
	 * Set the value related to the column: rins_insuficiencia
	 * @param rinsInsuficiencia the rins_insuficiencia value
	 */
	public void setRinsInsuficiencia (java.lang.Long rinsInsuficiencia) {
//        java.lang.Long rinsInsuficienciaOld = this.rinsInsuficiencia;
		this.rinsInsuficiencia = rinsInsuficiencia;
//        this.getPropertyChangeSupport().firePropertyChange ("rinsInsuficiencia", rinsInsuficienciaOld, rinsInsuficiencia);
	}



	/**
	 * Return the value associated with the column: rins_outros
	 */
	public java.lang.Long getRinsOutros () {
		return getPropertyValue(this, rinsOutros, PROP_RINS_OUTROS); 
	}

	/**
	 * Set the value related to the column: rins_outros
	 * @param rinsOutros the rins_outros value
	 */
	public void setRinsOutros (java.lang.Long rinsOutros) {
//        java.lang.Long rinsOutrosOld = this.rinsOutros;
		this.rinsOutros = rinsOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("rinsOutros", rinsOutrosOld, rinsOutros);
	}



	/**
	 * Return the value associated with the column: rins_nao_sabe
	 */
	public java.lang.Long getRinsNaoSabe () {
		return getPropertyValue(this, rinsNaoSabe, PROP_RINS_NAO_SABE); 
	}

	/**
	 * Set the value related to the column: rins_nao_sabe
	 * @param rinsNaoSabe the rins_nao_sabe value
	 */
	public void setRinsNaoSabe (java.lang.Long rinsNaoSabe) {
//        java.lang.Long rinsNaoSabeOld = this.rinsNaoSabe;
		this.rinsNaoSabe = rinsNaoSabe;
//        this.getPropertyChangeSupport().firePropertyChange ("rinsNaoSabe", rinsNaoSabeOld, rinsNaoSabe);
	}



	/**
	 * Return the value associated with the column: doenca_respiratorio
	 */
	public java.lang.Long getDoencaRespiratoria () {
		return getPropertyValue(this, doencaRespiratoria, PROP_DOENCA_RESPIRATORIA); 
	}

	/**
	 * Set the value related to the column: doenca_respiratorio
	 * @param doencaRespiratoria the doenca_respiratorio value
	 */
	public void setDoencaRespiratoria (java.lang.Long doencaRespiratoria) {
//        java.lang.Long doencaRespiratoriaOld = this.doencaRespiratoria;
		this.doencaRespiratoria = doencaRespiratoria;
//        this.getPropertyChangeSupport().firePropertyChange ("doencaRespiratoria", doencaRespiratoriaOld, doencaRespiratoria);
	}



	/**
	 * Return the value associated with the column: resp_asma
	 */
	public java.lang.Long getRespiratoriaAsma () {
		return getPropertyValue(this, respiratoriaAsma, PROP_RESPIRATORIA_ASMA); 
	}

	/**
	 * Set the value related to the column: resp_asma
	 * @param respiratoriaAsma the resp_asma value
	 */
	public void setRespiratoriaAsma (java.lang.Long respiratoriaAsma) {
//        java.lang.Long respiratoriaAsmaOld = this.respiratoriaAsma;
		this.respiratoriaAsma = respiratoriaAsma;
//        this.getPropertyChangeSupport().firePropertyChange ("respiratoriaAsma", respiratoriaAsmaOld, respiratoriaAsma);
	}



	/**
	 * Return the value associated with the column: resp_efisema
	 */
	public java.lang.Long getRespiratoriaEfisema () {
		return getPropertyValue(this, respiratoriaEfisema, PROP_RESPIRATORIA_EFISEMA); 
	}

	/**
	 * Set the value related to the column: resp_efisema
	 * @param respiratoriaEfisema the resp_efisema value
	 */
	public void setRespiratoriaEfisema (java.lang.Long respiratoriaEfisema) {
//        java.lang.Long respiratoriaEfisemaOld = this.respiratoriaEfisema;
		this.respiratoriaEfisema = respiratoriaEfisema;
//        this.getPropertyChangeSupport().firePropertyChange ("respiratoriaEfisema", respiratoriaEfisemaOld, respiratoriaEfisema);
	}



	/**
	 * Return the value associated with the column: resp_outros
	 */
	public java.lang.Long getRespiratoriaOutros () {
		return getPropertyValue(this, respiratoriaOutros, PROP_RESPIRATORIA_OUTROS); 
	}

	/**
	 * Set the value related to the column: resp_outros
	 * @param respiratoriaOutros the resp_outros value
	 */
	public void setRespiratoriaOutros (java.lang.Long respiratoriaOutros) {
//        java.lang.Long respiratoriaOutrosOld = this.respiratoriaOutros;
		this.respiratoriaOutros = respiratoriaOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("respiratoriaOutros", respiratoriaOutrosOld, respiratoriaOutros);
	}



	/**
	 * Return the value associated with the column: resp_nao_sabe
	 */
	public java.lang.Long getRespiratoriaNaoSabe () {
		return getPropertyValue(this, respiratoriaNaoSabe, PROP_RESPIRATORIA_NAO_SABE); 
	}

	/**
	 * Set the value related to the column: resp_nao_sabe
	 * @param respiratoriaNaoSabe the resp_nao_sabe value
	 */
	public void setRespiratoriaNaoSabe (java.lang.Long respiratoriaNaoSabe) {
//        java.lang.Long respiratoriaNaoSabeOld = this.respiratoriaNaoSabe;
		this.respiratoriaNaoSabe = respiratoriaNaoSabe;
//        this.getPropertyChangeSupport().firePropertyChange ("respiratoriaNaoSabe", respiratoriaNaoSabeOld, respiratoriaNaoSabe);
	}



	/**
	 * Return the value associated with the column: out_prat_integrativas
	 */
	public java.lang.Long getOutrasPraticasIntegrativas () {
		return getPropertyValue(this, outrasPraticasIntegrativas, PROP_OUTRAS_PRATICAS_INTEGRATIVAS); 
	}

	/**
	 * Set the value related to the column: out_prat_integrativas
	 * @param outrasPraticasIntegrativas the out_prat_integrativas value
	 */
	public void setOutrasPraticasIntegrativas (java.lang.Long outrasPraticasIntegrativas) {
//        java.lang.Long outrasPraticasIntegrativasOld = this.outrasPraticasIntegrativas;
		this.outrasPraticasIntegrativas = outrasPraticasIntegrativas;
//        this.getPropertyChangeSupport().firePropertyChange ("outrasPraticasIntegrativas", outrasPraticasIntegrativasOld, outrasPraticasIntegrativas);
	}



	/**
	 * Return the value associated with the column: cond_saude_1
	 */
	public java.lang.String getCondicaoSaude1 () {
		return getPropertyValue(this, condicaoSaude1, PROP_CONDICAO_SAUDE1); 
	}

	/**
	 * Set the value related to the column: cond_saude_1
	 * @param condicaoSaude1 the cond_saude_1 value
	 */
	public void setCondicaoSaude1 (java.lang.String condicaoSaude1) {
//        java.lang.String condicaoSaude1Old = this.condicaoSaude1;
		this.condicaoSaude1 = condicaoSaude1;
//        this.getPropertyChangeSupport().firePropertyChange ("condicaoSaude1", condicaoSaude1Old, condicaoSaude1);
	}



	/**
	 * Return the value associated with the column: cond_saude_2
	 */
	public java.lang.String getCondicaoSaude2 () {
		return getPropertyValue(this, condicaoSaude2, PROP_CONDICAO_SAUDE2); 
	}

	/**
	 * Set the value related to the column: cond_saude_2
	 * @param condicaoSaude2 the cond_saude_2 value
	 */
	public void setCondicaoSaude2 (java.lang.String condicaoSaude2) {
//        java.lang.String condicaoSaude2Old = this.condicaoSaude2;
		this.condicaoSaude2 = condicaoSaude2;
//        this.getPropertyChangeSupport().firePropertyChange ("condicaoSaude2", condicaoSaude2Old, condicaoSaude2);
	}



	/**
	 * Return the value associated with the column: cond_saude_3
	 */
	public java.lang.String getCondicaoSaude3 () {
		return getPropertyValue(this, condicaoSaude3, PROP_CONDICAO_SAUDE3); 
	}

	/**
	 * Set the value related to the column: cond_saude_3
	 * @param condicaoSaude3 the cond_saude_3 value
	 */
	public void setCondicaoSaude3 (java.lang.String condicaoSaude3) {
//        java.lang.String condicaoSaude3Old = this.condicaoSaude3;
		this.condicaoSaude3 = condicaoSaude3;
//        this.getPropertyChangeSupport().firePropertyChange ("condicaoSaude3", condicaoSaude3Old, condicaoSaude3);
	}



	/**
	 * Return the value associated with the column: version_all
	 */
	public java.lang.Long getVersionAll () {
		return getPropertyValue(this, versionAll, PROP_VERSION_ALL); 
	}

	/**
	 * Set the value related to the column: version_all
	 * @param versionAll the version_all value
	 */
	public void setVersionAll (java.lang.Long versionAll) {
//        java.lang.Long versionAllOld = this.versionAll;
		this.versionAll = versionAll;
//        this.getPropertyChangeSupport().firePropertyChange ("versionAll", versionAllOld, versionAll);
	}



	/**
	 * Return the value associated with the column: possui_deficiencia
	 */
	public java.lang.Long getPossuiDeficiencia () {
		return getPropertyValue(this, possuiDeficiencia, PROP_POSSUI_DEFICIENCIA); 
	}

	/**
	 * Set the value related to the column: possui_deficiencia
	 * @param possuiDeficiencia the possui_deficiencia value
	 */
	public void setPossuiDeficiencia (java.lang.Long possuiDeficiencia) {
//        java.lang.Long possuiDeficienciaOld = this.possuiDeficiencia;
		this.possuiDeficiencia = possuiDeficiencia;
//        this.getPropertyChangeSupport().firePropertyChange ("possuiDeficiencia", possuiDeficienciaOld, possuiDeficiencia);
	}



	/**
	 * Return the value associated with the column: inf_orientacao_sexual
	 */
	public java.lang.Long getInformaOrientacaoSexual () {
		return getPropertyValue(this, informaOrientacaoSexual, PROP_INFORMA_ORIENTACAO_SEXUAL); 
	}

	/**
	 * Set the value related to the column: inf_orientacao_sexual
	 * @param informaOrientacaoSexual the inf_orientacao_sexual value
	 */
	public void setInformaOrientacaoSexual (java.lang.Long informaOrientacaoSexual) {
//        java.lang.Long informaOrientacaoSexualOld = this.informaOrientacaoSexual;
		this.informaOrientacaoSexual = informaOrientacaoSexual;
//        this.getPropertyChangeSupport().firePropertyChange ("informaOrientacaoSexual", informaOrientacaoSexualOld, informaOrientacaoSexual);
	}



	/**
	 * Return the value associated with the column: sofrimento_psiquico_grave
	 */
	public java.lang.Long getSofrimentoPsiquicoGrave () {
		return getPropertyValue(this, sofrimentoPsiquicoGrave, PROP_SOFRIMENTO_PSIQUICO_GRAVE); 
	}

	/**
	 * Set the value related to the column: sofrimento_psiquico_grave
	 * @param sofrimentoPsiquicoGrave the sofrimento_psiquico_grave value
	 */
	public void setSofrimentoPsiquicoGrave (java.lang.Long sofrimentoPsiquicoGrave) {
//        java.lang.Long sofrimentoPsiquicoGraveOld = this.sofrimentoPsiquicoGrave;
		this.sofrimentoPsiquicoGrave = sofrimentoPsiquicoGrave;
//        this.getPropertyChangeSupport().firePropertyChange ("sofrimentoPsiquicoGrave", sofrimentoPsiquicoGraveOld, sofrimentoPsiquicoGrave);
	}



	/**
	 * Return the value associated with the column: utiliza_protese
	 */
	public java.lang.Long getUtilizaProtese () {
		return getPropertyValue(this, utilizaProtese, PROP_UTILIZA_PROTESE); 
	}

	/**
	 * Set the value related to the column: utiliza_protese
	 * @param utilizaProtese the utiliza_protese value
	 */
	public void setUtilizaProtese (java.lang.Long utilizaProtese) {
//        java.lang.Long utilizaProteseOld = this.utilizaProtese;
		this.utilizaProtese = utilizaProtese;
//        this.getPropertyChangeSupport().firePropertyChange ("utilizaProtese", utilizaProteseOld, utilizaProtese);
	}



	/**
	 * Return the value associated with the column: protese_auditiva
	 */
	public java.lang.Long getProteseAuditiva () {
		return getPropertyValue(this, proteseAuditiva, PROP_PROTESE_AUDITIVA); 
	}

	/**
	 * Set the value related to the column: protese_auditiva
	 * @param proteseAuditiva the protese_auditiva value
	 */
	public void setProteseAuditiva (java.lang.Long proteseAuditiva) {
//        java.lang.Long proteseAuditivaOld = this.proteseAuditiva;
		this.proteseAuditiva = proteseAuditiva;
//        this.getPropertyChangeSupport().firePropertyChange ("proteseAuditiva", proteseAuditivaOld, proteseAuditiva);
	}



	/**
	 * Return the value associated with the column: protese_membros_superiores
	 */
	public java.lang.Long getProteseMembrosSuperiores () {
		return getPropertyValue(this, proteseMembrosSuperiores, PROP_PROTESE_MEMBROS_SUPERIORES); 
	}

	/**
	 * Set the value related to the column: protese_membros_superiores
	 * @param proteseMembrosSuperiores the protese_membros_superiores value
	 */
	public void setProteseMembrosSuperiores (java.lang.Long proteseMembrosSuperiores) {
//        java.lang.Long proteseMembrosSuperioresOld = this.proteseMembrosSuperiores;
		this.proteseMembrosSuperiores = proteseMembrosSuperiores;
//        this.getPropertyChangeSupport().firePropertyChange ("proteseMembrosSuperiores", proteseMembrosSuperioresOld, proteseMembrosSuperiores);
	}



	/**
	 * Return the value associated with the column: protese_membros_inferiores
	 */
	public java.lang.Long getProteseMembrosInferiores () {
		return getPropertyValue(this, proteseMembrosInferiores, PROP_PROTESE_MEMBROS_INFERIORES); 
	}

	/**
	 * Set the value related to the column: protese_membros_inferiores
	 * @param proteseMembrosInferiores the protese_membros_inferiores value
	 */
	public void setProteseMembrosInferiores (java.lang.Long proteseMembrosInferiores) {
//        java.lang.Long proteseMembrosInferioresOld = this.proteseMembrosInferiores;
		this.proteseMembrosInferiores = proteseMembrosInferiores;
//        this.getPropertyChangeSupport().firePropertyChange ("proteseMembrosInferiores", proteseMembrosInferioresOld, proteseMembrosInferiores);
	}



	/**
	 * Return the value associated with the column: protetese_cadeira_de_rodas
	 */
	public java.lang.Long getProteseCadeiraRodas () {
		return getPropertyValue(this, proteseCadeiraRodas, PROP_PROTESE_CADEIRA_RODAS); 
	}

	/**
	 * Set the value related to the column: protetese_cadeira_de_rodas
	 * @param proteseCadeiraRodas the protetese_cadeira_de_rodas value
	 */
	public void setProteseCadeiraRodas (java.lang.Long proteseCadeiraRodas) {
//        java.lang.Long proteseCadeiraRodasOld = this.proteseCadeiraRodas;
		this.proteseCadeiraRodas = proteseCadeiraRodas;
//        this.getPropertyChangeSupport().firePropertyChange ("proteseCadeiraRodas", proteseCadeiraRodasOld, proteseCadeiraRodas);
	}



	/**
	 * Return the value associated with the column: protese_outros
	 */
	public java.lang.Long getProteseOutros () {
		return getPropertyValue(this, proteseOutros, PROP_PROTESE_OUTROS); 
	}

	/**
	 * Set the value related to the column: protese_outros
	 * @param proteseOutros the protese_outros value
	 */
	public void setProteseOutros (java.lang.Long proteseOutros) {
//        java.lang.Long proteseOutrosOld = this.proteseOutros;
		this.proteseOutros = proteseOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("proteseOutros", proteseOutrosOld, proteseOutros);
	}



	/**
	 * Return the value associated with the column: parentesco_responsavel
	 */
	public java.lang.Long getParentescoResponsavel () {
		return getPropertyValue(this, parentescoResponsavel, PROP_PARENTESCO_RESPONSAVEL); 
	}

	/**
	 * Set the value related to the column: parentesco_responsavel
	 * @param parentescoResponsavel the parentesco_responsavel value
	 */
	public void setParentescoResponsavel (java.lang.Long parentescoResponsavel) {
//        java.lang.Long parentescoResponsavelOld = this.parentescoResponsavel;
		this.parentescoResponsavel = parentescoResponsavel;
//        this.getPropertyChangeSupport().firePropertyChange ("parentescoResponsavel", parentescoResponsavelOld, parentescoResponsavel);
	}



	/**
	 * Return the value associated with the column: mae_desconhecido
	 */
	public java.lang.Long getMaeDesconhecido () {
		return getPropertyValue(this, maeDesconhecido, PROP_MAE_DESCONHECIDO); 
	}

	/**
	 * Set the value related to the column: mae_desconhecido
	 * @param maeDesconhecido the mae_desconhecido value
	 */
	public void setMaeDesconhecido (java.lang.Long maeDesconhecido) {
//        java.lang.Long maeDesconhecidoOld = this.maeDesconhecido;
		this.maeDesconhecido = maeDesconhecido;
//        this.getPropertyChangeSupport().firePropertyChange ("maeDesconhecido", maeDesconhecidoOld, maeDesconhecido);
	}



	/**
	 * Return the value associated with the column: pai_desconhecido
	 */
	public java.lang.Long getPaiDesconhecido () {
		return getPropertyValue(this, paiDesconhecido, PROP_PAI_DESCONHECIDO); 
	}

	/**
	 * Set the value related to the column: pai_desconhecido
	 * @param paiDesconhecido the pai_desconhecido value
	 */
	public void setPaiDesconhecido (java.lang.Long paiDesconhecido) {
//        java.lang.Long paiDesconhecidoOld = this.paiDesconhecido;
		this.paiDesconhecido = paiDesconhecido;
//        this.getPropertyChangeSupport().firePropertyChange ("paiDesconhecido", paiDesconhecidoOld, paiDesconhecido);
	}



	/**
	 * Return the value associated with the column: dt_naturalizado
	 */
	public java.util.Date getDataNaturalizado () {
		return getPropertyValue(this, dataNaturalizado, PROP_DATA_NATURALIZADO); 
	}

	/**
	 * Set the value related to the column: dt_naturalizado
	 * @param dataNaturalizado the dt_naturalizado value
	 */
	public void setDataNaturalizado (java.util.Date dataNaturalizado) {
//        java.util.Date dataNaturalizadoOld = this.dataNaturalizado;
		this.dataNaturalizado = dataNaturalizado;
//        this.getPropertyChangeSupport().firePropertyChange ("dataNaturalizado", dataNaturalizadoOld, dataNaturalizado);
	}



	/**
	 * Return the value associated with the column: portaria_naturalizacao
	 */
	public java.lang.String getPortariaNaturalizacao () {
		return getPropertyValue(this, portariaNaturalizacao, PROP_PORTARIA_NATURALIZACAO); 
	}

	/**
	 * Set the value related to the column: portaria_naturalizacao
	 * @param portariaNaturalizacao the portaria_naturalizacao value
	 */
	public void setPortariaNaturalizacao (java.lang.String portariaNaturalizacao) {
//        java.lang.String portariaNaturalizacaoOld = this.portariaNaturalizacao;
		this.portariaNaturalizacao = portariaNaturalizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("portariaNaturalizacao", portariaNaturalizacaoOld, portariaNaturalizacao);
	}



	/**
	 * Return the value associated with the column: dt_entrada_brasil
	 */
	public java.util.Date getDtEntradaBrasil () {
		return getPropertyValue(this, dtEntradaBrasil, PROP_DT_ENTRADA_BRASIL); 
	}

	/**
	 * Set the value related to the column: dt_entrada_brasil
	 * @param dtEntradaBrasil the dt_entrada_brasil value
	 */
	public void setDtEntradaBrasil (java.util.Date dtEntradaBrasil) {
//        java.util.Date dtEntradaBrasilOld = this.dtEntradaBrasil;
		this.dtEntradaBrasil = dtEntradaBrasil;
//        this.getPropertyChangeSupport().firePropertyChange ("dtEntradaBrasil", dtEntradaBrasilOld, dtEntradaBrasil);
	}



	/**
	 * Return the value associated with the column: flag_fora_area
	 */
	public java.lang.Long getFlagForaArea () {
		return getPropertyValue(this, flagForaArea, PROP_FLAG_FORA_AREA); 
	}

	/**
	 * Set the value related to the column: flag_fora_area
	 * @param flagForaArea the flag_fora_area value
	 */
	public void setFlagForaArea (java.lang.Long flagForaArea) {
//        java.lang.Long flagForaAreaOld = this.flagForaArea;
		this.flagForaArea = flagForaArea;
//        this.getPropertyChangeSupport().firePropertyChange ("flagForaArea", flagForaAreaOld, flagForaArea);
	}



	/**
	 * Return the value associated with the column: identidade_genero
	 */
	public java.lang.Long getIdentidadeGenero () {
		return getPropertyValue(this, identidadeGenero, PROP_IDENTIDADE_GENERO); 
	}

	/**
	 * Set the value related to the column: identidade_genero
	 * @param identidadeGenero the identidade_genero value
	 */
	public void setIdentidadeGenero (java.lang.Long identidadeGenero) {
//        java.lang.Long identidadeGeneroOld = this.identidadeGenero;
		this.identidadeGenero = identidadeGenero;
//        this.getPropertyChangeSupport().firePropertyChange ("identidadeGenero", identidadeGeneroOld, identidadeGenero);
	}



	/**
	 * Return the value associated with the column: dt_obito
	 */
	public java.util.Date getDataObito () {
		return getPropertyValue(this, dataObito, PROP_DATA_OBITO); 
	}

	/**
	 * Set the value related to the column: dt_obito
	 * @param dataObito the dt_obito value
	 */
	public void setDataObito (java.util.Date dataObito) {
//        java.util.Date dataObitoOld = this.dataObito;
		this.dataObito = dataObito;
//        this.getPropertyChangeSupport().firePropertyChange ("dataObito", dataObitoOld, dataObito);
	}



	/**
	 * Return the value associated with the column: numero_do
	 */
	public java.lang.String getNumeroDo () {
		return getPropertyValue(this, numeroDo, PROP_NUMERO_DO); 
	}

	/**
	 * Set the value related to the column: numero_do
	 * @param numeroDo the numero_do value
	 */
	public void setNumeroDo (java.lang.String numeroDo) {
//        java.lang.String numeroDoOld = this.numeroDo;
		this.numeroDo = numeroDo;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDo", numeroDoOld, numeroDo);
	}



	/**
	 * Return the value associated with the column: flag_desnutricao
	 */
	public java.lang.Long getFlagDesnutricaoGrave () {
		return getPropertyValue(this, flagDesnutricaoGrave, PROP_FLAG_DESNUTRICAO_GRAVE); 
	}

	/**
	 * Set the value related to the column: flag_desnutricao
	 * @param flagDesnutricaoGrave the flag_desnutricao value
	 */
	public void setFlagDesnutricaoGrave (java.lang.Long flagDesnutricaoGrave) {
//        java.lang.Long flagDesnutricaoGraveOld = this.flagDesnutricaoGrave;
		this.flagDesnutricaoGrave = flagDesnutricaoGrave;
//        this.getPropertyChangeSupport().firePropertyChange ("flagDesnutricaoGrave", flagDesnutricaoGraveOld, flagDesnutricaoGrave);
	}



	/**
	 * Return the value associated with the column: flag_analfabeto
	 */
	public java.lang.Long getFlagAnalfabeto () {
		return getPropertyValue(this, flagAnalfabeto, PROP_FLAG_ANALFABETO); 
	}

	/**
	 * Set the value related to the column: flag_analfabeto
	 * @param flagAnalfabeto the flag_analfabeto value
	 */
	public void setFlagAnalfabeto (java.lang.Long flagAnalfabeto) {
//        java.lang.Long flagAnalfabetoOld = this.flagAnalfabeto;
		this.flagAnalfabeto = flagAnalfabeto;
//        this.getPropertyChangeSupport().firePropertyChange ("flagAnalfabeto", flagAnalfabetoOld, flagAnalfabeto);
	}



	/**
	 * Return the value associated with the column: inf_identidade_genero
	 */
	public java.lang.Long getInformaIdentidadeGenero () {
		return getPropertyValue(this, informaIdentidadeGenero, PROP_INFORMA_IDENTIDADE_GENERO); 
	}

	/**
	 * Set the value related to the column: inf_identidade_genero
	 * @param informaIdentidadeGenero the inf_identidade_genero value
	 */
	public void setInformaIdentidadeGenero (java.lang.Long informaIdentidadeGenero) {
//        java.lang.Long informaIdentidadeGeneroOld = this.informaIdentidadeGenero;
		this.informaIdentidadeGenero = informaIdentidadeGenero;
//        this.getPropertyChangeSupport().firePropertyChange ("informaIdentidadeGenero", informaIdentidadeGeneroOld, informaIdentidadeGenero);
	}



	/**
	 * Return the value associated with the column: alimentos_acabaram_antes_ter_dinheiro_comprar_mais
	 */
	public java.lang.Long getAlimentosAcabaramAntesTerDinheiroComprarMais () {
		return getPropertyValue(this, alimentosAcabaramAntesTerDinheiroComprarMais, PROP_ALIMENTOS_ACABARAM_ANTES_TER_DINHEIRO_COMPRAR_MAIS); 
	}

	/**
	 * Set the value related to the column: alimentos_acabaram_antes_ter_dinheiro_comprar_mais
	 * @param alimentosAcabaramAntesTerDinheiroComprarMais the alimentos_acabaram_antes_ter_dinheiro_comprar_mais value
	 */
	public void setAlimentosAcabaramAntesTerDinheiroComprarMais (java.lang.Long alimentosAcabaramAntesTerDinheiroComprarMais) {
//        java.lang.Long alimentosAcabaramAntesTerDinheiroComprarMaisOld = this.alimentosAcabaramAntesTerDinheiroComprarMais;
		this.alimentosAcabaramAntesTerDinheiroComprarMais = alimentosAcabaramAntesTerDinheiroComprarMais;
//        this.getPropertyChangeSupport().firePropertyChange ("alimentosAcabaramAntesTerDinheiroComprarMais", alimentosAcabaramAntesTerDinheiroComprarMaisOld, alimentosAcabaramAntesTerDinheiroComprarMais);
	}



	/**
	 * Return the value associated with the column: comeu_alguns_alimentos_que_tinha_dinheiro_acabou
	 */
	public java.lang.Long getComeuAlgunsAlimentosQueTinhaDinheiroAcabou () {
		return getPropertyValue(this, comeuAlgunsAlimentosQueTinhaDinheiroAcabou, PROP_COMEU_ALGUNS_ALIMENTOS_QUE_TINHA_DINHEIRO_ACABOU); 
	}

	/**
	 * Set the value related to the column: comeu_alguns_alimentos_que_tinha_dinheiro_acabou
	 * @param comeuAlgunsAlimentosQueTinhaDinheiroAcabou the comeu_alguns_alimentos_que_tinha_dinheiro_acabou value
	 */
	public void setComeuAlgunsAlimentosQueTinhaDinheiroAcabou (java.lang.Long comeuAlgunsAlimentosQueTinhaDinheiroAcabou) {
//        java.lang.Long comeuAlgunsAlimentosQueTinhaDinheiroAcabouOld = this.comeuAlgunsAlimentosQueTinhaDinheiroAcabou;
		this.comeuAlgunsAlimentosQueTinhaDinheiroAcabou = comeuAlgunsAlimentosQueTinhaDinheiroAcabou;
//        this.getPropertyChangeSupport().firePropertyChange ("comeuAlgunsAlimentosQueTinhaDinheiroAcabou", comeuAlgunsAlimentosQueTinhaDinheiroAcabouOld, comeuAlgunsAlimentosQueTinhaDinheiroAcabou);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEsus)) return false;
		else {
			br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEsus usuarioCadsusEsus = (br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEsus) obj;
			if (null == this.getCodigo() || null == usuarioCadsusEsus.getCodigo()) return false;
			else return (this.getCodigo().equals(usuarioCadsusEsus.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}