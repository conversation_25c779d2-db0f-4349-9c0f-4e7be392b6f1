<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.investigacao">

    <class name="InvestigacaoAgravoPeste" table="investigacao_agr_peste">

        <id name="codigo"
            type="java.lang.Long"
            column="codigo" >
            <generator class="sequence">
                <param name="sequence">seq_investigacao_agr_peste</param>
            </generator>
        </id>

        <version column="version" name="version" type="long"/>

        <property
                name="flagInformacoesComplementares"
                column="flag_informacoes_complementares"
                type="java.lang.String"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo"
                name="registroAgravo"
                column="cd_registro_agravo"
                not-null="true"
        />

        <!-- INVESTIGAÇÃO -->
        <property
                name="dataInvestigacao"
                column="dt_investigacao"
                type="java.util.Date"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"
                name="ocupacaoCbo"
                column="ocupacao_cbo"
                not-null="false"
        />

        <!-- DADOS EPIDEMIOLOGICOS -->
        <property
                name="cumpreCondicoesBasicasRisco"
                column="cumpre_condicoes_basicas_risco"
                type="java.lang.Long"
        />
        <property
                name="casoAssociadoEventosPositivos"
                column="caso_associado_eventos_positivos"
                type="java.lang.Long"
        />

        <!-- DADOS CLINICOS -->
        <property
                name="sinaisSintomasCompativeis"
                column="sinais_sintomas_compativeis"
                type="java.lang.Long"
        />
        <property
                name="sintomalogiaEspecificaGanglionar"
                column="sintomalogia_especifica_ganglionar"
                type="java.lang.Long"
        />
        <property
                name="sintomalogiaEspecificaPulmonar"
                column="sintomalogia_especifica_pulmonar"
                type="java.lang.Long"
        />

        <!-- DADOS LABORATORIAIS -->
        <property
                name="exameBacteriologicoHemocultura"
                column="exame_bacteriologico_hemocultura"
                type="java.lang.Long"
        />
        <property
                name="exameBacteriologicoEsfregacaoDireto"
                column="exame_bacteriologico_esfregaco_direto"
                type="java.lang.Long"
        />

        <property
                name="dataColetaS1"
                column="data_coleta_s1"
                type="java.util.Date"
        />
        <property
                name="dataColetaS2"
                column="data_coleta_s2"
                type="java.util.Date"
        />

        <property
                name="resultadoSorologiaS1"
                column="resultado_sorologia_s1"
                type="java.lang.Long"
        />
        <property
                name="resultadoSorologiaS2"
                column="resultado_sorologia_s2"
                type="java.lang.Long"
        />

        <property
                name="resultadoHemoaglutinacaoIgm"
                column="resultado_hemoaglutinacao_igm"
                type="java.lang.Long"
        />
        <property
                name="resultadoHemoaglutinacaoIgmTitulo"
                column="resultado_hemoaglutinacao_igm_titulo"
                type="java.lang.String"
                length="5"
        />
        <property
                name="resultadoHemoaglutinacaoIgg"
                column="resultado_hemoaglutinacao_igg"
                type="java.lang.Long"
        />
        <property
                name="resultadoHemoaglutinacaoIggTitulo"
                column="resultado_hemoaglutinacao_igg_titulo"
                type="java.lang.String"
                length="5"
        />

        <!-- CASO AUTOCTONE -->
        <property
                name="casoAutoctone"
                column="caso_autoctone"
                type="java.lang.Long"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Cidade"
                name="cidadeLocalInfeccao"
                not-null="false">
            <column name="cd_cidade_infeccao"/>
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Pais"
                name="paisLocalInfeccao"
                not-null="false">
            <column name="cd_pais_infeccao"/>
        </many-to-one>

        <property
                name="distritoLocalInfeccao"
                column="str_distrito_infeccao"
                type="java.lang.String"
                length="200"
        />

        <property
                name="bairroLocalInfeccao"
                column="str_bairro_infeccao"
                type="java.lang.String"
                length="200"
        />

        <!-- CONCLUSAO -->
        <property
                name="casoTratado"
                column="caso_tratado"
                type="java.lang.Long"
        />
        <property
                name="controleFocal"
                column="controle_focal"
                type="java.lang.Long"
        />
        <property
                name="classificacaoFinal"
                column="classificacao_final"
                type="java.lang.Long"
        />
        <property
                name="criterioConfirmacaoDescarte"
                column="criterio_confirmacao_descarte"
                type="java.lang.Long"
        />
        <property
                name="classificacaoFormaClinica"
                column="classificacao_forma_clinica"
                type="java.lang.Long"
        />
        <property
                name="gravidade"
                column="gravidade"
                type="java.lang.Long"
        />
        <property
                name="doencaRelacionadaTrabalho"
                column="doenca_relacionada_trabalho"
                type="java.lang.Long"
        />
        <property
                name="evolucaoCaso"
                column="evolucao_caso"
                type="java.lang.Long"
        />

        <property
                name="dataObito"
                column="data_obito"
                type="java.util.Date"
        />

        <!-- OBS -->
        <property
                name="observacao"
                column="observacao"
                type="java.lang.String"
        />

        <!-- Encerramento -->
        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                name="usuarioEncerramento"
                column="cd_usuario_encerramento"
                not-null="false"
        />

    </class>
</hibernate-mapping>