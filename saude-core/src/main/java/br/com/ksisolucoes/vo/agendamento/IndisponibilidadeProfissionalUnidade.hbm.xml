<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
	
<hibernate-mapping package="br.com.ksisolucoes.vo.agendamento"  >
	<class name="IndisponibilidadeProfissionalUnidade" table="indisponibilidade_prof_unidade" >

		<id	name="codigo" type="java.lang.Long"	column="cd_indisponib_prof_unidade">
			<generator class="sequence">
				<param name="sequence">seq_indisponibilidade_prof_unidade</param>
			</generator>
		</id>

		<version column="version" name="version" type="long" />

		<many-to-one column="cd_profissional_solic" class="br.com.ksisolucoes.vo.cadsus.Profissional" name="profissionalSolicitante" />

		<many-to-one column="empresa" class="br.com.ksisolucoes.vo.basico.Empresa" name="empresa" />

		<property column="dt_inicial" not-null="true" type="date" name="dataInicial" />

		<property column="dt_final" not-null="true" type="date" name="dataFinal" />

		<property column="hora_inicial"	not-null="true"	type="java.util.Date" name="horaInicial" />

		<property column="hora_final"	not-null="true"	type="java.util.Date" name="horaFinal" />

		<property column="motivo_indisponibilidade"	type="java.lang.String" name="motivoIndisponibilidade" />

		<property column="motivo_cancelamento"	type="java.lang.String" name="motivoCancelamento" />

		<property column="situacao" name="situacao" type="java.lang.Long" />

		<property column="dt_cadastro" not-null="true" type="java.util.Date" name="dataCadastro" />

		<property column="dt_usuario" not-null="true" type="java.util.Date" name="dataUsuario" />

		<many-to-one column="cd_usuario" class="br.com.ksisolucoes.vo.controle.Usuario" name="usuario" />

	</class>
</hibernate-mapping>