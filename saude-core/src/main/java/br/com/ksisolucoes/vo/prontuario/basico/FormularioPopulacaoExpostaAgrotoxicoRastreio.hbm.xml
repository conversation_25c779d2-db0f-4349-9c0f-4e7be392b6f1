<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="FormularioPopulacaoExpostaAgrotoxicoRastreio" table="formulario_populacao_exposta_agrotoxico_rastreio" >
        
        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_formulario_populacao_exposta_agrotoxico_rastreio"
        >
            <generator class="sequence">
                <param name="sequence">seq_formulario_populacao_exposta_agrotoxico_rastreio</param>
            </generator>
        </id>
		<version column="version" name="version" type="long" />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.basico.EstratificacaoRisco"
                name="estratificacaoRisco"
                column="cd_estratificacao_risco"
                not-null="true"
        />

        <property
                name="zonaRural"
                type="java.lang.Long"
                column="zona_rural"
        />

        <property
                name="gestante"
                type="java.lang.Long"
                column="gestante"
        />

        <property
                name="ocupacao"
                type="java.lang.String"
                column="ocupacao"
        />

        <property
                name="contatoAgrotoxico"
                type="java.lang.Long"
                column="contato_agrotoxico"
        />

        <property
                name="contatoPor"
                type="java.lang.Long"
                column="contato_por"
        />

        <property
                name="atividadeAgricultura"
                type="java.lang.Long"
                column="atividade_agricultura"
        />

        <property
                name="atividadePecuaria"
                type="java.lang.Long"
                column="atividade_pecuaria"
        />

        <property
                name="atividadeAvicultura"
                type="java.lang.Long"
                column="atividade_avicultura"
        />

        <property
                name="atividadePiscicultura"
                type="java.lang.Long"
                column="atividade_piscicultura"
        />

        <property
                name="atividadeOutras"
                type="java.lang.Long"
                column="atividade_outras"
        />

        <property
                name="outraAtividade"
                type="java.lang.String"
                column="outra_atividade"
        />

        <property
                name="nomeAgrotoxico"
                type="java.lang.String"
                column="nome_agrotoxico"
        />

        <property
                name="tempoExposicao"
                type="java.lang.Long"
                column="tempo_exposicao"
        />

        <property
                name="unidadeTempoExposicao"
                type="java.lang.Long"
                column="unidade_tempo_exposicao"
        />

        <property
                name="temContatoAgrotoxico"
                type="java.lang.Long"
                column="tem_contato_agrotoxico"
        />

        <property
                name="contatoPorAtual"
                type="java.lang.String"
                column="contato_por_atual"
        />

        <property
                name="tempoExposicaoAtual"
                type="java.lang.Long"
                column="tempo_exposicao_atual"
        />

        <property
                name="tipoTempoExposicaoAtual"
                type="java.lang.Long"
                column="tipo_tempo_exposicao_atual"
        />

        <property
                name="dataUltimoContato"
                type="java.util.Date"
                column="data_ultimo_contato"
        />

        <property
                name="agrotoxicosTemContato"
                type="java.lang.String"
                column="agrotoxicos_tem_contato"
        />

        <property
                name="atividadeAtualAgricultura"
                type="java.lang.Long"
                column="atividade_atual_agricultura"
        />

        <property
                name="atividadeAtualPecuaria"
                type="java.lang.Long"
                column="atividade_atual_pecuaria"
        />

        <property
                name="atividadeAtualIndustria"
                type="java.lang.Long"
                column="atividade_atual_industria"
        />

        <property
                name="atividadeAtualDesinsetizacao"
                type="java.lang.Long"
                column="atividade_atual_desinsetizacao"
        />

        <property
                name="atividadeAtualAgenteEndemias"
                type="java.lang.Long"
                column="atividade_atual_agente_endemias"
        />

        <property
                name="atividadeAtualUsoDomestico"
                type="java.lang.Long"
                column="atividade_atual_domestico"
        />

        <property
                name="atividadeAtualOutros"
                type="java.lang.Long"
                column="atividade_atual_outros"
        />

        <property
                name="outrasAtividadesAtual"
                type="java.lang.String"
                column="outras_atividades_atual"
        />

        <property
                name="formaContatoAtualPreparo"
                type="java.lang.Long"
                column="forma_contato_atual_preparo"
        />

        <property
                name="formaContatoAtualDiluicao"
                type="java.lang.Long"
                column="forma_contato_atual_diluicao"
        />

        <property
                name="formaContatoAtualTratamentoSementes"
                type="java.lang.Long"
                column="forma_contato_atual_tratamento_sementes"
        />

        <property
                name="formaContatoAtualAplicacao"
                type="java.lang.Long"
                column="forma_contato_atual_aplicacao"
        />

        <property
                name="formaContatoAtualColheita"
                type="java.lang.Long"
                column="forma_contato_atual_colheita"
        />

        <property
                name="formaContatoAtualSupervisao"
                type="java.lang.Long"
                column="forma_contato_atual_supervisao"
        />

        <property
                name="formaContatoAtualArmazenamento"
                type="java.lang.Long"
                column="forma_contato_atual_armazenamento"
        />

        <property
                name="formaContatoAtualDescarteEmbalagem"
                type="java.lang.Long"
                column="forma_contato_atual_descarte_embalagem"
        />

        <property
                name="formaContatoAtualLimpezaEquipamento"
                type="java.lang.Long"
                column="forma_contato_atual_limpeza_equipamento"
        />

        <property
                name="formaContatoAtualLavagemRoupa"
                type="java.lang.Long"
                column="forma_contato_atual_lavagem_roupa"
        />

        <property
                name="formaContatoAtualCarga"
                type="java.lang.Long"
                column="forma_contato_atual_carga"
        />

        <property
                name="formaContatoAtualTransporte"
                type="java.lang.Long"
                column="forma_contato_atual_transporte"
        />

        <property
                name="formaContatoAtualControleExpedicao"
                type="java.lang.Long"
                column="forma_contato_atual_controle_expedicao"
        />

        <property
                name="formaContatoAtualProducao"
                type="java.lang.Long"
                column="forma_contato_atual_producao"
        />

        <property
                name="formaContatoAtualContaminacaoAmbiental"
                type="java.lang.Long"
                column="forma_contato_atual_contaminacao_ambiental"
        />

        <property
                name="formaContatoAtualOutras"
                type="java.lang.Long"
                column="forma_contato_atual_outras"
        />

        <property
                name="outrasFormasContatoAtual"
                type="java.lang.String"
                column="outras_formas_contato_atual"
        />

        <property
                name="nroIntoxicacoes"
                type="java.lang.Long"
                column="nro_intoxicacoes"
        />

        <property
                name="sintomasGastrointestinais"
                type="java.lang.Long"
                column="sintomas_gastrointestinais"
        />

        <property
                name="sintomasSensorio"
                type="java.lang.Long"
                column="sintomas_sensorio"
        />

        <property
                name="sintomasPele"
                type="java.lang.Long"
                column="sintomas_pele"
        />

        <property
                name="sintomasCardiovascular"
                type="java.lang.Long"
                column="sintomas_cardiovascular"
        />

        <property
                name="sintomasRespiratoria"
                type="java.lang.Long"
                column="sintomas_resíratoria"
        />
        <property
                name="sintomasNaoLembra"
                type="java.lang.Long"
                column="sintomas_nao_lembra"
        />

        <property
                name="sintomasOutros"
                type="java.lang.Long"
                column="sintomas_outros"
        />

        <property
                name="outrosSintomas"
                type="java.lang.String"
                column="outros_sintomas"
        />

        <property
                name="temAgrotoxicoUnidade"
                type="java.lang.Long"
                column="tem_agrotoxico_unidade"
        />

        <property
                name="observacao"
                type="java.lang.String"
                column="observacao"
        />

    </class>
</hibernate-mapping>