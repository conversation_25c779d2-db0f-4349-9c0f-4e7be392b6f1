package br.com.ksisolucoes.vo.basico;

import br.com.ksisolucoes.enums.IEnumSum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.base.BaseInvestigacaoAgravo;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;


public class InvestigacaoAgravo extends BaseInvestigacaoAgravo implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public InvestigacaoAgravo () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public InvestigacaoAgravo (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public InvestigacaoAgravo (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		br.com.ksisolucoes.vo.cadsus.Profissional profissionalResponsavelNotificacao,
		java.util.Date dataCadastro,
		java.util.Date dataNotificacao) {

		super (
			codigo,
			registroAgravo,
			profissionalResponsavelNotificacao,
			dataCadastro,
			dataNotificacao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

	public enum TipoClassificacao {

		NAO_GRAVE_EANG(1L),
		GRAVE_EAG(2L),
		ERRO_IMUNIZACAO_EI(3L);
		private Long value;

		private TipoClassificacao(Long value) {
			this.value = value;
		}

		public Long getValue() {
			return value;
		}
	}

	public enum TipoHistoricoConvulsao {

		SEM_HISTORIA(1L),
		AFEBRIL(2L),
		FEBRIL(3L),
		IGNORADO(4L);
		private Long value;

		private TipoHistoricoConvulsao(Long value) {
			this.value = value;
		}

		public Long getValue() {
			return value;
		}
	}

	public enum Evolucao {

		EVENTO_ADVERSO_BCG_SEM_NECESSIDADE_ADM_ISO(1L),
		EVENTO_ADVERSO_BCG_COM_NECESSIDADE_ADM_ISO(2L),
		EVENTO_ADVERSO_BCG_COM_NECESSIDADE_ADM_ESQ_TRI_QUA(3L),
		CURA_SEM_SEQUELAS(4L),
		CURA_COM_SEQUELAS(5L),
		OBITO(6L),
		NAO_EAPV(7L),
		PERDA_SEGUIMENTO(8L),
		EM_ACOMPANHAMENTO(9L);

		private Long value;

		private Evolucao(Long value) {
			this.value = value;
		}

		public Long getValue() {
			return value;
		}
	}

	public enum TipoAtendimentoMedico {

		AMBULATORIO_CONSULTORIO(1L),
		OBSERVACAO_PERMANENCIA_UNIDADE(2L),
		HOSPITALIZACAO_PERMANCIA_UNIDADE(3L);
		private Long value;

		private TipoAtendimentoMedico(Long value) {
			this.value = value;
		}

		public Long getValue() {
			return value;
		}
	}

    public enum ClassificacaoFinal {

        EA_NAO_GRAVE(1L),
        EA_GRAVE(2L),
        ERRO_IMUNIZACAO(3L),
        INCLASSIFICAVEL(4L);

        private Long value;

        private ClassificacaoFinal(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }
    }

    public enum ErroImunizacao {

        TIPO_IMUNUBIOLOGICO_UTILIZADO(1L),
        ERROS_ADMINISTRACAO_TECNICA_APLICACAO(2L),
        ERROS_ADMINISTRACAO_USO_INCORRETO(3L),
        ERROS_MANUSEIO(4L),
        INTERVALO_INADEQUADO_ENTRE_DOSES(5L),
        VALIDADE_VENCIDA(6L),
        ERROS_PRESCRICAO_INDICACOES(7L),
        NAO_AVALIACAO_CONTRAINDICACOES_PRECAUCOES(8L),
        OUTROS(9L);

        private Long value;

        private ErroImunizacao(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }
    }

    public String getDescricaoErroImunizacao() {
    	if (ErroImunizacao.TIPO_IMUNUBIOLOGICO_UTILIZADO.value.equals(getErrosImunizacao())) {
			return Bundle.getStringApplication("rotulo_tipo_imunibiologico_utilizado");
		} else if (ErroImunizacao.ERROS_ADMINISTRACAO_TECNICA_APLICACAO.value.equals(getErrosImunizacao())) {
			return Bundle.getStringApplication("rotulo_erros_administracao_tecnica_aplicacao");
		} else if (ErroImunizacao.ERROS_ADMINISTRACAO_USO_INCORRETO.value.equals(getErrosImunizacao())) {
			return Bundle.getStringApplication("rotulo_erros_administracao_uso_incorreto");
		} else if (ErroImunizacao.ERROS_MANUSEIO.value.equals(getErrosImunizacao())) {
			return Bundle.getStringApplication("rotulo_erros_manuseio");
		} else if (ErroImunizacao.INTERVALO_INADEQUADO_ENTRE_DOSES.value.equals(getErrosImunizacao())) {
			return Bundle.getStringApplication("rotulo_intervalo_inadequado_entre_doses");
		} else if (ErroImunizacao.VALIDADE_VENCIDA.value.equals(getErrosImunizacao())) {
			return Bundle.getStringApplication("rotulo_validade_vencida");
		} else if (ErroImunizacao.ERROS_PRESCRICAO_INDICACOES.value.equals(getErrosImunizacao())) {
			return Bundle.getStringApplication("rotulo_erros_prescricao_indicacoes");
		} else if (ErroImunizacao.NAO_AVALIACAO_CONTRAINDICACOES_PRECAUCOES.value.equals(getErrosImunizacao())) {
			return Bundle.getStringApplication("rotulo_nao_avaliacao_contraindicacoes_precaucoes");
		} else if (ErroImunizacao.OUTROS.value.equals(getErrosImunizacao())) {
			return Bundle.getStringApplication("rotulo_outros_descrever_detalhadamente_erros");
		}
		return Bundle.getStringApplication("rotulo_nao_informado");
	}

    public enum CondutaErroImunizacao {

        DOSE_CONSIDERADA_VALIDA(1L),
        DOSE_CONSIDERADA_INVALIDA(2L);

        private Long value;

        private CondutaErroImunizacao(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }
    }

    public enum DoseConsideradaInvalida {

        REPETIR_DOSE_UNICA_MAIS_RAPIDO_POSSIVEL(1L),
        REPETIR_DOSE_MAIS_RAPIDO_POSSIVEL_CONSIDERANDO_INTERVALO_MINIMO(2L),
        REPETIR_DOSE_COM_APRAZAMENTO_REFORCO(3L),
        ACOMPANHAMENTO_DOSAGEM_ANTICORPOS(4L),
        OUTROS(5L);

        private Long value;

        private DoseConsideradaInvalida(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }
    }


    public enum TipoSelecionado {

		SIM(1L),
		NAO(2L),
		IGNORADO(3L);
		private Long value;

		private TipoSelecionado(Long value) {
			this.value = value;
		}

		public Long getValue() {
			return value;
		}
	}

	public enum DoencasPreExistentes implements IEnumSum {

		AIDS(1L, 1L, Bundle.getStringApplication("rotulo_aids_hiv")),
		ALERGIA_MEDICAMENTO(2L, 2L, Bundle.getStringApplication("rotulo_alergia_medicamento")),
		ALERGIA_ALIMENTAR(4L, 3L, Bundle.getStringApplication("rotulo_alergia_alimentar")),
		DIABETES(8L, 4L, Bundle.getStringApplication("rotulo_diabete")),
		DOENCA_AUTOIMUNE(16L, 5L, Bundle.getStringApplication("rotulo_doenca_autoimune")),
		DOENCA_CARDIACA(32L, 6L, Bundle.getStringApplication("rotulo_doenca_cardiaca")),
		DOENCA_HEPATICA(64L, 7L, Bundle.getStringApplication("rotulo_doenca_hepatica")),
		DOENCA_NEUROLOGICA_PSIQUIATRICA(128L, 8L, Bundle.getStringApplication("rotulo_doenca_neurologica_psiquiatrica")),
		DOENCA_PULMONAR(256L, 9L, Bundle.getStringApplication("rotulo_doenca_pulmonar")),
		DOENCA_RENAL(512L, 10L, Bundle.getStringApplication("rotulo_doenca_renal")),
		DOENCA_OUTRAS(1024L, 11L, Bundle.getStringApplication("rotulo_outras_especificar")),;

		private Long sum;
		private Long value;
		private String descricao;

		private DoencasPreExistentes(Long sum, Long value, String descricao) {
			this.value = value;
			this.sum = sum;
			this.descricao = descricao;
		}

		public static DoencasPreExistentes valueOf(Long sum) {
			for (DoencasPreExistentes doencasPreExistentes : DoencasPreExistentes.values()) {
				if (doencasPreExistentes.sum().equals(sum)) {
					return doencasPreExistentes;
				}
			}
			return null;
		}

		@Override
		public Long sum() {
			return this.sum;
		}

		@Override
		public Object value() {
			return this.value;
		}

		@Override
		public String descricao() {
			return this.descricao;
		}

	}

	public String getDescricaoCriancaAleitamento() {
    	if (RepositoryComponentDefault.SIM_LONG.equals(getFlagCriancaAleitamento())) {
			return Bundle.getStringApplication("rotulo_sim");
		}
		return Bundle.getStringApplication("rotulo_nao");
	}

	public String getDescricaoClassificacao() {
    	if (TipoClassificacao.NAO_GRAVE_EANG.value.equals(getClassificacao())) {
			return Bundle.getStringApplication("rotulo_nao_grave_eang");
		} else if (TipoClassificacao.GRAVE_EAG.value.equals(getClassificacao())) {
			return Bundle.getStringApplication("rotulo_grave_eag");
		} else if (TipoClassificacao.ERRO_IMUNIZACAO_EI.value.equals(getClassificacao())) {
			return Bundle.getStringApplication("rotulo_erro_imunizacao_ei");
		}
		return Bundle.getStringApplication("rotulo_nao_informado");
	}

	public String getDescricaoTipoSelecionado(Long tipoSelecionado) {
		if (TipoSelecionado.SIM.value.equals(tipoSelecionado)) {
			return Bundle.getStringApplication("rotulo_sim");
		} else if (TipoSelecionado.NAO.value.equals(tipoSelecionado)) {
			return Bundle.getStringApplication("rotulo_nao");
		} else if (TipoSelecionado.IGNORADO.value.equals(tipoSelecionado)) {
			return Bundle.getStringApplication("rotulo_ignorado");
		}
		return Bundle.getStringApplication("rotulo_nao_informado");
	}

	public String getDescricaoHistoriaPreviaConvulsao() {
		if (TipoHistoricoConvulsao.SEM_HISTORIA.value.equals(getHistoriaPreviaConvulsao())) {
			return Bundle.getStringApplication("rotulo_sem_historia_convulsao");
		} else if (TipoHistoricoConvulsao.AFEBRIL.value.equals(getHistoriaPreviaConvulsao())) {
			return Bundle.getStringApplication("rotulo_convulsao_afebril");
		} else if (TipoHistoricoConvulsao.FEBRIL.value.equals(getHistoriaPreviaConvulsao())) {
			return Bundle.getStringApplication("rotulo_convulsao_febril");
		}
		return Bundle.getStringApplication("rotulo_ignorado");
	}

	public String getDescricaoTipoAtendimentoMedico() {
		if (TipoAtendimentoMedico.AMBULATORIO_CONSULTORIO.value.equals(getTipoAtendimentoMedico())) {
			return Bundle.getStringApplication("rotulo_ambulatorio_consultorio");
		} else if (TipoAtendimentoMedico.OBSERVACAO_PERMANENCIA_UNIDADE.value.equals(getTipoAtendimentoMedico())) {
			return Bundle.getStringApplication("rotulo_observacao_permanencia_unidade");
		} else if (TipoAtendimentoMedico.HOSPITALIZACAO_PERMANCIA_UNIDADE.value.equals(getTipoAtendimentoMedico())) {
			return Bundle.getStringApplication("rotulo_hospitalizacao_permancia_unidade");
		}
		return Bundle.getStringApplication("rotulo_nao_informado");
	}

	public String getDescricaoEvolucao() {
		if (Evolucao.EVENTO_ADVERSO_BCG_SEM_NECESSIDADE_ADM_ISO.value.equals(getEvolucao())) {
			return Bundle.getStringApplication("rotulo_evento_adverso_bcg_sem_necessidade_adm_iso");
		} else if (Evolucao.EVENTO_ADVERSO_BCG_COM_NECESSIDADE_ADM_ISO.value.equals(getEvolucao())) {
			return Bundle.getStringApplication("rotulo_evento_adverso_bcg_com_necessidade_adm_iso");
		} else if (Evolucao.EVENTO_ADVERSO_BCG_COM_NECESSIDADE_ADM_ESQ_TRI_QUA.value.equals(getEvolucao())) {
			return Bundle.getStringApplication("rotulo_evento_adverso_bcg_com_necessidade_adm_esq_tri_qua");
		} else if (Evolucao.CURA_SEM_SEQUELAS.value.equals(getEvolucao())) {
			return Bundle.getStringApplication("rotulo_cura_sem_sequelas");
		} else if (Evolucao.CURA_COM_SEQUELAS.value.equals(getEvolucao())) {
			return Bundle.getStringApplication("rotulo_cura_com_sequelas");
		} else if (Evolucao.OBITO.value.equals(getEvolucao())) {
			return Bundle.getStringApplication("rotulo_obito");
		} else if (Evolucao.NAO_EAPV.value.equals(getEvolucao())) {
			return Bundle.getStringApplication("rotulo_nao_eapv");
		} else if (Evolucao.PERDA_SEGUIMENTO.value.equals(getEvolucao())) {
			return Bundle.getStringApplication("rotulo_perda_seguimento");
		} else if (Evolucao.EM_ACOMPANHAMENTO.value.equals(getEvolucao())) {
			return Bundle.getStringApplication("rotulo_em_acompanhamento");
		}
		return Bundle.getStringApplication("rotulo_nao_informado");
	}

	public String getDescricaoClassificacaoFinal() {
		if (ClassificacaoFinal.EA_NAO_GRAVE.value.equals(getClassificacaoFinal())) {
			return Bundle.getStringApplication("rotulo_ea_nao_grave");
		} else if (ClassificacaoFinal.EA_GRAVE.value.equals(getClassificacaoFinal())) {
			return Bundle.getStringApplication("rotulo_ea_grave");
		} else if (ClassificacaoFinal.ERRO_IMUNIZACAO.value.equals(getClassificacaoFinal())) {
			return Bundle.getStringApplication("rotulo_erro_imunizacao");
		} else if (ClassificacaoFinal.INCLASSIFICAVEL.value.equals(getClassificacaoFinal())) {
			return Bundle.getStringApplication("rotulo_inclassificavel");
		}
		return Bundle.getStringApplication("rotulo_nao_informado");
	}

	public String getDescricaoCondutaFrenteErroImunizacao() {
		if (CondutaErroImunizacao.DOSE_CONSIDERADA_VALIDA.value.equals(getCondutaErrosImunizacao())) {
			return Bundle.getStringApplication("rotulo_dose_considerada_valida");
		} else if (CondutaErroImunizacao.DOSE_CONSIDERADA_INVALIDA.value.equals(getCondutaErrosImunizacao())) {
			return Bundle.getStringApplication("rotulo_dose_considerada_invalida");
		}
		return Bundle.getStringApplication("rotulo_nao_informado");
	}

	public String getDescricaoCondutaFrenteErroImunizacaoItem() {
		if (DoseConsideradaInvalida.REPETIR_DOSE_UNICA_MAIS_RAPIDO_POSSIVEL.value.equals(getCondutaErrosImunizacaoItem())) {
			return Bundle.getStringApplication("rotulo_repetir_dose_unica");
		} else if (DoseConsideradaInvalida.REPETIR_DOSE_MAIS_RAPIDO_POSSIVEL_CONSIDERANDO_INTERVALO_MINIMO.value.equals(getCondutaErrosImunizacaoItem())) {
			return Bundle.getStringApplication("rotulo_repetir_dose_intervalo_minimo");
		} else if (DoseConsideradaInvalida.REPETIR_DOSE_COM_APRAZAMENTO_REFORCO.value.equals(getCondutaErrosImunizacaoItem())) {
			return Bundle.getStringApplication("rotulo_repetir_dose_aprazamento_reforco");
		} else if (DoseConsideradaInvalida.ACOMPANHAMENTO_DOSAGEM_ANTICORPOS.value.equals(getCondutaErrosImunizacaoItem())) {
			return Bundle.getStringApplication("rotulo_acompanhamento_dosagem_anticorpos");
		} else if (DoseConsideradaInvalida.OUTROS.value.equals(getCondutaErrosImunizacaoItem())) {
			return Bundle.getStringApplication("rotulo_outros_especificar");
		}
		return "";
	}

	public String getDescricaoMulherAmamentando() {
		if (RepositoryComponentDefault.SIM_LONG.equals(getFlagMulherAmamentando())) {
			return Bundle.getStringApplication("rotulo_sim");
		}
		return Bundle.getStringApplication("rotulo_nao");
	}

}