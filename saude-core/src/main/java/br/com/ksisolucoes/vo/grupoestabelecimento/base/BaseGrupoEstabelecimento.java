package br.com.ksisolucoes.vo.grupoestabelecimento.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the grupo_estabelecimento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="grupo_estabelecimento"
 */

public abstract class BaseGrupoEstabelecimento extends BaseRootVO implements Serializable {

	public static String REF = "GrupoEstabelecimento";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_FORMATACAO_ALVARA = "formatacaoAlvara";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_CLASSIFICACAO = "classificacao";


	// constructors
	public BaseGrupoEstabelecimento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseGrupoEstabelecimento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseGrupoEstabelecimento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.ClassificacaoGrupoEstabelecimento classificacao,
		java.lang.String descricao) {

		this.setCodigo(codigo);
		this.setClassificacao(classificacao);
		this.setDescricao(descricao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.lang.String formatacaoAlvara;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.ClassificacaoGrupoEstabelecimento classificacao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_grupo_estabelecimento"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_grupo_estabelecimento
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_grupo_estabelecimento
	 * @param descricao the ds_grupo_estabelecimento value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: formatacao_alvara
	 */
	public java.lang.String getFormatacaoAlvara () {
		return getPropertyValue(this, formatacaoAlvara, PROP_FORMATACAO_ALVARA); 
	}

	/**
	 * Set the value related to the column: formatacao_alvara
	 * @param formatacaoAlvara the formatacao_alvara value
	 */
	public void setFormatacaoAlvara (java.lang.String formatacaoAlvara) {
//        java.lang.String formatacaoAlvaraOld = this.formatacaoAlvara;
		this.formatacaoAlvara = formatacaoAlvara;
//        this.getPropertyChangeSupport().firePropertyChange ("formatacaoAlvara", formatacaoAlvaraOld, formatacaoAlvara);
	}



	/**
	 * Return the value associated with the column: cd_cla_grupo_estab
	 */
	public br.com.ksisolucoes.vo.vigilancia.ClassificacaoGrupoEstabelecimento getClassificacao () {
		return getPropertyValue(this, classificacao, PROP_CLASSIFICACAO); 
	}

	/**
	 * Set the value related to the column: cd_cla_grupo_estab
	 * @param classificacao the cd_cla_grupo_estab value
	 */
	public void setClassificacao (br.com.ksisolucoes.vo.vigilancia.ClassificacaoGrupoEstabelecimento classificacao) {
//        br.com.ksisolucoes.vo.vigilancia.ClassificacaoGrupoEstabelecimento classificacaoOld = this.classificacao;
		this.classificacao = classificacao;
//        this.getPropertyChangeSupport().firePropertyChange ("classificacao", classificacaoOld, classificacao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.grupoestabelecimento.GrupoEstabelecimento)) return false;
		else {
			br.com.ksisolucoes.vo.grupoestabelecimento.GrupoEstabelecimento grupoEstabelecimento = (br.com.ksisolucoes.vo.grupoestabelecimento.GrupoEstabelecimento) obj;
			if (null == this.getCodigo() || null == grupoEstabelecimento.getCodigo()) return false;
			else return (this.getCodigo().equals(grupoEstabelecimento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}