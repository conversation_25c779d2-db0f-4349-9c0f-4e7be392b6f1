package br.com.ksisolucoes.vo.cadsus;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.UsuarioEmpresa;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ProfissionalHelper implements Serializable {

    public static Empresa getEmpresaFaturamentoBpaProfissional(Empresa empresa, Profissional profissional){
        Empresa empresaBpa = null;
        if (profissional != null) {
            Usuario usuario = LoadManager.getInstance(Usuario.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Usuario.PROP_PROFISSIONAL), profissional))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Usuario.PROP_FLAG_USUARIO_TEMPORARIO), RepositoryComponentDefault.NAO_LONG))
                    .setMaxResults(1).start().getVO();

            if (usuario != null) {
                UsuarioEmpresa usuarioEmpresa = LoadManager.getInstance(UsuarioEmpresa.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioEmpresa.PROP_USUARIO), usuario))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioEmpresa.PROP_EMPRESA), empresa))
                        .setMaxResults(1).start().getVO();
                if (usuarioEmpresa != null && usuarioEmpresa.getEmpresaBpa() != null) {
                    empresaBpa = usuarioEmpresa.getEmpresaBpa();
                }
            }
        }

        return empresaBpa != null ? empresaBpa : empresa;
    }

    public static boolean isMedicCbo(TabelaCbo tabelaCbo) {
        if (tabelaCbo == null || tabelaCbo.getCbo() == null) return false;

        final String[] medicCboPrefixList = {"2251", "2252", "2253"};

        return StringUtils.startsWithAny(tabelaCbo.getCbo(), medicCboPrefixList);
    }

}
