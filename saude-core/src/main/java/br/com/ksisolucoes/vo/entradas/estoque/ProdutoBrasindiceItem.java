package br.com.ksisolucoes.vo.entradas.estoque;

import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.log.Loggable;
import java.io.Serializable;

import br.com.ksisolucoes.vo.entradas.estoque.base.BaseProdutoBrasindiceItem;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.ParseException;
import java.util.Locale;
import javax.swing.text.MaskFormatter;

public class ProdutoBrasindiceItem extends BaseProdutoBrasindiceItem implements CodigoManager {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public ProdutoBrasindiceItem() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public ProdutoBrasindiceItem(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public ProdutoBrasindiceItem(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.entradas.estoque.ProdutoBrasindice produtoBrasindice,
            java.lang.Double preco,
            java.lang.String tipoPreco,
            java.lang.Long versaoBrasindice,
            java.util.Date dataInicioVigencia) {

        super(
                codigo,
                produtoBrasindice,
                preco,
                tipoPreco,
                versaoBrasindice,
                dataInicioVigencia);
    }

    /*[CONSTRUCTOR MARKER END]*/
    public String getDataVigenciaFormatado() {
        return Data.formatar(getDataInicioVigencia());
    }

    public String getPrecoFormatado() {
        if (getPreco() != null) {
            DecimalFormat df = new DecimalFormat("##,###,###,##0.00", new DecimalFormatSymbols (new Locale ("pt", "BR")));  
            df.setMinimumFractionDigits(2);   
            df.setParseBigDecimal (true);  
            return df.format(getPreco());
        }
        return "";

    }

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}
