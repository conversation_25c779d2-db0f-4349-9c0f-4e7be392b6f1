package br.com.ksisolucoes.vo.prontuario.procedimento;

import java.io.Serializable;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.procedimento.base.BaseTabelaCboSubGrupo;



public class TabelaCboSubGrupo extends BaseTabelaCboSubGrupo implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public TabelaCboSubGrupo () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public TabelaCboSubGrupo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboSubGrupoPK id) {
		super(id);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setId( (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboSubGrupoPK)key );
    }

    public Serializable getCodigoManager() {
        return this.getId();
    }
}