package br.com.ksisolucoes.vo.cadsus;

import org.apache.commons.lang.StringUtils;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ProfissionalCargaHorariaHelper implements Serializable {

    public String getSelectValidaCargaHoraria(String propSelect, Long codigoEmpresa, Long codigoProfissional, String propCodigoProfissional, boolean validaDataAtivacao){
        return "select "+propSelect+" from ProfissionalCargaHoraria pch where "+getWhereValidaCargaHoraria("pch", codigoEmpresa, codigoProfissional, propCodigoProfissional, validaDataAtivacao);
    }

    public String getWhereValidaCargaHoraria(String alias, Long codigoEmpresa, Long codigoProfissional, String propCodigoProfissional, boolean validaDataAtivacao){
        return getWhereValidaCargaHoraria(alias, codigoEmpresa, codigoProfissional, propCodigoProfissional, null, validaDataAtivacao);
    }
    
    public String getWhereValidaCargaHoraria(String alias, Long codigoEmpresa, Long codigoProfissional, String propCodigoProfissional, String propCodigoEmpresa,boolean validaDataAtivacao){
        String hql = "";
        if (validaDataAtivacao) {
            hql = " current_date between coalesce(" + alias + ".dataAtivacao, current_date + 1) "
                    + " and coalesce(" + alias + ".dataDesativacao, current_date + 1) "
                    + " and coalesce(" + alias + ".tipoRegistro,'" + ProfissionalCargaHoraria.TIPO_REGISTRO_INCLUSAO + "') <> '" + ProfissionalCargaHoraria.TIPO_REGISTRO_ERRO + "'  ";
        } else {
            hql = " coalesce(" + alias + ".tipoRegistro,'" + ProfissionalCargaHoraria.TIPO_REGISTRO_INCLUSAO + "') <> '" + ProfissionalCargaHoraria.TIPO_REGISTRO_ERRO + "'  ";
        }

        if (codigoEmpresa!=null) {
            hql += " and "+alias+".empresa.codigo = "+codigoEmpresa+" ";
        }

        if (codigoProfissional!=null) {
            hql += " and "+alias+".profissional.codigo = "+codigoProfissional +" ";
        }

        if (StringUtils.trimToNull(propCodigoProfissional)!=null) {
            hql += " and "+alias+".profissional.codigo = "+propCodigoProfissional+" ";
        }
        if (StringUtils.trimToNull(propCodigoEmpresa)!=null) {
            hql += " and "+alias+".empresa.codigo = "+propCodigoEmpresa+" ";
        }

        return hql;
    }

    public String getWhereValidaCargaHorariaSemUnidade(String alias, Long codigoProfissional, String propCodigoProfissional, boolean validaDataAtivacao){
        String hql = "";
        if (validaDataAtivacao) {
            hql = " current_date between coalesce(" + alias + ".dataAtivacao, current_date + 1) "
                    + " and coalesce(" + alias + ".dataDesativacao, current_date + 1) "
                    + " and coalesce(" + alias + ".tipoRegistro,'" + ProfissionalCargaHoraria.TIPO_REGISTRO_INCLUSAO + "') <> '" + ProfissionalCargaHoraria.TIPO_REGISTRO_ERRO + "'  ";
        } else {
            hql = " coalesce(" + alias + ".tipoRegistro,'" + ProfissionalCargaHoraria.TIPO_REGISTRO_INCLUSAO + "') <> '" + ProfissionalCargaHoraria.TIPO_REGISTRO_ERRO + "'  ";
        }

        if (codigoProfissional!=null) {
            hql += " and "+alias+".profissional.codigo = "+codigoProfissional +" ";
        }

        if (StringUtils.trimToNull(propCodigoProfissional)!=null) {
            hql += " and " + alias + ".profissional.codigo = " + propCodigoProfissional + " ";
        }

        return hql;
    }

}
