<?xml version="1.0"?> 
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.basico"  >
	<class 
		name="PessoaEndereco"
		table="pessoa_endereco"
	>
		<composite-id name="id" class="PessoaEnderecoPK">
			<key-many-to-one
				name="tipoEndereco"
				class="br.com.ksisolucoes.vo.basico.TipoEndereco"
				column="cod_tip_end"
			/>
			<key-many-to-one
				name="pessoa"
				class="br.com.ksisolucoes.vo.basico.Pessoa"
				column="cod_pessoa"
			/>
		</composite-id> <version column="version" name="version" type="long" />

		<!-- please tell <PERSON> that the type 'descricao' could not be resolved.. defaulting to java.lang.String -->

        <!-- Somente leitura, utilizado para consultas pela Criteria -->		
		<many-to-one
			name="roTipoEndereco"
			class="br.com.ksisolucoes.vo.basico.TipoEndereco"
			column="cod_tip_end"
			insert="false"
			update="false"
		/>
		<many-to-one
			name="roPessoa"
			class="br.com.ksisolucoes.vo.basico.Pessoa"
			column="cod_pessoa"
			insert="false"
			update="false"
		/>
        <!-- Somente leitura, utilizado para consultas pela Criteria -->
		
		<property
			name="bairro"
			column="bairro"
			type="java.lang.String"
			not-null="false"
			length="40"
		/>
		<property
			name="celular"
			column="celular"
			type="string"
			not-null="false"
			length="20"
		/>
		<property
			name="complemento"
			column="complemento"
			type="string"
			not-null="false"
			length="250"
		/>
		<property
			name="logradouro"
			column="logradouro"
			type="string"
			not-null="false"
			length="10"
		/>
		<property
			name="fax"
			column="fax"
			type="string"
			not-null="false"
			length="15"
		/>
		<property
			name="nomeLogradouro"
			column="nome_logradouro"
			type="string"
			not-null="true"
			length="60"
		/>
		<!-- please tell Joe Hudson that the type 'codigo' could not be resolved.. defaulting to java.lang.String -->
		<property
			name="usuario"
			column="usuario"
			type="java.lang.Long"
			not-null="true"
			length="4"
		/>
		<property
			name="numero"
			column="numero"
			type="java.lang.Long"
			not-null="false"
			length="4"
		/>
		<property
			name="homePage"
			column="home_page"
			type="string"
			not-null="false"
			length="50"
		/>
		<property
			name="cep"
			column="cep"
			type="string"
			not-null="true"
			length="10"
		/>
		<property
			name="ddd"
			column="ddd"
			type="string"
			not-null="false"
			length="4"
		/>
		<property
			name="ddi"
			column="ddi"
			type="string"
			not-null="false"
			length="4"
		/>
		<property
			name="email"
			column="email"
			type="string"
			not-null="false"
			length="30"
		/>
		<property
			name="telefone"
			column="telefone"
			type="string"
			not-null="false"
			length="20"
		/>
		<property
			name="dataUsuario"
			column="dt_usuario"
			type="date"
			not-null="true"
			length="4"
		/>
                
        <property
			name="anoFixacao"
			column="ano_fixacao"
			type="java.lang.Long"
			not-null="false"
			length="4"
		/>

		<many-to-one
			name="cidade"
			class="br.com.ksisolucoes.vo.basico.Cidade"
			not-null="true"
		>
			<column name="cod_cid"/>
		</many-to-one>
		
		<many-to-one
			name="localidade"
			class="br.com.ksisolucoes.vo.basico.Localidade"
			not-null="false"
		>
			<column name="cod_loc"/>
		</many-to-one>
		
		
	</class>
</hibernate-mapping>