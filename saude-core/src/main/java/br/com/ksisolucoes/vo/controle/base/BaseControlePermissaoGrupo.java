package br.com.ksisolucoes.vo.controle.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the controle_permissoes_grupo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="controle_permissoes_grupo"
 */

public abstract class BaseControlePermissaoGrupo extends BaseRootVO implements Serializable {

	public static String REF = "ControlePermissaoGrupo";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_PERMISSAO = "permissao";
	public static final String PROP_CONTROLE_PROGRAMA_GRUPO = "controleProgramaGrupo";


	// constructors
	public BaseControlePermissaoGrupo () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseControlePermissaoGrupo (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.controle.ControleProgramaGrupo controleProgramaGrupo;
	private br.com.ksisolucoes.vo.controle.Permissao permissao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_ctr_permissao_grupo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_prg_grupo
	 */
	public br.com.ksisolucoes.vo.controle.ControleProgramaGrupo getControleProgramaGrupo () {
		return getPropertyValue(this, controleProgramaGrupo, PROP_CONTROLE_PROGRAMA_GRUPO); 
	}

	/**
	 * Set the value related to the column: cd_prg_grupo
	 * @param controleProgramaGrupo the cd_prg_grupo value
	 */
	public void setControleProgramaGrupo (br.com.ksisolucoes.vo.controle.ControleProgramaGrupo controleProgramaGrupo) {
//        br.com.ksisolucoes.vo.controle.ControleProgramaGrupo controleProgramaGrupoOld = this.controleProgramaGrupo;
		this.controleProgramaGrupo = controleProgramaGrupo;
//        this.getPropertyChangeSupport().firePropertyChange ("controleProgramaGrupo", controleProgramaGrupoOld, controleProgramaGrupo);
	}



	/**
	 * Return the value associated with the column: cd_permissao
	 */
	public br.com.ksisolucoes.vo.controle.Permissao getPermissao () {
		return getPropertyValue(this, permissao, PROP_PERMISSAO); 
	}

	/**
	 * Set the value related to the column: cd_permissao
	 * @param permissao the cd_permissao value
	 */
	public void setPermissao (br.com.ksisolucoes.vo.controle.Permissao permissao) {
//        br.com.ksisolucoes.vo.controle.Permissao permissaoOld = this.permissao;
		this.permissao = permissao;
//        this.getPropertyChangeSupport().firePropertyChange ("permissao", permissaoOld, permissao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.controle.ControlePermissaoGrupo)) return false;
		else {
			br.com.ksisolucoes.vo.controle.ControlePermissaoGrupo controlePermissaoGrupo = (br.com.ksisolucoes.vo.controle.ControlePermissaoGrupo) obj;
			if (null == this.getCodigo() || null == controlePermissaoGrupo.getCodigo()) return false;
			else return (this.getCodigo().equals(controlePermissaoGrupo.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}