package br.com.ksisolucoes.vo.vacina.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the registro_vacinas table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="registro_vacinas"
 */

public abstract class BaseRegistroVacina extends BaseRootVO implements Serializable {

	public static String REF = "RegistroVacina";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TIPO_VACINA = "tipoVacina";
	public static final String PROP_LOTE = "lote";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_SITUACAO = "situacao";
	public static final String PROP_VERSION_ALL = "versionAll";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_DATA_APLICACAO = "dataAplicacao";


	// constructors
	public BaseRegistroVacina () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRegistroVacina (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRegistroVacina (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		java.lang.Long versionAll,
		java.lang.Long situacao) {

		this.setCodigo(codigo);
		this.setUsuario(usuario);
		this.setUsuarioCadsus(usuarioCadsus);
		this.setVersionAll(versionAll);
		this.setSituacao(situacao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataAplicacao;
	private java.lang.String observacao;
	private java.lang.String lote;
	private java.lang.Long versionAll;
	private java.lang.Long situacao;

	// many to one
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.vacina.TipoVacina tipoVacina;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_reg_vac"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_aplicacao
	 */
	public java.util.Date getDataAplicacao () {
		return getPropertyValue(this, dataAplicacao, PROP_DATA_APLICACAO); 
	}

	/**
	 * Set the value related to the column: dt_aplicacao
	 * @param dataAplicacao the dt_aplicacao value
	 */
	public void setDataAplicacao (java.util.Date dataAplicacao) {
//        java.util.Date dataAplicacaoOld = this.dataAplicacao;
		this.dataAplicacao = dataAplicacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAplicacao", dataAplicacaoOld, dataAplicacao);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: lote
	 */
	public java.lang.String getLote () {
		return getPropertyValue(this, lote, PROP_LOTE); 
	}

	/**
	 * Set the value related to the column: lote
	 * @param lote the lote value
	 */
	public void setLote (java.lang.String lote) {
//        java.lang.String loteOld = this.lote;
		this.lote = lote;
//        this.getPropertyChangeSupport().firePropertyChange ("lote", loteOld, lote);
	}



	/**
	 * Return the value associated with the column: version_all
	 */
	public java.lang.Long getVersionAll () {
		return getPropertyValue(this, versionAll, PROP_VERSION_ALL); 
	}

	/**
	 * Set the value related to the column: version_all
	 * @param versionAll the version_all value
	 */
	public void setVersionAll (java.lang.Long versionAll) {
//        java.lang.Long versionAllOld = this.versionAll;
		this.versionAll = versionAll;
//        this.getPropertyChangeSupport().firePropertyChange ("versionAll", versionAllOld, versionAll);
	}



	/**
	 * Return the value associated with the column: situacao
	 */
	public java.lang.Long getSituacao () {
		return getPropertyValue(this, situacao, PROP_SITUACAO); 
	}

	/**
	 * Set the value related to the column: situacao
	 * @param situacao the situacao value
	 */
	public void setSituacao (java.lang.Long situacao) {
//        java.lang.Long situacaoOld = this.situacao;
		this.situacao = situacao;
//        this.getPropertyChangeSupport().firePropertyChange ("situacao", situacaoOld, situacao);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadastro
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadastro
	 * @param usuario the cd_usu_cadastro value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_vacina
	 */
	public br.com.ksisolucoes.vo.vacina.TipoVacina getTipoVacina () {
		return getPropertyValue(this, tipoVacina, PROP_TIPO_VACINA); 
	}

	/**
	 * Set the value related to the column: cd_vacina
	 * @param tipoVacina the cd_vacina value
	 */
	public void setTipoVacina (br.com.ksisolucoes.vo.vacina.TipoVacina tipoVacina) {
//        br.com.ksisolucoes.vo.vacina.TipoVacina tipoVacinaOld = this.tipoVacina;
		this.tipoVacina = tipoVacina;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoVacina", tipoVacinaOld, tipoVacina);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vacina.RegistroVacina)) return false;
		else {
			br.com.ksisolucoes.vo.vacina.RegistroVacina registroVacina = (br.com.ksisolucoes.vo.vacina.RegistroVacina) obj;
			if (null == this.getCodigo() || null == registroVacina.getCodigo()) return false;
			else return (this.getCodigo().equals(registroVacina.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}