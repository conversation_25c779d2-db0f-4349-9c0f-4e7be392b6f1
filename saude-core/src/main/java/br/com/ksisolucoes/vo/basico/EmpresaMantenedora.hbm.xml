<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.basico">
    <class name="EmpresaMantenedora" table="empresa_mantenedora">
        <id name="codigo" type="java.lang.String" column="cnpj_mantenedora"
                    length="14">
        </id>
        <version column="version" name="version" type="long" />

        <property name="nomeMantenedora" column="nm_mantenedora"
                          type="java.lang.String" length="60" />

        <property name="logradouro" column="logradouro" type="java.lang.String"
                          length="60" />

        <property name="numero" column="numero" type="java.lang.String"
                          length="5" />

        <property name="bairro" column="bairro" type="java.lang.String"
                          length="60" />

        <property name="cep" column="cep" type="java.lang.String"
                          length="8" />

        <property name="regSaude" column="reg_saude" type="java.lang.String"
                          length="4" />

        <property name="telefone" column="telefone" type="java.lang.String"
                          length="40" />

        <property name="dataAtualizacao" column="dt_atualizacao"
                          type="java.util.Date" />

        <property name="cnes" column="cnes" type="java.lang.String" 
                            length="7"/>

        <many-to-one class="br.com.ksisolucoes.vo.cadsus.Profissional"
                             column="cd_prof_dir_clin" name="diretorClinico" />
                
        <property name="orgaoEmissor" column="cod_orgao_emissor" type="java.lang.String" />

    </class>
</hibernate-mapping>