<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.entradas.estoque.permissao"  >
    <class name="PermissaoGrupoEstoqueItem" table="permissao_grupo_estoque_item" >

        <id name="codigo" 
            column="cd_permissao_grupo_estoque_item" 
            type="java.lang.Long">
            <generator class="assigned"/>
        </id>
        <version column="version" name="version" type="long"/>

        <many-to-one
            class="br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto"
            column="cd_gru"
            name="grupoProduto"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.entradas.estoque.SubGrupo"
            name="subGrupo"
            not-null="true"
        >
            <column name="cd_sub" />
            <column name="cd_gru_sub" />
        </many-to-one>
        
        <many-to-one
            class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
            column="cd_prod"
            name="produto"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Grupo"
            column="cd_grupo"
            name="funcao"
        />
        
        <property
            name="tipoPermissao"
            column="tp_permissao"
            type="java.lang.Long"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.entradas.estoque.permissao.PermissaoGrupoEstoque"
            column="cd_permissao_grupo_estoque"
            name="permissaoGrupoEstoque"
        />
        
    </class>
</hibernate-mapping>
