/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.dao;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

/**
 *
 * <AUTHOR>
 */
public interface TransactionManagerListener {

    void execute(TransactionManager transaction) throws DAOException, ValidacaoException ;

}
