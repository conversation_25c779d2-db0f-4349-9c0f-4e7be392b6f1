/*
 * Created on 24/05/2004
 *
 * To change the template for this generated file go to
 * Window - Preferences - Java - Code Generation - Code and Comments
 */
package br.com.ksisolucoes.dao.interfaces;

import java.io.Serializable;

import org.hibernate.Session;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;


/**
 * <AUTHOR>
 *
 * Interface com metodos para manipulao dos objetos.
 */
public interface ManipulacaoDAO {

    public Serializable saveBatch(Object obj) throws DAOException;
    
    public void constructRootDAOServer() throws DAOException;

    public Class getReferenceClass();

    public Session getSession();

    /**
     * Usado pelas classes base DAO.
     * Persiste um objeto transiente, designando a criao do identificador
     * (Ou usando um valor corrente na propriedade do identificador se um
     * gerador foi usado).
     * 
     * @param obj Objeto a ser persistido.
     */
    public Serializable save(Object obj) throws DAOException;

    /**
     * Usado pelas classes base DAO.
     * Persiste um objeto transiente, designendo a criao do identificador.
     * (Ou usando um valor corrente na propriedade do identificador se um
     * gerador foi usado.
     * 
     * @param obj Objeto a ser persistido.
 	 * @param s Sesso para usar no metodo.
     */
    public Serializable save(Object obj, Session s) throws DAOException;

    /**
     * Usado pelas classes base DAO.
     * Persiste um objeto transiente, designendo a criao do identificador.
     * (Ou usando um valor corrente na propriedade do identificador se um
     * gerador foi usado. 
     * Executa save() ou update(), dependendo do valor do identificador da propriedade.
     * 
     * @param obj Objeto a ser persistido ou atualizado. 
     */
    public void saveOrUpdate(Object obj) throws DAOException;

    /**
     * Usado pelas classes base DAO.
     * Persiste um objeto transiente, designendo a criao do identificador.
     * (Ou usando um valor corrente na propriedade do identificador se um
     * gerador foi usado. Executa save() ou update(), dependendo do valor do
     * identificador da propriedade.
     * 
     * @param obj instancia a ser persistida.
 	 * @param s Sesso para usar no metodo.
     */
    public void saveOrUpdate(Object obj, Session s) throws DAOException;

    /**
     * Usado pelas classes base DAO.
     * Persiste um objeto transiente, designendo a criao do identificador.
     * (Ou usando um valor corrente na propriedade do identificador se um
     * gerador foi usado. 
     * Executa save() ou update(), dependendo do valor do identificador da propriedade.
     * 
     * @param obj Objeto a ser persistido ou atualizado. 
     */
    @Deprecated
    public Object saveOrUpdateCopy(Object obj) throws DAOException;
    
    /**
     * Usado pelas classes base DAO.
     * Persiste um objeto transiente, designendo a criao do identificador.
     * (Ou usando um valor corrente na propriedade do identificador se um
     * gerador foi usado. Executa save() ou update(), dependendo do valor do
     * identificador da propriedade.
     * 
     * @param obj instancia a ser persistida.
 	 * @param s Sesso para usar no metodo.
     */
    @Deprecated
    public Object saveOrUpdateCopy(Object obj, Session s) throws DAOException;
    
    /**
     * Usado pelas classes base DAO.
     * Atualiza o estado persistente associado a um identificador. Uma exceo
     *  lanada se este for uma instncia persistente do identificador na
     * corrente instncia.
     * 
     * @param obj instancia a ser persistida.
     */
    public void update(Object obj) throws DAOException;

    /**
     * Usado pelas classes base DAO.
     * Atualiza o estado persistente associado a um identificador. Uma exceo
     *  lanada se este for uma instncia persistente do identificador na
     * corrente instncia.
     * 
     * @param obj instancia a ser persistida.
 	 * @param s Sesso para usar no metodo.
     */
    public void update(Object obj, Session s) throws DAOException;
    
    /**
     * Usado pelas classes base DAO.
     * Remove um objeto persistente da base de dados. O argumento pode ser uma
     * instncia associada ao recebimento de uma sesso ou uma instancia
     * transiente associada a um identificador existente em um objeto com
     * estado persistente.
     * 
     * @param obj instancia a ser apagada.
     */
    public void delete(Object obj) throws DAOException, ValidacaoException;

    /**
     * Usado pelas classes base DAO.
     * Remove um objeto persistente da base de dados. O argumento pode ser uma
     * instncia associada ao recebimento de uma sesso ou uma instancia
     * transiente associada a um identificador existente em um objeto com
     * estado persistente.
     * 
     * @param obj instancia a ser apagada.
 	 * @param s Sesso para usar no metodo.
     */
    public void delete(Object obj, Session s) throws DAOException, ValidacaoException;
    

}    
