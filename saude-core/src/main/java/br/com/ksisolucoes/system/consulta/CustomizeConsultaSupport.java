/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.system.consulta;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaSupport implements ICustomizeConsultaSupport{

    private List<ICustomizeConsultaQuery> customizeConsultaList = new ArrayList<ICustomizeConsultaQuery>();
    private ICustomizeConsultaQuery defaultCustomizeConsultaQuery;

    public void addCustomizeConsulta(ICustomizeConsultaQuery customizeConsulta){
        this.customizeConsultaList.add(customizeConsulta);
    }

    public List<ICustomizeConsultaQuery> getCustomizeConsultaList() {
        return customizeConsultaList;
    }

    public void setCustomizeConsultaList(List<ICustomizeConsultaQuery> customizeConsultaList) {
        this.customizeConsultaList = customizeConsultaList;
    }

    public ICustomizeConsultaQuery getDefaultCustomizeConsulta() {
        return defaultCustomizeConsultaQuery;
    }

    public void setDefaultCustomizeConsulta(ICustomizeConsultaQuery customizeConsultaQuery) {
        this.defaultCustomizeConsultaQuery = customizeConsultaQuery;
    }

}
