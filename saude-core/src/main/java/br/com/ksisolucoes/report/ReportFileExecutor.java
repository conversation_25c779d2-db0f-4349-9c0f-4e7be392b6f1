/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.jasperreport.LoaderReport;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JRExporter;
import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.JasperPrint;

import java.io.File;
import java.io.IOException;

/**
 *
 * <AUTHOR>
 */
public class ReportFileExecutor {

    private Report report;
    private File file;

    public ReportFileExecutor(Report report) {
        this.report = report;
    }

    public ReportFileExecutor start() throws ReportException {
        JasperPrint jasperPrint = new LoaderReport(report).getJasperPrint();

        if (!jasperPrint.getPages().isEmpty()) {
            try {
                TipoRelatorio tipoRelatorio = TipoRelatorio.byValue(report.getTipoRelatorio().value());
                file = File.createTempFile(jasperPrint.getName(), tipoRelatorio.descricao());
            } catch (IOException e) {
                throw new ReportException(e);
            }


            JRExporter exporter = new net.sf.jasperreports.engine.export.JRPdfExporter();

            exporter.setParameter(JRExporterParameter.OUTPUT_FILE, file);
            exporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);


            try {
                exporter.exportReport();
            } catch (JRException e) {
                throw new ReportException(e);
            }
        }

        return this;
    }

    public FileReport getReport() {
        return new FileReport() {
            @Override
            public File getReport() {
                return file;
            }
        };
    }

}
