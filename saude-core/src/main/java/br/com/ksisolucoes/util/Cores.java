/*
 * Cores.java
 *
 * Created on 13 de Agosto de 2004, 08:56
 */

package br.com.ksisolucoes.util;

import java.awt.Color;

import javax.swing.UIManager;

/**
 *  Classe para armazenar constantes de cores
 * <AUTHOR>
 */
public class Cores {
    
    /**
     * Contante para cor preta;
     */
    public static final Color PRETO = Color.BLACK;
    
    /**
     * Contante para cor AZUL;
     */
    public static final Color AZUL = Color.BLUE;
    
    /**
     * Contante para cor vermelha;
     */
    public static final Color VERMELHO = Color.RED;
    
    /**
     * Contante para cor branca;
     */
    public static final Color BRANCO = Color.WHITE;

    /**
     * Contante para cor cinza;
     */
    public static final Color CINZA = Color.GRAY;
    
    /**
     * Contante para cor de aviso, um amarelo personalizado;
     * <br>Contrutor: <CODE>new Color( 255, 255, 204 )</CODE>
     */
    public static final Color AMARELO_AVISO = new Color( 255, 255, 204 );

    public static final Color CAMPO_OBRIGATORIO = new Color( 255, 255, 230 );

    /**
     * Contante para cor de erro, um vermelho personalizado;
     * <br>Contrutor: <CODE>new Color( 255,102,102 )</CODE>
     */
    public static final Color VERMELHO_ERRO = new Color( 255, 102, 102 );

    /**
     * Contante para cor de validacao ok, um vermelho personalizado;
     * <br>Contrutor: <CODE>new Color(0, 0, 158)</CODE>
     */
    public static final Color AZUL_VALIDACAO = new Color(0, 0, 158);
    
    /**
     * Contante para cor de componentes de texto os quais no esto em modo de edio,
     * mas podem ser editados;
     * <br>Contrutor: <CODE>new Color( 228,226,226 )</CODE>
     */
    public static final Color SEM_EDICAO_BACKGROUND = new Color( 228,226,226 );
    
    /**
     * Contante para cor de componentes de texto os quais no podem ser editados,
     * ou seja, <CODE>setEditable( false )</CODE>;
     * <br>Contrutor: <CODE>new Color( 204, 204, 204 )</CODE>
     */
    public static final Color DESABILITADO_BACKGROUND = new Color( 204, 204, 204 );
    
    /**
     * Contante para cor de componentes de texto em modo de edio no momento;
     * <br>Contrutor: <CODE>Cores.BRANCO</CODE>
     */
    public static final Color EDICAO_BACKGROUND = Cores.BRANCO;
    
    /**
     * Contante para cor de componentes de texto os quais esto em modo de edio;
     * <br>Contrutor: <CODE>new Color( 153, 153, 153 )</CODE>
     */
    public static final Color TITLE_BACKGROUND = new java.awt.Color(153, 153, 153);
    
    /**
     * Contante para cor de componentes de progressbar;
     * <br>Contrutor: <CODE>new Color( 153, 153, 153 )</CODE>
     */
    public static final Color PROGRESS_BAR = new java.awt.Color(51, 51, 255);
    
    /**
     * Cores padrao do componente JTable;
     */
    public static final Color TABLE_SELECTION_BACKGROUND = UIManager.getColor("Table.selectionBackground");
    public static final Color TABLE_SELECTION_FOREGROUND = UIManager.getColor("Table.selectionForeground");
    public static final Color TABLE_BACKGROUND = UIManager.getColor("Table.background");
    public static final Color TABLE_FOREGROUND = UIManager.getColor("Table.foreground");
    /*---------------------------*/

    /**
     *Cores Personalizadas para o componente JTable
     */
    public static final Color TABLE_WARNING = AMARELO_AVISO;

    public static final Color TABLE_CANCEL = new Color(255, 138, 138);

    public static final Color TABLE_APPLY = new Color(100, 167, 218);

    public static final Color TABLE_CHECK = new Color(185, 255, 185);

}
