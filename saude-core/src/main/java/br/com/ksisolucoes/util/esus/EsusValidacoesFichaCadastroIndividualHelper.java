package br.com.ksisolucoes.util.esus;

import br.com.celk.util.Util;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.EmailValidator;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Pais;
import br.com.ksisolucoes.vo.cadsus.Raca;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaUsuarioCadsusEsus;
import br.com.ksisolucoes.vo.esus.dto.EsusValidacoesFichasDTOParam;

import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static br.com.ksisolucoes.util.esus.EsusHelper.*;

/**
 * Created by maicon on 04/09/17.
 */
public class EsusValidacoesFichaCadastroIndividualHelper {

    private EsusValidacoesFichaCadastroIndividualHelper() {}

    public static String executeCommonValidations(EsusValidacoesFichasDTOParam validationParam) throws ValidacaoException {
        StringBuilder stringBuilder = new StringBuilder();

        stringBuilder.append(getInconsistencies(validationParam, validateCareUnit(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateProfessional(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateTeamProfessional(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validatePatient(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateHealthConditions(validationParam)));

        getReturnMessage(validationParam, stringBuilder.toString());

        return stringBuilder.toString();
    }

    public static String validateHealthConditions(EsusValidacoesFichasDTOParam validationParam) throws ValidacaoException {
        StringBuilder stringBuilder = new StringBuilder();
        String patientString = "Paciente: ";

        validateHeartDisease(validationParam, stringBuilder, patientString);
        validateRespiratoryDisease(validationParam, stringBuilder, patientString);
        validateKidneyDisease(validationParam, stringBuilder, patientString);

        getReturnMessage(validationParam, stringBuilder.toString());

        return stringBuilder.toString();
    }

    public static void validateKidneyDisease(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder, String pacienteString) {
        validateItem(
                RepositoryComponentDefault.SIM_LONG.equals(validationParam.getEsusFichaUsuarioCadsusEsus().getDoencaRins())
                    && validationParam.getEsusFichaUsuarioCadsusEsus().getRinsInsuficiencia() == null
                    && validationParam.getEsusFichaUsuarioCadsusEsus().getRinsNaoSabe() == null
                    && validationParam.getEsusFichaUsuarioCadsusEsus().getRinsOutros() == null,
                stringBuilder,
                pacienteString + validationParam.getUsuarioCadsus().getNome() + ", sinalizado com Doença Renal. Deve ser informado qual doença."
        );
    }

    public static void validateRespiratoryDisease(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder, String pacienteString) {
        validateItem(
                RepositoryComponentDefault.SIM_LONG.equals(validationParam.getEsusFichaUsuarioCadsusEsus().getDoencaRespiratoria())
                    && validationParam.getEsusFichaUsuarioCadsusEsus().getRespiratoriaAsma() == null
                    && validationParam.getEsusFichaUsuarioCadsusEsus().getRespiratoriaEfisema() == null
                    && validationParam.getEsusFichaUsuarioCadsusEsus().getRespiratoriaNaoSabe() == null
                    && validationParam.getEsusFichaUsuarioCadsusEsus().getRespiratoriaOutros() == null,
                stringBuilder,
                pacienteString + validationParam.getUsuarioCadsus().getNome() + ", sinalizado com Doença Respiratória. Deve ser informado qual doença."
        );
    }

    public static void validateHeartDisease(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder, String pacienteString) {
        validateItem(
                RepositoryComponentDefault.SIM_LONG.equals(validationParam.getEsusFichaUsuarioCadsusEsus().getDoencaCardiaca())
                        && validationParam.getEsusFichaUsuarioCadsusEsus().getCardiacaInsuficiencia() == null
                        && validationParam.getEsusFichaUsuarioCadsusEsus().getCardiacaNaoSabe() == null
                        && validationParam.getEsusFichaUsuarioCadsusEsus().getCardiacaOutros() == null,
                stringBuilder,
                pacienteString + validationParam.getUsuarioCadsus().getNome() + ", sinalizado com Doença Cardiaca. Deve ser informado qual doença."
        );
    }

    public static String validateProfessional(EsusValidacoesFichasDTOParam validationParam) throws ValidacaoException {
        StringBuilder stringBuilder = new StringBuilder();
        if (validationParam.getProfissional() == null) {
            appendInconsitency(stringBuilder,"Profissional não informado");
        } else {
            validateItem(validationParam.getProfissional().getCodigoCns() == null, stringBuilder, "Profissional " + validationParam.getProfissional().getNome() + " sem CNS definido.");
        }

        getReturnMessage(validationParam, stringBuilder.toString());

        return stringBuilder.toString();
    }

    public static String validatePatient(EsusValidacoesFichasDTOParam validationParam) throws ValidacaoException {
        //Validações replicadas no metodo validarUsuarioCadsusEsus(), para serem validadas na tela ao cadastrar.
        StringBuilder stringBuilder = new StringBuilder();

        validateItem(!EsusHelper.isNameValid(validationParam.getUsuarioCadsus().getNome()), stringBuilder, "Nome do paciente não está em conformidade com os padrões do eSUS, paciente: " + validationParam.getUsuarioCadsus().getNome());
        validateItem(validationParam.getEsusFichaUsuarioCadsusEsus().getUsuarioCadsusEsus() == null, stringBuilder, "Sem domicílio informado, paciente: " + validationParam.getUsuarioCadsus().getNome());
        validateEthnicGroup(validationParam, stringBuilder);
        validateBirthdatePatient(validationParam, stringBuilder, validationParam.getEsusFichaUsuarioCadsusEsus().getDataObito());
        validateExclusionReason(validationParam, stringBuilder, validationParam.getEsusFichaUsuarioCadsusEsus().getDataObito(), validationParam.getEsusFichaUsuarioCadsusEsus().getNumeroDo());
        validatePatientCitizenship(validationParam, stringBuilder);
        validateNamesFromFicha(validationParam, stringBuilder);
        validateItem(validationParam.getUsuarioCadsus().getEmail() != null && !EmailValidator.validarEmail(validationParam.getUsuarioCadsus().getEmail()), stringBuilder, "E-Mail incompleto ou inválido, paciente: " + validationParam.getUsuarioCadsus().getNome());
        validateGender(validationParam, stringBuilder);

        getReturnMessage(validationParam, stringBuilder.toString());

        return stringBuilder.toString();
    }

    public static void validateGender(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
       validateGenderEsus(validationParam.getEsusFichaUsuarioCadsusEsus().getUsuarioCadsusEsus(), validationParam.getEsusFichaUsuarioCadsusEsus().getUsuarioCadsusEsus(), stringBuilder, validationParam.getUsuarioCadsus().getNome());
    }

    public static void validateNamesFromFicha(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        validateItem(!RepositoryComponentDefault.SIM_LONG.equals(validationParam.getEsusFichaUsuarioCadsusEsus().getMaeDesconhecido()) && !EsusHelper.isNameValid(validationParam.getUsuarioCadsus().getNomeMae()), stringBuilder, "Nome da mãe incompleto ou inválido, paciente: " + validationParam.getUsuarioCadsus().getNome());
        validateItem(!RepositoryComponentDefault.SIM_LONG.equals(validationParam.getEsusFichaUsuarioCadsusEsus().getPaiDesconhecido()) && !EsusHelper.isNameValid(validationParam.getUsuarioCadsus().getNomePai()), stringBuilder, "Nome do pai incompleto ou inválido, paciente: " + validationParam.getUsuarioCadsus().getNome());
        validateItem(validationParam.getUsuarioCadsus().getApelido() != null &&!EsusHelper.isNomeSocialValido(validationParam.getUsuarioCadsus().getApelido()), stringBuilder, "Nome Social incompleto ou inválido, paciente: " + validationParam.getUsuarioCadsus().getNome());
    }

    public static void validateEthnicGroup(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        if (validationParam.getUsuarioCadsus().getRaca() == null) {
            appendInconsitency(stringBuilder,"Sem raça informada, paciente: " + validationParam.getUsuarioCadsus().getNome());
        } else if (Raca.TipoRaca.INDIGENA.value().equals(validationParam.getUsuarioCadsus().getRaca().getCodigo()) && validationParam.getUsuarioCadsus().getEtniaIndigena() == null) {
            appendInconsitency(stringBuilder,"Etnia não informada, paciente: " + validationParam.getUsuarioCadsus().getNome());
        }
    }

    public static String validateTeamProfessional(EsusValidacoesFichasDTOParam validationParam) throws ValidacaoException {
        StringBuilder stringBuilder = new StringBuilder();
        EsusFichaUsuarioCadsusEsus esusFichaUsuarioCadsusEsus = validationParam.getEsusFichaUsuarioCadsusEsus();

        if (validationParam.isValidarDomicilio()) {
            if (esusFichaUsuarioCadsusEsus.getUsuarioCadsusEsus().getUsuarioCadsus().getEnderecoDomicilio() == null) {
                appendInconsitency(stringBuilder,"Usuário / Cidadão " + esusFichaUsuarioCadsusEsus.getUsuarioCadsusEsus().getUsuarioCadsus().getDescricaoFormatado() + " sem Domicílio cadastrado.");
            } else if (esusFichaUsuarioCadsusEsus.getEquipeMicroArea() == null) {
                appendInconsitency(stringBuilder,"Domicílio " + esusFichaUsuarioCadsusEsus.getUsuarioCadsusEsus().getUsuarioCadsus().getEnderecoDomicilio().getCodigo() + " sem microárea definida.");
            } else if (esusFichaUsuarioCadsusEsus.getEquipeMicroArea().getEquipeProfissional() == null) {
                appendInconsitency(stringBuilder,"Área " + esusFichaUsuarioCadsusEsus.getEquipeMicroArea().getEquipeArea().getDescricao() + " Microárea " + esusFichaUsuarioCadsusEsus.getEquipeMicroArea().getMicroArea() + " sem equipe profissional definida.");
            }
        }

        getReturnMessage(validationParam, stringBuilder.toString());

        return stringBuilder.toString();
    }

    public static String validateBirthCityIbgeId(EsusValidacoesFichasDTOParam validationParam, int maxLengthBirthCityId) throws ValidacaoException {
        StringBuilder stringBuilder = new StringBuilder();

        validateItem(
                UsuarioCadsus.Nacionalidade.BRASILEIRO.value().equals(validationParam.getUsuarioCadsus().getNacionalidade())
                        && ((Util.isNull(validationParam.getUsuarioCadsus().getCidadeNascimento()) || Util.isNull(validationParam.getUsuarioCadsus().getCidadeNascimento().getCodigo()))
                        || validationParam.getUsuarioCadsus().getCidadeNascimento().getCodigo().toString().length() != maxLengthBirthCityId),
                stringBuilder,
                Bundle.getStringApplication("msg_paciente_cidade_endereco_codigo_IBGE_valido", validationParam.getUsuarioCadsus().getCodigo(), validationParam.getUsuarioCadsus().getDescricaoFormatado())
        );

        getReturnMessage(validationParam, stringBuilder.toString());

        return stringBuilder.toString();
    }

    public static String validateCpfCnsPatient(EsusValidacoesFichasDTOParam validationParam) throws ValidacaoException, DAOException {
        StringBuilder stringBuilder = new StringBuilder();
        UsuarioCadsus usuarioCadsus = validationParam.getUsuarioCadsus();

        validateItem((Util.isNull(usuarioCadsus.getCpf()) || usuarioCadsus.getCpf().isEmpty()) && Util.isNull(UsuarioCadsusHelper.carregarNumeroCartao(usuarioCadsus)), stringBuilder, Bundle.getStringApplication("msg_paciente_sem_cpf_cns", validationParam.getUsuarioCadsus().getCodigo(), validationParam.getUsuarioCadsus().getDescricaoFormatado()));

        getReturnMessage(validationParam, stringBuilder.toString());

        return stringBuilder.toString();
    }

    public static String validatePatientCitizenship(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) throws ValidacaoException {
        validateItem(Util.isNull(validationParam.getUsuarioCadsus().getNacionalidade()), stringBuilder,"Sem nacionalidade informada, paciente: " + validationParam.getUsuarioCadsus().getNome());
        validateItem(UsuarioCadsus.Nacionalidade.BRASILEIRO.value().equals(validationParam.getUsuarioCadsus().getNacionalidade()) && validationParam.getUsuarioCadsus().getCidadeNascimento() == null, stringBuilder, "Cidade de Nascimento não informada, paciente: " + validationParam.getUsuarioCadsus().getNome());
        validateItem(
                UsuarioCadsus.Nacionalidade.ESTRANGEIRO.value().equals(validationParam.getUsuarioCadsus().getNacionalidade()) && Pais.BRASIL.getCodigo().equals(validationParam.getUsuarioCadsus().getPaisNascimento().getCodigo()),
                stringBuilder,
                "Paciente com nacionalidade estrangeira e país de nascimento Brasil, paciente: " + validationParam.getUsuarioCadsus().getNome()
        );

        validatePatientNaturalization(validationParam, stringBuilder);

        validateForeignPatient(validationParam, stringBuilder);

        getReturnMessage(validationParam, stringBuilder.toString());

        return stringBuilder.toString();
    }

    public static void validateForeignPatient(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) throws ValidacaoException {
        if (UsuarioCadsus.Nacionalidade.ESTRANGEIRO.value().equals(validationParam.getUsuarioCadsus().getNacionalidade())) {
            if (validationParam.getUsuarioCadsus().getPaisNascimento() == null) {
                appendInconsitency(stringBuilder, "Usuário / Cidadão " + validationParam.getUsuarioCadsus().getDescricaoFormatado() + " é de nacionalidade estrangeira e não possui país de nascimento definido.");
            } else if (validationParam.getUsuarioCadsus().getPaisNascimento().getCodigoEsus() == null) {
                appendInconsitency(stringBuilder, "PAÍS: " + validationParam.getUsuarioCadsus().getPaisNascimento().getDescricao() + ", sem código e-SUS definido.");
            }

            validateItem(validationParam.getEsusFichaUsuarioCadsusEsus() != null && validationParam.getEsusFichaUsuarioCadsusEsus().getDtEntradaBrasil() == null, stringBuilder, "Data de entrada no Brasil não informada, paciente: " + validationParam.getUsuarioCadsus().getNome());

            getReturnMessage(validationParam, stringBuilder.toString());
        }
    }

    public static void validatePatientNaturalization(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) throws ValidacaoException {
        if (UsuarioCadsus.Nacionalidade.NATURALIZADO.value().equals(validationParam.getUsuarioCadsus().getNacionalidade())) {
            validateItem(validationParam.getEsusFichaUsuarioCadsusEsus() != null && validationParam.getEsusFichaUsuarioCadsusEsus().getDataNaturalizado() == null, stringBuilder, "Data de naturalização não informada, paciente: " + validationParam.getUsuarioCadsus().getNome());

            if (validationParam.getEsusFichaUsuarioCadsusEsus() != null && validationParam.getEsusFichaUsuarioCadsusEsus().getPortariaNaturalizacao() == null) {
                appendInconsitency(stringBuilder, "Portaria de naturalização não informada, paciente: " + validationParam.getUsuarioCadsus().getNome());
            } else if (validationParam.getEsusFichaUsuarioCadsusEsus() != null && !isNationalizationDecreeValid(validationParam.getEsusFichaUsuarioCadsusEsus().getPortariaNaturalizacao())) {
                appendInconsitency(stringBuilder, "Portaria de naturalização inválida, paciente: " + validationParam.getUsuarioCadsus().getNome());
            }

            getReturnMessage(validationParam, stringBuilder.toString());
        }
    }

    public static boolean isNationalizationDecreeValid(String str) {
        if (Util.isNull(str)) return false;

        Pattern p = Pattern.compile("[a-zA-Z0-9]+");
        Matcher m = p.matcher(str);
        return m.matches();
    }

    public static String validateUsuarioCadsusEsus(EsusValidacoesFichasDTOParam validationParam) throws ValidacaoException {
        StringBuilder stringBuilder = new StringBuilder();

        validateItem(!EsusHelper.isNameValid(validationParam.getUsuarioCadsus().getNome()), stringBuilder,"Nome do paciente não está em conformidade com os padrões do eSUS, paciente: " + validationParam.getUsuarioCadsus().getNome());
        validateEthnicGroup(validationParam, stringBuilder);
        validateBirthdatePatient(validationParam, stringBuilder, validationParam.getUsuarioCadsusEsus().getDataObito());
        validateExclusionReason(validationParam, stringBuilder, validationParam.getUsuarioCadsusEsus().getDataObito(), validationParam.getUsuarioCadsusEsus().getNumeroDo());
        validateNames(validationParam, stringBuilder);
        validateItem(validationParam.getUsuarioCadsus().getEmail() != null && !EmailValidator.validarEmail(validationParam.getUsuarioCadsus().getEmail()), stringBuilder,"E-Mail incompleto ou inválido, paciente: " + validationParam.getUsuarioCadsus().getNome());
        validateGenderCad(validationParam, stringBuilder);

        getReturnMessage(validationParam, stringBuilder.toString());

        return stringBuilder.toString();
    }

    public static void validateGenderCad(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        validateGenderEsus(validationParam.getUsuarioCadsusEsus().getInformaIdentidadeGenero(), validationParam.getUsuarioCadsusEsus().getInformaOrientacaoSexual(), stringBuilder, validationParam.getUsuarioCadsus().getNome());
    }

    public static void validateGenderEsus(Object informaIdentidadeGenero, Object informaOrientacaoSexual, StringBuilder stringBuilder, String nome) {
        validateItem(informaIdentidadeGenero == null, stringBuilder, "Informar ou não a Orientação Sexual: " + nome);
        validateItem(informaOrientacaoSexual == null, stringBuilder, "Informar ou não a Identidade de Gênero: " + nome);
    }

    public static void validateNames(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) throws ValidacaoException {
        validateItem(!RepositoryComponentDefault.SIM_LONG.equals(validationParam.getUsuarioCadsusEsus().getMaeDesconhecido()) && !EsusHelper.isNameValid(validationParam.getUsuarioCadsus().getNomeMae()), stringBuilder,"Nome da mãe incompleto ou inválido, paciente: " + validationParam.getUsuarioCadsus().getNome());
        validateItem(!RepositoryComponentDefault.SIM_LONG.equals(validationParam.getUsuarioCadsusEsus().getPaiDesconhecido()) && !EsusHelper.isNameValid(validationParam.getUsuarioCadsus().getNomePai()), stringBuilder,"Nome do pai incompleto ou inválido, paciente: " + validationParam.getUsuarioCadsus().getNome());
        validateItem(validationParam.getUsuarioCadsus().getApelido() != null && !EsusHelper.isNomeSocialValido(validationParam.getUsuarioCadsus().getApelido()), stringBuilder,"Nome Social incompleto ou inválido, paciente: " + validationParam.getUsuarioCadsus().getNome());

        getReturnMessage(validationParam, stringBuilder.toString());
    }

    public static void validateExclusionReason(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder, Date dataObito, String numeroDo) throws ValidacaoException {
        if (UsuarioCadsus.MotivoExclusao.OBITO.value().equals(validationParam.getUsuarioCadsus().getMotivoExclusao())) {
            validateItem(dataObito == null, stringBuilder, "Motivo de exclusão do paciente como óbito e data de óbito não preenchido. Paciente: " + validationParam.getUsuarioCadsus().getNome());
            validateItem(
                    numeroDo != null && numeroDo.trim().length() != UsuarioCadsusHelper.TAMANHO_OBRIGATORIO_NUMERO_DO,
                    stringBuilder,
                    "Motivo de exclusão do paciente como óbito e número DO preenchido com quantidade de caracteres diferente de " + UsuarioCadsusHelper.TAMANHO_OBRIGATORIO_NUMERO_DO + ", paciente: " + validationParam.getUsuarioCadsus().getNome()
            );
        }

        getReturnMessage(validationParam, stringBuilder.toString());
    }

    public static void validateBirthdatePatient(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder, Date dataObito) throws ValidacaoException {
        if (validationParam.getUsuarioCadsus().getDataNascimento() == null) {
            appendInconsitency(stringBuilder, "Sem data de nascimento informada, paciente: " + validationParam.getUsuarioCadsus().getNome());
        } else if (dataObito != null && validationParam.getUsuarioCadsus().getDataNascimento().after(dataObito)) {
            appendInconsitency(stringBuilder, "A data de óbito é menor que a data de nascimento, paciente: " + validationParam.getUsuarioCadsus().getNome());
        }

        getReturnMessage(validationParam, stringBuilder.toString());
    }

    public static String getUnidadeCentralInconsistencies(Empresa unidadeCentral) {
        StringBuilder stringBuilder = new StringBuilder();

        validateItem(Util.isNull(unidadeCentral), stringBuilder, "Parametro UnidadeCentralIntegracaoEsus não definido.");

        if (Util.isNotNull(unidadeCentral)){
            validateItem(Util.isNull(unidadeCentral.getCnpj()), stringBuilder, "CNPJ da Unidade Central não definido.");
        }

        return !stringBuilder.toString().isEmpty() ? stringBuilder.toString() : null;
    }

    public static String validateDiseases(EsusValidacoesFichasDTOParam validationParam) throws ValidacaoException {
        StringBuilder stringBuilder = new StringBuilder();

        EsusFichaUsuarioCadsusEsus esusFichaUsuarioCadsusEsus = validationParam.getEsusFichaUsuarioCadsusEsus();

        validateItem(EsusHelper.isEqualsSimLong(esusFichaUsuarioCadsusEsus.getDoencaCardiaca())
                        && !EsusHelper.isEqualsSimLong(esusFichaUsuarioCadsusEsus.getCardiacaInsuficiencia())
                        && !EsusHelper.isEqualsSimLong(esusFichaUsuarioCadsusEsus.getCardiacaNaoSabe())
                        && !EsusHelper.isEqualsSimLong(esusFichaUsuarioCadsusEsus.getCardiacaOutros()),
                stringBuilder,
                "Pelo menos uma doença cardíaca deve ser informada quando campo Tem Doença Cardíaca ou do Coração for Sim.");
        validateItem(EsusHelper.isEqualsSimLong(esusFichaUsuarioCadsusEsus.getDoencaRespiratoria())
                        && !EsusHelper.isEqualsSimLong(esusFichaUsuarioCadsusEsus.getRespiratoriaAsma())
                        && !EsusHelper.isEqualsSimLong(esusFichaUsuarioCadsusEsus.getRespiratoriaEfisema())
                        && !EsusHelper.isEqualsSimLong(esusFichaUsuarioCadsusEsus.getRespiratoriaNaoSabe())
                        && !EsusHelper.isEqualsSimLong(esusFichaUsuarioCadsusEsus.getRespiratoriaOutros()),
                stringBuilder,
                "Pelo menos uma doença respiratória deve ser informada quando campo Tem Doença Respiratória ou no Pulmão for Sim.");
        validateItem(EsusHelper.isEqualsSimLong(esusFichaUsuarioCadsusEsus.getDoencaRins())
                        && !EsusHelper.isEqualsSimLong(esusFichaUsuarioCadsusEsus.getRinsInsuficiencia())
                        && !EsusHelper.isEqualsSimLong(esusFichaUsuarioCadsusEsus.getRinsNaoSabe())
                        && !EsusHelper.isEqualsSimLong(esusFichaUsuarioCadsusEsus.getRinsOutros()),
                stringBuilder,
                "Pelo menos uma doença renal deve ser informada quando campo Tem ou Teve Problemas nos Rins for Sim.");

        getReturnMessage(validationParam, stringBuilder.toString());

        return stringBuilder.toString();
    }

    public static String validatePatientPhone(EsusValidacoesFichasDTOParam validationParam) throws ValidacaoException {
        final int PHONE_MIN_LENGTH = 10;
        final int PHONE_MAX_LENGTH = 11;

        StringBuilder stringBuilder = new StringBuilder();

        UsuarioCadsus usuarioCadsus = validationParam.getUsuarioCadsus();
        String patientPhone = UsuarioCadsusHelper.getPatientPhone(usuarioCadsus);

        validateItem(!EsusHelper.isOnlyNumbersOrNull(patientPhone), stringBuilder, "Telefone deve conter somente números.");

        validateItem((UsuarioCadsus.Nacionalidade.BRASILEIRO.value().equals(usuarioCadsus.getNacionalidade()) || UsuarioCadsus.Nacionalidade.NATURALIZADO.value().equals(usuarioCadsus.getNacionalidade()))
                        && (Util.isNull(patientPhone) || (patientPhone.length() < PHONE_MIN_LENGTH || patientPhone.length() > PHONE_MAX_LENGTH)),
                stringBuilder,
                "Telefone é obrigatório para brasileiros e naturalizados."
        );
        
        getReturnMessage(validationParam, stringBuilder.toString());

        return stringBuilder.toString();
    }
}