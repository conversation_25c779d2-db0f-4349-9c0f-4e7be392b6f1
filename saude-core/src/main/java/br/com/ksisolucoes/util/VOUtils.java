/*
 * Created on 07/07/2004
 *
 */
package br.com.ksisolucoes.util;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.clone.DefinerPropertiesCloning;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.interfaces.HashTransfer;
import org.apache.commons.beanutils.BeanUtils;

import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * <AUTHOR>
 *
 */
public class VOUtils implements Serializable{

    private VOUtils(){
    }

    public static <T> T cloneObject(T bean){
        if(bean != null){
            return (T) new DefinerPropertiesCloning().define(bean);
        }
        return null;
    }

    public static void persistirListaVosModificados(Class clazz, Collection list, BuilderQueryCustom.QueryParameter... parametersUniqueKey) throws DAOException, ValidacaoException {
        LoadManager loadManager = LoadManager.getInstance(clazz);
        if(parametersUniqueKey != null){
            for (BuilderQueryCustom.QueryParameter queryParameter : parametersUniqueKey) {
                loadManager.addParameter(queryParameter);
            }
        }
        List cadastrados = loadManager.start().getList();

        cadastrados.removeAll(list);

        for (Object object : cadastrados) {
            BOFactory.delete(object);
        }

        //Inserido um flush devido a um issue com o hibernate, que executa comandos INSERT antes dos comandos DELETE, gerando uma violação de índice único em algumas situações.
        //Informações sobre o problema: https://forum.hibernate.org/viewtopic.php?f=1&t=934483

        HibernateSessionFactory.getSession().flush();

        for (Object object : list) {
            BOFactory.save(object);
        }

    }

    public static void removerListaVos(Class clazz, BuilderQueryCustom.QueryParameter... parametersUniqueKey) throws DAOException, ValidacaoException {
        LoadManager loadManager = LoadManager.getInstance(clazz);
        if(parametersUniqueKey != null){
            for (BuilderQueryCustom.QueryParameter queryParameter : parametersUniqueKey) {
                loadManager.addParameter(queryParameter);
            }
        }
        List cadastrados = loadManager.start().getList();

        for (Object object : cadastrados) {
            BOFactory.delete(object);
        }

        //Inserido um flush devido a um issue com o hibernate, que executa comandos INSERT antes dos comandos DELETE, gerando uma violação de índice único em algumas situações.
        //Informações sobre o problema: https://forum.hibernate.org/viewtopic.php?f=1&t=934483
        HibernateSessionFactory.getSession().flush();

    }
    
    public static java.util.Map getMap(java.util.List param, Object vo) {
        return getMap(param, null, vo);
    }
    
    /**
     * Metodo responsvel pela retorno de infromaes para
     *  implementao da consulta ou outros.
     *
     * @param param lista de atributos a retornar.
     * @return map
     *
     *
     * Favor no mexer neste mtodo sem antes falar com o Gustavo, obrigado.
     *
     **/
    public static java.util.Map getMap(java.util.List param, String pacote, Object vo) {
        
        if(pacote == null){
            pacote = "";
        }else{
            pacote = pacote+".";
        }
        
        java.util.Map map = new java.util.HashMap();
        java.util.List list = null;
        java.util.TreeSet listOrder = new java.util.TreeSet(param);
        String nivelAtualTemp = null;
        
        for(java.util.Iterator i = listOrder.iterator(); i.hasNext();) {
            String valor = (String)i.next();
            String nivelAtual = null;
            String proximoNivel = null;
            int posicao = valor.indexOf('.');
            
            if( posicao > 0) {
                nivelAtual = valor.substring(0, posicao);
                
                if(nivelAtual.equals(nivelAtualTemp)){
                    proximoNivel = valor.substring(posicao + 1);
                    list.add(proximoNivel);
                }else{
                    // Insere o map de outro VO, antes de construir um novo
                    HashTransfer hashTransfer = null;
                    java.util.Map m = null;
                    if (list != null){
                        try{
                            hashTransfer = (HashTransfer)getValorAtributo( nivelAtualTemp, vo );
                            m = (java.util.Map)hashTransfer.getMap( list, pacote + nivelAtualTemp );
                            map.putAll(m);
                        } catch ( Exception e ){
                            //Mesmo ocorrendo algum erro, como NullPointerException, a
                            //consulta deve consitunar.
                        }
                    }
                    
                    list  = new java.util.ArrayList();
                    nivelAtualTemp = nivelAtual;
                    proximoNivel = valor.substring(posicao + 1);
                    list.add(proximoNivel);
                }
            } else {
                map.put(pacote + valor, getValorAtributo( valor, vo ));
            }
        }
        
        // Insere o map de outro VO, quando este  o ultimo da lista
        if (list!=null && list.size()!=0){
            HashTransfer hashTransfer = (HashTransfer)getValorAtributo( nivelAtualTemp, vo );
            //Verifica se o objeto no  nulo, pois estava dando nullpointer em alguns casos
            if ( hashTransfer != null ){
                java.util.Map m = (java.util.Map)hashTransfer.getMap( list, pacote + nivelAtualTemp );
                map.putAll(m);
            }
        }
        
        return map;
    }
    
    
    public static Object getValorAtributo(String atributo, Object vo) {
        String nomeMetodo = "get" + org.apache.commons.lang.StringUtils.capitalize(atributo);
        java.lang.reflect.Method metodo = null;
        Object obj = null;
        try {
            metodo = vo.getClass().getMethod(nomeMetodo);
            
            obj = metodo.invoke( vo );
            
        } catch(Exception ex) {
            Loggable.log.error(ex);
        }
        return obj;
    }
    
    public static String montarPath(String... args) {
        String path = "";
        for ( String atr : args ){
            if (atr != null && !atr.trim().equals("")) {
                path += atr.trim() + ".";
            }
        }
        
        return path.substring(0, (path.length()-1));
    }
    
    public static String[] mergeProperties(String[]... properties){
        List<String> props = new ArrayList<String>();
        for (String[] property : properties) {
            props.addAll(Arrays.asList(property));
        }
        
        return props.toArray(new String[props.size()]);
    }

    public static <T, P> Map<T,P> getMapValues(List<T> props, List<P> values){
        Map<T, P> map = new LinkedHashMap<T, P>();

        for (int i = 0; i < props.size(); i++) {
            T prop = props.get( i );
            P value = values.get( i );

            map.put(prop, value);
        }

        return map;
    }

    public static void copyProperties(Object dest, Object orig) {
        try {
            BeanUtils.copyProperties(dest, orig);
        } catch (IllegalAccessException ex) {
            Logger.getLogger(VOUtils.class.getName()).log(Level.SEVERE, null, ex);
        } catch (InvocationTargetException ex) {
            Logger.getLogger(VOUtils.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
}
