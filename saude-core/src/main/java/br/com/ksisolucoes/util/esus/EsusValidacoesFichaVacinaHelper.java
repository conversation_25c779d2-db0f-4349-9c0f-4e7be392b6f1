package br.com.ksisolucoes.util.esus;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.esus.CboFichaEsus;
import br.com.ksisolucoes.vo.esus.CboFichaEsusItem;
import br.com.ksisolucoes.vo.esus.dto.EsusValidacoesFichasDTOParam;
import br.com.ksisolucoes.vo.vacina.Calendario;
import br.com.ksisolucoes.vo.vacina.TipoVacina;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;
import br.com.ksisolucoes.vo.vacina.VacinaCalendario;
import ch.lambdaj.Lambda;

import java.util.*;

import static br.com.ksisolucoes.util.esus.EsusHelper.*;
import static br.com.ksisolucoes.vo.vacina.VacinaCalendario.Doses.DOSE;
import static ch.lambdaj.Lambda.having;
import static ch.lambdaj.Lambda.on;
import static org.hamcrest.Matchers.equalTo;

/**
 * Created by izael on 19/11/19.
 */
public class EsusValidacoesFichaVacinaHelper {

    private EsusValidacoesFichaVacinaHelper() {
    }


    private static final List<Long> DOSES_1_2 = Arrays.asList(
            VacinaCalendario.Doses.DOSE_1.value(),
            VacinaCalendario.Doses.DOSE_2.value()
    );

    private static final List<Long> DOSES_1_2_REFORCO = Arrays.asList(
            VacinaCalendario.Doses.DOSE_1.value(),
            VacinaCalendario.Doses.DOSE_2.value(),
            VacinaCalendario.Doses.REFORCO.value(),
            VacinaCalendario.Doses.DOSE_ADICIONAL.value()
    );

    private static final List<Long> DOSE_1_REFORCO = Arrays.asList(
            DOSE.value(),
            VacinaCalendario.Doses.REFORCO.value(),
            VacinaCalendario.Doses.DOSE_ADICIONAL.value()
    );

    private static final List<Long> DOSE_1_2_REF_R2_DA = Arrays.asList(
            VacinaCalendario.Doses.DOSE_1.value(),
            VacinaCalendario.Doses.DOSE_2.value(),
            VacinaCalendario.Doses.REFORCO.value(),
            VacinaCalendario.Doses.DOSE_2_REFORCO.value(),
            VacinaCalendario.Doses.DOSE_ADICIONAL.value()
    );

    private static final List<Long> DOSE_1_REF_R2_DA = Arrays.asList(
            VacinaCalendario.Doses.DOSE_1.value(),
            VacinaCalendario.Doses.REFORCO.value(),
            VacinaCalendario.Doses.DOSE_2_REFORCO.value(),
            VacinaCalendario.Doses.DOSE_ADICIONAL.value()
    );

    private static final List<Long> DOSES_1_2_3 = Arrays.asList(
            VacinaCalendario.Doses.DOSE_1.value(),
            VacinaCalendario.Doses.DOSE_2.value(),
            VacinaCalendario.Doses.DOSE_3.value()
    );

    private static final List<Long> DOSES_1_2_U = Arrays.asList(
            VacinaCalendario.Doses.DOSE_1.value(),
            VacinaCalendario.Doses.DOSE_2.value(),
            VacinaCalendario.Doses.DOSE_UNICA.value()
    );

    private static final List<Long> DOSES_1_2_3_R1_R2 = Arrays.asList(
            VacinaCalendario.Doses.DOSE_1.value(),
            VacinaCalendario.Doses.DOSE_2.value(),
            VacinaCalendario.Doses.DOSE_3.value(),
            VacinaCalendario.Doses.DOSE_1_REFORCO.value(),
            VacinaCalendario.Doses.DOSE_2_REFORCO.value()
    );

    private static final List<Long> DOSES_1_2_3_R_U = Arrays.asList(
            VacinaCalendario.Doses.DOSE_1.value(),
            VacinaCalendario.Doses.DOSE_2.value(),
            VacinaCalendario.Doses.DOSE_3.value(),
            VacinaCalendario.Doses.REFORCO.value(),
            VacinaCalendario.Doses.DOSE_UNICA.value()
    );

    private static final List<Long> DOSES_1_2_REF_R2 = Arrays.asList(
            VacinaCalendario.Doses.DOSE_1.value(),
            VacinaCalendario.Doses.DOSE_2.value(),
            VacinaCalendario.Doses.REFORCO.value(),
            VacinaCalendario.Doses.DOSE_2_REFORCO.value()
    );

    private static final List<Long> DOSES_1_2_3_4_5_REV = Arrays.asList(
            VacinaCalendario.Doses.DOSE_1.value(),
            VacinaCalendario.Doses.DOSE_2.value(),
            VacinaCalendario.Doses.DOSE_3.value(),
            VacinaCalendario.Doses.DOSE_4.value(),
            VacinaCalendario.Doses.DOSE_5.value(),
            VacinaCalendario.Doses.REVACINACAO.value()
    );

    private static final List<Long> TRATAMENTO_COM_UMA_A_CINCO_DOSES = Arrays.asList(
            VacinaCalendario.Doses.TRATAMENTO_COM_UMA_DOSE.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_DUAS_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_TRES_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_QUATRO_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_CINCO_DOSES.value()
    );

    private static final List<Long> TRATAMENTO_COM_UMA_A_VINTE_DOSES = Arrays.asList(
            VacinaCalendario.Doses.TRATAMENTO_COM_UMA_DOSE.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_DUAS_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_TRES_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_QUATRO_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_CINCO_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_SEIS_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_SETE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_OITO_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_NOVE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_DEZ_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_ONZE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_DOZE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_TREZE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_QUATORZE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_QUINZE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_DEZESSEIS_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_DEZESSETE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_DEZOITO_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_DEZENOVE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_VINTE_DOSES.value()
    );

    private static final List<Long> TRATAMENTO_COM_UMA_A_VINTE_E_QUATRO_DOSES = Arrays.asList(
            VacinaCalendario.Doses.TRATAMENTO_COM_UMA_DOSE.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_DUAS_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_TRES_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_QUATRO_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_CINCO_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_SEIS_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_SETE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_OITO_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_NOVE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_DEZ_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_ONZE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_DOZE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_TREZE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_QUATORZE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_QUINZE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_DEZESSEIS_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_DEZESSETE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_DEZOITO_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_DEZENOVE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_VINTE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_VINTE_E_QUATRO_DOSES.value()
    );

    public static final List<Long> TRATAMENTO_DOSE_UNICA_MAIS_20_DOSES = Arrays.asList(
            VacinaCalendario.Doses.DOSE_UNICA.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_UMA_DOSE.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_DUAS_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_TRES_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_QUATRO_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_CINCO_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_SEIS_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_SETE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_OITO_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_NOVE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_DEZ_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_ONZE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_DOZE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_TREZE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_QUATORZE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_QUINZE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_DEZESSEIS_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_DEZESSETE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_DEZOITO_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_DEZENOVE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_VINTE_DOSES.value(),
            VacinaCalendario.Doses.TRATAMENTO_COM_VINTE_E_QUATRO_DOSES.value()
    );

    public static final List<Long> TRATAMENTO_PROFILAXIA_UM_A_VINTE_E_QUATRO_FRASCOS_AMPOLAS = Arrays.asList(
            VacinaCalendario.Doses.PROFILAXIA_1_FRASCO_AMPOLA.value(),
            VacinaCalendario.Doses.PROFILAXIA_2_FRASCOS_AMPOLAS.value(),
            VacinaCalendario.Doses.PROFILAXIA_3_FRASCOS_AMPOLAS.value(),
            VacinaCalendario.Doses.PROFILAXIA_4_FRASCOS_AMPOLAS.value(),
            VacinaCalendario.Doses.PROFILAXIA_5_FRASCOS_AMPOLAS.value(),
            VacinaCalendario.Doses.PROFILAXIA_6_FRASCOS_AMPOLAS.value(),
            VacinaCalendario.Doses.PROFILAXIA_7_FRASCOS_AMPOLAS.value(),
            VacinaCalendario.Doses.PROFILAXIA_8_FRASCOS_AMPOLAS.value(),
            VacinaCalendario.Doses.PROFILAXIA_9_FRASCOS_AMPOLAS.value(),
            VacinaCalendario.Doses.PROFILAXIA_10_FRASCOS_AMPOLAS.value(),
            VacinaCalendario.Doses.PROFILAXIA_11_FRASCOS_AMPOLAS.value(),
            VacinaCalendario.Doses.PROFILAXIA_12_FRASCOS_AMPOLAS.value(),
            VacinaCalendario.Doses.PROFILAXIA_13_FRASCOS_AMPOLAS.value(),
            VacinaCalendario.Doses.PROFILAXIA_14_FRASCOS_AMPOLAS.value(),
            VacinaCalendario.Doses.PROFILAXIA_15_FRASCOS_AMPOLAS.value(),
            VacinaCalendario.Doses.PROFILAXIA_16_FRASCOS_AMPOLAS.value(),
            VacinaCalendario.Doses.PROFILAXIA_17_FRASCOS_AMPOLAS.value(),
            VacinaCalendario.Doses.PROFILAXIA_18_FRASCOS_AMPOLAS.value(),
            VacinaCalendario.Doses.PROFILAXIA_19_FRASCOS_AMPOLAS.value(),
            VacinaCalendario.Doses.PROFILAXIA_20_FRASCOS_AMPOLAS.value(),
            VacinaCalendario.Doses.PROFILAXIA_21_FRASCOS_AMPOLAS.value(),
            VacinaCalendario.Doses.PROFILAXIA_22_FRASCOS_AMPOLAS.value(),
            VacinaCalendario.Doses.PROFILAXIA_23_FRASCOS_AMPOLAS.value(),
            VacinaCalendario.Doses.PROFILAXIA_24_FRASCOS_AMPOLAS.value()
    );


    public static String validate(EsusValidacoesFichasDTOParam validationParam) throws ValidacaoException {
        StringBuilder stringBuilder = new StringBuilder();

        stringBuilder.append(getInconsistencies(validationParam, EsusValidacoesFichaPadraoHelper.validate(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateCboProfissional(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateCpfCnsUsuarioEsus(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateBirthdate(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateServiceLocation(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateImunibiologico(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateComunicanteHanseniase(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateGender(validationParam)));
        if (!RepositoryComponentDefault.SIM_LONG.equals(validationParam.getVacinaAplicacao().getFlagHistorico())) {
            stringBuilder.append(getInconsistencies(validationParam, validateLote(validationParam)));
            stringBuilder.append(getInconsistencies(validationParam, validateFabricante(validationParam)));
            stringBuilder.append(getInconsistencies(validationParam, validateVaccines(validationParam)));
        }
        stringBuilder.append(getInconsistencies(validationParam, validateEndDate(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateGrupoVacinacao(validationParam)));

        return stringBuilder.toString();
    }

    public static String validateEstrategiaEsus(EsusValidacoesFichasDTOParam validationParam) throws ValidacaoException {
        StringBuilder stringBuilder = new StringBuilder();

        validateItem(validationParam.getVacinaAplicacao().getVacinaCalendario().getCalendario().getEstrategiaEsus() == null, stringBuilder, "Estratégia e-sus não definida.");

        if (!stringBuilder.toString().isEmpty()) {
            getReturnMessage(validationParam, stringBuilder.toString());
        }

        return stringBuilder.toString();
    }

    public static String validateEndDate(EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();

        validateItem(validationParam.getEsusFichaVacina().getVacinaAplicacao().getDataAplicacaoFim() == null, stringBuilder, "Data/hora final não foi definida.");

        return stringBuilder.toString();
    }

    public static String validateGrupoVacinacao(EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();

        boolean isNotHistorico = RepositoryComponentDefault.NAO_LONG.equals(validationParam.getEsusFichaVacina().getVacinaAplicacao().getFlagHistorico());
        boolean grupoVacinacaoNull = validationParam.getEsusFichaVacina().getVacinaAplicacao().getGrupoAtendimento() == null && validationParam.getEsusFichaVacina().getUsuarioCadsus().getGrupoVacinacao() == null;

        validateItem(isNotHistorico && grupoVacinacaoNull, stringBuilder, "Grupo de Vacinação é obrigatório quando vacina não for histórico.");

        return stringBuilder.toString();
    }

    public static String validateCboProfissional(EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();

        if (validationParam.getTabelaCbo() == null) {
            appendInconsitency(stringBuilder, validationParam.getProfissional().getNome() + " com CBO não definido.");
        } else if (CollectionUtils.isEmpty(validationParam.getCboFichaEsusItemList())) {
            if (EsusHelper.doesntExistsCboFichaEsusItem(validationParam, CboFichaEsus.TipoFicha.FICHA_VACINA)) {
                appendInconsitency(stringBuilder, "CBO " + validationParam.getTabelaCbo().getDescricaoFormatado() + " do profissional " + validationParam.getProfissional().getNome() + " não permitido.");
            }
        } else if (!Lambda.exists(validationParam.getCboFichaEsusItemList(), having(on(CboFichaEsusItem.class).getTabelaCbo().getCbo(), equalTo(validationParam.getTabelaCbo().getCbo())))) {
            appendInconsitency(stringBuilder, "CBO " + validationParam.getTabelaCbo().getDescricaoFormatado() + " do profissional " + validationParam.getProfissional().getNome() + " não permitido.");
        }

        return stringBuilder.toString();
    }

    public static String validateCpfCnsUsuarioEsus(EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();

        if (("".equals(validationParam.getEsusFichaVacina().getUsuarioCadsus().getCpf()) || validationParam.getEsusFichaVacina().getUsuarioCadsus().getCpf() == null)
                && ("".equals(validationParam.getEsusFichaVacina().getUsuarioCadsus().getCns()) || validationParam.getEsusFichaVacina().getUsuarioCadsus().getCns() == null)) {
            appendInconsitency(stringBuilder, validationParam.getEsusFichaVacina().getUsuarioCadsus().getNome() + " com CPF ou CNS em branco");
        }

        return stringBuilder.toString();
    }

    public static String validateBirthdate(EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();

        if (validationParam.getEsusFichaVacina().getUsuarioCadsus().getDataNascimento() == null) {
            appendInconsitency(stringBuilder, validationParam.getTipoFicha().descricao() + ": Data de Nascimento é obrigatória.");
        } else {
            Date dataNascimento = Data.adjustRangeHour(validationParam.getEsusFichaVacina().getUsuarioCadsus().getDataNascimento()).getDataInicial();
            Date dataAtendimento = Data.adjustRangeHour(validationParam.getVacinaAplicacao().getDataAplicacao()).getDataInicial();

            if (dataNascimento.after(dataAtendimento)) {
                appendInconsitency(stringBuilder, validationParam.getTipoFicha().descricao() + ": Data de Nascimento não pode ser posterior à Data de Atendimento.");
            } else if (DataUtil.getAnosDiferenca(Data.adjustRangeHour(validationParam.getEsusFichaVacina().getUsuarioCadsus().getDataNascimento()).getDataInicial(),
                    Data.adjustRangeHour(validationParam.getVacinaAplicacao().getDataAplicacao()).getDataInicial()) > 130L) {
                appendInconsitency(stringBuilder, validationParam.getTipoFicha().descricao() + ": Data de Nascimento não pode ser anterior a 130 anos a partir da Data de Atendimento.");
            }
        }

        return stringBuilder.toString();
    }

    public static String validateServiceLocation(EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();

        if (validationParam.getEsusFichaVacina().getLocalAtendimento() == null) {
            appendInconsitency(stringBuilder, validationParam.getTipoFicha().descricao() + ": Local do Atendimento deve ser informado.");
        } else if (VacinaAplicacao.LocalAtendimento.valueOf(validationParam.getEsusFichaVacina().getLocalAtendimento()) == null) {
            appendInconsitency(stringBuilder, validationParam.getTipoFicha().descricao() + ": Local do Atendimento inválido: " + validationParam.getEsusFichaVacina().getLocalAtendimento() + ".");
        }

        return stringBuilder.toString();
    }

    public static String validateComunicanteHanseniase(EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();

        if (validationParam.getEsusFichaVacina().getVacinaAplicacao() != null &&
                validationParam.getEsusFichaVacina().getVacinaAplicacao().getTipoVacina() != null &&
                !TipoVacina.TipoEsus.VACINA_BCG.value().equals(validationParam.getEsusFichaVacina().getVacinaAplicacao().getTipoVacina().getTipoEsus()) &&
                RepositoryComponentDefault.SIM_LONG.equals(validationParam.getEsusFichaVacina().getVacinaAplicacao().getComunicanteHanseniase())) {
            appendInconsitency(stringBuilder, validationParam.getTipoFicha().descricao() + ": O campo Comunicante de Hanseníase somente pode ser preenchido se for registrada uma vacinação com o imunobiológico 15 - BCG");
        }

        return stringBuilder.toString();
    }

    public static String validateImunibiologico(EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();

        validateItem(validationParam.getEsusFichaVacina().getVacinaAplicacao().getTipoVacina() == null, stringBuilder, validationParam.getTipoFicha().descricao() + ": Defina o Tipo Vacina");
        if (validationParam.getEsusFichaVacina().getVacinaAplicacao().getTipoVacina() != null) {
            validateItem(validationParam.getEsusFichaVacina().getVacinaAplicacao().getTipoVacina().getTipoEsus() == null, stringBuilder, validationParam.getTipoFicha().descricao() + ": Defina o código do imunobiológico");
        }
        validateItem(
                (validationParam.getEsusFichaVacina().getVacinaAplicacao().getDose() == null &&
                        (validationParam.getEsusFichaVacina().getVacinaAplicacao().getVacinaCalendario() != null &&
                                validationParam.getEsusFichaVacina().getVacinaAplicacao().getVacinaCalendario().getDose() == null)),
                stringBuilder,
                validationParam.getTipoFicha().descricao() + ": Defina a Dose Vacina");

        return stringBuilder.toString();
    }

    public static String validateLote(EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();

        validateItem(validationParam.getEsusFichaVacina().getVacinaAplicacao().getLote() == null, stringBuilder, validationParam.getTipoFicha().descricao() + ": Lote é obrigatório");

        return stringBuilder.toString();
    }

    public static String validateGender(EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();

        validateItem(validationParam.getEsusFichaVacina().getSexo() == null, stringBuilder, validationParam.getTipoFicha().descricao() + ": Sexo do paciente é obrigatório");

        return stringBuilder.toString();
    }

    public static String validateFabricante(EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();
        VacinaAplicacao vacinaAplicacao = validationParam.getEsusFichaVacina().getVacinaAplicacao();

        if (vacinaAplicacao == null
                || vacinaAplicacao.getProdutoVacina() == null
                || vacinaAplicacao.getProdutoVacina().getProduto() == null
                || vacinaAplicacao.getProdutoVacina().getProduto().getFabricanteEsus() == null) {
            appendInconsitency(stringBuilder, validationParam.getTipoFicha().descricao() + ": Fabricante E-SUS é obrigatório");
        }
        if (vacinaAplicacao != null && vacinaAplicacao.getTipoVacina() != null &&
                TipoVacina.TIPO_VACINA_COVID_19.contains(vacinaAplicacao.getTipoVacina().getTipoEsus())) {
            if (TipoVacina.FabricanteEsusVacinaCovid.valueOf(vacinaAplicacao.getTipoVacina().getTipoEsus()) == null) {
                appendInconsitency(stringBuilder, validationParam.getTipoFicha().descricao().concat(": Fabricante e-SUS não encontrado para a vacina '").concat(vacinaAplicacao.getTipoVacina().getDescricao()).concat("'. Entre em contato com o suporte."));
            }
        }

        return stringBuilder.toString();
    }

    public static String validateVaccines(EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();

        validateBCG(validationParam, stringBuilder);
        validateColera(validationParam, stringBuilder);
        validateDengue(validationParam, stringBuilder);
        validateDuplaAdulto(validationParam, stringBuilder);
        validateDuplaInfantil(validationParam, stringBuilder);
        validateDTP(validationParam, stringBuilder);
        validateTricipeAcelularInfantil(validationParam, stringBuilder);
        validateRaivaCultivoCelularEmbriao(validationParam, stringBuilder);
        validateFebreAmarela(validationParam, stringBuilder);
        validateFrebreAmarelaFracionada(validationParam, stringBuilder);
        validateInfluenzaTrivalente(validationParam, stringBuilder);
        validateFebreTifoideAtenuada(validationParam, stringBuilder);
        validateFebreTifoidePolissacaridica(validationParam, stringBuilder);
        validateHepatiteACrie(validationParam, stringBuilder);
        validateADSHepatiteA(validationParam, stringBuilder);
        validateDuplaViral(validationParam, stringBuilder);
        validateHepatiteAInfantil(validationParam, stringBuilder);
        validateHepatiteB(validationParam, stringBuilder);
        validateHaemophilusTipoB(validationParam, stringBuilder);
        validateHPVQuadrivalente(validationParam, stringBuilder);
        validateImunoglobinaAntiHepatiteB(validationParam, stringBuilder);
        validateImunoglobinaAntiRabica(validationParam, stringBuilder);
        validateImunoglobinaAntitetanica(validationParam, stringBuilder);
        validateImunoglobinaAntiVaricelarZoster(validationParam, stringBuilder);
        validateMeningoC(validationParam, stringBuilder);
        validateMeningocicaACWY(validationParam, stringBuilder);
        validateDtpHbHib(validationParam, stringBuilder);
        validatePneumococica10V(validationParam, stringBuilder);
        validatePneumococica23V(validationParam, stringBuilder);
        validateSoroDifterico(validationParam, stringBuilder);
        validateSoroAracnidicoEscorpionico(validationParam, stringBuilder);
        validateSoroRabicoHumano(validationParam, stringBuilder);
        validateSoroTetanico(validationParam, stringBuilder);
        validateSoroBotropicoCrotalico(validationParam, stringBuilder);
        validateSoroBotropicoLaquetico(validationParam, stringBuilder);
        validateSoroBotropico(validationParam, stringBuilder);
        validateSoroBotulinicoBivalente(validationParam, stringBuilder);
        validateSoroBotulinicoTrivalente(validationParam, stringBuilder);
        validateSarampoCaxumbaRubeola(validationParam, stringBuilder);
        validateSoroCrotalico(validationParam, stringBuilder);
        validateSoroElapidico(validationParam, stringBuilder);
        validateSoroEscorpionico(validationParam, stringBuilder);
        validateSoroLonomico(validationParam, stringBuilder);
        validateSoroLoxoscelico(validationParam, stringBuilder);
        validateTetraViral(validationParam, stringBuilder);
        validateVaricelaAtenuada(validationParam, stringBuilder);
        validateRaivaCultivoCelularVero(validationParam, stringBuilder);
        validatePolioInjetavel(validationParam, stringBuilder);
        validatePolioOral(validationParam, stringBuilder);
        validateRotavirusHumano(validationParam, stringBuilder);
        validateCovid19JanssenCilag(validationParam, stringBuilder);
        validateCovid19PfizerBiotech(validationParam, stringBuilder);
        validateCovid19CoronavacButantanSinoVac(validationParam, stringBuilder);
        validateCovid19FiocruzOxford(validationParam, stringBuilder);
        validateCovid19Astrazeneca(validationParam, stringBuilder);
        validateCovid19PfizerBivalente(validationParam, stringBuilder);
        validateCovid19PfizerMenor5Anos(validationParam, stringBuilder);
        validateCovid19PfizerPediatrica(validationParam, stringBuilder);
        validateDengueAtenuada(validationParam, stringBuilder);
        validateCovid19Moderna(validationParam, stringBuilder);
        validateSinovacCoronavac(validationParam, stringBuilder);
        validateHexavalente(validationParam, stringBuilder);
        validatePneumococia13V(validationParam, stringBuilder);
        validateDtpaHibPolioInativada(validationParam, stringBuilder);
        validateVariolaBavarianNordic(validationParam, stringBuilder);
        validateDtpaAdulto(validationParam, stringBuilder);
        validateVacinaVirusSincicialRespiratorioRecombinanteAdjuvada(validationParam, stringBuilder);
        validateVacinaCovid19RecombinanteSerumZalika(validationParam, stringBuilder);

        return stringBuilder.toString();
    }

    public static void validateBCG(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        final List<Long> commonDoses = Arrays.asList(
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.DOSE_UNICA.value()
        );


        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), commonDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), commonDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(), commonDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.INTENSIFICACAO.getCodigoEsusPni(), Arrays.asList(
                VacinaCalendario.Doses.DOSE_UNICA.value()
        ));

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_BCG);
    }

    public static void validateColera(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE.value()
                )
        );

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.COLERA_ORAL);
    }

    public static void validateDuplaAdulto(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        final List<Long> commonDoses = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE_3.value(),
                VacinaCalendario.Doses.REFORCO.value()
        );

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.BLOQUEIO.getCodigoEsusPni(), commonDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.INTENSIFICACAO.getCodigoEsusPni(), commonDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), commonDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), DOSES_1_2);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(), commonDoses);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_DIFTERIA_TETANO_ADULTO);
    }

    public static void validateDengue(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), DOSES_1_2);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_DENGUE_1_2_3_4);
    }

    public static void validateFrebreAmarelaFracionada(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.INTENSIFICACAO.getCodigoEsusPni(), Arrays.asList(VacinaCalendario.Doses.DOSE.value()));

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.FEBRE_AMARELA_DOSE_FRACIONADA);
    }


    public static void validateDuplaInfantil(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        final List<Long> commonDoses = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE_1_REFORCO.value(),
                VacinaCalendario.Doses.DOSE_2_REFORCO.value()
        );

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), commonDoses);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_DIFTERIA_E_TETANO_INFANTIL);
    }

    public static void validateDTP(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        final List<Long> commonDoses = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1_REFORCO.value(),
                VacinaCalendario.Doses.DOSE_2_REFORCO.value()
        );

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), commonDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.BLOQUEIO.getCodigoEsusPni(), commonDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.INTENSIFICACAO.getCodigoEsusPni(), commonDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(), commonDoses);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_DTP);
    }

    public static void validateTricipeAcelularInfantil(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        final List<Long> commonDoses = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1_REFORCO.value(),
                VacinaCalendario.Doses.DOSE_2_REFORCO.value()
        );

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), commonDoses);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.TRIPLICE_ACELULAR_INFANTIL);
    }

    public static void validateRaivaCultivoCelularEmbriao(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), DOSES_1_2_3_4_5_REV);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.RAIVA_CULTIVO_CELULAR_EMBRIAO);
    }

    public static void validateFebreAmarela(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        final List<Long> commonDoses = Arrays.asList(
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.DOSE_UNICA.value(),
                VacinaCalendario.Doses.REFORCO.value()
        );

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), commonDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.BLOQUEIO.getCodigoEsusPni(), commonDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.INTENSIFICACAO.getCodigoEsusPni(), commonDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(), commonDoses);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.FEBRE_AMARELA);
    }

    public static void validateInfluenzaTrivalente(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_INDISCRIMINADA.getCodigoEsusPni(), DOSES_1_2_U);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), DOSES_1_2_U);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), DOSES_1_2_U);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(), DOSES_1_2_U);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.INFLUENZA_TRIVALENTE);
    }

    public static void validateFebreTifoideAtenuada(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.FEBRE_TIFOIDE_ATENUADA);
    }

    public static void validateFebreTifoidePolissacaridica(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE_UNICA.value(),
                        VacinaCalendario.Doses.REVACINACAO.value()
                )
        );

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.FEBRE_TIFOIDE_POLISSACARIDICA);
    }

    public static void validateHepatiteACrie(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), DOSES_1_2);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.HEPATITE_A_CRIE);
    }

    public static void validateADSHepatiteA(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), DOSES_1_2);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_HEPATITE_A_ADULTO);
    }

    public static void validateHepatiteAInfantil(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), Arrays.asList(VacinaCalendario.Doses.DOSE_UNICA.value()));
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), DOSES_1_2_U);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.BLOQUEIO.getCodigoEsusPni(), Arrays.asList(VacinaCalendario.Doses.DOSE_UNICA.value()));
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.INTENSIFICACAO.getCodigoEsusPni(), Arrays.asList(VacinaCalendario.Doses.DOSE_UNICA.value()));
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(), Arrays.asList(VacinaCalendario.Doses.DOSE_UNICA.value()));

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_HEPATITE_A_INFANTIL);
    }

    public static void validateHepatiteB(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        final List<Long> commonDoses = Arrays.asList(
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE_3.value()
        );

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE_1.value(),
                        VacinaCalendario.Doses.DOSE_2.value(),
                        VacinaCalendario.Doses.DOSE_3.value(),
                        VacinaCalendario.Doses.DOSE_1_REVACINACAO.value(),
                        VacinaCalendario.Doses.DOSE_2_REVACINACAO.value(),
                        VacinaCalendario.Doses.DOSE_3_REVACINACAO.value(),
                        VacinaCalendario.Doses.DOSE_1_DOBRADA.value(),
                        VacinaCalendario.Doses.DOSE_2_DOBRADA.value(),
                        VacinaCalendario.Doses.DOSE_3_DOBRADA.value(),
                        VacinaCalendario.Doses.DOSE_4_DOBRADA.value(),
                        VacinaCalendario.Doses.DOSE_1_REVACINACAO_DOBRADA.value(),
                        VacinaCalendario.Doses.DOSE_2_REVACINACAO_DOBRADA.value(),
                        VacinaCalendario.Doses.DOSE_3_REVACINACAO_DOBRADA.value(),
                        VacinaCalendario.Doses.DOSE_4_REVACINACAO_DOBRADA.value()
                )
        );

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), commonDoses);

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(), commonDoses);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_HEPATITE_B);
    }

    public static void validateHaemophilusTipoB(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE_1.value(),
                        VacinaCalendario.Doses.DOSE_2.value(),
                        VacinaCalendario.Doses.DOSE_3.value(),
                        VacinaCalendario.Doses.DOSE_UNICA.value(),
                        VacinaCalendario.Doses.DOSE_1_REFORCO.value(),
                        VacinaCalendario.Doses.DOSE.value()
                )
        );

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_HIB);
    }

    public static void validateHPVQuadrivalente(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), Arrays.asList(VacinaCalendario.Doses.DOSE_UNICA.value()));
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), DOSES_1_2_3);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.INTENSIFICACAO.getCodigoEsusPni(), Arrays.asList(VacinaCalendario.Doses.DOSE_UNICA.value()));
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(), Arrays.asList(VacinaCalendario.Doses.DOSE_UNICA.value()));

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.HPV_QUADRIVALENTE);
    }

    public static void validateDuplaViral(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.BLOQUEIO.getCodigoEsusPni(), Arrays.asList(VacinaCalendario.Doses.DOSE.value()));
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.INTENSIFICACAO.getCodigoEsusPni(), Arrays.asList(VacinaCalendario.Doses.DOSE_1.value(), VacinaCalendario.Doses.DOSE_2.value()));
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), Arrays.asList(VacinaCalendario.Doses.DOSE.value()));

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.DUPLA_VIRAL);
    }


    public static void validateImunoglobinaAntiHepatiteB(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), TRATAMENTO_PROFILAXIA_UM_A_VINTE_E_QUATRO_FRASCOS_AMPOLAS);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.IMUNOGLOBULINA_HUMANA_ANTI_HEPATITE_B);
    }

    public static void validateImunoglobinaAntiRabica(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), TRATAMENTO_PROFILAXIA_UM_A_VINTE_E_QUATRO_FRASCOS_AMPOLAS);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.IMUNOGLOBULINA_HUMANA_ANTI_RABICA);
    }

    public static void validateImunoglobinaAntitetanica(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), TRATAMENTO_PROFILAXIA_UM_A_VINTE_E_QUATRO_FRASCOS_AMPOLAS);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.IMUNOGLOBULINA_HUMANA_ANTITETANO);
    }

    public static void validateImunoglobinaAntiVaricelarZoster(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), TRATAMENTO_COM_UMA_A_CINCO_DOSES);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.IMUNOGLOBULINA_HUMANA_ANTIVARICELA);
    }

    public static void validateMeningoC(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        final List<Long> dosesRotina = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.DOSE_UNICA.value(),
                VacinaCalendario.Doses.REFORCO.value()
        );

        final List<Long> dosesEspecial = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.REFORCO.value()
        );

        final List<Long> dosesBloqueio = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE_UNICA.value(),
                VacinaCalendario.Doses.REFORCO.value()
        );

        final List<Long> dosesIntensificacao = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.DOSE_UNICA.value(),
                VacinaCalendario.Doses.REFORCO.value()
        );

        final List<Long> dosesVacinacaoEscolar = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.DOSE_UNICA.value(),
                VacinaCalendario.Doses.REFORCO.value()
        );


        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), dosesRotina);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), dosesEspecial);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.BLOQUEIO.getCodigoEsusPni(), dosesBloqueio);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.INTENSIFICACAO.getCodigoEsusPni(), dosesIntensificacao);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(), dosesVacinacaoEscolar);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_MENINGO_C);
    }

    public static void validateMeningocicaACWY(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        final List<Long> commonDoses = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.DOSE_UNICA.value(),
                VacinaCalendario.Doses.REFORCO.value()
        );

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE_1.value(),
                        VacinaCalendario.Doses.DOSE_2.value(),
                        VacinaCalendario.Doses.REFORCO.value()
                )
        );

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE_1.value(),
                        VacinaCalendario.Doses.DOSE_2.value(),
                        VacinaCalendario.Doses.DOSE.value(),
                        VacinaCalendario.Doses.DOSE_UNICA.value(),
                        VacinaCalendario.Doses.REFORCO.value()
                )
        );

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.BLOQUEIO.getCodigoEsusPni(), commonDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.INTENSIFICACAO.getCodigoEsusPni(), commonDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(), commonDoses);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.MENINGOCOCICA_ACWY);
    }

    public static void validateDtpHbHib(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.INTENSIFICACAO.getCodigoEsusPni(), DOSES_1_2_3);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), DOSES_1_2_3_R1_R2);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.MONITORAMENTO_RAPIDO_COBERTURA_VACINAL.getCodigoEsusPni(), DOSES_1_2_3);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), Arrays.asList(VacinaCalendario.Doses.DOSE.value()));
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(), DOSES_1_2_3_R1_R2);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_PENTA_DTP_HEPB_HIB);
    }

    public static void validatePneumococica10V(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        final List<Long> commonDoses = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE_UNICA.value(),
                VacinaCalendario.Doses.REFORCO.value()
        );

        final List<Long> especialDoses = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE_3.value(),
                VacinaCalendario.Doses.DOSE_UNICA.value(),
                VacinaCalendario.Doses.REFORCO.value()
        );

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), commonDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), especialDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.INTENSIFICACAO.getCodigoEsusPni(), commonDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(), commonDoses);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_PNEUMO_10);
    }

    public static void validatePneumococica23V(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(),DOSES_1_2);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), DOSES_1_2);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(), DOSES_1_2);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_PNEUMO_23);
    }

    public static void validateSoroDifterico(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.SOROTERAPIA.getCodigoEsusPni(), TRATAMENTO_PROFILAXIA_UM_A_VINTE_E_QUATRO_FRASCOS_AMPOLAS);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.SORO_DIFTERICO);
    }

    public static void validateSoroAracnidicoEscorpionico(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.SOROTERAPIA.getCodigoEsusPni(), TRATAMENTO_PROFILAXIA_UM_A_VINTE_E_QUATRO_FRASCOS_AMPOLAS);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.SORO_ARACNIDICO_ESCORPIONICO);
    }

    public static void validateSoroRabicoHumano(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.SOROTERAPIA.getCodigoEsusPni(), TRATAMENTO_PROFILAXIA_UM_A_VINTE_E_QUATRO_FRASCOS_AMPOLAS);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.SORO_RABICO_HUMANO);
    }

    public static void validateSoroTetanico(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.SOROTERAPIA.getCodigoEsusPni(), TRATAMENTO_PROFILAXIA_UM_A_VINTE_E_QUATRO_FRASCOS_AMPOLAS);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.SORO_TETANICO);
    }

    public static void validateSoroBotropicoCrotalico(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.SOROTERAPIA.getCodigoEsusPni(), TRATAMENTO_PROFILAXIA_UM_A_VINTE_E_QUATRO_FRASCOS_AMPOLAS);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.SORO_BOTROPICO_CROTALICO);
    }

    public static void validateSoroBotropicoLaquetico(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.SOROTERAPIA.getCodigoEsusPni(), TRATAMENTO_PROFILAXIA_UM_A_VINTE_E_QUATRO_FRASCOS_AMPOLAS);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.SORO_BOTROPICO_LAQUETICO);
    }

    public static void validateSoroBotropico(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.SOROTERAPIA.getCodigoEsusPni(), TRATAMENTO_PROFILAXIA_UM_A_VINTE_E_QUATRO_FRASCOS_AMPOLAS);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.SORO_BOTROPICO);
    }

    public static void validateSoroBotulinicoBivalente(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.SOROTERAPIA.getCodigoEsusPni(), TRATAMENTO_PROFILAXIA_UM_A_VINTE_E_QUATRO_FRASCOS_AMPOLAS);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.SORO_BOTULINICO_BIVALENTE);
    }

    public static void validateSoroBotulinicoTrivalente(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.SOROTERAPIA.getCodigoEsusPni(), TRATAMENTO_COM_UMA_A_VINTE_E_QUATRO_DOSES);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.SORO_BOTULINICO_TRIVALENTE);
    }

    public static void validateSarampoCaxumbaRubeola(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        final List<Long> commonDoses = Arrays.asList(
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value()
        );

        final List<Long> dosesBloqueio = Arrays.asList(
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE_ZERO.value()
        );

        final List<Long> dosesIntensificacao = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.DOSE_ZERO.value()
        );

        final List<Long> MonitoriamentoCommonDoses = Arrays.asList(
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE_ZERO.value()
        );

        final List<Long> campanhaSeletivaDoses = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE_3.value(),
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.DOSE_ZERO.value()
        );

        final List<Long> vacinacaoEscolarDoses = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value()
        );

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), commonDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.BLOQUEIO.getCodigoEsusPni(), dosesBloqueio);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.INTENSIFICACAO.getCodigoEsusPni(), dosesIntensificacao);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_INDISCRIMINADA.getCodigoEsusPni(), commonDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.MONITORAMENTO_RAPIDO_COBERTURA_VACINAL.getCodigoEsusPni(), MonitoriamentoCommonDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_SELETIVA.getCodigoEsusPni(), campanhaSeletivaDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(), vacinacaoEscolarDoses);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_SARAMPO_CAXUMBA_RUBEOLA);
    }

    public static void validateSoroCrotalico(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.SOROTERAPIA.getCodigoEsusPni(), TRATAMENTO_PROFILAXIA_UM_A_VINTE_E_QUATRO_FRASCOS_AMPOLAS);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.SORO_CROTALICO);
    }

    public static void validateSoroElapidico(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.SOROTERAPIA.getCodigoEsusPni(), TRATAMENTO_PROFILAXIA_UM_A_VINTE_E_QUATRO_FRASCOS_AMPOLAS);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.SORO_ELAPIDICO);
    }

    public static void validateSoroEscorpionico(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.SOROTERAPIA.getCodigoEsusPni(), TRATAMENTO_PROFILAXIA_UM_A_VINTE_E_QUATRO_FRASCOS_AMPOLAS);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.SORO_ESCORPIONICO);
    }

    public static void validateSoroLonomico(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.SOROTERAPIA.getCodigoEsusPni(), TRATAMENTO_PROFILAXIA_UM_A_VINTE_E_QUATRO_FRASCOS_AMPOLAS);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.SORO_LONOMICO);
    }

    public static void validateSoroLoxoscelico(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.SOROTERAPIA.getCodigoEsusPni(), TRATAMENTO_PROFILAXIA_UM_A_VINTE_E_QUATRO_FRASCOS_AMPOLAS);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.SORO_LOXOSCELICO);
    }

    public static void validateTetraViral(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.BLOQUEIO.getCodigoEsusPni(), Arrays.asList(VacinaCalendario.Doses.DOSE_UNICA.value()));
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.INTENSIFICACAO.getCodigoEsusPni(), Arrays.asList(VacinaCalendario.Doses.DOSE_UNICA.value()));
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), Arrays.asList(VacinaCalendario.Doses.DOSE_UNICA.value()));
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.MONITORAMENTO_RAPIDO_COBERTURA_VACINAL.getCodigoEsusPni(), DOSES_1_2_U);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), Arrays.asList(VacinaCalendario.Doses.DOSE_UNICA.value()));
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(), Arrays.asList(VacinaCalendario.Doses.DOSE_UNICA.value()));

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_SARAMPO_CAXUMBA_RUBEOLA_VARICELA);
    }

    public static void validateVaricelaAtenuada(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        final List<Long> dosesBloqueio = Arrays.asList(
                VacinaCalendario.Doses.DOSE_UNICA.value(),
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE_ZERO.value()
        );

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.BLOQUEIO.getCodigoEsusPni(), dosesBloqueio);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), DOSES_1_2);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.INTENSIFICACAO.getCodigoEsusPni(), DOSES_1_2);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), DOSES_1_2_U);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(), DOSES_1_2_U);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_VARICELA);
    }

    public static void validateRaivaCultivoCelularVero(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        final List<Long> commonDoses = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE_3.value(),
                VacinaCalendario.Doses.DOSE_4.value()

        );

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), DOSES_1_2_3_4_5_REV);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.PRE_EXPOSICAO.getCodigoEsusPni(), DOSES_1_2);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.REEXPOSICAO.getCodigoEsusPni(), commonDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.POS_EXPOSICAO.getCodigoEsusPni(), commonDoses);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.RAIVA_CULTIVO_CELULAR_VERO);
    }

    public static void validatePolioInjetavel(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        final List<Long> dosesRotina = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE_3.value(),
                VacinaCalendario.Doses.REFORCO.value()
        );

        final List<Long> dosesEspecial = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE_3.value(),
                VacinaCalendario.Doses.DOSE_1_REFORCO.value(),
                VacinaCalendario.Doses.DOSE_2_REFORCO.value()
        );

        final List<Long> dosesIntensificacao = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE_3.value(),
                VacinaCalendario.Doses.REFORCO.value()
        );

        final List<Long> dosesIndiscriminada = Arrays.asList(
                VacinaCalendario.Doses.DOSE.value()
        );

        final List<Long> dosesMonitoramento = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE_3.value(),
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.REFORCO.value()
        );

        final List<Long> dosesEscolar = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE_3.value(),
                VacinaCalendario.Doses.REFORCO.value()
        );

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), dosesRotina);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), dosesEspecial);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.INTENSIFICACAO.getCodigoEsusPni(), dosesIntensificacao);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_INDISCRIMINADA.getCodigoEsusPni(), dosesIndiscriminada);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.MONITORAMENTO_RAPIDO_COBERTURA_VACINAL.getCodigoEsusPni(), dosesMonitoramento);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(), dosesEscolar);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_POLIO_INJETAVEL);
    }

    public static void validatePolioOral(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        final List<Long> dosesIntensificacao = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1_REFORCO.value(),
                VacinaCalendario.Doses.DOSE_2_REFORCO.value()
        );

        final List<Long> dosesCampanhaIndiscriminada = Arrays.asList(
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.DOSE_1_REFORCO.value(),
                VacinaCalendario.Doses.DOSE_2_REFORCO.value(),
                VacinaCalendario.Doses.REFORCO_ZERO.value()
        );

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), DOSES_1_2_3_R1_R2);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.INTENSIFICACAO.getCodigoEsusPni(), dosesIntensificacao);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_INDISCRIMINADA.getCodigoEsusPni(), dosesCampanhaIndiscriminada);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.MONITORAMENTO_RAPIDO_COBERTURA_VACINAL.getCodigoEsusPni(), Arrays.asList(DOSE.value()));

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_POLIO_ORAL);
    }

    public static void validateRotavirusHumano(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.INTENSIFICACAO.getCodigoEsusPni(), DOSES_1_2);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), DOSES_1_2);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.MONITORAMENTO_RAPIDO_COBERTURA_VACINAL.getCodigoEsusPni(), DOSES_1_2);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(), DOSES_1_2);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_ROTAVIRUS_HUMANO);
    }

    public static void validateCovid19PfizerBiotech(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), Arrays.asList(
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.REVACINACAO.value()
        ));

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_INDISCRIMINADA.getCodigoEsusPni(), Arrays.asList(
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.REVACINACAO.value()
        ));
        final List<Long> especialDoses = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE_3.value(),
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.REVACINACAO.value(),
                VacinaCalendario.Doses.DOSE_1_REVACINACAO.value(),
                VacinaCalendario.Doses.DOSE_2_REVACINACAO.value(),
                VacinaCalendario.Doses.DOSE_3_REVACINACAO.value()
                );

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_SELETIVA.getCodigoEsusPni(), DOSES_1_2_REF_R2);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), especialDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), Arrays.asList(
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.REVACINACAO.value()
        ));


        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_SELETIVA.getCodigoEsusPni(), Arrays.asList(
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.REVACINACAO.value()
        ));

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.COVID_19_PFIZER_COMIRNATY);
    }

    public static void validateCovid19FiocruzOxford(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_SELETIVA.getCodigoEsusPni(), DOSE_1_2_REF_R2_DA);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.COVID_19_FIOCRUZ_OXFORD);
    }

    public static void validateCovid19JanssenCilag(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_SELETIVA.getCodigoEsusPni(), DOSE_1_REF_R2_DA);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.COVID_19_JANSSEN_CILAG);
    }


    public static void validateCovid19CoronavacButantanSinoVac(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE_1.value(),
                        VacinaCalendario.Doses.DOSE_2.value(),
                        VacinaCalendario.Doses.DOSE_1_REFORCO.value()
                ));

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_SELETIVA.getCodigoEsusPni(), Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE_3.value(),
                VacinaCalendario.Doses.DOSE_1_REFORCO.value(),
                VacinaCalendario.Doses.DOSE_2_REFORCO.value(),
                VacinaCalendario.Doses.REVACINACAO.value(),
                VacinaCalendario.Doses.DOSE_1_REVACINACAO.value(),
                VacinaCalendario.Doses.DOSE_2_REVACINACAO.value(),
                VacinaCalendario.Doses.DOSE_3_REVACINACAO.value(),
                VacinaCalendario.Doses.DOSE_4_REVACINACAO.value(),
                VacinaCalendario.Doses.DOSE_5_REVACINACAO.value()
        ));


        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_COVID_19_INANTIVADA_SINOVAC_BUTANTAN_CORONAVAC);
    }

    public static void validateCovid19Astrazeneca(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_SELETIVA.getCodigoEsusPni(), DOSES_1_2_REFORCO);

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.COVID_19_ASTRAZENECA_AB);
    }

    public static void validateCovid19PfizerBivalente(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_SELETIVA.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE_1.value(),
                        VacinaCalendario.Doses.DOSE_2.value(),
                        VacinaCalendario.Doses.DOSE_3.value(),
                        VacinaCalendario.Doses.DOSE_1_REVACINACAO.value(),
                        VacinaCalendario.Doses.DOSE_2_REVACINACAO.value(),
                        VacinaCalendario.Doses.DOSE_3_REVACINACAO.value(),
                        VacinaCalendario.Doses.REFORCO.value()
                )
        );

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_COVID19_RNAM_PFZ_COMIRNATY_BIVALENTE);
    }

    public static void validateCovid19PfizerMenor5Anos(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_INDISCRIMINADA.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE.value(),
                        VacinaCalendario.Doses.REVACINACAO.value()
                ));
        final List<Long> especialDoses = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE_3.value(),
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.REVACINACAO.value(),
                VacinaCalendario.Doses.DOSE_1_REVACINACAO.value(),
                VacinaCalendario.Doses.DOSE_2_REVACINACAO.value(),
                VacinaCalendario.Doses.DOSE_3_REVACINACAO.value()
        );

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), especialDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_INDISCRIMINADA.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE.value(),
                        VacinaCalendario.Doses.REVACINACAO.value()
                ));

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), DOSES_1_2_3);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_SELETIVA.getCodigoEsusPni(),  Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE_3.value(),
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.REVACINACAO.value()
        ));
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE_1.value(),
                        VacinaCalendario.Doses.DOSE_2.value(),
                        VacinaCalendario.Doses.DOSE_3.value()
                ));

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_COVID_19_RNAM_PFIZER_COMIRNATY_PEDIATRICA_MENOR);
    }

    public static void validateCovid19PfizerPediatrica(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        final List<Long> especialDoses = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE_3.value(),
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.REVACINACAO.value(),
                VacinaCalendario.Doses.DOSE_1_REVACINACAO.value(),
                VacinaCalendario.Doses.DOSE_2_REVACINACAO.value(),
                VacinaCalendario.Doses.DOSE_3_REVACINACAO.value()
        );

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), especialDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_SELETIVA.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE.value(),
                        VacinaCalendario.Doses.DOSE_1.value(),
                        VacinaCalendario.Doses.DOSE_2.value(),
                        VacinaCalendario.Doses.DOSE_3.value(),
                        VacinaCalendario.Doses.DOSE_1_REFORCO.value(),
                        VacinaCalendario.Doses.REVACINACAO.value(),
                        VacinaCalendario.Doses.DOSE_1_REVACINACAO.value(),
                        VacinaCalendario.Doses.DOSE_2_REVACINACAO.value(),
                        VacinaCalendario.Doses.DOSE_3_REVACINACAO.value()
                ));
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_INDISCRIMINADA.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE.value(),
                        VacinaCalendario.Doses.REVACINACAO.value()
                ));
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE.value(),
                        VacinaCalendario.Doses.REVACINACAO.value()
                ));
        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_COVID_19_RNAM_PFZ_COMIRNATY);
    }

    public static void validateDengueAtenuada(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), DOSES_1_2);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(), DOSES_1_2);
        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_DENGUE_ATENUADA);
    }

    public static void validateHexavalente(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE_1.value(),
                        VacinaCalendario.Doses.DOSE_2.value(),
                        VacinaCalendario.Doses.DOSE_3.value(),
                        VacinaCalendario.Doses.DOSE_1_REFORCO.value(),
                        VacinaCalendario.Doses.DOSE_2_REFORCO.value(),
                        VacinaCalendario.Doses.DOSE.value()
                ));

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE_1.value(),
                        VacinaCalendario.Doses.DOSE_2.value(),
                        VacinaCalendario.Doses.DOSE_3.value()
                ));

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_HEXA_DTPA_HEPB_VIP_HIB);
    }

    public static void validatePneumococia13V(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE_1.value(),
                        VacinaCalendario.Doses.DOSE_2.value(),
                        VacinaCalendario.Doses.DOSE_3.value(),
                        VacinaCalendario.Doses.DOSE_UNICA.value(),
                        VacinaCalendario.Doses.DOSE_1_REFORCO.value()
                ));

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE_1.value(),
                        VacinaCalendario.Doses.DOSE_2.value(),
                        VacinaCalendario.Doses.DOSE_UNICA.value(),
                        VacinaCalendario.Doses.DOSE.value(),
                        VacinaCalendario.Doses.REFORCO.value()
                ));

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.PNEUMOCOCICA_13V);
    }

    public static void validateDtpaHibPolioInativada(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE_1.value(),
                        VacinaCalendario.Doses.DOSE_2.value(),
                        VacinaCalendario.Doses.DOSE_3.value(),
                        VacinaCalendario.Doses.REFORCO.value()
                ));
        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.DTPA_HIB_POLIO_INATIVA);
    }

    public static void validateDtpaAdulto(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        final List<Long> dosesRotina = Arrays.asList(
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.REFORCO.value()
        );
        final List<Long> dosesEspecial = Arrays.asList(
                VacinaCalendario.Doses.DOSE_1.value(),
                VacinaCalendario.Doses.DOSE_2.value(),
                VacinaCalendario.Doses.DOSE_3.value(),
                VacinaCalendario.Doses.DOSE.value()
        );
        final List<Long> dosesIntensificacao = Arrays.asList(
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.REFORCO.value()
        );
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.BLOQUEIO.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE.value()
                )
        );
        final List<Long> dosesVacinacaoEscolar = Arrays.asList(
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.REFORCO.value()
        );
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), dosesRotina);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(), dosesEspecial);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.INTENSIFICACAO.getCodigoEsusPni(), dosesIntensificacao);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(), dosesVacinacaoEscolar);
        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_DTPA_ADULTO);
    }

    public static void validateCovid19Moderna(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE_1.value(),
                        VacinaCalendario.Doses.DOSE_2.value(),
                        VacinaCalendario.Doses.REVACINACAO.value(),
                        VacinaCalendario.Doses.DOSE.value()
                ));

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_SELETIVA.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE_1.value(),
                        VacinaCalendario.Doses.DOSE_2.value(),
                        VacinaCalendario.Doses.DOSE_1_REFORCO.value(),
                        VacinaCalendario.Doses.DOSE.value(),
                        VacinaCalendario.Doses.REVACINACAO.value(),
                        VacinaCalendario.Doses.DOSE_1_REVACINACAO.value(),
                        VacinaCalendario.Doses.DOSE_2_REVACINACAO.value()
                ));

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_INDISCRIMINADA.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE.value(),
                        VacinaCalendario.Doses.REVACINACAO.value()
                ));

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE_1.value(),
                        VacinaCalendario.Doses.DOSE_2.value(),
                        VacinaCalendario.Doses.DOSE_3.value(),
                        VacinaCalendario.Doses.DOSE.value(),
                        VacinaCalendario.Doses.REVACINACAO.value(),
                        VacinaCalendario.Doses.DOSE_1_REVACINACAO.value(),
                        VacinaCalendario.Doses.DOSE_2_REVACINACAO.value(),
                        VacinaCalendario.Doses.DOSE_3_REVACINACAO.value()
                ));

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.VACINACAO_ESCOLAR.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE_1.value(),
                        VacinaCalendario.Doses.DOSE_2.value()
                ));

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.COVID_19_MODERNA);
    }

    public static void validateVariolaBavarianNordic(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_SELETIVA.getCodigoEsusPni(), DOSES_1_2);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.PRE_EXPOSICAO.getCodigoEsusPni(), DOSES_1_2);
        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VARIOLA_BAVARIAN_NORDIC);
    }

    public static void validateSinovacCoronavac(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_SELETIVA.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE_1.value(),
                        VacinaCalendario.Doses.DOSE_2.value(),
                        VacinaCalendario.Doses.DOSE_3.value(),
                        VacinaCalendario.Doses.DOSE_1_REFORCO.value(),
                        VacinaCalendario.Doses.DOSE_2_REFORCO.value(),
                        VacinaCalendario.Doses.REVACINACAO.value(),
                        VacinaCalendario.Doses.DOSE_1_REVACINACAO.value(),
                        VacinaCalendario.Doses.DOSE_2_REVACINACAO.value(),
                        VacinaCalendario.Doses.DOSE_3_REVACINACAO.value(),
                        VacinaCalendario.Doses.DOSE_4_REVACINACAO.value(),
                        VacinaCalendario.Doses.DOSE_5_REVACINACAO.value()
                ));
        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.COVID_19_SINOVAC_CORONAVAC);
    }

    public static void validateVacinaVirusSincicialRespiratorioRecombinanteAdjuvada(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), Arrays.asList(VacinaCalendario.Doses.REVACINACAO.value()));

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_VIRUS_SINCICIAL_RESPIRATORIO_RECOMBINANTE_ADJUVADA);
    }

    public static void validateVacinaCovid19RecombinanteSerumZalika(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder) {
        Map<Long, List<Long>> validStrategies = new HashMap<>();

        final List<Long> commonDoses = Arrays.asList(
                VacinaCalendario.Doses.DOSE.value(),
                VacinaCalendario.Doses.REVACINACAO.value()
        );

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_INDISCRIMINADA.getCodigoEsusPni(), commonDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.CAMPANHA_SELETIVA.getCodigoEsusPni(), commonDoses);
        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ROTINA.getCodigoEsusPni(), commonDoses);

        validStrategies.put(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni(),
                Arrays.asList(
                        VacinaCalendario.Doses.DOSE_1.value(),
                        VacinaCalendario.Doses.DOSE_2.value(),
                        VacinaCalendario.Doses.DOSE_3.value(),
                        VacinaCalendario.Doses.DOSE.value(),
                        VacinaCalendario.Doses.REVACINACAO.value(),
                        VacinaCalendario.Doses.DOSE_1_REVACINACAO.value(),
                        VacinaCalendario.Doses.DOSE_2_REVACINACAO.value(),
                        VacinaCalendario.Doses.DOSE_3_REVACINACAO.value()
                ));

        validateVaccine(validationParam, stringBuilder, validStrategies, TipoVacina.TipoEsus.VACINA_COVID19_RECOMBINANTE_SERUM_ZALIKA);
    }


    public static void validateVaccine(EsusValidacoesFichasDTOParam validationParam, StringBuilder stringBuilder, Map<Long, List<Long>> validStrategies, TipoVacina.TipoEsus tipoEsus) {
        VacinaAplicacao vacinaAplicacao = validationParam.getEsusFichaVacina().getVacinaAplicacao();

        if (vacinaAplicacao.getTipoVacina() != null && tipoEsus.value().equals(vacinaAplicacao.getTipoVacina().getTipoEsus())) {
            // Valida se estratégia usada está entre as estratégias válidas

            Calendario calendario = vacinaAplicacao.getEstrategiaOuVacinaCalendario();

            boolean isVaccineInvalid = calendario == null || !validStrategies.containsKey(calendario.getEstrategiaEsus());

            if (!isVaccineInvalid) {
                for (Map.Entry<Long, List<Long>> entry : validStrategies.entrySet()) {
                    Long strategy = entry.getKey();
                    List<Long> doses = entry.getValue();

                    // Procura uma dose valida para a estratégia usada
                    if (strategy.equals(calendario.getEstrategiaEsus()) && doses.contains(getDose(vacinaAplicacao))) {
                        isVaccineInvalid = false;
                        break;
                    }
                    isVaccineInvalid = true;
                }
            }

            if (isVaccineInvalid) {
                appendInconsitency(stringBuilder, buildInconsistencyMessage(vacinaAplicacao, validationParam));
            }
        }
    }

    private static Long getDose(VacinaAplicacao vacinaAplicacao) {
        return vacinaAplicacao.getDose() != null ? vacinaAplicacao.getDose() : vacinaAplicacao.getVacinaCalendario().getDose();
    }

    private static String buildInconsistencyMessage(VacinaAplicacao vacinaAplicacao, EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();

        Calendario calendario = vacinaAplicacao.getEstrategiaOuVacinaCalendario();

        if (vacinaAplicacao.getVacinaCalendario() == null) {
            stringBuilder.append("Não é possível registrar o imunobiológico ");
            stringBuilder.append(vacinaAplicacao.getTipoVacina().getTipoEsusDescricao());
            stringBuilder.append(" sem calendário cadastrado").append(".");
        } else {
            stringBuilder.append(validationParam.getTipoFicha().descricao());
            stringBuilder.append(": ");
            stringBuilder.append("Não é possível registrar o imunobiológico ");
            stringBuilder.append(vacinaAplicacao.getTipoVacina().getTipoEsusDescricaoComCodigo());
            stringBuilder.append(" com a estratégia ");
            stringBuilder.append(calendario.getDescricaoEstrategiaEsus());
            stringBuilder.append(" e dose ");
            stringBuilder.append(vacinaAplicacao.getVacinaCalendario().getDosesDescricao());
            stringBuilder.append(".");
        }

        return stringBuilder.toString();
    }
}