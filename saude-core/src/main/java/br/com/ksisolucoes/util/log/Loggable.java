/*
 * Created on 27/09/2004
 *
 */
package br.com.ksisolucoes.util.log;

import org.apache.log4j.Logger;


/**
 * Interface a qual adiciona a funcionalidade a suas subclasses de serem logveis,
 * ou seja, apresentarem mensagem em determinadas sadas apropriadamente.
 *
 * <AUTHOR>
 */
public interface Loggable {
    
    /**
     * Objeto responsvel por gerenciar todas as atividades de log.
     */
    public Logger portal = Logger.getLogger( "portal" );
    public Logger vigilancia = Logger.getLogger( "vigilancia" );
    public Logger log = Logger.getLogger( "ksisolucoes" );
    public Logger request = Logger.getLogger( "request" );
    public Logger regulacao = Logger.getLogger( "regulacao" );

}
