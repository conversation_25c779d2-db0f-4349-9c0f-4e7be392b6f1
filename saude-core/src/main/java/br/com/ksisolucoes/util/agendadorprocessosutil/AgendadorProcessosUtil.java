package br.com.ksisolucoes.util.agendadorprocessosutil;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.vo.basico.AgendadorProcessoUsuarios;
import br.com.ksisolucoes.vo.controle.Usuario;
import ch.lambdaj.Lambda;

import java.util.List;

/**
 * Created by roger on 02/08/16.
 */
public class AgendadorProcessosUtil {

    public static List<Usuario> getAgendadorProcessoUsuarios(Long codigoAgendadorProcesso) {
        List<AgendadorProcessoUsuarios> agendadorProcessoUsuariosList = LoadManager.getInstance(AgendadorProcessoUsuarios.class)
                .addParameter(new QueryCustom.QueryCustomParameter(AgendadorProcessoUsuarios.PROP_AGENDADOR_PROCESSO, codigoAgendadorProcesso))
                .start().getList();
        return Lambda.extract(agendadorProcessoUsuariosList, Lambda.on(AgendadorProcessoUsuarios.class).getUsuario());
    }

}
