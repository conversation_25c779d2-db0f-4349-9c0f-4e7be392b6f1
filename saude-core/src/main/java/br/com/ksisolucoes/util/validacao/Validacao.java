package br.com.ksisolucoes.util.validacao;


import java.util.List;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.bo.BusinessObjectConstants;
import br.com.ksisolucoes.util.financeiro.FinanceiroPadraoInterface;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Pessoa;

public class Validacao {

    /**
     * Verifica se j existe o CPF/CNPJ da pessoa a cadastrar no banco.
     * Retorna uma exception ValidacaoException caso o cpf ou cnpj j estiver cadastrado ou caso ocorra algum erro com a query.
     *
     * @throws ValidacaoException
     * @throws DAOException
     */
    public static void validacaoCpfCnpjExistente(Pessoa pessoa) throws SGKException {
        if( FinanceiroPadraoInterface.NAO.equals( CargaBasicoPadrao.getInstance().getParametroPadrao().getValidaCpfCnpj() ) ) {
            return;
        }
        
        if( pessoa.isPessoaExportacao() && pessoa.getCnpjCpf().equals("") ) {
            return;
        }
        
//        Filtro filtro = new Filtro(Pessoa.class);
//        filtro.addIgual(Pessoa.PROP_CNPJ_CPF, pessoa.getCnpjCpf());
//        filtro.isNotNull(Pessoa.PROP_CNPJ_CPF);
//        filtro.setProjection(Projections.projectionList()
//        .add(Projections.property(Pessoa.PROP_CODIGO))
//        .add(Projections.property(Pessoa.PROP_DESCRICAO)));
        
        List<Pessoa> pessoaTeste = LoadManager.getInstance(Pessoa.class)
                .addProperties(Pessoa.PROP_CODIGO, Pessoa.PROP_DESCRICAO)
                .addParameter(new QueryCustomParameter(Pessoa.PROP_CNPJ_CPF, pessoa.getCnpjCpf()))
                .addParameter(new QueryCustomParameter(Pessoa.PROP_CNPJ_CPF, QueryParameter.DIFERENTE,"",HQLHelper.RESOLVE_CHAR_TYPE,""))
                .start().getList();
        
        if(CollectionUtils.isNotNullEmpty(pessoaTeste)) {
            for (Pessoa p : pessoaTeste) {
                if (! Coalesce.asString(pessoa.getCnpjCpf()).trim().equals("")) {
                    if (pessoa.getCodigo() == null || !pessoa.getCodigo().equals(p.getCodigo())) {
                        Long codigo = p.getCodigo();
                        String descricao = p.getDescricao();

                        StringBuffer msgDetalhe = new StringBuffer();
                        msgDetalhe.append(Bundle.getStringBO(BusinessObjectConstants.MENSAGEM_CPF_CNPJ_CADASTRADO));
                        msgDetalhe.append(Bundle.getStringApplication("rotulo_codigo") + ": " + codigo + ".");
                        msgDetalhe.append(Bundle.getStringApplication("rotulo_descricao") + ": " + descricao + ".");
                        msgDetalhe.append(Bundle.getStringApplication("rotulo_cpf_cnpj") + ": " + pessoa.getCnpjCpf() + ".");

                        if (pessoa.getCodigo() == null || !pessoa.getCodigo().equals(codigo)) {
                            throw new ValidacaoException(Bundle.getStringApplication("msg_cpf_cnpj_ja_cadastrado_X_X",codigo,descricao));
                            //throw new ValidacaoException(new RetornoValidacao().add(Bundle.getStringBO(BusinessObjectConstants.MENSAGEM_CPF_CNPJ_CADASTRADO), msgDetalhe.toString()));
                        }
                    }
                }
            }
        }
    }

    /**
     * Funo para verificar se o CPF  vlido.
     *
     * @param cpf
     * @return boolean indicando se o cpf  vlido.
     */
    public static boolean CPFIsValid(String cpf){
        if( FinanceiroPadraoInterface.NAO.equals( CargaBasicoPadrao.getInstance().getParametroPadrao().getValidaCpfCnpj() ) ) {
            return true;
        }

        if(cpf.length() == 11){
            int[] num=new int[11];
            num[0] = Integer.parseInt(cpf.substring(0,1));
            num[1] = Integer.parseInt(cpf.substring(1,2));
            num[2] = Integer.parseInt(cpf.substring(2,3));
            num[3] = Integer.parseInt(cpf.substring(3,4));
            num[4] = Integer.parseInt(cpf.substring(4,5));
            num[5] = Integer.parseInt(cpf.substring(5,6));
            num[6] = Integer.parseInt(cpf.substring(6,7));
            num[7] = Integer.parseInt(cpf.substring(7,8));
            num[8] = Integer.parseInt(cpf.substring(8,9));
            num[9] = Integer.parseInt(cpf.substring(9,10));
            num[10] = Integer.parseInt(cpf.substring(10,11));
            /**Verificar primeiro digito*/
            int soma = (num[0]*10)+(num[1]*9)+(num[2]*8)+(num[3]*7)+(num[4]*6)+(num[5]*5)+(num[6]*4)+(num[7]*3)+(num[8]*2);
            int div = soma/11;
            int valor = div * 11;
            int result = soma - valor;
            int dig1;
            if(result == 1 || result == 0){
                dig1 = 0;
            }else{
                dig1 = 11 - result;
            }
            if(dig1 == num[9]){
                /**Verificar segundo digito*/
                soma = (num[0]*11)+(num[1]*10)+(num[2]*9)+(num[3]*8)+(num[4]*7)+(num[5]*6)+(num[6]*5)+(num[7]*4)+(num[8]*3)+(num[9]*2);
                div = soma / 11;
                valor = div * 11;
                result = soma - valor;
                int dig2;
                if(result == 1 || result == 0){
                    dig2 = 0;
                }else{
                    dig2 = 11 - result;
                }
                if(dig2 == num[10]){
                    return true;
                }else{
                    return false;
                }
            }else{
                return false;
            }
        }else{
            return false;
        }
    }

    /**
     * Funo para verificar se o CNPJ/CGC  vlido.
     *
     * @param cnpj
     * @return boolean indicando se o CNPJ/CGC  vlido.
     */
    public static boolean CNPJIsValid(String cnpj){
        if( FinanceiroPadraoInterface.NAO.equals( CargaBasicoPadrao.getInstance().getParametroPadrao().getValidaCpfCnpj() ) ) {
            return true;
        }

        if(cnpj.length() == 14){
            int[] num=new int[14];
            num[0] = Integer.parseInt(cnpj.substring(0,1));
            num[1] = Integer.parseInt(cnpj.substring(1,2));
            num[2] = Integer.parseInt(cnpj.substring(2,3));
            num[3] = Integer.parseInt(cnpj.substring(3,4));
            num[4] = Integer.parseInt(cnpj.substring(4,5));
            num[5] = Integer.parseInt(cnpj.substring(5,6));
            num[6] = Integer.parseInt(cnpj.substring(6,7));
            num[7] = Integer.parseInt(cnpj.substring(7,8));
            num[8] = Integer.parseInt(cnpj.substring(8,9));
            num[9] = Integer.parseInt(cnpj.substring(9,10));
            num[10] = Integer.parseInt(cnpj.substring(10,11));
            num[11] = Integer.parseInt(cnpj.substring(11,12));
            num[12] = Integer.parseInt(cnpj.substring(12,13));
            num[13] = Integer.parseInt(cnpj.substring(13,14));

            /**Verifica o primerio digito*/
            int soma = (num[0]*5)+(num[1]*4)+(num[2]*3)+(num[3]*2)+(num[4]*9)+(num[5]*8)+(num[6]*7)+(num[7]*6)+(num[8]*5)+(num[9]*4)+(num[10]*3)+(num[11]*2);
            int div = soma/11;
            int valor = div * 11;
            int result = soma - valor;
            int dig1;
            if(result == 0 || result == 1){
                dig1 = 0;
            }else{
                dig1 = 11 - result;
            }
            if(dig1 == num[12]){
                soma = (num[0]*6)+(num[1]*5)+(num[2]*4)+(num[3]*3)+(num[4]*2)+(num[5]*9)+(num[6]*8)+(num[7]*7)+(num[8]*6)+(num[9]*5)+(num[10]*4)+(num[11]*3)+(num[12]*2);
                div = soma/11;
                valor = div * 11;
                result = soma - valor;
                int dig2;
                if(result == 0 || result == 1){
                    dig2 = 0;
                }else{
                    dig2 = 11 - result;
                }
                if(dig2 == num[13]){
                    return true;
                }else{
                    return false;
                }
            }else{
                return false;
            }
        }else{
            return false;
        }
    }

}
