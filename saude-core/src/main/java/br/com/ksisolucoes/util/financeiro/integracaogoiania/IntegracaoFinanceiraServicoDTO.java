package br.com.ksisolucoes.util.financeiro.integracaogoiania;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

@XmlRootElement(name = "servico")
@XmlType(propOrder = { "numero", "cliente", "datahora", "hash"})
public class IntegracaoFinanceiraServicoDTO  implements Serializable {

    private String numero;
    private String cliente;
    private String datahora;
    private String hash;

    @XmlElement(name = "numero")
    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    @XmlElement(name = "cliente")
    public String getCliente() {
        return cliente;
    }

    public void setCliente(String cliente) {
        this.cliente = cliente;
    }

    @XmlElement(name = "datahora")
    public String getDatahora() {
        return datahora;
    }

    public void setDatahora(String datahora) {
        this.datahora = datahora;
    }

    @XmlElement(name = "hash")
    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }
}
