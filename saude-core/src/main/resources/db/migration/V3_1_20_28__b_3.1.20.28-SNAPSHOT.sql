SET application_name = 'flyway|3.1.20.28';

/*
    Laudecir - 04/03/2019
*/
alter table usuarios add column public_key_certificate varchar;
alter table auditschema.usuarios add column public_key_certificate varchar;

create unique index idx_chave_publica_certificado_usuario on usuarios (public_key_certificate);

/*
    Elton #25481
*/

INSERT INTO agendador_processo (cd_agendador_processo, cd_processo, nm_servico, ds_servico, status, tp_processo, version)
VALUES (59, 59, 'Consultar Unidades', 'Executa a consulta dos dados das empresas/unidades na base de dados do terceiro, envia arquivo de retorno via mensagem interna ao usuários configurados', 0, 18, 0);

INSERT INTO agendador_processo (cd_agendador_processo, cd_processo, nm_servico, ds_servico, status, tp_processo, version)
VALUES (60, 60, 'Consultar Produtos', 'Executa a consulta dos dados das produtos na base de dados do terceiro, envia arquivo de retorno via mensagem interna ao usuários configurados', 0, 18, 0);

INSERT INTO agendador_processo (cd_agendador_processo, cd_processo, nm_servico, ds_servico, status, tp_processo, version)
VALUES (61, 61, 'Sincronizar Itens Atendidos', 'Sincroniza os itens que foram atendidos da solicitação enviada, dando entrada no estoque dos itens recebidos.', 0, 18, 0);

-- EMPRESA --
alter table empresa add column id_integracao_terceiro INT8;
alter table auditschema.empresa add column id_integracao_terceiro INT8;

alter table empresa add column nm_integracao_terceiro varchar;
alter table auditschema.empresa add column nm_integracao_terceiro varchar;

-- PRODUTO --
alter table produtos add column id_integracao_terceiro INT8;
alter table auditschema.produtos add column id_integracao_terceiro INT8;

alter table produtos add column nm_integracao_terceiro varchar;
alter table auditschema.produtos add column nm_integracao_terceiro varchar;

-- TABELA: integracao_prof_branet --
create sequence seq_integracao_prof_branet;

create table integracao_prof_branet (
    cd_integracao_prof_branet     INT8              not null,
    cd_profissional               INT4              not null,
    id                            INT8              not null,
    id_responsavel                INT8              not null,
    json                          TEXT              null,
    mensagem                      TEXT              null,
    data                          timestamp         null,
    status                        TEXT              null,
    tipo_log                      TEXT              null,
    resposta                      TEXT              null,
    cd_usuario                    NUMERIC(6)        not null,
    dt_cadastro                   timestamp         not null,
    version                       INT8              not null
);
alter table integracao_prof_branet add constraint PK_INT_PROF_BRANET primary key (cd_integracao_prof_branet);

alter table integracao_prof_branet add constraint FK_INT_PROF_BRANET_REF_PROF foreign key (cd_profissional) references profissional (cd_profissional) on delete restrict on update restrict;

alter table integracao_prof_branet add constraint FK_INT_PROF_BRANET_REF_USUARIO foreign key (cd_usuario) references usuarios (cd_usuario) on delete restrict on update restrict;

CREATE TABLE auditschema.integracao_prof_branet AS SELECT t2.*, t1.* FROM integracao_prof_branet t1, audit_temp t2 WHERE 1=2;
create sequence seq_audit_id_integracao_prof_branet;
alter table auditschema.integracao_prof_branet add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON integracao_prof_branet FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

-- TABELA: integracao_pedido_branet --
create sequence seq_integracao_pedido_branet;

create table integracao_pedido_branet (
    cd_integracao_pedido_branet   INT8              not null,
    pedido                        numeric(8)        not null,
    id                            INT8              not null,
    id_responsavel                INT8              not null,
    recebido                      INT2              null,
    json                          TEXT              null,
    mensagem                      TEXT              null,
    data                          timestamp         null,
    status                        TEXT              null,
    tipo_log                      TEXT              null,
    resposta                      TEXT              null,
    cd_usuario                    NUMERIC(6)        not null,
    dt_cadastro                   timestamp         not null,
    version                       INT8              not null
);
alter table integracao_pedido_branet add constraint PK_INT_PEDIDO_BRANET primary key (cd_integracao_pedido_branet);

alter table integracao_pedido_branet add constraint FK_INT_PED_BRANET_REF_PED_TRANSF foreign key (pedido) references pedido_transferencia (pedido) on delete restrict on update restrict;

alter table integracao_pedido_branet add constraint FK_INT_PED_BRANET_REF_USUARIO foreign key (cd_usuario) references usuarios (cd_usuario) on delete restrict on update restrict;

CREATE TABLE auditschema.integracao_pedido_branet AS SELECT t2.*, t1.* FROM integracao_pedido_branet t1, audit_temp t2 WHERE 1=2;
CREATE sequence seq_audit_id_integracao_pedido_branet;
ALTER TABLE auditschema.integracao_pedido_branet ADD PRIMARY KEY (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON integracao_pedido_branet FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

-- TABELA: integracao_recebimento_branet --
CREATE sequence seq_integracao_recebimento_branet;

CREATE TABLE integracao_recebimento_branet (
    cd_integracao_recebimento_branet    INT8              not null,
    id_solicitacao			     		INT8              not null,
    json                          		TEXT              null,
    mensagem                      		TEXT              null,
    data                          		timestamp         null,
    status                        		TEXT              null,
    resposta                      		TEXT              null,
    cd_usuario                    		NUMERIC(6)        not null,
    dt_cadastro                   		timestamp         not null,
    version                       		INT8              not null
);

ALTER TABLE integracao_recebimento_branet ADD CONSTRAINT PK_INT_RECEB_BRANET PRIMARY KEY (cd_integracao_recebimento_branet);

ALTER TABLE integracao_recebimento_branet ADD CONSTRAINT FK_INT_RECEB_REF_USUARIO FOREIGN KEY (cd_usuario) REFERENCES usuarios (cd_usuario) ON DELETE RESTRICT ON UPDATE RESTRICT;

CREATE TABLE auditschema.integracao_recebimento_branet AS SELECT t2.*, t1.* FROM integracao_recebimento_branet t1, audit_temp t2 WHERE 1=2;
CREATE sequence seq_audit_id_integracao_recebimento_branet;
ALTER TABLE auditschema.integracao_recebimento_branet ADD PRIMARY KEY (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON integracao_recebimento_branet FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();