SET application_name = 'flyway|3.1.45';

SET statement_timeout TO 600000;

DO
$do$
BEGIN
IF (SELECT to_regclass('public.solicitacao_agendamento_posicao_fila')) IS NULL THEN

    /*
        Sulivan - 15/01/2020
    */
    alter table solicitacao_agendamento add column flag_calcular_posicao INT2;
    alter table auditschema.solicitacao_agendamento add column flag_calcular_posicao INT2;

    alter table agendamento_lote_processo add column dt_finalizacao timestamp;
    alter table auditschema.agendamento_lote_processo add column dt_finalizacao timestamp;

    alter table agendamento_lote_processo add column qtd_agendada INT8;
    alter table auditschema.agendamento_lote_processo add column qtd_agendada INT8;

    alter table agendamento_lote_processo add column qtd_erro INT8;
    alter table auditschema.agendamento_lote_processo add column qtd_erro INT8;

    alter table agendamento_lote_processo add column solicitacoes_agendadas TEXT;
    alter table auditschema.agendamento_lote_processo add column solicitacoes_agendadas TEXT;

      /*
     *  Kegan - 01/04/2020 - ENG-159
     */

	DROP TABLE IF EXISTS public.solicitacao_agendamento_posicao_fila;
	CREATE TABLE public.solicitacao_agendamento_posicao_fila AS
	SELECT
	    solicitacao_agendamento.cd_solicitacao AS cd_solicitacao,
	    solicitacao_agendamento.posicao_fila_espera AS posicao_fila_espera
	FROM solicitacao_agendamento;

	CREATE INDEX idx_solic_agend_teste ON public.solicitacao_agendamento (status, cd_tp_procedimento, tp_fila);
	CREATE INDEX idx_solic_agend_posic_fila_cd_solicitacao ON public.solicitacao_agendamento_posicao_fila (cd_solicitacao, posicao_fila_espera);

	ALTER TABLE public.solicitacao_agendamento_posicao_fila DROP CONSTRAINT IF EXISTS fk_solic_agend_posic_fila_ref_sol_agend;
	ALTER TABLE public.solicitacao_agendamento_posicao_fila
	    ADD CONSTRAINT fk_solic_agend_posic_fila_ref_sol_agend FOREIGN KEY (cd_solicitacao) REFERENCES public.solicitacao_agendamento(cd_solicitacao);

	CREATE TABLE AUDITSCHEMA.solicitacao_agendamento_posicao_fila AS SELECT T2.*, T1.* FROM solicitacao_agendamento_posicao_fila T1, AUDIT_TEMP T2 WHERE 1 = 2;
	CREATE sequence SEQ_AUDIT_ID_solicitacao_agendamento_posicao_fila;
	ALTER TABLE AUDITSCHEMA.solicitacao_agendamento_posicao_fila ADD PRIMARY KEY(AUDIT_ID);
	CREATE trigger EMP_AUDIT after INSERT OR UPDATE OR DELETE ON solicitacao_agendamento_posicao_fila FOR each row execute procedure PROCESS_EMP_AUDIT();


    /*
        PostgreSQL
        Rafael Ramos - 09/06/2020 - #REG-178
    */

    /*==============================================================*/
    /* Índices para otimizar consulta de agendamento em lote */
    /*==============================================================*/
    create index idx_solicitacao_agendamento_usu_cadsus on solicitacao_agendamento (cd_usu_cadsus);
    create index idx_solicitacao_agendamento_status on solicitacao_agendamento (status);
    create index idx_solicitacao_agendamento_flag_bloqueado on solicitacao_agendamento (flag_bloqueado);
    create index idx_solicitacao_agendamento_tp_consulta on solicitacao_agendamento (tp_consulta);
    create index idx_solicitacao_agendamento_tp_fila on solicitacao_agendamento (tp_fila);

    create index idx_solicitacao_agendamento_cd_profissional on solicitacao_agendamento (cd_profissional);
    create index idx_solicitacao_agendamento_cd_profissional_desejado on solicitacao_agendamento (cd_profissional_desejado);
    create index idx_solicitacao_agendamento_classificacao_risco on solicitacao_agendamento (classificacao_risco);
    create index idx_solicitacao_agendamento_cd_tp_procedimento on solicitacao_agendamento (cd_tp_procedimento);
    create index idx_solicitacao_agendamento_unidade on solicitacao_agendamento (unidade);
    create index idx_solicitacao_agendamento_cd_procedimento on solicitacao_agendamento (cd_procedimento);
    create index idx_solicitacao_agendamento_cd_solicitacao on solicitacao_agendamento (cd_solicitacao);

    create index idx_profissional_cd_profissional on profissional (cd_profissional);
    create index idx_classificacao_risco_cd_classificacao_risco on classificacao_risco (cd_classificacao_risco);
    create index idx_tipo_procedimento_cd_tp_procedimento on tipo_procedimento (cd_tp_procedimento);
    create index idx_empresa_empresa on empresa (empresa);
    create index idx_procedimento_cd_procedimento on procedimento (cd_procedimento);
    create index idx_solicitacao_agendamento_posicao_fila on solicitacao_agendamento_posicao_fila (cd_solicitacao);
    create index idx_solicitacao_agendamento_posicao_fila_espera on solicitacao_agendamento_posicao_fila (posicao_fila_espera);

    /*=====================================================================================================*/
    /*   task:  REG-190 - Permissão para visualizar agendamento de outra unidades                          */
    /*   autor: Rafael.Dantas                                                                              */
    /*   data:  22/06/2020                                                                                 */
    /*=====================================================================================================*/

    ALTER TABLE empresa ADD COLUMN flag_visualiza_agenda_outras_unidades INT2 NULL DEFAULT 1;
    ALTER TABLE auditschema.empresa ADD COLUMN flag_visualiza_agenda_outras_unidades INT2 NULL DEFAULT 1;

    /*
        Carolina Pottmeier - 24/07/2020 - REG-254
    */


    alter table sms_mensagem alter column mensagem type varchar(200);
    alter table auditschema.sms_mensagem alter column mensagem type varchar(200);


    /*=====================================================================================================*/
    /*   task:  REG-234 - Incluir coluna "Forma de Agendamento" na tabela de agenda atendimento horario    */
    /*   autor: Rafael.Dantas                                                                              */
    /*   data:  16/07/2020                                                                                 */
    /*=====================================================================================================*/

    ALTER TABLE agenda_gra_ate_horario ADD COLUMN flag_forma_agendamento VARCHAR(1) NULL;
    ALTER TABLE auditschema.agenda_gra_ate_horario ADD COLUMN flag_forma_agendamento VARCHAR(1) NULL;


    /*==============================================================*/
    /*   TABLE: TIPO_PROCEDIMENTO                                   */
    /*   autor: Eduardo Ximendes                                    */
    /*   tarefa: REG-257                                            */
    /*==============================================================*/
    ALTER TABLE tipo_procedimento ADD COLUMN quebra_solicitacao char not null default 'N';
    ALTER TABLE auditschema.tipo_procedimento ADD COLUMN quebra_solicitacao char not  null default 'N';


    /*==============================================================*/
    /*   TABLE: PARAMETRO_GEM                                       */
    /*   autor: Eduardo Ximendes                                    */
    /*   tarefa: REG-256                                            */
    /*==============================================================*/
    delete from parametro_gem where parametro = 'SeparaSolicitacaoAgendaAutomatico';


    /* ENG-526 - RICARDO */

    CREATE EXTENSION IF NOT EXISTS PG_TRGM;

    DO $BLOCK$
    BEGIN
        BEGIN
            CREATE INDEX TRGM_IDX_DOM_USUARIO_CADSUS_REF_NM_REFERENCIA ON DOM_USUARIO_CADSUS USING GIN (NOME_REFERENCIA GIN_TRGM_OPS);
        EXCEPTION
            WHEN DUPLICATE_TABLE
            THEN RAISE NOTICE 'INDEX ''TRGM_IDX_DOM_USUARIO_CADSUS_REF_NM_REFERENCIA '' ON DOM_USUARIO_CADSUS ALREADY EXISTS, SKIPPING';
        END;
    END;
    $BLOCK$;

    DO $BLOCK$
    BEGIN
        BEGIN
            CREATE INDEX TRGM_IDX_DOM_USUARIO_CADSUS_REF_NM_MAE ON DOM_USUARIO_CADSUS USING GIN (NOME_MAE GIN_TRGM_OPS);
        EXCEPTION
            WHEN DUPLICATE_TABLE
            THEN RAISE NOTICE 'INDEX ''TRGM_IDX_DOM_USUARIO_CADSUS_REF_NM_MAE '' ON DOM_USUARIO_CADSUS ALREADY EXISTS, SKIPPING';
        END;
    END;
    $BLOCK$;


    DO $BLOCK$
    BEGIN
        BEGIN
            CREATE INDEX TRGM_IDX_DOM_USUARIO_CADSUS_REF_KEY_WORD ON DOM_USUARIO_CADSUS USING GIN (KEY_WORD GIN_TRGM_OPS);
        EXCEPTION
            WHEN DUPLICATE_TABLE
            THEN RAISE NOTICE 'INDEX ''TRGM_IDX_DOM_USUARIO_CADSUS_REF_KEY_WORD '' ON DOM_USUARIO_CADSUS ALREADY EXISTS, SKIPPING';
        END;
    END;
    $BLOCK$;

    /*=======================================================================================================*/
    /*   task:  REG-231 - Execução da rotina para cancelar pacientes que não comparecerem aos agendamentos   */
    /*   autor: Rafael.Dantas                                                                                */
    /*   data:  20/07/2020                                                                                   */
    /*=======================================================================================================*/

    INSERT INTO agendador_processo (cd_agendador_processo, cd_processo, nm_servico, ds_servico, status, tp_processo, version)
    VALUES (71, 71, 'Cancelar agendamentos em aberto e vencidos',
     'Executa a rotina de cancelamento automatizado para os pacientes que não comparecerem aos agendamentos.' ||
     chr(13) || 'ATENÇÃO: Não configurar esse processo para executar em horário de expediente.', 0, 4, 0);

     /*=======================================================================================================*/
     /*   task:  REG-259 - Sanear o legado quanto as unidades executantes na tabela de exames                 */
     /*   autor: Rafael.Dantas                                                                                */
     /*   data:  31/07/2020                                                                                   */
     /*=======================================================================================================*/

     WITH unidades AS (
        SELECT e.cd_exame, agah.local_agendamento
        FROM exame e
        INNER JOIN exame_requisicao er ON e.cd_exame = er.cd_exame
        INNER JOIN agenda_gra_ate_horario agah ON er.cd_solicitacao = agah.cd_solicitacao
        WHERE e.local_exame IS NULL
     )
     UPDATE exame SET local_exame = unidades.local_agendamento
     FROM unidades
     WHERE unidades.cd_exame = exame.cd_exame;


    /*=======================================================================================================*/
    /*   task:  REG-209 - Adicionar a Origem da Solicitação nos agendamentos em lote                         */
    /*   autor: Rafael.Dantas                                                                                */
    /*   data:  06/08/2020                                                                                   */
    /*=======================================================================================================*/

    ALTER TABLE agendamento_lote_processo ADD COLUMN cd_origem_solicitacao INT4;

    ALTER TABLE agendamento_lote_processo
       ADD CONSTRAINT FK_AG_LOTE_PROCESSO_REF_EMPRESA foreign key (cd_origem_solicitacao)
       REFERENCES empresa (empresa);

    ALTER TABLE auditschema.agendamento_lote_processo ADD COLUMN cd_origem_solicitacao INT4;

    /*=====================================================================================================*/
    /*   task:  REG-267 - Reprocessamento das filas na solicitação de agendamento                          */
    /*   autor: Rafael.Dantas                                                                              */
    /*   data:  29/07/2020                                                                                 */
    /*=====================================================================================================*/

    INSERT INTO programa_pagina VALUES (1838, 'br.com.celk.view.controle.reprocessarfilassolicitacaoagendamento.ReprocessarFilasSolicitacaoAgendamentoPage', 'N');
    INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (1012, 'Reprocessar filas - Solicitações agendamento', 1838, 'N');
    INSERT INTO programa_web_pagina VALUES (1930, 1012, 1838);
    INSERT INTO painel_controle_programa_web VALUES (13, 'reprocessarFilasSolicitacaoAgendamento', 1012, 'M', 0);

END IF;
END
$do$;
