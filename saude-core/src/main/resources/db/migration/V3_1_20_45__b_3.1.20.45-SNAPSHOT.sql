SET application_name = 'flyway|3.1.20.45';

/*
    Silvio - 04/09/2019 #27452
*/
create table tecidos_moles (
    cd_tecidos_moles 	int8 		not null,
    descricao	        TEXT 		not null,
    dt_cadastro      	timestamp,
    version             int8 		not null
);
alter table tecidos_moles add constraint PK_TECIDOS_MOLES PRIMARY key (cd_tecidos_moles);

CREATE TABLE auditschema.tecidos_moles AS SELECT t2.*, t1.* FROM tecidos_moles t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tecidos_moles;alter table auditschema.tecidos_moles add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tecidos_moles FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

INSERT INTO programa_pagina VALUES (1740, 'br.com.celk.view.unidadesaude.tecidosmoles.ConsultaTecidosMolesPage', 'N');
INSERT INTO programa_pagina VALUES (1741, 'br.com.celk.view.unidadesaude.tecidosmoles.CadastroTecidosMolesPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (966, 'Tecidos Moles', 1740, 'N');
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1216,'Tecidos Moles','tecidosMoles',324,966,143,0,0);
INSERT INTO programa_web_pagina VALUES (1858, 966, 1740);
INSERT INTO programa_web_pagina VALUES (1859, 966, 1741);

ALTER TABLE atendimento_odonto_plano ADD COLUMN cd_tecidos_moles INT8;
ALTER TABLE auditschema.atendimento_odonto_plano ADD COLUMN cd_tecidos_moles INT8;

ALTER TABLE atendimento_odonto_plano
    ADD CONSTRAINT FK_ATEN_ODON_PLA_REF_TEC_MOL FOREIGN KEY (cd_tecidos_moles)
        REFERENCES tecidos_moles (cd_tecidos_moles)
        on delete restrict on update restrict;
