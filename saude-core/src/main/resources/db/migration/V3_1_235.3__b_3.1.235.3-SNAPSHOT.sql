SET application_name = 'flyway|3.1.235.3';

SET statement_timeout TO 600000;

/*
<PERSON><PERSON><PERSON> V<PERSON> 21/05/24
*/

alter table dispensacao_medicamento add column if not exists cd_cid bpchar(8);
alter table dispensacao_medicamento ADD constraint fk_cid foreign key (cd_cid) references cid(cd_cid);

alter table auditschema.dispensacao_medicamento add column if not exists cd_cid bpchar(8);

alter table bnafar_dispensacao add column if not exists cd_cid bpchar(8);
alter table auditschema.bnafar_dispensacao add column if not exists cd_cid bpchar(8);


--LUIS SEIDEL | HOTFIX/VSA-2293

--licenca transporte
alter table public.configuracao_vigilancia rename column atividade_estabelecimento_data_vencimento to flag_licenca_transporte_usa_dt_vencimento_atividade_estab;
alter table auditschema.configuracao_vigilancia rename column atividade_estabelecimento_data_vencimento to flag_licenca_transporte_usa_dt_vencimento_atividade_estab;


--alvara inicial
alter table public.configuracao_vigilancia add column if not exists flag_alvara_inicial_usa_dt_vencimento_atividade_estab int2 null;
alter table auditschema.configuracao_vigilancia add column if not exists flag_alvara_inicial_usa_dt_vencimento_atividade_estab int2 null;

update public.configuracao_vigilancia set flag_alvara_inicial_usa_dt_vencimento_atividade_estab = 0;


--Revalidacao de alvara
alter table public.configuracao_vigilancia add column if not exists flag_revalidacao_alvara_usa_dt_vencimento_atividade_estab int2 null;
alter table auditschema.configuracao_vigilancia add column if not exists flag_revalidacao_alvara_usa_dt_vencimento_atividade_estab int2 null;

update public.configuracao_vigilancia set flag_revalidacao_alvara_usa_dt_vencimento_atividade_estab = 0;

alter table public.configuracao_vigilancia add column if not exists flag_alvara_revalidacao_usa_data_fixa int2 null;
alter table auditschema.configuracao_vigilancia add column if not exists flag_alvara_revalidacao_usa_data_fixa int2 null;

update public.configuracao_vigilancia set flag_alvara_revalidacao_usa_data_fixa = validade_alvara_data_fixa;

alter table public.configuracao_vigilancia add column if not exists tipo_data_calculo_revalidacao_alvara int2 null;
alter table auditschema.configuracao_vigilancia add column if not exists tipo_data_calculo_revalidacao_alvara int2 null;

update public.configuracao_vigilancia set tipo_data_calculo_revalidacao_alvara = tp_database_calc_alvara;

alter table public.configuracao_vigilancia add column if not exists periodo_validade_revalidacao_alvara int2 null;
alter table auditschema.configuracao_vigilancia add column if not exists periodo_validade_revalidacao_alvara int2 null;

update public.configuracao_vigilancia set periodo_validade_revalidacao_alvara = validade_alvara_periodo;

alter table public.configuracao_vigilancia add column if not exists data_vencimento_revalidacao_alvara date null;
alter table auditschema.configuracao_vigilancia add column if not exists data_vencimento_revalidacao_alvara date null;

update public.configuracao_vigilancia set data_vencimento_revalidacao_alvara = validade_alvara_data_vencimento;

alter table public.configuracao_vigilancia add column if not exists ano_base_revalidacao_alvara int4 null;
alter table auditschema.configuracao_vigilancia add column if not exists ano_base_revalidacao_alvara int4 null;

update public.configuracao_vigilancia set ano_base_revalidacao_alvara = ano_base_alvara;


--autorizacao sanitaria
alter table public.configuracao_vigilancia add column if not exists flag_autorizacao_sanitaria_usa_data_fixa int2 null;
alter table auditschema.configuracao_vigilancia add column if not exists flag_autorizacao_sanitaria_usa_data_fixa int2 null;

update public.configuracao_vigilancia set flag_autorizacao_sanitaria_usa_data_fixa = validade_alvara_data_fixa;

alter table public.configuracao_vigilancia add column if not exists tipo_data_calculo_autorizacao_sanitaria int2 null;
alter table auditschema.configuracao_vigilancia add column if not exists tipo_data_calculo_autorizacao_sanitaria int2 null;

update public.configuracao_vigilancia set tipo_data_calculo_autorizacao_sanitaria = tp_database_calc_alvara;

alter table public.configuracao_vigilancia add column if not exists periodo_validade_autorizacao_sanitaria int2 null;
alter table auditschema.configuracao_vigilancia add column if not exists periodo_validade_autorizacao_sanitaria int2 null;

update public.configuracao_vigilancia set periodo_validade_autorizacao_sanitaria = validade_alvara_periodo;

alter table public.configuracao_vigilancia add column if not exists data_vencimento_autorizacao_sanitaria date null;
alter table auditschema.configuracao_vigilancia add column if not exists data_vencimento_autorizacao_sanitaria date null;

update public.configuracao_vigilancia set data_vencimento_autorizacao_sanitaria = validade_alvara_data_vencimento;

alter table public.configuracao_vigilancia add column if not exists ano_base_autorizacao_sanitaria int4 null;
alter table auditschema.configuracao_vigilancia add column if not exists ano_base_autorizacao_sanitaria int4 null;

update public.configuracao_vigilancia set ano_base_autorizacao_sanitaria = ano_base_licen_transp;

--estabelecimento
alter table public.estabelecimento rename column dt_validade_ult_alvara to dt_validade_primeira_autorizacao_sanitaria;
alter table auditschema.estabelecimento rename column dt_validade_ult_alvara to dt_validade_primeira_autorizacao_sanitaria;

/* Everton - 06/06/2024 - REG-1805 */
alter table agenda_gra_ate_horario disable trigger emp_audit;
update agenda_gra_ate_horario t1
   set empresa_origem = t2.unidade
  FROM solicitacao_agendamento t2
  where t2.cd_solicitacao = t1.cd_solicitacao
    and t1.status in (1,2)
    and t1.empresa_origem <> t2.unidade
    and t1.dt_agendamento >= current_date;
alter table agenda_gra_ate_horario enable trigger emp_audit;

