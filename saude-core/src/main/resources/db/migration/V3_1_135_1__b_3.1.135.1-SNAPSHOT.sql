SET application_name = 'flyway|3.1.135.1';

SET statement_timeout TO 600000;


/*
    Everton - CSF-342 - 15/06/2022
*/
insert into esus_ficha_usuario_cadsus_domicilio (status, dt_cadastro, responsavel, version, cd_usu_cadsus_dom, prontuario, reside_desde, motivo_exclusao, cd_esus_ficha_endereco_domicilio_esus, num_cartao, renda_familiar, cd_esus_ficha_usuario_cadsus_domicilio, dt_nascimento, nm_usuario_cadsus)
with ficha_esus as (
  SELECT 
      cd_end_dom_esus, max(cd_esus_ficha_endereco_domicilio_esus) as cd_esus_ficha_endereco_domicilio_esus
    FROM 
      esus_ficha_endereco_domicilio_esus
   group by 1
)
SELECT 
  t4.status, current_timestamp, 'S' as responsavel, 0 as version, t4.cd_usu_cadsus_dom, t3.prontuario, t3.reside_desde, 
  null as motivo_exclusao, t1.cd_esus_ficha_endereco_domicilio_esus, 
  (SELECT cd_numero_cartao FROM usuario_cadsus_cns where cd_usu_cadsus = t3.cd_usu_cadsus and st_excluido = 0 order by dt_alteracao desc limit 1),
  t3.renda_familiar, nextval('seq_gem') as codigo, t3.dt_nascimento,  t3.nm_usuario 
FROM 
  ficha_esus t1, endereco_domicilio_esus t2, usuario_cadsus t3, usuario_cadsus_domicilio t4
where t2.cd_end_dom_esus = t1.cd_end_dom_esus
  and t3.cd_domicilio = t2.cd_domicilio
  and t3.cd_usu_cadsus = t4.cd_usu_cadsus
  and t4.status <> 3
  and t3.flag_responsavel_familiar = 1
  and not exists (select 1 from esus_ficha_usuario_cadsus_domicilio where cd_esus_ficha_endereco_domicilio_esus = t1.cd_esus_ficha_endereco_domicilio_esus);
