SET application_name = 'flyway|3.1.268';

SET statement_timeout TO 600000;

/*
    feature/AMB-4181 - <PERSON> | 28/11/2024
*/

CREATE TABLE public.atendimento_sepse (
    cd_atendimento_sepse int8 PRIMARY KEY,
    dt_avaliacao TIMESTAMP NULL,
    nr_atendimento int8 NOT NULL,
    cd_atendimento_primario int8 NULL,
    confirmacao_sepse int4 null,
    cd_profissional_confirmacao_sepse int8 null,
    droga_vasoativa_indicada int4 null,
    hora_avaliacao_final timestamp null,
    cd_profissional_avaliacao_final int8 null,
    hipotese_sepse int4 null,
    news_final int4 null,
    desfecho int4 null,
    tempo_decorrente varchar(20) null,
    status int2 null,
    version int4 NOT NULL,
    FOREIGN KEY (nr_atendimento) REFERENCES atendimento(nr_atendimento),
    FOREIGN KEY (cd_atendimento_primario) REFERENCES atendimento_primario(cd_atendimento_primario),
    FOREIGN KEY (cd_profissional_confirmacao_sepse) REFERENCES profissional(cd_profissional),
    FOREIGN KEY (cd_profissional_avaliacao_final) REFERENCES profissional(cd_profissional)
);

create sequence seq_atendimento_sepse;

CREATE TABLE auditschema.atendimento_sepse AS SELECT t2.*, t1.* FROM atendimento_sepse t1, audit_temp t2 WHERE 1=2;
create sequence seq_audit_id_atendimento_sepse;
alter table auditschema.atendimento_sepse add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_sepse FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

/*
    feature/AMB-4225 - Douglas Rampinelli | 16/12/2024
*/

ALTER TABLE receituario_item
ADD COLUMN foco_sepse SMALLINT;

ALTER TABLE auditschema.receituario_item
ADD COLUMN foco_sepse SMALLINT;

CREATE TABLE public.atendimento_sepse_item (
    cd_atendimento_sepse_item int8 PRIMARY KEY,
    cd_atendimento_sepse int8 not null,
    hora_reavaliacao timestamp NULL,
	cd_profissional_reavaliacao int8 null,
	situacao_reavaliacao int2 null,
	news_reavaliado int2 null,
	tempo_decorrente VARCHAR(20),
    version int4 NOT NULL,
    FOREIGN KEY (cd_atendimento_sepse) REFERENCES atendimento_sepse(cd_atendimento_sepse),
    FOREIGN KEY (cd_profissional_reavaliacao) REFERENCES profissional(cd_profissional)
);

create sequence seq_atendimento_sepse_item;

CREATE TABLE auditschema.atendimento_sepse_item AS SELECT t2.*, t1.* FROM atendimento_sepse_item t1, audit_temp t2 WHERE 1=2;
create sequence seq_audit_id_atendimento_sepse_item;
alter table auditschema.atendimento_sepse_item add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_sepse_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

/*
    feature/AMB-4275 - Douglas Rampinelli | 17/12/2024
*/

CREATE TABLE public.ocorrencia_atendimento_sepse (
    cd_ocorrencia_atendimento_sepse int8 PRIMARY KEY,
    dt_ocorrencia timestamp not null,
    cd_atendimento_sepse int8 not null,
	cd_profissional_ocorrencia int8 not null,
	empresa_unidade int8 not null,
	descricao VARCHAR(200) not null,
	tempo_decorrente VARCHAR(20) null,
    version int4 NOT NULL,
    FOREIGN KEY (cd_atendimento_sepse) REFERENCES atendimento_sepse(cd_atendimento_sepse),
    FOREIGN KEY (cd_profissional_ocorrencia) REFERENCES profissional(cd_profissional),
    FOREIGN KEY (empresa_unidade) REFERENCES empresa(empresa)
);

create sequence seq_ocorrencia_atendimento_sepse;

CREATE TABLE auditschema.ocorrencia_atendimento_sepse AS SELECT t2.*, t1.* FROM ocorrencia_atendimento_sepse t1, audit_temp t2 WHERE 1=2;
create sequence seq_audit_id_ocorrencia_atendimento_sepse;
alter table auditschema.ocorrencia_atendimento_sepse add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ocorrencia_atendimento_sepse FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

alter table receituario_item add column cd_atendimento_sepse int8;
alter table auditschema.receituario_item add column cd_atendimento_sepse int8;

ALTER TABLE receituario_item
  ADD CONSTRAINT FK_RECEITUARIO_ITEM_REF_ATENDIMENTO_SEPSE FOREIGN KEY (cd_atendimento_sepse)
  REFERENCES atendimento_sepse (cd_atendimento_sepse);

alter table exame_requisicao add column cd_atendimento_sepse int8;
alter table auditschema.exame_requisicao add column cd_atendimento_sepse int8;

ALTER TABLE exame_requisicao
  ADD CONSTRAINT FK_EXAME_REQUISICAO_REF_ATENDIMENTO_SEPSE FOREIGN KEY (cd_atendimento_sepse)
  REFERENCES atendimento_sepse (cd_atendimento_sepse);

/*
    03/02/2025 - Roni Alves -  VAC-790
*/

ALTER TABLE vac_aplicacao  ALTER COLUMN ds_vacina TYPE VARCHAR(200);
ALTER TABLE auditschema.vac_aplicacao  ALTER COLUMN ds_vacina TYPE VARCHAR(200);