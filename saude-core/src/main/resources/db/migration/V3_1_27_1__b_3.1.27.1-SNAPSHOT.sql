SET application_name = 'flyway|3.1.27.1';

SET statement_timeout TO 600000;

/*==============================================================*/
/*   ALTER TABLE SINTOMAS COVID19                               */
/*   autor: <PERSON>                                    */
/*   tarefa: AMB-680                                            */
/*==============================================================*/
ALTER TABLE sintomas_covid19 ADD COLUMN exibe_formulario_triagem int8 null;

/*==============================================================*/
/*   ALTER TABLE AUDITSCHEMA.SINTOMAS_COVID19                   */
/*   autor: <PERSON>                                    */
/*   tarefa: AMB-680                                            */
/*==============================================================*/
ALTER TABLE auditschema.sintomas_covid19 ADD COLUMN exibe_formulario_triagem int8 null;


/*==============================================================*/
/*   UPDATE TABLE SINTOMAS_COVID19                              */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-680                                            */
/*==============================================================*/
UPDATE sintomas_covid19 SET exibe_formulario_triagem = 1 WHERE cd_sintomas_covid19 IN (1, 2, 3, 5, 6, 8, 9, 23, 25, 26, 24);
UPDATE sintomas_covid19 SET exibe_formulario_triagem = 0 WHERE cd_sintomas_covid19 NOT IN (1, 2, 3, 5, 6, 8, 9, 23, 25, 26, 24);


/*==============================================================*/
/*   UPDATE TABLE morbidade_previa_covid19                      */
/*   autor: Carolina Pottmeier                                  */
/*   tarefa: AMB-680                                            */
/*==============================================================*/
update morbidade_previa_covid19 set descricao = 'Doenças renais crônicas em estágio avançado (graus 3, 4 e 5)' where cd_morbidade_previa_covid19 = 12;
update morbidade_previa_covid19 set descricao = 'Portador de doenças cromossômicas ou estado de fragilidade imunológica' where cd_morbidade_previa_covid19 = 15;