SET application_name = 'flyway|3.1.180';

SET statement_timeout TO 600000;

INSERT INTO public.ficha_investigacao_agravo(
    cd_ficha_investigacao_agravo, ordem, dt_cadastro, descricao, status, "version")
VALUES (47, 1, NOW(), 'ESQUISTOSSOMOSE', 1, 0);

CREATE TABLE public.investigacao_agr_esquistossomose(
    cd_invest_agr_esquistossomose int8 PRIMARY KEY NOT NULL,
    "version" int8 NOT NULL,
    flag_informacoes_complementares varchar(1) NOT NULL DEFAULT 'S'::character varying,
    cd_registro_agravo int8 NOT NULL REFERENCES public.registro_agravo(cd_registro_agravo),

--INVESTIGAÇÃO
    dt_investigacao date NULL,
    ocupacao_cbo varchar(10) NULL REFERENCES public.tabela_cbo(cd_cbo),

--DADOS LABORATORIO
    dt_coproscopia date NULL,
    analise_quantitativa int2 null,
    analise_qualitativa int2 null,
    outros int2 null,
    outros_exames_desc varchar(50) null,

--    TRATAMENTO
    fez_tratamento int2 null,
    dt_tratamento date NULL,
    motivo int2 null,

    resultado_1_amostra int2 null,
    resultado_2_amostra int2 null,
    resultado_3_amostra int2 null,

    dt_resultado_3_amostra date NULL,

--CASO AUTÓCTONE
    caso_autoctone int8 NULL,
    cd_cidade_infeccao int8 NULL REFERENCES public.cidade(cod_cid),
    cd_pais_infeccao int8 NULL REFERENCES public.nacionalidade(cd_pais),
    str_distrito_infeccao varchar(200) NULL,
    str_bairro_infeccao varchar(200) NULL,
    propriedade_rural varchar(50) NULL,
    colecao_hidrica varchar(50) NULL,
    doenca_relacionada_trabalho int2 null,

--CONCLUSAO
    forma_clinica int2 null,
    forma_clinica_desc varchar(50) NULL,
    evolucao_caso int2 null,
    dt_obito date null,

--OBS
    observacao varchar(5000) NULL,

--ENCERRAMENTO
    dt_encerramento date NULL,
    cd_usuario_encerramento int8 REFERENCES public.usuarios(cd_usuario)
);

CREATE SEQUENCE seq_investigacao_agr_esquistossomose INCREMENT 1 START 1;

CREATE TABLE auditschema.investigacao_agr_esquistossomose AS
    (SELECT t2.*, t1.* FROM investigacao_agr_esquistossomose t1, audit_temp t2 WHERE 1=2);

CREATE sequence seq_audit_id_investigacao_agr_esquistossomose;

ALTER TABLE auditschema.investigacao_agr_esquistossomose ADD PRIMARY KEY (audit_id);

CREATE trigger emp_audit AFTER INSERT OR UPDATE OR DELETE
    ON public.investigacao_agr_esquistossomose FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();


INSERT INTO public.ficha_investigacao_agravo(cd_ficha_investigacao_agravo, ordem, dt_cadastro, descricao, status, "version") VALUES (18, 1, NOW(), 'TUBERCULOSE', 1, 0);

CREATE TABLE public.investigacao_agr_tuberculose(
    cd_invest_agr_tuberculose int8 PRIMARY KEY NOT NULL,
    "version" int8 NOT NULL,
    flag_informacoes_complementares varchar(1) NOT NULL DEFAULT 'S'::character varying,
    cd_registro_agravo int8 NOT NULL REFERENCES public.registro_agravo(cd_registro_agravo),

--investigacao
    dt_investigacao date NULL,
    dt_inicio_tratamento_atual DATE NULL,
    ocupacao_cbo varchar(10) NULL REFERENCES public.tabela_cbo(cd_cbo),

--observacao
    observacao text NULL,

--encerramento
    dt_encerramento date NULL,
    cd_usuario_encerramento int8 NULL,

--dados complementares
    tipo_entrada int2 NULL,

    populacao_privada_liberdade int2 NULL,
    populacao_profissional_saude int2 NULL,
    populacao_situacao_rua int2 NULL,
    populacao_imigrante int2 NULL,

    beneficiario_transferencia_renda int2 NULL,
    forma_tuberculose int2 NULL,
    forma_extrapulmonar int2 NULL,
    forma_extrapulmonar_outros VARCHAR(50) NULL,

    agravo_associado_adis int2 NULL,
    agravo_associado_alcoolismo int2 NULL,
    agravo_associado_diabetes int2 NULL,
    agravo_associado_doenca_mental int2 NULL,
    agravo_associado_drogas int2 NULL,
    agravo_associado_tabagismo int2 NULL,
    agravo_associado_outros int2 NULL,
    agravo_associado_outros_str VARCHAR(50) NULL,

    baciloscopia_escarro int2 NULL,
    radiografia_torax int2 NULL,
    hiv int2 NULL,
    terapia_antirretroviral_tratamento int2 NULL,
    exame_histopatologia int2 NULL,
    exame_cultura int2 NULL,
    teste_molecular_rapido int2 NULL,
    teste_sensibilidade int2 NULL,
    total_contatos_identificados VARCHAR(2) NULL
);

CREATE SEQUENCE seq_investigacao_agr_tuberculose INCREMENT 1 START 1;
CREATE TABLE auditschema.investigacao_agr_tuberculose AS (SELECT t2.*, t1.* FROM investigacao_agr_tuberculose t1, audit_temp t2 WHERE 1=2);
CREATE sequence seq_audit_id_investigacao_agr_tuberculose;
ALTER TABLE auditschema.investigacao_agr_tuberculose ADD PRIMARY KEY (audit_id);
CREATE trigger emp_audit AFTER INSERT OR UPDATE OR DELETE ON public.investigacao_agr_tuberculose FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();


/*
* Luis Seidel - VSA-106
*/

INSERT INTO public.ficha_investigacao_agravo(cd_ficha_investigacao_agravo, ordem, dt_cadastro, descricao, status, "version")
VALUES (52, 1, NOW(), 'MICROCEFALIA', 1, 0);

CREATE TABLE public.investigacao_agr_microcefalia(
    codigo int8 PRIMARY KEY NOT NULL,
    "version" int8 NOT NULL,
    flag_informacoes_complementares varchar(1) NOT NULL DEFAULT 'S'::character varying,
    cd_registro_agravo int8 NOT NULL REFERENCES public.registro_agravo(cd_registro_agravo),

    --INVESTIGAÇÃO
    dt_investigacao date NULL,
    tipo_investigacao int2 NULL,

    --IDENTIFICACAO NASCIDO VIVO
    nome_recem_nascido varchar(200) null,
    sexo_recem_nascido int2 null,
    dt_nascimento_recem_nascido date null,
    peso_recem_nascido varchar(5) null,
    comprimento_recem_nascido varchar(3) null,
    nr_declaracao_nascido_vivo varchar(50) null,

    --GESTACAO E PARTO

    --tipos alteracao congenita
    tipo_alteracao_congenita_ignorado int2 null,
    microcefalia_apenas int2 null,
    microcefalia_alteracao_snc int2 null,
    microcefalia_alteracao_congenita int2 null,
    alteracoes_congenitas_sem_microcefalia int2 null,
    deficiencia_neurologica int2 null,
    deficiencia_auditiva int2 null,
    deficiencia_visual int2 null,

    deteccao_alteracao_congenita int2 null,
    idade_gestacional_identificacao varchar(2) null,
    tipo_gravidez int2 null,
    classificacao_nascido_vivo_natimorto int2 null,
    perimetro_cefalico varchar(3) null,
    dt_medicao_perimetro_cefalico date null,
    circunferencia_craniana varchar(3) null,

    --DADOS CLINICOS EPIDEMIOLOGICOS MAE/GESTANTE
    mae_dt_provavel_inicio_sintomas date null,
    mae_febre_durante_gestacao int2 null,
    mae_exantema_durante_gestacao int2 null,

    mae_sinal_sintoma_prurido int2 null,
    mae_sinal_sintoma_hiperemia_conjuntival int2 null,
    mae_sinal_sintoma_dor_articulacao int2 null,
    mae_sinal_sintoma_dor_muscular int2 null,
    mae_sinal_sintoma_edema_articulacao int2 null,
    mae_sinal_sintoma_cefaleia int2 null,
    mae_sinal_sintoma_hipertrofia_ganglionar int2 null,
    mae_sinal_sintoma_acometimento_neurologico int2 null,

    mae_realizou_exame_laboratorial int2 null,
    mae_resultado_sifilis int2 null,
    mae_resultado_toxoplasmose int2 null,

    mae_historico_infeccao_arbobirus int2 null,
    mae_historico_malformacao_congenita int2 null,

    mae_exame_citomegalovirus int2 null,
    mae_resultado_citomegalovirus int2 null,

    mae_exame_herpes int2 null,
    mae_resultado_herpes int2 null,

    mae_resultado_igg_zika int2 null,
    mae_resultado_teste_rapido_igg_zika int2 null,
    mae_resultado_igm_zika int2 null,
    mae_resultado_teste_rapido_igm_zika int2 null,
    mae_resultado_pcr_zika int2 null,

    --DADOS CLINICOS EPIDEMIOLOGICOS RECEM-NASCIDO
    filho_exame_laboratorial int2 null,
    filho_resultado_sifilis int2 null,
    filho_resultado_toxoplasmose int2 null,
    filho_resultado_citomegalovirus int2 null,
    filho_resultado_herpes int2 null,
    filho_resultado_igg_zika int2 null,
    filho_resultado_teste_rapido_igg_zika int2 null,
    filho_resultado_igm_zika int2 null,
    filho_resultado_teste_rapido_igm_zika int2 null,
    filho_resultado_pcr_zika int2 null,

    --EXAMES DE IMAGEM
    exame_ultrassonografia int2 null,
    dt_exame_ultrassonografia date null,
    txt_exame_ultrassonografia varchar(5000),

    exame_ultrassonografia_transfontanela int2 null,
    dt_exame_ultrassonografia_transfontanela date null,
    txt_exame_ultrassonografia_transfontanela varchar(5000),

    exame_tomografia_computadorizada int2 null,
    dt_exame_tomografia_computadorizada date null,
    txt_exame_tomografia_computadorizada varchar(5000),

    exame_ressonancia_magnetica int2 null,
    dt_exame_ressonancia_magnetica date null,
    txt_exame_ressonancia_magnetica varchar(5000),

    --LOCAL DE OCORRENCIA
    estabelecimento_saude_parto int8 NULL REFERENCES public.empresa(empresa),

    --EVOLUCAO
    obito int2 null,
    nr_declaracao_obito varchar(50),
    dt_obito date null,
    obito_neonatal_precoce int2 null,

    --OBSERVACOES
    txt_observacoes varchar(5000) null,

    --ENCERRAMENTO
    cd_usuario_encerramento int8 null REFERENCES public.usuarios(cd_usuario)
);


CREATE SEQUENCE seq_investigacao_agr_microcefalia INCREMENT 1 START 1;
CREATE TABLE auditschema.investigacao_agr_microcefalia AS (SELECT t2.*, t1.* FROM investigacao_agr_microcefalia t1, audit_temp t2 WHERE 1=2);
CREATE sequence seq_audit_id_investigacao_agr_microcefalia;
ALTER TABLE auditschema.investigacao_agr_microcefalia ADD PRIMARY KEY (audit_id);
CREATE trigger emp_audit AFTER INSERT OR UPDATE OR DELETE ON public.investigacao_agr_microcefalia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

/*
* Luis Seidel - feature/VSA-108
*/

INSERT INTO public.ficha_investigacao_agravo(cd_ficha_investigacao_agravo, ordem, dt_cadastro, descricao, status, "version")
VALUES (57, 1, NOW(), 'RUBÉOLA CONGÊNITA', 1, 0);

CREATE TABLE public.investigacao_agr_rubeola_congenita(
    codigo int8 PRIMARY KEY NOT NULL,
    "version" int8 NOT NULL,
    flag_informacoes_complementares varchar(1) NOT NULL DEFAULT 'S'::character varying,
    cd_registro_agravo int8 NOT NULL REFERENCES public.registro_agravo(cd_registro_agravo),

    --ANTECEDENTES EPIDEMIOLOGICOS
    dt_investigacao date NULL,
    recem_nascido int2 null,
    peso_ao_nascer varchar(4) null,

    --DADOS CLINICOS
    sinal_maior_catarata int2 null,
    sinal_maior_retinopatia_pigmentar int2 null,
    sinal_maior_glaucoma_congenito int2 null,
    sinal_maior_deficiencia_auditiva int2 null,
    sinal_maior_cardiopatia_congenita int2 null,
    sinal_maior_cardiopatia_congenita_outro varchar(100) null,

    sinal_menor_retardo_psicomotor int2 null,
    sinal_menor_microcefalia int2 null,
    sinal_menor_meningoencefalite int2 null,
    sinal_menor_hepatoesplenomegalia int2 null,
    sinal_menor_ictericia int2 null,
    sinal_menor_purpura int2 null,
    sinal_menor_alteracoes_osseas int2 null,

    --DADOS EXAME LABORATORIAL DA CRIANCA
    dt_coleta_sangue_1 date null,
    dt_coleta_sangue_2 date null,
    dt_coleta_sangue_3 date null,
    resultado_exame_sorologico_s1_igm int2 null,
    resultado_exame_sorologico_s1_igg int2 null,
    resultado_exame_sorologico_s2_igm int2 null,
    resultado_exame_sorologico_s2_igg int2 null,
    resultado_exame_sorologico_s3_igm int2 null,
    resultado_exame_sorologico_s3_igg int2 null,

    amostra_clinica_sangue_total int2 null,
    amostra_clinica_secrecao_nasofaringea int2 null,
    amostra_clinica_urina int2 null,
    amostra_clinica_liquor int2 null,

    deteccao_viral_resultado int2 null,
    deteccao_viral_resultado_outro varchar(100) null,

    --MEDICACAO DE CONTROLE
    bloqueio_vacinal_contatos int2 null,
    isolamento_recem_nascido int2 null,

    --DADOS COMPLEMENTARES DA MAE
    mae_idade varchar(3),
    vacinacao_triplice_viral int2 null,
    vacinacao_rubeola int2 null,
    vacinacao_dupla_viral int2 null,

    dt_ultima_dose_vacina date null,
    sinal_sintoma int2 null,
    periodo_gestacao_acometida int2 null,
    criterio_confirmacao_diagnostico_mae int2 null,

    --EVOLUCAO DA CRIANCA
    classificacao_final int2 null,
    criterio_confirmacao_descarte int2 null,
    diagnostico_descarte int2 null,
    diagnostico_descarte_outros varchar(100),
    evolucao int2 null,
    dt_obito date null,

    --OBSERVACOES
    observacao varchar(5000) null,

    --ENCERRAMENTO
    cd_usuario_encerramento int8 null REFERENCES public.usuarios(cd_usuario)
);


CREATE SEQUENCE seq_investigacao_agr_rubeola_congenita INCREMENT 1 START 1;
CREATE TABLE auditschema.investigacao_agr_rubeola_congenita AS (SELECT t2.*, t1.* FROM investigacao_agr_rubeola_congenita t1, audit_temp t2 WHERE 1=2);
CREATE sequence seq_audit_id_investigacao_agr_rubeola_congenita;
ALTER TABLE auditschema.investigacao_agr_rubeola_congenita ADD PRIMARY KEY (audit_id);
CREATE trigger emp_audit AFTER INSERT OR UPDATE OR DELETE ON public.investigacao_agr_rubeola_congenita FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();