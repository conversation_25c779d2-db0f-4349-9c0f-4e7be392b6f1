SET application_name = 'flyway|3.1.244';

SET statement_timeout TO 600000;

/* Jo<PERSON> V - FRU-4 */

alter table estabelecimento add column if not exists uuid_app_fru varchar null;
alter table auditschema.estabelecimento add column if not exists uuid_app_fru varchar null;

alter table vigilancia_pessoa add column if not exists uuid_app_fru varchar null;
alter table auditschema.vigilancia_pessoa add column if not exists uuid_app_fru varchar null;

alter table registro_inspecao add column if not exists uuid_app_fru varchar null;
alter table auditschema.registro_inspecao add column if not exists uuid_app_fru varchar null;

/*
    EDUARDO 31/07/2024
*/

update tipo_equipe
set ds_tp_equipe = 'eMulti - Equipe Multiprofissional na AT. Primária à Saúde'
where cd_tp_equipe = '72';

/* Pedro de Aguiar Marques - AMB-3926 - 01/08/2024 */
CREATE OR REPLACE FUNCTION public.limpar_texto_impressao(
	t text)
    RETURNS text
    LANGUAGE 'plpgsql'
    COST 100
    IMMUTABLE PARALLEL UNSAFE
AS $BODY$
begin
    return regexp_replace(t, '[^[:ascii:]]', '', 'g');
end;
$BODY$;
