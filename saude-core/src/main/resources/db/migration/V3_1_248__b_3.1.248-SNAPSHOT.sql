SET application_name = 'flyway|3.1.248';

SET statement_timeout TO 600000;

/*
    <PERSON> - 14/08/2024
*/
INSERT INTO public.classificacao_risco VALUES (6, 6, 'Não Classificado', NULL, 0, 0);

alter table usuario_cadsus_esus add column alimentos_acabaram_antes_ter_dinheiro_comprar_mais int2;
alter table auditschema.usuario_cadsus_esus add column alimentos_acabaram_antes_ter_dinheiro_comprar_mais int2;

alter table usuario_cadsus_esus add column comeu_alguns_alimentos_que_tinha_dinheiro_acabou int2;
alter table auditschema.usuario_cadsus_esus add column comeu_alguns_alimentos_que_tinha_dinheiro_acabou int2;

alter table esus_ficha_usuario_cadsus_esus add column alimentos_acabaram_antes_ter_dinheiro_comprar_mais int2;
alter table auditschema.esus_ficha_usuario_cadsus_esus add column alimentos_acabaram_antes_ter_dinheiro_comprar_mais int2;

alter table esus_ficha_usuario_cadsus_esus add column comeu_alguns_alimentos_que_tinha_dinheiro_acabou int2;
alter table auditschema.esus_ficha_usuario_cadsus_esus add column comeu_alguns_alimentos_que_tinha_dinheiro_acabou int2;

update comunidade_tradicional set ds_comunidade = 'AGROEXTRATIVISTAS - POVOS OU COMUNIDADES TRADICIONAIS', cd_sus = 157 where cd_comunidade = 1;
update comunidade_tradicional set ds_comunidade = 'CAATINGUEIROS - POVOS OU COMUNIDADES TRADICIONAIS', cd_sus = 158 where cd_comunidade = 2;
update comunidade_tradicional set ds_comunidade = 'CAIÇARAS - POVOS OU COMUNIDADES TRADICIONAIS', cd_sus = 159 where cd_comunidade = 3;
update comunidade_tradicional set ds_comunidade = 'CERRADO - POVOS OU COMUNIDADES TRADICIONAIS', cd_sus = 160 where cd_comunidade = 4;
update comunidade_tradicional set ds_comunidade = 'CIGANOS - POVOS OU COMUNIDADES TRADICIONAIS', cd_sus = 161 where cd_comunidade = 5;
update comunidade_tradicional set ds_comunidade = 'COMUNIDADES DE FUNDO E FECHO DE PASTO - POVOS OU COMUNIDADES TRADICIONAIS', cd_sus = 162 where cd_comunidade = 6;
update comunidade_tradicional set ds_comunidade = 'EXTRATIVISTAS - POVOS OU COMUNIDADES TRADICIONAIS', cd_sus = 163 where cd_comunidade = 7;
update comunidade_tradicional set ds_comunidade = 'FAXINALENSES - POVOS OU COMUNIDADES TRADICIONAIS', cd_sus = 164 where cd_comunidade = 8;
update comunidade_tradicional set ds_comunidade = 'GERAIZEIROS - POVOS OU COMUNIDADES TRADICIONAIS', cd_sus = 165 where cd_comunidade = 9;
update comunidade_tradicional set ds_comunidade = 'MARISQUEIROS - POVOS OU COMUNIDADES TRADICIONAIS', cd_sus = 166 where cd_comunidade = 10;
update comunidade_tradicional set ds_comunidade = 'PANTANEIROS - POVOS OU COMUNIDADES TRADICIONAIS', cd_sus = 167 where cd_comunidade = 11;
update comunidade_tradicional set ds_comunidade = 'PESCADORES ARTESANAIS - POVOS OU COMUNIDADES TRADICIONAIS', cd_sus = 168 where cd_comunidade = 12;
update comunidade_tradicional set ds_comunidade = 'POMERANOS - POVOS OU COMUNIDADES TRADICIONAIS', cd_sus = 169 where cd_comunidade = 13;
update comunidade_tradicional set ds_comunidade = 'POVOS INDÍGENAS - POVOS OU COMUNIDADES TRADICIONAIS', cd_sus = 170 where cd_comunidade = 14;
update comunidade_tradicional set ds_comunidade = 'POVOS QUILOMBOLAS - POVOS OU COMUNIDADES TRADICIONAIS', cd_sus = 171 where cd_comunidade = 15;
update comunidade_tradicional set ds_comunidade = 'QUEBRADEIRAS DE COCO BABAÇU - POVOS OU COMUNIDADES TRADICIONAIS', cd_sus = 172 where cd_comunidade = 16;
update comunidade_tradicional set ds_comunidade = 'RETIREIROS - POVOS OU COMUNIDADES TRADICIONAIS', cd_sus = 173 where cd_comunidade = 17;
update comunidade_tradicional set ds_comunidade = 'RIBEIRINHOS - POVOS OU COMUNIDADES TRADICIONAIS', cd_sus = 174 where cd_comunidade = 18;
update comunidade_tradicional set ds_comunidade = 'SERINGUEIROS - POVOS OU COMUNIDADES TRADICIONAIS', cd_sus = 175 where cd_comunidade = 19;
update comunidade_tradicional set ds_comunidade = 'POVOS DE TERREIRO / MATRIZ AFRICANA - POVOS OU COMUNIDADES TRADICIONAIS', cd_sus = 176 where cd_comunidade = 20;
update comunidade_tradicional set ds_comunidade = 'VAZANTEIROS - POVOS OU COMUNIDADES TRADICIONAIS', cd_sus = 177 where cd_comunidade = 21;
update comunidade_tradicional set ds_comunidade = 'OUTROS - POVOS OU COMUNIDADES TRADICIONAIS', cd_sus = 178 where cd_comunidade = 22;
insert into comunidade_tradicional (cd_comunidade, ds_comunidade, cd_sus, version) values (23, 'ACAMPADA - POPULAÇÃO CAMPO, FLORESTA E ÁGUAS', 179, 0);
insert into comunidade_tradicional (cd_comunidade, ds_comunidade, cd_sus, version) values (24, 'ANDIROBEIRAS - POPULAÇÃO CAMPO, FLORESTA E ÁGUAS', 180, 0);
insert into comunidade_tradicional (cd_comunidade, ds_comunidade, cd_sus, version) values (25, 'ASSENTADA - POPULAÇÃO CAMPO, FLORESTA E ÁGUAS', 182, 0);
insert into comunidade_tradicional (cd_comunidade, ds_comunidade, cd_sus, version) values (26, 'CAMPONESES - POPULAÇÃO CAMPO, FLORESTA E ÁGUAS', 183, 0);
insert into comunidade_tradicional (cd_comunidade, ds_comunidade, cd_sus, version) values (27, 'CASTANHEIRAS - POPULAÇÃO CAMPO, FLORESTA E ÁGUAS', 184, 0);
insert into comunidade_tradicional (cd_comunidade, ds_comunidade, cd_sus, version) values (28, 'CATADORES DE MANGABA - POPULAÇÃO CAMPO, FLORESTA E ÁGUAS', 185, 0);
insert into comunidade_tradicional (cd_comunidade, ds_comunidade, cd_sus, version) values (29, 'ISQUEIROS - POPULAÇÃO CAMPO, FLORESTA E ÁGUAS', 186, 0);
insert into comunidade_tradicional (cd_comunidade, ds_comunidade, cd_sus, version) values (30, 'JANGADEIROS - POPULAÇÃO CAMPO, FLORESTA E ÁGUAS', 187, 0);
insert into comunidade_tradicional (cd_comunidade, ds_comunidade, cd_sus, version) values (31, 'MORROQUIANOS - POPULAÇÃO CAMPO, FLORESTA E ÁGUAS', 189, 0);
insert into comunidade_tradicional (cd_comunidade, ds_comunidade, cd_sus, version) values (32, 'POPULAÇÕES ATINGIDAS POR BARRAGENS - POPULAÇÃO CAMPO, FLORESTA E ÁGUAS', 190, 0);
insert into comunidade_tradicional (cd_comunidade, ds_comunidade, cd_sus, version) values (33, 'TRABALHADORES RURAIS ASSALARIADOS - POPULAÇÃO CAMPO, FLORESTA E ÁGUAS', 193, 0);
insert into comunidade_tradicional (cd_comunidade, ds_comunidade, cd_sus, version) values (34, 'TRABALHADORES RURAIS TEMPORÁRIOS - POPULAÇÃO CAMPO, FLORESTA E ÁGUAS', 194, 0);
insert into comunidade_tradicional (cd_comunidade, ds_comunidade, cd_sus, version) values (35, 'VARJEIROS - POPULAÇÃO CAMPO, FLORESTA E ÁGUAS', 195, 0);

/* João Vittor */

update conduta set ds_conduta = 'AGENDAMENTO P/eMulti', cd_esus = 14 where cd_esus = 3 and tp_conduta = 0;
update conduta set ds_conduta = 'AGENDAMENTO P/eMulti', cd_esus = 18 where cd_esus = 13 and tp_conduta = 1;

/* João */

 update esus_ficha_usuario_cadsus_esus es
   set orientacao_sexual = 11
  from esus_integracao_cds ic
where ic.cd_esus_ficha_usu_cadsus_esus = es.cd_esus_ficha_usu_cadsus_esus
   and ic.cd_exp_proc is null
   and es.orientacao_sexual  = 6;

update  usuario_cadsus_esus set orientacao_sexual = 11 where orientacao_sexual = 6;

alter table  usuario_cadsus_esus add column inf_identidade_genero int4;
alter table  auditschema.usuario_cadsus_esus add column inf_identidade_genero int4;

alter table  esus_ficha_usuario_cadsus_esus add column inf_identidade_genero int4;
alter table   auditschema.esus_ficha_usuario_cadsus_esus add column inf_identidade_genero int4;

 update esus_ficha_usuario_cadsus_esus es
   set inf_identidade_genero = 1
  from esus_integracao_cds ic
where ic.cd_esus_ficha_usu_cadsus_esus = es.cd_esus_ficha_usu_cadsus_esus
   and ic.cd_exp_proc is null
   and es.orientacao_sexual  is not null;

update  usuario_cadsus_esus set inf_identidade_genero = 1 where orientacao_sexual  is not null;

