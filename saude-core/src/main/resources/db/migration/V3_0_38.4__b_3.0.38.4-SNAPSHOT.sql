SET application_name = 'flyway|3.0.38.4';
/*
    Diego - 08/05/2014 - #9888
*/

alter table item_conta_paciente add column atr_vl_para int2;
alter table auditschema.item_conta_paciente add column atr_vl_para int2;
update item_conta_paciente set atr_vl_para = 1;

update item_conta_paciente as ic set empresa_faturamento =(
SELECT ep.empresa
FROM item_conta_paciente as icp
left join conta_paciente cp on icp.cd_conta_paciente=cp.cd_conta_paciente
left join atendimento_informacao ai on ai.cd_atend_inf=cp.cd_atend_inf
left join atendimento ap on ap.nr_atendimento=ai.nr_atendimento_principal
left join empresa e on ap.empresa=e.empresa
left join empresa ep on e.empresa_princ=ep.empresa
where icp.cd_it_conta_paciente = ic.cd_it_conta_paciente
)
where ic.empresa_faturamento is not null;

/*
    Diego - 13/05/2015 - #9910
*/

alter table conta_paciente add column lib_critica_cns int2;
alter table auditschema.conta_paciente add column lib_critica_cns int2;
alter table conta_paciente add column lib_critica_idade_maior int2;
alter table auditschema.conta_paciente add column lib_critica_idade_maior int2;
alter table conta_paciente add column lib_critica_idade_menor int2;
alter table auditschema.conta_paciente add column lib_critica_idade_menor int2;
alter table conta_paciente add column lib_critica_qtd_max int2;
alter table auditschema.conta_paciente add column lib_critica_qtd_max int2;
alter table conta_paciente add column lib_critica_telefone int2;
alter table auditschema.conta_paciente add column lib_critica_telefone int2;
alter table conta_paciente add column lib_critica_tmp_perman int2;
alter table auditschema.conta_paciente add column lib_critica_tmp_perman int2;
alter table conta_paciente add column lib_critica_just_cns varchar(50);
alter table auditschema.conta_paciente add column lib_critica_just_cns varchar(50);

/*
 PostgreSQL
 Everton - 14/05/2015
*/

DELETE FROM atendimento_prontuario WHERE cd_teste_rapido_realizado is not null; 