SET application_name = 'flyway|3.0.62.2';

/*
    Sulivan - 27/01/2017 - #14290
*/
ALTER TABLE requerimento_vigilancia ADD COLUMN obs_requerimento text NULL;
ALTER TABLE auditschema.requerimento_vigilancia ADD COLUMN obs_requerimento text NULL;

/*
    Sulivan - 30/01/2017 - #14295
*/
ALTER TABLE eventos_vigilancia ADD COLUMN cd_vigilancia_endereco int8 NULL;
ALTER TABLE auditschema.eventos_vigilancia ADD COLUMN cd_vigilancia_endereco int8 NULL;

alter table eventos_vigilancia
   add constraint FK_EVEN_VIGI_REF_VIGI_ENDE foreign key (cd_vigilancia_endereco)
      references vigilancia_endereco (cd_vigilancia_endereco)
      on delete restrict on update restrict;

/*
    Sulivan - 30/01/2017 - #14298
*/
ALTER TABLE veiculo_estabelecimento ALTER COLUMN renavam TYPE varchar(20);
ALTER TABLE auditschema.veiculo_estabelecimento ALTER COLUMN renavam TYPE varchar(20);

/*
    Roger - 30/01/2017 - #14279
*/
CREATE TABLE estabelecimento_setores (
cd_estabelecimento_setores                      bigint        NOT NULL,
cd_estabelecimento                              bigint        NOT NULL,
ds_setor                                        varchar(50)   NOT NULL,
status                                          smallint      NOT NULL,
version                                         bigint        NOT NULL ,
CONSTRAINT pk_cd_estabelecimento_setores PRIMARY KEY (cd_estabelecimento_setores),
CONSTRAINT fk_estab_setor_ref_setor FOREIGN KEY (cd_estabelecimento)
REFERENCES estabelecimento (cd_estabelecimento) ON UPDATE RESTRICT ON DELETE RESTRICT);

CREATE TABLE auditschema.estabelecimento_setores AS SELECT t2.*, t1.* FROM estabelecimento_setores t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_estabelecimento_setores;alter table auditschema.estabelecimento_setores add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON estabelecimento_setores FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE TABLE estabelecimento_alvara (
cd_estabelecimento_alvara                           bigint        NOT NULL,
cd_requerimento_alvara                              bigint        NOT NULL,
cd_estabelecimento_setores                          bigint            NULL,
cd_estabelecimento                                  bigint        NOT NULL,
status                                              smallint      NOT NULL,
version                                             bigint        NOT NULL,
CONSTRAINT pk_estabelecimento_alvara PRIMARY KEY (cd_estabelecimento_alvara),
CONSTRAINT fk_req_alvara_setor_ref_req_alv FOREIGN KEY (cd_requerimento_alvara)
REFERENCES requerimento_alvara (cd_requerimento_alvara) ON UPDATE RESTRICT ON DELETE RESTRICT,
CONSTRAINT fk_req_alv_setores_ref_est_setores FOREIGN KEY (cd_estabelecimento_setores)
REFERENCES estabelecimento_setores (cd_estabelecimento_setores) ON UPDATE RESTRICT ON DELETE RESTRICT,
CONSTRAINT fk_estab_alvara_ref_estab FOREIGN KEY (cd_estabelecimento)
REFERENCES estabelecimento (cd_estabelecimento) ON UPDATE RESTRICT ON DELETE RESTRICT
);

CREATE UNIQUE INDEX IDX_PK_ESTABELECIMENTO_ALVARA ON estabelecimento_alvara (
cd_estabelecimento,
cd_estabelecimento_setores
);

CREATE TABLE auditschema.estabelecimento_alvara AS SELECT t2.*, t1.* FROM estabelecimento_alvara t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_estabelecimento_alvara;alter table auditschema.estabelecimento_alvara add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON estabelecimento_alvara FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

ALTER TABLE requerimento_vigilancia ALTER nome TYPE varchar(300);
ALTER TABLE auditschema.requerimento_vigilancia ALTER nome TYPE varchar(300);

ALTER TABLE requerimento_alvara ADD COLUMN cd_estabelecimento_setores bigint NULL;
ALTER TABLE auditschema.requerimento_alvara ADD COLUMN cd_estabelecimento_setores bigint NULL;

ALTER TABLE requerimento_alvara ADD CONSTRAINT fk_req_alv_ref_est_setores FOREIGN KEY (cd_estabelecimento_setores)
REFERENCES estabelecimento_setores (cd_estabelecimento_setores) ON UPDATE RESTRICT ON DELETE RESTRICT;

insert into estabelecimento_alvara  SELECT nextval('seq_gem'), t2.cd_requerimento_alvara, null, t1.cd_estabelecimento, 1, 0
  FROM estabelecimento t1, requerimento_alvara t2
  where cd_req_vig_origem_alvara is not null
    and t1.cd_req_vig_origem_alvara = t2.cd_req_vigilancia;


UPDATE responsavel_tecnico SET version = 0 WHERE version IS NULL;
ALTER TABLE responsavel_tecnico alter version set not null;