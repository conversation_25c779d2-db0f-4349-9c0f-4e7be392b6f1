SET application_name = 'flyway|3.1.44';

SET statement_timeout TO 600000;
/*
    Rony - 15/07/2020
*/
ALTER TABLE dom_paciente_mobile ADD observacao_ultima_visita varchar NULL;


/*
    <PERSON> - CSF-135 - 01/08/2020
*/

--ATUALIZA dom_paciente_mobile.observacao_ultima_visita COM visita_domiciliar.observacao DA ÚLTIMA VISITA ATIVA
create or replace function ftg_update_ultima_obs_dom_paciente_visita_dom()
returns trigger
as $$
    begin
        if(new.cd_usu_cadsus is not null) --POSSUI PACIENTE
            then
                update
                    dom_paciente_mobile
                set
                    observacao_ultima_visita = (
                        select
                            observacao
                        from
                            visita_domiciliar
                        where
                            cd_usu_cadsus = new.cd_usu_cadsus
                            and situacao = 0
                        group by
                            dt_visita,
                            turno,
                            cd_visita,
                            observacao
                        order by
                            dt_visita desc,
                            turno desc,
                            cd_visita desc
                        limit 1
                    ),
                    version_all = nextval('seq_version_dom_paciente_mobile')
                where
                    cd_usu_cadsus = new.cd_usu_cadsus;
        end if;
    return new;
end;
$$ language plpgsql;


--TRIGGER AO INSERIR/ATULIZAR/CANCELAR visita_domiciliar
create trigger tg_update_ultima_obs_dom_paciente_visita_dom
after insert or update or delete on visita_domiciliar
for each row execute procedure ftg_update_ultima_obs_dom_paciente_visita_dom();


create index idx_visita_domiciliar_usuario_situacao on visita_domiciliar (cd_usu_cadsus, situacao);