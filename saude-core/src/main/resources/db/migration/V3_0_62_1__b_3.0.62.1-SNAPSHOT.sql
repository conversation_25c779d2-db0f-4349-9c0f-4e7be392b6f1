SET application_name = 'flyway|3.0.62.1';

/*
    Sulivan - 20/01/2017 - #14253
*/
alter table requerimento_vigilancia add column dt_entrega date;
alter table auditschema.requerimento_vigilancia add column dt_entrega date;
alter table requerimento_vigilancia add column nome_entrega VARCHAR(60);
alter table auditschema.requerimento_vigilancia add column nome_entrega VARCHAR(60);
alter table requerimento_vigilancia add column vinculo_entrega VARCHAR(60);
alter table auditschema.requerimento_vigilancia add column vinculo_entrega VARCHAR(60);
INSERT INTO programa_pagina_permissao VALUES(310, 38, 403, 0, 'entregaDocumento');

/*
    Everton - 23/01/2017
*/
update vigilancia_profissional t1
   set cd_profissional_endereco = (select cd_profissional_endereco from profissional_endereco where cd_vigilancia_profissional = t1.cd_vigilancia_profissional and flag_principal = 1)
   where cd_profissional_endereco is null;

/*
    Sulivan - 23/01/2017 - #14259
*/
INSERT INTO programa_pagina_permissao VALUES(311, 18, 34, 0, 'unidade');


/*
    Roger - 20/01/2017 - #14256
*/
ALTER TABLE atividade_estabelecimento ADD COLUMN obs_destaque_alvara VARCHAR(1024) NULL;
ALTER TABLE auditschema.atividade_estabelecimento ADD COLUMN obs_destaque_alvara VARCHAR(1024) NULL;
ALTER TABLE estabelecimento ADD COLUMN obs_destaque_alvara VARCHAR(1024) NULL;
ALTER TABLE auditschema.estabelecimento ADD COLUMN obs_destaque_alvara VARCHAR(1024) NULL;
ALTER TABLE requerimento_alvara ADD COLUMN obs_destaque_alvara VARCHAR(1024) NULL;
ALTER TABLE auditschema.requerimento_alvara ADD COLUMN obs_destaque_alvara VARCHAR(1024) NULL;
ALTER TABLE requerimento_evento ADD COLUMN obs_destaque_alvara VARCHAR(1024) NULL;
ALTER TABLE auditschema.requerimento_evento ADD COLUMN obs_destaque_alvara VARCHAR(1024) NULL;

UPDATE requerimento_alvara set obs_destaque_alvara = (SELECT obs_destaque_alvara FROM configuracao_vigilancia) WHERE obs_destaque_alvara IS NULL;
UPDATE requerimento_evento set obs_destaque_alvara = (SELECT obs_destaque_alvara FROM configuracao_vigilancia) WHERE obs_destaque_alvara IS NULL;

/*
    Roger - 20/01/2017 - #14230
*/
ALTER TABLE vigilancia_profissional ADD COLUMN cpf VARCHAR(14) NULL;
ALTER TABLE auditschema.vigilancia_profissional ADD COLUMN cpf VARCHAR(14) NULL;

ALTER TABLE vigilancia_profissional ADD COLUMN rg VARCHAR(20) NULL;
ALTER TABLE auditschema.vigilancia_profissional ADD COLUMN rg VARCHAR(20) NULL;

/*
    Roger - 20/01/2017 - #14269
*/
CREATE TABLE requerimento_alteracao_razao_social(
cd_requerimento_alteracao_razao_social          bigint       NOT NULL,
cd_req_vigilancia                               bigint       NOT NULL,
razao_social	                                varchar      NOT NULL,
fantasia	                                     varchar      NULL,
version                                         bigint       NOT NULL,
CONSTRAINT pk_cd_requerimento_alteracao_razao_social PRIMARY KEY (cd_requerimento_alteracao_razao_social),
CONSTRAINT fk_req_alt_raz_soc_ref_req_vigi FOREIGN KEY (cd_req_vigilancia)
REFERENCES requerimento_vigilancia (cd_req_vigilancia) ON UPDATE RESTRICT ON DELETE RESTRICT
);

CREATE TABLE auditschema.requerimento_alteracao_razao_social AS SELECT t2.*, t1.* FROM requerimento_alteracao_razao_social t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requerimento_alteracao_razao_social;alter table auditschema.requerimento_alteracao_razao_social add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requerimento_alteracao_razao_social FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

INSERT INTO programa_pagina VALUES (1370, 'br.com.celk.view.vigilancia.requerimentos.contratosocial.RequerimentoAlteracaoRazaoSocialPage', 'N');
INSERT INTO programa_web_pagina VALUES (1401, 684, 1370);

INSERT INTO programa_pagina VALUES (1371, 'br.com.celk.view.vigilancia.requerimentos.contratosocial.RequerimentoAlteracaoRazaoSocialExternoPage', 'N');
INSERT INTO programa_web_pagina VALUES (1402, 692, 1371);

/*
    Roger - 25/01/2017 - #14276
*/
ALTER TABLE vigilancia_endereco ADD COLUMN cd_tipo_logradouro numeric(3) NULL;
ALTER TABLE auditschema.vigilancia_endereco ADD COLUMN cd_tipo_logradouro numeric(3) NULL;
alter table vigilancia_endereco
        add constraint FK_VIG_END_REF_TIPO_LOGR foreign key (cd_tipo_logradouro)
        references tipo_logradouro_cadsus (cd_tipo_logradouro)
        on delete restrict on update restrict;

ALTER TABLE requerimento_vigilancia ADD COLUMN cd_tipo_logradouro numeric(3) NULL;
ALTER TABLE auditschema.requerimento_vigilancia ADD COLUMN cd_tipo_logradouro numeric(3) NULL;
alter table requerimento_vigilancia
        add constraint FK_REQ_VIG_REF_TIPO_LOGR foreign key (cd_tipo_logradouro)
        references tipo_logradouro_cadsus (cd_tipo_logradouro)
        on delete restrict on update restrict;

INSERT INTO programa_web_pagina VALUES (1403, 245, 1370);

/*
    Sulivan - 25/01/2017 - #14278
*/
ALTER TABLE configuracao_vigilancia ADD COLUMN ini_seq_alvara_ano_anterior int8 NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN ini_seq_alvara_ano_anterior int8 NULL;
ALTER TABLE configuracao_vigilancia ADD COLUMN ini_proto_lic_trans_ano_anterior int8 NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN ini_proto_lic_trans_ano_anterior int8 NULL;
ALTER TABLE requerimento_alvara ADD COLUMN ano_base int4 NULL;
ALTER TABLE auditschema.requerimento_alvara ADD COLUMN ano_base int4 NULL;
ALTER TABLE requerimento_licenca_transporte ADD COLUMN ano_base int4 NULL;
ALTER TABLE auditschema.requerimento_licenca_transporte ADD COLUMN ano_base int4 NULL;
ALTER TABLE requerimento_evento ADD COLUMN ano_base int4 NULL;
ALTER TABLE auditschema.requerimento_evento ADD COLUMN ano_base int4 NULL;

/*
    Sulivan - 26/01/2017
*/
DROP INDEX IF EXISTS idx_controle_num_receita_b_subtipo;
create unique index idx_controle_num_receita_b_subtipo on controle_num_receita_b (subtipo);

/*
    Sulivan - 26/01/2017 - #14282
*/
ALTER TABLE configuracao_vigilancia ADD COLUMN ini_numeracao_baixa int8 NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN ini_numeracao_baixa int8 NULL;
ALTER TABLE configuracao_vigilancia ADD COLUMN ini_numeracao_nada_consta int8 NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN ini_numeracao_nada_consta int8 NULL;
ALTER TABLE requerimento_nada_consta ADD COLUMN numeracao int8 NULL;
ALTER TABLE auditschema.requerimento_nada_consta ADD COLUMN numeracao int8 NULL;
ALTER TABLE requerimento_baixa_responsabilidade ADD COLUMN numeracao int8 NULL;
ALTER TABLE auditschema.requerimento_baixa_responsabilidade ADD COLUMN numeracao int8 NULL;

/*
    Sulivan - 26/01/2017 - #14284
*/
INSERT INTO permissao_web (cd_permissao, ds_permissao, version) VALUES (98, 'Visualizar Todos', 0);
INSERT INTO programa_pagina_permissao VALUES(312, 98, 403, 0, 'visualizarTodosRequerimentos');