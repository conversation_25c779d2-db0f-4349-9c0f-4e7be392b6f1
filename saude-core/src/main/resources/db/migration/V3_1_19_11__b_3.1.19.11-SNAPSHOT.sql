SET application_name = 'flyway|3.1.19.11';

/*
    Silvio - 29/01/2019 - #23748
*/
create table cva_vacina_animal (
    cd_vacina_animal              INT8          not null,
    ds_vacina_animal              VARCHAR(100)  not null,
    situacao                        INT2          not null,
    version                       INT8          not null
);

alter table cva_vacina_animal
	add constraint pk_cva_vacina_animal
	    primary key (cd_vacina_animal);

CREATE TABLE auditschema.cva_vacina_animal AS SELECT t2.*, t1.* FROM cva_vacina_animal t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_cva_vacina_animal;alter table auditschema.cva_vacina_animal add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON cva_vacina_animal FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

INSERT INTO programa_pagina VALUES (1663, 'br.com.celk.view.vigilancia.cva.vacinaanimal.ConsultaCvaVacinaAnimalPage', 'N');
INSERT INTO programa_pagina VALUES (1664, 'br.com.celk.view.vigilancia.cva.vacinaanimal.CadastroCvaVacinaAnimalPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (925, 'Vacina Animal', 1663, 'N');
INSERT INTO programa_web_pagina VALUES (1777, 925, 1663);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1175,'Vacina Animal','',764,925,307,0,0);
INSERT INTO programa_web_pagina VALUES (1778, 925, 1664);

create table cva_animal_vacina (
    cd_cva_animal_vacina              INT8          not null,
    cd_cva_animal                     INT8          not null,
    cd_vacina_animal                  INT8          not null,
    dt_vacina                         TIMESTAMP,
    dt_cadastro                       TIMESTAMP     not null,
    cd_usuario                        NUMERIC(6)    not null,
    version                       	  INT8          not null
);

alter table cva_animal_vacina
	add constraint pk_cva_animal_vacina
	    primary key (cd_cva_animal_vacina);

alter table cva_animal_vacina
   add constraint FK_CVA_ANIMAL_VAC_REF_ANIMAL foreign key (cd_cva_animal)
   references cva_animal (cd_cva_animal)
   on delete restrict on update restrict;

alter table cva_animal_vacina
   add constraint FK_CVA_ANIMAL_VAC_REF_VACINA foreign key (cd_vacina_animal)
   references cva_vacina_animal (cd_vacina_animal)
   on delete restrict on update restrict;

CREATE TABLE auditschema.cva_animal_vacina AS SELECT t2.*, t1.* FROM cva_animal_vacina t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_cva_animal_vacina;alter table auditschema.cva_animal_vacina add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON cva_animal_vacina FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

/*
    Elton - 28/01/2019 - #23712
*/
alter table cva_proprietario_responsavel add COLUMN tp_proprietario_responsavel INT2 null;
alter table auditschema.cva_proprietario_responsavel add COLUMN tp_proprietario_responsavel INT2 null;

/*
    Elton - 06/02/2019 - #23806
*/
INSERT INTO programa_pagina VALUES (1619, 'br.com.celk.view.cadsus.usuariocadsus.CartaoIdentificacaoPacientePage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (910, 'Cartão de Identificação do Paciente', 1619, 'N');
INSERT INTO programa_web_pagina VALUES (1713, 910, 1619);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (1164, 'Cartão de Identificação do Paciente', 'cartaoIdentificacaoPaciente', 161, 910, 0, 0, 0);

/*
    Leonardo - 05/02/2018 - #23108
*/

INSERT INTO programa_pagina VALUES (1666, 'br.com.celk.view.materiais.dispensacao.relatorio.RelatorioLivroRegistroEspecificoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (926, 'Livro de Registro Específico', 1666, 'N');
INSERT INTO programa_web_pagina VALUES (1780, 926, 1666);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1176,'Livro de Registro Específico','livroRegistroEspecifico',371,926,14,0,0);