SET application_name = 'flyway|3.1.21.43';
SET statement_timeout TO 600000;

/*
    <PERSON> - GMT-76 - 19/12/2019
*/

-- TABELA: integra_confirma_receb_branet --
CREATE sequence seq_integra_confirma_receb_branet;

CREATE TABLE integra_confirma_receb_branet (
    cd_integra_confirma_receb_branet    INT8              not null,
    id_cnpj_integracao		     		INT8              not null,
    json                          		TEXT              null,
    resposta                            INT4              null,
    mensagem                      		TEXT              null,
    data                          		timestamp         null,
    status                        		TEXT              null,
	tipo_log                       		TEXT              null,
    cd_usuario                    		NUMERIC(6)        not null,
    dt_cadastro                   		timestamp         not null,
    version                       		INT8              not null
);

ALTER TABLE integra_confirma_receb_branet ADD CONSTRAINT PK_INT_CONFIRM_BRANET PRIMARY KEY (cd_integra_confirma_receb_branet);

ALTER TABLE integra_confirma_receb_branet ADD CONSTRAINT FK_INT_CONFIRM_REF_USUARIO FOREIGN KEY (cd_usuario) REFERENCES usuarios (cd_usuario) ON DELETE RESTRICT ON UPDATE RESTRICT;

CREATE TABLE auditschema.integra_confirma_receb_branet AS SELECT t2.*, t1.* FROM integra_confirma_receb_branet t1, audit_temp t2 WHERE 1=2;
CREATE SEQUENCE seq_audit_id_integra_confirma_receb_branet;
ALTER TABLE auditschema.integra_confirma_receb_branet ADD PRIMARY KEY (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON integra_confirma_receb_branet FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

alter table pedido_transferencia_item add column id_solicitacao_item int4;
alter table auditschema.pedido_transferencia_item add column id_solicitacao_item int4;

/*
 Maicon - 17/12/2019 - VSA-312
*/
CREATE SEQUENCE seq_atividade_estabelecimento_tipo_servico start with 1;

create table atividade_estabelecimento_tipo_servico (
    cd_atividade_estabelecimento_tipo_servico            int8            not null,
    cd_atividade_estabelecimento                         int8            not null,
    tp_servico                                           varchar         not null,
    dt_cadastro                                          TIMESTAMP         not null,
    dt_alteracao                                         TIMESTAMP         not null,
    cd_usuario                                           NUMERIC(6)        not null,
    version                                              int8            not null,
    constraint PK_ATIV_ESTAB_TP_SERVICO primary key (cd_atividade_estabelecimento_tipo_servico),
    constraint FK_ATIV_ESTAB_TP_SERVICO_REF_ATIV_ESTAB foreign key (cd_atividade_estabelecimento)
      references atividade_estabelecimento (cd_atividade_estabelecimento),
    constraint FK_ATIV_ESTAB_TP_SERVICO_REF_USU foreign key (cd_usuario) references usuarios (cd_usuario)
);

CREATE TABLE auditschema.atividade_estabelecimento_tipo_servico AS SELECT t2.*, t1.* FROM atividade_estabelecimento_tipo_servico t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atividade_estabelecimento_tipo_servico;alter table auditschema.atividade_estabelecimento_tipo_servico add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atividade_estabelecimento_tipo_servico FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

/*
    Cristian - VSA-347 - 20/12/2019
    PERMISSAO DE CADASTRO DA TELA DE BOLETO_VIGILANCIA_PAGE NO REQUERIMENTO EXTERNO
*/
INSERT INTO programa_pagina_permissao VALUES(541, 66, 1381, 0, '');
INSERT INTO programa_web_pagina VALUES (1889, 717, 1381);

/*
    Rafael Santos - 27/12/2019
*/
INSERT INTO programa_pagina VALUES (1767, 'br.com.celk.view.unidadesaude.esus.relatorios.RelatorioEstratificacaoRiscoFamiliarPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (981, 'Relatório Estratificação de Risco Familiar', 1767, 'N');
INSERT INTO programa_web_pagina VALUES (1890, 981, 1767);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1233,'Relatório Estratificação de Risco Familiar','relatorioEstratificacaoRiscoFamiliar',799,981,143,0,0);
INSERT INTO programa_pagina_permissao VALUES(542, 18, 1767, 0, '');
INSERT INTO programa_pagina_permissao VALUES(543, 14, 1767, 0, '');