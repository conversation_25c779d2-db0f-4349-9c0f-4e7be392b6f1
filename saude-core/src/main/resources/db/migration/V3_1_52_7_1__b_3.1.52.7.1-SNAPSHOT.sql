SET application_name = 'flyway|3.1.52.7.1';

SET statement_timeout TO 600000;

/*=====================================================================================================*/
/*   task:  REG-381 - Criar controle última alteração agendamento                                      */
/*   autor: Hallan.Neves - Rafael.Dantas                                                               */
/*   data:  12/10/2020                                                                                 */
/*=====================================================================================================*/

-- criação nova tabela de controle da última data de alteração no agendamento e inserção do primeiro/único registro
DROP TABLE IF EXISTS public.ultima_alteracao_agendamento;
CREATE TABLE ultima_alteracao_agendamento (dt_referencia TIMESTAMP);
DROP TABLE IF EXISTS auditschema.ultima_alteracao_agendamento;
CREATE TABLE auditschema.ultima_alteracao_agendamento AS SELECT t2.*, t1.* FROM ultima_alteracao_agendamento t1, audit_temp t2 WHERE 1=2;

create sequence seq_AUDIT_ID_ultima_alteracao_agendamento;
create trigger EMP_AUDIT after
insert
   or
update
   or
delete
   on
   ultima_alteracao_agendamento for each row execute procedure PROCESS_EMP_AUDIT();


INSERT INTO ultima_alteracao_agendamento(dt_referencia) VALUES (NULL);

DO $$
    BEGIN
        BEGIN
            ALTER TABLE solicitacao_agendamento ADD COLUMN dt_processamento_lote TIMESTAMP;
        EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column dt_processamento_lote already exists in solicitacao_agendamento.';
        END;
    END;
$$;

DO $$
    BEGIN
        BEGIN
            ALTER TABLE auditschema.solicitacao_agendamento ADD COLUMN dt_processamento_lote TIMESTAMP;
        EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column dt_processamento_lote already exists in auditschema.solicitacao_agendamento.';
        END;
    END;
$$;