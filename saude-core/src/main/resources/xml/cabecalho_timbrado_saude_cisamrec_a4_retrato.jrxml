<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="report name" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="6241c44e-6e7a-498a-8264-4d528a58fd1a">
	<property name="ireport.zoom" value="2.0000000000000053"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="DESC_CABECALHO_PADRAO" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="UNIDADE_ATENDIMENTO" class="java.lang.String"/>
	<parameter name="CAMINHO_IMAGEM_PADRAO" class="java.lang.String"/>
	<parameter name="RUA_UNIDADE" class="java.lang.String"/>
	<parameter name="BAIRRO_UNIDADE" class="java.lang.String"/>
	<parameter name="CIDADE_UNIDADE" class="java.lang.String"/>
	<parameter name="UF_UNIDADE" class="java.lang.String"/>
	<parameter name="CEP_UNIDADE" class="java.lang.String"/>
	<parameter name="FONE_UNIDADE" class="java.lang.String"/>
	<parameter name="EXIBIR_CABECALHO" class="java.lang.Boolean"/>
	<parameter name="EXIBIR_RODAPE" class="java.lang.Boolean"/>
	<parameter name="NUMERO_UNIDADE" class="java.lang.String"/>
	<parameter name="TITULO_REPORT" class="java.lang.String"/>
	<parameter name="CABECALHO_ADICIONAL_1" class="java.lang.String"/>
	<parameter name="CABECALHO_ADICIONAL_2" class="java.lang.String"/>
	<parameter name="CABECALHO_DIRETOR_TECNICO" class="java.lang.String"/>
	<parameter name="EXIBIR_NUMERO_PAGINAS" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[false]]></defaultValueExpression>
	</parameter>
	<parameter name="USUARIO_LOGADO" class="java.lang.String"/>
	<parameter name="EXIBIR_HORARIO" class="java.lang.Boolean"/>
	<parameter name="DADOS_UNIDADE" class="java.lang.String"/>
	<parameter name="ENDERECO_UNIDADE" class="java.lang.String"/>
	<parameter name="DADOS_UNIDADE_DOIS" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="91" splitType="Stretch">
			<image>
				<reportElement key="image-1" x="2" y="1" width="71" height="62" uuid="3d4066cb-bc5f-4763-a904-e7175f9499c2">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<imageExpression><![CDATA[$P{CAMINHO_IMAGEM_PADRAO}]]></imageExpression>
			</image>
			<line>
				<reportElement positionType="Float" x="0" y="71" width="555" height="1" uuid="46ea9593-e203-4cca-a012-1115ffb39548">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
			</line>
			<textField pattern="">
				<reportElement x="493" y="60" width="60" height="10" uuid="f414f67e-276c-4f61-a97e-1febf351aaa2">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="6" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.getDataAtual()]]></textFieldExpression>
				<patternExpression><![CDATA[$P{EXIBIR_HORARIO}
?
"dd/MM/yyyy HH:mm"
:
"dd/MM/yyyy"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="73" width="555" height="16" uuid="eeab5f6a-95a3-4cfd-ac23-b34d409d47ec">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{TITULO_REPORT}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="78" y="1" width="394" height="15" uuid="67c4aff1-f174-441e-b54b-c763b8bae0de">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{DESC_CABECALHO_PADRAO}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="541" y="50" width="12" height="10" uuid="2e3807e3-36ce-4855-a9fc-272e93a0a36f">
					<printWhenExpression><![CDATA[$P{EXIBIR_NUMERO_PAGINAS}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Page">
				<reportElement x="522" y="50" width="19" height="10" uuid="041de54d-5f6d-42df-965f-1a579209e656">
					<printWhenExpression><![CDATA[$P{EXIBIR_NUMERO_PAGINAS}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}+"    / "]]></textFieldExpression>
			</textField>
			<image>
				<reportElement x="472" y="1" width="80" height="49" uuid="cc254ac4-4052-457f-9417-8a4907ef67e0"/>
				<imageExpression><![CDATA["br/com/ksisolucoes/gui/imagens/logo_cisamrec.png"]]></imageExpression>
			</image>
			<textField isStretchWithOverflow="true" pattern="">
				<reportElement positionType="Float" x="78" y="31" width="394" height="13" uuid="bd0095b2-faec-4145-8b5e-878f88784eec">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{CABECALHO_ADICIONAL_1}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{EXIBIR_HORARIO}
?
"dd/MM/yyyy HH:mm"
:
"dd/MM/yyyy"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="">
				<reportElement positionType="Float" x="78" y="17" width="394" height="13" uuid="fef45439-d4e1-4b81-b6b1-434ace17c38f">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{UNIDADE_ATENDIMENTO}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{EXIBIR_HORARIO}
?
"dd/MM/yyyy HH:mm"
:
"dd/MM/yyyy"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="">
				<reportElement positionType="Float" x="78" y="47" width="394" height="13" uuid="1bdafbf0-05f4-46bd-81ef-db08ae548aa9">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{CABECALHO_ADICIONAL_2}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{EXIBIR_HORARIO}
?
"dd/MM/yyyy HH:mm"
:
"dd/MM/yyyy"]]></patternExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band splitType="Stretch"/>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="20" splitType="Stretch">
			<textField>
				<reportElement x="2" y="2" width="555" height="16" uuid="c08c8837-c6c5-4a71-b9b8-83e182d2f800">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="13" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{UNIDADE_ATENDIMENTO}+ " - "+$P{TITULO_REPORT}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
