package br.com.celk.service.web.vigilancia;

import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.vigilancia.ClassificacaoGrupoEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaInspecao;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import junit.framework.TestCase;
import org.joda.time.LocalDate;
import org.junit.Before;
import org.junit.Test;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class InspecaoEstabelecimentoServiceTest extends TestCase {
    SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
    InspecaoEstabelecimentoService instanceService;

    @Before
    public void setUp() {
        instanceService = InspecaoEstabelecimentoService.getInstance();
    }

    @Test
    public void testGetClassificacaoRiscoAtividadesEstabelecimento_parametroNullNaoDeveQuebrar() {
        assertNull(instanceService.getClassificacaoRiscoAtividadesEstabelecimento(null));
        assertNull(instanceService.getClassificacaoRiscoAtividadesEstabelecimento(new ArrayList<>()));
    }

    @Test
    public void testGetUltimaInspecaoEstabelecimento_parametroNullNaoDeveQuebrar() {
        assertNull(instanceService.getUltimaInspecaoEstabelecimento(null));
        assertNull(instanceService.getUltimaInspecaoEstabelecimento(new Estabelecimento()));
    }

    @Test
    public void testGtUltimaInspecaoEstabelecimentoFormatado_parametroNullNaoDeveQuebrar() {
        RequerimentoVigilanciaInspecao requerimentoVigilanciaInspecao = new RequerimentoVigilanciaInspecao();
        requerimentoVigilanciaInspecao.setDataInspecao(null);
        assertNull(instanceService.getUltimaInspecaoEstabelecimentoFormatado(null));
        assertNull(instanceService.getUltimaInspecaoEstabelecimentoFormatado(requerimentoVigilanciaInspecao));
    }

    @Test
    public void testGtUltimaInspecaoEstabelecimentoFormatado_deveRetornarApenasDataFormatada() {
        RequerimentoVigilanciaInspecao requerimentoVigilanciaInspecao = new RequerimentoVigilanciaInspecao();
        requerimentoVigilanciaInspecao.setDataInspecao(new Date());
        String ultimaInspecaoEstabelecimentoFormatado = instanceService.getUltimaInspecaoEstabelecimentoFormatado(requerimentoVigilanciaInspecao);
        assertEquals(sdf.format(new Date()), ultimaInspecaoEstabelecimentoFormatado);
    }

    @Test
    public void testGetProximaInspecaoSugerida_prasoNaoSeAplicaDeveRetornarNull() {
        ClassificacaoGrupoEstabelecimento classificacaoGrupoEstabelecimento = new ClassificacaoGrupoEstabelecimento();
        classificacaoGrupoEstabelecimento.setPrazoInspecaoPresencial(0L);//Não se aplica
        assertNull(instanceService.getProximaInspecaoSugerida(new RequerimentoVigilanciaInspecao(), null));
        assertNull(instanceService.getProximaInspecaoSugerida(new RequerimentoVigilanciaInspecao(), classificacaoGrupoEstabelecimento));
        classificacaoGrupoEstabelecimento.setPrazoInspecaoPresencial(null);//Não se aplica
        assertNull(instanceService.getProximaInspecaoSugerida(new RequerimentoVigilanciaInspecao(), classificacaoGrupoEstabelecimento));
    }

    @Test
    public void testGetProximaInspecaoSugerida_parametroUltimaInspecaoNullNaoDeveQuebrar() {
        RequerimentoVigilanciaInspecao requerimentoVigilanciaInspecao = new RequerimentoVigilanciaInspecao();
        ClassificacaoGrupoEstabelecimento classificacaoGrupoEstabelecimento = new ClassificacaoGrupoEstabelecimento();
        requerimentoVigilanciaInspecao.setDataInspecao(null);
        classificacaoGrupoEstabelecimento.setPrazoInspecaoPresencial(0L);//Não se aplica
        assertNull(instanceService.getProximaInspecaoSugerida(null, classificacaoGrupoEstabelecimento));
        assertNull(instanceService.getProximaInspecaoSugerida(requerimentoVigilanciaInspecao, classificacaoGrupoEstabelecimento));
        assertNull(instanceService.getProximaInspecaoSugerida(requerimentoVigilanciaInspecao, null));
    }

    @Test
    public void testGetUltimaInspecaoEstabelecimentoFormatado_parametroUltimaInspecaoNulOuDataUltimaInspecaoNullNaoDeveQuebrar() {
        RequerimentoVigilanciaInspecao requerimentoVigilanciaInspecao = new RequerimentoVigilanciaInspecao();
        ClassificacaoGrupoEstabelecimento classificacaoGrupoEstabelecimento = new ClassificacaoGrupoEstabelecimento();
        requerimentoVigilanciaInspecao.setDataInspecao(null);
        requerimentoVigilanciaInspecao.setTipoInspecao(1L);
        classificacaoGrupoEstabelecimento.setPrazoInspecaoPresencial(1L);
        assertNull(instanceService.getProximaInspecaoSugerida(null, classificacaoGrupoEstabelecimento));
        assertNull(instanceService.getProximaInspecaoSugerida(requerimentoVigilanciaInspecao, classificacaoGrupoEstabelecimento));
    }

    @Test
    public void testGetUltimaInspecaoEstabelecimentoFormatado_deveRetornarPresencialTipoInspecaoAnos() {
        RequerimentoVigilanciaInspecao requerimentoVigilancia = new RequerimentoVigilanciaInspecao();
        ClassificacaoGrupoEstabelecimento classificacaoGrupoEstabelecimento = new ClassificacaoGrupoEstabelecimento();
        classificacaoGrupoEstabelecimento.setPrazoInspecaoPresencial(1L);
        classificacaoGrupoEstabelecimento.setTipoPrazoInspecao(ClassificacaoGrupoEstabelecimento.TipoPrazo.ANOS.value());

        requerimentoVigilancia.setTipoInspecao(1L);
        requerimentoVigilancia.setDataInspecao(Data.removeDias(LocalDate.now().toDate(), 365));
        assertEquals(RequerimentoVigilancia.TipoInspecao.PRESENCIAL.descricao(), instanceService.getProximaInspecaoSugerida(requerimentoVigilancia, classificacaoGrupoEstabelecimento));
        requerimentoVigilancia.setDataInspecao(Data.removeDias(LocalDate.now().toDate(), 366));
        assertEquals(RequerimentoVigilancia.TipoInspecao.PRESENCIAL.descricao(), instanceService.getProximaInspecaoSugerida(requerimentoVigilancia, classificacaoGrupoEstabelecimento));
    }

    @Test
    public void testGetUltimaInspecaoEstabelecimentoFormatado_deveRetornarDocumentalTipoInspecaoAnos() {
        RequerimentoVigilanciaInspecao requerimentoVigilancia = new RequerimentoVigilanciaInspecao();
        ClassificacaoGrupoEstabelecimento classificacaoGrupoEstabelecimento = new ClassificacaoGrupoEstabelecimento();
        classificacaoGrupoEstabelecimento.setPrazoInspecaoPresencial(1L);
        classificacaoGrupoEstabelecimento.setTipoPrazoInspecao(ClassificacaoGrupoEstabelecimento.TipoPrazo.ANOS.value());

        requerimentoVigilancia.setTipoInspecao(1L);
        requerimentoVigilancia.setDataInspecao(Data.removeDias(LocalDate.now().toDate(), 364));
        assertEquals(RequerimentoVigilancia.TipoInspecao.DOCUMENTAL.descricao(), instanceService.getProximaInspecaoSugerida(requerimentoVigilancia, classificacaoGrupoEstabelecimento));
    }

    @Test
    public void testCalcularDataProximaInspecao_quandoPrasoInspecaoPresencialForZeroDeveRetornarNull() {
        ClassificacaoGrupoEstabelecimento classificacaoGrupoEstabelecimento = new ClassificacaoGrupoEstabelecimento();
        classificacaoGrupoEstabelecimento.setPrazoInspecaoPresencial(0L);
        assertNull(instanceService.calcularDataProximaInspecao(classificacaoGrupoEstabelecimento, new Date()));
    }

    @Test
    public void testCalcularDataProximaInspecao_quandoPrasoInspecaoPresencialForNullDeveRetornarNull() {
        ClassificacaoGrupoEstabelecimento classificacaoGrupoEstabelecimento = new ClassificacaoGrupoEstabelecimento();
        classificacaoGrupoEstabelecimento.setPrazoInspecaoPresencial(null);
        assertNull(instanceService.calcularDataProximaInspecao(classificacaoGrupoEstabelecimento, new Date()));
    }

    @Test
    public void testCalcularDataProximaInspecao() {
        ClassificacaoGrupoEstabelecimento classificacaoGrupoEstabelecimento = new ClassificacaoGrupoEstabelecimento();
        classificacaoGrupoEstabelecimento.setPrazoInspecaoPresencial(1L);
        classificacaoGrupoEstabelecimento.setTipoPrazoInspecao(ClassificacaoGrupoEstabelecimento.TipoPrazo.ANOS.value());

        Date dataUltimaInspecao = new LocalDate("2020-01-01").toDate();
        Long diasProximoPraso = 1L * 365L;
        Date proximaData = Data.addDias(dataUltimaInspecao, diasProximoPraso.intValue());

        assertEquals(proximaData, instanceService.calcularDataProximaInspecao(classificacaoGrupoEstabelecimento, dataUltimaInspecao));
        assertEquals(new LocalDate("2020-12-31").toDate(), instanceService.calcularDataProximaInspecao(classificacaoGrupoEstabelecimento, dataUltimaInspecao));
    }

    @Test
    public void testGetClassificacaoPredominante_quandoApenasUmRegistroDeveRetornarEleMesmo() {
        ClassificacaoGrupoEstabelecimento classificacaoGrupoEstabelecimento = new ClassificacaoGrupoEstabelecimento();
        classificacaoGrupoEstabelecimento.setCodigo(1L);
        assertEquals(classificacaoGrupoEstabelecimento, instanceService.getClassificacaoPredominante(Arrays.asList(classificacaoGrupoEstabelecimento)));
    }

    @Test
    public void testGetClassificacaoPredominante_quandoMaisDeUmRegistroComPrasoZeroDeveRetornarOPrimeiro() {
        ArrayList<ClassificacaoGrupoEstabelecimento> classificacaoGrupoEstabelecimentoLis = new ArrayList<>();
        ClassificacaoGrupoEstabelecimento classificacao = new ClassificacaoGrupoEstabelecimento();
        classificacao.setCodigo(1L);
        classificacao.setPrazoInspecaoPresencial(0L);
        classificacaoGrupoEstabelecimentoLis.add(classificacao);
        classificacao = new ClassificacaoGrupoEstabelecimento();
        classificacao.setCodigo(2L);
        classificacao.setPrazoInspecaoPresencial(0L);
        classificacaoGrupoEstabelecimentoLis.add(classificacao);

        assertEquals(Long.valueOf(1), instanceService.getClassificacaoPredominante(classificacaoGrupoEstabelecimentoLis).getCodigo());
    }

    @Test
    public void testGetClassificacaoPredominante_quandoMaisDeUmRegistroComPrasoDiferenteDeveRetornarOMenorPraso() {
        ArrayList<ClassificacaoGrupoEstabelecimento> classificacaoGrupoEstabelecimentoLis = new ArrayList<>();
        ClassificacaoGrupoEstabelecimento classificacao = new ClassificacaoGrupoEstabelecimento();
        classificacao.setCodigo(1L);
        classificacao.setPrazoInspecaoPresencial(0L);
        classificacaoGrupoEstabelecimentoLis.add(classificacao);

        classificacao = new ClassificacaoGrupoEstabelecimento();
        classificacao.setCodigo(2L);
        classificacao.setPrazoInspecaoPresencial(1L);
        classificacaoGrupoEstabelecimentoLis.add(classificacao);

        assertEquals(Long.valueOf(2), instanceService.getClassificacaoPredominante(classificacaoGrupoEstabelecimentoLis).getCodigo());

        classificacao = new ClassificacaoGrupoEstabelecimento();
        classificacao.setCodigo(3L);
        classificacao.setPrazoInspecaoPresencial(2L);
        classificacaoGrupoEstabelecimentoLis.add(classificacao);
        assertEquals(Long.valueOf(2), instanceService.getClassificacaoPredominante(classificacaoGrupoEstabelecimentoLis).getCodigo());
    }

    @Test
    public void testGetMenorPrasoInspecao_deveRetornarOMenorPraso() {
        ArrayList<ClassificacaoGrupoEstabelecimento> classificacaoGrupoEstabelecimentoLis = new ArrayList<>();
        ClassificacaoGrupoEstabelecimento classificacao = new ClassificacaoGrupoEstabelecimento();
        classificacao.setCodigo(1L);
        classificacao.setPrazoInspecaoPresencial(1L);
        classificacaoGrupoEstabelecimentoLis.add(classificacao);

        classificacao = new ClassificacaoGrupoEstabelecimento();
        classificacao.setCodigo(2L);
        classificacao.setPrazoInspecaoPresencial(2L);
        classificacaoGrupoEstabelecimentoLis.add(classificacao);

        assertEquals(Long.valueOf(1), instanceService.getMenorPrasoInspecao(classificacaoGrupoEstabelecimentoLis).getCodigo());
    }

    @Test
    public void testIsRequerimentoAlvara_deveRetornarCorretamente() {
        TipoSolicitacao tipoSolicitacao = new TipoSolicitacao();
        tipoSolicitacao.setTipoRequerimento(0L);
        assertTrue(instanceService.isRequerimentoAlvara(tipoSolicitacao));
        assertFalse(instanceService.isRequerimentoAlvara(null));
    }

}