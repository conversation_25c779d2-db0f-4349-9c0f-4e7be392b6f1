package br.com.celk.bo.resource;

import br.com.celk.appcidadao.dto.agendamento.QueryAgendamentoHistoricoDTOParam;
import br.com.celk.bo.dto.ContatoDTO;
import br.com.celk.bo.dto.PacienteDTO;
import br.com.celk.bo.dto.PacienteFormDTO;
import br.com.celk.bo.service.ExameService;
import br.com.celk.bo.service.PacienteService;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import com.google.zxing.WriterException;

import javax.inject.Inject;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.IOException;
import java.util.Date;

/**
 * <AUTHOR>
 */

@Path("/v1/paciente")
public class PacienteResource {

    @Inject
    private PacienteService pacienteService;

    @Inject
    private ExameService exameService;

    @GET
    @Path("/{pacienteId}")
    @Produces({MediaType.APPLICATION_JSON})
    public Response getPacienteById(@PathParam("pacienteId") Long pacienteId) {

        Response response;

        try {
            response = Response.ok(pacienteService.getPacienteById(pacienteId)).build();
        } catch (ValidacaoException | DAOException e) {
            Loggable.log.warn(e.getMessage(), e);
            response = Response.noContent().build();
        }

        return response;
    }

    @GET
    @Path("/{pacienteId}/medicamentoContinuo")
    @Produces({MediaType.APPLICATION_JSON})
    public Response getMedicamentosContinuosByPacienteId(@PathParam("pacienteId") Long pacienteId) {

        Response response;

        try {
            response = Response.ok(pacienteService.getMedicamentoByPacienteId(pacienteId)).build();
        } catch (ValidacaoException | DAOException e) {
            Loggable.log.warn(e.getMessage(), e);
            response = Response.noContent().build();
        }

        return response;
    }

    @GET
    @Path("/{pacienteId}/exame")
    @Produces({MediaType.APPLICATION_JSON})
    public Response getExameByPacienteId(@PathParam("pacienteId") Long pacienteId) {
        Response response;

        try {
            response = Response.ok(pacienteService.getExamesByPacienteId(pacienteId)).build();
        } catch (ValidacaoException | DAOException e) {
            Loggable.log.warn(e.getMessage(), e);
            response = Response.noContent().build();
        }

        return response;
    }

    @GET
    @Path("/{pacienteId}/agendamentos")
    @Produces({MediaType.APPLICATION_JSON})
    public Response getAgendamentosByPaciente(@PathParam("pacienteId") Long pacienteId,
                                              @QueryParam("dataInicial") String dataInicial,
                                              @QueryParam("dataFinal") String dataFinal,
                                              @QueryParam("status") Long status)
            throws ValidacaoException, DAOException {

        try {
            return Response.ok(pacienteService.getAgendamentosByPaciente(pacienteId, dataInicial, dataFinal, status)).build();
        } catch (ValidacaoException e) {
            Loggable.log.warn(e.getMessage(), e);
            return Response.status(Response.Status.BAD_REQUEST).entity(e.getMessage()).build();
        }
    }

    @GET
    @Path("/contato")
    @Produces({MediaType.APPLICATION_JSON})
    public Response getContato(@QueryParam("cpf") String cpf, @QueryParam("cns") Long cns) {
        Response response;

        try {
            ContatoDTO contato = pacienteService.getContato(cpf, cns);
            response = contato != null ? Response.ok(contato).build() : Response.noContent().build();
        } catch (ValidacaoException e) {
            Loggable.log.warn(e.getMessage(), e);
            response = Response.status(Response.Status.BAD_REQUEST).entity(e.getMessage()).build();
        }

        return response;
    }

    @POST
    @Path("/{pacienteId}/liberar")
    @Produces({MediaType.APPLICATION_JSON})
    public Response liberarContato(@PathParam("pacienteId") Long pacienteId) throws ValidacaoException, DAOException {
        Response response;

        try {
            pacienteService.liberarContato(pacienteId);
            response = Response.ok().build();
        } catch (ValidacaoException e) {
            Loggable.log.warn(e.getMessage(), e);
            response = Response.status(Response.Status.BAD_REQUEST).entity(e.getMessage()).build();
        }

        return response;
    }

    @PUT
    @Path("/{pacienteId}/dadosVacinacao")
    @Produces({MediaType.APPLICATION_JSON})
    public Response atualizarPaciente(@PathParam("pacienteId") Long pacienteId, PacienteDTO pacienteDTO) throws ValidacaoException, DAOException {
        Response response;

        pacienteDTO.setCodigo(pacienteId);
        try {
            pacienteService.atualizarPaciente(pacienteDTO);
            response = Response.ok().build();
        } catch (ValidacaoException e) {
            Loggable.log.warn(e.getMessage(), e);
            response = Response.status(Response.Status.BAD_REQUEST).entity(e.getMessage()).build();
        }

        return response;
    }


    @PUT
    @Path("/cadastroForms")
    @Produces({MediaType.APPLICATION_JSON})
    public Response putCadastroForms(PacienteFormDTO pacienteFomrDTO) throws DAOException, WriterException, IOException {
        Response response;
        try {
            pacienteService.cadastrarPaciente(pacienteFomrDTO);
            response = Response.ok().build();
        } catch (ValidacaoException e) {
            Loggable.log.warn(e.getMessage(), e);
            response = Response.status(Response.Status.BAD_REQUEST).entity(e.getMessage()).build();
        }

        return response;
    }
}
