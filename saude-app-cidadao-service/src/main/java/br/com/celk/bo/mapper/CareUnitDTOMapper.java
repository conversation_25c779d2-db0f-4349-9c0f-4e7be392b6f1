package br.com.celk.bo.mapper;

import br.com.celk.appcidadao.dto.agenda.CareUnitDTO;
import br.com.ksisolucoes.vo.basico.Empresa;

public class CareUnitDTOMapper {
    CareUnitDTO careUnitDTO;

    public CareUnitDTOMapper fromEmpresa(Empresa empresa) {
        careUnitDTO = new CareUnitDTO();
        careUnitDTO.setCareUnitId(empresa.getCodigo());
        careUnitDTO.setCareUnitName(empresa.getDescricao());
        careUnitDTO.setComplement(empresa.getComplemento());
        careUnitDTO.setDescription(empresa.getDescricao());
        careUnitDTO.setNeighborhood(empresa.getBairro());
        careUnitDTO.setNumber(empresa.getNumero());
        careUnitDTO.setPhone(empresa.getTelefone());
        careUnitDTO.setStreet(empresa.getRua());
        return this;
    }

    public CareUnitDTO map() {
        return careUnitDTO;
    }
}
