package br.com.celk.bo.service;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;

import javax.ejb.Stateless;
import javax.inject.Inject;

import static br.com.ksisolucoes.util.VOUtils.montarPath;

@Stateless
public class ProcedimentoService {
    @Inject
    LoadManagerService loadManagerService;
    public TipoProcedimento findTipoProcedimentoOnlyCodigoById(Long codigo) {
        return getTipoProcedimentoLoadManager()
                .addProperty(montarPath(TipoProcedimento.PROP_CODIGO))
                .addProperty(montarPath(TipoProcedimento.PROP_PROCEDIMENTO, Procedimento.PROP_CODIGO))
                .setId(codigo)
                .startLeitura().getVO();
    }

    private LoadManager getTipoProcedimentoLoadManager() {
        return loadManagerService.getInstance(TipoProcedimento.class);
    }
}
