package br.com.celk.bo.util;

import br.com.celk.bo.service.LoadManagerService;
import br.com.ksisolucoes.bo.command.LoadManager;
import org.mockito.Mockito;

import static org.mockito.ArgumentMatchers.any;

public class MockLoadManagerServiceUtil {
    public static LoadManager mockLoadManager(LoadManager loadManager) {
        Mockito.lenient().when(loadManager.startLeitura()).thenReturn(loadManager);
        Mockito.lenient().when(loadManager.start()).thenReturn(loadManager);
        Mockito.lenient().when(loadManager.addProperty(any())).thenReturn(loadManager);
        Mockito.lenient().when(loadManager.addProperties(any())).thenReturn(loadManager);
        Mockito.lenient().when(loadManager.addParameter(any())).thenReturn(loadManager);
        Mockito.lenient().when(loadManager.setId(any())).thenReturn(loadManager);
        Mockito.lenient().when(loadManager.setMaxResults(any())).thenReturn(loadManager);
        return loadManager;
    }

    public static void mockHqlProperties(LoadManagerService loadManagerService) {
        Mockito
                .lenient()
                .when(loadManagerService.getHqlProperties(Mockito.any()))
                .thenReturn(new String[0]);
        Mockito
                .lenient()
                .when(loadManagerService.getHqlProperties(Mockito.any(), Mockito.any()))
                .thenReturn(new String[0]);
    }

}
