# reCAPTCHA v3 - Cache Centralizado e Inteligente

## 🎯 Objetivo Alcançado

Moveu a lógica de cache com expiração para dentro do `ReCaptchaV3Util`, tornando-a **automática e reutilizável** para todas as páginas que usam reCAPTCHA v3.

## ✅ Benefícios da Centralização

### **1. Reutilização Automática**
- ✅ **Todas as páginas** se beneficiam automaticamente
- ✅ **Sem código duplicado** em cada página
- ✅ **Implementação única** e consistente

### **2. Manutenção Simplificada**
- ✅ **Um local** para ajustar configurações
- ✅ **Logs centralizados** e padronizados
- ✅ **Debugging facilitado**

### **3. Performance Otimizada**
- ✅ **Cache global** entre diferentes páginas
- ✅ **Limpeza automática** de entradas expiradas
- ✅ **Estatísticas** de uso do cache

## 🔧 Implementação Detalhada

### **Classe de Cache Interna:**
```java
private static class ValidationCache {
    private final ValidationResult result;
    private final long timestamp;
    
    public ValidationCache(ValidationResult result) {
        this.result = result;
        this.timestamp = System.currentTimeMillis();
    }
    
    public boolean isExpired() {
        return (System.currentTimeMillis() - timestamp) > CACHE_DURATION_MS;
    }
    
    public long getRemainingTimeSeconds() {
        long elapsed = System.currentTimeMillis() - timestamp;
        return Math.max(0, (CACHE_DURATION_MS - elapsed) / 1000);
    }
}
```

### **Cache Thread-Safe:**
```java
// Cache global thread-safe
private static final Map<String, ValidationCache> validationCache = new ConcurrentHashMap<>();
private static final long CACHE_DURATION_MS = 90000; // 90 segundos
```

### **Chave de Cache Inteligente:**
```java
private static String generateCacheKey(String token, String expectedAction, double minScore) {
    // Usar hash do token para evitar armazenar token completo
    int tokenHash = token != null ? token.hashCode() : 0;
    return String.format("token_%d_action_%s_score_%.2f", tokenHash, expectedAction, minScore);
}
```

## 📋 Métodos Disponíveis

### **1. Validação com Cache (Padrão):**
```java
// Usa cache automaticamente
ValidationResult result = ReCaptchaV3Util.validateToken(token, action, minScore);
ValidationResult result = ReCaptchaV3Util.validateComponent(component);
```

### **2. Validação Direta (Sem Cache):**
```java
// Força validação direta com Google
ValidationResult result = ReCaptchaV3Util.validateTokenDirect(token, action, minScore);
```

### **3. Métodos Utilitários:**
```java
// Limpar cache (útil para testes)
ReCaptchaV3Util.clearCache();

// Estatísticas do cache
String stats = ReCaptchaV3Util.getCacheStats();
// Retorna: "Cache stats: 5 total, 1 expired, 4 active"
```

## 🔄 Fluxo de Funcionamento

### **Primeira Validação:**
```
1. validateComponent(component) chamado
2. Cache miss - entrada não existe
3. validateTokenDirect() executado
4. Resultado cacheado por 90 segundos
5. Resultado retornado
```

### **Validações Subsequentes:**
```
1. validateComponent(component) chamado
2. Cache hit - entrada encontrada e válida
3. Resultado retornado do cache (sem chamar Google)
```

### **Cache Expirado:**
```
1. validateComponent(component) chamado
2. Cache hit - entrada encontrada mas expirada
3. Entrada removida do cache
4. validateTokenDirect() executado
5. Novo resultado cacheado
```

## 📊 Logs Detalhados

### **Cache Miss (Primeira Vez):**
```
DEBUG ReCaptchaV3Util - Cache miss: Executando validação pela primeira vez
DEBUG ReCaptchaV3Util - Google response: {"success": true, "score": 0.9, "action": "search"}
DEBUG ReCaptchaV3Util - Resultado cacheado por 90 segundos
```

### **Cache Hit (Válido):**
```
DEBUG ReCaptchaV3Util - Cache hit: Token válido por mais 45 segundos
```

### **Cache Expirado:**
```
DEBUG ReCaptchaV3Util - Cache expirado após 95 segundos, removendo e revalidando...
DEBUG ReCaptchaV3Util - Google response: {"success": true, "score": 0.8, "action": "search"}
DEBUG ReCaptchaV3Util - Resultado cacheado por 90 segundos
```

### **Limpeza Automática:**
```
DEBUG ReCaptchaV3Util - Removendo entrada expirada do cache: token_123456_action_search_score_0.50
```

## 🔧 Configuração e Personalização

### **Duração do Cache:**
```java
// Configuração atual (recomendada)
private static final long CACHE_DURATION_MS = 90000; // 90 segundos

// Outras opções
// private static final long CACHE_DURATION_MS = 60000;  // 60s (conservador)
// private static final long CACHE_DURATION_MS = 110000; // 110s (agressivo)
```

### **Configuração Dinâmica (Futura):**
```java
// Permitir configuração via parâmetro do sistema
private static long getCacheDuration() {
    try {
        String duration = BOFactory.getBO(CommomFacade.class)
            .getParametro("ReCaptchaCacheDurationSeconds");
        return Long.parseLong(duration) * 1000;
    } catch (Exception e) {
        return 90000; // Padrão 90s
    }
}
```

## 📋 Uso Simplificado nas Páginas

### **Antes (Complexo):**
```java
public class MinhaPage extends WebPage {
    // Cache local em cada página
    private Boolean recaptchaValidado = false;
    private Long recaptchaValidadoTimestamp = null;
    private static final long CACHE_DURATION_MS = 90000;
    
    private void validarRecaptcha() {
        // Lógica complexa de cache local
        long currentTime = System.currentTimeMillis();
        boolean cacheValido = recaptchaValidado && 
                              recaptchaValidadoTimestamp != null && 
                              (currentTime - recaptchaValidadoTimestamp) < CACHE_DURATION_MS;
        
        if (!cacheValido) {
            // Validação + cache manual
            ReCaptchaV3Util.ValidationResult result = ReCaptchaV3Util.validateComponent(reCaptcha);
            recaptchaValidado = true;
            recaptchaValidadoTimestamp = currentTime;
        }
    }
}
```

### **Depois (Simples):**
```java
public class MinhaPage extends WebPage {
    private void validarRecaptcha() {
        // Cache automático no ReCaptchaV3Util
        ReCaptchaV3Util.ValidationResult result = ReCaptchaV3Util.validateComponent(reCaptcha);
        if (!result.isSuccess()) {
            throw new ValidacaoException("reCAPTCHA inválido");
        }
    }
}
```

## 🎯 Vantagens da Nova Implementação

### **Para Desenvolvedores:**
- ✅ **Código mais limpo** nas páginas
- ✅ **Sem duplicação** de lógica de cache
- ✅ **Implementação automática** em todas as páginas
- ✅ **Debugging centralizado**

### **Para Performance:**
- ✅ **Cache global** entre páginas
- ✅ **Menos requisições** ao Google
- ✅ **Limpeza automática** de memória
- ✅ **Thread-safe** para alta concorrência

### **Para Manutenção:**
- ✅ **Configuração única** para todo o sistema
- ✅ **Logs padronizados** e estruturados
- ✅ **Estatísticas centralizadas**
- ✅ **Fácil ajuste** de parâmetros

## 🔍 Monitoramento e Debug

### **Verificar Estatísticas:**
```java
// Em qualquer lugar do código
String stats = ReCaptchaV3Util.getCacheStats();
System.out.println(stats);
// Output: "Cache stats: 12 total, 3 expired, 9 active"
```

### **Limpar Cache (Testes):**
```java
// Para testes ou reset
ReCaptchaV3Util.clearCache();
```

### **Logs Automáticos:**
Todos os logs são automaticamente gerados:
- Cache hits/misses
- Expirações
- Limpezas automáticas
- Estatísticas de uso

## ✅ Resultado Final

### **Implementação Perfeita:**
- [x] **Cache centralizado** no ReCaptchaV3Util
- [x] **Reutilização automática** em todas as páginas
- [x] **Thread-safe** para alta concorrência
- [x] **Limpeza automática** de entradas expiradas
- [x] **Logs detalhados** para monitoramento
- [x] **Configuração única** para todo o sistema

### **Páginas Simplificadas:**
- [x] **Código limpo** sem lógica de cache
- [x] **Implementação automática** de cache
- [x] **Sem duplicação** de código
- [x] **Manutenção facilitada**

### **Performance Otimizada:**
- [x] **Cache global** entre diferentes páginas
- [x] **Menos requisições** ao Google reCAPTCHA
- [x] **Resposta mais rápida** para usuários
- [x] **Uso eficiente** de memória

**A implementação agora é completamente centralizada, automática e reutilizável!** 🎉

### **Como Usar em Novas Páginas:**

```java
// Simplesmente chame - cache é automático!
ReCaptchaV3Util.ValidationResult result = ReCaptchaV3Util.validateComponent(reCaptcha);
```

**Sem necessidade de implementar cache em cada página!** 🚀
