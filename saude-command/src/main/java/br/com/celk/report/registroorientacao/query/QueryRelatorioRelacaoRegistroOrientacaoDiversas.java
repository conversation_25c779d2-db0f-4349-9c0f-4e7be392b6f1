package br.com.celk.report.registroorientacao.query;

import br.com.celk.report.registroorientacao.interfaces.dto.RelatorioRelacaoRegistroOrientacaoDiversasDTO;
import br.com.celk.report.registroorientacao.interfaces.dto.RelatorioRelacaoRegistroOrientacaoDiversasDTOParam;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.hibernate.Query;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioRelacaoRegistroOrientacaoDiversas extends CommandQuery<QueryRelatorioRelacaoRegistroOrientacaoDiversas> implements ITransferDataReport<RelatorioRelacaoRegistroOrientacaoDiversasDTOParam, RelatorioRelacaoRegistroOrientacaoDiversasDTO> {

    private RelatorioRelacaoRegistroOrientacaoDiversasDTOParam param;
    private List<RelatorioRelacaoRegistroOrientacaoDiversasDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        if (RelatorioRelacaoRegistroOrientacaoDiversasDTOParam.FormaApresentacao.PROFISSIONAL.equals(param.getFormaApresentacao())) {
            hql.addToSelectAndGroup("p.codigo", "profissional.codigo");
            hql.addToSelectAndGroupAndOrder("p.nome", "profissional.nome");
        } else if (RelatorioRelacaoRegistroOrientacaoDiversasDTOParam.FormaApresentacao.DATA_ORIENTACAO.equals(param.getFormaApresentacao())) {
            hql.addToSelectAndGroupAndOrder("ro.dataOrientacao", "registroOrientacao.dataOrientacao");
        }
        
        hql.addToSelectAndGroup("p.codigo", "profissional.codigo");
        hql.addToSelectAndGroupAndOrder("p.nome", "profissional.nome");
        
        if (RelatorioRelacaoRegistroOrientacaoDiversasDTOParam.TipoRelatorio.DETALHADO.equals(param.getTipoRelatorio())) {
            hql.addToSelectAndGroup("ro.dataOrientacao", "registroOrientacao.dataOrientacao");
            hql.addToSelectAndGroupAndOrder("ro.descricaoOrientado", "registroOrientacao.descricaoOrientado");
        }
        
        hql.addToSelect("count(ro.codigo)", "quantidade");
        
        hql.setTypeSelect(RelatorioRelacaoRegistroOrientacaoDiversasDTO.class.getName());
        hql.addToFrom("RegistroOrientacao ro"
                +"  left join ro.profissional p");

        hql.addToWhereWhithAnd("p in ", param.getProfissional());
        hql.addToWhereWhithAnd("ro.dataOrientacao ", param.getPeriodo());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("ro.descricaoOrientado", param.getOrientado()));

        if (CollectionUtils.isNotNullEmpty(param.getProfissional()) && CollectionUtils.isNotNullEmpty(param.getTabelaCbo())) {
            hql.addToWhereWhithAnd("(exists (select 1 from ProfissionalCargaHoraria pch "
                    + " where pch.profissional.codigo = " + param.getProfissional().get(0).getCodigo()
                    + " and pch.tabelaCbo.cbo = :cbo))");
        }
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        super.setParameters(hql, query);
        if (CollectionUtils.isNotNullEmpty(param.getProfissional()) && CollectionUtils.isNotNullEmpty(param.getTabelaCbo())) {
            query.setParameter("cbo", param.getTabelaCbo().get(0).getCbo());
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioRelacaoRegistroOrientacaoDiversasDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelatorioRelacaoRegistroOrientacaoDiversasDTOParam param) {
        this.param = param;
    }

}