<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_medicamentos_uso_continuo_detalhado" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="1f0ca790-68de-470a-bf61-0b41027e7d35">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.1435888100000016"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo"/>
	<import value="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<import value="br.com.ksisolucoes.vo.basico.Empresa"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="formaApresentacao" class="br.com.celk.unidadesaude.receita.RelacaoMedicamentosUsoContinuoDTOParam.FormaApresentacao"/>
	<field name="medicamentoPaciente" class="br.com.ksisolucoes.vo.prontuario.basico.MedicamentoPaciente"/>
	<field name="dataUltimaReceita" class="java.util.Date"/>
	<field name="dataUltimaDispensacao" class="java.util.Date"/>
	<field name="descricaoMedicamento" class="java.lang.String"/>
	<group name="FormaApresentacao" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$P{formaApresentacao}.equals(br.com.celk.unidadesaude.receita.RelacaoMedicamentosUsoContinuoDTOParam.FormaApresentacao.PACIENTE)
?
    $F{medicamentoPaciente}.getUsuarioCadsus().getCodigo()
:
    $P{formaApresentacao}.equals(br.com.celk.unidadesaude.receita.RelacaoMedicamentosUsoContinuoDTOParam.FormaApresentacao.MEDICAMENTO)
    ?
        Coalesce.asLong($F{medicamentoPaciente}.getProduto() != null ? $F{medicamentoPaciente}.getProduto().getCodigo() : null, 0L)
    :
        $P{formaApresentacao}.equals(br.com.celk.unidadesaude.receita.RelacaoMedicamentosUsoContinuoDTOParam.FormaApresentacao.CID)
        ?
            Coalesce.asString($F{medicamentoPaciente}.getCid() != null ? $F{medicamentoPaciente}.getCid().getCodigo() : null, "0")
        :
            $P{formaApresentacao}.equals(br.com.celk.unidadesaude.receita.RelacaoMedicamentosUsoContinuoDTOParam.FormaApresentacao.TIPO_RECEITA)
            ?
                $F{medicamentoPaciente}.getTipoReceita().getCodigo()
            :
                $P{formaApresentacao}.equals(br.com.celk.unidadesaude.receita.RelacaoMedicamentosUsoContinuoDTOParam.FormaApresentacao.LOCAL_FORNECIMENTO)
                ?
                    Coalesce.asLong($F{medicamentoPaciente}.getLocalFornecimento() != null ? $F{medicamentoPaciente}.getLocalFornecimento().getCodigo() : null, 0L)
                :
                    null]]></groupExpression>
		<groupHeader>
			<band height="42">
				<rectangle radius="5">
					<reportElement mode="Transparent" x="0" y="11" width="802" height="13" uuid="9a736b87-bcbf-41ed-aa36-d77618238c35"/>
				</rectangle>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="0" y="11" width="802" height="13" uuid="6d5efa6f-b032-4f7b-9f85-3d425c48220a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{formaApresentacao}.equals(br.com.celk.unidadesaude.receita.RelacaoMedicamentosUsoContinuoDTOParam.FormaApresentacao.PACIENTE)
?
    Bundle.getStringApplication("rotulo_paciente")+": "+$F{medicamentoPaciente}.getUsuarioCadsus().getNome()
:
    $P{formaApresentacao}.equals(br.com.celk.unidadesaude.receita.RelacaoMedicamentosUsoContinuoDTOParam.FormaApresentacao.MEDICAMENTO)
    ?
        Bundle.getStringApplication("rotulo_produto")+": "+(($F{medicamentoPaciente}.getProduto() != null && $F{medicamentoPaciente}.getProduto().getDescricao() != null) ? $F{medicamentoPaciente}.getProduto().getDescricao() : Bundle.getStringApplication("rotulo_sem_cadastro"))
    :
        $P{formaApresentacao}.equals(br.com.celk.unidadesaude.receita.RelacaoMedicamentosUsoContinuoDTOParam.FormaApresentacao.CID)
        ?
            Bundle.getStringApplication("rotulo_cid")+": "+(($F{medicamentoPaciente}.getCid() != null && $F{medicamentoPaciente}.getCid().getDescricao() != null) ? $F{medicamentoPaciente}.getCid().getDescricaoFormatado() : Bundle.getStringApplication("rotulo_nao_informado"))
        :
            $P{formaApresentacao}.equals(br.com.celk.unidadesaude.receita.RelacaoMedicamentosUsoContinuoDTOParam.FormaApresentacao.TIPO_RECEITA)
            ?
                Bundle.getStringApplication("rotulo_tipo_receita")+": "+$F{medicamentoPaciente}.getTipoReceita().getDescricao()
            :
                $P{formaApresentacao}.equals(br.com.celk.unidadesaude.receita.RelacaoMedicamentosUsoContinuoDTOParam.FormaApresentacao.LOCAL_FORNECIMENTO)
                ?
                    Bundle.getStringApplication("rotulo_local_fornecimento")+": "+(($F{medicamentoPaciente}.getLocalFornecimento() != null && $F{medicamentoPaciente}.getLocalFornecimento().getDescricao() != null) ? $F{medicamentoPaciente}.getLocalFornecimento().getDescricao() : Bundle.getStringApplication("rotulo_nao_informado"))
                :
                    null]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="39" width="802" height="1" uuid="93d64167-0e24-4529-b2dd-95228b62ae58"/>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="703" y="28" width="52" height="12" uuid="419c81ae-0a4b-4223-aaf8-203cfafbeb42"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[/*ult Receita*/Bundle.getStringApplication("rotulo_ultima_receita_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="252" y="28" width="219" height="12" uuid="23a519bc-2df3-4ce0-9141-a04105bad957"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Medicamento*/Bundle.getStringApplication("rotulo_medicamento")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="476" y="28" width="16" height="12" uuid="f0673ba2-be6b-4083-88e7-a80aa1aa69da"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[/*UN*/Bundle.getStringApplication("rotulo_un")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="493" y="28" width="57" height="12" uuid="660fd761-5bc4-4785-b82a-d3e86c23bdf6"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Qtd. Prescrita*/Bundle.getStringApplication("rotulo_qtd_prescrita_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="594" y="28" width="102" height="12" uuid="24252ae4-f248-4c98-b760-9ec902a5f7cf"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Local Fornecimento*/Bundle.getStringApplication("rotulo_local_fornecimento")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="4" y="28" width="243" height="12" uuid="63ba4489-dd78-46b7-b8fc-60400ee91e43"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Paciente*/Bundle.getStringApplication("rotulo_paciente")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="759" y="28" width="43" height="12" uuid="7f098c08-1074-4da5-81ba-bf969e68b1d1"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Ultima Dispensacao*/Bundle.getStringApplication("rotulo_ultima_dispensacao_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="557" y="28" width="37" height="12" uuid="f6caf0cb-c50a-41d0-8188-3eda2dd07a6d"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[/*CID*/Bundle.getStringApplication("rotulo_cid")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<detail>
		<band height="12" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="4" y="0" width="243" height="12" uuid="92e5087f-7c97-4226-b1ed-a01bf823ad6f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{medicamentoPaciente}.getUsuarioCadsus().getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="703" y="0" width="52" height="12" uuid="c7dd5b83-4337-4a49-9575-69ed266c9df1"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.formatar($F{dataUltimaReceita})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="252" y="0" width="219" height="12" uuid="79e5e68f-b593-4966-ad50-cef949ebad0a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoMedicamento}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="476" y="0" width="16" height="12" uuid="7dc1f2db-835c-46aa-80e1-d894383ea151"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{medicamentoPaciente}.getUnidade().getUnidade()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="493" y="0" width="57" height="12" uuid="a8e63d50-0f10-4e69-a03a-fd986dd21f37"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{medicamentoPaciente}.getQuantidadePrescrita()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="594" y="0" width="102" height="12" uuid="cbbf8077-95cf-4889-bc09-cc40886b5037"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{medicamentoPaciente}.getLocalFornecimento().getDescricao()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="759" y="0" width="43" height="12" uuid="bc45d6a4-eeaf-496c-80c3-f4f3daac687b"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.formatar($F{dataUltimaDispensacao})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="557" y="0" width="37" height="12" uuid="b6a1254f-0ffb-48b4-a53c-3ad07654cc0e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{medicamentoPaciente}.getCid().getCodigo()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
