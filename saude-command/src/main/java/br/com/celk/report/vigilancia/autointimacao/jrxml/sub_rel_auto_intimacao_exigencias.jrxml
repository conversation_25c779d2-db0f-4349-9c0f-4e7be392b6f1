<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="sub_rel_auto_intimacao_exigencias" columnDirection="RTL" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="56eab6e4-5301-4f11-a723-bfb165341f5f">
	<property name="ireport.zoom" value="1.815000000000002"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<field name="exigencia" class="java.lang.String"/>
	<field name="dataCumprimentoPrazo" class="java.util.Date"/>
	<field name="prazo" class="java.lang.Long"/>
	<field name="flagImediato" class="java.lang.Long"/>
	<field name="lei" class="java.lang.String"/>
	<field name="observacaoPrazo" class="java.lang.String"/>
	<field name="descricaoPrazoDias" class="java.lang.String"/>
	<detail>
		<band height="14">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="2" y="0" width="28" height="14" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true" uuid="db1281b1-c372-488d-93f4-70251b1749d4"/>
				<box topPadding="1" bottomPadding="2" rightPadding="2"/>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT} + ")"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="33" y="0" width="518" height="14" isRemoveLineWhenBlank="true" uuid="b956d7c6-b7b5-4070-addc-145bc8e32480"/>
				<box topPadding="1" bottomPadding="2"/>
				<textElement textAlignment="Justified" markup="html">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{exigencia}]]></textFieldExpression>
			</textField>
		</band>
		<band height="14">
			<textField isBlankWhenNull="true">
				<reportElement x="33" y="0" width="65" height="14" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true" uuid="8b568058-c9f3-4cb5-89b0-dac97c4b1320"/>
				<box topPadding="1" bottomPadding="2" rightPadding="0"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_legislacao").toUpperCase() + ":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="98" y="0" width="453" height="14" isRemoveLineWhenBlank="true" uuid="192fdf3a-a80c-4938-bb36-f94c84c72af5"/>
				<box topPadding="1" bottomPadding="2"/>
				<textElement textAlignment="Justified">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{lei}]]></textFieldExpression>
			</textField>
		</band>
		<band height="14">
			<textField isBlankWhenNull="true">
				<reportElement x="33" y="0" width="65" height="14" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true" uuid="b71c336c-227c-4447-8cd5-a27af145bb69"/>
				<box topPadding="1" bottomPadding="2" rightPadding="0"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_observacao").toUpperCase() + ":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="98" y="0" width="453" height="14" isRemoveLineWhenBlank="true" uuid="a032d526-eac3-4af5-8692-6a19b5a250e7"/>
				<box topPadding="1" bottomPadding="2"/>
				<textElement textAlignment="Justified">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{observacaoPrazo}]]></textFieldExpression>
			</textField>
		</band>
		<band height="18">
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="215" y="0" width="158" height="14" isRemoveLineWhenBlank="true" uuid="55d36af8-417d-4f15-b9b1-84382124dcde">
					<printWhenExpression><![CDATA[$F{descricaoPrazoDias} != null]]></printWhenExpression>
				</reportElement>
				<box topPadding="1" bottomPadding="2"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoPrazoDias}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="FixRelativeToBottom" x="4" y="14" width="547" height="1" forecolor="#DFDFDF" uuid="ed25eb1e-7ed2-4e1b-aa50-833953e75767"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement x="33" y="0" width="182" height="14" isRemoveLineWhenBlank="true" uuid="8af96cf4-6af2-47b6-8f54-7dbd9276761a"/>
				<box topPadding="1" bottomPadding="2"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_prazo_cumprimento_exigencia").toUpperCase() + ":"]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
