package br.com.celk.report.emprestimo.query;

import br.com.celk.bo.emprestimo.interfaces.dto.ResumoEmprestimosDTO;
import br.com.celk.bo.emprestimo.interfaces.dto.ResumoEmprestimosDTOParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryResumoEmprestimos extends CommandQuery implements ITransferDataReport<ResumoEmprestimosDTOParam, ResumoEmprestimosDTO> {

    private ResumoEmprestimosDTOParam param;
    private List<ResumoEmprestimosDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(ResumoEmprestimosDTO.class.getName());

        hql.addToSelect("sum(coalesce(lancamentoEmprestimoItem.quantidade, 0))", "quantidade");
        hql.addToSelect("sum(coalesce(lancamentoEmprestimoItem.quantidadeDevolvida, 0))", "quantidadeDevolvida");

        hql.addToSelectAndGroup("produto.descricao", "produto");
        hql.addToSelectAndGroup("unidade.unidade", "unidade");

        hql.addToFrom("LancamentoEmprestimoItem lancamentoEmprestimoItem "
                + "left join lancamentoEmprestimoItem.produto produto "
                + "left join lancamentoEmprestimoItem.lancamentoEmprestimo lancamentoEmprestimo "
                + "left join lancamentoEmprestimo.empresa empresa "
                + "left join lancamentoEmprestimo.empresaEmprestimo empresaEmprestimo "
                + "left join lancamentoEmprestimo.usuarioCadsus usuarioCadsus "
                + "left join produto.unidade unidade "
                + "join lancamentoEmprestimo.tipoEmprestimo tipoEmprestimo");

        hql.addToWhereWhithAnd("produto = ", param.getProduto());
        hql.addToWhereWhithAnd("empresa", param.getEstabelecimento());
        hql.addToWhereWhithAnd("usuarioCadsus = ", param.getPaciente());
        hql.addToWhereWhithAnd("tipoEmprestimo = ", param.getTipoEmprestimo());
        hql.addToWhereWhithAnd("empresaEmprestimo = ", param.getEstabelecimentoEmprestimo());
        hql.addToWhereWhithAnd("lancamentoEmprestimoItem.status = ", this.param.getSituacao());
        hql.addToWhereWhithAnd("lancamentoEmprestimo.dataEmprestimo", param.getPeriodo());
        hql.addToWhereWhithAnd("tipoEmprestimo.tipoEmprestimo = ", param.getTipoOperacao());

        if (this.param.isViewFormaApresentacao()) {
            if (ResumoEmprestimosDTOParam.FormaApresentacao.TIPO_EMPRESTIMO.equals(this.param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("tipoEmprestimo.descricaoTipoEmprestimo", "descricaoTipoEmprestimo");
            } else if (ResumoEmprestimosDTOParam.FormaApresentacao.ESTABELECIMENTO_EMPRESTIMO.equals(this.param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("empresaEmprestimo.descricao", "empresaEmprestimo");
                hql.addToWhereWhithAnd("lancamentoEmprestimo.empresaEmprestimo is not null");
            } else if (ResumoEmprestimosDTOParam.FormaApresentacao.PACIENTE.equals(this.param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("usuarioCadsus.nome", "paciente");
                hql.addToSelectAndGroupAndOrder("usuarioCadsus.dataNascimento", "dataNascimento");
                hql.addToWhereWhithAnd("lancamentoEmprestimo.usuarioCadsus is not null");
            } else if (ResumoEmprestimosDTOParam.FormaApresentacao.SIGNATARIO.equals(this.param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("empresa.descricao", "estabelecimento");
                hql.addToSelectAndGroupAndOrder("usuarioCadsus.nome", "paciente");
            }
        }

        if (ResumoEmprestimosDTOParam.TipoResumo.TIPO_EMPRESTIMO.equals(this.param.getTipoResumo())) {
            hql.addToSelectAndGroupAndOrder("tipoEmprestimo.descricaoTipoEmprestimo", "descricaoTipoEmprestimo");
        } else if (ResumoEmprestimosDTOParam.TipoResumo.ESTABELECIMENTO_EMPRESTIMO.equals(this.param.getTipoResumo())) {
            hql.addToSelectAndGroupAndOrder("empresaEmprestimo.descricao", "empresaEmprestimo");
            hql.addToWhereWhithAnd("lancamentoEmprestimo.empresaEmprestimo is not null");
        } else if (ResumoEmprestimosDTOParam.TipoResumo.PACIENTE.equals(this.param.getTipoResumo())) {
            hql.addToSelectAndGroupAndOrder("usuarioCadsus.nome", "paciente");
            hql.addToWhereWhithAnd("lancamentoEmprestimo.usuarioCadsus is not null");
        } else if (ResumoEmprestimosDTOParam.TipoResumo.SIGNATARIO.equals(this.param.getTipoResumo())) {
            hql.addToSelectAndGroupAndOrder("usuarioCadsus.nome", "paciente");
            hql.addToSelectAndGroupAndOrder("empresa.descricao", "estabelecimento");
        }

        if (param.getOrdenacao().equals(ResumoEmprestimosDTOParam.Ordenacao.PRODUTO)) {
            hql.addToOrder("produto.descricao" + param.getTipoOrdenacao().getCommand());
        } else if (param.getOrdenacao().equals(ResumoEmprestimosDTOParam.Ordenacao.QUANTIDADE)) {
            hql.addToOrder("1" + param.getTipoOrdenacao().getCommand());
            hql.addToOrder("2" + param.getTipoOrdenacao().getCommand());
        }
    }

    @Override
    public void setDTOParam(ResumoEmprestimosDTOParam param) {
        this.param = param;
    }

    @Override
    public Collection getResult() {
        return this.result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

}
