package br.com.celk.report.vigilancia.autoinfracao;

import br.com.celk.report.vigilancia.autoinfracao.dto.RelatorioAutoInfracaoDTOParam;
import br.com.celk.report.vigilancia.autoinfracao.query.QueryRelatorioAutoInfracao;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaEnum;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

/**
 *
 * <AUTHOR>
 */
public class RelatorioAutoInfracao extends AbstractReport<RelatorioAutoInfracaoDTOParam> {

    public RelatorioAutoInfracao(RelatorioAutoInfracaoDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() throws ValidacaoException {
        this.addParametro("FORMA_APRESENTACAO", this.getParam().getFormaApresentacao());
        this.addParametro("gestaoAtividadeEstabelecimentoPorCnae", ConfiguracaoVigilanciaEnum.TipoGestaoAtividade.CNAE.value().equals(VigilanciaHelper.getConfiguracaoVigilancia().getFlagTipoGestaoAtividade()));
        return new QueryRelatorioAutoInfracao();
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/autoinfracao/jrxml/relatorio_auto_infracao.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relatorio_auto_infracao");
    }

}
