package br.com.celk.report.consorcio.query;

import br.com.celk.consorcio.dto.ImpressaoConsorcioGuiaProcedimentoDTO;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.consorcio.*;
import br.com.ksisolucoes.vo.controle.Usuario;
import ch.lambdaj.Lambda;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public class QueryImpressaoGuiaProcedimento extends CommandQuery<QueryImpressaoGuiaProcedimento> implements ITransferDataReport<Long, ImpressaoConsorcioGuiaProcedimentoDTO> {

    private Long param;
    private List<ImpressaoConsorcioGuiaProcedimentoDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(ImpressaoConsorcioGuiaProcedimentoDTO.class.getName());

        hql.addToSelect("consorcioGuiaProcedimento.codigo", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CODIGO));
        hql.addToSelect("consorcioGuiaProcedimento.dataCadastro", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_DATA_CADASTRO));
        hql.addToSelect("consorcioGuiaProcedimento.numeroChave", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_NUMERO_CHAVE));
        hql.addToSelect("consorcioGuiaProcedimento.codigo", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CODIGO));
        hql.addToSelect("consorcioGuiaProcedimento.dataAgendamento", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_DATA_AGENDAMENTO));
        hql.addToSelect("consorcioGuiaProcedimento.nomePaciente", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_NOME_PACIENTE));
        hql.addToSelect("consorcioGuiaProcedimento.cnsPaciente", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CNS_PACIENTE));
        hql.addToSelect("consorcioGuiaProcedimento.dataNascimento", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_DATA_NASCIMENTO));
        hql.addToSelect("consorcioGuiaProcedimento.orientacaoGeral", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_ORIENTACAO_GERAL));
        hql.addToSelect("consorcioGuiaProcedimento.dataAplicacao", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_DATA_APLICACAO));

        hql.addToSelect("usuarioAplicacao.codigo", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_USUARIO_APLICACAO, Usuario.PROP_CODIGO));
        hql.addToSelect("usuarioAplicacao.nome", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_USUARIO_APLICACAO, Usuario.PROP_NOME));

        hql.addToSelect("usuarioCadsus.codigo", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO));
        hql.addToSelect("usuarioCadsus.nome", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME));
        hql.addToSelect("usuarioCadsus.sexo", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_SEXO));
        hql.addToSelect("usuarioCadsus.dataNascimento", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_DATA_NASCIMENTO));
        hql.addToSelect("usuarioCadsus.telefone", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_TELEFONE));
        hql.addToSelect("usuarioCadsus.celular", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CELULAR));

        hql.addToSelect("usuarioCadastro.codigo", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_USUARIO_CADASTRO, Usuario.PROP_CODIGO));
        hql.addToSelect("usuarioCadastro.nome", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_USUARIO_CADASTRO, Usuario.PROP_NOME));
        hql.addToSelect("usuarioCadastro.cargo", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_USUARIO_CADASTRO, Usuario.PROP_CARGO));

        hql.addToSelect("cidadeConsorciado.codigo", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CIDADE, Cidade.PROP_CODIGO));
        hql.addToSelect("cidadeConsorciado.descricao", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CIDADE, Cidade.PROP_DESCRICAO));
        hql.addToSelect("estadoConsorciado.sigla", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CIDADE, Cidade.PROP_ESTADO, Estado.PROP_SIGLA));

        hql.addToSelect("consorcioPrestador.codigo", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_CODIGO));
        hql.addToSelect("prestador.codigo", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR, Empresa.PROP_CODIGO));
        hql.addToSelect("prestador.descricao", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR, Empresa.PROP_DESCRICAO));
        hql.addToSelect("prestador.bairro", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR, Empresa.PROP_BAIRRO));
        hql.addToSelect("prestador.rua", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR, Empresa.PROP_RUA));
        hql.addToSelect("prestador.numero", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR, Empresa.PROP_NUMERO));
        hql.addToSelect("prestador.telefone", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR, Empresa.PROP_TELEFONE));
        hql.addToSelect("cidadePrestador.codigo", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR, Empresa.PROP_CIDADE, Cidade.PROP_CODIGO));
        hql.addToSelect("cidadePrestador.descricao", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR, Empresa.PROP_CIDADE, Cidade.PROP_DESCRICAO));
        hql.addToSelect("estadoPrestador.codigo", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR, Empresa.PROP_CIDADE, Cidade.PROP_ESTADO, Estado.PROP_CODIGO));
        hql.addToSelect("estadoPrestador.sigla", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR, Empresa.PROP_CIDADE, Cidade.PROP_ESTADO, Estado.PROP_SIGLA));

        hql.addToSelect("consorciado.codigo", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_SUB_CONTA, SubConta.PROP_CONTA, Conta.PROP_CONSORCIADO, Empresa.PROP_CODIGO));
        hql.addToSelect("consorciado.descricao", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_SUB_CONTA, SubConta.PROP_CONTA, Conta.PROP_CONSORCIADO, Empresa.PROP_DESCRICAO));
        hql.addToSelect("consorciado.telefone", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_SUB_CONTA, SubConta.PROP_CONTA, Conta.PROP_CONSORCIADO, Empresa.PROP_TELEFONE));
        hql.addToSelect("cidade.codigo", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_SUB_CONTA, SubConta.PROP_CONTA, Conta.PROP_CONSORCIADO, Empresa.PROP_CIDADE, Cidade.PROP_CODIGO));
        hql.addToSelect("cidade.descricao", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_SUB_CONTA, SubConta.PROP_CONTA, Conta.PROP_CONSORCIADO, Empresa.PROP_CIDADE, Cidade.PROP_DESCRICAO));
        hql.addToSelect("estado.codigo", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_SUB_CONTA, SubConta.PROP_CONTA, Conta.PROP_CONSORCIADO, Empresa.PROP_CIDADE, Cidade.PROP_ESTADO, Estado.PROP_CODIGO));
        hql.addToSelect("estado.sigla", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_SUB_CONTA, SubConta.PROP_CONTA, Conta.PROP_CONSORCIADO, Empresa.PROP_CIDADE, Cidade.PROP_ESTADO, Estado.PROP_SIGLA));
        hql.addToSelect("consorcioGuiaProcedimentoItem.codigo", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CODIGO));
        hql.addToSelect("consorcioGuiaProcedimentoItem.codigoSisreg", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CODIGO_SISREG));
        hql.addToSelect("consorcioGuiaProcedimentoItem.numeroSisreg", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_NUMERO_SISREG));
        hql.addToSelect("consorcioGuiaProcedimentoItem.dataSisreg", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_DATA_SISREG));
        hql.addToSelect("consorcioGuiaProcedimentoItem.quantidade", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_QUANTIDADE));
        hql.addToSelect("consorcioGuiaProcedimentoItem.valorProcedimento", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_VALOR_PROCEDIMENTO));
        hql.addToSelect("consorcioProcedimento.codigo", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_PROCEDIMENTO, ConsorcioProcedimento.PROP_CODIGO));
        hql.addToSelect("consorcioProcedimento.descricaoProcedimento", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_PROCEDIMENTO, ConsorcioProcedimento.PROP_DESCRICAO_PROCEDIMENTO));
        hql.addToSelect("consorcioProcedimento.referencia", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_PROCEDIMENTO, ConsorcioProcedimento.PROP_REFERENCIA));
        hql.addToSelect("consorcioProcedimento.flagModeloRequisicao", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_PROCEDIMENTO, ConsorcioProcedimento.PROP_FLAG_MODELO_REQUISICAO));

        hql.addToSelect("cidadeEnderecoUsuario.descricao", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_CIDADE, Cidade.PROP_DESCRICAO));
        hql.addToSelect("estadoUsuario.sigla", VOUtils.montarPath(ImpressaoConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO_ITEM, ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_CIDADE, Cidade.PROP_ESTADO, Estado.PROP_SIGLA));

        hql.addToFrom("ConsorcioGuiaProcedimentoItem consorcioGuiaProcedimentoItem"
                + " left join consorcioGuiaProcedimentoItem.consorcioGuiaProcedimento consorcioGuiaProcedimento"
                + " left join consorcioGuiaProcedimento.consorcioPrestador consorcioPrestador"
                + " left join consorcioGuiaProcedimento.usuarioCadsus usuarioCadsus"
                + " left join consorcioGuiaProcedimento.usuarioCadastro usuarioCadastro"
                + " left join consorcioGuiaProcedimento.usuario usuarioAplicacao"
                + " left join consorcioGuiaProcedimento.cidade cidadeConsorciado"
                + " left join cidadeConsorciado.estado estadoConsorciado"
                + " left join consorcioGuiaProcedimento.subConta subConta"
                + " left join consorcioGuiaProcedimento.enderecoUsuarioCadsus enderecoUsuarioCadsus"
                + " left join consorcioGuiaProcedimentoItem.consorcioProcedimento consorcioProcedimento"
                + " left join consorcioPrestador.empresaPrestador prestador"
                + " left join prestador.cidade cidadePrestador"
                + " left join cidadePrestador.estado estadoPrestador"
                + " left join subConta.conta conta"
                + " left join conta.consorciado consorciado"
                + " left join consorciado.cidade cidade"
                + " left join enderecoUsuarioCadsus.cidade cidadeEnderecoUsuario"
                + " left join cidadeEnderecoUsuario.estado estadoUsuario"
                + " left join cidade.estado estado");

        hql.addToWhereWhithAnd("consorcioGuiaProcedimento.codigo = ", this.param);
        hql.addToWhereWhithAnd("consorcioGuiaProcedimento.status in ", Arrays.asList(
                ConsorcioGuiaProcedimento.StatusGuiaProcedimento.ABERTA.value(),
                ConsorcioGuiaProcedimento.StatusGuiaProcedimentoAudit.AGENDADO.value(),
                ConsorcioGuiaProcedimento.StatusGuiaProcedimento.A_PAGAR.value(),
                ConsorcioGuiaProcedimento.StatusGuiaProcedimento.PAGA.value()));

        hql.addToOrder("consorcioProcedimento.descricaoProcedimento");
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(this.result)) {
            ConsorcioProcedimento consorcioProcedimento = this.result.get(0).getConsorcioGuiaProcedimentoItem().getConsorcioProcedimento();
            Long flagModeloRequisicao = consorcioProcedimento.getFlagModeloRequisicao();
            if (ConsorcioProcedimento.ModeloRequisicao.CONSULTAS.value().equals(flagModeloRequisicao)) {
                setTitulo(Bundle.getStringApplication("rotulo_guia_requisicao_consulta_precedimento_especializado"));
            } else if (ConsorcioProcedimento.ModeloRequisicao.EXAMES.value().equals(flagModeloRequisicao)) {
                setTitulo(Bundle.getStringApplication("rotulo_guia_requisicao_procedimento_finalidade_diagnosticas"));
            } else if (ConsorcioProcedimento.ModeloRequisicao.TERAPIAS.value().equals(flagModeloRequisicao)) {
                setTitulo(Bundle.getStringApplication("rotulo_guia_requisicao_terapias"));
            } else if (ConsorcioProcedimento.ModeloRequisicao.PROTESE_DENTARIA.value().equals(flagModeloRequisicao)) {
                setTitulo(Bundle.getStringApplication("rotulo_guia_requisicao_protese_dentaria"));
            } else if (ConsorcioProcedimento.ModeloRequisicao.MEDICINA_TRABALHO.value().equals(flagModeloRequisicao)) {
                setTitulo(Bundle.getStringApplication("rotulo_guia_requisicao_servico_esp_medicina_seguranca_trabalho"));
            }
            for (ImpressaoConsorcioGuiaProcedimentoDTO dto : this.result) {
                dto.setPreparacao(getDescricaoPreparacaoConsorcioProcedimento(dto.getConsorcioGuiaProcedimentoItem().getConsorcioProcedimento()));
            }
        }
    }

    private String getDescricaoPreparacaoConsorcioProcedimento(ConsorcioProcedimento consorcioProcedimento) throws DAOException {
        List<PreparacaoConsorcioProcedimentoItem> preparacaoConsorcioProcedimentoItemList = this.getSession().createCriteria(PreparacaoConsorcioProcedimentoItem.class)
                .add(Restrictions.eq(PreparacaoConsorcioProcedimentoItem.PROP_CONSORCIO_PROCEDIMENTO, consorcioProcedimento))
                .list();
        List<String> stringList = new ArrayList<>();
        if(CollectionUtils.isNotNullEmpty(preparacaoConsorcioProcedimentoItemList)) {
            for (PreparacaoConsorcioProcedimentoItem preparacaoConsorcioProcedimentoItem : preparacaoConsorcioProcedimentoItemList) {
                if (preparacaoConsorcioProcedimentoItem != null) {
                    String descricao = StringUtil.removeBarraString(preparacaoConsorcioProcedimentoItem.getPreparacaoConsorcioProcedimento().getDescricao(), ", ", "\n");
                    descricao = StringUtil.removeBarraString(descricao, "", "\r");
                    stringList.add(descricao);
                }
            }
        }
        if(CollectionUtils.isNotNullEmpty(stringList)){
            return Lambda.join(stringList, "<br/>");
        }
        return null;
    }

    @Override
    public void setDTOParam(Long param) {
        this.param = param;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List) result);
    }

    public void setTitulo(String titulo) {
    }

    @Override
    public Collection getResult() {
        return result;
    }
}