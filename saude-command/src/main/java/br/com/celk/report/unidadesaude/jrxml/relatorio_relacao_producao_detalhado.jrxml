<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_acompanhamento" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="904ef39c-8037-443b-8222-af49929dd456">
	<property name="ireport.zoom" value="1.4019755979255701"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="br.com.celk.util.Coalesce"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<import value="org.apache.commons.lang.StringUtils"/>
	<style name="Crosstab Data Text" hAlign="Center"/>
	<subDataset name="ds-acompanhamento" uuid="bc42385e-a0a8-4625-b124-339ff8390e53">
		<field name="quantidade" class="java.lang.Long"/>
		<field name="nome" class="java.lang.String"/>
		<field name="data" class="java.util.Date"/>
	</subDataset>
	<parameter name="FORMA_APRESENTACAO" class="br.com.celk.unidadesaude.esus.relatorios.RelatorioProcedimentosDTOParam.FormaApresentacao"/>
	<field name="cadastrosList" class="java.util.List"/>
	<field name="producoesList" class="java.util.List"/>
	<variable name="FA" class="br.com.celk.unidadesaude.esus.relatorios.RelatorioProcedimentosDTOParam.FormaApresentacao"/>
	<detail>
		<band height="20">
			<textField>
				<reportElement style="Crosstab Data Text" x="0" y="0" width="802" height="14" uuid="37541641-0acc-4fda-b12d-5019e42ec665"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1"/>
				<textElement verticalAlignment="Bottom" textAlignment="Center">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_relatorio_resumo_producao_historica")]]></textFieldExpression>
			</textField>
		</band>
		<band height="20">
			<textField>
				<reportElement style="Crosstab Data Text" x="0" y="0" width="201" height="14" uuid="37541641-0acc-4fda-b12d-5019e42ec665"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1"/>
				<textElement verticalAlignment="Bottom" textAlignment="Left">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cadastros")]]></textFieldExpression>
			</textField>
		</band>
		<band height="42">
			<crosstab ignoreWidth="false">
				<reportElement positionType="Float" x="0" y="0" width="802" height="42" uuid="0152e232-6335-4f51-b753-980bcffdb9a5"/>
				<crosstabDataset>
					<dataset>
						<datasetRun subDataset="ds-acompanhamento" uuid="d1de19f4-30f0-4926-a28b-b6a889c6bb70">
							<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{cadastrosList})]]></dataSourceExpression>
						</datasetRun>
					</dataset>
				</crosstabDataset>
				<crosstabHeaderCell>
					<cellContents mode="Opaque">
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
						<textField>
							<reportElement style="Crosstab Data Text" x="0" y="0" width="201" height="14" uuid="37541641-0acc-4fda-b12d-5019e42ec665"/>
							<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1"/>
							<textElement verticalAlignment="Bottom" textAlignment="Center">
								<font fontName="Arial" isBold="true"/>
							</textElement>
							<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_descricao")]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabHeaderCell>
				<rowGroup name="nome" width="201" totalPosition="End">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{nome}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents>
							<box>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Crosstab Data Text" x="2" y="2" width="180" height="13" uuid="084c0441-a442-48a6-ab37-aeb4f89a4a79"/>
								<textElement textAlignment="Left"/>
								<textFieldExpression><![CDATA[$V{nome}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents>
							<box>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<staticText>
								<reportElement x="0" y="0" width="198" height="14" uuid="82d54fb4-d6e4-4335-8c39-00ca2558edcf"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Right" verticalAlignment="Middle">
									<font fontName="Arial" isBold="true"/>
								</textElement>
								<text><![CDATA[Total]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalRowHeader>
				</rowGroup>
				<columnGroup name="mesAno" height="30" totalPosition="End">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[Data.formatarMesAno($F{data})]]></bucketExpression>
					</bucket>
					<crosstabColumnHeader>
						<cellContents>
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField>
								<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="30" uuid="d485ef58-a34c-4690-83f4-cd87a969a510"/>
								<textElement verticalAlignment="Middle" />
								<textFieldExpression><![CDATA[$V{mesAno}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabColumnHeader>
					<crosstabTotalColumnHeader>
						<cellContents>
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<staticText>
								<reportElement x="0" y="0" width="50" height="30" uuid="0aa92caf-d99d-4209-8be1-ad7eceee2edf"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<text><![CDATA[Total]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalColumnHeader>
				</columnGroup>
				<measure name="quantidadeMeasure" class="java.lang.Long" calculation="Sum">
					<measureExpression><![CDATA[$F{quantidade}]]></measureExpression>
				</measure>
				<crosstabCell width="50" height="25">
					<cellContents>
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField>
							<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="25" uuid="d5a07ca3-3468-47c5-a6a5-6e614d4c2811"/>
							<textFieldExpression><![CDATA[$V{quantidadeMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell height="25" rowTotalGroup="nome">
					<cellContents mode="Opaque">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField>
							<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="25" uuid="25ea0a92-99ff-4fa0-9720-16ea0795a872"/>
							<textFieldExpression><![CDATA[$V{quantidadeMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="50" columnTotalGroup="mesAno">
					<cellContents>
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField>
							<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="25" uuid="f0869dd2-e83d-4157-981c-8178c4889286"/>
							<textFieldExpression><![CDATA[$V{quantidadeMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell rowTotalGroup="nome" columnTotalGroup="mesAno">
					<cellContents>
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField>
							<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="25" uuid="1c51f8da-e364-4af0-84a9-79ce36a31d9b"/>
							<textFieldExpression><![CDATA[$V{quantidadeMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
			</crosstab>
		</band>
		<band height="20" />
		<band height="20">
			<textField>
				<reportElement style="Crosstab Data Text" x="0" y="0" width="201" height="14" uuid="37541641-0acc-4fda-b12d-5019e42ec665"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1"/>
				<textElement verticalAlignment="Bottom" textAlignment="Left">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_producao")]]></textFieldExpression>
			</textField>
		</band>
		<band height="42">
			<crosstab ignoreWidth="false">
				<reportElement positionType="Float" x="0" y="0" width="802" height="42" uuid="0152e232-6335-4f51-b753-980bcffdb9a5"/>
				<crosstabDataset>
					<dataset>
						<datasetRun subDataset="ds-acompanhamento" uuid="d1de19f4-30f0-4926-a28b-b6a889c6bb70">
							<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{producoesList})]]></dataSourceExpression>
						</datasetRun>
					</dataset>
				</crosstabDataset>
				<crosstabHeaderCell>
					<cellContents mode="Opaque">
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
						<textField>
							<reportElement style="Crosstab Data Text" x="0" y="0" width="201" height="14" uuid="37541641-0acc-4fda-b12d-5019e42ec665"/>
							<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1"/>
							<textElement verticalAlignment="Bottom" textAlignment="Center">
								<font fontName="Arial" isBold="true"/>
							</textElement>
							<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_descricao")]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabHeaderCell>
				<rowGroup name="nome" width="201" totalPosition="End">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{nome}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents>
							<box>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Crosstab Data Text" x="2" y="2" width="180" height="13" uuid="084c0441-a442-48a6-ab37-aeb4f89a4a79"/>
								<textElement textAlignment="Left"/>
								<textFieldExpression><![CDATA[$V{nome}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents>
							<box>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<staticText>
								<reportElement x="0" y="0" width="198" height="14" uuid="82d54fb4-d6e4-4335-8c39-00ca2558edcf"/>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Right" verticalAlignment="Middle">
									<font fontName="Arial" isBold="true"/>
								</textElement>
								<text><![CDATA[Total]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalRowHeader>
				</rowGroup>
				<columnGroup name="mesAno" height="30" totalPosition="End">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[Data.formatarMesAno($F{data})]]></bucketExpression>
					</bucket>
					<crosstabColumnHeader>
						<cellContents>
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField>
								<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="30" uuid="d485ef58-a34c-4690-83f4-cd87a969a510"/>
								<textElement verticalAlignment="Middle" />
								<textFieldExpression><![CDATA[$V{mesAno}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabColumnHeader>
					<crosstabTotalColumnHeader>
						<cellContents>
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<staticText>
								<reportElement x="0" y="0" width="50" height="30" uuid="0aa92caf-d99d-4209-8be1-ad7eceee2edf"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<text><![CDATA[Total]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalColumnHeader>
				</columnGroup>
				<measure name="quantidadeMeasure" class="java.lang.Long" calculation="Sum">
					<measureExpression><![CDATA[$F{quantidade}]]></measureExpression>
				</measure>
				<crosstabCell width="50" height="25">
					<cellContents>
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField>
							<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="25" uuid="d5a07ca3-3468-47c5-a6a5-6e614d4c2811"/>
							<textFieldExpression><![CDATA[$V{quantidadeMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell height="25" rowTotalGroup="nome">
					<cellContents mode="Opaque">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField>
							<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="25" uuid="25ea0a92-99ff-4fa0-9720-16ea0795a872"/>
							<textFieldExpression><![CDATA[$V{quantidadeMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="50" columnTotalGroup="mesAno">
					<cellContents>
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField>
							<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="25" uuid="f0869dd2-e83d-4157-981c-8178c4889286"/>
							<textFieldExpression><![CDATA[$V{quantidadeMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell rowTotalGroup="nome" columnTotalGroup="mesAno">
					<cellContents>
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField>
							<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="25" uuid="1c51f8da-e364-4af0-84a9-79ce36a31d9b"/>
							<textFieldExpression><![CDATA[$V{quantidadeMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
			</crosstab>
		</band>
	</detail>
</jasperReport>
