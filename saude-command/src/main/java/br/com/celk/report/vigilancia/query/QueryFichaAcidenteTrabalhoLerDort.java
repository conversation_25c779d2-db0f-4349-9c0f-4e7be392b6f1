package br.com.celk.report.vigilancia.query;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaTrabalhoLerDort;
import org.hibernate.Session;

import java.util.HashMap;
import java.util.Map;

public class QueryFichaAcidenteTrabalhoLerDort extends QueryFichaInvestigacaoBase {

    @Override
    protected Class getClasseFichaInvestigacao() { return InvestigacaoAgravoDoencaTrabalhoLerDort.class; }

    @Override
    protected Map<String, String> getCamposInvestigacaoAgravo() {
        Map<String, String> campos = new HashMap<>();

        campos.put("ocupacaoCbo.descricao", "_31_ocupacao");

        campos.put("investigacaoAgravo.situacaoMercadoTrabalho", "_32_situacao_mercado_trab");
        campos.put("investigacaoAgravo.tempoTrabalhoOcupacao", "_33_tempo_trabalho");
        campos.put("investigacaoAgravo.tempoTrabalhoOcupacaoUnidadeMedida", "_33_tempo_trabalho_um");

        campos.put("empresaContratante.cnpj", "_34_cnpj");
        campos.put("empresaContratante.descricao", "_35_empresa");
        campos.put("atividade.descricao", "_36_cnae");

        campos.put("cidade.estado.sigla", "_37_uf");
        campos.put("empresaContratante.cidade.descricao", "_38_municipio");
        campos.put("empresaContratante.cidade.codigo", "_38_ibge");
        campos.put("investigacaoAgravo.empresaDistrito", "_39_distrito");
        campos.put("empresaContratante.bairro", "_40_bairro");
        campos.put("empresaContratante.rua", "_41_endereco");
        campos.put("empresaContratante.numero", "_42_numero");
        campos.put("investigacaoAgravo.empresaPontoReferencia", "_43_ponto_ref");
        campos.put("investigacaoAgravo.empresaTelefone", "_44_telefone");
        campos.put("investigacaoAgravo.empresaTerceirizada", "_45_empresa_terceirizada");

        campos.put("investigacaoAgravo.agravosAssociadosHipertensao", "_46_hipertensao_arterial");
        campos.put("investigacaoAgravo.agravosAssociadosTuberculose", "_46_tuberculose");
        campos.put("investigacaoAgravo.agravosAssociadosDiabetes", "_46_diabetes_mellitus");
        campos.put("investigacaoAgravo.agravosAssociadosAsma", "_46_asma");
        campos.put("investigacaoAgravo.agravosAssociadosHanseniase", "_46_hanseniase");
        campos.put("investigacaoAgravo.agravosAssociadosTranstornoMental", "_46_transtorno_mental");
        campos.put("investigacaoAgravo.agravosAssociadosOutros", "_46_outro_descricao");
        campos.put("investigacaoAgravo.tempoExposicaoAgenteRisco", "_47_tempo_exposicao");
        campos.put("investigacaoAgravo.tempoExposicaoAgenteRiscoUnidadeMedida", "_47_tempo_exposicao_um");
        campos.put("investigacaoAgravo.regimeTratamento", "_48_regime_tratamento");

        campos.put("investigacaoAgravo.sinaisSintomasAlteracaoSensibilidade", "_49_sinais_sintomas_sensibilidade");
        campos.put("investigacaoAgravo.sinaisSintomasLimitacaoMovimentos", "_49_sinais_sintomas_movimentos");
        campos.put("investigacaoAgravo.sinaisSintomasDiminuicaoForca", "_49_sinais_sintomas_diminuicao_forca");
        campos.put("investigacaoAgravo.sinaisSintomasSinaisFlogisticos", "_49_sinais_sintomas_flogisticos");
        campos.put("investigacaoAgravo.sinaisSintomasDiminuicaoMovimento", "_49_sinais_sintomas_diminuicao_movimento");
        campos.put("investigacaoAgravo.sinaisSintomasDor", "_49_sinais_sintomas_dor");
        campos.put("investigacaoAgravo.sinaisSintomasOutros", "_49_sinais_sintomas_outros_descricao");

        campos.put("investigacaoAgravo.limitacaoExercicioTarefas", "_50_limitacao_exercicio_tarefa");

        campos.put("investigacaoAgravo.pacienteExpostoPremiosProducao", "_51_paciente_exposto_premios");
        campos.put("investigacaoAgravo.pacienteExpostoPausas", "_51_paciente_exposto_pausas");
        campos.put("investigacaoAgravo.pacienteExpostoMovimentosRepetitivos", "_51_paciente_exposto_movimentos_repetitivos");
        campos.put("investigacaoAgravo.pacienteExpostoJornadaMaiorSeisHoras", "_51_paciente_exposto_jornada_6_horas");
        campos.put("investigacaoAgravo.pacienteExpostoAmbienteEstressante", "_51_paciente_exposto_ambiente_estressante");

        campos.put("diagnosticoEspecifico.codigo", "_52_cid");

        campos.put("investigacaoAgravo.afastamentoParaTratamento", "_53_afastamento_trabalho");
        campos.put("investigacaoAgravo.tempoAfastamento", "_54_tempo_afastamento_trabalho");
        campos.put("investigacaoAgravo.tempoAfastamentoUnidadeMedida", "_54_tempo_afastamento_trabalho_um");
        campos.put("investigacaoAgravo.conclusaoAfastamento", "_55_tempo_melhora_afastamento");
        campos.put("investigacaoAgravo.outrosTrabalhadoresMesmaDoenca", "_56_outros_trabalhadores");

        campos.put("investigacaoAgravo.condutaGeralAfastamentoAgenteRisco", "_57_conduta_geral_afastamento_risco");
        campos.put("investigacaoAgravo.condutaGeralMudancaOrganizacaoTrabalho", "_57_conduta_geral_mudanca_organizacao");
        campos.put("investigacaoAgravo.condutaGeralProtecaoColetiva", "_57_conduta_geral_protecao_coletiva");
        campos.put("investigacaoAgravo.condutaGeralProtecaoIndividual", "_57_conduta_geral_afastamento_local");
        campos.put("investigacaoAgravo.condutaGeralNenhum", "_57_conduta_geral_protecao_individual");
        campos.put("investigacaoAgravo.condutaGeralAfastamentoLocalTrabalho", "_57_conduta_geral_nenhum");
        campos.put("investigacaoAgravo.condutaGeralOutros", "_57_conduta_geral_outros");

        campos.put("investigacaoAgravo.evolucaoCaso", "_58_evolucao_caso");
        campos.put(formatarData("investigacaoAgravo.dataObito"), "_59_data_obito");
        campos.put("investigacaoAgravo.emitidaCat", "_60_emitida_cat");
        campos.put("investigacaoAgravo.observacao", "_observacao");

        return campos;
    }

    @Override
    protected String getJuncoesFicha() {
        return "left join investigacaoAgravo.ocupacaoCbo ocupacaoCbo "
                + "left join investigacaoAgravo.empresaContratante empresaContratante "
                + "left join empresaContratante.atividade atividade "
                + "left join empresaContratante.cidade cidade "
                + "left join cidade.estado estado "
                + "left join investigacaoAgravo.diagnosticoEspecifico diagnosticoEspecifico ";
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        super.customProcess(session);
        for (Map<String, Object> map : getResult()) {
            addTabelaCBO(map);
        }
    }

    private void addTabelaCBO(Map<String, Object> map) {
        TabelaCbo cbo = (TabelaCbo) map.get("ocupacaoCbo.descricao");
        if (cbo != null) {
            map.put("ocupacaoCbo.descricao", cbo.getDescricaoFormatado());
        }
    }

}
