package br.com.celk.report.geral.despesa.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.geral.despesa.RelatorioDespesasDTO;
import br.com.ksisolucoes.report.geral.despesa.RelatorioDespesasDTOParam;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.vo.geral.LancamentoDespesa;
import ch.lambdaj.Lambda;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelacaoDespesas extends CommandQuery<QueryRelacaoDespesas> implements ITransferDataReport<RelatorioDespesasDTOParam, RelatorioDespesasDTO> {

    private RelatorioDespesasDTOParam param;
    private Collection<RelatorioDespesasDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(RelatorioDespesasDTO.class.getName());
        hql.setConvertToLeftJoin(true);
        
        LancamentoDespesa ld = Lambda.on(LancamentoDespesa.class);
        RelatorioDespesasDTO dto = Lambda.on(RelatorioDespesasDTO.class);

        hql.addToSelect("ld."+path(ld.getCodigo()), path(dto.getLancamentoDespesa().getCodigo()));
        hql.addToSelect("ld."+path(ld.getDescricao()), path(dto.getLancamentoDespesa().getDescricao()));
        hql.addToSelect("ld."+path(ld.getValorDespesa()), path(dto.getLancamentoDespesa().getValorDespesa()));
        hql.addToSelect("ld."+path(ld.getDataDespesa()), path(dto.getLancamentoDespesa().getDataDespesa()));
        
        hql.addToSelect("ld."+path(ld.getEmpresa().getCodigo()), path(dto.getLancamentoDespesa().getEmpresa().getCodigo()));
        hql.addToSelect("ld."+path(ld.getEmpresa().getDescricao()), path(dto.getLancamentoDespesa().getEmpresa().getDescricao()));
        
        hql.addToSelect("ld."+path(ld.getTipoMovimentacao().getCodigo()), path(dto.getLancamentoDespesa().getTipoMovimentacao().getCodigo()));
        hql.addToSelect("ld."+path(ld.getTipoMovimentacao().getDescricao()), path(dto.getLancamentoDespesa().getTipoMovimentacao().getDescricao()));
        
        hql.addToSelect("ld."+path(ld.getPessoa().getCodigo()), path(dto.getLancamentoDespesa().getPessoa().getCodigo()));
        hql.addToSelect("ld."+path(ld.getPessoa().getDescricao()), path(dto.getLancamentoDespesa().getPessoa().getDescricao()));
        
        hql.addToSelect("ld."+path(ld.getCentroCusto().getCodigo()), path(dto.getLancamentoDespesa().getCentroCusto().getCodigo()));
        hql.addToSelect("ld."+path(ld.getCentroCusto().getDescricao()), path(dto.getLancamentoDespesa().getCentroCusto().getDescricao()));
        
        hql.addToSelect("ld."+path(ld.getProgramaSaude().getCodigo()), path(dto.getLancamentoDespesa().getProgramaSaude().getCodigo()));
        hql.addToSelect("ld."+path(ld.getProgramaSaude().getDescricao()), path(dto.getLancamentoDespesa().getProgramaSaude().getDescricao()));

        if (this.param.getFormaApresentacao().equals(RelatorioDespesasDTOParam.FormaApresentacao.BLOCO_PROGRMA)) {
            hql.addToOrder("ld.programaSaude.descricao");
        } else if (this.param.getFormaApresentacao().equals(RelatorioDespesasDTOParam.FormaApresentacao.SETOR)) {
            hql.addToOrder("ld.centroCusto.descricao");
        } else if (this.param.getFormaApresentacao().equals(RelatorioDespesasDTOParam.FormaApresentacao.TIPO_DESPESA)) {
            hql.addToOrder("ld.tipoMovimentacao.descricao");
        }

        hql.addToOrder("ld.dataDespesa desc");

        hql.addToFrom("LancamentoDespesa ld");

        hql.addToWhereWhithAnd("ld.empresa = ", this.param.getEmpresa());
        hql.addToWhereWhithAnd("ld.dataDespesa ", this.param.getPeriodo());
        hql.addToWhereWhithAnd("ld.centroCusto = ", this.param.getCentroCusto());
        hql.addToWhereWhithAnd("ld.pessoa = ", this.param.getPessoa());
        hql.addToWhereWhithAnd("ld.programaSaude = ", this.param.getProgramaSaude());
        hql.addToWhereWhithAnd("ld.tipoMovimentacao = ", this.param.getTipoMovimentacao());
    }

    @Override
    public void setDTOParam(RelatorioDespesasDTOParam param) {
        this.param = param;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection<RelatorioDespesasDTO> getResult() {
        return this.result;
    }

}
