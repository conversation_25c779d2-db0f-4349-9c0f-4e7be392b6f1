<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_programa_mais_medicos" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="904ef39c-8037-443b-8222-af49929dd456">
	<property name="ireport.zoom" value="1.8181818181818326"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.celk.util.*"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<parameter name="COMPETENCIA" class="java.lang.String"/>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="lstDadosAtuaisTerritorioEquipe" class="java.util.List"/>
	<field name="lstDadosTerritorioEquipe" class="java.util.List"/>
	<field name="lstDadosProducaoEquipe" class="java.util.List"/>
	<group name="EMPRESA" isReprintHeaderOnEachPage="true" minHeightToStartNewPage="67">
		<groupExpression><![CDATA[$F{empresa}.getCodigo()]]></groupExpression>
		<groupHeader>
			<band height="27">
				<rectangle radius="10">
					<reportElement x="0" y="7" width="556" height="20" isPrintWhenDetailOverflows="true" backcolor="#E6E6E6" uuid="079f4e83-f3c8-45f2-aa94-a9932dd70517"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField>
					<reportElement mode="Transparent" x="0" y="8" width="555" height="18" isPrintWhenDetailOverflows="true" backcolor="#DFDFDF" uuid="709a99b3-457f-43a8-ae29-3b774eff581d"/>
					<box topPadding="2" leftPadding="8" bottomPadding="2" rightPadding="0"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{empresa}.getDescricao()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<detail>
		<band height="40">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{lstDadosAtuaisTerritorioEquipe})]]></printWhenExpression>
			<elementGroup>
				<rectangle>
					<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="0" y="5" width="555" height="35" isPrintWhenDetailOverflows="true" uuid="20072b8f-ef55-40b8-8dcd-eab500571a83"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField>
					<reportElement mode="Transparent" x="4" y="5" width="547" height="18" isPrintWhenDetailOverflows="true" backcolor="#DFDFDF" uuid="6884c92d-0c5d-4049-a680-f5f1d0a85684"/>
					<box topPadding="2" leftPadding="3" bottomPadding="2" rightPadding="3"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_atuais_territorio_equipe").toUpperCase()]]></textFieldExpression>
				</textField>
				<subreport>
					<reportElement x="0" y="25" width="555" height="14" isPrintWhenDetailOverflows="true" uuid="1275ae48-fea2-4b36-8536-a0ef15109b8d"/>
					<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{lstDadosAtuaisTerritorioEquipe})]]></dataSourceExpression>
					<subreportExpression><![CDATA["/br/com/celk/report/esus/jrxml/sub_relatorio_programa_mais_medicos_territorio_equipe.jasper"]]></subreportExpression>
				</subreport>
			</elementGroup>
		</band>
		<band height="40">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{lstDadosTerritorioEquipe})]]></printWhenExpression>
			<elementGroup>
				<rectangle>
					<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="0" y="5" width="555" height="35" isPrintWhenDetailOverflows="true" uuid="85cf5a2c-7d1e-4bd0-af97-ffa5e632de03"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isStretchWithOverflow="true">
					<reportElement mode="Transparent" x="4" y="5" width="547" height="18" isPrintWhenDetailOverflows="true" backcolor="#DFDFDF" uuid="4248b513-66b5-4a4a-a888-5813c055a83e"/>
					<box topPadding="2" leftPadding="3" bottomPadding="2" rightPadding="3"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_territorio_equipe").toUpperCase()]]></textFieldExpression>
				</textField>
				<subreport>
					<reportElement x="0" y="25" width="555" height="14" isPrintWhenDetailOverflows="true" uuid="6fc98820-a99b-4c95-a902-10d7be3331b5"/>
					<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{lstDadosTerritorioEquipe})]]></dataSourceExpression>
					<subreportExpression><![CDATA["/br/com/celk/report/esus/jrxml/sub_relatorio_programa_mais_medicos_territorio_equipe.jasper"]]></subreportExpression>
				</subreport>
			</elementGroup>
		</band>
		<band height="54">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{lstDadosProducaoEquipe})]]></printWhenExpression>
			<elementGroup>
				<rectangle>
					<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="0" y="5" width="555" height="49" isPrintWhenDetailOverflows="true" uuid="0ccb469e-b06e-4b64-8e94-eaabeb750e06"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField>
					<reportElement mode="Transparent" x="4" y="5" width="547" height="18" isPrintWhenDetailOverflows="true" backcolor="#DFDFDF" uuid="5f26b180-5d23-47e6-9783-f5b120bef618"/>
					<box topPadding="2" leftPadding="3" bottomPadding="2" rightPadding="3"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_producao_equipe").toUpperCase()]]></textFieldExpression>
				</textField>
				<subreport>
					<reportElement x="0" y="25" width="555" height="28" isPrintWhenDetailOverflows="true" uuid="1074d3a5-fe6d-49f3-bb43-bbd1694a6854"/>
					<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{lstDadosProducaoEquipe})]]></dataSourceExpression>
					<subreportExpression><![CDATA["/br/com/celk/report/esus/jrxml/sub_relatorio_programa_mais_medicos_producao_equipe.jasper"]]></subreportExpression>
				</subreport>
			</elementGroup>
		</band>
	</detail>
</jasperReport>
