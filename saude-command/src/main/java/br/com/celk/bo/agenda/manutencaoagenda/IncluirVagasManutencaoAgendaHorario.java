package br.com.celk.bo.agenda.manutencaoagenda;

import br.com.celk.agendamento.CadastroAgendaBehavior;
import br.com.celk.bo.agenda.interfaces.dto.CadastroManutencaoAgendaHorarioDTO;
import br.com.celk.bo.agenda.interfaces.dto.ManutencaoAgendaHorarioDTO;
import br.com.celk.bo.agenda.interfaces.dto.ManutencaoAgendaHorarioDTOParam;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.agendamento.dto.AgendaGradeAtendimentoHorariosDTO;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import org.apache.commons.lang.SerializationUtils;
import org.hamcrest.Matchers;
import org.joda.time.DateTime;

import java.util.ArrayList;
import java.util.List;

import static ch.lambdaj.Lambda.by;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class IncluirVagasManutencaoAgendaHorario extends AbstractCommandTransaction{

    private CadastroManutencaoAgendaHorarioDTO dto;

    public IncluirVagasManutencaoAgendaHorario(CadastroManutencaoAgendaHorarioDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        validarVagas();
        
        if(CollectionUtils.isNotNullEmpty(dto.getHorariosSelecionadosDTOList())){
            Group<ManutencaoAgendaHorarioDTO> byAgendaGradeAtendimento = Lambda.group(dto.getHorariosSelecionadosDTOList(), 
                        by(on(ManutencaoAgendaHorarioDTO.class).getAgendaGradeHorario().getAgendaGradeAtendimento()));

            AgendaGradeAtendimento aga;
            StringBuilder sb = new StringBuilder();

            for(Group<ManutencaoAgendaHorarioDTO> group : byAgendaGradeAtendimento.subgroups()){
                aga = group.first().getAgendaGradeHorario().getAgendaGradeAtendimento();
                Agenda agenda = aga.getAgendaGrade().getAgenda();
                if (agenda != null) {
                    agenda = LoadManager.getInstance(Agenda.class)
                            .addProperties(new HQLProperties(Agenda.class).getProperties())
                            .addProperties(new HQLProperties(TipoProcedimento.class, Agenda.PROP_TIPO_PROCEDIMENTO).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(Agenda.PROP_CODIGO, agenda.getCodigo()))
                            .start().getVO();
                    if (agenda.getTipoProcedimento() != null && RepositoryComponentDefault.SIM_LONG.equals(agenda.getTipoProcedimento().getFlagRequerAprovacao())) {
                        agenda.setStatus(Agenda.STATUS_PENDENTE);
                        agenda.setMotivoNaoAprovacao(null);
                        BOFactory.save(agenda);
                    }
                }
                BOFactory.save(aga.getAgendaGrade());
                BOFactory.save(aga);

                sb.append(aga.getAgendaGrade().getDescricaoData());
                sb.append(": ");
                
                for(ManutencaoAgendaHorarioDTO madDTO : group.findAll()){
                    sb.append(new DateTime(madDTO.getAgendaGradeHorario().getHora()).toString("HH:mm"));
                    sb.append("; ");
                    
                    BOFactory.save(madDTO.getAgendaGradeHorario());
                }
            }
            BOFactory.getBO(AgendamentoFacade.class).gerarAgendaOcorrencia(dto.getMotivo(), dto.getTipoOcorrencia(), dto.getCodigoAgenda(), 
                    Bundle.getStringApplication("rotulo_horarios_incluidos_X", sb.toString()));
        }
    }
    
    private void validarVagas() throws ValidacaoException, DAOException{        
        /**
         *  VALIDA CONFLITO DE HORÁRIOS COM A AGENDA ATUAL, COM AS VAGAS E HORAS FINAIS JÁ ATUALIZADAS
         */
        CadastroAgendaBehavior behavior = new CadastroAgendaBehavior(dto.getHorariosSelecionadosDTOList().get(0).getAgendaGradeHorario().getAgendaGradeAtendimento().getAgendaGrade().getAgenda());

        List<ManutencaoAgendaHorarioDTO> horarioSelecionadoListClone = new ArrayList<>();
        for(ManutencaoAgendaHorarioDTO madDTO : dto.getHorariosSelecionadosDTOList()){
            horarioSelecionadoListClone.add((ManutencaoAgendaHorarioDTO) SerializationUtils.clone(madDTO));
        }
        
        ManutencaoAgendaHorarioDTOParam param = new ManutencaoAgendaHorarioDTOParam();
        param.setCodigoAgenda(horarioSelecionadoListClone.get(0).getAgendaGradeHorario().getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getCodigo());
        param.setPeriodo(new DatePeriod(DataUtil.getDataAtual(), null));

        // Faz a consulta de todos os horários da agenda
        List<ManutencaoAgendaHorarioDTO> horariosList = BOFactory.getBO(AgendamentoFacade.class).consultarHorariosManutencaoAgendaHorario(param);

        if(CollectionUtils.isNotNullEmpty(horariosList)){
            // Lista de códigos da AgendaGradeAtendimento selecionado
            List<Long> codigoAgendaGradeAtendimentoSelecioadoList = Lambda.extract(horarioSelecionadoListClone, 
                    Lambda.on(ManutencaoAgendaHorarioDTO.class).getAgendaGradeHorario().getAgendaGradeAtendimento().getCodigo());

            // Remove os horários selecionados, pois os selecionados estarão com o número de vagas e hora final já atualizados
            horariosList.removeAll(Lambda.select(horariosList, 
                    Lambda.having(Lambda.on(ManutencaoAgendaHorarioDTO.class).getAgendaGradeHorario().getAgendaGradeAtendimento().getCodigo(), Matchers.isIn(codigoAgendaGradeAtendimentoSelecioadoList))));

            if(CollectionUtils.isNotNullEmpty(horariosList)){
                horariosList.addAll(0, horarioSelecionadoListClone);
                List<AgendaGradeAtendimentoHorariosDTO> list = new ArrayList<>();
                AgendaGradeAtendimentoHorariosDTO agahDTO;
                for(ManutencaoAgendaHorarioDTO madDTO : horariosList){
                    agahDTO = new AgendaGradeAtendimentoHorariosDTO();
                    agahDTO.setAgendaGradeAtendimento(madDTO.getAgendaGradeHorario().getAgendaGradeAtendimento());
                    list.add(agahDTO);
                }

                // Faz a validação de conflito com os demais horários
                for(ManutencaoAgendaHorarioDTO madDTO : horarioSelecionadoListClone){
                    behavior.validarConflitosAgendasNaoSalvas(list, madDTO.getAgendaGradeHorario().getAgendaGradeAtendimento(), true);
                }
            }
        }
    }
}