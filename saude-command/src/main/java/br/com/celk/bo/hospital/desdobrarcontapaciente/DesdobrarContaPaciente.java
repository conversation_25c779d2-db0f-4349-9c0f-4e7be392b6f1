package br.com.celk.bo.hospital.desdobrarcontapaciente;

import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.CadastroFechamentoContaDTO;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Projections;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAlta;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang.SerializationUtils;
import org.hibernate.Query;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class DesdobrarContaPaciente extends AbstractCommandTransaction {

    private final ContaPaciente contaPaciente;
    private Date dataDesdobramento;
    private final UsuarioCadsus usuarioCadsus;
    private AtendimentoInformacao novoAtendimentoInformacao;
    private Atendimento atendimento;
    private final Atendimento atendimentoPrincipal;

    public DesdobrarContaPaciente(ContaPaciente contaPaciente) {
        this.contaPaciente = contaPaciente;
        this.usuarioCadsus = contaPaciente.getUsuarioCadsus();
        this.atendimentoPrincipal = contaPaciente.getAtendimentoInformacao().getAtendimentoPrincipal();
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        AtendimentoInformacao ai = (AtendimentoInformacao) getSession().get(AtendimentoInformacao.class, contaPaciente.getAtendimentoInformacao().getCodigo());

        // Carregar a aih
        Aih aih = carregarAih();

        // Gerar alta paciente
        gerarAltaPaciente(aih);

        // Gerar novo atendimento informação
        novoAtendimentoInformacao = gerarNovoAtendimentoInformacao(ai);

        // Fechar o atendimento informação antigo
        fecharAtendimentoInformacaoAntigo(ai);

        // Gerar nova conta paciente
        ContaPaciente novaContaPaciente = gerarNovaContaPaciente();

        // Atualizar sequência de ciclos do(s) atendimento(s)
        atualizarSequenciaCiclosAtendimentos();

        // Atualizar os itens da conta paciente antiga para a nova
        atualizarItensContaPaciente(novaContaPaciente);

        // Gerar nova AIH para nova Conta
        gerarNovaAih(novaContaPaciente, aih);
    }

    private AtendimentoInformacao gerarNovoAtendimentoInformacao(AtendimentoInformacao ai) throws DAOException, ValidacaoException {
        AtendimentoInformacao atendimentoInformacao = (AtendimentoInformacao) SerializationUtils.clone(ai);

        atendimentoInformacao.setCodigo(null);
        atendimentoInformacao.setVersion(null);
        atendimentoInformacao.setDataSaida(null);
        atendimentoInformacao.setAtendimentoAlta(null);
        atendimentoInformacao.setProfissionalAlta(null);
        atendimentoInformacao.setMotivoAlta(null);
        atendimentoInformacao.setStatusAtendimento(AtendimentoInformacao.StatusAtendimento.ABERTO.value());
        atendimentoInformacao.setSequenciaCiclo(new Dinheiro(Coalesce.asLong(atendimentoInformacao.getSequenciaCiclo())).somar(1D).longValue());
        BOFactory.save(atendimentoInformacao);

        return atendimentoInformacao;
    }

    private void fecharAtendimentoInformacaoAntigo(AtendimentoInformacao ai) throws DAOException, ValidacaoException {
        ai.setStatusAtendimento(AtendimentoInformacao.StatusAtendimento.CONCLUIDO.value());
        ai.setDataSaida(DataUtil.getDataAtual());

        BOFactory.save(ai);
    }

    private ContaPaciente gerarNovaContaPaciente() throws DAOException, ValidacaoException {
        CadastroFechamentoContaDTO cadastroDTO = new CadastroFechamentoContaDTO();
        cadastroDTO.setAtendimentoInformacao(novoAtendimentoInformacao);
        cadastroDTO.setConvenio(novoAtendimentoInformacao.getConvenio());
        cadastroDTO.setUsuarioCadsus(usuarioCadsus);
        cadastroDTO.setDataCompetencia(Data.addMeses(contaPaciente.getCompetenciaAtendimento(), 1));

        return BOFactory.getBO(HospitalFacade.class).salvarContaPaciente(cadastroDTO);
    }

    private void atualizarSequenciaCiclosAtendimentos() throws DAOException, ValidacaoException {
        List<Long> codigosAtendimentoList = getSession().createCriteria(Atendimento.class)
                .setProjection(Projections.distinct(Projections.property(Atendimento.PROP_CODIGO)))
                .add(Restrictions.eq(Atendimento.PROP_ATENDIMENTO_PRINCIPAL, atendimentoPrincipal))
                .add(Restrictions.gt(Atendimento.PROP_DATA_CADASTRO, getDataDesdobramento()))
                .list();

        if (CollectionUtils.isNotNullEmpty(codigosAtendimentoList)) {
            Query queryUpdate = getSession().createQuery(" UPDATE Atendimento a "
                    + " SET a.sequencialCiclo = :ciclo "
                    + " WHERE a.codigo IN (:codigosAtendimentos) ");
            queryUpdate.setParameterList("codigosAtendimentos", codigosAtendimentoList);
            queryUpdate.setLong("ciclo", novoAtendimentoInformacao.getSequenciaCiclo());
            queryUpdate.executeUpdate();
        }
    }

    private void atualizarItensContaPaciente(ContaPaciente novaContaPaciente) throws DAOException, ValidacaoException {
        List<Long> codigosItemContaPacienteList = getSession().createCriteria(ItemContaPaciente.class)
                .setProjection(Projections.distinct(Projections.property(ItemContaPaciente.PROP_CODIGO)))
                .add(Restrictions.eq(ItemContaPaciente.PROP_CONTA_PACIENTE, contaPaciente))
                .add(Restrictions.gt(ItemContaPaciente.PROP_DATA_LANCAMENTO, getDataDesdobramento()))
                .list();

        if (CollectionUtils.isNotNullEmpty(codigosItemContaPacienteList)) {
            /* Foi necessário o flush, pois neste momento a nova conta de paciente não está 
             registrada no banco de dados e a mesma deve ser utilizada para atualizar os itens de conta; */
            getSession().flush();

            Query queryUpdate = getSession().createQuery(" UPDATE ItemContaPaciente icp "
                    + " SET icp.contaPaciente = :novaContaPaciente "
                    + " WHERE icp.codigo IN (:codigosItensContaPaciente) ");
            queryUpdate.setParameterList("codigosItensContaPaciente", codigosItemContaPacienteList);
            queryUpdate.setEntity("novaContaPaciente", novaContaPaciente);
            queryUpdate.executeUpdate();
        }
    }

    private Date getDataDesdobramento() {
        if (dataDesdobramento == null) {
            dataDesdobramento = Data.addDias(atendimentoPrincipal.getDataChegada(),
                    new Dinheiro(Coalesce.asLong(novoAtendimentoInformacao.getSequenciaCiclo()).intValue()).multiplicar(30D).intValue());
        }
        return Data.adjustRangeHour(dataDesdobramento).getDataInicial();
    }

    private void gerarAltaPaciente(Aih aih) throws DAOException, ValidacaoException {
        Atendimento ultimoAtendimento = LoadManager.getInstance(Atendimento.class)
                .addParameter(new QueryCustom.QueryCustomParameter(Atendimento.PROP_USUARIO_CADSUS, usuarioCadsus))
                .addInterceptor(
                        new LoadInterceptor() {
                            @Override
                            public void customHQL(HQLHelper hql, String alias) {
                                hql.addToWhereWhithAnd(alias + ".codigo = (select max(atendimento2.codigo)"
                                        + " from Atendimento atendimento2 "
                                        + " left join atendimento2.usuarioCadsus usuarioCadsus2 "
                                        + " where usuarioCadsus2.codigo = " + usuarioCadsus.getCodigo()
                                        + " and atendimento2.status <> " + Atendimento.STATUS_CANCELADO
                                        + " and atendimento2.atendimentoPrincipal.codigo = " + contaPaciente.getAtendimentoInformacao().getAtendimentoPrincipal().getCodigo()
                                        + " and atendimento2.sequencialCiclo <= " + contaPaciente.getAtendimentoInformacao().getSequenciaCiclo() + ")"
                                );
                            }
                        }
                )
                .setMaxResults(1)
                .start().getVO();

        if (aih.getProfissionalResponsavel() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_profissional_responsavel_aih"));
        }

        AtendimentoAlta atendimentoAlta = new AtendimentoAlta();
        atendimentoAlta.setDataAlta(DataUtil.getDataAtual());
        atendimentoAlta.setMotivoAlta(AtendimentoAlta.MotivoAlta.ENCERRAMENTO_ADMINISTRATIVO.value());
        atendimentoAlta.setAtendimento(ultimoAtendimento);

        BOFactory.getBO(AtendimentoFacade.class).gerarAltaAdministrativa(atendimentoAlta);
    }

    public Aih carregarAih() throws ValidacaoException {
        Aih aih = (Aih) getSession().createCriteria(Aih.class)
                .add(Restrictions.eq(Aih.PROP_CONTA_PACIENTE, contaPaciente))
                .add(Restrictions.ne(Aih.PROP_STATUS, Aih.Status.CANCELADA.value()))
                .uniqueResult();

        if (aih == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_aih_nao_encontrada"));
        }

        if (aih.getProfissionalResponsavel() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_profissional_responsavel_aih"));
        }

        return aih;
    }

    public void gerarNovaAih(ContaPaciente novaContaPaciente, Aih aih) throws DAOException, ValidacaoException {
        if (aih != null) {

            Aih novaAih = VOUtils.cloneObject(aih);

            //Remove autorizacao
            novaAih.setStatus(Aih.Status.AGUARDANDO_ANALISE.value());
            novaAih.setUsuarioAutorizacao(null);
            novaAih.setDtAutorizacao(null);
            novaAih.setNroAutorizacao(null);
            novaAih.setTpDocProfAut(null);
            novaAih.setNomeProfissionalAutorizador(null);
            novaAih.setProfissionalAutorizador(null);

            //Seta pra nova conta
            novaAih.setContaPaciente(novaContaPaciente);

            //A nova AIH não foi exportada ainda
            novaAih.setDataExportacao(null);

            //Ajusta as datas
            novaAih.setDataCadastro(DataUtil.getDataAtual());
            novaAih.setDataAlteracao(DataUtil.getDataAtual());

            //Aumenta em um mes a data de solicitacao
            if (aih.getDataSolicitacao() != null) {
                novaAih.setDataSolicitacao(Data.addMeses(aih.getDataSolicitacao(), 1));
            }
            BOFactory.save(novaAih);
        }
    }

    public Atendimento getAtendimento() {
        return this.atendimento;
    }
}
