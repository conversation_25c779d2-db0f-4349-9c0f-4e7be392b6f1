package br.com.celk.bo.cadsus.exclusao;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.FichaAcolhimento;
import java.util.List;
import java.util.Map;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class QueryUsuarioCadsusExclusaoFichaAcolhimento extends CommandQuery {

    private final List<Long> codigoProcessoList;
    private List<FichaAcolhimento> list;

    public QueryUsuarioCadsusExclusaoFichaAcolhimento(List<Long> codigoProcessoList) {
        this.codigoProcessoList = codigoProcessoList;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(FichaAcolhimento.class.getName());
        
        hql.addToSelect("fa.codigo", "codigo");
        hql.addToSelect("fa.dataAdmissao", "dataAdmissao");
        hql.addToSelect("fa.somatorioHistoricoSaude", "somatorioHistoricoSaude");
        hql.addToSelect("fa.tempoFrequentoOutrosServicos", "tempoFrequentoOutrosServicos");
        hql.addToSelect("fa.consultouPsiquiatraOutros", "consultouPsiquiatraOutros");
        hql.addToSelect("fa.servicoPsiquiatra", "servicoPsiquiatra");
        hql.addToSelect("fa.consultouNeurologista", "consultouNeurologista");
        hql.addToSelect("fa.examesRealizadosNeuro", "examesRealizadosNeuro");
        hql.addToSelect("fa.cirurgiasFeitas", "cirurgiasFeitas");
        hql.addToSelect("fa.outrosDadosHistoricoSaude", "outrosDadosHistoricoSaude");
        hql.addToSelect("fa.relacoesTrabalho", "relacoesTrabalho");
        hql.addToSelect("fa.comoComecou", "comoComecou");
        hql.addToSelect("fa.comOqueComecou", "comOqueComecou");
        hql.addToSelect("fa.quandoUsa", "quandoUsa");
        hql.addToSelect("fa.praQueUsa", "praQueUsa");
        hql.addToSelect("fa.acompanhado", "acompanhado");
        hql.addToSelect("fa.ondeUsa", "ondeUsa");
        hql.addToSelect("fa.estagioMotivacional", "estagioMotivacional");
        hql.addToSelect("fa.notaHistoriaUso", "notaHistoriaUso");
        hql.addToSelect("fa.quantasInternacoes", "quantasInternacoes");
        hql.addToSelect("fa.ondeInternacao", "ondeInternacao");
        hql.addToSelect("fa.quandoInternacao", "quandoInternacao");
        hql.addToSelect("fa.observacaoEstadoMental", "observacaoEstadoMental");
        hql.addToSelect("fa.avaliacaoTestagem", "avaliacaoTestagem");
        hql.addToSelect("fa.planoTerapeutico", "planoTerapeutico");
        hql.addToSelect("fa.observacaoAtividadeTerapeutica", "observacaoAtividadeTerapeutica");
        hql.addToSelect("fa.descricaoOutroHistoricoSaude", "descricaoOutroHistoricoSaude");
        hql.addToSelect("fa.frequentouOutrosServicos", "frequentouOutrosServicos");
        hql.addToSelect("fa.horaEntrada", "horaEntrada");
        hql.addToSelect("fa.horaSaida", "horaSaida");
        hql.addToSelect("fa.status", "status");
        hql.addToSelect("fa.moradorRua", "moradorRua");
        hql.addToSelect("fa.version", "version");
        
        hql.addToSelect("ua.codigo", "ultimoAtendimento.codigo");
        hql.addToSelect("ua.dataChegada", "ultimoAtendimento.dataChegada");
        hql.addToSelect("ua.dataAtendimento", "ultimoAtendimento.dataAtendimento");
        
        hql.addToSelect("te.codigo", "tipoEncaminhamento.codigo");
        hql.addToSelect("te.descricao", "tipoEncaminhamento.descricao");
        
        hql.addToSelect("p.codigo", "atendimento.profissional.codigo");
        hql.addToSelect("p.nome", "atendimento.profissional.nome");
        
        hql.addToSelect("c.codigo", "cid.codigo");
        hql.addToSelect("c.descricao", "cid.descricao");
        
        hql.addToSelect("a.codigo", "atendimento.codigo");
        hql.addToSelect("a.version", "atendimento.version");
        
        hql.addToSelect("uc.codigo", "atendimento.usuarioCadsus.codigo");
        hql.addToSelect("uc.nome", "atendimento.usuarioCadsus.nome");
        hql.addToSelect("uc.motivoExclusao", "atendimento.usuarioCadsus.motivoExclusao");

        hql.addToFrom("FichaAcolhimento fa "
                + " left join fa.ultimoAtendimento ua"
                + " left join fa.tipoEncaminhamento te"
                + " left join fa.cid c"
                + " left join fa.atendimento a"
                + " left join a.profissional p"
                + " left join a.usuarioCadsus uc"
        );

        hql.addToWhereWhithAnd("fa.codigo in :codigoProcessoList");
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setParameterList("codigoProcessoList", codigoProcessoList);        
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    public List<FichaAcolhimento> getResult() {
        return this.list;
    }
    
}