package br.com.celk.bo.materiais.produto;

import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.MovimentoEstoqueFacade;
import br.com.ksisolucoes.bo.entradas.estoque.produto.QueryProdutoEstoque;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Parametro;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoque;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoquePK;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ManutencaoGrupoSubgrupoProduto extends AbstractCommandTransaction<ManutencaoGrupoSubgrupoProduto> {

    private Produto produto;
    private SubGrupo subGrupo;
    private TipoDocumento tipoDocumento;

    public ManutencaoGrupoSubgrupoProduto(Produto produto, SubGrupo subGrupo) {
        this.produto = produto;
        this.subGrupo = subGrupo;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Parametro parametro = CargaBasicoPadrao.getInstance().getParametroPadrao();
        //Seta tipo de documento padrão para inventário, ou seja, tipo documento do tipo entrada
        tipoDocumento = parametro.getTipoDocumentoEntrada();

        RetornoValidacao retornoValidacao = new RetornoValidacao();
        if (tipoDocumento == null) {
            retornoValidacao.add(Bundle.getStringApplication("msg_parametro_X_nao_definido", Bundle.getStringApplication("rotulo_tipo_documento", this.sessao.getLocale())), "parametro." + Parametro.PROP_TIPO_DOCUMENTO);
        }

        if (!retornoValidacao.isValido()) {
            throw new ValidacaoException(retornoValidacao);
        }

        this.produto = (Produto) getSession().get(Produto.class, produto.getCodigo());
        this.subGrupo = (SubGrupo) getSession().get(SubGrupo.class, subGrupo.getId());

        List<GrupoEstoque> lstGrupoEstoque = new QueryProdutoEstoque(produto).start().getDtoList();

        // Zera o estoque
        movimentarEstoque(lstGrupoEstoque, true);

        // Atualiza o subgrupo do produto
        this.produto.setSubGrupo(subGrupo);
        BOFactory.save(this.produto);

        // Dá novamente entrada no estoque do produto
        movimentarEstoque(lstGrupoEstoque, false);
    }

    private void movimentarEstoque(List<GrupoEstoque> lstGrupoEstoque, boolean zerarEstoque) throws DAOException, ValidacaoException {
        for (GrupoEstoque grupoEstoque : lstGrupoEstoque) {
            Empresa empresa = grupoEstoque.getId().getEstoqueEmpresa().getId().getEmpresa();

            MovimentoEstoque movimentoEstoque = new MovimentoEstoque();
            movimentoEstoque.setProduto(this.produto);
            MovimentoEstoquePK movimentoEstoquePK = new MovimentoEstoquePK();
            movimentoEstoquePK.setEmpresa(empresa);
            movimentoEstoque.setId(movimentoEstoquePK);
            movimentoEstoque.setTipoDocumento(tipoDocumento);
            movimentoEstoque.setDeposito(grupoEstoque.getRoDeposito());
            movimentoEstoque.setGrupoEstoque(grupoEstoque.getId().getGrupo());
            movimentoEstoque.setDataValidadeGrupoEstoque(grupoEstoque.getDataValidade());
            movimentoEstoque.setObservacao(Bundle.getStringApplication("msg_lancto_gerado_troca_grupo_subgrupo"));

            if (zerarEstoque) {
                movimentoEstoque.setQuantidade(new Double(0));
            } else {
                movimentoEstoque.setQuantidade(grupoEstoque.getEstoqueFisico());
            }

            BOFactory.getBO(MovimentoEstoqueFacade.class).save(movimentoEstoque);
            getSession().flush();
        }
    }

}
