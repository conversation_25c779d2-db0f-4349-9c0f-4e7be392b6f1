package br.com.celk.bo.atendimento.prontuario;

import br.com.celk.atendimento.prontuario.interfaces.dto.AtendimentoProntuarioDTO;
import br.com.celk.atendimento.prontuario.interfaces.dto.ProntuariosDemmandPaggingDTOParam;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoProntuario;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class ProntuariosDemmandPagging extends AbstractCommandTransaction {

    private final ProntuariosDemmandPaggingDTOParam param;
    private List<AtendimentoProntuarioDTO> prontuarios;

    public ProntuariosDemmandPagging(ProntuariosDemmandPaggingDTOParam param) {
        this.param = param;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        QueryAtendimentoPrincipalProntuarios query = new QueryAtendimentoPrincipalProntuarios(param);
        query.start();
        List<Long> codigosProntuarios = query.getCodigos();
        if (CollectionUtils.isNotNullEmpty(codigosProntuarios)) {
            QueryProntuariosDemmandPagging queryProntuariosDemmandPagging = new QueryProntuariosDemmandPagging(codigosProntuarios);
            queryProntuariosDemmandPagging.start();
            prontuarios = queryProntuariosDemmandPagging.getResult();
        }
    }

    public List<AtendimentoProntuarioDTO> getProntuarios() {
        return prontuarios;
    }

    private class QueryProntuariosDemmandPagging extends CommandQuery<QueryProntuariosDemmandPagging> {

        private List<AtendimentoProntuarioDTO> result;
        private final List<Long> codigosProntuarios;

        public QueryProntuariosDemmandPagging(List<Long> codigosProntuarios) {
            this.codigosProntuarios = codigosProntuarios;
        }

        @Override
        protected void createQuery(HQLHelper hql) {
            hql.addToSelect(new HQLProperties(AtendimentoProntuario.class, "ap").getSingleProperties());

            hql.addToSelect("ap.codigo", "codigo");
            hql.addToSelect("ap.codigoAtendimentoPrincipalExterno", "codigoAtendimentoPrincipalExterno");

            hql.addToSelect("aPrinc.codigo", "atendimento.atendimentoPrincipal.codigo");
            hql.addToSelect("aPrinc.dataAtendimento", "atendimento.atendimentoPrincipal.dataAtendimento");

            hql.addToSelect("prof.codigo", "profissional.codigo");
            hql.addToSelect("prof.nome", "profissional.nome");
            hql.addToSelect("prof.unidadeFederacaoConselhoRegistro", "profissional.unidadeFederacaoConselhoRegistro");
            hql.addToSelect("prof.numeroRegistro", "profissional.numeroRegistro");

            hql.addToSelect("cbo.cbo", "tabelaCbo.cbo");
            hql.addToSelect("cbo.descricao", "tabelaCbo.descricao");

            hql.addToSelect("conselhoClasse.codigo", "profissional.conselhoClasse.codigo");
            hql.addToSelect("conselhoClasse.descricao", "profissional.conselhoClasse.descricao");
            hql.addToSelect("conselhoClasse.sigla", "profissional.conselhoClasse.sigla");

            hql.addToSelect("emp.codigo", "empresa.codigo");
            hql.addToSelect("emp.descricao", "empresa.descricao");

            hql.addToSelect("npta.codigo", "atendimento.atendimentoPrincipal.naturezaProcuraTipoAtendimento.codigo");

            hql.addToSelect("ta.codigo", "atendimento.atendimentoPrincipal.naturezaProcuraTipoAtendimento.tipoAtendimento.codigo");
            hql.addToSelect("ta.descricao", "atendimento.atendimentoPrincipal.naturezaProcuraTipoAtendimento.tipoAtendimento.descricao");

            hql.addToSelect("cp.codigo", "contaPaciente.codigo");
            hql.addToSelect("cp.dataGeracao", "contaPaciente.dataGeracao");
            hql.addToSelect("cp.dataFinal", "contaPaciente.dataFinal");

            hql.setTypeSelect(AtendimentoProntuarioDTO.class.getName());

            StringBuilder sbFrom = new StringBuilder();
            sbFrom.append("AtendimentoProntuario ap ");
            sbFrom.append(" join ap.usuarioCadsus uc ");
            sbFrom.append(" left join ap.contaPaciente cp");
            sbFrom.append(" left join ap.profissional prof ");
            sbFrom.append(" left join ap.tabelaCbo cbo ");
            sbFrom.append(" left join prof.conselhoClasse conselhoClasse ");
            sbFrom.append(" left join ap.atendimento a ");
            sbFrom.append(" left join a.atendimentoPrincipal aPrinc ");
            sbFrom.append(" left join ap.empresa emp ");
            sbFrom.append(" left join aPrinc.naturezaProcuraTipoAtendimento npta ");
            sbFrom.append(" left join npta.tipoAtendimento ta ");

            hql.addToFrom(sbFrom.toString());

            hql.addToWhereWhithAnd("uc.codigo = ", param.getAtendimento().getUsuarioCadsus().getCodigo());
            hql.addToWhereWhithAnd("ap.codigo in ", codigosProntuarios);
            if (Coalesce.asString(param.getDescricao()).trim().length() > 0) {
                hql.addToWhereWhithAnd(hql.getConsultaLiked("ap.descricao", param.getDescricao()));
            }

            if (param.getTipoRegistroProntuario() != null) {
                hql.addToWhereWhithAnd("coalesce(ap.tipoRegistro,1) = ", param.getTipoRegistroProntuario());
            }

//            hql.addToWhereWhithAnd("ap.flagOrigem = ", AtendimentoProntuario.Origem.INTERNO.value());

            HQLHelper hqlWhere = hql.getNewInstanceSubQuery();
            if (param.getTipoAtendimento() != null) {
                hql.addToWhereWhithAnd("ap.descricaoTipoAtendimento = ", param.getTipoAtendimento().getDescricao());
            }

            if (param.getPeriodo() != null) {
                Date dataAtual = DataUtil.getDataAtual();
                DatePeriod periodo = Data.adjustRangeHour(new DatePeriod(Data.removeMeses(dataAtual, param.getPeriodo()), dataAtual));
                hql.addToWhereWhithAnd("ap.data", periodo);
            }

            hql.addToOrder("ap.data desc");
        }

        @Override
        public List<AtendimentoProntuarioDTO> getResult() {
            return this.result;
        }

        @Override
        protected void result(HQLHelper hql, Object result) {
            this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
        }
    }

}
