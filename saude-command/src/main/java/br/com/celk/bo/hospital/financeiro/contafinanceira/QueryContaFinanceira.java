package br.com.celk.bo.hospital.financeiro.contafinanceira;

import br.com.celk.bo.hospital.financeiro.interfaces.dto.QueryConsultaContaFinanceiraDTOParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.hospital.financeiro.ContaFinanceira;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryContaFinanceira extends CommandQuery<QueryContaFinanceira> {

    private QueryConsultaContaFinanceiraDTOParam param;
    private ContaFinanceira contaFinanceira;

    public QueryContaFinanceira(QueryConsultaContaFinanceiraDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(ContaFinanceira.class.getName());

        hql.addToSelect("cf.codigo", "codigo");
        hql.addToSelect("cf.dataEmissao", "dataEmissao");
        hql.addToSelect("cf.dataCriacao", "dataCriacao");
        hql.addToSelect("cf.status", "status");
        hql.addToSelect("cf.version", "version");
        hql.addToSelect("cf.valor", "valor");
        hql.addToSelect("cf.valorPago", "valorPago");
        hql.addToSelect("cf.valorDesconto", "valorDesconto");
        
        hql.addToSelect("ap.codigo", "atendimentoPrincipal.codigo");
        
        hql.addToSelect("uc.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("uc.nome", "usuarioCadsus.nome");
        
        hql.addToSelect("fp.codigo", "formaPagamento.codigo");
        hql.addToSelect("fp.descricao", "formaPagamento.descricao");
        
        hql.addToSelect("tmcf.codigo", "formaPagamento.tipoMovimentoContaFinanceira.codigo");
        hql.addToSelect("tmcf.descricao", "formaPagamento.tipoMovimentoContaFinanceira.descricao");
        hql.addToSelect("tmcf.tipoMovimento", "formaPagamento.tipoMovimentoContaFinanceira.tipoMovimento");
        
        hql.addToSelect("cp.codigo", "contaPaciente.codigo");
        hql.addToSelect("cp.status", "contaPaciente.status");

        hql.addToFrom("ContaFinanceira cf "
                + "left join cf.atendimentoPrincipal ap "
                + "left join cf.usuarioCadsus uc "
                + "left join cf.formaPagamento fp "
                + "left join fp.tipoMovimentoContaFinanceira tmcf "
                + "left join cf.contaPaciente cp ");

        hql.addToWhereWhithAnd("cf.atendimentoPrincipal = ", this.param.getAtendimentoPrincipal());
        hql.addToWhereWhithAnd("cf.usuarioCadsus = ", this.param.getUsuarioCadsus());
        hql.addToWhereWhithAnd("cf.status in ", this.param.getStatusList());
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        try {
            this.contaFinanceira = (ContaFinanceira) hql.getBean((List) result);
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage());
        }
    }

    public ContaFinanceira getContaFinanceira() {
        return contaFinanceira;
    }
    
}
