package br.com.celk.bo.atendimento.acolhimentoexterno;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;

public class AcolhimentoExternoParametroGEM {


    public static Long horas(){
        Long horas = 48L;
        try {
            horas = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("horasLimiteAcolhimentoExterno");
            horas = horas != 0L ? horas : 48L;

        } catch (DAOException e) {
            Loggable.log.error(e);
        }
        return horas;
    }

    public static Long periodo(){
        Long horas = 6L;
        try {
            horas = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("horasPeriodoAcolhimentoExterno");
            horas = horas != 0L ? horas : 6L;
        } catch (DAOException e) {
            Loggable.log.error(e);
        }
        return horas;
    }
}
