/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.celk.aih.query;

import br.com.celk.aih.dto.AIHAtendimentoDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AutorizacaoInternacaoHospitalarDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaAtendimento extends CommandQuery<QueryConsultaAtendimento> {

    AutorizacaoInternacaoHospitalarDTO param;
    List<Atendimento> result;

    public QueryConsultaAtendimento(AutorizacaoInternacaoHospitalarDTO param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.addToSelect("atendimento.codigo", "codigo");

        hql.addToSelect("u.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("u.nome", "usuarioCadsus.nome");

        hql.addToSelect("e.codigo", "empresa.codigo");
        hql.addToSelect("e.descricao", "empresa.descricao");

        hql.addToSelect("ap.codigo", "atendimentoPrincipal.codigo");

        hql.addToSelect("npta.codigo", "naturezaProcuraTipoAtendimento.codigo");

        hql.addToSelect("ta.codigo", "naturezaProcuraTipoAtendimento.tipoAtendimento.codigo");
        hql.addToSelect("ta.descricao", "naturezaProcuraTipoAtendimento.tipoAtendimento.descricao");

        hql.setTypeSelect(Atendimento.class.getName());

        hql.addToFrom("Atendimento atendimento"
                + " left join atendimento.usuarioCadsus u"
                + " left join atendimento.atendimentoPrincipal ap"
                + " left join atendimento.naturezaProcuraTipoAtendimento npta"
                + " left join npta.tipoAtendimento ta"
                + " left join atendimento.empresa e");

        hql.addToWhereWhithAnd("u.codigo = ", param.getAutorizacaoInternacaoHospitalar().getUsuarioCadSus().getCodigo());
        hql.addToWhereWhithAnd("atendimento.status <> ", Atendimento.STATUS_CANCELADO);

        hql.addToWhereWhithAnd("atendimento.codigo = ap.codigo");
        hql.addToWhereWhithAnd("not exists(select 1 from Aih aih left join aih.atendimento a where a.codigo = ap.codigo)");
        Convenio convenioSus = (Convenio) BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("convenioSUS");

        hql.addToWhereWhithAnd("atendimento.convenio = ", convenioSus.getCodigo());
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    public List<Atendimento> getResult() {
        return result;
    }

}
