package br.com.celk.services.mobile;

import br.com.ksisolucoes.vo.mobile.DispositivosMoveis;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class VerificarDispositivoRegistrado extends AbstractCommandTransaction{

    private final String id;
    private boolean dispositivoRegistrado;

    public VerificarDispositivoRegistrado(String id) {
        this.id = id;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Long count =  (Long) getSession().createCriteria(DispositivosMoveis.class)
            .setProjection(Projections.count(DispositivosMoveis.PROP_CODIGO))
            .add(Restrictions.eq(DispositivosMoveis.PROP_IDENTIFICADOR, id))
            .uniqueResult();
        
        dispositivoRegistrado = count > 0;
    }

    public boolean isDispositivoRegistrado() {
        return dispositivoRegistrado;
    }
}
