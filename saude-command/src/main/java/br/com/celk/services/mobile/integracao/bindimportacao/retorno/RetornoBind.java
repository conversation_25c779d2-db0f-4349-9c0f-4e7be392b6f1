package br.com.celk.services.mobile.integracao.bindimportacao.retorno;

import org.apache.camel.dataformat.bindy.annotation.CsvRecord;
import org.apache.camel.dataformat.bindy.annotation.DataField;

/**
 *
 * <AUTHOR>
 */
@CsvRecord(separator = "\\|", crlf = "UNIX")
public class RetornoBind {
    
    @DataField(pos = 1, required=true)
    private Long codigoMobile;
    @DataField(pos = 2)
    private Long codigoSistema;
    @DataField(pos = 3)
    private Long versaoSistema;
    @DataField(pos = 4)
    private Long status;
    @DataField(pos = 5)
    private String mensagem;
    
    public Long getCodigoMobile() {
        return codigoMobile;
    }

    public void setCodigoMobile(Long codigoMobile) {
        this.codigoMobile = codigoMobile;
    }

    public Long getCodigoSistema() {
        return codigoSistema;
    }

    public void setCodigoSistema(Long codigoSistema) {
        this.codigoSistema = codigoSistema;
    }

    public Long getVersaoSistema() {
        return versaoSistema;
    }

    public void setVersaoSistema(Long versaoSistema) {
        this.versaoSistema = versaoSistema;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }
}
