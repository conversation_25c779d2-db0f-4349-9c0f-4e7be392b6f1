package br.com.celk.services.mobile.integracao.importarrecurso;

import br.com.ksisolucoes.command.ImportacaoMobileCommand;

/**
 *
 * <AUTHOR>
 */
public interface IBindVoImp<T> {

    public Class getClassVo();
    
    public T customProperties(T vo, ImpUtil impUtil);

    public Long getCodigoMobile();
    
    public Long getCodigoSistema();

    public Long getCodigoUsuario();

    public ImportacaoMobileCommand customProcess(T convertedVo) throws Exception;
}
