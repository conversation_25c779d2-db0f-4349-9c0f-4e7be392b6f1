package br.com.celk.services.mobile.integracao.bindexportacao.perguntapesquisa;

import br.com.celk.services.mobile.integracao.exportarrecurso.IBindVoExport;
import br.com.ksisolucoes.vo.basico.pesquisa.PerguntaPesquisa;
import org.apache.camel.dataformat.bindy.annotation.CsvRecord;
import org.apache.camel.dataformat.bindy.annotation.DataField;

/**
 *
 * <AUTHOR>
 */
@CsvRecord(separator = "\\|",  crlf = "UNIX")
public class PerguntaPesquisaBind implements IBindVoExport<PerguntaPesquisa>{

    @DataField(pos = 1, required = true)
    private Long codigoSistema;
    @DataField(pos = 2, required = true)
    private Long versao;
    @DataField(pos = 3, required = true)
    private String descricao;

    @Override
    public void buildProperties(PerguntaPesquisa vo) {
        codigoSistema = vo.getCodigo();
        versao = vo.getVersionAll();
        descricao = vo.getDescricao();
    }
}
