package br.com.celk.services.mobile.integracao.bindexportacao.usuariocadsusdado;

import br.com.celk.services.mobile.integracao.exportarrecurso.IBindVoExport;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import org.apache.camel.dataformat.bindy.annotation.CsvRecord;
import org.apache.camel.dataformat.bindy.annotation.DataField;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@CsvRecord(separator = "\\|",  crlf = "UNIX")
public class UsuarioCadsusDadoBind implements IBindVoExport<UsuarioCadsusDado>{

    @DataField(pos = 1, required = true)
    private Long codigoSistema;
    @DataField(pos = 2, required = true)
    private Long versao;
    @DataField(pos = 3, pattern = "dd-MM-yyyy HH:mm:ss", required = true)
    private Date dataDados;
    @DataField(pos = 4)
    private Long pesoEmGramas;
    @DataField(pos = 5)
    private Long alturaEmMilimetros;
    @DataField(pos = 6)
    private Long pesoNascer;
    @DataField(pos = 7)
    private Long temperatura;
    @DataField(pos = 8)
    private Long afericaoPas;
    @DataField(pos = 9)
    private Long afericaoPad;
    @DataField(pos = 10)
    private Long glicemiaCapilar;
    @DataField(pos = 11)
    private Long glicemiaColeta;

    @Override
    public void buildProperties(UsuarioCadsusDado vo) {
        codigoSistema = vo.getCodigo();
        versao = vo.getVersionAll();
        dataDados = vo.getDataDados();
        pesoEmGramas = new Dinheiro(Coalesce.asDouble(vo.getPeso())).multiplicar(1000).longValue();
        if (vo.getAltura() != null) {
            alturaEmMilimetros = new Dinheiro(Coalesce.asDouble(vo.getAltura())).multiplicar(10).longValue();
        }
        if (vo.getTemperatura() != null) {
            temperatura = new Dinheiro(Coalesce.asDouble(vo.getTemperatura())).multiplicar(10).longValue();
        }
        pesoNascer = vo.getPesoNascer();
        afericaoPas = vo.getPressaoArterialSistolica();
        afericaoPad = vo.getPressaoArterialDiastolica();
        glicemiaCapilar = vo.getGlicemia();
        glicemiaColeta = vo.getGlicemiaTipo();
    }
    
}
