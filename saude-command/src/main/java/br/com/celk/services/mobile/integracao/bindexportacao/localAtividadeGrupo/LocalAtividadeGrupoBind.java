package br.com.celk.services.mobile.integracao.bindexportacao.localAtividadeGrupo;

import br.com.celk.services.mobile.integracao.exportarrecurso.IBindVoExport;
import br.com.ksisolucoes.vo.atividadegrupo.LocalAtividadeGrupo;
import org.apache.camel.dataformat.bindy.annotation.CsvRecord;
import org.apache.camel.dataformat.bindy.annotation.DataField;

/**
 * <AUTHOR>
 */
@CsvRecord(separator = "\\|", crlf = "UNIX")
public class LocalAtividadeGrupoBind implements IBindVoExport<LocalAtividadeGrupo> {

    @DataField(pos = 1, required = true)
    private Long codigoSistema;
    @DataField(pos = 2)
    private Long versao;
    @DataField(pos = 3)
    private Long cd_local_acao;
    @DataField(pos = 4)
    private String ds_local_acao;
    @DataField(pos = 5)
    private Long empresa;
    @DataField(pos = 6)
    private Long tipo_local;
    @DataField(pos = 7)
    private Long cod_inep;

    @Override
    public void buildProperties(LocalAtividadeGrupo vo) {
        codigoSistema = vo.getCodigo();
        versao = vo.getVersionAll();
        cd_local_acao = vo.getCodigo();
        ds_local_acao = vo.getDescricao();
        if (vo.getEmpresa() != null) {
            empresa = vo.getEmpresa().getCodigo();
        }
        tipo_local = vo.getTipoLocal();
        cod_inep = vo.getNumeroInep();
    }
}
