package br.com.celk.services.mobile.integracao.exportarrecurso;

import br.com.celk.integracao.MobileHelper;
import br.com.ksisolucoes.util.log.Loggable;
import org.hibernate.Query;

import javax.resource.ResourceException;
import java.util.List;

/**
 * <AUTHOR>
 *         Classe utilitaria para exportacao, especifica filtros necessarios
 */
public class ExportarRecursoHelper extends MobileHelper {

    //<editor-fold defaultstate="collapsed" desc="Variaveis locais">
    private final Long codigoProfissional;
    private int first;
    private final Long versao;
    private final Long versaoFinal;
    private final int limit;
    //</editor-fold>

    //<editor-fold defaultstate="collapsed" desc="Construtores">
    ExportarRecursoHelper(Long versao, Long versaoFinal, Long codigoProfissional, int first, int limit) {
        this.versao = versao;
        this.versaoFinal = versaoFinal;
        this.first = first;
        this.limit = limit;
        this.codigoProfissional = codigoProfissional;
    }
    //</editor-fold>

    //<editor-fold defaultstate="collapsed" desc="Metodos publicos">

    public void nextPage() {
        this.first += this.limit;
    }
    //</editor-fold>

    //<editor-fold defaultstate="collapsed" desc="Getters and setters">
    public Long getCodigoProfissional() {
        return this.codigoProfissional;
    }

    public Long getVersao() {
        return this.versao;
    }

    public Long getVersaoFinal() {
        return this.versaoFinal;
    }

    public int getLimit() {
        return this.limit;
    }

    public int getFirst() {
        return this.first;
    }
    //</editor-fold>

    //<editor-fold defaultstate="collapsed" desc="Case dos recursos por profissional/Micro-área/Global">
    public List<?> getResourceList(TypeExpEnum exportacao) {
        switch (exportacao) {
            case USUARIO_CADSUS:
                return filter(emUsuarioCadSus(), exportacao);

            case USUARIO_CADSUS_CNS:
                return filter(emUsuarioCadSusCns(), exportacao);

            case USUARIO_CADSUS_DOCUMENTO:
                return filter(emUsuarioCadSusDocumento(), exportacao);

            case PACIENTE_ESUS:
                return filter(emUsuarioCadSusEsus(), exportacao);

            case USUARIO_CADSUS_DADO:
                return filter(emUsuarioCadSusDado(), exportacao);

            case ENDERECO:
                return filter(emEnderecoUsuarioCadSus(), exportacao);

            case DOMICILIO:
                return filter(emEnderecoDomicilioUsuarioCadSus(), exportacao);

            case DOMICILIO_ESUS:
                return filter(emEnderecoDomicilioEsusUsuarioCadSus(), exportacao);

            case NOTIFICACAO_PACIENTE:
                return filter(emUsuarioCadSusNotificacao(), exportacao);

            case NOTIFICACAO_AGENDA:
                return filter(emNotificacaoAgenda(), exportacao);

            case CEP_BRASIL:
                return filter(emCepBrasil(), exportacao);

            case NASCIDOS_VIVOS:
                return filter(emFichaNascidoVivo(), exportacao);

            case VAC_APLICACAO:
                return filter(emVacinaAplicacao(), exportacao);
            default:
                return null;
        }

    }
    //</editor-fold>

    //<editor-fold defaultstate="collapsed" desc="Select padrao">
    private StringBuilder selectTabelaFrom() {
        return new StringBuilder("SELECT tabela FROM ");
    }
    //</editor-fold>

    //<editor-fold defaultstate="collapsed" desc="HQL por profissional/Micro-área/Área">
    private StringBuilder emUsuarioCadSus() {
        return selectTabelaFrom()
                .append("UsuarioCadsus tabela ")
                .append("left join fetch tabela.enderecoDomicilio enderecoDomicilio ")
                .append("left join enderecoDomicilio.equipeMicroArea equipeMicroArea ")
                .append("left join equipeMicroArea.equipeProfissional equipeProfissional ")
                .append("left join equipeProfissional.profissional profissional ")
                .append("WHERE ")
                .append("profissional.codigo = :codigoProfissional");
    }

    private StringBuilder emUsuarioCadSusEsus() {
        return selectTabelaFrom()
                .append("UsuarioCadsusEsus tabela ")
                .append("left join tabela.usuarioCadsus usuarioCadsus ")
                .append("left join usuarioCadsus.enderecoDomicilio enderecoDomicilio ")
                .append("left join enderecoDomicilio.equipeMicroArea equipeMicroArea ")
                .append("left join equipeMicroArea.equipeProfissional equipeProfissional ")
                .append("left join equipeProfissional.profissional profissional ")
                .append("WHERE ")
                .append("profissional.codigo = :codigoProfissional");
    }

    private StringBuilder emUsuarioCadSusCns() {
        return selectTabelaFrom()
                .append("UsuarioCadsusCns tabela ")
                .append("left join fetch tabela.usuarioCadsus usuarioCadsus ")
                .append("left join usuarioCadsus.enderecoDomicilio enderecoDomicilio ")
                .append("left join enderecoDomicilio.equipeMicroArea equipeMicroArea ")
                .append("left join equipeMicroArea.equipeProfissional equipeProfissional ")
                .append("left join equipeProfissional.profissional profissional ")
                .append("WHERE ")
                .append("tabela.excluido = 0 ")
                .append("AND ")
                .append("profissional.codigo = :codigoProfissional");
    }

    private StringBuilder emUsuarioCadSusNotificacao() {
        return selectTabelaFrom()
                .append("UsuarioCadsusNotificacao tabela ")
                .append("left join fetch tabela.usuarioCadsus usuarioCadsus ")
                .append("left join usuarioCadsus.enderecoDomicilio enderecoDomicilio ")
                .append("left join enderecoDomicilio.equipeMicroArea equipeMicroArea ")
                .append("left join equipeMicroArea.equipeProfissional equipeProfissional ")
                .append("left join equipeProfissional.profissional profissional ")
                .append("WHERE ")
                .append("profissional.codigo = :codigoProfissional");
    }

    private StringBuilder emNotificacaoAgenda() {
        return selectTabelaFrom()
                .append("NotificacaoAgendas tabela ")
                .append("left join fetch tabela.agendaGradeAtendimentoHorario agendaGradeAtendimentoHorario ")
                .append("left join fetch agendaGradeAtendimentoHorario.usuarioCadsus usuarioCadsus ")
                .append("left join usuarioCadsus.enderecoDomicilio enderecoDomicilio ")
                .append("left join enderecoDomicilio.equipeMicroArea equipeMicroArea ")
                .append("left join equipeMicroArea.equipeProfissional equipeProfissional ")
                .append("left join equipeProfissional.profissional profissional ")
                .append("WHERE ")
                .append("profissional.codigo = :codigoProfissional");
    }

    private StringBuilder emUsuarioCadSusDocumento() {
        return selectTabelaFrom()
                .append("UsuarioCadsusDocumento tabela ")
                .append("left join fetch tabela.usuarioCadsus usuarioCadsus ")
                .append("left join usuarioCadsus.enderecoDomicilio enderecoDomicilio ")
                .append("left join enderecoDomicilio.equipeMicroArea equipeMicroArea ")
                .append("left join equipeMicroArea.equipeProfissional equipeProfissional ")
                .append("left join equipeProfissional.profissional profissional ")
                .append("WHERE ")
                .append("tabela.situacaoExcluido = 0 ")
                .append("AND ")
                .append("profissional.codigo = :codigoProfissional");
    }

    private StringBuilder emUsuarioCadSusDado() {
        return selectTabelaFrom()
                .append("UsuarioCadsusDado tabela, UsuarioCadsus usuarioCadsus ")
                .append("left join usuarioCadsus.enderecoDomicilio enderecoDomicilio ")
                .append("left join enderecoDomicilio.equipeMicroArea equipeMicroArea ")
                .append("left join equipeMicroArea.equipeProfissional equipeProfissional ")
                .append("left join equipeProfissional.profissional profissional ")
                .append("WHERE ")
                .append("tabela.codigo = usuarioCadsus.codigo ")
                .append("AND ")
                .append("profissional.codigo = :codigoProfissional");
    }


    private StringBuilder emEnderecoUsuarioCadSus() {
        return selectTabelaFrom()
                .append("EnderecoDomicilio enderecoDomicilio ")
                .append("left join enderecoDomicilio.enderecoUsuarioCadsus tabela ")
                .append("left join enderecoDomicilio.equipeMicroArea equipeMicroArea ")
                .append("left join equipeMicroArea.equipeProfissional equipeProfissional ")
                .append("left join equipeProfissional.profissional profissional ")
                .append("WHERE ")
                .append("profissional.codigo = :codigoProfissional ");
    }

    private StringBuilder emEnderecoDomicilioEsusUsuarioCadSus() {
        return selectTabelaFrom()
                .append("EnderecoDomicilioEsus tabela ")
                .append("left join fetch tabela.enderecoDomicilio enderecoDomicilio ")
                .append("left join enderecoDomicilio.equipeMicroArea equipeMicroArea ")
                .append("left join equipeMicroArea.equipeProfissional equipeProfissional ")
                .append("left join equipeProfissional.profissional profissional ")
                .append("WHERE ")
                .append("enderecoDomicilio.excluido = 0 ")
                .append("AND ")
                .append("profissional.codigo = :codigoProfissional ");

    }

    private StringBuilder emEnderecoDomicilioUsuarioCadSus() {
        return selectTabelaFrom()
                .append("EnderecoDomicilio tabela ")
                .append("left join fetch tabela.enderecoUsuarioCadsus enderecoUsuarioCadsus ")
                .append("left join tabela.equipeMicroArea equipeMicroArea ")
                .append("left join equipeMicroArea.equipeProfissional equipeProfissional ")
                .append("left join equipeProfissional.profissional profissional ")
                .append("WHERE ")
                .append("tabela.excluido = 0 ")
                .append("AND ")
                .append("profissional.codigo = :codigoProfissional ");
    }

    private StringBuilder emCepBrasil() {
        return selectTabelaFrom()
                .append("CepBrasil tabela ")
                .append("left join tabela.cidade cidade, ")
                .append("EquipeProfissional equipeProfissional ")
                .append("left join equipeProfissional.profissional profissional ")
                .append("left join equipeProfissional.equipeMicroArea equipeMicroArea ")
                .append("left join equipeMicroArea.equipeArea equipeArea ")
                .append("WHERE ")
                .append("equipeProfissional.profissional.codigo = :codigoProfissional ")
                .append("AND ")
                .append("cidade = equipeArea.cidade");
    }

    private StringBuilder emFichaNascidoVivo() {
        return selectTabelaFrom()
                .append("FichaNascidoVivo tabela ")
                .append("left join fetch tabela.usuarioCadsusNascido usuarioCadsus, ")
                .append("EquipeProfissional equipeProfissional ")
                .append("left join equipeProfissional.profissional profissional ")
                .append("left join equipeProfissional.equipeMicroArea equipeMicroArea ")
                .append("left join equipeMicroArea.equipeArea equipeArea ")
                .append("WHERE ")
                .append("equipeProfissional.profissional.codigo = :codigoProfissional ");
    }

    private StringBuilder emVacinaAplicacao() {
        return selectTabelaFrom()
                .append("VacinaAplicacao tabela ")
                .append("left join fetch tabela.usuarioCadsus usuarioCadsus ")
                .append("left join usuarioCadsus.enderecoDomicilio enderecoDomicilio ")
                .append("left join enderecoDomicilio.equipeMicroArea equipeMicroArea ")
                .append("left join equipeMicroArea.equipeProfissional equipeProfissional ")
                .append("left join equipeProfissional.profissional profissional ")
                .append("WHERE ")
                .append("profissional.codigo = :codigoProfissional");
    }
    //</editor-fold>

    //<editor-fold defaultstate="collapsed" desc="Filtros">
    private List filter(StringBuilder sb, TypeExpEnum typeExpEnum) {

        //<editor-fold defaultstate="collapsed" desc="Filtro por versionAll">
        filterVersionAll(sb, typeExpEnum);
        //<editor-fold defaultstate="collapsed" desc="Group by">
        sb.append(" group by tabela.codigo");
        if (sb.toString().contains("fetch tabela.usuarioCadsus")) {
            sb.append(", usuarioCadsus.codigo");
        }
        if (sb.toString().contains("fetch tabela.enderecoUsuarioCadsus")) {
            sb.append(", enderecoUsuarioCadsus.codigo");
        }
        if (sb.toString().contains("fetch tabela.enderecoDomicilio")) {
            sb.append(", enderecoDomicilio.codigo, tabela.enderecoDomicilio.enderecoUsuarioCadsus.codigo");
        }
        if (sb.toString().contains("agendaGradeAtendimentoHorario")) {
            sb.append(", agendaGradeAtendimentoHorario.codigo, usuarioCadsus.codigo");
        }
        //</editor-fold>
        sb.append(String.format(" order by tabela.%s asc ", typeExpEnum.getPropVersionAll()));
        //</editor-fold>
        //<editor-fold defaultstate="collapsed" desc="Filtro por profissional e paginação">
        try {
            Query query = MobileHelper.getSessionReadOnly().createQuery(sb.toString());
            query.setParameter("codigoProfissional", this.codigoProfissional);
            query.setFirstResult(this.first)
                    .setMaxResults(this.limit);
            first += limit;
            return query.list();
        } catch (ResourceException e) {
            Loggable.log.error(e);
            return null;
        }

        //</editor-fold>

    }
    //</editor-fold>

    //<editor-fold defaultstate="collapsed" desc="Filtro por versionAll">
    private StringBuilder filterVersionAll(StringBuilder sb, TypeExpEnum typeExpEnum) {
        if (this.versaoFinal != null) {
            sb.append(String.format(" AND tabela.%s <= ", typeExpEnum.getPropVersionAll()))
                    .append(this.versaoFinal);
        }

        sb.append(String.format(" AND tabela.%s > ", typeExpEnum.getPropVersionAll()))
                .append(this.versao);
        return sb;
    }
    //</editor-fold>
}