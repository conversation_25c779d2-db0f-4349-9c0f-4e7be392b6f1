package br.com.celk.helper.parse;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class ParseDate {
    public static Date parseDate(String s) throws ParseException {
        boolean invalidString = s == null || s.isEmpty() || "0".equalsIgnoreCase(s) || "null".equalsIgnoreCase(s);
        if(invalidString)
            return null;
        return new SimpleDateFormat("yyyy-MM-dd").parse(s);
    }
}
