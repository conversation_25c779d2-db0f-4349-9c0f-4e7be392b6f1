package br.com.celk.rnds.builder;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.List;

public class PacienteRnds {

    public static class Root {
        @JsonIgnore
        public String resourceType;
        @JsonIgnore
        public String id;
        @JsonIgnore
        public Object meta;
        @JsonIgnore
        public String type;
        @JsonIgnore
        public int total;
        @JsonIgnore
        public List<Object> link;

        public List<Entry> entry;

        public Root() {
        }

        public Root(List<Entry> entry) {
            this.entry = entry;
        }
    }

    public static class Coding {
        public String system;
        public String code;
        public String display;

        public Coding() {
        }
    }

    public static class ValueCodeableConcept {
        public List<Coding> coding;

        public ValueCodeableConcept() {
        }
    }

    public static class Identifier {
        public String use;
        public String system;
        public String value;

        @JsonIgnore
        public Object period;

        public Identifier() {
        }
    }

    public static class Name {
        public String use;
        public String text;

        public Name() {
        }
    }

    public static class Resource {
        @JsonIgnore
        public String resourceType;

        public String id;

        @JsonIgnore
        public Object meta;

        @JsonIgnore
        public List<Object> extension;

        public List<Identifier> identifier;
        public boolean active;
        public List<Name> name;

        @JsonIgnore
        public List<Object> telecom;
        @JsonIgnore
        public String gender;
        public String birthDate;
        @JsonIgnore
        public boolean deceasedBoolean;
        @JsonIgnore
        public List<Object> address;
        @JsonIgnore
        public boolean multipleBirthBoolean;

        public Resource() {
        }
    }

    public static class Entry {
        public String fullUrl;
        public Resource resource;

        public Entry() {
        }
    }
}