package br.com.celk.vigilancia.helper;

import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vacina.interfaces.dto.ModeloDocumentoBuilderDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.vigilancia.TemplateDocumentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;



/**
 *
 * <AUTHOR>
 */
public class ModeloDocumentoVigilanciaHelper {

    public static String builder(TemplateDocumentoVigilancia templateDocumentoVigilancia, ModeloDocumentoBuilderDTO documentoBuilderDTO) throws ValidacaoException, DAOException {
        if (templateDocumentoVigilancia == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_modelo_documento"));
        }

        String modeloCompilado = templateDocumentoVigilancia.getTexto();

        modeloCompilado = modeloCompilado.replaceAll("\\|@DescricaoContribuinte\\|", Coalesce.asString(documentoBuilderDTO.getDescricaoContribuinte()));
        modeloCompilado = modeloCompilado.replaceAll("\\|@EnderecoContribuinte\\|", Coalesce.asString(documentoBuilderDTO.getEnderecoContribuinte()));
        modeloCompilado = modeloCompilado.replaceAll("\\|@NumeroProcessoAdministrativo\\|", Coalesce.asString(documentoBuilderDTO.getNumeroProcessoAdministrativo()));
        modeloCompilado = modeloCompilado.replaceAll("\\|@NumeroAuto\\|", Coalesce.asString(documentoBuilderDTO.getNumeroAuto()));
        modeloCompilado = modeloCompilado.replaceAll("\\|@TextoManual\\|", Coalesce.asString(documentoBuilderDTO.getDescricaoManual()));
        modeloCompilado = modeloCompilado.replaceAll("\\|@TextoDefesaRecursoExterno\\|", Coalesce.asString(documentoBuilderDTO.getTextoDefesaRecursoExterno()));

        ConfiguracaoVigilancia cv = VigilanciaHelper.getConfiguracaoVigilancia();
        String localDataImpresao = null;
        if(cv != null) {
            Empresa empresa = getEmpresa(cv);
            StringBuilder stringBuilder = new StringBuilder(empresa.getCidade().getDescricaoCidadeUf());
            stringBuilder.append(", ");
            stringBuilder.append(DataUtil.getDataFormatadaMesString());
            localDataImpresao = stringBuilder.toString();
        }

        modeloCompilado = modeloCompilado.replaceAll("\\|@LocalDataImpressao\\|", Coalesce.asString(localDataImpresao));

        return modeloCompilado;
    }


    public static TemplateDocumentoVigilancia getTemplateDocumentoVigilancia(Long tipo) {
        return LoadManager.getInstance(TemplateDocumentoVigilancia.class)
                .addParameter(new QueryCustom.QueryCustomParameter(TemplateDocumentoVigilancia.PROP_TIPO_DOCUMENTO, tipo))
                .start().getVO();
    }

    private static Empresa getEmpresa(ConfiguracaoVigilancia cv) {
        return LoadManager.getInstance(Empresa.class)
                .addProperties(new HQLProperties(Empresa.class).getProperties())
                .addProperties(new HQLProperties(Cidade.class, Empresa.PROP_CIDADE).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Empresa.PROP_CODIGO), cv.getEmpresa().getCodigo()))
                .start().getVO();
    }
}
