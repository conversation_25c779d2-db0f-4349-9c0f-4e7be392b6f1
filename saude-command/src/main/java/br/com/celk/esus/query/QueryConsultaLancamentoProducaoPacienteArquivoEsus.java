package br.com.celk.esus.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.prontuario.basico.ItemContaEsusDTO;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaEsus;
import org.hibernate.Query;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryConsultaLancamentoProducaoPacienteArquivoEsus extends CommandQuery<QueryConsultaLancamentoProducaoPacienteArquivoEsus> {

    private int first;
    private int limit;
    private DatePeriod periodo;
    private List<ItemContaEsusDTO> result;
    private ItemContaEsus.TipoFicha tipoFicha;

    public QueryConsultaLancamentoProducaoPacienteArquivoEsus(int first, int limit, DatePeriod periodo, ItemContaEsus.TipoFicha tipoFicha) {
        this.first = first;
        this.limit = limit;
        this.periodo = periodo;
        this.tipoFicha = tipoFicha;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(ItemContaEsusDTO.class.getName());

        hql.addToSelect("contaPaciente.documento","documento");
        hql.addToSelect("contaPaciente.dataGeracao","dataAtendimento");

        hql.addToSelect("itemContaEsus.codigo","itemContaEsus.codigo");
        hql.addToSelect("itemContaEsus.turno","turno");
        hql.addToSelect("itemContaEsus.tipoAtendimento","tipoAtendimento");
        hql.addToSelect("itemContaEsus.tipoConsulta","tipoConsulta");
        hql.addToSelect("itemContaEsus.vigilanciaSaudeBucal","vigilanciaSaudeBucal");
        hql.addToSelect("itemContaEsus.tipoFicha","tipoFicha");

        hql.addToSelect("contaPaciente.codigo","contaPaciente.codigo");

        hql.addToSelect("profissional.codigo", "profissional.codigo");
        hql.addToSelect("profissional.nome", "profissional.nome");
        hql.addToSelect("profissional.cpf", "profissional.cpf");
        hql.addToSelect("profissional.codigoCns", "profissional.codigoCns");

        hql.addToSelect("cidadeProfissional.codigo", "profissional.cidade.codigo");
        hql.addToSelect("cidadeProfissional.descricao", "profissional.cidade.descricao");
        hql.addToSelect("cidadeProfissional.codigoEsus", "profissional.cidade.codigoEsus");

        hql.addToSelect("empresa.codigo", "empresa.codigo");
        hql.addToSelect("empresa.descricao", "empresa.descricao");
        hql.addToSelect("empresa.cnes", "empresa.cnes");
        hql.addToSelect("empresa.cnpj", "empresa.cnpj");
        hql.addToSelect("empresa.localAtendimento", "empresa.localAtendimento");

        hql.addToSelect("cidade.codigo", "empresa.cidade.codigo");
        hql.addToSelect("cidade.descricao", "empresa.cidade.descricao");
        hql.addToSelect("cidade.codigoEsus", "empresa.cidade.codigoEsus");

        hql.addToSelect("usuarioCadsus.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.dataNascimento", "usuarioCadsus.dataNascimento");
        hql.addToSelect("usuarioCadsus.sexo", "usuarioCadsus.sexo");

        hql.addToSelect("classificacaoAtendimento.codigo", "classificacaoAtendimento.codigo");
        hql.addToSelect("classificacaoAtendimento.codigoEsus", "classificacaoAtendimento.codigoEsus");

        hql.addToSelect("conduta.codigo", "conduta.codigo");
        hql.addToSelect("conduta.codigoEsus", "conduta.codigoEsus");
        hql.addToSelect("conduta.classificacaoEsus", "conduta.classificacaoEsus");

        hql.addToSelect("tabelaCbo.cbo", "tabelaCbo.cbo");
        hql.addToSelect("tabelaCbo.descricao", "tabelaCbo.descricao");
        hql.addToSelect("tabelaCbo.nivelEnsino", "tabelaCbo.nivelEnsino");

        hql.addToSelect("ciap.codigo", "ciap.codigo");
        hql.addToSelect("ciap.referencia", "ciap.referencia");

        hql.addToSelect("cid.codigo", "cidPrincipal.codigo");

        StringBuilder from = new StringBuilder(" ItemContaEsus itemContaEsus ");
        from.append(" left join itemContaEsus.contaPaciente contaPaciente ");
        from.append(" left join itemContaEsus.profissional profissional ");
        from.append(" left join itemContaEsus.empresaFaturamento empresa ");
        from.append(" left join empresa.cidade cidade ");
        from.append(" left join profissional.cidade cidadeProfissional ");
        from.append(" join contaPaciente.usuarioCadsus usuarioCadsus ");
        from.append(" left join itemContaEsus.classificacaoAtendimento classificacaoAtendimento ");
        from.append(" left join itemContaEsus.conduta conduta ");
        from.append(" left join itemContaEsus.tabelaCbo tabelaCbo ");
        from.append(" left join itemContaEsus.ciap ciap ");
        from.append(" left join itemContaEsus.cid cid");
        hql.addToFrom(from.toString());

        if (ItemContaEsus.TipoFicha.PROCEDIMENTOS.equals(this.tipoFicha)) {
            hql.addToWhereWhithAnd("itemContaEsus.tipoFicha in (0,3)"); //Individual e Procedimento
        } else {
            hql.addToWhereWhithAnd("itemContaEsus.tipoFicha = ", this.tipoFicha.value());
        }

        hql.addToWhereWhithAnd("contaPaciente.status = ", ContaPaciente.Status.FECHADA.value());

        hql.addToWhereWhithAnd("contaPaciente.flagFechadaAutomatico = ", RepositoryComponentDefault.NAO_LONG);

        if (periodo != null) {
            if (periodo.getDataInicial() != null) {
                hql.addToWhereWhithAnd("contaPaciente.dataGeracao >= ", periodo.getDataInicial());
            }
            if (periodo.getDataFinal() != null) {
                hql.addToWhereWhithAnd("contaPaciente.dataGeracao <= ", periodo.getDataFinal());
            }
        }

        hql.addToOrder("empresa.codigo asc");
        hql.addToOrder("profissional.codigo asc");
        hql.addToOrder("contaPaciente.dataGeracao asc");
    }

    @Override
    public List<ItemContaEsusDTO> getResult() {
        return this.result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    protected void customQuery(Query query) {
        query.setFirstResult(first);
        query.setMaxResults(limit);
    }

}
