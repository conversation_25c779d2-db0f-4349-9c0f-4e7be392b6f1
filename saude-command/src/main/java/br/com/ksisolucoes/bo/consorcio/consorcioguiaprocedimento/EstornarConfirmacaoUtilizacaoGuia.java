package br.com.ksisolucoes.bo.consorcio.consorcioguiaprocedimento;

import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.ConsorcioGuiaProcedimentoItemDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.ValorReservadoDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento;
import br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimentoItem;
import br.com.ksisolucoes.vo.consorcio.Conta;
import br.com.ksisolucoes.vo.consorcio.SubConta;
import org.hibernate.criterion.Restrictions;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class EstornarConfirmacaoUtilizacaoGuia extends AbstractCommandTransaction {

    private ConsorcioGuiaProcedimento consorcioGuiaProcedimento;
    private List<ConsorcioGuiaProcedimentoItemDTO> itens;

    public EstornarConfirmacaoUtilizacaoGuia(ConsorcioGuiaProcedimento consorcioGuiaProcedimento) {
        this.consorcioGuiaProcedimento = consorcioGuiaProcedimento;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        SubConta subConta = HibernateUtil.lockTable(SubConta.class, consorcioGuiaProcedimento.getSubConta().getCodigo());
        
        Conta conta = HibernateUtil.lockTable(Conta.class, subConta.getConta().getCodigo());
        
        consorcioGuiaProcedimento.setStatus(ConsorcioGuiaProcedimento.StatusGuiaProcedimento.ABERTA.value());
        consorcioGuiaProcedimento.setUsuarioAplicacao(null);
        consorcioGuiaProcedimento.setDataAplicacao(null);

        // ESTORNA AS RESERVAS DO PAGAMENTO
        List<ConsorcioGuiaProcedimentoItem> itensExistentesConfirmados = getSession().createCriteria(ConsorcioGuiaProcedimentoItem.class)
                .add(Restrictions.eq(ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, consorcioGuiaProcedimento))
                .add(Restrictions.ne(ConsorcioGuiaProcedimentoItem.PROP_STATUS, ConsorcioGuiaProcedimentoItem.StatusGuiaProcedimentoItem.CANCELADA.value()))
                .list();

        estornarReservasItensConfirmados(itensExistentesConfirmados);

        // GERA NOVAMENTE AS RESERVAS COM BASE NOS ITENS CADASTRADOS.
        List<ConsorcioGuiaProcedimentoItem> itensExistentesGuia = getSession().createCriteria(ConsorcioGuiaProcedimentoItem.class)
                .add(Restrictions.eq(ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, consorcioGuiaProcedimento))
                .list();

        gerarReservaItensGuiaCadastrados(itensExistentesGuia);

        BOFactory.save(consorcioGuiaProcedimento);
    }

    private void gerarReservaItensGuiaCadastrados(List<ConsorcioGuiaProcedimentoItem> itensExistentesGuia) throws DAOException, ValidacaoException {
        for (ConsorcioGuiaProcedimentoItem consorcioGuiaProcedimentoItem : itensExistentesGuia) {
            // todas os itens da guia saem com Status ABERTO
            consorcioGuiaProcedimentoItem.setStatus(ConsorcioGuiaProcedimentoItem.StatusGuiaProcedimentoItem.ABERTA.value());
            Double valorReservar = new Dinheiro(consorcioGuiaProcedimentoItem.getValorProcedimento())
                    .multiplicar(consorcioGuiaProcedimentoItem.getQuantidade()).doubleValue();

            consorcioGuiaProcedimentoItem.setQuantidadeAplicacao(0L);
            BOFactory.save(consorcioGuiaProcedimentoItem);
            if (Coalesce.asDouble(consorcioGuiaProcedimentoItem.getValorProcedimentoImposto()) > 0D) {
                valorReservar = new Dinheiro(valorReservar).somar(consorcioGuiaProcedimentoItem.getValorProcedimentoImposto()).doubleValue();
            }
            BOFactory.getBO(ConsorcioFacade.class).gerarValorReservado(new ValorReservadoDTO(consorcioGuiaProcedimento.getSubConta(), consorcioGuiaProcedimento.getAnoCadastro(), valorReservar));
        }
    }

    private void estornarReservasItensConfirmados(List<ConsorcioGuiaProcedimentoItem> itensExistentesConfirmados) throws DAOException, ValidacaoException {
        for (ConsorcioGuiaProcedimentoItem consorcioGuiaProcedimentoItem : itensExistentesConfirmados) {
            if (Coalesce.asLong(consorcioGuiaProcedimentoItem.getQuantidadeAplicacao()).equals(0L)) {
                consorcioGuiaProcedimentoItem.setQuantidadeAplicacao(null);
            }
            Double valorRemover = new Dinheiro(consorcioGuiaProcedimentoItem.getValorProcedimento())
                    .multiplicar(Coalesce.asLong(consorcioGuiaProcedimentoItem.getQuantidadeAplicacao(), consorcioGuiaProcedimentoItem.getQuantidade())).doubleValue();

            consorcioGuiaProcedimentoItem.setQuantidadeAplicacao(0L);
            BOFactory.save(consorcioGuiaProcedimentoItem);

            if (Coalesce.asDouble(consorcioGuiaProcedimentoItem.getValorProcedimentoImposto()) > 0D) {
                valorRemover = new Dinheiro(valorRemover).somar(consorcioGuiaProcedimentoItem.getValorProcedimentoImposto()).doubleValue();
            }
            BOFactory.getBO(ConsorcioFacade.class).removerValorReservado(new ValorReservadoDTO(consorcioGuiaProcedimento.getSubConta(), consorcioGuiaProcedimento.getAnoCadastro(), valorRemover));
        }
    }
}
