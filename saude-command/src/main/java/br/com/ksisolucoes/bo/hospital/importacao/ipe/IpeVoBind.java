package br.com.ksisolucoes.bo.hospital.importacao.ipe;

import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import org.hibernate.Criteria;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public interface IpeVoBind<T> {

    public void setRestrictions(Criteria criteria);

    public Class getVoClass();

    public void setAttributesVO(T vo, Procedimento procedimento, Session session);

    public String getCodigo();

    public String getDescricao();
}
