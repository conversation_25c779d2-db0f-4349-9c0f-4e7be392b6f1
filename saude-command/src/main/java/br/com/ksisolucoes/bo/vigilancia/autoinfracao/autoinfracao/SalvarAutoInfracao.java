package br.com.ksisolucoes.bo.vigilancia.autoinfracao.autoinfracao;

import br.com.celk.util.Coalesce;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.AutosHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.GerarChaveProcessoAdministrativoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.SalvarAutoInfracaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracaoEmail;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracaoProvidencia;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.FiscalAutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoAutenticacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import ch.lambdaj.Lambda;

/**
 * <AUTHOR>
 */
public class SalvarAutoInfracao extends AbstractCommandTransaction {

    private AutoInfracao autoInfracao;
    private SalvarAutoInfracaoDTO dto;
    private ConfiguracaoVigilancia configuracaoVigilancia;

    public SalvarAutoInfracao(SalvarAutoInfracaoDTO salvarAutoInfracaoDTO) {
        this.autoInfracao = salvarAutoInfracaoDTO.getAutoInfracao();
        this.dto = salvarAutoInfracaoDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        boolean cadastro = false;
        if (autoInfracao.getCodigo() == null) {
            cadastro = true;
        }

        carregarConfiguracaoVigilancia();
        if (configuracaoVigilancia == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existe_configuracao"));
        }
        configurarNumeracao();

        autoInfracao = BOFactory.save(autoInfracao);


        if (CollectionUtils.isNotNullEmpty(dto.getLstFiscal())) {
            Lambda.forEach(dto.getLstFiscal()).setAutoInfracao(autoInfracao);
            VOUtils.persistirListaVosModificados(FiscalAutoInfracao.class, dto.getLstFiscal(), new QueryCustom.QueryCustomParameter(FiscalAutoInfracao.PROP_AUTO_INFRACAO, autoInfracao));
        }

        if (CollectionUtils.isNotNullEmpty(dto.getLstProvidencias())) {
            Lambda.forEach(dto.getLstProvidencias()).setAutoInfracao(autoInfracao);
            VOUtils.persistirListaVosModificados(AutoInfracaoProvidencia.class, dto.getLstProvidencias(), new QueryCustom.QueryCustomParameter(AutoInfracaoProvidencia.PROP_AUTO_INFRACAO, autoInfracao));
        }

        if (CollectionUtils.isNotNullEmpty(dto.getListAutoInfracaoEmail())) {
            Lambda.forEach(dto.getListAutoInfracaoEmail()).setAutoInfracao(autoInfracao);
            VOUtils.persistirListaVosModificados(AutoInfracaoEmail.class, dto.getListAutoInfracaoEmail(), new QueryCustom.QueryCustomParameter(AutoInfracaoEmail.PROP_AUTO_INFRACAO, autoInfracao));
        }

        // anexos do auto de infração
        if (dto.getPnlRequerimentoVigilanciaAnexoDTO() != null) {
            BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaAnexo(autoInfracao, dto.getPnlRequerimentoVigilanciaAnexoDTO().getRequerimentoVigilanciaAnexoDTOList(), dto.getPnlRequerimentoVigilanciaAnexoDTO().getRequerimentoVigilanciaAnexoExcluidoDTOList());
        }

        if (CollectionUtils.isNotNullEmpty(dto.getLstRequerimentoVigilanciaAnexo())) {
            Lambda.forEach(dto.getLstRequerimentoVigilanciaAnexo()).setAutoInfracao(autoInfracao);
            VOUtils.persistirListaVosModificados(RequerimentoVigilanciaAnexo.class, dto.getLstRequerimentoVigilanciaAnexo(), new QueryCustom.QueryCustomParameter(RequerimentoVigilanciaAnexo.PROP_AUTO_INFRACAO, autoInfracao));
        }


        if (cadastro) {
            if (autoInfracao.getRequerimentoVigilancia() != null && autoInfracao.getRequerimentoVigilancia().getCodigo() != null) {
                gerarOcorrenciaRequerimentoVigilancia(autoInfracao, autoInfracao.getRequerimentoVigilancia());
                enviarEmailResponsavelRequerimentoVigilancia(autoInfracao, autoInfracao.getRequerimentoVigilancia());
            }
            BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoAutoInfracao(autoInfracao.getDataInfracao(), autoInfracao);
            if (autoInfracao.getProcessoAdministrativoAutenticacao() == null) {
                gerarChaveProcesso();
                BOFactory.save(autoInfracao);
            }
        }
    }

    private void configurarNumeracao() throws ValidacaoException, DAOException {
        if (autoInfracao.getNumero() == null && autoInfracao.getCodigo() == null) {
            if (configuracaoVigilancia.getAnoBaseGeral() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_ano_base_configuracao_vigilancia"));
            }
            if (configuracaoVigilancia.getNumAutoInfracao() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_numeracao_base_auto_infracao"));
            }

            long sequencial = 0L;
            if (configuracaoVigilancia != null && Coalesce.asLong(configuracaoVigilancia.getNumAutoInfracao()) > 0L) {
                sequencial = configuracaoVigilancia.getNumAutoInfracao();
            }
            sequencial++;
            String montaId = String.valueOf(sequencial).concat(String.valueOf(configuracaoVigilancia.getAnoBaseGeral()));
            Long nextId = Long.valueOf(StringUtil.getDigits(montaId));
            autoInfracao.setNumero(nextId);

            configuracaoVigilancia.setNumAutoInfracao(sequencial);

            BOFactory.save(configuracaoVigilancia);
        }
    }

    private void carregarConfiguracaoVigilancia() throws DAOException, ValidacaoException {
        configuracaoVigilancia = BOFactory.getBO(VigilanciaFacade.class).carregarConfiguracaoVigilancia();
    }

    public AutoInfracao getAutoInfracao() {
        return autoInfracao;
    }

    private void gerarChaveProcesso() throws ValidacaoException, DAOException {
        if (this.autoInfracao.getAutoIntimacao() != null) {
            AutoIntimacao autoIntimacao = (AutoIntimacao) this.getSession().get(AutoIntimacao.class, this.autoInfracao.getAutoIntimacao().getCodigo());
            if (autoIntimacao.getProcessoAdministrativoAutenticacao() != null) {
                this.autoInfracao.setProcessoAdministrativoAutenticacao(autoIntimacao.getProcessoAdministrativoAutenticacao());
            } else {
                throw new ValidacaoException(Bundle.getStringApplication("msg_ocorreu_erro_gerar_chave_acesso_auto"));
            }
        } else {
            GerarChaveProcessoAdministrativoDTO dto = new GerarChaveProcessoAdministrativoDTO();
            dto.setCodigoAuto(autoInfracao.getCodigo());
            dto.setCpf(AutosHelper.getCpfAuto(autoInfracao));
            dto.setNome(AutosHelper.getNomeResponsavel(autoInfracao));
            dto.setEmail(AutosHelper.getEmailAutuado(autoInfracao));
            dto.setTelefone(AutosHelper.getTelefoneAutuado(autoInfracao));
            ProcessoAdministrativoAutenticacao processoAdministrativoAutenticacao = BOFactory.getBO(VigilanciaFacade.class).gerarChaveProcessoAdministrativo(dto);
            if (processoAdministrativoAutenticacao == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_ocorreu_erro_gerar_chave_acesso_auto"));
            }
            this.autoInfracao.setProcessoAdministrativoAutenticacao(processoAdministrativoAutenticacao);
        }
    }

    private void gerarOcorrenciaRequerimentoVigilancia(AutoInfracao autoInfracao, RequerimentoVigilancia requerimentoVigilancia) throws ValidacaoException, DAOException {
        String mensagemOcorrencia = "Registro do Auto de Infração Nº " + autoInfracao.getNumeroFormatado();
        BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(mensagemOcorrencia, requerimentoVigilancia, null);
    }

    private void enviarEmailResponsavelRequerimentoVigilancia(AutoInfracao autoInfracao, RequerimentoVigilancia requerimentoVigilancia) throws ValidacaoException, DAOException {
        String assunto = "Registro do Auto de Infração Nº " + autoInfracao.getNumeroFormatado();
        StringBuilder sb = new StringBuilder();
        sb.append("<html>");
        sb.append("<body>");
        sb.append("<p>Informativo do registro de Auto de Infração na Vigilância Sanitária.</p>");
        sb.append("<br/>");
        sb.append("<strong>Protocolo: </strong>").append(requerimentoVigilancia.getProtocoloFormatado());
        sb.append("<br/>");
        sb.append("<strong>Requerimento: </strong>").append(requerimentoVigilancia.getDescricaoTipoDocumento());
        sb.append("<br/>");
        sb.append("<strong>Situação do Requerimento: </strong>").append(requerimentoVigilancia.getDescricaoSituacao());
        sb.append("<br/>");
        sb.append("<strong>Ambiente de recursos disponível em: </strong>");
        String realContext = TenantContext.getRealContext();
        if (realContext.equals("localhost")) {
            realContext = realContext.concat(":8080");
        }
        sb.append(realContext);
        sb.append(("/vigilancia"));
        sb.append("</body>");
        sb.append("</html>");
        BOFactory.getBO(VigilanciaFacade.class).enviarEmailRequerimentoVigilancia(requerimentoVigilancia, sb.toString(), assunto);
    }
}
