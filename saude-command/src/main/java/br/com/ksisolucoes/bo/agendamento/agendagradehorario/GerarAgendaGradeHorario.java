package br.com.ksisolucoes.bo.agendamento.agendagradehorario;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeHorario;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class GerarAgendaGradeHorario extends AbstractCommandTransaction {

    private AgendaGradeAtendimento agendaGradeAtendimento;

    public GerarAgendaGradeHorario(AgendaGradeAtendimento agendaGradeAtendimento) {
        this.agendaGradeAtendimento = agendaGradeAtendimento;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Date hora = agendaGradeAtendimento.getAgendaGrade().getHoraInicial();
        Long tempoMedio = agendaGradeAtendimento.getTempoMedio();
        
        for(int i = 0; i < agendaGradeAtendimento.getQuantidadeAtendimento(); i++){
            AgendaGradeHorario agendaGradeHorario = new AgendaGradeHorario();
            
            if(i != 0){
                Data.addMinutos(hora, tempoMedio.intValue());
            }
            agendaGradeHorario.setAgendaGradeAtendimento(agendaGradeAtendimento);
            agendaGradeHorario.setHora(hora);
            
            BOFactory.save(agendaGradeHorario);
        }
    }
}