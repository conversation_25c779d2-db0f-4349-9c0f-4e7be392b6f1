package br.com.ksisolucoes.bo.agendamento.exame;

import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTOParam;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoGroupDTO;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.hibernate.Query;

import java.util.*;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class QueryConsultarHorariosAgendaExameGroupBy extends CommandQuery<QueryConsultarHorariosAgendaExameGroupBy> {

    private final AgendaGradeAtendimentoDTOParam param;
    private List<AgendaGradeAtendimentoGroupDTO> listaDiasDisponiveis;
    private final Long codigoEmpresaOrigem;
    private final AgendaGradeAtendimentoDTOParam.GroupBy groupBy;

    public QueryConsultarHorariosAgendaExameGroupBy(AgendaGradeAtendimentoDTOParam param, AgendaGradeAtendimentoDTOParam.GroupBy groupBy) {
        this.param = param;
        this.groupBy = groupBy;
        if (this.param.getEmpresaOrigem() != null) {
            codigoEmpresaOrigem = this.param.getEmpresaOrigem().getCodigo();
        } else {
            codigoEmpresaOrigem = getSessao().getCodigoEmpresa();
        }
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        AgendaGradeAtendimentoGroupDTO proxy = on(AgendaGradeAtendimentoGroupDTO.class);
        if (AgendaGradeAtendimentoDTOParam.GroupBy.EMPRESA.equals(groupBy)) {
            hql.addToSelectAndGroup("emp.codigo", path(proxy.getEmpresa().getCodigo()));
            hql.addToSelectAndGroup("emp.referencia", path(proxy.getEmpresa().getReferencia()));
            hql.addToSelectAndGroup("emp.descricao", path(proxy.getEmpresa().getDescricao()));
            hql.addToSelectAndGroup("emp.tipoUnidade", path(proxy.getEmpresa().getTipoUnidade()));
        }
        hql.addToSelectAndGroup("tp.codigo", path(proxy.getTipoProcedimento().getCodigo()));
        hql.addToSelectAndGroup("tp.descricao", path(proxy.getTipoProcedimento().getDescricao()));

        hql.setTypeSelect(AgendaGradeAtendimentoGroupDTO.class.getName());
        hql.addToFrom("AgendaGradeAtendimento aga"
                + " join aga.agendaGrade ag"
                + " join aga.tipoAtendimentoAgenda taa"
                + " join ag.agenda a"
                + " join a.tipoProcedimento tp"
                + " left join a.profissional prof"
                + " join a.empresa emp");

        hql.addToWhereWhithAnd("a.status =", Agenda.STATUS_CONFIRMADO);

        if (param.getUsuarioCadsus() != null) {
            hql.addToWhereWhithAnd("(a.idadeInicio is null or " + param.getUsuarioCadsus().getIdade() + " >= a.idadeInicio)");
            hql.addToWhereWhithAnd("(a.idadeFim is null or " + param.getUsuarioCadsus().getIdade() + " <= a.idadeFim)");
            hql.addToWhereWhithAnd("(coalesce(a.sexo, 'N') = 'N' or '" + param.getUsuarioCadsus().getSexo() + "' = a.sexo)");
        }

        Calendar c = GregorianCalendar.getInstance();
        int diferencaSemana = 7 - c.get(Calendar.DAY_OF_WEEK);
        hql.addToWhereWhithAnd("cast(ag.data as date) <= current_date + ((a.visibilidadeAgenda * 7) + " + diferencaSemana + " )");

        if (CollectionUtils.isNotNullEmpty(param.getProfissionalList())) {
            hql.addToWhereWhithAnd("prof in :profissionalList");
        } else if (param.getProfissional() != null) {
            hql.addToWhereWhithAnd("prof =", param.getProfissional());
        }

        hql.addToWhereWhithAnd("EXTRACT(DOW FROM ag.data) in ", param.getDiaDaSemanaList());
        hql.addToWhereWhithAnd("emp ", param.getEstabelecimento());
        hql.addToWhereWhithAnd("aga =", param.getAgendaGradeAtendimento());
        hql.addToWhereWhithAnd("aga <>", param.getNotAgendaGradeAtendimento());
        hql.addToWhereWhithAnd("tp =", param.getTipoProcedimento());
        hql.addToWhereWhithAnd("coalesce(a.tipoAgenda,'" + Agenda.TIPO_AGENDA_COMPARTILHADA + "') = ", param.getTipoAgenda());
        hql.addToWhereWhithAnd("emp =", param.getEmpresa());
        hql.addToWhereWhithAnd("taa.tipoAtendimento in ", param.getTipoAtendimentoAgendaList());
        hql.addToWhereWhithAnd("coalesce(tp.agendaAutomatico,'" + RepositoryComponentDefault.SIM + "') = ", param.getAgendamentoAutomatico());

        if (param.isVisualizaAtendimentoAtencaoBasica()) {
            hql.addToWhereWhithAnd("tp.flagVisualizaAtencaoBasica =", RepositoryComponentDefault.SIM_LONG);
        }

        if (param.isAguardaProcedimento()) {
            hql.addToWhereWhithAnd("tp.flagAguardaProcedimento =", RepositoryComponentDefault.SIM_LONG);
        }

        if (CollectionUtils.isNotNullEmpty(param.getTipoAgendaList())) {
            hql.addToWhereWhithAnd("exists(select 1 from TipoProcedimentoAgenda tpa where tpa.empresa.codigo = emp.codigo and tpa.tipoProcedimento.codigo = tp.codigo and tpa.tipoAgenda in (:tipoAgendaList) )");
        }

        if (param.getDataAgendasRemanejamento() != null || param.getDatePeriod() != null && param.getDatePeriod().getDataInicial() != null && Data.adjustRangeHour(param.getDatePeriod()).getDataInicial().after(Data.adjustRangeHour(Data.getDataAtual()).getDataInicial())) {
            if (param.isAdjustRangeHour()) {
                hql.addToWhereWhithAnd("ag.data >=", Data.adjustRangeHour(param.getDatePeriod()).getDataInicial());
            } else {
                if (param.getDataAgendasRemanejamento() != null) {
                    hql.addToWhereWhithAnd("to_timestamp(to_char(ag.data, 'dd Mon YYYY '), 'dd Mon YYYY') >=", Data.adjustRangeHour(param.getDataAgendasRemanejamento()).getDataInicial());
                } else {
                    hql.addToWhereWhithAnd("to_timestamp(to_char(ag.data, 'dd Mon YYYY ')||to_char(ag.horaInicial, ' HH24 MI '), 'dd Mon YYYY HH24 MI') >", param.getDatePeriod().getDataInicial());
                }
            }
        } else if (!param.isApenasUmDiaAgenda()) {
            hql.addToWhereWhithAnd("ag.data >=", Data.adjustRangeHour(Data.getDataAtual()).getDataInicial());
        }

        if (!param.isApenasUmDiaAgenda() && param.getDatePeriod() != null && param.getDatePeriod().getDataFinal() != null) {
            if (param.isAdjustRangeHour()) {
                hql.addToWhereWhithAnd("ag.data <=", Data.adjustRangeHour(param.getDatePeriod()).getDataFinal());
            } else {
                hql.addToWhereWhithAnd("ag.data <", param.getDatePeriod().getDataFinal());
            }
        }

        if (param.isApenasUmDiaAgenda() && param.getDatePeriod() != null && param.getDatePeriod().getDataInicial() != null && param.getDatePeriod().getDataFinal() != null) {
            hql.addToWhereWhithAnd("ag.data >= :dataInicial and ag.data <= :dataFinal");
        }

        if (param.isSemAgendamento()) {
            hql.addToWhereWhithAnd("not exists (select 1 from AgendaGradeAtendimentoHorario agah "
                    + " where agah.agendaGradeAtendimento = aga"
                    + " and agah.status <> " + AgendaGradeAtendimentoHorario.STATUS_CANCELADO + ")");
        }

        if (param.getCodigoAgenda() != null) {
            hql.addToWhereWhithAnd("a.codigo = ", param.getCodigoAgenda());
        }

        // Se permite encaixar pacientes então não deve fazer o filtro para buscar somente agendas com vagas.
        if (!param.isPermiteEncaixarPaciente()) {
            hql.addToWhereWhithAnd("aga.quantidadeAtendimento > coalesce((select sum(quantidadeVagasOcupadas) from AgendaGradeAtendimentoHorario agah "
                    + " where agah.agendaGradeAtendimento = aga"
                    + " and agah.status not in (3,5)),0)");
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.listaDiasDisponiveis = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(param.getProfissionalList())) {
            query.setParameter("profissionalList", param.getProfissionalList());
        }
        if (param.isApenasUmDiaAgenda() && param.getDatePeriod() != null && param.getDatePeriod().getDataInicial() != null && param.getDatePeriod().getDataFinal() != null) {
            query.setParameter("dataInicial", param.getDatePeriod().getDataInicial());
            query.setParameter("dataFinal", param.getDatePeriod().getDataFinal());
        }
        if (CollectionUtils.isNotNullEmpty(param.getTipoAgendaList())) {
            ArrayList<Long> tipoAgendaList = new ArrayList();
            for (TipoProcedimento.TipoAgenda tipoAgenda : param.getTipoAgendaList()) {
                tipoAgendaList.add(tipoAgenda.value());
            }
            query.setParameterList("tipoAgendaList", tipoAgendaList);
        }
    }

    public List<AgendaGradeAtendimentoGroupDTO> getListaDiasDisponiveis() {
        return listaDiasDisponiveis;
    }
}