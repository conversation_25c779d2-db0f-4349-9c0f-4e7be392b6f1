package br.com.ksisolucoes.bo.entradas.estoque.inventario;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.AbstractSessaoAplicacao;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoContext;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.Inventario;

/**
 * <AUTHOR>
 */
public class SaveInventario extends SaveVO<Inventario> {


    public SaveInventario(Inventario vo) {
        super(vo);
    }

    protected void antesSave() throws ValidacaoException, DAOException {
        AbstractSessaoAplicacao sessaoAplicacao = (AbstractSessaoAplicacao) SessaoAplicacaoContext.getContext();

        if (this.vo.getSituacao() == null) {
            this.vo.setSituacao(Inventario.Situacao.ABERTO.value());
        }

        if (this.vo.getUsuarioCadastro() == null) {
            this.vo.setUsuarioCadastro(sessaoAplicacao.getUsuario());
        }

        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(DataUtil.getDataAtual());
        }

        //Quando reabrir o inventario pela tela de consulta
        if (this.vo.getCodigo() != null && Inventario.Situacao.ABERTO.value().equals(this.vo.getSituacao())) {
            this.vo.setDataFechamento(null);
            this.vo.setUsuarioFechamento(null);
            this.vo.setMotivoFechamento(null);
        }

        //Não há necessidade de comparacao
        this.vo.setUsuarioAlteracao(sessaoAplicacao.getUsuario());
        this.vo.setDataAlteracao(DataUtil.getDataAtual());

    }
}