package br.com.ksisolucoes.bo.hospital.exportacao.producaoaih;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.io.FileUtils;
import br.com.ksisolucoes.io.sftp.SftpManager;
import br.com.ksisolucoes.report.io.sftp.SftpBuilderSaude;
import br.com.ksisolucoes.report.io.sftp.SftpSaudePropertiesDefault;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.AihProcesso;
import com.jcraft.jsch.ChannelSftp;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.Charset;
import java.util.Date;
import java.util.Vector;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class DownloadProducaoAih extends AbstractCommandTransaction {

    AihProcesso aihProcesso;
    private String filePath;

    public DownloadProducaoAih(AihProcesso aihProcesso) {
        this.aihProcesso = aihProcesso;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        aihProcesso = (AihProcesso) getSession().get(AihProcesso.class, aihProcesso.getCodigo());

        try {
            criaArquivo(aihProcesso.getTexto(), aihProcesso.getCompetencia());

        } catch (IOException ex) {
            Logger.getLogger(DownloadProducaoAih.class.getName()).log(Level.SEVERE, null, ex);
        }

        aihProcesso.setCaminhoArquivo("aih/producaoAih_" + aihProcesso.getCompetencia().toString() + ".txt");
        BOFactory.save(aihProcesso);

    }

    private void criaArquivo(String texto, Date competencia) throws IOException, ValidacaoException {

        File file = File.createTempFile("producaoAih_" + competencia.toString(), "txt");
        filePath = file.getAbsolutePath();
        BufferedWriter bufferedWriter = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file), "ISO-8859-1"));
        bufferedWriter.write(texto);
        bufferedWriter.close();

        salvaFtp(competencia);
    }

    private void salvaFtp(Date competencia) throws ValidacaoException {

        String caminho = "aih";

        FileUtils.enviarArquivoFtp(filePath, caminho + "/producaoAih_" + competencia.toString() + ".txt");
    }

    public AihProcesso getAihProcesso() {
        return aihProcesso;
    }
}
