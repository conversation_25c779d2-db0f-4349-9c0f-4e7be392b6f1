/*
 * AtualizarEstoqueMinimo.java
 *
 * Criada em 26 de Junho de 2006, 11:24
 */

package br.com.ksisolucoes.bo.entradas.estoque.estoqueempresa;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresaPK;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.util.List;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class AtualizarEstoqueMinimo extends AbstractCommandTransaction {
    
    private List<EstoqueEmpresa> estoqueEmpresaList;
    private Double estoqueMinimo;
    private Produto produto;
    private Long codigoEmpresa;
    
    public AtualizarEstoqueMinimo(List<EstoqueEmpresa> estoqueEmpresaList) {
        this.estoqueEmpresaList = estoqueEmpresaList;
    }

    public AtualizarEstoqueMinimo(Double estoqueMinimo, Produto produto, Long codigoEmpresa) {
        this.estoqueMinimo = estoqueMinimo;
        this.produto = produto;
        this.codigoEmpresa = codigoEmpresa;
        estoqueEmpresaList = null;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        if(estoqueEmpresaList != null){
            atualizarEstoqueMinimo();
        }else{
            atualizarEstoqueMinimoSomente();
        }
    }
    
    private void atualizarEstoqueMinimo() throws DAOException, ValidacaoException {
        for(EstoqueEmpresa estoqueEmpresa: estoqueEmpresaList){
            
            EstoqueEmpresa loadEstoqueEmpresa = (EstoqueEmpresa) getSession().get(EstoqueEmpresa.class, estoqueEmpresa.getId());

            loadEstoqueEmpresa.setEstoqueMinimo(estoqueEmpresa.getEstoqueMinimo());
            loadEstoqueEmpresa.setEstoqueReposicao(estoqueEmpresa.getEstoqueReposicao());
            loadEstoqueEmpresa.setQuantidadeIdeal(estoqueEmpresa.getQuantidadeIdeal());
            loadEstoqueEmpresa.setQuantidadeMultipla(estoqueEmpresa.getQuantidadeMultipla());

//            List<String> props = Arrays.asList( EstoqueEmpresa.PROP_ESTOQUE_MINIMO, EstoqueEmpresa.PROP_ESTOQUE_REPOSICAO, EstoqueEmpresa.PROP_QUANTIDADE_IDEAL, EstoqueEmpresa.PROP_QUANTIDADE_MULTIPLA );
//            List values = Arrays.asList( estoqueEmpresa.getEstoqueMinimo(), estoqueEmpresa.getEstoqueReposicao(), estoqueEmpresa.getQuantidadeIdeal(), estoqueEmpresa.getQuantidadeMultipla() );

//            new UpdateFragmentEstoqueEmpresa( props, values, estoqueEmpresa.getId().getProduto(),
//                    estoqueEmpresa.getId().getEmpresa().getCodigo() ).start();
        }
    }

    private void atualizarEstoqueMinimoSomente() throws DAOException, ValidacaoException {
        EstoqueEmpresa loadEstoqueEmpresa = (EstoqueEmpresa) getSession().createCriteria(EstoqueEmpresa.class)
                .add(Restrictions.eq(VOUtils.montarPath(EstoqueEmpresa.PROP_ID,EstoqueEmpresaPK.PROP_PRODUTO, Produto.PROP_CODIGO), produto.getCodigo()))
                .add(Restrictions.eq(VOUtils.montarPath(EstoqueEmpresa.PROP_ID,EstoqueEmpresaPK.PROP_EMPRESA, Empresa.PROP_CODIGO), codigoEmpresa))
                .uniqueResult();

        loadEstoqueEmpresa.setEstoqueMinimo(this.estoqueMinimo);
    }
}