package br.com.ksisolucoes.bo.prontuario.basico.atendimentocidnotificavel;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtendimentoCidNotificavelDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryAtendimentoCidNotificavel extends CommandQuery<QueryAtendimentoCidNotificavel> {

    private Long codigoAtendimento;
    private Long codigoPaciente;
    private List<AtendimentoCidNotificavelDTO> result;

    public QueryAtendimentoCidNotificavel(Long codigoAtendimento, Long codigoPaciente) {
        this.codigoAtendimento = codigoAtendimento;
        this.codigoPaciente = codigoPaciente;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.addToSelect("atendimento.codigo", "atendimento.codigo");
        hql.addToSelect("atendimento.dataAtendimento", "atendimento.dataAtendimento");

        hql.addToSelect("atendimentoCidNotificavel.codigo", "atendimentoCidNotificavel.codigo");
        hql.addToSelect("atendimentoCidNotificavel.flagPreencheuNotificacao", "atendimentoCidNotificavel.flagPreencheuNotificacao");

        hql.addToSelect("cid.codigo", "cid.codigo");
        hql.addToSelect("cid.descricao", "cid.descricao");

        hql.setTypeSelect(AtendimentoCidNotificavelDTO.class.getName());

        StringBuilder from = new StringBuilder("AtendimentoCidNotificavel atendimentoCidNotificavel");
        from.append(" left join atendimentoCidNotificavel.atendimento atendimento");
        from.append(" left join atendimentoCidNotificavel.cid cid");
        hql.addToFrom(from.toString());

        hql.addToWhereWhithAnd("atendimento.usuarioCadsus.codigo = ", codigoPaciente);
        hql.addToWhereWhithAnd("atendimento.status <>", Atendimento.STATUS_CANCELADO);

        hql.addToOrder("1 desc");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    public List<AtendimentoCidNotificavelDTO> getResult() {
         return result;
    }

}
