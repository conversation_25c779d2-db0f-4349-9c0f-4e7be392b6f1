package br.com.ksisolucoes.bo.consorcio.consorcioguiaprocedimentoitem;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimentoItem;
import br.com.ksisolucoes.vo.controle.Usuario;

/**
 *
 * <AUTHOR>
 */
public class SaveConsorcioGuiaProcedimentoItem extends SaveVO<ConsorcioGuiaProcedimentoItem> {

    public SaveConsorcioGuiaProcedimentoItem(ConsorcioGuiaProcedimentoItem vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {

        Usuario usuario = getSessao().<Usuario>getUsuario();

        if (this.vo.getCodigo() == null){
            this.vo.setSituacaoSisreg(ConsorcioGuiaProcedimentoItem.SituacaoSisreg.PENDENTE.value());
        }

        this.vo.setUsuarioSituacaoSisreg(usuario);
        this.vo.setDataSituacaoSisreg(Data.getDataAtual());

    }

}
