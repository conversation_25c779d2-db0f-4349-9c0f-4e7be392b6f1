package br.com.ksisolucoes.bo.samu.checklist;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.samu.SamuChecklist;
import br.com.ksisolucoes.vo.samu.SamuChecklistItem;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SalvarChecklistSamu extends AbstractCommandTransaction{
    
    private SamuChecklist samuChecklist;
    private List<SamuChecklistItem> lstItem;

    public SalvarChecklistSamu(SamuChecklist samuChecagem, List<SamuChecklistItem> lstItem) {
        this.samuChecklist = samuChecagem;
        this.lstItem = lstItem;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        samuChecklist.setDataCadastro(DataUtil.getDataAtual());
        samuChecklist = BOFactory.save(samuChecklist);
        for(SamuChecklistItem item : lstItem){
            item.setSamuChecklist(samuChecklist);
            if(item.getUsuario() == null){
                item.setUsuario(SessaoAplicacaoImp.getInstance().getUsuario());
            }
            if(item.getDataCadastro() == null){
                item.setDataCadastro(DataUtil.getDataAtual());
            }
            item.setDataUsuario(DataUtil.getDataAtual());
        }
        
        VOUtils.persistirListaVosModificados(SamuChecklistItem.class, lstItem, new QueryCustom.QueryCustomParameter(SamuChecklistItem.PROP_SAMU_CHECKLIST, samuChecklist));
    }
    
    public SamuChecklist getObject(){
        return samuChecklist;
    }
    
}
