package br.com.ksisolucoes.bo.vacina.vacinaaplicacao;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vacina.GrupoAtendimentoVacinacaoEsus;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class SaveVacinaAplicacao extends SaveVO<VacinaAplicacao> {

    public SaveVacinaAplicacao(VacinaAplicacao vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getUsuarioCadsus()==null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_usuario_cadsus"));
        }
        if (this.vo.getUsuario()==null) {
            this.vo.setUsuario((Usuario)getSessao().getUsuario());
        }
        if (this.vo.getDataCadastro()==null) {
            this.vo.setDataCadastro(DataUtil.getDataAtual());
        }
        if (this.vo.getStatus()==null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_status"));
        }
        if (this.vo.getEmpresa()==null) {
            this.vo.setEmpresa(getSessao().<Empresa>getEmpresa());
        }
        if(this.vo.getNovoFrasco() == null){
            this.vo.setNovoFrasco(RepositoryComponentDefault.NAO_LONG);
        }
        if(this.vo.getGrupoAtendimento() == null){
            this.vo.setGrupoAtendimento(GrupoAtendimentoVacinacaoEsus.GrupoAtendimentoEsusVacina.FAIXA_ETARIA.value());
        }
        if(this.vo.getFlagGestante() == null){
            this.vo.setFlagGestante(RepositoryComponentDefault.NAO_LONG);
        }
        if(this.vo.getComunicanteHanseniase() == null){
            this.vo.setComunicanteHanseniase(RepositoryComponentDefault.NAO_LONG);
        }
        if(this.vo.getFlagForaEsquemaVacinal() == null){
            this.vo.setFlagForaEsquemaVacinal(RepositoryComponentDefault.NAO_LONG);
        }
        if (this.vo.getDataAplicacao() != null && DataUtil.zerarHora(this.vo.getDataAplicacao()).after(DataUtil.getDataAtualSemHora())){
            throw new ValidacaoException(Bundle.getStringApplication("msg_data_aplicacao_nao_pode_ser_maior_que_data_atual"));            
        }
        if(this.vo.getTipoVacina() == null && this.vo.getVacinaCalendario() != null){
            this.vo.setTipoVacina(this.vo.getVacinaCalendario().getTipoVacina());
        }
        if(this.vo.getDose() == null && this.vo.getVacinaCalendario() != null){
            this.vo.setDose(this.vo.getVacinaCalendario().getDose());
        }
        if(this.vo.getEstrategia() == null && this.vo.getVacinaCalendario() != null){
            this.vo.setEstrategia(this.vo.getVacinaCalendario().getCalendario());
        }
        if(this.vo.getFlagHistorico() == null){
            this.vo.setFlagHistorico(RepositoryComponentDefault.NAO_LONG);
        }

        if (isDataAplicacaoMesmoDiaNascimento()) {
            Date dataFim = DataUtil.finalHora(this.vo.getDataAplicacao());
            Date dataInicio = DataUtil.adjustDataAddMinutos(dataFim, -5);
            this.vo.setDataAplicacao(dataInicio);
            this.vo.setDataAplicacaoFim(dataFim);
        }
        
        this.vo.setDataAlteracao(DataUtil.getDataAtual());
        this.vo.setDataIntegracaoInovamfri(null);
    }

    private boolean isDataAplicacaoMesmoDiaNascimento() {
        return DataUtil.isDateEqualSemHora(this.vo.getDataAplicacao(), this.vo.getUsuarioCadsus().getDataNascimento());
    }

}
