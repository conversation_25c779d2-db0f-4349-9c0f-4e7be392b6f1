package br.com.ksisolucoes.bo.cadsus.cds.esusfichausuariocadsusesus;

import br.com.celk.util.Util;
import br.com.ksisolucoes.bo.cadsus.interfaces.QueryConsultaProfissionalCargaHorariaDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.ProfissionalFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.AbstractSessaoAplicacao;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEsus;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaUsuarioCadsusEsus;
import br.com.ksisolucoes.vo.esus.CboFichaEsusItem;
import br.com.ksisolucoes.vo.esus.EsusIntegracaoCds;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import ch.lambdaj.Lambda;
import org.hamcrest.Matchers;
import org.jetbrains.annotations.NotNull;

import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Created by sulivan on 07/08/17.
 */
public class GerarAtualizarEsusFichaUsuarioCadsusEsusSimplificada extends AbstractCommandTransaction<GerarAtualizarEsusFichaUsuarioCadsusEsusSimplificada> {

    private final UsuarioCadsusEsus usuarioCadsusEsus;
    private EsusFichaUsuarioCadsusEsus esusFichaUsuarioCadsusEsus;

    public GerarAtualizarEsusFichaUsuarioCadsusEsusSimplificada(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsusEsus = usuarioCadsus.getUsuarioCadsusEsus();
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        AbstractSessaoAplicacao session = SessaoAplicacaoImp.getInstance();

        //Se tiver equipe setada no paciente, avalia se esta ativo.
        EquipeProfissional equipeProfissional = getEquipeProfissional(usuarioCadsusEsus.getUsuarioCadsus().getEquipeProfissional());

        if (equipeProfissional == null
                && temInformacoesNecessariasNaSessao()) {
            equipeProfissional = getEquipeProfissional(session.getUsuario().getProfissional());
        }

        if (equipeProfissional != null || SessaoAplicacaoImp.getInstance().getUsuario().getProfissional() != null) {
            EsusIntegracaoCds esusIntegracaoCds = GerarAtualizarEsusFichaUsuarioCadsusEsusHelper.getEsusIntegracaoCds(getSession(), usuarioCadsusEsus);
            boolean criarEsusIntegracaoCds = false;

            if (esusIntegracaoCds != null
                    && esusIntegracaoCds.getEsusFichaUsuarioCadsusEsus() != null
                    && esusIntegracaoCds.getEsusFichaUsuarioCadsusEsus().getCodigo() != null) {
                atualizaIntegracaoCds(esusIntegracaoCds);
            } else {
                criarEsusIntegracaoCds = criaEsusFichaUsuarioCadsusEsus();
            }

            setProfissionalInfo(equipeProfissional != null ? equipeProfissional.getProfissional() : SessaoAplicacaoImp.getInstance().getUsuario().getProfissional());

            if (equipeProfissional != null) {
                setEquipeProfissional(equipeProfissional);
            } else {
                esusFichaUsuarioCadsusEsus.setEmpresa(SessaoAplicacaoImp.getInstance().getEmpresa());
            }

            if (esusFichaUsuarioCadsusEsus.getEmpresa() == null
                    || esusFichaUsuarioCadsusEsus.getTabelaCbo() == null) {
                return;
            }

            esusFichaUsuarioCadsusEsus = GerarAtualizarEsusFichaUsuarioCadsusEsusHelper.populateComumFieldsAndSaveEsusFichaUsuarioCadsusEsus(esusFichaUsuarioCadsusEsus, usuarioCadsusEsus);

            if (criarEsusIntegracaoCds) {
                // Gerar e-SUS Integração CDS
                GerarAtualizarEsusFichaUsuarioCadsusEsusHelper.criarEsusIntegracaoCds(esusFichaUsuarioCadsusEsus);
            }
        }
    }

    private void setEquipeProfissional(EquipeProfissional equipeProfissional) {
        Equipe equipe = null;
        Empresa empresa = null;

        if (Util.isNotNull(equipeProfissional.getEquipe())) {
            equipe = equipeProfissional.getEquipe();
            empresa = equipeProfissional.getEquipe().getEmpresa();
        }
        esusFichaUsuarioCadsusEsus.setEmpresa(empresa);
        esusFichaUsuarioCadsusEsus.setEquipe(equipe);
        esusFichaUsuarioCadsusEsus.setProfissional(equipeProfissional.getProfissional());
    }

    private void setProfissionalInfo(Profissional profissional) throws DAOException, ValidacaoException {
        esusFichaUsuarioCadsusEsus.setProfissional(profissional);
        List<TabelaCbo> tabelaCbosProfissional = BOFactory.getBO(ProfissionalFacade.class).consultaCbosProfissional(getQueryConsultaProfissionalCargaHorariaDTOParam());
        List<CboFichaEsusItem> cboFichaEsusItemList = GerarAtualizarEsusFichaUsuarioCadsusEsusHelper.getCbosPermitidos();

        for (TabelaCbo tabelaCbo : tabelaCbosProfissional) {
            if (Lambda.exists(cboFichaEsusItemList, Lambda.having(on(CboFichaEsusItem.class).getTabelaCbo().getCbo(), Matchers.equalTo(tabelaCbo.getCbo())))) {
                esusFichaUsuarioCadsusEsus.setTabelaCbo(tabelaCbo);
                break;
            }
        }
    }

    @NotNull
    private QueryConsultaProfissionalCargaHorariaDTOParam getQueryConsultaProfissionalCargaHorariaDTOParam() {
        QueryConsultaProfissionalCargaHorariaDTOParam param = new QueryConsultaProfissionalCargaHorariaDTOParam();
        param.setEmpresa(esusFichaUsuarioCadsusEsus.getEmpresa());
        param.setProfissional(esusFichaUsuarioCadsusEsus.getProfissional());
        return param;
    }

    private boolean criaEsusFichaUsuarioCadsusEsus() {
        esusFichaUsuarioCadsusEsus = new EsusFichaUsuarioCadsusEsus();
        esusFichaUsuarioCadsusEsus.setIsFichaSimplificada(RepositoryComponentDefault.SIM_LONG);
        return true;
    }

    private void atualizaIntegracaoCds(EsusIntegracaoCds esusIntegracaoCds) throws DAOException, ValidacaoException {
        esusFichaUsuarioCadsusEsus = HibernateUtil.lockTable(EsusFichaUsuarioCadsusEsus.class, esusIntegracaoCds.getEsusFichaUsuarioCadsusEsus().getCodigo());
        esusIntegracaoCds.setExportacaoEsusProcesso(null);
        esusIntegracaoCds.setDescricaoInconsistenciaEsus(null);
        esusIntegracaoCds.setUuid(null);
        BOFactory.save(esusIntegracaoCds);
    }

    public EsusFichaUsuarioCadsusEsus getEsusFichaUsuarioCadsusEsus() {
        return esusFichaUsuarioCadsusEsus;
    }

    private boolean temInformacoesNecessariasNaSessao() {
        return SessaoAplicacaoImp.getInstance() != null
                && SessaoAplicacaoImp.getInstance().getUsuario() != null
                && SessaoAplicacaoImp.getInstance().getUsuario().getProfissional() != null
                && SessaoAplicacaoImp.getInstance().getUsuario().getProfissional().getCodigo() != null;
    }

    private EquipeProfissional getEquipeProfissional(Profissional profissional) {
        if (profissional == null) {
            return null;
        }

        EquipeProfissional equipeProfissionalProxy = on(EquipeProfissional.class);
        return LoadManager.getInstance(EquipeProfissional.class)
                .addProperty(path(equipeProfissionalProxy.getCodigo()))
                .addProperty(path(equipeProfissionalProxy.getEquipe().getCodigo()))
                .addProperty(path(equipeProfissionalProxy.getEquipe().getVersion()))
                .addProperty(path(equipeProfissionalProxy.getEquipe().getEmpresa().getCodigo()))
                .addProperty(path(equipeProfissionalProxy.getEquipe().getEmpresa().getCnes()))
                .addProperty(path(equipeProfissionalProxy.getEquipe().getEmpresa().getCnpj()))
                .addProperty(path(equipeProfissionalProxy.getProfissional().getCodigo()))
                .addProperty(path(equipeProfissionalProxy.getProfissional().getCodigoCns()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(equipeProfissionalProxy.getProfissional()), profissional))
                .addParameter(new QueryCustom.QueryCustomParameter(path(equipeProfissionalProxy.getDataDesligamento()), BuilderQueryCustom.QueryParameter.IS_NULL))
                .setMaxResults(1)
                .start().getVO();
    }

    private EquipeProfissional getEquipeProfissional(EquipeProfissional equipeProfissional) {
        if (equipeProfissional == null) return null;

        EquipeProfissional equipeProfissionalProxy = on(EquipeProfissional.class);

        return LoadManager.getInstance(EquipeProfissional.class)
                .addProperty(EquipeProfissional.PROP_CODIGO)
                .addProperty(EquipeProfissional.PROP_DATA_DESLIGAMENTO)
                .addProperty(path(equipeProfissionalProxy.getProfissional().getCodigo()))
                .addProperty(path(equipeProfissionalProxy.getProfissional().getCodigoCns()))
                .addProperty(path(equipeProfissionalProxy.getProfissional().getNome()))
                .addProperty(path(equipeProfissionalProxy.getEquipe().getCodigo()))
                .addProperty(path(equipeProfissionalProxy.getEquipe().getEmpresa().getCodigo()))
                .addProperty(path(equipeProfissionalProxy.getEquipe().getEmpresa().getDescricao()))
                .addProperty(path(equipeProfissionalProxy.getEquipe().getEmpresa().getCnpj()))
                .addProperty(path(equipeProfissionalProxy.getEquipe().getEmpresa().getCnes()))
                .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_CODIGO, equipeProfissional.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_STATUS, EquipeProfissional.STATUS_ATIVO))
                .start().getVO();
    }
}