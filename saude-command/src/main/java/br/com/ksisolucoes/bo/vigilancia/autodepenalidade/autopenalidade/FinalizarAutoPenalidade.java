package br.com.ksisolucoes.bo.vigilancia.autodepenalidade.autopenalidade;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidade;

/**
 * <AUTHOR>
 */
public class FinalizarAutoPenalidade extends AbstractCommandTransaction<FinalizarAutoPenalidade> {

    private AutoPenalidade autoPenalidade;
    private boolean validacoes;

    public FinalizarAutoPenalidade(AutoPenalidade autoPenalidade, boolean validacoes) {
        this.autoPenalidade = autoPenalidade;
        this.validacoes = validacoes;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        AutoPenalidade autoPenalidade = (AutoPenalidade) getSession().get(AutoPenalidade.class, this.autoPenalidade.getCodigo());
        if(validacoes) {
            if (AutoPenalidade.Situacao.CONCLUIDO.value().equals(autoPenalidade.getSituacao())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_registro_alterado_outro_processo"));
            }
            if (autoPenalidade.getDataRecebimento() == null) {
                throw new ValidacaoException("Data de Recebimento da Penalidade Obrigatória");
            }
        }

        autoPenalidade.setSituacao(AutoPenalidade.Situacao.CONCLUIDO.value());
        this.autoPenalidade = BOFactory.save(autoPenalidade);
    }

    public AutoPenalidade getAutoPenalidade() {
        return autoPenalidade;
    }
}