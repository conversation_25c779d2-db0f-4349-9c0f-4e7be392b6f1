/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.cadsus.usuariocadsuspatologia;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusPatologia;

/**
 *
 * <AUTHOR>
 */
public class SaveUsuarioCadsusPatologia extends SaveVO<UsuarioCadsusPatologia> {

    public SaveUsuarioCadsusPatologia(UsuarioCadsusPatologia vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        UsuarioCadsusPatologia itemSalvo = LoadManager.getInstance(UsuarioCadsusPatologia.class)
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusPatologia.PROP_CID, vo.getCid()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusPatologia.PROP_USUARIO_CADSUS,UsuarioCadsus.PROP_CODIGO), vo.getUsuarioCadsus().getCodigo()))
                .start().getVO();
        if(itemSalvo != null){
            vo.setCodigo(itemSalvo.getCodigo());
        }
    }
    
}
