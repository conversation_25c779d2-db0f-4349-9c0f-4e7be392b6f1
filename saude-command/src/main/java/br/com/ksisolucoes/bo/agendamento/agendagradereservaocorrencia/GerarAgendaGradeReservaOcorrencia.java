package br.com.ksisolucoes.bo.agendamento.agendagradereservaocorrencia;

import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeReserva;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeReservaOcorrencia;

/**
 * Created by sulivan on 30/01/19.
 */
public class GerarAgendaGradeReservaOcorrencia extends AbstractCommandTransaction {

    private Long codigoAgendaGradeReserva;
    private String descricaoOcorrencia;

    public GerarAgendaGradeReservaOcorrencia(Long codigoAgendaGradeReserva, String descricaoOcorrencia) {
        this.codigoAgendaGradeReserva = codigoAgendaGradeReserva;
        this.descricaoOcorrencia = descricaoOcorrencia;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        AgendaGradeReservaOcorrencia agendaGradeReservaOcorrencia = new AgendaGradeReservaOcorrencia();
        AgendaGradeReserva agendaGradeReserva = (AgendaGradeReserva) getSession().get(AgendaGradeReserva.class, codigoAgendaGradeReserva);

        agendaGradeReservaOcorrencia.setAgendaGradeReserva(agendaGradeReserva);
        agendaGradeReservaOcorrencia.setMotivo(descricaoOcorrencia);

        BOFactory.getBO(CadastroFacade.class).save(agendaGradeReservaOcorrencia);
    }
}