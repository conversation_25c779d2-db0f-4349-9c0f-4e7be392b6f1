/*
 * AtualizarEstoqueMinimo.java
 *
 * Criada em 26 de Junho de 2006, 11:24
 */

package br.com.ksisolucoes.bo.entradas.estoque.estoqueempresa;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import org.hibernate.SQLQuery;

/**
 *
 * <AUTHOR>
 */
public class ZerarEstoqueMinimo extends AbstractCommandTransaction {
    
    private Empresa empresa;
    
    public ZerarEstoqueMinimo(Empresa empresa) {
        this.empresa = empresa;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {

        StringBuilder sql = new StringBuilder();

        sql.append("update estoque_empresa ee");
        sql.append(" set estoque_minimo = 0");

        sql.append(" where coalesce(estoque_minimo,0) > 0");


        if (empresa != null) {
            sql.append(" and empresa = ").append(empresa.getCodigo());
        }

        sql.append(" and cod_pro in (SELECT ee.cod_pro FROM estoque_empresa ee left join produtos p on ee.cod_pro = p.cod_pro "
                + "                  where p.cont_min = 'S' "
                + "                  and ee.flag_ativo = 'S' ");

        if (empresa!=null) {
            sql.append(" and empresa = ").append(empresa.getCodigo());
        }

        sql.append(")");

        try {
            SQLQuery query = getSession().createSQLQuery(sql.toString());
            query.executeUpdate();
        } catch (Exception ex) {
            throw new DAOException(ex);
        }

    }
}