package br.com.ksisolucoes.bo.service.sms.smscadastro;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.service.sms.SmsCadastro;

/**
 *
 * <AUTHOR>
 */
public class SaveSmsCadastro extends SaveVO<SmsCadastro> {

    public SaveSmsCadastro(SmsCadastro vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        
        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(DataUtil.getDataAtual());
        }
        if (this.vo.getUsuarioCadastro() == null) {
            this.vo.setUsuarioCadastro((Usuario) this.sessao.getUsuario());
        }
        if (this.vo.getStatus() == null) {
            this.vo.setStatus(SmsCadastro.Status.PENDENTE.value());
        }
    }
}
