/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.controle.usuario;

import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.UsuarioPrograma;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SalvarConfiguracoesAtalhos extends AbstractCommandTransaction {

    private String exibeNomes;
    private List<UsuarioPrograma> usuarioProgramas;

    public SalvarConfiguracoesAtalhos(String exibeNomes, List<UsuarioPrograma> usuarioProgramas) {
        this.exibeNomes = exibeNomes;
        this.usuarioProgramas = usuarioProgramas;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        BOFactory.getBO(UsuarioFacade.class).salvarUsuarioProgramas(usuarioProgramas);

        Usuario usuario = getSessao().<Usuario>getUsuario();

        usuario.setFlagExibeNomeAtalho(exibeNomes);

        getSession().saveOrUpdate(usuario);
    }

}
