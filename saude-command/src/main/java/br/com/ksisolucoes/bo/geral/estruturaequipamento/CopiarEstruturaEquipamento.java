package br.com.ksisolucoes.bo.geral.estruturaequipamento;

import br.com.ksisolucoes.bo.geral.estruturaequipamentorevisao.FinalizarEstruturaEquipamentoRevisao;
import br.com.ksisolucoes.bo.geral.estruturaequipamentorevisao.GerarEstruturaEquipamentoRevisao;
import br.com.ksisolucoes.bo.geral.interfaces.facade.EstruturaEquipamentoFacade;
import br.com.ksisolucoes.util.Bundle;


import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;

public class CopiarEstruturaEquipamento extends AbstractCommandTransaction {

    private Produto produtoOri;
    private Produto produtoDes;
    private boolean aprovar;
    
    public CopiarEstruturaEquipamento(Produto produtoOri, Produto produtoDes, boolean aprovar) {
        this.produtoOri = produtoOri;
        this.produtoDes = produtoDes;
        this.aprovar = aprovar;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        /*
         * VALIDACAO
         * ---------
         * No  possvel copiar a estrutura de um produto para ele mesmo
         *---------------------------------------------------------------------*/
        if( produtoOri.equals(produtoDes) ) {
            //O Produto de destino no pode ser igual ao Produto de origem.
            throw new ValidacaoException( Bundle.getStringBO( "2147" ) );
        }
        /*--------------------------------------------------------------------*/

        /*
         * VALIDACAO
         * ---------
         * O produto de destino no deve possuir estrutura.
         *---------------------------------------------------------------------*/
        if (BOFactory.getBO(EstruturaEquipamentoFacade.class).containsEstruturaEquipamento(produtoDes)) {
            //O Produto de destino no pode possuir estrutura.
            throw new ValidacaoException(Bundle.getStringBO( "2148" ));
        }
        /*--------------------------------------------------------------------*/

        /*
         * VALIDACAO
         * ---------
         * O produto de origem deve possuir estrutura e a procedencia deve ser
         * fabricado (sigla = 'F')
         *---------------------------------------------------------------------*/
        if( !BOFactory.getBO(EstruturaEquipamentoFacade.class).containsEstruturaEquipamento(produtoOri) ){
            //O Produto de origem deve possuir estrutura para ser copiada.
            throw new ValidacaoException(Bundle.getStringBO( "2149" ));
        }
        /*--------------------------------------------------------------------*/

//        List<EstruturaEquipamento> estruturaEquipamentoList = produtoOri.getEstruturaEquipamentoListNoLazyVerdadeiro(new EstruturaEquipamentoHelper.Bean(1) );
//
//        for (EstruturaEquipamento estrutura : estruturaEquipamentoList) {
//            estrutura.getId().setProduto(produtoDes);
//            estrutura.setRoProduto(produtoDes);
//            estrutura.setUsuario(sessao.getUsuario());
//        }
//        produtoDes.setEstruturaEquipamentoList(estruturaEquipamentoList);
        //Gerando a reviso
        GerarEstruturaEquipamentoRevisao gerar = new GerarEstruturaEquipamentoRevisao(produtoDes, this.produtoOri);
        gerar.start();
        if ( aprovar ){
            new FinalizarEstruturaEquipamentoRevisao(gerar.getProduto()).start();
        }
    }
}
