package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentoinspecaosanitaria;

import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.requerimentos.inspecaosanitaria.PrestadorServicosDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoInspecaoSanitariaPrestador;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoInspecaoSanitaria;

import java.util.List;

public class SalvarRequerimentoInspecaoSanitariaPrestadores extends AbstractCommandTransaction<SalvarRequerimentoInspecaoSanitariaPrestadores> {

    private RequerimentoInspecaoSanitaria requerimentoInspecaoSanitaria;
    private List<PrestadorServicosDTO> prestadores;
    private List<PrestadorServicosDTO> prestadoresExcluir;

    public SalvarRequerimentoInspecaoSanitariaPrestadores(RequerimentoInspecaoSanitaria requerimentoInspecaoSanitaria, List<PrestadorServicosDTO> prestadores, List<PrestadorServicosDTO> prestadoresExcluir) {
        this.requerimentoInspecaoSanitaria = requerimentoInspecaoSanitaria;
        this.prestadores = prestadores;
        this.prestadoresExcluir = prestadoresExcluir;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        RequerimentoInspecaoSanitariaPrestador prestador;
        for (PrestadorServicosDTO dto : prestadores){

            prestador = dto.getRequerimentoInspecaoSanitariaPrestador();
            if (dto.getRequerimentoInspecaoSanitariaPrestador() == null){
                prestador = new RequerimentoInspecaoSanitariaPrestador();
            }
            prestador.setRequerimentoInspecaoSanitaria(requerimentoInspecaoSanitaria);
            prestador.setEndereco(dto.getEndereco());
            prestador.setDescricaoEmpresa(dto.getEmpresa());
            prestador.setEtapaFabricacaoProcesso(dto.getEtapaFabricacaoProcesso());

            BOFactory.save(prestador);
        }
        for (PrestadorServicosDTO dto : prestadoresExcluir){
            BOFactory.delete(dto.getRequerimentoInspecaoSanitariaPrestador());
        }
    }
}
