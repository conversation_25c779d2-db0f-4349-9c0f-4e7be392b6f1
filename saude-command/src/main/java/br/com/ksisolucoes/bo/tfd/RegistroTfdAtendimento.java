package br.com.ksisolucoes.bo.tfd;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.bo.tfd.interfaces.facade.TfdFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.encaminhamento.interfaces.dto.LaudoTfdDTO;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.TipoOcorrencia;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoProntuario;
import br.com.ksisolucoes.vo.prontuario.basico.ExameApac;
import br.com.ksisolucoes.vo.prontuario.basico.ExameBpai;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.hibernate.Criteria;
import org.hibernate.criterion.Restrictions;
import org.hibernate.sql.JoinType;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RegistroTfdAtendimento extends AbstractCommandTransaction {

    private LaudoTfdDTO laudoTfdDTO;

    public RegistroTfdAtendimento(LaudoTfdDTO laudoTfdDTO) {
        this.laudoTfdDTO = laudoTfdDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        validaExisteLaudoAberto();
        boolean obrigatorioTodosCamposLaudo = RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("obrigatorioTodosCamposLaudo"));
        if (obrigatorioTodosCamposLaudo) {
            if (CollectionUtils.isAllEmpty(laudoTfdDTO.getProcedimentoSolicitadoTfdList()) && LaudoTfd.Tipo.INTRAESTADUAL.value().equals(laudoTfdDTO.getLaudoTfd().getFlagTipo())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_obrigatorio_informar_pelo_menos_um_procedimento_tratamento_solicitado"));
            }
        }
        LaudoTfd laudoTfd = this.laudoTfdDTO.getLaudoTfd();

        TipoProcedimento tipoProcedimento = (TipoProcedimento) this.getSession().get(TipoProcedimento.class, laudoTfd.getTipoProcedimento().getCodigo());
        laudoTfd.setTipoProcedimento(tipoProcedimento);

        laudoTfd.setEmpresa((Empresa) getSessao().getEmpresa());
        laudoTfd.setStatus(LaudoTfd.StatusLaudoTfd.ENCAMINHADO_MEDICO.value());

        Long cns = LoadManager.getInstance(UsuarioCadsusCns.class)
                .addGroup(new QueryCustom.QueryCustomGroup(VOUtils.montarPath(UsuarioCadsusCns.PROP_NUMERO_CARTAO), BuilderQueryCustom.QueryGroup.MIN))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), laudoTfd.getUsuarioCadsus().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_EXCLUIDO), RepositoryComponentDefault.NAO_EXCLUIDO))
                .start().getVO();
        laudoTfd.setNumeroCartao(cns);

        String observacaoOcorrencia;
        if (laudoTfd.getCodigo() == null) {
            observacaoOcorrencia = Bundle.getStringApplication("msg_laudo_adicionado_atendimento");
        } else {
            observacaoOcorrencia = Bundle.getStringApplication("msg_laudo_alterado");
        }

        ExameApac exameApac = null;
        if (laudoTfd.getExameApac() != null) {
            exameApac = (ExameApac) getSession().get(ExameApac.class, laudoTfd.getExameApac().getCodigo());
        }
        ExameBpai exameBpai = null;
        if (laudoTfd.getExameBpai() != null) {
            exameBpai = (ExameBpai) getSession().get(ExameBpai.class, laudoTfd.getExameBpai().getCodigo());
        }
        laudoTfd.setExameApac(null);
        laudoTfd.setExameBpai(null);

        BOFactory.getBO(CadastroFacade.class).save(laudoTfd);

        if(exameApac != null){
            BOFactory.getBO(ExameFacade.class).cancelarExameApac(exameApac, true);
        }
        if(exameBpai != null){
            BOFactory.getBO(ExameFacade.class).cancelarExameBpai(exameBpai, true);
        }

        BOFactory.getBO(UsuarioCadsusFacade.class).gerarOcorrenciaUsuarioCadsus(laudoTfd.getUsuarioCadsus(), TipoOcorrencia.TIPO_TFD, observacaoOcorrencia, laudoTfd);

        BOFactory.getBO(TfdFacade.class).salvarProcedimentoSolicitadoTfdList(laudoTfdDTO.getProcedimentoSolicitadoTfdList(), laudoTfdDTO.getLaudoTfd());

        salvarAtendimentoProntuario();

        if (!TipoProcedimento.FLAG_BPAI_APAC_IMPRIMIR_NAO.equals(laudoTfd.getTipoProcedimento().getImprimeBpaiApac())) {
            List<Long> codigoExameApac = Collections.singletonList(BOFactory.getBO(ExameFacade.class).gerarExameLaudoTfd(laudoTfd.getCodigo()));
            Long codigoExame = (BOFactory.getBO(ExameFacade.class).gerarExameLaudoTfd(laudoTfd.getCodigo()));
            if (TipoProcedimento.FLAG_BPAI_APAC_IMPRIMIR_BPAI.equals(laudoTfd.getTipoProcedimento().getImprimeBpaiApac())) {
                BOFactory.getBO(ExameFacade.class).gerarExameBpaiLaudoTfd(laudoTfd.getCodigo(), codigoExame);
            } else if (TipoProcedimento.FLAG_BPAI_APAC_IMPRIMIR_APAC.equals(laudoTfd.getTipoProcedimento().getImprimeBpaiApac())) {
                BOFactory.getBO(ExameFacade.class).gerarExameApacLaudoTfd(laudoTfd.getCodigo(), codigoExameApac);
            }
        }

     }

    /**
     * Valida se já possui laudo para o mesmo procedimento e tipo de procedimento
     */
    private void validaExisteLaudoAberto() throws ValidacaoException {
        Criteria criteria = getSession().createCriteria(LaudoTfd.class)
                .add(Restrictions.eq(LaudoTfd.PROP_USUARIO_CADSUS, laudoTfdDTO.getLaudoTfd().getUsuarioCadsus()))
                .add(Restrictions.eq(LaudoTfd.PROP_TIPO_PROCEDIMENTO, laudoTfdDTO.getLaudoTfd().getTipoProcedimento()))
                .add(Restrictions.eq(LaudoTfd.PROP_PROCEDIMENTO, laudoTfdDTO.getLaudoTfd().getProcedimento()))
                .add(Restrictions.in(LaudoTfd.PROP_STATUS,
                                Arrays.asList(
                                        LaudoTfd.StatusLaudoTfd.ENCAMINHADO_MEDICO.value(),
                                        LaudoTfd.StatusLaudoTfd.RECEBIDO.value(),
                                        LaudoTfd.StatusLaudoTfd.ENCAMINHADO_REGIONAL.value(),
                                        LaudoTfd.StatusLaudoTfd.AUTORIZADO.value()
                                )
                        ))
                .createCriteria(LaudoTfd.PROP_PEDIDO_TFD, JoinType.LEFT_OUTER_JOIN)
                .createCriteria(PedidoTfd.PROP_SOLICITACAO_AGENDAMENTO, JoinType.LEFT_OUTER_JOIN);
        
        if(laudoTfdDTO.getLaudoTfd() != null && laudoTfdDTO.getLaudoTfd().getCodigo() != null){
            criteria.add(Restrictions.ne(LaudoTfd.PROP_CODIGO, laudoTfdDTO.getLaudoTfd().getCodigo()));
        }
         
        List<LaudoTfd> listLaudoTfd = criteria.list();

        for (LaudoTfd laudoTfd : listLaudoTfd) {
            if (laudoTfd != null) {
                if (LaudoTfd.StatusLaudoTfd.AUTORIZADO.value().equals(laudoTfd.getStatus())) {
                    // NÃO deve validar se STATUS for AUTORIZADO e a DATA AGENDAMENTO for MENOR que a DATA ATUAL
                    if (laudoTfd.getPedidoTfd() != null
                            && laudoTfd.getPedidoTfd().getSolicitacaoAgendamento() != null
                            && laudoTfd.getPedidoTfd().getSolicitacaoAgendamento().getDataAgendamento() != null
                            && laudoTfd.getPedidoTfd().getSolicitacaoAgendamento().getDataAgendamento().before(DataUtil.getDataAtual())) {
                        continue;
                    }
                }
                throw new ValidacaoException(Bundle.getStringApplication("msg_existe_laudo_tfd_aberto_para_este_paciente"));
            }
        }

    }

    public LaudoTfdDTO getLaudoTfdDTO() {
        return laudoTfdDTO;
    }

    private void salvarAtendimentoProntuario() throws DAOException, ValidacaoException {
        LaudoTfd laudoTfd = this.laudoTfdDTO.getLaudoTfd();

        AtendimentoProntuario atendimentoProntuario = (AtendimentoProntuario) getSession().createCriteria(AtendimentoProntuario.class)
                .add(Restrictions.eq(AtendimentoProntuario.PROP_LAUDO_TFD, laudoTfd))
                .add(Restrictions.eq(AtendimentoProntuario.PROP_ATENDIMENTO, laudoTfd.getAtendimento()))
                .uniqueResult();

        if (atendimentoProntuario != null) {
            BOFactory.getBO(AtendimentoFacade.class).removerAtendimentoProntuario(AtendimentoProntuario.TipoRegistro.TFD.value(), laudoTfd.getAtendimento(), laudoTfd.getCodigo());
        }

        atendimentoProntuario = new AtendimentoProntuario();
        atendimentoProntuario.setData(DataUtil.getDataAtual());
        atendimentoProntuario.setDescricao(getDescricaoLaudoTfdFormatado());
        atendimentoProntuario.setTipoRegistro(AtendimentoProntuario.TipoRegistro.TFD.value());
        atendimentoProntuario.setUsuarioCadsus(laudoTfd.getUsuarioCadsus());
        atendimentoProntuario.setEmpresa(laudoTfd.getEmpresa());
        atendimentoProntuario.setProfissional(laudoTfd.getProfissional());
        atendimentoProntuario.setAtendimento(laudoTfd.getAtendimento());
        atendimentoProntuario.setLaudoTfd(laudoTfd);
        if(laudoTfd.getAtendimento() != null){
            atendimentoProntuario.setTabelaCbo(laudoTfd.getAtendimento().getTabelaCbo());
        }

        BOFactory.save(atendimentoProntuario);
    }

    private String getDescricaoLaudoTfdFormatado() {
        StringBuilder descricao = new StringBuilder();

        if (RepositoryComponentDefault.SIM_LONG.equals(getSessao().getUsuario().getFlagUsuarioTemporario())) {
            descricao.append("<b>Registrado por: </b>");
            descricao.append(getSessao().getUsuario().getNome());
            descricao.append("\n<br/>");
        }

        String urgente = "Não";
        if (RepositoryComponentDefault.NAO_LONG.equals(laudoTfdDTO.getLaudoTfd().getFlagUrgente())) {
            urgente = "Sim";
        }

        descricao.append("<u>");
        descricao.append(Bundle.getStringApplication("rotulo_encaminhamento_tfd"));
        descricao.append(": </u>");
        descricao.append("\n<br/>");
        descricao.append(Bundle.getStringApplication("rotulo_procedimento"));
        descricao.append(": ");
        descricao.append(laudoTfdDTO.getLaudoTfd().getTipoProcedimento().getDescricao());
        descricao.append(" - ");
        descricao.append(Bundle.getStringApplication("rotulo_urgente"));
        descricao.append(": ");
        descricao.append(urgente);
        descricao.append("\n<br/>");
        descricao.append(laudoTfdDTO.getLaudoTfd().getHistoricoDoenca());

        return descricao.toString();
    }
}
