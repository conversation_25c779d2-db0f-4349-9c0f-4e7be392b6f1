package br.com.ksisolucoes.bo.cadsus.usuariocadsusdado;

import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Doenca;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDoenca;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class AtualizarUsuarioCadsusDadoGestante extends AbstractCommandTransaction {

    private UsuarioCadsus usuarioCadsus;
    private Date dum;

    public AtualizarUsuarioCadsusDadoGestante(UsuarioCadsus usuarioCadsus, Date dum) {
        this.usuarioCadsus = usuarioCadsus;
        this.dum = dum;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        // UsuarioCadsusDado
        UsuarioCadsusDado usuarioCadsusDado = (UsuarioCadsusDado) getSession().createCriteria(UsuarioCadsusDado.class)
                .add(Restrictions.eq(UsuarioCadsusDado.PROP_CODIGO, usuarioCadsus.getCodigo()))
                .uniqueResult();

        boolean salvarUsuarioCadsusDado = false;

        if (usuarioCadsusDado == null) {
            usuarioCadsusDado = new UsuarioCadsusDado();
            usuarioCadsusDado.setCodigo(usuarioCadsus.getCodigo());
            salvarUsuarioCadsusDado = true;
        }

        if (usuarioCadsusDado.getGestante() == null || RepositoryComponentDefault.NAO_LONG.equals(usuarioCadsusDado.getGestante())) {
            usuarioCadsusDado.setGestante(RepositoryComponentDefault.SIM_LONG);
            usuarioCadsusDado.setDum(dum);
            salvarUsuarioCadsusDado = true;
        }

        if (salvarUsuarioCadsusDado) {
            BOFactory.save(usuarioCadsusDado);
        }

        // Condição/Situações de Saúde
        boolean existsUsuarioCadsusDoenca = this.getSession().createCriteria(UsuarioCadsusDoenca.class)
                .add(Restrictions.eq(UsuarioCadsusDoenca.PROP_USUARIO_CADSUS, usuarioCadsus))
                .createCriteria(UsuarioCadsusDoenca.PROP_DOENCA)
                .add(Restrictions.eq(Doenca.PROP_CONDICAO_ESUS, Doenca.CondicaoEsus.GESTANTE.value()))
                .list().size() > 0;

        if (!existsUsuarioCadsusDoenca) {
            Doenca doenca = (Doenca) getSession().createCriteria(Doenca.class)
                    .add(Restrictions.eq(Doenca.PROP_CONDICAO_ESUS, Doenca.CondicaoEsus.GESTANTE.value()))
                    .add(Restrictions.eq(Doenca.PROP_PADRAO, RepositoryComponentDefault.SIM))
                    .uniqueResult();

            UsuarioCadsusDoenca usuarioCadsusDoenca = new UsuarioCadsusDoenca();
            usuarioCadsusDoenca.setUsuarioCadsus(usuarioCadsus);
            usuarioCadsusDoenca.setDoenca(doenca);

            BOFactory.save(usuarioCadsusDoenca);

            BOFactory.getBO(UsuarioCadsusFacade.class).atualizarDoencasUsuarioCadsusEsus(usuarioCadsus);
        }
    }

}
