package br.com.ksisolucoes.bo.basico.graficodesenvolvimento;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.prontuario.web.chart.dto.CurvaCrescimentoPacienteDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.GraficoDesenvolvimento;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.transform.ResultTransformer;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryCurvaCrescimentoPaciente extends CommandQuery<QueryCurvaCrescimentoPaciente> {
    
    private UsuarioCadsus usuarioCadsus;
    private Long tipoGrafico;
    private CurvaCrescimentoPacienteDTO result = new CurvaCrescimentoPacienteDTO();
    private List<Number> pesoList = new ArrayList<Number>();
    private List<Number> alturaList = new ArrayList<Number>();
    private List<Number> imcList = new ArrayList<Number>();
    private List<Date> dataAvaliacaoList = new ArrayList<Date>();
    private List<Number> perimetroCefalicoList = new ArrayList<Number>();

    public QueryCurvaCrescimentoPaciente(UsuarioCadsus usuarioCadsus, Long tipoGrafico) {
        this.usuarioCadsus = usuarioCadsus;
        this.tipoGrafico = tipoGrafico;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("atendimentoPrimario.peso");
        hql.addToSelect("atendimentoPrimario.altura");
        hql.addToSelect("atendimentoPrimario.imc");
        hql.addToSelect("atendimentoPrimario.dataAvaliacao");
        hql.addToSelect("atendimentoPrimario.perimetroCefalico");

        hql.addToFrom("AtendimentoPrimario atendimentoPrimario"
                + " left join atendimentoPrimario.atendimento atendimento"
                + " left join atendimento.usuarioCadsus usuarioCadsus");
        
        hql.addToWhereWhithAnd("usuarioCadsus = ", this.usuarioCadsus);
        hql.addToWhereWhithAnd("atendimentoPrimario.dataAvaliacao between :dataInicial and :dataFinal" );
        
        if(GraficoDesenvolvimento.TipoGrafico.PESO_POR_COMPRIMENTO.value().equals(this.tipoGrafico)
                || GraficoDesenvolvimento.TipoGrafico.PESO_POR_ESTATURA.value().equals(this.tipoGrafico)){   
            hql.addToWhereWhithAnd("atendimentoPrimario.altura between :alturaInicial and :alturaFinal" );
        }
        
        if(GraficoDesenvolvimento.TipoGrafico.PESO_POR_IDADE.value().equals(this.tipoGrafico)
                || GraficoDesenvolvimento.TipoGrafico.PESO_POR_IDADE_CINCO_A_DEZ_ANOS.value().equals(this.tipoGrafico)){
            hql.addToOrder("atendimentoPrimario.dataAvaliacao asc");
            hql.addToOrder("atendimentoPrimario.peso asc");
        } else if(GraficoDesenvolvimento.TipoGrafico.PESO_POR_COMPRIMENTO.value().equals(this.tipoGrafico)
                || GraficoDesenvolvimento.TipoGrafico.PESO_POR_ESTATURA.value().equals(this.tipoGrafico)){
            hql.addToOrder("atendimentoPrimario.altura asc");
            hql.addToOrder("atendimentoPrimario.peso asc");
        } else if(GraficoDesenvolvimento.TipoGrafico.COMPRIMENTO_ESTATURA_POR_IDADE.value().equals(this.tipoGrafico)
                || GraficoDesenvolvimento.TipoGrafico.ESTATURA_POR_IDADE_CINCO_A_DEZENOVE_ANOS.value().equals(this.tipoGrafico)){
            hql.addToOrder("atendimentoPrimario.dataAvaliacao asc");
            hql.addToOrder("atendimentoPrimario.altura asc");
        } else if(GraficoDesenvolvimento.TipoGrafico.IMC_POR_IDADE.value().equals(this.tipoGrafico)
                || GraficoDesenvolvimento.TipoGrafico.IMC_POR_IDADE_CINCO_A_DEZENOVE_ANOS.value().equals(this.tipoGrafico)){
            hql.addToOrder("atendimentoPrimario.dataAvaliacao asc");
            hql.addToOrder("atendimentoPrimario.imc asc");
        } else if(GraficoDesenvolvimento.TipoGrafico.PERIMETRO_CEFALICO_POR_IDADE.value().equals(this.tipoGrafico)) {
            hql.addToOrder("atendimentoPrimario.dataAvaliacao asc");
        }
    }
    
    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setDate("dataInicial", this.usuarioCadsus.getDataNascimento());
        query.setDate("dataFinal", Data.addDias(this.usuarioCadsus.getDataNascimento(), 1855));
        
        if(GraficoDesenvolvimento.TipoGrafico.PESO_POR_COMPRIMENTO.value().equals(this.tipoGrafico)){
            query.setLong("alturaInicial", 45L);
            query.setLong("alturaFinal", 110L);
        } else if(GraficoDesenvolvimento.TipoGrafico.PESO_POR_ESTATURA.value().equals(this.tipoGrafico)){
            query.setLong("alturaInicial", 65L);
            query.setLong("alturaFinal", 120L);
        }
        
        if(GraficoDesenvolvimento.TipoGrafico.PESO_POR_IDADE_CINCO_A_DEZ_ANOS.value().equals(this.tipoGrafico)){
            query.setDate("dataInicial", Data.addMeses(Data.addAnos(this.usuarioCadsus.getDataNascimento(), 5), 1));
            query.setDate("dataFinal", Data.addDias(Data.addAnos(this.usuarioCadsus.getDataNascimento(), 10), 29));   
        } else if(GraficoDesenvolvimento.TipoGrafico.ESTATURA_POR_IDADE_CINCO_A_DEZENOVE_ANOS.value().equals(this.tipoGrafico)
                || GraficoDesenvolvimento.TipoGrafico.IMC_POR_IDADE_CINCO_A_DEZENOVE_ANOS.value().equals(this.tipoGrafico)){
            query.setDate("dataInicial", Data.addMeses(Data.addAnos(this.usuarioCadsus.getDataNascimento(), 5), 1));
            query.setDate("dataFinal", Data.addDias(Data.addAnos(this.usuarioCadsus.getDataNascimento(), 19), 29));   
        }
    }
    
    
    @Override
    protected void customQuery(Query query) {
        query.setResultTransformer(new ResultTransformer() {
            @Override
            public Object transformTuple(Object[] os, String[] strings) {
                pesoList.add((Number) os[0]);
                alturaList.add((Number) os[1]);
                imcList.add((Number) os[2]);
                dataAvaliacaoList.add((Date) os[3]);
                perimetroCefalicoList.add((Double) os[4]);
                return null;
            }

            @Override
            public List transformList(List list) {
                return list;
            }
        });
    }
    
    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        this.result.setPeso(pesoList);
        this.result.setAltura(alturaList);
        this.result.setImc(imcList);
        this.result.setDataAvaliacao(dataAvaliacaoList);
        this.result.setPerimetroCefalico(perimetroCefalicoList);
    }
    
    public CurvaCrescimentoPacienteDTO getResultDTO() {
        return this.result;
    }
}
