package br.com.ksisolucoes.bo.entradas.dispensacao.dispensacaomedicamentoitem;

import br.com.celk.materiais.bnafar.dto.GeracaoBnafarDTO;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.Util;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.bo.entradas.estoque.movimentoestoque.SaveMovimentoEstoque;
import br.com.ksisolucoes.bo.estoque.EstoqueHelper;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.ReceituarioHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.ConcurrentDAOException;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.parametrogem.IParameterModuleContainer;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Parametro;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.dispensacao.*;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItemComponente;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItemKit;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import org.hibernate.Criteria;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;

/**
 * <AUTHOR> Giordani
 *
 */
public class SaveDispensacaoMedicamentoItem extends SaveVO {

    private static final long serialVersionUID = 1L;
    private DispensacaoMedicamentoItem dispensacaoMedicamentoItem;
    private boolean isNew;

    public SaveDispensacaoMedicamentoItem(Object vo) {
        super(vo);
        this.dispensacaoMedicamentoItem = (DispensacaoMedicamentoItem) vo;
        this.dispensacaoMedicamentoItem.getRetornoValidacao().limpar();
    }

    /**
     * {@inheritDoc}
     *
     * @return {@inheritDoc}
     */
    /* (non-Javadoc)
     * @see br.com.ksisolucoes.bo.command.SaveVO#antesSave()
     */
    protected void antesSave() throws ValidacaoException, DAOException {

        this.dispensacaoMedicamentoItem.setDataUltimaDispensacao(dispensacaoMedicamentoItem.getDispensacaoMedicamento().getDataDispensacao());

        DispensacaoMedicamento dispensacaoMedicamento = this.dispensacaoMedicamentoItem.getDispensacaoMedicamento();
        this.isNew = this.dispensacaoMedicamentoItem.getCodigo() == null;

        //Somente so avaliados dados para itens ainda no persistidos.
        if (this.isNew) {

            Long itemMax = (Long) this.getSession().createCriteria(DispensacaoMedicamentoItem.class)
                    .add(Restrictions.eq(DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO, this.dispensacaoMedicamentoItem.getDispensacaoMedicamento()))
                    .setProjection(Projections.max(DispensacaoMedicamentoItem.PROP_ITEM))
                    .uniqueResult();

            if (itemMax == null) {
                this.dispensacaoMedicamentoItem.setItem(1L);
            } else {
                this.dispensacaoMedicamentoItem.setItem(++itemMax);
            }

            boolean isMedicamento = ProdutoHelper.isMedicamento(this.dispensacaoMedicamentoItem.getProduto());

            //Replica usuario sus no item.
            this.dispensacaoMedicamentoItem.setUsuarioCadsusDestino(dispensacaoMedicamento.getUsuarioCadsusDestino());
            if (this.dispensacaoMedicamentoItem.getStatus() == null) {
                this.dispensacaoMedicamentoItem.setStatus(DispensacaoMedicamentoItem.STATUS_NORMAL);
            } else if (DispensacaoMedicamentoItem.STATUS_SEM_ESTOQUE.equals(dispensacaoMedicamentoItem.getStatus()) && dispensacaoMedicamentoItem.getQuantidadeDispensada() == null) {
                dispensacaoMedicamentoItem.setQuantidadeDispensada(0D);
            }

            if (isMedicamento) {
                /**
                 * VALIDACAO --------- Verificando se o produto tem qtd
                 * prescrita, o unico que no preciso ter prescrita o tipo de
                 * receita continua.
                 */
                if (!DispensacaoMedicamentoHelper.isReceitaContinua(this.dispensacaoMedicamentoItem.getDispensacaoMedicamento())
                        && Coalesce.asDouble(this.dispensacaoMedicamentoItem.getQuantidadePrescrita()) <= 0D) {
                    dispensacaoMedicamentoItem.getRetornoValidacao().add(Bundle.getStringApplication("msg_campo_X_deve_ser_definido", Bundle.getStringApplication("rotulo_quantidade_prescricao", this.sessao.getLocale()), this.sessao.getLocale()));
                }


                /*
                 * CONTROLE SOBRE VALIDADE DA RECEITA
                 * ----------------------------------
                 * A data de validade da receita  definido com base na data da receita
                 * acrescido da quantidade de dias validos para uma receita de um determinado
                 * grupo de produtos. Caso a receita for do tipo basico e continua o prazo de
                 * validade  a do parametro. Parametro.PROP_INTERVALO_VALIDADE_CONTINUO
                 *---------------------------------------------------------------------*/
                if(!ReceituarioHelper.isAntibioticos(this.dispensacaoMedicamentoItem.getProduto())) {
                    IParameterModuleContainer parametrosMateriais = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS);
                    if (DispensacaoMedicamentoHelper.isReceitaContinua(this.dispensacaoMedicamentoItem.getDispensacaoMedicamento())) {
                        this.dispensacaoMedicamentoItem.setDataValidadeReceita(DispensacaoMedicamentoItemHelper.getDataValidadeReceita(this.dispensacaoMedicamentoItem.getDispensacaoMedicamento().getDataDispensacao(),
                                Coalesce.asLong(this.dispensacaoMedicamentoItem.getProduto().getValidadeReceitaContinua(), (Long) parametrosMateriais.getParametro("validadeReceitaUsoContinuo"))));
                    } else {
                        Long validadeReceita = Coalesce.asLong(dispensacaoMedicamento.getTipoReceita().getDiasMaximoTratamento());
                        this.dispensacaoMedicamentoItem.setDataValidadeReceita(DispensacaoMedicamentoItemHelper.getDataValidadeReceita(this.dispensacaoMedicamentoItem.getDispensacaoMedicamento().getDataReceita(), validadeReceita));
                    }
                }
                /*---------------------------------------------------------------------*/

                /*
                 * CONTROLE SOBRE STATUS
                 * ---------------------
                 * Caso o saldo no ter sido ainda definido, ser ento definido com base
                 * em algumas validaes:
                 * - Caso no exista nmero de receita, no haver controle de saldo;
                 * - Caso a quantidade dispensada seja igual a quantidade prescrita,
                 * caracteriza-se por ser uma dispensao completa;
                 * - Caso seja uma receita continua.
                 * - Caso contrrio, ser considerada, COM SALDO.
                 *---------------------------------------------------------------------*/
                if (this.dispensacaoMedicamentoItem.getStatus() == null) {
                    Long status = DispensacaoMedicamentoItem.STATUS_SEM_ESTOQUE;

                    if (Coalesce.asString(dispensacaoMedicamento.getReceita()).equals("")
                            || Coalesce.asDouble(this.dispensacaoMedicamentoItem.getQuantidadePrescrita()).equals(this.dispensacaoMedicamentoItem.getCoalesceQuantidadeDispensada())
                            || (TipoReceita.RECEITA_BASICA.equals(dispensacaoMedicamento.getTipoReceita())
                            && DispensacaoMedicamentoHelper.isReceitaContinua(this.dispensacaoMedicamentoItem.getDispensacaoMedicamento()))) {
                        status = DispensacaoMedicamentoItem.STATUS_NORMAL;
                    }

                    this.dispensacaoMedicamentoItem.setStatus(status);
                }
                if (!DispensacaoMedicamentoItem.STATUS_SEM_ESTOQUE.equals(dispensacaoMedicamentoItem.getStatus())) {
                    /*---------------------------------------------------------------------*/

                    /*
                     * CONTROLE SOBRE DATA DA PROXIMA DISPENSACAO
                     * ------------------------------------------
                     * A data da prxima dispensao ser definido pelo valor da validade da receita
                     * descontados os dias de validade do restante do saldo para um determinado
                     * grupo de produtos.
                     *---------------------------------------------------------------------*/
                    /*
                     * Comentado pois esta data é utilizada para controle para retirada, mesmo quando não há controle saldo dispensacaoMedicamentoItem = null
                     */
                    //            if(DispensacaoMedicamentoItem.STATUS_COM_SALDO.equals(this.dispensacaoMedicamentoItem.getStatus())){
                    TipoReceita tipoReceita = (TipoReceita) getSession().get(TipoReceita.class, dispensacaoMedicamento.getTipoReceita().getCodigo());

                    if (!TipoReceita.RECEITA_PRESCRICAO_ATENDIMENTO.equals(tipoReceita.getTipoReceita()) && !TipoReceita.RECEITA_SOLICITACAO_MATERIAIS.equals(tipoReceita.getTipoReceita())) {
                        this.dispensacaoMedicamentoItem.setDataProximaDispensacao(DispensacaoMedicamentoItemHelper.getDataProximaDispensacao(this.dispensacaoMedicamentoItem, dispensacaoMedicamento.getUsuarioCadsusDestino(), dispensacaoMedicamento.getDataDispensacao()));
                    } else {
                        this.dispensacaoMedicamentoItem.setDataProximaDispensacao(dispensacaoMedicamento.getDataDispensacao());
                    }

                    if (this.dispensacaoMedicamentoItem.getDataProximaDispensacao() == null) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_nao_foi_possivel_dispensar_medicamentos_data_proxima_dispensacao_nao_gerada"));
                    }
                    //            }
                    /*---------------------------------------------------------------------*/
                }
            }
        }

        this.dispensacaoMedicamentoItem.setUltimoPreco(EstoqueEmpresaHelper.getUltimoPreco(this.dispensacaoMedicamentoItem.getDispensacaoMedicamento().getEmpresa(), this.dispensacaoMedicamentoItem.getProduto()));

        if (this.dispensacaoMedicamentoItem.getTipoUso() == null) {
            this.dispensacaoMedicamentoItem.setTipoUso(DispensacaoMedicamentoItem.TipoUso.DIA.value());
        }

        /**
         * PREOS -------- Salvando na dispensao o preo de custo e preo mdio do
         * produto.
         */
        if (Coalesce.asDouble(this.dispensacaoMedicamentoItem.getPrecoCusto()) == 0D) {
            this.dispensacaoMedicamentoItem.setPrecoCusto(EstoqueEmpresaHelper.getPrecoCusto(empresaDispensacaoMedicamento(), this.dispensacaoMedicamentoItem.getProduto()));
        }
        if (Coalesce.asDouble(this.dispensacaoMedicamentoItem.getPrecoMedio()) == 0D) {
            this.dispensacaoMedicamentoItem.setPrecoMedio(EstoqueEmpresaHelper.getPrecoMedio(empresaDispensacaoMedicamento(), this.dispensacaoMedicamentoItem.getProduto()));
        }
        /**
         * *****************************************
         */
    }

    protected void depoisSave() throws ValidacaoException, DAOException {
        //Somente so avaliados dados para itens ainda no persistidos.
        Produto produto = this.dispensacaoMedicamentoItem.getProduto();
        SubGrupo subGrupo = produto.getSubGrupo();
        if (subGrupo == null || subGrupo.getFlagControlaEstoque() == null) {
            produto = LoadManager.getInstance(Produto.class)
                    .addProperties(new HQLProperties(Produto.class).getProperties())
                    .addProperties(new HQLProperties(SubGrupo.class, Produto.PROP_SUB_GRUPO).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(Produto.PROP_CODIGO, produto.getCodigo()))
                    .start().getVO();
            subGrupo = produto.getSubGrupo();
        }

        if (!DispensacaoMedicamentoItem.STATUS_SEM_ESTOQUE.equals(dispensacaoMedicamentoItem.getStatus())
                && this.isNew && RepositoryComponentDefault.SIM_LONG.equals(subGrupo.getFlagControlaEstoque())) {
            /*
             * MOVIMENTAO DE ESTOQUE
             * -----------------------
             * Gerao de movimentao de estoque da dispensao.
             *---------------------------------------------------------------------*/
            Empresa empresa = this.dispensacaoMedicamentoItem.getDispensacaoMedicamento().getEmpresa();
            Empresa unidadeOrigem = this.dispensacaoMedicamentoItem.getDispensacaoMedicamento().getEmpresaOrigem();
            Double quantidade = this.dispensacaoMedicamentoItem.getCoalesceQuantidadeDispensada();
            Long numeroDocumento = this.dispensacaoMedicamentoItem.getDispensacaoMedicamento().getCodigo();
            Long itemNumeroDocumento = this.dispensacaoMedicamentoItem.getItem();
            String pessoa = this.dispensacaoMedicamentoItem.getDispensacaoMedicamento().getNomeUsuarioDestino();
//            String observacao = Bundle.getStringApplication("msg_observacao_mov_estoque_dispensacao",
//                    this.dispensacaoMedicamentoItem.getDispensacaoMedicamento().getNomeUsuarioDestino(),
//                    this.dispensacaoMedicamentoItem.getDispensacaoMedicamento().getReceita(),
//                    this.sessao.getLocale());

            Parametro parametro = CargaBasicoPadrao.getInstance().getParametroPadrao();
            TipoDocumento tipoDocumentoDispensacaoMedicamento = parametro.getTipoDocumentoDispensacaoMedicamento();

            /*
             * Validao de parmetros obrigatrios.
             *---------------------------------------------------------------------*/
            RetornoValidacao retornoValidacao = new RetornoValidacao();
            if (tipoDocumentoDispensacaoMedicamento == null) {
                retornoValidacao.add(Bundle.getStringApplication("msg_parametro_X_nao_definido", Parametro.PROP_TIPO_DOCUMENTO_DISPENSACAO_MEDICAMENTO, this.sessao.getLocale()), "parametro." + Parametro.PROP_TIPO_DOCUMENTO_DISPENSACAO_MEDICAMENTO);
            }

            if (!retornoValidacao.isValido()) {
                throw new ValidacaoException(retornoValidacao);
            }

            MovimentoEstoque movimentoEstoque = new MovimentoEstoque(new MovimentoEstoquePK(empresa, null));

            try {

                movimentoEstoque.setTipoDocumento(tipoDocumentoDispensacaoMedicamento);
                movimentoEstoque.setProduto(produto);
                movimentoEstoque.setQuantidade(quantidade);
                movimentoEstoque.setNumeroDocumento(Coalesce.asString(numeroDocumento));
                movimentoEstoque.setItemDocumento(itemNumeroDocumento);
                movimentoEstoque.setNomePessoa(pessoa);
                movimentoEstoque.setUsuarioCadsus(this.dispensacaoMedicamentoItem.getDispensacaoMedicamento().getUsuarioCadsusDestino());
                movimentoEstoque.setObservacao("");
                movimentoEstoque.setEmpresaDestino(dispensacaoMedicamentoItem.getDispensacaoMedicamento().getEmpresa());
                movimentoEstoque.setCentroCusto(SessaoAplicacaoImp.getInstance().<Usuario>getUsuario().getCentroCusto());
                movimentoEstoque.setProfissional(this.dispensacaoMedicamentoItem.getDispensacaoMedicamento().getProfissional());
                movimentoEstoque.setMovimentosGrupoEstoqueItem(this.dispensacaoMedicamentoItem.getMovimentoGrupoEstoqueItemDTOList());
                movimentoEstoque.setDataPortaria(dispensacaoMedicamentoItem.getDispensacaoMedicamento().getDataDispensacao());
                movimentoEstoque.setDispensacaoMedicamentoItem(dispensacaoMedicamentoItem);

                MovimentoEstoquePK movimentoEstoquePK = new MovimentoEstoquePK();
                movimentoEstoquePK.setEmpresa(empresaDispensacaoMedicamento());
                movimentoEstoque.setId(movimentoEstoquePK);

                new SaveMovimentoEstoque(movimentoEstoque).start();



            } catch (ValidacaoException exc) {
                this.dispensacaoMedicamentoItem.getRetornoValidacao().merge(exc.getRetornoValidacao());
            }

            if (!this.dispensacaoMedicamentoItem.getRetornoValidacao().isVazio()) {
                throw new ValidacaoException(this.dispensacaoMedicamentoItem.getRetornoValidacao());
            }

            GeracaoBnafarDTO dto = new GeracaoBnafarDTO(movimentoEstoque);
            BOFactory.getBO(MaterialBasicoFacade.class).gerarBnafarDispensacao(dto);


//            }
            /*---------------------------------------------------------------------*/

            /*
             * GERAR DISPENSACAO MEDICAMENTO SALDO
             * -----------------------------------
             * Somente gera registros de saldo para qnd for um usurio SUS cadastrado
             * e haver saldo remanescente.
             *
             * Gera o saldo com empresaDispensacao = null para indicar que foi o primeiro/único.
             *---------------------------------------------------------------------*/
//            if ( dispensacaoMedicamento.getUsuarioCadsusDestino() != null &&
//                    DispensacaoMedicamentoItem.STATUS_COM_SALDO.equals( this.dispensacaoMedicamentoItem.getStatus() ) &&
//                    this.dispensacaoMedicamentoItem.getSaldo() > 0D ){
            SubGrupo subgrupo = (SubGrupo) getSession().get(SubGrupo.class, this.dispensacaoMedicamentoItem.getProduto().getSubGrupo().getId());

            if (subgrupo.isExigeGrupo()) {
                if (CollectionUtils.isNotNullEmpty(this.dispensacaoMedicamentoItem.getMovimentoGrupoEstoqueItemDTOList())) {
                    for (MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO : this.dispensacaoMedicamentoItem.getMovimentoGrupoEstoqueItemDTOList()) {
                        /**
                         * se houver codigo de barras cadastrado, gera um
                         * DispensacaoItemLote para cada codigo de barras se
                         * houver registros sem codigo de barras, gera um
                         * DispensacaoItemLote separado com a quantidade
                         * selecionada
                         */
                        if (!movimentoGrupoEstoqueItemDTO.getLstCodigoBarrasProduto().isEmpty()) {
                            for (CodigoBarrasProduto cbp : movimentoGrupoEstoqueItemDTO.getLstCodigoBarrasProduto()) {
                                if(cbp != null) {
                                    cbp.setDispensacaoMedicamentoItem(this.dispensacaoMedicamentoItem);
                                    cbp.setStatus(CodigoBarrasProduto.Status.DISPENSADO.value());
                                    BOFactory.getBO(CadastroFacade.class).save(cbp);
                                }
                            }

                        }
                        DispensacaoItemLote dispensacaoItemLote = new DispensacaoItemLote();

                        GrupoEstoque ge = (GrupoEstoque) getSession().get(GrupoEstoque.class, movimentoGrupoEstoqueItemDTO.getGrupoEstoquePK());

                        dispensacaoItemLote.setDispensacaoMedicamentoItem(this.dispensacaoMedicamentoItem);
                        dispensacaoItemLote.setGrupoEstoque(ge);
                        dispensacaoItemLote.setQuantidade(movimentoGrupoEstoqueItemDTO.getQuantidade());

                        BOFactory.getBO(CadastroFacade.class).save(dispensacaoItemLote);
                    }
                }
            } else {
                GrupoEstoquePK gepk = new GrupoEstoquePK(
                        new EstoqueEmpresa(new EstoqueEmpresaPK(this.dispensacaoMedicamentoItem.getProduto(), empresaDispensacaoMedicamento())),
                        GrupoEstoque.GRUPO_ESTOQUE_PADRAO,
                        empresaDispensacaoMedicamento().getEmpresaMaterial().getDeposito().getCodigo(), EstoqueHelper.getLocalizacaoEstruturaPadrao());

                if (!dispensacaoMedicamentoItem.getLstCodigoBarrasProduto().isEmpty()) {
                    for (CodigoBarrasProduto cbp : dispensacaoMedicamentoItem.getLstCodigoBarrasProduto()) {
                        cbp.setDispensacaoMedicamentoItem(this.dispensacaoMedicamentoItem);

                        cbp.setStatus(CodigoBarrasProduto.Status.DISPENSADO.value());
                        BOFactory.getBO(CadastroFacade.class).save(cbp);
                    }
                }
                DispensacaoItemLote dispensacaoItemLote = new DispensacaoItemLote();

//                GrupoEstoque ge = (GrupoEstoque) getSession().get(GrupoEstoque.class, gepk);
                GrupoEstoque ge = (GrupoEstoque) getSession().createCriteria(GrupoEstoque.class)
                        .add(Restrictions.eq(GrupoEstoque.PROP_ID, gepk))
                        .uniqueResult();

                dispensacaoItemLote.setDispensacaoMedicamentoItem(this.dispensacaoMedicamentoItem);
                dispensacaoItemLote.setGrupoEstoque(ge);
                dispensacaoItemLote.setQuantidade(this.dispensacaoMedicamentoItem.getCoalesceQuantidadeDispensada());

                BOFactory.getBO(CadastroFacade.class).save(dispensacaoItemLote);

            }

//            }
            /*---------------------------------------------------------------------*/

            /*
             * CONTROLE SOBRE LIBERAO DE RECEITAS
             * ------------------------------------
             * Se houver uma liberao vinculada a dispensao atual, ser ento realizado
             * sua devida atualizao.
             *---------------------------------------------------------------------*/
            Criteria criteria = this.getSession().createCriteria(LiberacaoReceita.class);
            criteria.add(Restrictions.eq(LiberacaoReceita.PROP_PRODUTO, this.dispensacaoMedicamentoItem.getProduto()));
            criteria.add(Restrictions.eq(LiberacaoReceita.PROP_USUARIO_CADSUS, this.dispensacaoMedicamentoItem.getDispensacaoMedicamento().getUsuarioCadsusDestino()));
            criteria.add(Restrictions.eq(LiberacaoReceita.PROP_STATUS, LiberacaoReceita.STATUS_ABERTO));
            criteria.add(Restrictions.eq(LiberacaoReceita.PROP_EMPRESA, empresaDispensacaoMedicamento()));

            LiberacaoReceita liberacaoReceita = (LiberacaoReceita) criteria.setMaxResults(1).uniqueResult();

            if (liberacaoReceita != null) {
                if (liberacaoReceita.getDispensacaoMedicamentoItem() != null) {
                    throw new ConcurrentDAOException();
                }
                liberacaoReceita.setDispensacaoMedicamentoItem(this.dispensacaoMedicamentoItem);
                liberacaoReceita.setQuantidade(this.dispensacaoMedicamentoItem.getQuantidadeDispensada());
                liberacaoReceita.setStatus(LiberacaoReceita.STATUS_LIBERADO);

                BOFactory.getBO(CadastroFacade.class).save(liberacaoReceita);
            }
            /*---------------------------------------------------------------------*/

            if (!this.dispensacaoMedicamentoItem.getRetornoValidacao().isValido()) {
                throw new ValidacaoException(this.dispensacaoMedicamentoItem.getRetornoValidacao());
            }
        }

        /**
         * ATUALIZA QUANTIDADE_MG_ML DO PRODUTO
         */
        if (this.dispensacaoMedicamentoItem.getProduto().isAtualizarProduto()) {
            Produto prod = (Produto) getSession().get(Produto.class, this.dispensacaoMedicamentoItem.getProduto().getCodigo());
            prod.setQtdadeMgMl(this.dispensacaoMedicamentoItem.getProduto().getQtdadeMgMl());
        }

        String validaSaldo = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("ValidaSaldoDispensacaoPrescricao");
        ReceituarioItemComponente receituarioItemComponente = this.dispensacaoMedicamentoItem.getReceituarioItemComponente();
        ReceituarioItem receituarioItem = this.dispensacaoMedicamentoItem.getReceituarioItem();

        if (receituarioItemComponente != null) {
            receituarioItemComponente = (ReceituarioItemComponente) getSession().get(ReceituarioItemComponente.class, receituarioItemComponente.getCodigo());
            receituarioItemComponente.setQuantidadeDispensada(Coalesce.asLong(receituarioItemComponente.getQuantidadeDispensada()) + dispensacaoMedicamentoItem.getQuantidadeDispensada().longValue());

            if (RepositoryComponentDefault.SIM.equals(validaSaldo)
                    && RepositoryComponentDefault.NAO.equals(dispensacaoMedicamentoItem.getProduto().getFlagPermiteDispensarMais())) {
                if (Coalesce.asLong(receituarioItemComponente.getQuantidadePrescrita()) < Coalesce.asLong(receituarioItemComponente.getQuantidadeDispensada())) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_saldo_dispensado_ultrapassou_receituario_prescrito"));
                }
            }

            BOFactory.getBO(CadastroFacade.class).save(receituarioItemComponente);
        }
        if (receituarioItem != null && DispensacaoMedicamentoItem.Tipo.ITEM.value().equals(Coalesce.asLong(dispensacaoMedicamentoItem.getTipo()))) {
            Produto produtoAuxiliar = receituarioItem.getProduto();
            receituarioItem = (ReceituarioItem) getSession().get(ReceituarioItem.class, receituarioItem.getCodigo());
            if (receituarioItem.getProduto() == null) {
                receituarioItem.setProduto(produtoAuxiliar);
            }
            receituarioItem.setDispensacaoMedicamentoItem(this.dispensacaoMedicamentoItem);
            if (TipoReceita.RECEITA_SOLICITACAO_MATERIAIS.equals(receituarioItem.getReceituario().getTipoReceita())) {
                receituarioItem.setQuantidadePrescrita(ReceituarioHelper.calcularQuantidadePrescrita(receituarioItem));
            }
            receituarioItem.setQuantidadeDispensada(dispensacaoMedicamentoItem.getQuantidadeDispensada().longValue());

            BOFactory.getBO(CadastroFacade.class).save(receituarioItem);
        }

        if (dispensacaoMedicamentoItem.getHistoricoKit() != null && DispensacaoMedicamentoItem.Tipo.KIT.value().equals(dispensacaoMedicamentoItem.getTipo())) {
            ReceituarioItemKit historicoKit = dispensacaoMedicamentoItem.getHistoricoKit();
            historicoKit.setQuantidadeDispensada(Coalesce.asDouble(dispensacaoMedicamentoItem.getQuantidadeDispensada()) + Coalesce.asDouble(historicoKit.getQuantidadeDispensada()));
            historicoKit.setProdutoDispensado(dispensacaoMedicamentoItem.getProduto());
            if (RepositoryComponentDefault.SIM.equals(validaSaldo)
                    && RepositoryComponentDefault.NAO.equals(dispensacaoMedicamentoItem.getProduto().getFlagPermiteDispensarMais())) {
                if (Coalesce.asDouble(historicoKit.getQuantidade()) < Coalesce.asDouble(historicoKit.getQuantidadeDispensada())) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_saldo_dispensado_ultrapassou_receituario_prescrito"));
                }
            }
            BOFactory.save(historicoKit);
        }
    }

    private Empresa empresaDispensacaoMedicamento(){
        Empresa empresa = this.dispensacaoMedicamentoItem.getDispensacaoMedicamento().getEmpresa();
        if(empresa != null){
            return empresa;
        }
        return getSessao().getEmpresa();
    }
}
