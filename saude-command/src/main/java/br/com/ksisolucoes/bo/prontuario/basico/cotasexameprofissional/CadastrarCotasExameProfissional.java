package br.com.ksisolucoes.bo.prontuario.basico.cotasexameprofissional;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.exame.interfaces.dto.ConsultaCotaExameProfissionalDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProfissional;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProfissionalEmpresa;

import java.util.List;

import static ch.lambdaj.Lambda.forEach;

/**
 * Created by sulivan on 22/02/19.
 */
public class CadastrarCotasExameProfissional extends AbstractCommandTransaction<CadastrarCotasExameProfissional> {

    private ExameProfissional exameProfissional;
    private List<ExameProfissionalEmpresa> empresas;
    private boolean gerarCotaMesAtual;

    public CadastrarCotasExameProfissional(ExameProfissional exameProfissional, boolean gerarCotaMesAtual, List<ExameProfissionalEmpresa> empresas) {
        this.exameProfissional = exameProfissional;
        this.gerarCotaMesAtual = gerarCotaMesAtual;
        this.empresas = empresas;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        exameProfissional = BOFactory.save(exameProfissional);

        if(gerarCotaMesAtual){
            ConsultaCotaExameProfissionalDTO dto = new ConsultaCotaExameProfissionalDTO();
            dto.setExameProfissional(exameProfissional);
            BOFactory.getBO(ExameFacade.class).gerarCotaProfissionalMesAtual(dto);
        }

        if (CollectionUtils.isNotNullEmpty(empresas)) {
            forEach(empresas).setExameProfissional(exameProfissional);
        }

        VOUtils.persistirListaVosModificados(ExameProfissionalEmpresa.class, empresas, new QueryCustom.QueryCustomParameter(ExameProfissionalEmpresa.PROP_EXAME_PROFISSIONAL, exameProfissional));
    }

    public ExameProfissional getExameProfissional() {
        return exameProfissional;
    }
}
