package br.com.ksisolucoes.bo.portal;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.portal.UsuarioPortal;
import org.hibernate.criterion.Restrictions;

/**
 * <AUTHOR>
 */
public class RegistrarNovaSenhaPortal extends AbstractCommandTransaction{

    private Long codigoUsuarioPortal;
    private String senha;
    private String codigoSenha;

    public RegistrarNovaSenhaPortal(Long codigoUsuarioPortal, String senha, String codigoSenha) {
        this.codigoUsuarioPortal = codigoUsuarioPortal;
        this.senha = senha;
        this.codigoSenha = codigoSenha;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        if(codigoUsuarioPortal == null){
            throw new ValidacaoException(Bundle.getStringApplication("contaNaoLocalizada"));
        }
        String senhaCriptografada = Util.criptografarSenha(senha);

        UsuarioPortal up = (UsuarioPortal) getSession().createCriteria(UsuarioPortal.class)
            .add(Restrictions.idEq(codigoUsuarioPortal))
            .add(Restrictions.eq(UsuarioPortal.PROP_CHAVE_NOVA_SENHA, codigoSenha))
            .createCriteria(UsuarioPortal.PROP_USUARIO)
            .uniqueResult();
                
        if(up == null){
            throw new ValidacaoException(Bundle.getStringApplication("contaNaoLocalizada"));
        }

        up.setChaveNovaSenha(null);
        BOFactory.save(up);
        
        up.getUsuario().setSenha(senhaCriptografada);
        BOFactory.save(up.getUsuario());
    }
}
