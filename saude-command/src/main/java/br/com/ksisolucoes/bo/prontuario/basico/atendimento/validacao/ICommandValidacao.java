package br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public interface ICommandValidacao extends Serializable{

    public ICommandValidacao startValidacao() throws DAOException, ValidacaoException;
    
    public ValidacaoProcessoGroup getValidacaoProcessoGroup();
    
}
