package br.com.ksisolucoes.bo.prontuario.basico.atendimentoanamnese;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAnamnese;

/**
 *
 * <AUTHOR>
 */
public class SaveAtendimentoAnamnese extends SaveVO {

        private AtendimentoAnamnese atendimentoAnamnese;

    public SaveAtendimentoAnamnese(Object vo) {
        super( vo );
        this.atendimentoAnamnese = (AtendimentoAnamnese) vo;
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if(this.atendimentoAnamnese.getDataAnamnese() == null){
            atendimentoAnamnese.setDataAnamnese(Data.getDataAtual());
        }
        if(this.atendimentoAnamnese.getUsuario() == null){
            this.atendimentoAnamnese.setUsuario((Usuario) sessao.getUsuario());
        }
    }

    public AtendimentoAnamnese getAtendimentoAnamnese() {
        return atendimentoAnamnese;
    }

}
