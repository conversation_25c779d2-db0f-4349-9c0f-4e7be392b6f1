/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.cadsus.usuariocadsus.restservice;

import br.com.celk.bo.service.rest.paciente.ConsultaDocumentoPacienteRestDTO;
import br.com.celk.bo.service.rest.paciente.ConsultaPacienteRestDTO;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import static ch.lambdaj.Lambda.on;
import java.util.List;
import java.util.Map;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class ConsultaPacienteRest extends CommandQuery<ConsultaPacienteRest> {

    private String cpf;
    private String cns;
    private List<ConsultaPacienteRestDTO> result;

    public ConsultaPacienteRest(String cpf, String cns) {
        this.cpf = cpf;
        this.cns = cns;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        ConsultaPacienteRestDTO proxy = on(ConsultaPacienteRestDTO.class);

        hql.addToSelect("uc.codigo", path(proxy.getCodigo()));
        hql.addToSelect("uc.nome", path(proxy.getNome()));
        hql.addToSelect("uc.apelido", path(proxy.getApelido()));
        hql.addToSelect("uc.nomeMae", path(proxy.getNomeMae()));
        hql.addToSelect("uc.nomePai", path(proxy.getNomePai()));
        hql.addToSelect("uc.cpf", path(proxy.getCpf()));
        hql.addToSelect("uc.rg", path(proxy.getRg()));
        hql.addToSelect("uc.dataCadastro", path(proxy.getDataCadastro()));
        hql.addToSelect("uc.dataNascimento", path(proxy.getDataNascimento()));
        hql.addToSelect("uc.sexo", path(proxy.getSexo()));
        hql.addToSelect("uc.telefone", path(proxy.getTelefone()));
        hql.addToSelect("uc.telefone2", path(proxy.getTelefone2()));
        hql.addToSelect("uc.telefone3", path(proxy.getTelefone3()));
        hql.addToSelect("uc.telefone4", path(proxy.getTelefone4()));
        hql.addToSelect("uc.celular", path(proxy.getCelular()));
        hql.addToSelect("uc.email", path(proxy.getEmail()));
        hql.addToSelect("uc.nomeConjuge", path(proxy.getNomeConjuge()));
        hql.addToSelect("e.codigoSus", path(proxy.getEtnia().getCodigoSus()));
        hql.addToSelect("e.descricao", path(proxy.getEtnia().getDescricao()));
        hql.addToSelect("cbo.descricao", path(proxy.getOcupacao()));

        hql.addToSelect("uc.cidadeNascimento.codigo", path(proxy.getCodigoCidadeNascimento()));

        hql.addToSelect("euc.codigo", path(proxy.getEnderecoUsuarioCadsus().getCodigo()));
        hql.addToSelect("euc.logradouro", path(proxy.getEnderecoUsuarioCadsus().getLogradouro()));
        hql.addToSelect("euc.complementoLogradouro", path(proxy.getEnderecoUsuarioCadsus().getComplementoLogradouro()));
        hql.addToSelect("euc.numeroLogradouro", path(proxy.getEnderecoUsuarioCadsus().getNumeroLogradouro()));
        hql.addToSelect("euc.bairro", path(proxy.getEnderecoUsuarioCadsus().getBairro()));
        hql.addToSelect("euc.cep", path(proxy.getEnderecoUsuarioCadsus().getCep()));
        hql.addToSelect("cidade.codigo", path(proxy.getEnderecoUsuarioCadsus().getCodigoCidade()));
        hql.addToSelect("tipoLogradouro.codigo", path(proxy.getEnderecoUsuarioCadsus().getCodigoTipoLogradouro()));

        hql.addToSelect("(select min(ucc.numeroCartao) from UsuarioCadsusCns ucc where ucc.usuarioCadsus = uc and excluido = " + RepositoryComponentDefault.NAO_EXCLUIDO + ")", path(proxy.getCns()));

        hql.addToSelect("ec.codigo", path(proxy.getEstadoCivil().getCodigo()));
        hql.addToSelect("ec.descricao", path(proxy.getEstadoCivil().getDescricao()));

        hql.addToSelect("raca.codigo", path(proxy.getRaca().getCodigo()));
        hql.addToSelect("raca.descricao", path(proxy.getRaca().getDescricao()));

        hql.addToSelect("pNasc.codigo", path(proxy.getPaisNascimento().getCodigo()));
        hql.addToSelect("pNasc.descricao", path(proxy.getPaisNascimento().getDescricao()));

        hql.addToSelect("esc.codigo", path(proxy.getCodEscolaridade()));
        hql.addToSelect("esc.descricao", path(proxy.getDescricaoEscolaridade()));

        hql.setTypeSelect(ConsultaPacienteRestDTO.class.getName());
        hql.addToFrom("UsuarioCadsus uc"
                + " left join uc.tabelaCbo cbo"
                + " left join uc.etniaIndigena e"
                + " left join uc.enderecoUsuarioCadsus euc"
                + " left join euc.cidade cidade"
                + " left join euc.tipoLogradouro tipoLogradouro"
                + " left join uc.raca raca"
                + " left join uc.estadoCivil ec "
                + " left join uc.paisNascimento pNasc"
                + " left join uc.escolaridade esc");

        hql.addToWhereWhithAnd("uc.situacao = ", UsuarioCadsus.SITUACAO_ATIVO);
        if (cpf != null) {
            hql.addToWhereWhithAnd("uc.cpf = ", cpf);
        }
        if (cns != null) {
            hql.addToWhereWhithAnd("(select min(ucc.numeroCartao) from UsuarioCadsusCns ucc where ucc.usuarioCadsus = uc and excluido = " + RepositoryComponentDefault.NAO_EXCLUIDO + ") = ", Long.parseLong(cns));
        }

        hql.addToOrder("uc.versionAll");
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(result)) {
            //Documentos Paciente
            QueryConsultaDocumentosPacienteRest queryDocumentos = new QueryConsultaDocumentosPacienteRest(result.get(0).getCodigo());
            queryDocumentos.start();
            List<ConsultaDocumentoPacienteRestDTO> documentosRestDTO = queryDocumentos.getResult();
            result.get(0).setLstConsultaDocumentoPacienteRestDTO(documentosRestDTO);
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    public List<ConsultaPacienteRestDTO> getResult() {
        return result;
    }
}
