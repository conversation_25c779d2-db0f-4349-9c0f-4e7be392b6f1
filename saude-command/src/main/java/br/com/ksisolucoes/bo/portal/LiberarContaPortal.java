package br.com.ksisolucoes.bo.portal;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.portal.UsuarioPortal;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class LiberarContaPortal extends AbstractCommandTransaction{

    Long codigoUsuarioPortal;
    String codigoLiberacao;

    public LiberarContaPortal(Long codigoUsuarioPortal, String codigoLiberacao) {
        this.codigoUsuarioPortal = codigoUsuarioPortal;
        this.codigoLiberacao = codigoLiberacao;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        if(codigoLiberacao == null || codigoUsuarioPortal == null){
            throw new ValidacaoException(Bundle.getStringApplication("codigoLiberacaoInvalido"));
        }
        
        UsuarioPortal up = (UsuarioPortal) getSession().createCriteria(UsuarioPortal.class)
            .add(Restrictions.idEq(codigoUsuarioPortal))
            .add(Restrictions.eq(UsuarioPortal.PROP_CHAVE_VERIFICACAO, codigoLiberacao))
            .uniqueResult();
        
        if(up == null){
            throw new ValidacaoException(Bundle.getStringApplication("codigoLiberacaoInvalido"));
        }
        
        up.setDataConfirmacao(DataUtil.getDataAtual());
        BOFactory.save(up);
    }
    
}
