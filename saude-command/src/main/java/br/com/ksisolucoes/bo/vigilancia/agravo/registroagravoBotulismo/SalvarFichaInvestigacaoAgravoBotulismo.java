package br.com.ksisolucoes.bo.vigilancia.agravo.registroagravoBotulismo;

import br.com.celk.util.CollectionUtils;
import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoBotulismoDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.agravo.helper.FichaInvestigacaoHelper;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoBotulismo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoBotulismoAlimentos;
import ch.lambdaj.Lambda;


public class SalvarFichaInvestigacaoAgravoBotulismo extends AbstractCommandTransaction {

    private FichaInvestigacaoAgravoBotulismoDTO fichaDto;

    public SalvarFichaInvestigacaoAgravoBotulismo(
            FichaInvestigacaoAgravoBotulismoDTO dto
    ) {
        this.fichaDto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        InvestigacaoAgravoBotulismo investigacaoAgravo = fichaDto.getInvestigacaoAgravoBotulismo();
        RegistroAgravo registroAgravo = FichaInvestigacaoHelper.getInstance().getRegistroAgravo(fichaDto.getRegistroAgravo().getCodigo());
        investigacaoAgravo.setRegistroAgravo(registroAgravo);
        Profissional profissional = FichaInvestigacaoHelper.getInstance().getProfissional(getSessao(), registroAgravo);
        Empresa empresa = FichaInvestigacaoHelper.getInstance().getEmpresa(getSessao());

        registroAgravo.setProfissionalInvestigacao(profissional);
        registroAgravo.setUnidadeProfissionalInvestigacao(empresa);
        registroAgravo.setDataPrimeirosSintomas(fichaDto.getRegistroAgravo().getDataPrimeirosSintomas());
        registroAgravo.setStatus(RegistroAgravo.Status.EM_INVESTIGACAO.value());
        registroAgravo = FichaInvestigacaoHelper.getInstance().getStatusEncerramentoFicha(fichaDto.isEncerrarFicha(),registroAgravo);

        if (!isTemFichas(investigacaoAgravo, registroAgravo)) {
            InvestigacaoAgravoBotulismo agravoBotulismo = BOFactory.save(investigacaoAgravo);
            BOFactory.save(registroAgravo);
            salvarBotulismoAlimentosList(fichaDto, agravoBotulismo);
        } else {
            throw new ValidacaoException(Bundle.getStringApplication("msg_ja_existe_um_registro_cadastrado_com_o_mesmo_registro_agravo"));
        }
    }

    private void salvarBotulismoAlimentosList(FichaInvestigacaoAgravoBotulismoDTO fichaInvestigacaoAgravoBotulismoDTO, InvestigacaoAgravoBotulismo investigacaoAgravo) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(fichaInvestigacaoAgravoBotulismoDTO.getInvestigacaoAgravoBotulismoAlimentosList())) {
            Lambda.forEach(fichaInvestigacaoAgravoBotulismoDTO.getInvestigacaoAgravoBotulismoAlimentosList()).setInvestigacaoAgravoBotulismo(investigacaoAgravo);
        }

        VOUtils.persistirListaVosModificados(InvestigacaoAgravoBotulismoAlimentos.class, fichaInvestigacaoAgravoBotulismoDTO.getInvestigacaoAgravoBotulismoAlimentosList(),
                new QueryCustom.QueryCustomParameter(InvestigacaoAgravoBotulismoAlimentos.PROP_INVESTIGACAO_AGRAVO_BOTULISMO, investigacaoAgravo));
    }

    private boolean isTemFichas(InvestigacaoAgravoBotulismo investigacaoAgravo, RegistroAgravo registroAgravo) {
        LoadManager loadManager = LoadManager.getInstance(InvestigacaoAgravoBotulismo.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(InvestigacaoAgravoBotulismo.PROP_REGISTRO_AGRAVO, RegistroAgravo.PROP_CODIGO), registroAgravo.getCodigo()));
        if (investigacaoAgravo.getCodigo() != null) {
            loadManager.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(InvestigacaoAgravoBotulismo.PROP_CODIGO), BuilderQueryCustom.QueryParameter.DIFERENTE, investigacaoAgravo.getCodigo()));
        }
        return loadManager.start().exists();
    }
}
