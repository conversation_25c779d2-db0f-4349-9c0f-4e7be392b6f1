package br.com.ksisolucoes.bo.vigilancia.roteiroinspecao.registroinspecaoroteiroitemperguntaresposta;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecaoRoteiroItemPerguntaResposta;

/**
 *
 * <AUTHOR>
 */
public class SaveRegistroInspecaoRoteiroItemPerguntaResposta extends SaveVO<RegistroInspecaoRoteiroItemPerguntaResposta> {

    public SaveRegistroInspecaoRoteiroItemPerguntaResposta(RegistroInspecaoRoteiroItemPerguntaResposta vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getDataUsuario() == null)
            this.vo.setDataUsuario(DataUtil.getDataAtual());

        if (this.vo.getUsuario() == null)
            this.vo.setUsuario(SessaoAplicacaoImp.getInstance().getUsuario());

        if (this.vo.getRegistroInspecaoRoteiroItemPerguntaRespostaOrigem() == null) {
            this.vo.setRegistroInspecaoRoteiroItemPerguntaRespostaOrigem(this.vo);
        }

        if (this.vo.getResposta() == null) {
            this.vo.setResposta(RegistroInspecaoRoteiroItemPerguntaResposta.Resposta.SIM.value());
        }
    }

}
