package br.com.ksisolucoes.bo.prontuario.basico.exame;

import br.com.ksisolucoes.agendamento.exame.dto.ExameCadastroAprovacaoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR> <PERSON>
 */
public class CadastrarRequisicaoCargaViralHIVRNALacen extends AbstractCommandTransaction {

    private ExameCadastroAprovacaoDTO param;
    private RequisicaoCargaViralHIVRNALacen requisicaoCargaViralHIVRNALacen;
    private Long codigoExameCadastrado;

    public CadastrarRequisicaoCargaViralHIVRNALacen(ExameCadastroAprovacaoDTO param, RequisicaoCargaViralHIVRNALacen requisicaoCargaViralHIVRNALacen) {
        this.param = param;
        this.requisicaoCargaViralHIVRNALacen = requisicaoCargaViralHIVRNALacen;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        param.setTipoConvenio(TipoExame.CONVENIO_SUS);
        param.setGeraAtendimentoProntuario(Boolean.TRUE);

        codigoExameCadastrado = BOFactory.getBO(ExameFacade.class).salvarExame(param);

        ExameRequisicao exameRequisicao = (ExameRequisicao) getSession().createCriteria(ExameRequisicao.class)
            .add(Restrictions.eq(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_CODIGO), codigoExameCadastrado))
            .add(Restrictions.eq(ExameRequisicao.PROP_EXAME_PROCEDIMENTO, param.getExameProcedimentoDTOs().get(0).getExameProcedimento()))
            .uniqueResult();

        requisicaoCargaViralHIVRNALacen.setExameRequisicao(exameRequisicao);
        BOFactory.save(requisicaoCargaViralHIVRNALacen);
    }
    
    public Long getCodigoExameCadastrado() {
        return codigoExameCadastrado;
    }
}