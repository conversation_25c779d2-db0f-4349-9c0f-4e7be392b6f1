/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.entradas.estoque.pedidotransferencia;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryConsultaProdutoUnidadePedidoTransferenciaDTO;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryConsultaProdutoUnidadePedidoTransferenciaDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaProdutoUnidadePedidoTransferencia extends CommandQuery<QueryConsultaProdutoUnidadePedidoTransferencia> {

    private boolean controlaVencimentoLote;
    private Date dataAtual;

    private QueryConsultaProdutoUnidadePedidoTransferenciaDTOParam param;
    private List<QueryConsultaProdutoUnidadePedidoTransferenciaDTO> result;

    public QueryConsultaProdutoUnidadePedidoTransferencia(QueryConsultaProdutoUnidadePedidoTransferenciaDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {

        controlaVencimentoLote = this.param.getDeposito() == null || !this.param.getDeposito().equals(SessaoAplicacaoImp.getInstance().<Empresa>getEmpresa().getEmpresaMaterial().getDepositoVencido());
        dataAtual = Data.getDataAtual();

        hql.addToGroup("empresa.codigo");

        hql.addToSelectAndGroup("produto.codigo","produto.codigo");
        hql.addToSelectAndGroup("produto.referencia","produto.referencia");
        hql.addToSelectAndGroup("produto.descricao","produto.descricao");
        
        hql.addToSelectAndGroup("unidade.codigo","produto.unidade.codigo");
        hql.addToSelectAndGroup("unidade.unidade","produto.unidade.unidade");

        hql.addToSelectAndGroup("subGrupo.id.codigo","produto.subGrupo.id.codigo");
        hql.addToSelectAndGroup("subGrupo.descricao","produto.subGrupo.descricao");
        hql.addToSelectAndGroup("subGrupo.flagControlaGrupoEstoque","produto.subGrupo.flagControlaGrupoEstoque");
        hql.addToSelectAndGroup("grupoProduto.codigo","produto.subGrupo.id.codigoGrupoProduto");
        hql.addToSelectAndGroup("grupoProduto.codigo","produto.subGrupo.roGrupoProduto.codigo");
        hql.addToSelectAndGroup("grupoProduto.descricao","produto.subGrupo.roGrupoProduto.descricao");

        hql.addToSelectAndGroup("estoqueEmpresa.quantidadePadraoDispensacao","quantidadePadraoDispensacao");
        hql.addToSelectAndGroup("estoqueEmpresa.estoqueMinimo","quantidadeConsumo");

        hql.addToSelect("sum(coalesce(grupoEstoque.estoqueFisico,0))","estoqueFisico");


        {
            HQLHelper hqlSub = getSubHqlHelperGrupoEstoqueSessao(hql.getNewInstanceSubQuery());
            hqlSub.addToSelect("sum(grupoEstoqueES.estoqueFisico)");
            hql.addToSelect("("+hqlSub.getQuery()+")","estoqueFisicoEmpresaSessao");
        }
        {
            HQLHelper hqlSub = getSubHqlHelperGrupoEstoqueSessao(hql.getNewInstanceSubQuery());
            hqlSub.addToSelect("sum(grupoEstoqueES.estoqueEncomendado)");
            hql.addToSelect("("+hqlSub.getQuery()+")","estoqueEncomendadoEmpresaSessao");
        }
        {
            HQLHelper hqlSub = getSubHqlHelperGrupoEstoqueSessao(hql.getNewInstanceSubQuery());
            hqlSub.addToSelect("sum(grupoEstoqueES.estoqueReservado)");
            hql.addToSelect("("+hqlSub.getQuery()+")","estoqueReservadoEmpresaSessao");
        }
        

        hql.setTypeSelect(QueryConsultaProdutoUnidadePedidoTransferenciaDTO.class.getName());
        hql.addToFrom("GrupoEstoque grupoEstoque "
                + " right join grupoEstoque.id.estoqueEmpresa estoqueEmpresa "
                + " left join estoqueEmpresa.id.produto produto "
                + " left join estoqueEmpresa.id.empresa empresa "
                + " left join produto.subGrupo subGrupo"
                + " left join subGrupo.roGrupoProduto grupoProduto"
                + " left join grupoEstoque.roDeposito deposito "
                + " left join produto.unidade unidade"
                );
        
        hql.addToWhereWhithAnd("empresa = ",this.param.getUnidade());

        if (this.param.getDeposito() != null) {
            hql.addToWhereWhithAnd("coalesce(deposito.codigo,"+this.param.getDeposito().getCodigo()+") = ",this.param.getDeposito().getCodigo());
        }

        hql.addToWhereWhithAnd("produto not in",this.param.getProdutosExclusao());

        hql.addToWhereWhithAnd("estoqueEmpresa.flagAtivo = ", RepositoryComponentDefault.SIM);


        if (this.param.isEstoqueFisicoValido()) {
            HQLHelper hqlSub = getSubHqlHelperGrupoEstoqueSessao(hql.getNewInstanceSubQuery());
            hqlSub.addToSelect("sum(grupoEstoqueES.estoqueFisico)");
            hql.addToWhereWhithAnd("("+hqlSub.getQuery()+") > 0");
        }

        hql.addToOrder("produto.codigo");

    }

    private HQLHelper getSubHqlHelperGrupoEstoqueSessao(HQLHelper hqlSub){
        hqlSub.addToFrom("GrupoEstoque grupoEstoqueES "
                + " left join grupoEstoqueES.id.estoqueEmpresa estoqueEmpresaES "
                + " left join estoqueEmpresaES.id.produto produtoES "
                + " left join estoqueEmpresaES.id.empresa empresaES "
                + " left join grupoEstoqueES.roDeposito depositoES "
                );
        hqlSub.addToWhereWhithAnd("empresaES = ",SessaoAplicacaoImp.getInstance().<Empresa>getEmpresa());
        hqlSub.addToWhereWhithAnd("produto = produtoES");
        hqlSub.addToWhereWhithAnd("depositoES = ",this.param.getDeposito());
        if (controlaVencimentoLote ) {
            hqlSub.addToWhereWhithAnd("coalesce(grupoEstoqueES.dataValidade,:dataAtual) >= ", this.dataAtual);
        }

        return hqlSub;
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        if (controlaVencimentoLote ) {
            query.setDate("dataAtual", this.dataAtual);
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<QueryConsultaProdutoUnidadePedidoTransferenciaDTO> getResult() {
        return result;
    }

}
