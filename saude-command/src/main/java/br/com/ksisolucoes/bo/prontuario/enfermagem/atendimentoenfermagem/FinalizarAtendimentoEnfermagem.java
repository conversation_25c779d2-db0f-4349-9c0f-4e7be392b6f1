package br.com.ksisolucoes.bo.prontuario.enfermagem.atendimentoenfermagem;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryGroup;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomGroup;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.AtendimentoHelper;
import br.com.ksisolucoes.bo.prontuario.basico.atendimentomedico.FinalizarAtendimento;
import br.com.ksisolucoes.bo.prontuario.basico.cid.QueryPagerCid;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.QueryPagerCidDTOParam;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingImpl;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.system.consulta.ConsultaQuerySupportImpl;
import br.com.ksisolucoes.system.consulta.IConsultaQuerySupport;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.ProfissionalCargaHoraria;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoPrimario;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoTransferenciaLeito;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoTransferenciaSetor;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimentoCboProcedimento;
import br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagem;
import br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoHistorico;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import java.util.List;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class FinalizarAtendimentoEnfermagem extends AbstractCommandTransaction {

    private AtendimentoEnfermagem atendimentoEnfermagem;

    public FinalizarAtendimentoEnfermagem(AtendimentoEnfermagem prontuarioEnfermagem) {
        this.atendimentoEnfermagem = prontuarioEnfermagem;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        BOFactory.getBO(CadastroFacade.class).save(atendimentoEnfermagem);

        Atendimento atendimento = (Atendimento) getSession().get(Atendimento.class, atendimentoEnfermagem.getAtendimento().getCodigo());
        atendimentoEnfermagem.setAtendimento(atendimento);
        /**
         * GERAR PROXIMO ATENDIMENTO CASO NECESSARIO
         */
        boolean gerarProximo = false;
        AtendimentoTransferenciaSetor ats = LoadManager.getInstance(AtendimentoTransferenciaSetor.class)
            .addParameter(new QueryCustomParameter(VOUtils.montarPath(AtendimentoTransferenciaSetor.PROP_ATENDIMENTO), atendimento))
            .start().getVO();
        if(ats != null){
            gerarProximo = true;
        }

        if (gerarProximo || atendimentoEnfermagem.getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getTipoAtendimentoProximo() != null) {
            Atendimento newAtendimento = BOFactory.getBO(AtendimentoFacade.class).inserirAtendimento(atendimentoEnfermagem.getAtendimento());
            newAtendimento.setProfissional(atendimentoEnfermagem.getAtendimento().getProfissionalResponsavel());
            AtendimentoPrimario atendimentoPrimario = (AtendimentoPrimario) this.getSession().createCriteria(AtendimentoPrimario.class)
                    .add(Restrictions.eq(AtendimentoPrimario.PROP_ATENDIMENTO, atendimentoEnfermagem.getAtendimento()))
                    .uniqueResult();
            if (atendimentoPrimario != null) {
                newAtendimento.setPrioridade(atendimentoPrimario.getPrioridade());
            }
            
            if(TipoAtendimento.TiposAtendimento.TRANSFERENCIA_INTERNACAO.value().equals(atendimentoEnfermagem.getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getTipoAtendimento())){
                AtendimentoTransferenciaLeito atendimentoTransferenciaLeito = LoadManager.getInstance(AtendimentoTransferenciaLeito.class)
                    .addParameter(new QueryCustomParameter(VOUtils.montarPath(AtendimentoTransferenciaLeito.PROP_ATENDIMENTO, Atendimento.PROP_CODIGO), atendimentoEnfermagem.getAtendimento().getCodigo()))
                    .start().getVO();
                if(atendimentoTransferenciaLeito == null){
                    throw new ValidacaoException(Bundle.getStringApplication("msg_informe_leito_transferencia"));
                }else{
                    LeitoQuarto leitoQuarto = BOFactory.getBO(AtendimentoFacade.class).ocuparLeito(atendimentoTransferenciaLeito.getLeitoQuarto().getCodigo(), newAtendimento.getCodigo());

                    newAtendimento.setLeitoQuarto(leitoQuarto);
                    newAtendimento.setEmpresa(leitoQuarto.getQuartoInternacao().getEmpresa());
                    newAtendimento.setDataChegada(Data.getDataAtual());
                    newAtendimento.setConvenio(atendimentoTransferenciaLeito.getConvenio());
                }
            }
            BOFactory.save(newAtendimento);
            
//           newAtendimento.setProfissionalResponsavel(atendimentoEnfermagem.getAtendimento().getProfissionalResponsavel());
            atendimentoEnfermagem.getAtendimento().setAtendimentoProximo(newAtendimento);
        }

        if (atendimentoEnfermagem.getAtendimento().getClassificacaoAtendimento() != null
                && atendimentoEnfermagem.getAtendimento().getClassificacaoAtendimento().getProcedimento() != null) {
            ProcedimentoCompetencia pc = new AtendimentoHelper().validaProcedimentoCompetencia(atendimentoEnfermagem.getAtendimento().getClassificacaoAtendimento().getProcedimento());
            atendimentoEnfermagem.getAtendimento().setProcedimentoCompetencia(pc);
        } else {
            List<ProfissionalCargaHoraria> pchList = LoadManager.getInstance(ProfissionalCargaHoraria.class)
                    .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProfissionalCargaHoraria.PROP_EMPRESA), atendimento.getEmpresa()))
                    .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProfissionalCargaHoraria.PROP_PROFISSIONAL), atendimentoEnfermagem.getAtendimento().getProfissional()))
                    .start().getList();
            TipoAtendimentoCboProcedimento tacp = null;
            for (ProfissionalCargaHoraria pch : pchList) {
                tacp = LoadManager.getInstance(TipoAtendimentoCboProcedimento.class)
                        .addParameter(new QueryCustomParameter(VOUtils.montarPath(TipoAtendimentoCboProcedimento.PROP_TIPO_ATENDIMENTO), atendimentoEnfermagem.getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento()))
                        .addParameter(new QueryCustomParameter(VOUtils.montarPath(TipoAtendimentoCboProcedimento.PROP_TABELA_CBO), pch.getTabelaCbo()))
                        .start().getVO();
                if (tacp != null) {
                    break;
                }
            }
            if (tacp != null) {
                ProcedimentoCompetencia pc = new AtendimentoHelper().validaProcedimentoCompetencia(tacp.getProcedimento());
                atendimentoEnfermagem.getAtendimento().setProcedimentoCompetencia(pc);
            }
        }

        RetornoValidacao retornoValidacao = new RetornoValidacao();
        if (!RepositoryComponentDefault.NAO.equals(atendimentoEnfermagem.getAtendimento().getProcedimentoCompetencia().getId().getProcedimento().getFlagFaturavel())) {
            if (!TipoAtendimento.TiposAtendimento.FECHAMENTO_PEQUENA_CIRURGIA.value().equals(atendimentoEnfermagem.getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getTipoAtendimento())
                    && !TipoAtendimento.TiposAtendimento.PRIMARIO.value().equals(atendimentoEnfermagem.getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getTipoAtendimento())) {
                Long proximos = LoadManager.getInstance(Atendimento.class)
                        .addGroup(new QueryCustomGroup(Atendimento.PROP_DATA_ALTA, QueryGroup.COUNT))
                        .addParameter(new QueryCustomParameter(VOUtils.montarPath(Atendimento.PROP_ATENDIMENTO_ORIGEM), atendimentoEnfermagem.getAtendimento()))
                        .start().getVO();

                if (proximos != null && proximos == 0L) {

                    if (atendimentoEnfermagem.getAtendimento().getClassificacaoAtendimento() == null) {
                        retornoValidacao.add("TIPO_ATENDIMENTO", Bundle.getStringApplication("msg_informe_tipo_atendimento"), null);
                    }
                }
            }
            if (atendimentoEnfermagem.getAtendimento().getCidPrincipal() != null) {
                QueryPagerCidDTOParam param = new QueryPagerCidDTOParam();
                param.setCid(atendimentoEnfermagem.getAtendimento().getCidPrincipal().getCodigo());
                param.setOrdenacao(ReportProperties.ORDENAR_CODIGO);

                QueryPagerCid qpc = new QueryPagerCid(param);

                DataPaging dataPaging = new DataPagingImpl(DataPaging.Type.ALVO_LIST);
                dataPaging.putClientProperty(Atendimento.REF, atendimentoEnfermagem.getAtendimento());
                dataPaging.putClientProperty("validarPrincipal", true);
                dataPaging.putClientProperty(IConsultaQuerySupport.class.getName(), new ConsultaQuerySupportImpl(Cid.class, null, null, null));

                qpc.setDataPaging(dataPaging);

                if (!CollectionUtils.isNotNullEmpty(qpc.start().getDataPagingResult().getList())) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_cid_incompativel_procedimentos"));
                }
            }
        }

        BOFactory.getBO(AtendimentoFacade.class).save(atendimentoEnfermagem.getAtendimento());
        new FinalizarAtendimento(atendimentoEnfermagem.getAtendimento().getCodigo()).start();

        /*Adicionar atendimento historico*/
        AtendimentoHistorico atendimentoHistorico = new AtendimentoHistorico();
        atendimentoHistorico.setAtendimento(atendimento);
        atendimentoHistorico.setDataHistorico(Data.getDataAtual());
        atendimentoHistorico.setUsuario((Usuario) sessao.getUsuario());
        atendimentoHistorico.setProfissional((Profissional) atendimentoEnfermagem.getAtendimento().getProfissional());
        atendimentoHistorico.setDescricaoHistorico(Bundle.getStringApplication("rotulo_finalzar_atendimento_enfermagem"));

        BOFactory.getBO(CadastroFacade.class).save(atendimentoHistorico);

        new AtendimentoHelper().validaGeracaoEncaminhamento(this.atendimentoEnfermagem.getAtendimento());

        BOFactory.getBO(AtendimentoFacade.class).atualizarAtendimentosCiclo(this.atendimentoEnfermagem.getAtendimento().getCodigo());

        if (!retornoValidacao.isValido()) {
            throw new ValidacaoException(retornoValidacao);
        }

    }
}
