package br.com.ksisolucoes.bo.vigilancia.denuncia.denunciaocorrencia;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia;
import br.com.ksisolucoes.vo.vigilancia.denuncia.DenunciaOcorrencia;

/**
 *
 * <AUTHOR>
 */
public class SalvarDenunciaOcorrencia extends AbstractCommandTransaction {

    private final DenunciaOcorrencia denunciaOcorrencia;
    private final boolean concluirRegistroAgravo;

    public SalvarDenunciaOcorrencia(DenunciaOcorrencia denunciaOcorrencia, boolean concluirDenunciaOcorrencia) {
        this.denunciaOcorrencia = denunciaOcorrencia;
        this.concluirRegistroAgravo = concluirDenunciaOcorrencia;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if(Denuncia.Status.PENDENTE.value().equals(denunciaOcorrencia.getDenuncia().getStatus())){
            denunciaOcorrencia.getDenuncia().setStatus(RegistroAgravo.Status.MONITORAMENTO.value());
            BOFactory.save(denunciaOcorrencia.getDenuncia());
        } else if(concluirRegistroAgravo){
            denunciaOcorrencia.getDenuncia().setStatus(RegistroAgravo.Status.CONCLUIDO.value());
            BOFactory.save(denunciaOcorrencia.getDenuncia());
        }

        BOFactory.save(denunciaOcorrencia);
    }
}
