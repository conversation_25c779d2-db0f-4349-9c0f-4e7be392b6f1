package br.com.ksisolucoes.bo.vigilancia.agravo.registroAgravoBrucelose;

import br.com.celk.util.CollectionUtils;
import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoBruceloseDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.agravo.helper.FichaInvestigacaoHelper;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoBrucelose;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoBruceloseExame;
import ch.lambdaj.Lambda;

/**
 * <AUTHOR> Lucas
 */


public class SalvarFichaInvestigacaoAgravoBrucelose extends AbstractCommandTransaction {

    private FichaInvestigacaoAgravoBruceloseDTO fichaDto;

    public SalvarFichaInvestigacaoAgravoBrucelose(FichaInvestigacaoAgravoBruceloseDTO dto) {
        this.fichaDto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Empresa empresa = FichaInvestigacaoHelper.getInstance().getEmpresa(getSessao());
        RegistroAgravo registroAgravo = FichaInvestigacaoHelper.getInstance().getRegistroAgravo(fichaDto.getRegistroAgravo().getCodigo());
        Profissional profissional = FichaInvestigacaoHelper.getInstance().getProfissional (getSessao(),registroAgravo);

        registroAgravo.setProfissionalInvestigacao(profissional);
        registroAgravo.setUnidadeProfissionalInvestigacao(empresa);
        registroAgravo.setEscolaridade(registroAgravo.getUsuarioCadsus().getNivelEscolaridade());
        registroAgravo.setEndereco(registroAgravo.getUsuarioCadsus().getEnderecoUsuarioCadsus());
        registroAgravo.setDataPrimeirosSintomas(fichaDto.getRegistroAgravo().getDataPrimeirosSintomas());
        registroAgravo.setStatus(RegistroAgravo.Status.EM_INVESTIGACAO.value());
        registroAgravo = FichaInvestigacaoHelper.getInstance().getStatusEncerramentoFicha(fichaDto.isEncerrarFicha(),registroAgravo);

        if (RegistroAgravo.Status.EM_INVESTIGACAO.value().equals(registroAgravo.getStatus())) {
            registroAgravo.setDataEncerramento(null);
        }

        InvestigacaoAgravoBrucelose investigacaoAgravo = fichaDto.getInvestigacaoAgravoBrucelose();
        investigacaoAgravo.setRegistroAgravo(registroAgravo);
        if (!isTemFichas(investigacaoAgravo.getCodigo())) {
            InvestigacaoAgravoBrucelose investigacaoAgravoBrucelose = BOFactory.save(investigacaoAgravo);
            BOFactory.save(registroAgravo);
            salvarExames(fichaDto, investigacaoAgravoBrucelose);
        } else {
            throw new ValidacaoException(Bundle.getStringApplication("msg_ja_existe_um_registro_cadastrado_com_o_mesmo_registro_agravo"));
        }

    }

    private void salvarExames(FichaInvestigacaoAgravoBruceloseDTO fichaInvestigacaoAgravoBruceloseDTO, InvestigacaoAgravoBrucelose investigacaoAgravo) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(fichaInvestigacaoAgravoBruceloseDTO.getInvestigacaoAgravoBruceloseExames())) {
            Lambda.forEach(fichaInvestigacaoAgravoBruceloseDTO.getInvestigacaoAgravoBruceloseExames()).setInvestigacaoAgravoBrucelose(investigacaoAgravo);
        }

        VOUtils.persistirListaVosModificados(InvestigacaoAgravoBruceloseExame.class, fichaInvestigacaoAgravoBruceloseDTO.getInvestigacaoAgravoBruceloseExames(),
                new QueryCustom.QueryCustomParameter(InvestigacaoAgravoBruceloseExame.PROP_INVESTIGACAO_AGRAVO_BRUCELOSE, investigacaoAgravo));

    }

    private boolean isTemFichas(Long idInvestigacaoAgravo) {
        LoadManager loadManager = LoadManager.getInstance(InvestigacaoAgravoBrucelose.class)
                .addParameter(new QueryCustom.QueryCustomParameter(
                        VOUtils.montarPath(InvestigacaoAgravoBrucelose.PROP_REGISTRO_AGRAVO,
                        RegistroAgravo.PROP_CODIGO), fichaDto.getRegistroAgravo().getCodigo()));
        if (idInvestigacaoAgravo != null) {
            loadManager.addParameter(new QueryCustom.QueryCustomParameter(
                    VOUtils.montarPath(InvestigacaoAgravoBrucelose.PROP_CODIGO),
                    BuilderQueryCustom.QueryParameter.DIFERENTE, idInvestigacaoAgravo));
        }
        return loadManager.start().exists();
    }

}
