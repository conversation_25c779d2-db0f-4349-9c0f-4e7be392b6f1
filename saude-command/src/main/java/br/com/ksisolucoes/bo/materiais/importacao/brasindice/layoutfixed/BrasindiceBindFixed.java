package br.com.ksisolucoes.bo.materiais.importacao.brasindice.layoutfixed;

import br.com.ksisolucoes.bo.materiais.importacao.brasindice.dto.ProdutoBrasindiceDTO;
import br.com.ksisolucoes.bo.materiais.importacao.brasindice.layout.BrasindiceVoBind;
import br.com.ksisolucoes.vo.entradas.estoque.ProdutoBrasindice;
import java.io.Serializable;
import org.apache.camel.dataformat.bindy.annotation.DataField;
import org.apache.camel.dataformat.bindy.annotation.FixedLengthRecord;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
@FixedLengthRecord(length = 361, crlf = "UNIX")
public class BrasindiceBindFixed implements BrasindiceVoBind<ProdutoBrasindiceDTO>, Serializable {

    @DataField(pos = 1, length = 3, required = true)
    private String codigoLaboratorio;

    @DataField(pos = 2, length = 40, required = true, trim = true)
    private String nomeLaboratorio;

    @DataField(pos = 3, length = 5, required = true)
    private String codigoMedicamento;

    @DataField(pos = 4, length = 80, required = true, trim = true)
    private String nomeMedicamento;

    @DataField(pos = 5, length = 4, required = true)
    private String codigoApresentacao;

    @DataField(pos = 6, length = 150, required = true, trim = true)
    private String nomeApresentacao;

    @DataField(pos = 7, length = 15, required = true, precision = 2, impliedDecimalSeparator = true)
    private Double ultimoPreco;

    @DataField(pos = 8, length = 4, required = true)
    private String quantidadeFracionamento;

    @DataField(pos = 9, length = 3, required = true)
    private String flagTipoPreco;

    @DataField(pos = 10, length = 15, required = true)
    private String precoMedicamentoFracionado;

    @DataField(pos = 11, length = 5, required = true)
    private String ultimaVersao;

    @DataField(pos = 12, length = 5, required = true)
    private String ipiMedicamento;

    @DataField(pos = 13, length = 1, required = true)
    private String flagPortaria;

    @DataField(pos = 14, length = 13, required = false)
    private String codigoBarrasEan;

    @DataField(pos = 15, length = 10, required = true)
    private String tiss;

    @DataField(pos = 16, length = 8, required = true)
    private String tuss;

    @Override
    public Class getVoClass() {
        return ProdutoBrasindiceDTO.class;
    }

    @Override
    public ProdutoBrasindiceDTO vosToSave(ProdutoBrasindiceDTO dto, Session session) {

        dto.getProdutoBrasindice().setLaboratorio(codigoLaboratorio);
        dto.getProdutoBrasindice().setDescricaoLaboratorio(nomeLaboratorio);
        dto.getProdutoBrasindice().setMedicamento(codigoMedicamento);
        dto.getProdutoBrasindice().setDescricaoMedicamento(nomeMedicamento);
        dto.getProdutoBrasindice().setApresentacao(codigoApresentacao);
        dto.getProdutoBrasindice().setDescricaoApresentacao(nomeApresentacao);
        dto.getProdutoBrasindice().setCodigoTiss(tiss);
        dto.getProdutoBrasindice().setCodigoTuss(tuss);

        dto.getProdutoBrasindiceItem().setPreco(ultimoPreco);
        dto.getProdutoBrasindiceItem().setTipoPreco(flagTipoPreco);
        dto.getProdutoBrasindiceItem().setVersaoBrasindice(Long.parseLong(StringUtils.trimToNull(ultimaVersao)));

        dto.getProdutoBrasindiceItem().setProdutoBrasindice(dto.getProdutoBrasindice());

        return dto;
    }

    @Override
    public String getFlagTipoPreco() {
        return flagTipoPreco;
    }

    @Override
    public void setRestrictions(Criteria criteria) {
        criteria.add(Restrictions.eq(ProdutoBrasindice.PROP_LABORATORIO, codigoLaboratorio));
        criteria.add(Restrictions.eq(ProdutoBrasindice.PROP_MEDICAMENTO, codigoMedicamento));
        criteria.add(Restrictions.eq(ProdutoBrasindice.PROP_APRESENTACAO, codigoApresentacao));
    }
}
