package br.com.ksisolucoes.bo.prontuario.basico.cotasexameprofissional;

import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.agendamento.exame.CotasExamesHelper;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.exame.interfaces.dto.ConsultaCotaExameProfissionalDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.exame.ExameProfissionalCompetencia;
import br.com.ksisolucoes.vo.exame.ExameProfissionalSemana;
import br.com.ksisolucoes.vo.prontuario.basico.ExameCotaPpi;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProfissional;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProfissionalEmpresa;
import org.hibernate.sql.JoinType;

import java.util.Date;

/**
 * Created by sulivan on 22/02/19.
 */
public class GerarCotaProfissionalMesAtual extends AbstractCommandTransaction<GerarCotaProfissionalMesAtual> {

    private ConsultaCotaExameProfissionalDTO dto;

    public GerarCotaProfissionalMesAtual(ConsultaCotaExameProfissionalDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (dto.getExameProfissional().getTipoExame() == null || dto.getExameProfissional().getTipoExame().getCodigo() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_possivel_gerar_cota_para_este_profissional_pois_tipo_exame_nao_foi_definido"));
        }

        Date competenciaAtual = Data.getDataParaPrimeiroDiaMes(DataUtil.getDataAtual());
        Double tetoFinanceiro;

        ExameProfissionalCompetencia epc = (ExameProfissionalCompetencia) getSession().createCriteria(ExameProfissionalCompetencia.class)
                .add(Restrictions.eq(ExameProfissionalCompetencia.PROP_DATA_COMPETENCIA, competenciaAtual))
                .add(Restrictions.eq(ExameProfissionalCompetencia.PROP_EMPRESA, dto.getExameProfissional().getEmpresa()))
                .add(Restrictions.eq(ExameProfissionalCompetencia.PROP_PROFISSIONAL, dto.getExameProfissional().getProfissional()))
                .add(Restrictions.eq(ExameProfissionalCompetencia.PROP_TIPO_EXAME, dto.getExameProfissional().getTipoExame()))
                .uniqueResult();

        if(epc != null){
            epc = HibernateUtil.lockTable(ExameProfissionalCompetencia.class, epc.getCodigo());
        } else {
            epc = new ExameProfissionalCompetencia();
            epc.setDataCompetencia(competenciaAtual);

            ExameProfissionalEmpresa exameProfissionalEmpresa = (ExameProfissionalEmpresa) getSession().createCriteria(ExameProfissionalEmpresa.class)
                    .add(Restrictions.eq(ExameProfissionalEmpresa.PROP_EMPRESA, dto.getExameProfissional().getEmpresa()))
                    .createCriteria(ExameProfissionalEmpresa.PROP_EXAME_PROFISSIONAL, JoinType.LEFT_OUTER_JOIN)
                    .add(Restrictions.eq(VOUtils.montarPath(ExameProfissional.PROP_TIPO_EXAME), dto.getExameProfissional().getTipoExame()))
                    .add(Restrictions.eq(VOUtils.montarPath(ExameProfissional.PROP_PROFISSIONAL), dto.getExameProfissional().getProfissional()))
                    .uniqueResult();

            if (exameProfissionalEmpresa != null) {
                epc.setEmpresa(exameProfissionalEmpresa.getExameProfissional().getEmpresa());
            } else {
                epc.setEmpresa(dto.getExameProfissional().getEmpresa());
            }

            epc.setProfissional(dto.getExameProfissional().getProfissional());
            epc.setTipoExame(dto.getExameProfissional().getTipoExame());
            epc.setTetoFinanceiroRealizado(0D);
        }

        if (Coalesce.asDouble(dto.getExameProfissional().getTetoFinanceiroPercentual()) > 0D) {
            tetoFinanceiro = Coalesce.asDouble(epc.getTetoFinanceiro());
            tetoFinanceiro = new Dinheiro(tetoFinanceiro).multiplicar(dto.getExameProfissional().getTetoFinanceiroPercentual()).dividir(100, 2).doubleValue();
        } else {
            tetoFinanceiro = dto.getExameProfissional().getTetoFinanceiroValor();
        }

        // Se o tipo do teto estiver configurado como físico, as cotas geradas devem ser com valor inteiro (#8263);">
        ExameCotaPpi exameCotaPpi = (ExameCotaPpi) getSession().createCriteria(ExameCotaPpi.class)
                .add(Restrictions.eq(ExameCotaPpi.PROP_TIPO_EXAME, epc.getTipoExame()))
                .uniqueResult();

        if (exameCotaPpi != null && ExameCotaPpi.TipoTeto.FISICO.value().equals(exameCotaPpi.getTipoTeto())) {
            tetoFinanceiro = Math.rint(tetoFinanceiro);
        }

        if(Coalesce.asDouble(tetoFinanceiro) < Coalesce.asDouble(epc.getTetoFinanceiroRealizado())){
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_possivel_gerar_cota_para_este_profissional_unidade_tipo_exame_pois_valor_cota_configurado_menor_valor_ja_utilizado"));
        }

        Double saldoRegistroAnterior = CotasExamesHelper.saldoInicioAnoOuSaldoAnterior(dto.getExameProfissional().getTetoSemanal(), epc, true);
        if (saldoRegistroAnterior != null && saldoRegistroAnterior > 0D) {
            tetoFinanceiro = new Dinheiro(tetoFinanceiro).somar(saldoRegistroAnterior).subtrair(Coalesce.asDouble(dto.getExameProfissional().getTetoSemanal())).doubleValue();
            epc.setTetoFinanceiro(tetoFinanceiro);
        } else {
            epc.setTetoFinanceiro(tetoFinanceiro);
        }

        epc.setTetoSemanal(dto.getExameProfissional().getTetoSemanal());
        epc.setValorExtraCota(0D);

        epc = BOFactory.save(epc);

        if(Coalesce.asDouble(dto.getExameProfissional().getTetoSemanal()) > 0D){
            ExameProfissionalSemana.Semana semana = ExameProfissionalSemana.Semana.valueOf(DataUtil.getWeekOfMonth(DataUtil.getDataAtual()));
            if(semana != null){
                ExameProfissionalSemana exameProfissionalSemana = (ExameProfissionalSemana) getSession().createCriteria(ExameProfissionalSemana.class)
                        .add(Restrictions.eq(ExameProfissionalSemana.PROP_EXAME_PROFISSIONAL_COMPETENCIA, epc))
                        .add(Restrictions.eq(ExameProfissionalSemana.PROP_SEMANA, semana.value()))
                        .uniqueResult();

                if (exameProfissionalSemana != null) {
                    exameProfissionalSemana = HibernateUtil.lockTable(ExameProfissionalSemana.class, exameProfissionalSemana.getCodigo());

                    if(Coalesce.asDouble(dto.getExameProfissional().getTetoSemanal()) < Coalesce.asDouble(exameProfissionalSemana.getTetoUtilizado())){
                        throw new ValidacaoException(Bundle.getStringApplication("msg_nao_possivel_gerar_cota_para_este_profissional_unidade_tipo_exame_pois_limite_semanal_configurado_menor_limite_semanal_ja_utilizado"));
                    }
                    exameProfissionalSemana.setTetoSemanal(dto.getExameProfissional().getTetoSemanal());
                    BOFactory.save(exameProfissionalSemana);
                } else {
                    Double saldoSemanaAnterior = 0D;

                    //Se a semana atual for diferente da primeira semana, deve ser realizado a atualização de saldo das semanas anteriores.
                    if(!ExameProfissionalSemana.Semana.SEMANA_1.value().equals(semana.value())) {

                        // A variável epsInicial serve para verificar em que semana foi gerado cota para o profissional
                        ExameProfissionalSemana epsInicial = LoadManager.getInstance(ExameProfissionalSemana.class)
                                .addParameter(new QueryCustom.QueryCustomParameter(ExameProfissionalSemana.PROP_EXAME_PROFISSIONAL_COMPETENCIA, epc))
                                .addSorter(new QueryCustom.QueryCustomSorter(ExameProfissionalSemana.PROP_SEMANA, BuilderQueryCustom.QuerySorter.CRESCENTE))
                                .setMaxResults(1)
                                .start().getVO();

                        if(epsInicial != null){
                            // A variável semanaAux dirá de que semana em diante deve ser verificado se o profissional possui cota semanal.
                            Long semanaAux = epsInicial.getSemana() + 1;
                            ExameProfissionalSemana epsSemanaAnterior;
                            boolean existsExameProfissionalSemana;

                            // Enquando semanaAux for menor que a semana atual, pois para a atual já será gerada.
                            while(semanaAux < semana.value()){
                                existsExameProfissionalSemana = getSession().createCriteria(ExameProfissionalSemana.class)
                                        .add(Restrictions.eq(ExameProfissionalSemana.PROP_EXAME_PROFISSIONAL_COMPETENCIA, epc))
                                        .add(Restrictions.eq(ExameProfissionalSemana.PROP_SEMANA, ExameProfissionalSemana.Semana.valueOf(semanaAux).value()))
                                        .list().size() > 0;

                                if(!existsExameProfissionalSemana){
                                    epsSemanaAnterior = (ExameProfissionalSemana) getSession().createCriteria(ExameProfissionalSemana.class)
                                            .add(org.hibernate.criterion.Restrictions.eq(ExameProfissionalSemana.PROP_EXAME_PROFISSIONAL_COMPETENCIA, epc))
                                            .add(org.hibernate.criterion.Restrictions.eq(ExameProfissionalSemana.PROP_SEMANA, ExameProfissionalSemana.Semana.valueOf(semanaAux - 1).value()))
                                            .uniqueResult();

                                    exameProfissionalSemana = new ExameProfissionalSemana();
                                    exameProfissionalSemana.setExameProfissionalCompetencia(epc);
                                    exameProfissionalSemana.setSemana(ExameProfissionalSemana.Semana.valueOf(semanaAux).value());
                                    if(epsSemanaAnterior != null){
                                        exameProfissionalSemana.setTetoSemanal(new Dinheiro(Coalesce.asDouble(epsSemanaAnterior.getSaldo())).somar(epc.getTetoSemanal()).doubleValue());
                                    } else {
                                        exameProfissionalSemana.setTetoSemanal(epc.getTetoSemanal());
                                    }
                                    exameProfissionalSemana.setTetoUtilizado(0D);
                                    BOFactory.save(exameProfissionalSemana);
                                }
                                semanaAux++;
                            }
                        }
//                        List<ExameProfissionalSemana> exameProfissionalSemanaList = getSession().createCriteria(ExameProfissionalSemana.class)
//                                .setLockMode(LockMode.PESSIMISTIC_WRITE)
//                                .add(org.hibernate.criterion.Restrictions.eq(ExameProfissionalSemana.PROP_EXAME_PROFISSIONAL_COMPETENCIA, epc))
//                                .addOrder(Order.desc(ExameProfissionalSemana.PROP_SEMANA))
//                                .list();
//
//                        if (CollectionUtils.isNotNullEmpty(exameProfissionalSemanaList)) {
//                            saldoSemanaAnterior = exameProfissionalSemanaList.get(0).getSaldo();
//                        }
                    }
//                    Calendar cal = new GregorianCalendar();
//                    cal.setTime(epc.getDataCompetencia());
//                    int primeiroDiaMes = cal.get(Calendar.DAY_OF_WEEK);

                    exameProfissionalSemana = new ExameProfissionalSemana();
                    exameProfissionalSemana.setExameProfissionalCompetencia(epc);
                    exameProfissionalSemana.setSemana(semana.value());
                    exameProfissionalSemana.setTetoSemanal(CotasExamesHelper.saldoInicioAnoOuSaldoAnterior(dto.getExameProfissional().getTetoSemanal(), epc, false));
                    exameProfissionalSemana.setTetoUtilizado(0D);
                    BOFactory.save(exameProfissionalSemana);
                }
            }
        } else {
            ExameProfissionalSemana exameProfissionalSemana = (ExameProfissionalSemana) getSession().createCriteria(ExameProfissionalSemana.class)
                    .add(Restrictions.eq(ExameProfissionalSemana.PROP_EXAME_PROFISSIONAL_COMPETENCIA, epc))
                    .uniqueResult();

            if(exameProfissionalSemana != null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_necessario_configurar_limite_semanal_para_este_profissional_unidade_tipo_exame_pois_ja_foi_gerado_registro_para_competencia_atual_com_limite_semanal_configurado"));
            }
        }
        BOFactory.getBO(ExameFacade.class).recalcularPercentualProfissional(epc);
    }
}
