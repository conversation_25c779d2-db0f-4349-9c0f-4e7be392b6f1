package br.com.ksisolucoes.bo.agendamento.agendagradeatendimentohorario;

import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamentoOcorrencia.TipoOcorrencia;

/**
 *
 * <AUTHOR>
 */
public class ReverterNaoComparecimentoAgendaGradeAtendimentoHorario extends AbstractCommandTransaction {

    private Long codigo;

    public ReverterNaoComparecimentoAgendaGradeAtendimentoHorario(Long codigo) {
        this.codigo = codigo;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (codigo == null) {
            return;
        }
        
        AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario = (AgendaGradeAtendimentoHorario) this.getSession().get(AgendaGradeAtendimentoHorario.class, this.codigo);
        
        if (agendaGradeAtendimentoHorario == null) {
            return;
        }
        
        agendaGradeAtendimentoHorario.setStatus(AgendaGradeAtendimentoHorario.STATUS_AGENDADO);
        agendaGradeAtendimentoHorario.setUsuarioConfirmacao(SessaoAplicacaoImp.getInstance().<Usuario>getUsuario());
        agendaGradeAtendimentoHorario.setDataConfirmacao(Data.getDataAtual());
        BOFactory.getBO(CadastroFacade.class).save(agendaGradeAtendimentoHorario);

        if(agendaGradeAtendimentoHorario.getSolicitacaoAgendamento() != null){
            BOFactory.getBO(AgendamentoFacade.class).gerarOcorrenciaSolicitacaoAgendamento(TipoOcorrencia.SOLICITACAO,
                Bundle.getStringApplication("msg_paciente_reversao_nao_comparecimento_X", agendaGradeAtendimentoHorario.getDataAgendamento(), agendaGradeAtendimentoHorario.getLocalAgendamento().getDescricao(), (agendaGradeAtendimentoHorario.getProfissional() == null ? "" : agendaGradeAtendimentoHorario.getProfissional().getNome())),
                    agendaGradeAtendimentoHorario.getSolicitacaoAgendamento());
        }
    }
    
}
