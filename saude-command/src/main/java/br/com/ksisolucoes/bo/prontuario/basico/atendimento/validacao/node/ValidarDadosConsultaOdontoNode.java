package br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.node;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.AbstractCommandValidacaoV2;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.annotations.ValidacaoProntuarioNode;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.DadosConsultaOdontoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaOdontoItem;
import br.com.ksisolucoes.vo.prontuario.basico.*;

import static ch.lambdaj.Lambda.forEach;

/**
 *
 * <AUTHOR>
 */
@ValidacaoProntuarioNode(value = NodesAtendimentoRef.DADOS_ODONTO, refClass = DadosConsultaOdontoDTO.class)
public class ValidarDadosConsultaOdontoNode extends AbstractCommandValidacaoV2<DadosConsultaOdontoDTO> {

    public ValidarDadosConsultaOdontoNode(Atendimento atendimento) {
        super(atendimento);
    }

    @Override
    public DadosConsultaOdontoDTO executarValidacao(DadosConsultaOdontoDTO object) throws DAOException, ValidacaoException {
        validarESUS(object);
        if (object != null) {
            Long count = LoadManager.getInstance(Encaminhamento.class)
                    .addGroup(new QueryCustom.QueryCustomGroup(Encaminhamento.PROP_CODIGO, BuilderQueryCustom.QueryGroup.COUNT))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Encaminhamento.PROP_ATENDIMENTO, Atendimento.PROP_CODIGO), getAtendimento().getCodigo()))
                    .start().getVO();
            if (count > 0) {
                if (object.getCondutaAtendimento() == null) {
                    validacao(Bundle.getStringApplication("msg_informe_conduta_quando_encaminhamento"));
                }
            }

            if (object.getClassificacaoAtendimento() == null) {
                validacao(Bundle.getStringApplication("msg_informe_vigilancia_saude_bucal"));
            }

            if (object.getProfissionalAuxiliar() != null) {
                if (object.getProfissionalAuxiliar().equals(atendimento.getProfissional())) {
                    validacao(Bundle.getStringApplication("msg_profissional_aux_nao_pode_ser_mesmo_profissional_atendimento"));
                } else if (object.getCboProfissionalAuxiliar() == null) {
                    validacao(Bundle.getStringApplication("msg_informe_cbo_profissional_auxiliar"));
                }
            }
        } else {
            validacao(Bundle.getStringApplication("msg_para_finalizar_atendimento_informe_dados_consulta"));
        }
        return object;
    }

    private void validarESUS(DadosConsultaOdontoDTO object) throws ValidacaoException, DAOException {
        Long tipoClassificacao = getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getTipoClassificacao();
        if (TipoAtendimento.TipoClassificacao.ODONTOLOGICA.value().equals(tipoClassificacao)) {
            getSessao().putClientProperty("VALIDACAO_ESUS_CONDUTA_" + getAtendimento().getCodigo(), true);
            if (object == null || (object.getCondutaAtendimento() == null || object.getClassificacaoAtendimento() == null)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_faturamento_esus_necessario_preencher_conduta_vigilancia_saude_bucal"));
            }
        }
        if (object.getTipoAtendimentoOdonto() == null) {
            throw new ValidacaoException("Por favor, informe o tipo de atendimento.");
        }
        if (EsusFichaOdontoItem.TipoAtendimento.CONSULTA_AGENDADA.value().equals(object.getTipoAtendimentoOdonto()) && object.getTipoConsulta() == null) {
            throw new ValidacaoException("Por favor, informe o tipo de consulta.");
        }
        if (EsusFichaOdontoItem.TipoAtendimento.ESCUTA_INICIAL_ORIENTACAO.value().equals(object.getTipoAtendimentoOdonto()) && object.getTipoConsulta() != null) {
            throw new ValidacaoException("O tipo de consulta não pode ser informado para este tipo de atendimento.");
        }
        if (EsusFichaOdontoItem.TipoAtendimento.ATENDIMENTO_URGENCIA.value().equals(object.getTipoAtendimentoOdonto()) && EsusFichaOdontoItem.TipoConsulta.CONSULTA_RETORNO.value().equals(object.getTipoConsulta())) {
            throw new ValidacaoException("O tipo de consulta não pode ser informado para este tipo de atendimento.");
        }
    }

    @Override
    public void processar(DadosConsultaOdontoDTO obj) throws DAOException, ValidacaoException {
        if (obj != null) {
            atendimento.setClassificacaoAtendimento(obj.getClassificacaoAtendimento());
            atendimento.setConduta(obj.getCondutaAtendimento());
            atendimento.setDiasRetorno(obj.getDiasRetorno());
            atendimento.setLocalAtendimento(obj.getLocalAtendimento());
            atendimento.setCodigoTipoConsulta(obj.getTipoConsulta());
            if (obj.getProfissionalAuxiliar() != null) {
                atendimento.setProfissionalAuxiliar(obj.getProfissionalAuxiliar());
                atendimento.setTabelaCboAuxiliar(obj.getCboProfissionalAuxiliar());
            }
            atendimento.setTipoAtendimentoOdonto(obj.getTipoAtendimentoOdonto());
            atendimento.setTipoFornecimentoOdonto(obj.getTipoFornecimento());
            atendimento.setCodigoTipoConsulta(obj.getTipoConsulta());

            atendimento = BOFactory.getBO(CadastroFacade.class).save(atendimento);
            
            if (CollectionUtils.isNotNullEmpty(obj.getCondutaAtendimentoList())) {
                forEach(obj.getCondutaAtendimentoList()).setAtendimento(atendimento);
            }
            VOUtils.persistirListaVosModificados(CondutaAtendimento.class, obj.getCondutaAtendimentoList(), new QueryCustom.QueryCustomParameter(CondutaAtendimento.PROP_ATENDIMENTO, atendimento));

            if (CollectionUtils.isNotNullEmpty(obj.getFornecimentoOdontoAtendList())) {
                forEach(obj.getFornecimentoOdontoAtendList()).setAtendimento(atendimento);
                VOUtils.persistirListaVosModificados(FornecimentoOdontoAtend.class, obj.getFornecimentoOdontoAtendList(), new QueryCustom.QueryCustomParameter(FornecimentoOdontoAtend.PROP_ATENDIMENTO, atendimento));
            }
        }
    }

}
