package br.com.ksisolucoes.bo.vacina.entradavacina;

import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.MovimentoEstoqueFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoque;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;
import br.com.ksisolucoes.vo.insumo.ItemPedidoInsumo;
import br.com.ksisolucoes.vo.vacina.*;
import java.util.ArrayList;
import java.util.List;
import org.hibernate.criterion.Order;

/**
 *
 * <AUTHOR>
 */
public class EstornarEntradaVacina extends AbstractCommandTransaction {

    private EntradaVacina entradaVacina;

    public EstornarEntradaVacina(EntradaVacina entradaVacina) {
        this.entradaVacina = entradaVacina;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        TipoDocumento tipoDocumento = getParametroContainer(Modulos.VACINAS).getParametro("TipoDocumentoEstornoVacinas");
        
        entradaVacina = (EntradaVacina) getSession().get(EntradaVacina.class, entradaVacina.getCodigo());

        if (!entradaVacina.getStatus().equals(EntradaVacina.StatusEntradaVacina.CONFIRMADO.getValue())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_este_recebimento_nao_mais_confirmado_consulte_novamente"));
        }
        
        entradaVacina.setStatus(EntradaVacina.StatusEntradaVacina.ABERTO.getValue());
        
        List<ItemEntradaVacina> itens = getSession().createCriteria(ItemEntradaVacina.class)
                .add(Restrictions.eq(ItemEntradaVacina.PROP_ENTRADA_VACINA, entradaVacina))
                .addOrder(Order.asc(ItemEntradaVacina.PROP_CODIGO))
                .list();
        
        Long i = 0L;
        for (ItemEntradaVacina itemEntradaVacina : itens) {
            MovimentoEstoque me = new MovimentoEstoque();
            
            me.setTipoDocumento(tipoDocumento);
            me.setNumeroDocumento(entradaVacina.getNota());
            me.setItemDocumento(++i);
            me.setProduto(itemEntradaVacina.getProduto());
            me.setQuantidade(itemEntradaVacina.getQuantidadeEntrada().doubleValue());
            me.setPessoa(entradaVacina.getFornecedor());
            me.setNomePessoa(entradaVacina.getFornecedor().getDescricao());
            me.setObservacao(Bundle.getStringApplication("movimentoGeradoEstornoVacina", entradaVacina.getNota(), i));
            me.setGrupoEstoque(itemEntradaVacina.getLote());
            me.setDataValidadeGrupoEstoque(itemEntradaVacina.getDataValidade());
            me.setDataPortaria(entradaVacina.getDataPortaria());
            
            BOFactory.getBO(MovimentoEstoqueFacade.class).gerarMovimentoEstoque(me);
        }
        
        List<PedidoVacinaInsumo> pedidos = new ArrayList<PedidoVacinaInsumo>();
        
        List<EloItemEntradaItemInsumo> elosInsumos = getSession().createCriteria(EloItemEntradaItemInsumo.class)
                .createCriteria(VOUtils.montarPath(EloItemEntradaItemInsumo.PROP_ITEM_ENTRADA_VACINA))
                    .add(Restrictions.eq(VOUtils.montarPath(ItemEntradaVacina.PROP_ENTRADA_VACINA), entradaVacina))
                .list();
        
        for (EloItemEntradaItemInsumo eloItemEntradaItemInsumo : elosInsumos) {
            ItemPedidoInsumo itemPedidoInsumo = eloItemEntradaItemInsumo.getItemPedidoInsumo();
            itemPedidoInsumo.setQuantidadeRecebida(itemPedidoInsumo.getQuantidadeRecebida()-eloItemEntradaItemInsumo.getQuantidadeRecebida());
            
            itemPedidoInsumo.setStatus(ItemPedidoInsumo.StatusItemPedidoInsumo.ABERTO.value());
            
            BOFactory.save(itemPedidoInsumo);
            BOFactory.delete(eloItemEntradaItemInsumo);
            
            if (!pedidos.contains(itemPedidoInsumo.getPedidoVacinaInsumo())) {
                pedidos.add(itemPedidoInsumo.getPedidoVacinaInsumo());
            }
        }
        
        List<EloItemEntradaItemVacina> elosVacinas = getSession().createCriteria(EloItemEntradaItemVacina.class)
                .createCriteria(VOUtils.montarPath(EloItemEntradaItemVacina.PROP_ITEM_ENTRADA_VACINA))
                .add(Restrictions.eq(VOUtils.montarPath(ItemEntradaVacina.PROP_ENTRADA_VACINA), entradaVacina))
                .list();
        
        for (EloItemEntradaItemVacina eloItemEntradaItemVacina : elosVacinas) {
            ItemPedidoVacina itemPedidoVacina = eloItemEntradaItemVacina.getItemPedidoVacina();
            itemPedidoVacina.setDosesRecebidas(itemPedidoVacina.getDosesRecebidas()-eloItemEntradaItemVacina.getDosesRecebidas());
            
            itemPedidoVacina.setStatus(ItemPedidoVacina.StatusItemPedidoVacina.ABERTO.value());
            
            BOFactory.save(itemPedidoVacina);
            BOFactory.delete(eloItemEntradaItemVacina);
            
            if (!pedidos.contains(itemPedidoVacina.getPedidoVacinaInsumo())) {
                pedidos.add(itemPedidoVacina.getPedidoVacinaInsumo());
            }
        }
        
        for (PedidoVacinaInsumo pedidoVacinaInsumo : pedidos) {
            pedidoVacinaInsumo.setStatus(PedidoVacinaInsumo.StatusPedidoVacinaInsumo.ENCAMINHADO.value());
            BOFactory.save(pedidoVacinaInsumo);
        }
    }

}
