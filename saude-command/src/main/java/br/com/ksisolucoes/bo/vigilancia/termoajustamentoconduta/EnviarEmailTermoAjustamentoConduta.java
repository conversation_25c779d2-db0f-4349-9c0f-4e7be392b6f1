package br.com.ksisolucoes.bo.vigilancia.termoajustamentoconduta;

import br.com.celk.util.Util;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QRCodeGenerateDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioRequerimentoVigilanciaComprovanteDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Email;
import br.com.ksisolucoes.util.MailAttachment;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.TermoAjustamentoConduta;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperExportManager;
import org.apache.commons.lang.StringUtils;

import javax.ws.rs.core.MediaType;
import java.io.File;
import java.io.IOException;

/**
 * <AUTHOR>
 */
public class EnviarEmailTermoAjustamentoConduta extends AbstractCommandTransaction {

    private final TermoAjustamentoConduta termoAjustamentoConduta;
    private String assunto;
    private String mensagem;
    private String email;
    private final boolean isNew;

    public EnviarEmailTermoAjustamentoConduta(TermoAjustamentoConduta termoAjustamentoConduta, boolean isNew) {
        this.termoAjustamentoConduta = termoAjustamentoConduta;
        this.isNew = isNew;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        Estabelecimento estabelecimento = termoAjustamentoConduta.getEstabelecimento();
        VigilanciaPessoa pessoa = termoAjustamentoConduta.getVigilanciaPessoa();

        if (estabelecimento != null && estabelecimento.getEmail() != null) {
            this.email = estabelecimento.getEmail();
        } else if (pessoa != null && pessoa.getEmail() != null) {
            this.email = pessoa.getEmail();
        }

        if (email != null) {
            if (!Util.isEmailValido(email)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_email_contribuinte_invalido"));
            }
            if(StringUtils.trimToNull(this.assunto) == null) {
                montarAssunto();
            }
            if(StringUtils.trimToNull(this.mensagem) == null) {
                montarMensagem();
            }
            try {
                File file = getAnexoComprovanteTermoAjustamentoConduta();
                MailAttachment mailAttachment = new MailAttachment(file, "pdf", "comprovante_requerimento");
                Email.create()
                        .assunto(assunto)
                        .para(email)
                        .mensagem(mensagem)
                        .addAttachment(mailAttachment)
                        .content(MediaType.TEXT_HTML).send();

            } catch (Throwable ex) {
                Loggable.log.warn("Email: " + email + " - " + ex.getMessage());
                throw new ValidacaoException("Não foi possível enviar o e-mail. Verifique se o e-mail definido está correto e tente novamente.");
            }
        }
    }

    private File getAnexoComprovanteTermoAjustamentoConduta() throws ReportException, IOException {
        DataReport dr;

        dr = BOFactory.getBO(VigilanciaReportFacade.class).impressaoComprovanteTermoAjustamentoConduta(
                termoAjustamentoConduta.getCodigo(),
                termoAjustamentoConduta.getNumeroFormatado(),
                termoAjustamentoConduta.getSituacao(),
                termoAjustamentoConduta.getFlagFinalizado()
        );
        return criaFileReport(dr);
    }

    private File criaFileReport(DataReport dataReport) throws IOException {
        try {
            File newFile = File.createTempFile("comprovante_termo_ajustamento_conduta_email", ".pdf");
            JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), newFile.getAbsolutePath());
            return newFile;
        } catch (JRException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return null;
    }

    private void montarAssunto() {
        StringBuilder sb = new StringBuilder();
        if (isNew) {
            sb.append("Solicitação de Termo de Ajustamento de Conduta - Vigilância Sanitária.");
        } else {
            sb.append("Atualização do Termo de Ajustamento de Conduta - Vigilância Sanitária.");
        }
        assunto = sb.toString();
    }

    private void montarMensagem() {
        StringBuilder sb = new StringBuilder();
        sb.append("<html>");
        sb.append("<body>");
        if (isNew) {
            sb.append("<p>Mensagem encaminhada referente ao Cadastro do Termo de Ajustamento de Conduta da Vigilância Sanitária.</p>");
            sb.append("<br/>");
        } else {
            sb.append("<p>Atualizamos a situação do seu Termo de Ajustamento de Conduta.</p>");
        }
        sb.append("<br/>");
        if(termoAjustamentoConduta.getEstabelecimento() != null && termoAjustamentoConduta.getEstabelecimento().getCodigo() != null) {
            sb.append("<strong>Estabelecimento: </strong>").append(termoAjustamentoConduta.getEstabelecimento().getDescricaoVO());
        } else if(termoAjustamentoConduta.getVigilanciaPessoa() != null && termoAjustamentoConduta.getVigilanciaPessoa().getCodigo() != null){
            sb.append("<strong>Contribuinte: </strong>").append(termoAjustamentoConduta.getVigilanciaPessoa().getDescricaoVO());
        }

        sb.append("<br/>");
        sb.append("<strong>Protocolo: </strong>").append(termoAjustamentoConduta.getNumeroFormatado());
        sb.append("<br/>");
        if(!isNew) {
            sb.append("<strong>Situação: </strong> FINALIZADO");
        } else {
            sb.append("<strong>Segue anexo o comprovante.</strong>");
        }
        sb.append("<p>Esta é uma mensagem automática, favor não responder.</p>");

        sb.append("</body>");
        sb.append("</html>");
        mensagem = sb.toString();
    }
}
