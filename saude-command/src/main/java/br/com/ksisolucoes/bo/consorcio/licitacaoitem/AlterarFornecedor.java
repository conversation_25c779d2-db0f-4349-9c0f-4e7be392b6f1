package br.com.ksisolucoes.bo.consorcio.licitacaoitem;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.Valor;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.EloLicitacaoPedido;
import br.com.ksisolucoes.vo.consorcio.LicitacaoItem;
import br.com.ksisolucoes.vo.consorcio.ManutencaoLicitacaoItem;
import br.com.ksisolucoes.vo.consorcio.PedidoLicitacaoItem;
import org.hibernate.criterion.Restrictions;

import java.math.MathContext;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AlterarFornecedor extends AbstractCommandTransaction{

    private final ManutencaoLicitacaoItem manutencaoLicitacaoItem;
    private LicitacaoItem licitacaoItem;
    private final boolean alterarValorTotal;
    private final boolean validaNovoFornecedor;

    public AlterarFornecedor(ManutencaoLicitacaoItem manutencaoLicitacaoItem, boolean alterarValorTotal, boolean validaNovoFornecedor) {
        this.manutencaoLicitacaoItem = manutencaoLicitacaoItem;
        this.alterarValorTotal = alterarValorTotal;
        this.validaNovoFornecedor = validaNovoFornecedor;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (validaNovoFornecedor) {
            if (manutencaoLicitacaoItem.getFornecedor() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_novo_fornecedor"));
            }
        }
        if (manutencaoLicitacaoItem.getJustificativa()==null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_justificativa"));
        }

        licitacaoItem = manutencaoLicitacaoItem.getLicitacaoItem();

        licitacaoItem.setPessoa(manutencaoLicitacaoItem.getFornecedor());

        if (manutencaoLicitacaoItem.getFabricante() != null) {
            licitacaoItem.setFabricante(manutencaoLicitacaoItem.getFabricante());
        }

        List<EloLicitacaoPedido> elos = getSession().createCriteria(EloLicitacaoPedido.class)
                .add(Restrictions.eq(VOUtils.montarPath(EloLicitacaoPedido.PROP_LICITACAO_ITEM), licitacaoItem))
                .createCriteria(VOUtils.montarPath(EloLicitacaoPedido.PROP_PEDIDO_LICITACAO_ITEM))
                .add(Restrictions.eq(VOUtils.montarPath(PedidoLicitacaoItem.PROP_STATUS), PedidoLicitacaoItem.StatusPedidoLicitacaoItem.LICITADO.value()))
                .list();

        if(Coalesce.asDouble(licitacaoItem.getPrecoTotal()) > 0D && Coalesce.asDouble(licitacaoItem.getPrecoTotal()) > 0D){
            if (alterarValorTotal){
                if (manutencaoLicitacaoItem.getPrecoTotal() == null ){
                    throw new ValidacaoException(Bundle.getStringApplication("msg_preco_total_obrigatorio"));
                }

                Double valorTotal = 0D;

                for (EloLicitacaoPedido eloLicitacaoPedido : elos) {
                    valorTotal = new Dinheiro(Coalesce.asDouble(eloLicitacaoPedido.getPedidoLicitacaoItem().getValorTotalLicitado()), MathContext.DECIMAL128).somar(valorTotal).round().doubleValue();
                }

                if (valorTotal>manutencaoLicitacaoItem.getPrecoTotal()) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_valor_total_pedidos_consorciados_X_nao_pode_maior_valor_informado", Valor.adicionarFormatacaoMonetaria(valorTotal, 2)));
                }

                licitacaoItem.setPrecoTotal(manutencaoLicitacaoItem.getPrecoTotal());
                licitacaoItem.setStatus(LicitacaoItem.StatusLicitacaoItem.LICITADO.value());
            }
        }

        licitacaoItem.setPrecoUnitarioOriginal(manutencaoLicitacaoItem.getPrecoUnitario());
        licitacaoItem.setPrecoUnitario(licitacaoItem.getPrecoUnitario());

        licitacaoItem.setPrecoTotal(new Dinheiro(licitacaoItem.getQuantidadeLicitada()).multiplicar(licitacaoItem.getPrecoUnitario(), 4).round().doubleValue());

        licitacaoItem.setJustificativa(manutencaoLicitacaoItem.getJustificativa());


        BOFactory.save(licitacaoItem);

        manutencaoLicitacaoItem.setTipoManutencao(ManutencaoLicitacaoItem.TipoManutencao.ALTERACAO_FORNECEDOR.value());
        BOFactory.save(manutencaoLicitacaoItem);

        for (EloLicitacaoPedido eloLicitacaoPedido : elos) {
            eloLicitacaoPedido.getPedidoLicitacaoItem().setPrecoReal(licitacaoItem.getPrecoUnitario());
            BOFactory.save(eloLicitacaoPedido.getPedidoLicitacaoItem());
        }

    }

    public LicitacaoItem getLicitacaoItem() {
        return licitacaoItem;
    }

}
