package br.com.ksisolucoes.bo.vigilancia.requerimentos.contratosocial;

import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoRepresentanteLegalDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaSolicitacaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.CpfCnpJValidator;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaEnum;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoRepresentanteLegal;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

/**
 * <AUTHOR>
 */
public class SalvarRequerimentoRepresentanteLegal extends AbstractCommandTransaction<SalvarRequerimentoRepresentanteLegal> {

    private final RequerimentoRepresentanteLegalDTO dto;
    private RequerimentoVigilancia requerimentoVigilancia;
    private boolean gerarOcorrenciaCadastro = false;
    private ConfiguracaoVigilancia configuracaoVigilancia;

    public SalvarRequerimentoRepresentanteLegal(RequerimentoRepresentanteLegalDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        configuracaoVigilancia = BOFactory.getBO(VigilanciaFacade.class).carregarConfiguracaoVigilancia();
        RequerimentoRepresentanteLegal requerimentoRepresentanteLegal = dto.getRequerimentoRepresentanteLegal();

        if (requerimentoRepresentanteLegal.getRepresentanteCpf() != null && !CpfCnpJValidator.CPFIsValid(StringUtil.getDigits(requerimentoRepresentanteLegal.getRepresentanteCpf()))) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_cpf_representante_invalido"));
        }

        if (requerimentoRepresentanteLegal.getRequerimentoVigilancia().getCodigo() == null) {
            dto.getRequerimentoRepresentanteLegal().getRequerimentoVigilancia().setEstabelecimento(dto.getRequerimentoRepresentanteLegal().getEstabelecimento());
            dto.getRequerimentoRepresentanteLegal().getRequerimentoVigilancia().setSituacao(RequerimentoVigilancia.Situacao.PENDENTE.value());
            dto.getRequerimentoRepresentanteLegal().getRequerimentoVigilancia().setTipoDocumento(TipoSolicitacao.TipoDocumento.ALTERACAO_RESPONSABILIDADE_LEGAL.value());
            if (ConfiguracaoVigilanciaEnum.TipoGestaoRequerimento.FISCAL.value().equals(configuracaoVigilancia.getFlagTipoGestaoRequerimento())) {
                dto.getRequerimentoRepresentanteLegal().getRequerimentoVigilancia().setSituacaoAprovacao(RequerimentoVigilancia.SituacaoAprovacao.AGUARDANDO_INFORMAR_FISCAL.value());
            } else {
                dto.getRequerimentoRepresentanteLegal().getRequerimentoVigilancia().setSituacaoAprovacao(RequerimentoVigilancia.SituacaoAprovacao.APROVADO.value());
            }
            gerarOcorrenciaCadastro = true;
        }

        requerimentoVigilancia = BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilancia(new RequerimentoVigilanciaSolicitacaoDTO(dto.getRequerimentoRepresentanteLegal().getRequerimentoVigilancia()));

        requerimentoVigilancia = VigilanciaHelper.atualizarGestaoRequerimento(requerimentoVigilancia, dto.getRequerimentoVigilanciaFiscalList(), dto.getEloRequerimentoVigilanciaSetorVigilanciaList(), gerarOcorrenciaCadastro);

        if (gerarOcorrenciaCadastro) {
            BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(Bundle.getStringApplication("msg_requerimento_cadastrado"), requerimentoVigilancia, null);
        }

        dto.getRequerimentoRepresentanteLegal().setRequerimentoVigilancia(requerimentoVigilancia);


        BOFactory.save(requerimentoRepresentanteLegal);
        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaAnexo(requerimentoVigilancia, dto.getRequerimentoVigilanciaAnexoDTOList(), dto.getRequerimentoVigilanciaAnexoExcluidoDTOList(), false);
        BOFactory.getBO(VigilanciaFacade.class).salvarEloRequerimentoVigilanciaSetorVigilancia(requerimentoVigilancia, dto.getEloRequerimentoVigilanciaSetorVigilanciaList(), dto.getEloRequerimentoVigilanciaSetorVigilanciaExcluirList());
        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaFiscais(requerimentoVigilancia, dto.getRequerimentoVigilanciaFiscalList(), dto.getRequerimentoVigilanciaFiscalListExcluir());

        if (gerarOcorrenciaCadastro) {
            BOFactory.getBO(VigilanciaFacade.class).enviarEmailNovoRequerimentoVigilancia(this.requerimentoVigilancia);
        }
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }
}