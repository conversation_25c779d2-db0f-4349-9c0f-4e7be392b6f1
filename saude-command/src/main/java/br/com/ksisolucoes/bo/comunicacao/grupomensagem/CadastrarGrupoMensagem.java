package br.com.ksisolucoes.bo.comunicacao.grupomensagem;

import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.GrupoMensagemDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.GrupoMensagem;
import br.com.ksisolucoes.vo.comunicacao.GrupoMensagemUsuario;
import static ch.lambdaj.Lambda.*;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CadastrarGrupoMensagem extends AbstractCommandTransaction{

    private GrupoMensagemDTO dto;

    public CadastrarGrupoMensagem(GrupoMensagemDTO dto) {
        this.dto = dto;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        GrupoMensagem grupoMensagem = dto.getGrupoMensagem();
        BOFactory.save(grupoMensagem);
        
        List<GrupoMensagemUsuario> usuarios = dto.getUsuarios();
        
        forEach(usuarios).setGrupoMensagem(grupoMensagem);
        
        VOUtils.persistirListaVosModificados(GrupoMensagemUsuario.class, usuarios, new QueryCustom.QueryCustomParameter(GrupoMensagemUsuario.PROP_GRUPO_MENSAGEM, grupoMensagem));
    }

}
