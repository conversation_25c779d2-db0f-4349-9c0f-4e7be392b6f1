package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentoreceitab;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoReceitaB;

/**
 *
 * <AUTHOR>
 */
public class SaveRequerimentoReceitaB extends SaveVO<RequerimentoReceitaB> {

    public SaveRequerimentoReceitaB(RequerimentoReceitaB vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getUsuarioCadastro() == null) {
            this.vo.setUsuarioCadastro(getSessao().<Usuario>getUsuario());
        }
        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(DataUtil.getDataAtual());
        }
    }
}