package br.com.ksisolucoes.bo.consorcio.subconta;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.SubConta;
import br.com.ksisolucoes.vo.controle.Usuario;

/**
 *
 * <AUTHOR>
 */
public class SaveSubConta extends SaveVO<SubConta> {

    public SaveSubConta(SubConta vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getCodigo()==null) {
            this.vo.setSaldoAtual(0D);
            this.vo.setValorReservado(0D);
        }
        this.vo.setDataUsuario(Data.getDataAtual());
        this.vo.setUsuario(getSessao().<Usuario>getUsuario());
    }

}
