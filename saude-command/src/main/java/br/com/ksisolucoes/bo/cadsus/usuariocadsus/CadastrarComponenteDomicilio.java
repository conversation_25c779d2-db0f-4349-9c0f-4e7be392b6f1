package br.com.ksisolucoes.bo.cadsus.usuariocadsus;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.UsuarioCadsusEnderecoDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.EnderecoUsuarioCadsusFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper;

/**
 *
 * <AUTHOR>
 */
public class CadastrarComponenteDomicilio extends AbstractCommandTransaction {

    private UsuarioCadsusEnderecoDTO usuarioCadsusEnderecoDTO;
    private UsuarioCadsus usuarioCadsus;
    private UsuarioCadsusEsus usuarioCadsusEsus;

    public CadastrarComponenteDomicilio(UsuarioCadsusEnderecoDTO usuarioCadsusEnderecoDTO) {
        this.usuarioCadsusEnderecoDTO = usuarioCadsusEnderecoDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        this.usuarioCadsus = this.usuarioCadsusEnderecoDTO.getUsuarioCadsusEsus().getUsuarioCadsus();
        this.usuarioCadsusEsus = this.usuarioCadsusEnderecoDTO.getUsuarioCadsusEsus();

        if (usuarioCadsus.getFlagDocumento() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_paciente_apresentou_documentos"));
        }

        if (usuarioCadsus.getSituacao().equals(UsuarioCadsus.SITUACAO_EXCLUIDO)) {
            usuarioCadsus.setExcluido(RepositoryComponentDefault.EXCLUIDO);
        } else if (usuarioCadsus.getSituacao().equals(UsuarioCadsus.SITUACAO_INATIVO)) {
            usuarioCadsus.setDataInativacao(DataUtil.getDataAtual());
        }

        String validarDocumentoCadastroNovo = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("ValidarDocumentoCadastroNovo");
        String validarCnsCadastroNovo = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("ValidarCnsCadastroNovo");
        String validarTelefoneCadastroNovo = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("ValidarTelefoneCadastroNovo");
        if (!UsuarioCadsus.SITUACAO_EXCLUIDO.equals(usuarioCadsus.getSituacao())) {

            if (usuarioCadsus.getDataCadastro() == null || Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial().compareTo(Data.adjustRangeHour(usuarioCadsus.getDataCadastro()).getDataInicial()) == 0) {
                if (RepositoryComponentDefault.SIM.equals(validarDocumentoCadastroNovo)) {
                    // Valida obrigatoriedade dos documentos e se os documentos estão corretos
                    UsuarioCadsusHelper.validarIdentidadeCertidao(usuarioCadsusEnderecoDTO.getDocumentos(), true, false, true);
                } else {
                    // Não valida obrigatoriedade dos documentos, apenas se os documentos estão corretos
                    UsuarioCadsusHelper.validarIdentidadeCertidao(usuarioCadsusEnderecoDTO.getDocumentos(), false, false, true);
                }
                if (RepositoryComponentDefault.SIM.equals(validarCnsCadastroNovo)) {
                    // Valida se foi informado um cns e se o mesmo é ativo
                    UsuarioCadsusHelper.validarCns(true, usuarioCadsusEnderecoDTO.getCartoes());
                }
                if (RepositoryComponentDefault.SIM.equals(validarTelefoneCadastroNovo)) {
                    // Valida se foi informado pelo menos um telefone
                    UsuarioCadsusHelper.validarTelefoneUsuarioCadsus(usuarioCadsus);
                }
            } else {
                // Valida se foi informado pelo um documento, incluíndo cns como documento
                UsuarioCadsusHelper.validarIdentidadeCertidao(usuarioCadsusEnderecoDTO.getDocumentos(), true, true, false, usuarioCadsusEnderecoDTO.getCartoes());
            }
        }

        BOFactory.getBO(UsuarioCadsusFacade.class).save(usuarioCadsus);
        if (usuarioCadsus.getReferencia() == null){
            usuarioCadsus.setReferencia(usuarioCadsus.getCodigo().toString());
            usuarioCadsus = BOFactory.getBO(UsuarioCadsusFacade.class).save(usuarioCadsus);
        }
        BOFactory.getBO(UsuarioCadsusFacade.class).salvarUsuarioCadsusEsus(usuarioCadsusEsus);

        BOFactory.getBO(UsuarioCadsusFacade.class).saveUsuarioCadsusDocumentos(this.usuarioCadsusEnderecoDTO.getDocumentos(), usuarioCadsus, this.usuarioCadsusEnderecoDTO.getTipoDocumentos());

        new CadastrarUsuarioCadsusCns(this.usuarioCadsusEnderecoDTO.getCartoes(), usuarioCadsus, false).start();

        new GerarNumeroProntuarioUsuarioCadsus(usuarioCadsus, usuarioCadsusEnderecoDTO.getNumeroProntuario()).start();
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }
}
