/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.cadsus.usuariocadsus;

import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHistoricoAprovacao;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHistoricoAprovacaoPK;
import br.com.ksisolucoes.vo.controle.Usuario;

/**
 *
 * <AUTHOR>
 */
public class ConfirmarEntregaCartaoUsuarioCadsus extends AbstractCommandTransaction{

    private Long codigoUsuarioCadsus;
    private String responsavel;

    public ConfirmarEntregaCartaoUsuarioCadsus(Long codigoUsuarioCadsus, String responsavel) {
        this.codigoUsuarioCadsus = codigoUsuarioCadsus;
        this.responsavel = responsavel;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        UsuarioCadsus usuarioCadsus = (UsuarioCadsus) getSession().get(UsuarioCadsus.class, codigoUsuarioCadsus);
        usuarioCadsus.setSituacaoAprovacao(UsuarioCadsus.SITUACAO_APROVACAO_ENTREGUE);

        UsuarioCadsusHistoricoAprovacao ucha = new UsuarioCadsusHistoricoAprovacao();
        ucha.setId(new UsuarioCadsusHistoricoAprovacaoPK());
        ucha.getId().setUsuarioCadsus(usuarioCadsus);
        ucha.getId().setDataAprovacao(Data.getDataAtual());
        ucha.setUsuario((Usuario) getSessao().getUsuario());
        ucha.setSituacao(UsuarioCadsus.SITUACAO_APROVACAO_ENTREGUE);
        ucha.setObservacao(Bundle.getStringApplication("rotulo_responsavel")+": "+responsavel);

        BOFactory.getBO(CadastroFacade.class).save(ucha);

    }

}
