package br.com.ksisolucoes.bo.vigilancia.setorvigilancia;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QueryConsultaSetorVigilanciaDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.vigilancia.SetorVigilancia;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaSetorVigilancia extends CommandQueryPager<QueryConsultaSetorVigilancia> {

    private QueryConsultaSetorVigilanciaDTOParam param;

    public QueryConsultaSetorVigilancia(QueryConsultaSetorVigilanciaDTOParam param) {
        this.param = param;
    }
    
    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect("c.codigo", true);
        hql.addToSelect("c.descricao", true);
        
        hql.setTypeSelect(SetorVigilancia.class.getName());
        hql.addToFrom("SetorVigilancia c");
        
        hql.addToWhereWhithAnd("c.codigo = ", param.getCodigo());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("c.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("c.codigo || ' ' || c.descricao",param.getKeyword()));
        
        if(param.getPropSort() != null){
            hql.addToOrder("c."+param.getPropSort()+" "+ (param.isAscending()?"asc":"desc"));
        }else{
            hql.addToOrder("c.descricao");
        }
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list =  hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
