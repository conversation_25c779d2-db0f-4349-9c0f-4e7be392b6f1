/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.prontuario.procedimento.pequenacirurgia;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.prontuario.procedimento.interfaces.dto.PequenaCirurgiaDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.ParametroAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoEncaminhamento;
import br.com.ksisolucoes.vo.prontuario.procedimento.PequenaCirurgia;
import br.com.ksisolucoes.vo.prontuario.procedimento.PequenaCirurgia.StatusPequenaCirurgia;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SalvarPequenaCirurgia extends AbstractCommandTransaction{

    private PequenaCirurgiaDTO dto;

    public SalvarPequenaCirurgia(PequenaCirurgiaDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        TipoEncaminhamento tipoEncaminhamentoPequenaCirurgia = (TipoEncaminhamento) CargaBasicoPadrao.getInstance().getParametroAtendimento().getPropertyValue(ParametroAtendimento.PROP_TIPO_ENCAMINHAMENTO_PEQUENA_CIRURGIA);

        dto.getEncaminhamento().setTipoEncaminhamento(tipoEncaminhamentoPequenaCirurgia);

        BOFactory.getBO(CadastroFacade.class).save(dto.getEncaminhamento());

        List<PequenaCirurgia> itensSalvos = LoadManager.getInstance(PequenaCirurgia.class)
            .addProperty(PequenaCirurgia.PROP_CODIGO)
            .addParameter(new QueryCustomParameter(PequenaCirurgia.PROP_ENCAMINHAMENTO, dto.getEncaminhamento()))
            .start().getList();
        if(CollectionUtils.isNotNullEmpty(itensSalvos)){
            for (PequenaCirurgia pequenaCirurgiaItem : itensSalvos) {
                BOFactory.getBO(CadastroFacade.class).delete(pequenaCirurgiaItem);
            }
        }

        if(CollectionUtils.isNotNullEmpty(dto.getItens())){
            for (PequenaCirurgia pequenaCirurgiaItem : dto.getItens()) {
               pequenaCirurgiaItem.setCodigo(null);
               pequenaCirurgiaItem.setEncaminhamento(dto.getEncaminhamento());
               pequenaCirurgiaItem.setStatus(StatusPequenaCirurgia.ABERTO.value());

               BOFactory.getBO(CadastroFacade.class).save(pequenaCirurgiaItem);
            }
        }
    }

    public PequenaCirurgiaDTO getDto() {
        return dto;
    }
    
}

