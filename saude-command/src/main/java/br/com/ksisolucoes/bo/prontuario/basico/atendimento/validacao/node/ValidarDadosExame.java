package br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.node;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.AbstractCommandValidacaoV2;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.annotations.ValidacaoProntuarioNode;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.DadosExameDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;

/**
 *
 * <AUTHOR>
 */
@ValidacaoProntuarioNode(value=NodesAtendimentoRef.DADOS_EXAME, refClass= DadosExameDTO.class)
public class ValidarDadosExame  extends AbstractCommandValidacaoV2<DadosExameDTO>{
    
    public ValidarDadosExame(Atendimento atendimento) {
        super(atendimento);
    }

    @Override
    public DadosExameDTO executarValidacao(DadosExameDTO object) throws DAOException, ValidacaoException {
        return object;
    }

    @Override
    public void processar(DadosExameDTO object) throws DAOException, ValidacaoException {
        if (object!=null && object.getAtendimentoExame() != null) {
            object.getAtendimentoExame().setAtendimento(atendimento);
            
            BOFactory.getBO(ExameFacade.class).cadastrarAtendimentoExameWeb(object.getAtendimentoExame(), object.getLstAtendimentoExameItem());
        }
    }
    
}
