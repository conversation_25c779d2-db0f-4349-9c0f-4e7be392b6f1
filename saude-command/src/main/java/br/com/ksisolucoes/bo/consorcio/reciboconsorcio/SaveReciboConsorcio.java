package br.com.ksisolucoes.bo.consorcio.reciboconsorcio;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.ReciboConsorcio;

/**
 * Created by laudecir on 24/01/18.
 */
public class SaveReciboConsorcio extends SaveVO<ReciboConsorcio> {
    /**
     * Construtor para o Objeto de Negcio. Este prove obrigatoriedade
     * na passagem do VO como parmetro.
     *
     * @param vo
     */
    public SaveReciboConsorcio(ReciboConsorcio vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        this.vo.setUsuario(getSessao().getUsuario());

        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(DataUtil.getDataAtual());
        }
        if(this.vo.getStatus() == null) {
            this.vo.setStatus(ReciboConsorcio.Status.EMITIDO.value());
        }
    }
}
