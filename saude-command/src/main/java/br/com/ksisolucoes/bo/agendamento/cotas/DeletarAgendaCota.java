package br.com.ksisolucoes.bo.agendamento.cotas;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaCota;
import br.com.ksisolucoes.vo.agendamento.AgendaCotaProfissional;
import static ch.lambdaj.Lambda.on;
import java.util.List;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class DeletarAgendaCota extends AbstractCommandTransaction {

    private final AgendaCota agendaCota;

    public DeletarAgendaCota(AgendaCota agendaCota) {
        this.agendaCota = agendaCota;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        AgendaCotaProfissional proxy = on(AgendaCotaProfissional.class);
        
        List<AgendaCotaProfissional> agendaCotaProfissionalList = getSession().createCriteria(AgendaCotaProfissional.class)
                .add(Restrictions.eq(path(proxy.getAgendaCota()), agendaCota))
                .list();
        
        if(CollectionUtils.isNotNullEmpty(agendaCotaProfissionalList)){
            for(AgendaCotaProfissional acp : agendaCotaProfissionalList){
                BOFactory.delete(acp);                
            }
        }
        
        BOFactory.delete(agendaCota);
    }
}