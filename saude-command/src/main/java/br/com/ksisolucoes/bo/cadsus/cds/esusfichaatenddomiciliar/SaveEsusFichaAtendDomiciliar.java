package br.com.ksisolucoes.bo.cadsus.cds.esusfichaatenddomiciliar;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendDomiciliar;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.esus.dto.EsusValidacoesFichasDTOParam;
import br.com.ksisolucoes.vo.esus.helper.EsusIntegracaoHelper;
import br.com.ksisolucoes.util.esus.EsusValidacoesFichaAtendimentoDomiciliarHelper;

/**
 *
 * <AUTHOR>
 */
public class SaveEsusFichaAtendDomiciliar extends SaveVO<EsusFichaAtendDomiciliar> {

    public SaveEsusFichaAtendDomiciliar(EsusFichaAtendDomiciliar vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        EsusValidacoesFichasDTOParam paramValidacao = new EsusValidacoesFichasDTOParam();
        paramValidacao.setRetorno(EsusValidacoesFichasDTOParam.Retorno.EXCEPTION);
        paramValidacao.setEmpresa(vo.getEmpresa());
        paramValidacao.setProfissional(vo.getProfissionalPrincipal());
        paramValidacao.setTabelaCbo(vo.getCboPrincipal());
        paramValidacao.setEsusFichaAtendimentoDomiciliar(vo);

        EsusValidacoesFichaAtendimentoDomiciliarHelper.validate(paramValidacao);

        EsusIntegracaoHelper.validarEquipeProfissional(vo.getEmpresa(), vo.getProfissionalPrincipal());

        if (this.vo.getUsuario() == null) {
            this.vo.setUsuario(getSessao().<Usuario>getUsuario());
        }
        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(DataUtil.getDataAtual());
        }
    }
}
