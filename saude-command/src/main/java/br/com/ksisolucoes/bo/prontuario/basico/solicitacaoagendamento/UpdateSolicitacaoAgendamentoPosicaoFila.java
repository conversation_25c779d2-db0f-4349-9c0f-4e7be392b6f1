package br.com.ksisolucoes.bo.prontuario.basico.solicitacaoagendamento;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import org.hibernate.Query;
import org.hibernate.type.LongType;

public class UpdateSolicitacaoAgendamentoPosicaoFila extends AbstractCommandTransaction<UpdateSolicitacaoAgendamentoPosicaoFila> {

    private final Long posicaoFilaEspera;
    private final SolicitacaoAgendamento solicitacaoAgendamento;
    private boolean posicaoFilaEsperaManual = false;

    public UpdateSolicitacaoAgendamentoPosicaoFila(SolicitacaoAgendamento solicitacaoAgendamento, Long posicaoFilaEspera) {
        this.solicitacaoAgendamento = solicitacaoAgendamento;
        this.posicaoFilaEspera = posicaoFilaEspera;
    }

    public UpdateSolicitacaoAgendamentoPosicaoFila(SolicitacaoAgendamento solicitacaoAgendamento, Long posicaoFilaEspera,boolean posicaoFilaEsperaManual) {
        this.solicitacaoAgendamento = solicitacaoAgendamento;
        this.posicaoFilaEspera = posicaoFilaEspera;
        this.posicaoFilaEsperaManual = posicaoFilaEsperaManual;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if(solicitacaoAgendamento != null){
            String composicaoSql = posicaoFilaEsperaManual ? ":posicaoFilaEsperaManual" : "null";
            String sqlUpdate = "update solicitacao_agendamento_posicao_fila set posicao_fila_espera = :posicaoFilaEspera, posicao_fila_espera_manual= "+composicaoSql+" where cd_solicitacao = :codigoSolicitacao";
            Query updatePosicaoFilaEspera = getSession().createSQLQuery(sqlUpdate);
            updatePosicaoFilaEspera.setParameter("posicaoFilaEspera", posicaoFilaEspera, LongType.INSTANCE);
            updatePosicaoFilaEspera.setParameter("codigoSolicitacao", solicitacaoAgendamento.getCodigo());
            if(posicaoFilaEsperaManual)
                updatePosicaoFilaEspera.setParameter("posicaoFilaEsperaManual", posicaoFilaEspera, LongType.INSTANCE);
            updatePosicaoFilaEspera.executeUpdate();
        }
    }
}
