package br.com.ksisolucoes.bo.vigilancia.fatorriscosanitario;


import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.FatorRiscoCnae;
import br.com.ksisolucoes.vo.vigilancia.FatorRiscoSanitario;
import br.com.ksisolucoes.vo.vigilancia.PerguntaFatorRiscoCnaeCondicao;
import br.com.ksisolucoes.vo.vigilancia.PerguntaFatorRiscoCnaePre;

import java.util.ArrayList;
import java.util.List;


public class SalvarCadastroFatorRiscoSanitario extends AbstractCommandTransaction<FatorRiscoSanitario> {

    private FatorRiscoCnae fatorRiscoSanitario;
    private List<PerguntaFatorRiscoCnaePre> listPre;
    private List<PerguntaFatorRiscoCnaeCondicao> listCondicao;

    public SalvarCadastroFatorRiscoSanitario(FatorRiscoCnae fatorRiscoSanitario, List<PerguntaFatorRiscoCnaePre> listPre, List<PerguntaFatorRiscoCnaeCondicao> listCondicao) {
        this.fatorRiscoSanitario = fatorRiscoSanitario;
        this.listPre = listPre;
        this.listCondicao = listCondicao;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        FatorRiscoCnae fatorRiscoCnae = BOFactory.save(fatorRiscoSanitario);

        List<PerguntaFatorRiscoCnaePre> list = new ArrayList<>();
        if (CollectionUtils.isNotNullEmpty(listPre)) {
            for (PerguntaFatorRiscoCnaePre perguntaFatorRiscoCnaePre : listPre) {
                perguntaFatorRiscoCnaePre.setFatorRiscoCnae(fatorRiscoCnae);
                list.add(perguntaFatorRiscoCnaePre);
            }
        }
        VOUtils.persistirListaVosModificados(PerguntaFatorRiscoCnaePre.class, list, new QueryCustom.QueryCustomParameter(PerguntaFatorRiscoCnaePre.PROP_FATOR_RISCO_CNAE,fatorRiscoCnae));

        List<PerguntaFatorRiscoCnaeCondicao> listCon = new ArrayList<>();
        if (CollectionUtils.isNotNullEmpty(listCondicao)) {
            for (PerguntaFatorRiscoCnaeCondicao perguntaFatorRiscoCnaeCondicao : listCondicao) {
                perguntaFatorRiscoCnaeCondicao.setFatorRiscoCnae(fatorRiscoCnae);
                listCon.add(perguntaFatorRiscoCnaeCondicao);
            }
        }
        VOUtils.persistirListaVosModificados(PerguntaFatorRiscoCnaeCondicao.class, listCon, new QueryCustom.QueryCustomParameter(PerguntaFatorRiscoCnaeCondicao.PROP_FATOR_RISCO_CNAE,fatorRiscoCnae));
    }
}
