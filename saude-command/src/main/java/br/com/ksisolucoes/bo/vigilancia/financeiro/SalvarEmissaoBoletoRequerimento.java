package br.com.ksisolucoes.bo.vigilancia.financeiro;

import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.FinanceiroVigilanciaHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.VigilanciaFinanceiroBoletoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaEnum;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SalvarEmissaoBoletoRequerimento extends AbstractCommandTransaction<SalvarEmissaoBoletoRequerimento> {

    private boolean atualizarSituacao;
    private VigilanciaFinanceiroBoletoDTO dto;
    private List<VigilanciaFinanceiro> vigilanciaFinanceiroList;
    private ConfiguracaoVigilanciaFinanceiro configuracaoVigilanciaFinanceiro;

    public SalvarEmissaoBoletoRequerimento(VigilanciaFinanceiroBoletoDTO vigilanciaFinanceiroBoletoDTO, boolean atualizarSituacao) {
        this.dto = vigilanciaFinanceiroBoletoDTO;
        this.atualizarSituacao = atualizarSituacao;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        // gera boleto/memorando unico
        vigilanciaFinanceiroList = new ArrayList<>();
        configuracaoVigilanciaFinanceiro = VigilanciaHelper.getConfiguracaoVigilanciaFinanceiro();

        FinanceiroVigilanciaHelper.validarBoletoRequerimento(dto, configuracaoVigilanciaFinanceiro);
        VigilanciaFinanceiro vigilanciaFinanceiro = null;
        if (!FinanceiroVigilanciaHelper.possuiFinanceiroGerado(dto.getRequerimentoVigilancia())) {
            if (FinanceiroVigilanciaHelper.isParcelar(dto.getQuantidadeParcelas())) {
                for (int i = 1; i <= dto.getQuantidadeParcelas(); i++) {
                    vigilanciaFinanceiro = FinanceiroVigilanciaHelper.processarGeracaoFinanceiroRequerimento(this.dto, configuracaoVigilanciaFinanceiro, i);
                }
            } else {
                vigilanciaFinanceiro = FinanceiroVigilanciaHelper.processarGeracaoFinanceiroRequerimento(this.dto, configuracaoVigilanciaFinanceiro, FinanceiroVigilanciaHelper.QTD_DEFAULT_PARCELAS);
            }
            VigilanciaFinanceiro saveVF = BOFactory.getBO(CadastroFacade.class).save(vigilanciaFinanceiro);
            vigilanciaFinanceiroList.add(saveVF);
        }

        RequerimentoVigilancia requerimentoVigilancia;
        if(atualizarSituacao) {
            requerimentoVigilancia = atualizarSituacaoAprovacaoRequerimentoVigilancia(this.dto.getRequerimentoVigilancia(), null);
        } else {
            requerimentoVigilancia = dto.getRequerimentoVigilancia();
        }
        gerarOcorrenciaRequerimentoVigilancia(requerimentoVigilancia, vigilanciaFinanceiro);
    }



    private RequerimentoVigilancia atualizarSituacaoAprovacaoRequerimentoVigilancia(RequerimentoVigilancia requerimentoVigilancia, VigilanciaFinanceiro vigilanciaFinanceiro) throws DAOException, ValidacaoException {
        requerimentoVigilancia.setSituacao(RequerimentoVigilancia.Situacao.PENDENTE.value());
        if(atualizarSituacao) {
            if (requerimentoVigilancia.getFlagIsentoMei() != null || (vigilanciaFinanceiro != null && Coalesce.asDouble(vigilanciaFinanceiro.getValor()) == 0D)) {
                if (ConfiguracaoVigilanciaEnum.TipoGestaoRequerimento.FISCAL.value().equals(VigilanciaHelper.getConfiguracaoVigilancia().getFlagTipoGestaoRequerimento()) && !VigilanciaHelper.isRequerimentoAnaliseProjeto(requerimentoVigilancia)) {
                    if (CollectionUtils.isNotNullEmpty(VigilanciaHelper.getFiscaisRequerimentoList(requerimentoVigilancia))) {
                        requerimentoVigilancia.setSituacaoAprovacao(RequerimentoVigilancia.SituacaoAprovacao.APROVADO.value());
                    } else {
                        requerimentoVigilancia.setSituacaoAprovacao(RequerimentoVigilancia.SituacaoAprovacao.AGUARDANDO_INFORMAR_FISCAL.value());
                    }
                } else {
                    requerimentoVigilancia.setSituacaoAprovacao(RequerimentoVigilancia.SituacaoAprovacao.APROVADO.value());
                }
            } else {
                requerimentoVigilancia.setSituacaoAprovacao(RequerimentoVigilancia.SituacaoAprovacao.EMISAO_BOLETO.value());
            }
        } else {
            requerimentoVigilancia.setSituacaoAprovacao(RequerimentoVigilancia.SituacaoAprovacao.EMISAO_BOLETO.value());
        }

        return BOFactory.getBO(CadastroFacade.class).save(requerimentoVigilancia);
    }

    private void gerarOcorrenciaRequerimentoVigilancia(RequerimentoVigilancia requerimentoVigilancia, VigilanciaFinanceiro vigilanciaFinanceiro) throws DAOException, ValidacaoException {
        String descricaoOcorrencia = null;
        if (requerimentoVigilancia.getFlagIsentoMei() != null || (vigilanciaFinanceiro != null && Coalesce.asDouble(vigilanciaFinanceiro.getValor()) == 0D)) {
            String descAuxIsencao = RequerimentoVigilancia.Isencao.MEI.value().equals(requerimentoVigilancia.getFlagIsentoMei()) ? RequerimentoVigilancia.Isencao.MEI.descricao() :
                    Coalesce.asString(requerimentoVigilancia.getDescricaoIsentoOutro());
            descricaoOcorrencia = Bundle.getStringApplication("msg_financeiro_gerado_isento_x", descAuxIsencao);
            BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(descricaoOcorrencia, requerimentoVigilancia, null);
        } else {
            if (ConfiguracaoVigilanciaFinanceiro.FormaCobranca.BOLETO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca())) {
                descricaoOcorrencia = Bundle.getStringApplication("msg_boleto_emitido") + " (Nº " + vigilanciaFinanceiro.getCodigo() + ")";
            } else {
                descricaoOcorrencia = Bundle.getStringApplication("msg_memorando_emitido");
            }
            BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(descricaoOcorrencia, requerimentoVigilancia, null);
        }
    }

    public List<VigilanciaFinanceiro> getVigilanciaFinanceiroList() {
        return vigilanciaFinanceiroList;
    }
}
