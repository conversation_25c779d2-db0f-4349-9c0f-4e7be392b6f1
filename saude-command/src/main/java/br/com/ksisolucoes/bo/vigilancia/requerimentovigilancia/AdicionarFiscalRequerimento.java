package br.com.ksisolucoes.bo.vigilancia.requerimentovigilancia;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaFiscal;
import ch.lambdaj.Lambda;

import java.util.List;

public class AdicionarFiscalRequerimento extends AbstractCommandTransaction {
    private RequerimentoVigilancia rv;
    private List<RequerimentoVigilanciaFiscal> fiscalList;
    private List<RequerimentoVigilanciaFiscal> fiscalExcluirList;

    public AdicionarFiscalRequerimento(RequerimentoVigilancia rv, List<RequerimentoVigilanciaFiscal> fiscalList, List<RequerimentoVigilanciaFiscal> fiscalExcluirList) {
        this.rv = rv;
        this.fiscalExcluirList = fiscalExcluirList;
        this.fiscalList = fiscalList;
    }


    @Override
    public void execute() throws DAOException, ValidacaoException {
        if(getSessao().getUsuario().getProfissional() != null) {
            Lambda.forEach(fiscalList).setProfissionalCadastro(getSessao().getUsuario().getProfissional());
        }

        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaFiscais(rv, fiscalList, fiscalExcluirList);
        rv.setSituacaoAprovacao(RequerimentoVigilancia.SituacaoAprovacao.APROVADO.value());
        BOFactory.save(rv);
    }


    public String getNomeFiscaisList() {
        List<String> nomeFiscaisList = Lambda.extract(fiscalList, Lambda.on(RequerimentoVigilanciaFiscal.class).getProfissional().getNome());
        String nomeFiscais = "";
        if (CollectionUtils.isNotNullEmpty(nomeFiscaisList)) {
            nomeFiscais = Lambda.join(nomeFiscaisList, ", ");
        }
        return nomeFiscais;
    }
}
