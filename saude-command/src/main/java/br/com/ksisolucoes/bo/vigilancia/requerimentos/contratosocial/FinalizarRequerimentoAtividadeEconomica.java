package br.com.ksisolucoes.bo.vigilancia.requerimentos.contratosocial;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.base.BaseEstabelecimentoCnae;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.*;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoAtividadeEconomica;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoAtividadeEconomicaAtividade;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoAtividadeEconomicaCnae;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.hibernate.criterion.Restrictions;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class FinalizarRequerimentoAtividadeEconomica extends AbstractCommandTransaction<FinalizarRequerimentoAtividadeEconomica> {

    private final RequerimentoVigilancia requerimentoVigilancia;

    public FinalizarRequerimentoAtividadeEconomica(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Estabelecimento estabelecimento = (Estabelecimento) getSession().get(Estabelecimento.class, requerimentoVigilancia.getEstabelecimento().getCodigo());

        RequerimentoAtividadeEconomica rae = (RequerimentoAtividadeEconomica) getSession().createCriteria(RequerimentoAtividadeEconomica.class)
                .add(Restrictions.eq(BaseRequerimentoAtividadeEconomica.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                .uniqueResult();

        if (rae != null) {
            List<RequerimentoAtividadeEconomicaAtividade> list = getSession().createCriteria(RequerimentoAtividadeEconomicaAtividade.class)
                    .add(Restrictions.eq(BaseRequerimentoAtividadeEconomicaAtividade.PROP_REQUERIMENTO_ATIVIDADE_ECONOMICA, rae))
                    .list();

            List<EstabelecimentoAtividade> estabelecimentoAtividadeList = new ArrayList<>();

            if (CollectionUtils.isNotNullEmpty(list)) {
                EstabelecimentoAtividade ea;
                for (RequerimentoAtividadeEconomicaAtividade raea : list) {
                    ea = new EstabelecimentoAtividade();
                    ea.setAtividadeEstabelecimento(raea.getAtividadeEstabelecimento());
                    ea.setEstabelecimento(rae.getEstabelecimento());
                    ea.setFlagPrincipal(raea.getFlagPrincipal());
                    ea.setQuantidadeTaxa(raea.getQuantidadeCobrada().doubleValue());
                    ea.setIsentoTaxa(raea.getIsentoTaxa());

                    estabelecimentoAtividadeList.add(ea);
                }
            }

            BOFactory.getBO(VigilanciaFacade.class).deletarEstabelecimentoAtividadesSetores(estabelecimento);

            for (EstabelecimentoAtividade x : estabelecimentoAtividadeList) {
                BOFactory.save(x);
            }

            List<RequerimentoAtividadeEconomicaCnae> listCnae = getSession().createCriteria(RequerimentoAtividadeEconomicaCnae.class)
                    .add(Restrictions.eq(BaseRequerimentoAtividadeEconomicaCnae.PROP_REQUERIMENTO_ATIVIDADE_ECONOMICA, rae))
                    .list();

            List<EstabelecimentoCnae> estabelecimentoCnaeList = new ArrayList<>();
            if (CollectionUtils.isNotNullEmpty(listCnae)) {
                EstabelecimentoCnae estabelecimentoCnae;
                for (RequerimentoAtividadeEconomicaCnae raec : listCnae) {
                    estabelecimentoCnae = new EstabelecimentoCnae();
                    estabelecimentoCnae.setEstabelecimento(rae.getEstabelecimento());
                    estabelecimentoCnae.setCnae(raec.getCnae());
                    estabelecimentoCnae.setTerceirizada(raec.getTerceirizada());

                    estabelecimentoCnaeList.add(estabelecimentoCnae);
                }
            }
            VOUtils.persistirListaVosModificados(EstabelecimentoCnae.class, estabelecimentoCnaeList, new QueryCustom.QueryCustomParameter(BaseEstabelecimentoCnae.PROP_ESTABELECIMENTO, estabelecimento));

            RequerimentoAlvara ultimoRequerimentoAlvaraDeferido = VigilanciaHelper.getUltimoRequerimentoAlvaraDeferido(estabelecimento);

            if (ultimoRequerimentoAlvaraDeferido != null) {
                RequerimentoAlvara novoAlvara = VOUtils.cloneObject(ultimoRequerimentoAlvaraDeferido);
                novoAlvara.setRequerimentoVigilancia(requerimentoVigilancia);
                BOFactory.save(novoAlvara);
            }

            BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoProtocoloSemBPA(requerimentoVigilancia, ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.CONTRATO_SOCIAL_DEFERIMENTO);
        }
    }
}