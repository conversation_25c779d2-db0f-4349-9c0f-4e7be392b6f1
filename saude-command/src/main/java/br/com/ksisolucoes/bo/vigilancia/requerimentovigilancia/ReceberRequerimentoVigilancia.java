/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.vigilancia.requerimentovigilancia;

import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;

/**
 *
 * <AUTHOR>
 */
public class ReceberRequerimentoVigilancia extends AbstractCommandTransaction {

    private RequerimentoVigilancia requerimentoVigilancia;

    public ReceberRequerimentoVigilancia(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        requerimentoVigilancia = (RequerimentoVigilancia) getSession().get(RequerimentoVigilancia.class, requerimentoVigilancia.getCodigo());

        BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia("Requerimento Recebido", requerimentoVigilancia, null);
        requerimentoVigilancia.setSituacao((Long) RequerimentoVigilancia.Situacao.RECEBIDO.value());
        BOFactory.save(requerimentoVigilancia);
    }
}
