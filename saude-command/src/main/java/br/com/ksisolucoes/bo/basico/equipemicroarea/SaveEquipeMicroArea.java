package br.com.ksisolucoes.bo.basico.equipemicroarea;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;

/**
 *
 * <AUTHOR>
 */
public class SaveEquipeMicroArea extends SaveVO<EquipeMicroArea> {

    public SaveEquipeMicroArea(EquipeMicroArea vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getStatus() == null) {
            this.vo.setStatus(RepositoryComponentDefault.ATIVO);
        }
    }

}
