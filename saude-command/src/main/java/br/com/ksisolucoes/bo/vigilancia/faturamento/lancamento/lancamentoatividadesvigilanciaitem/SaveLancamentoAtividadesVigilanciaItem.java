package br.com.ksisolucoes.bo.vigilancia.faturamento.lancamento.lancamentoatividadesvigilanciaitem;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilanciaItem;

/**
 * <AUTHOR>
 */
public class SaveLancamentoAtividadesVigilanciaItem extends SaveVO<LancamentoAtividadesVigilanciaItem> {

    public SaveLancamentoAtividadesVigilanciaItem(LancamentoAtividadesVigilanciaItem vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (vo.getDataCadastro() == null) {
            vo.setDataCadastro(DataUtil.getDataAtual());
        }
        vo.setDataAlteracao(DataUtil.getDataAtual());
        vo.setUsuarioCadastro(SessaoAplicacaoImp.getInstance().getUsuario());
    }

}