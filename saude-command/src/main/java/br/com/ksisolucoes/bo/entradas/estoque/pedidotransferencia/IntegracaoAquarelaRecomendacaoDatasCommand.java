package br.com.ksisolucoes.bo.entradas.estoque.pedidotransferencia;

import br.com.celk.bo.service.rest.integracao.aquarela.prescritor.AquarelaPrescritorDto;
import br.com.celk.bo.service.rest.integracao.aquarela.prescritor.AquarelaRecomendacaoDataDto;
import br.com.ksisolucoes.bo.integracao.aquarela.IntegracaoAquarelaHelper;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

import java.util.List;

public class IntegracaoAquarelaRecomendacaoDatasCommand extends AbstractCommandTransaction {

    private AquarelaPrescritorDto aquarelaPrescritorDto;
    private List<AquarelaRecomendacaoDataDto> recomendacoesDatas;

    public IntegracaoAquarelaRecomendacaoDatasCommand(AquarelaPrescritorDto aquarelaPrescritorDto) {
        this.aquarelaPrescritorDto = aquarelaPrescritorDto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        this.recomendacoesDatas = new IntegracaoAquarelaHelper().consultaRecomendacaoDatas(aquarelaPrescritorDto);
    }


    public List<AquarelaRecomendacaoDataDto> getRecomendacoesDatas() {
        return recomendacoesDatas;
    }
}
