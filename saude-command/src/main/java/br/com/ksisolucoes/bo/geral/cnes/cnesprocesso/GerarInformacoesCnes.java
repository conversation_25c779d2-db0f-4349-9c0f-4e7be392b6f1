package br.com.ksisolucoes.bo.geral.cnes.cnesprocesso;

import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.basico.equipe.RemoverEquipeProfissional;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.integracao.cnes.dto.*;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.io.FileUtils;
import br.com.ksisolucoes.system.consulta.Projections;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.ProfissionalCargaHoraria;
import br.com.ksisolucoes.vo.cadsus.ProfissionalHistorico;
import br.com.ksisolucoes.vo.cadsus.TipoLogradouroCnes;
import br.com.ksisolucoes.vo.geral.cnes.*;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoServicoCadastro;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoServicoClassificacao;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoServicoClassificacaoPk;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vacina.pni.Pni;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import org.apache.commons.lang.StringUtils;
import org.hamcrest.Matchers;
import org.hibernate.Criteria;
import org.hibernate.criterion.Restrictions;
import org.jetbrains.annotations.NotNull;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.io.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.ksisolucoes.vo.basico.base.BaseEmpresa.*;
import static ch.lambdaj.Lambda.*;

/**
 * Created by sulivan on 14/06/17.
 */
public class GerarInformacoesCnes extends AbstractCommandTransaction {

    private static final int MAX_COUNT = 500;
    private static final String NOME_ARQUIVO_CNES_TXT = "lfces";
    private List<String> variacoesNomeArquivo;
    private final Long codigoProcesso;
    private final String caminhoFtp;
    private final RetornoValidacao validacoes = new RetornoValidacao();
    private Pni pni;
    private final int controleTransacao = 0;
    private CnesProcesso.Status status;
    private CnesProcesso cnesProcesso;
    private CnesProcessoRootDTO cnesProcessoRootDTO;
    private String tipoDescricaoEstabelecimento;
    private final Map<String, CnesProcessoEmpresaDadosDTO> mapEstabelecimentos = new HashMap();
    private final Map<String, CnesProcessoProfissionaisDadosDTO> mapProfissionais = new HashMap();
    private final Map<String, CnesProcessoEquipesDadosDTO> mapEquipes = new HashMap();
    BufferedReader buffer = null;
    FileReader reader = null;


    public GerarInformacoesCnes(Long codigoProcesso, String caminhoFtp) {
        this.codigoProcesso = codigoProcesso;
        this.caminhoFtp = caminhoFtp;
    }
    @NotNull
    private CnesProcessoEquipeProfissionalDadosDTO getCnesProcessoEquipeProfissionalDadosDTO(String linha) {
        CnesProcessoEquipeProfissionalDadosDTO cnesProcessoEquipeProfissionalDadosDTO = new CnesProcessoEquipeProfissionalDadosDTO();
        cnesProcessoEquipeProfissionalDadosDTO.setProfissionalId(linha.substring(18, 34).trim());
        cnesProcessoEquipeProfissionalDadosDTO.setCodigoCbo(linha.substring(65, 71).trim());
        cnesProcessoEquipeProfissionalDadosDTO.setIndicaVinculacao(linha.substring(71, 77).trim());
        cnesProcessoEquipeProfissionalDadosDTO.setTipoSusNaoSus(linha.substring(77, 78).trim());
        cnesProcessoEquipeProfissionalDadosDTO.setFlagEquipeMinima(linha.substring(78, 79).trim());
        cnesProcessoEquipeProfissionalDadosDTO.setMicroArea(linha.substring(79, 81).trim());

        if (!linha.substring(81, 91).trim().isEmpty()) {
            cnesProcessoEquipeProfissionalDadosDTO.setDataEntrada(converteStringParaDate(linha.substring(81, 91).trim()));
        }
        if (!linha.substring(91, 101).trim().isEmpty()) {
            cnesProcessoEquipeProfissionalDadosDTO.setDataDesligamento(converteStringParaDate(linha.substring(91, 101).trim()));
        }
        cnesProcessoEquipeProfissionalDadosDTO.setUsuario(linha.substring(111, 123).trim());
        return cnesProcessoEquipeProfissionalDadosDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        try {
            tipoDescricaoEstabelecimento = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("descricaoEstabelecimentoImportacaoCNES");

            status = CnesProcesso.Status.PROCESSANDO;


            File f = File.createTempFile("anexo", "cnes");
            FileUtils.buscarArquivoFtp(this.caminhoFtp, f.getAbsolutePath());

            if (BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("tipoImportacaoCNES").equals("XML")) {

                JAXBContext context = JAXBContext.newInstance(CnesProcessoRootDTO.class);
                Unmarshaller unmarshaller = context.createUnmarshaller();
                InputStream inputStream = new FileInputStream(f);
                cnesProcessoRootDTO = (CnesProcessoRootDTO) unmarshaller.unmarshal(inputStream);
            } else {
                cnesProcessoRootDTO = new CnesProcessoRootDTO();
                converteZIPparaDTORoot(f);
            }

            if (cnesProcessoRootDTO != null && cnesProcessoRootDTO.getEmpresas() != null && CollectionUtils.isNotNullEmpty(cnesProcessoRootDTO.getEmpresas().getDadosEmpresasList())) {

                Loggable.log.info("Iniciando processo numero de empresas = " + cnesProcessoRootDTO.getEmpresas().getDadosEmpresasList().size() + " (CNES)");
                CnesProcessoEmpresa cnesProcessoEmpresa = null;
                CnesProcessoProfissional cnesProcessoProfissional = null;
                CnesProcessoProfissionalVinculo cnesProcessoProfissionalVinculo = null;
                CnesProcessoEquipe cnesProcessoEquipe = null;
                CnesProcessoEquipeProfissional cnesProcessoEquipeProfissional = null;
                CnesProcessoHabilitacao cnesProcessoHabilitacao = null;
                CnesProcessoServicosEspecializados cnesProcessoServicosEspecializados = null;
                List<CnesProcessoHabilitacao> cnesProcessoHabilitacaoList;
                List<CnesProcessoServicosEspecializados> cnesProcessoServicosEspecializadosList;
                CnesProcessoProfissionalVinculoDTO cnesProcessoProfissionalVinculoDTO;
                CnesProcessoEquipeProfissionaisDTO cnesProcessoEquipeProfissionaisDTO;
                List<CnesProcessoProfissionalVinculoDTO> cnesProcessoProfissionalVinculoDTOList;
                List<CnesProcessoEquipeProfissionaisDTO> cnesProcessoEquipeProfissionaisDTOList;

                // Gerar dados empresa
                for (CnesProcessoEmpresaDadosDTO empresaDadosDTO : cnesProcessoRootDTO.getEmpresas().getDadosEmpresasList()) {

                    int numeroProfissionais = 0;
                    if (empresaDadosDTO.getProfissionais() != null && CollectionUtils.isNotNullEmpty(empresaDadosDTO.getProfissionais().getDadosProfissionaisList())){
                        numeroProfissionais = empresaDadosDTO.getProfissionais().getDadosProfissionaisList().size();
                    }
                    Loggable.log.info("Montando empresa Nome: " + empresaDadosDTO.getNomeFantasia() + " ID: " + empresaDadosDTO.getUnidadeId() + " N Profissionais: " + numeroProfissionais + " (CNES)");
                    cnesProcessoEmpresa = gerarCnesProcessoEmpresa(empresaDadosDTO, cnesProcessoEmpresa);

                    // Gerar dados profissional
                    if (empresaDadosDTO.getProfissionais() != null && CollectionUtils.isNotNullEmpty(empresaDadosDTO.getProfissionais().getDadosProfissionaisList())) {
                        cnesProcessoProfissionalVinculoDTOList = new ArrayList<>();
                        for (CnesProcessoProfissionaisDadosDTO profissionaisDadosDTO : empresaDadosDTO.getProfissionais().getDadosProfissionaisList()) {
                            cnesProcessoProfissionalVinculoDTO = new CnesProcessoProfissionalVinculoDTO();
                            cnesProcessoProfissional = gerarCnesProcessoProfissional(profissionaisDadosDTO, cnesProcessoProfissional);
                            cnesProcessoProfissionalVinculoDTO.setCnesProcessoProfissional(cnesProcessoProfissional);

                            // Gerar dados vínculos profissional
                            if (profissionaisDadosDTO.getVinculosProfissionais() != null && CollectionUtils.isNotNullEmpty(profissionaisDadosDTO.getVinculosProfissionais().getDadosVinculosProfissionaisList())) {
                                for (CnesProcessoVinculosProfissionaisDadosDTO vinculosProfissionaisDadosDTO : profissionaisDadosDTO.getVinculosProfissionais().getDadosVinculosProfissionaisList()) {
                                    //Gera o vínculo se for a mesma unidade, a lista possui todos os vinculos do profissional.
                                    if (vinculosProfissionaisDadosDTO.getUnidadeId().equals(empresaDadosDTO.getUnidadeId())) {
                                        cnesProcessoProfissionalVinculo = gerarCnesProcessoProfissionalVinculo(vinculosProfissionaisDadosDTO, cnesProcessoProfissionalVinculo);
                                        cnesProcessoProfissionalVinculo.setUnidadeId(empresaDadosDTO.getUnidadeId());
                                        cnesProcessoProfissionalVinculoDTO.getCnesProcessoProfissionalVinculoList().add(cnesProcessoProfissionalVinculo);
                                    }
                                }
                            }
                            cnesProcessoProfissionalVinculoDTOList.add(cnesProcessoProfissionalVinculoDTO);
                        }
                    } else {
                        cnesProcessoProfissionalVinculoDTOList = new ArrayList<>();
                    }

                    // Gerar dados equipes
                    if (empresaDadosDTO.getEquipes() != null && CollectionUtils.isNotNullEmpty(empresaDadosDTO.getEquipes().getDadosEquipesList())) {
                        cnesProcessoEquipeProfissionaisDTOList = new ArrayList<>();
                        for (CnesProcessoEquipesDadosDTO equipesDadosDTO : empresaDadosDTO.getEquipes().getDadosEquipesList()) {
                            cnesProcessoEquipeProfissionaisDTO = new CnesProcessoEquipeProfissionaisDTO();
                            cnesProcessoEquipe = gerarCnesProcessoEquipes(equipesDadosDTO, cnesProcessoEquipe);
                            cnesProcessoEquipeProfissionaisDTO.setCnesProcessoEquipe(cnesProcessoEquipe);

                            // Gerar dados equipes profissionais
                            if (equipesDadosDTO.getEquipeProfissional() != null && CollectionUtils.isNotNullEmpty(equipesDadosDTO.getEquipeProfissional().getDadosProfissionalEquipeList())) {
                                for (CnesProcessoEquipeProfissionalDadosDTO equipeProfissionalDadosDTO : equipesDadosDTO.getEquipeProfissional().getDadosProfissionalEquipeList()) {
                                    cnesProcessoEquipeProfissional = gerarCnesProcessoEquipes(equipeProfissionalDadosDTO, cnesProcessoEquipeProfissional);
                                    cnesProcessoEquipeProfissionaisDTO.getCnesProcessoEquipeProfissionalList().add(cnesProcessoEquipeProfissional);
                                }
                            }
                            cnesProcessoEquipeProfissionaisDTOList.add(cnesProcessoEquipeProfissionaisDTO);
                        }
                    } else {
                        cnesProcessoEquipeProfissionaisDTOList = new ArrayList<>();
                    }
                    // Gerar dados habilitação
                    if (empresaDadosDTO.getHabilitacao() != null && CollectionUtils.isNotNullEmpty(empresaDadosDTO.getHabilitacao().getDadosHabilitacaoList())) {
                        cnesProcessoHabilitacaoList = new ArrayList<>();
                        for (CnesProcessoHabilitacaoDadosDTO habilitacaoDadosDTO : empresaDadosDTO.getHabilitacao().getDadosHabilitacaoList()) {
                            cnesProcessoHabilitacao = gerarCnesProcessoHabilitacao(habilitacaoDadosDTO, cnesProcessoHabilitacao);
                            cnesProcessoHabilitacaoList.add(cnesProcessoHabilitacao);
                        }
                    } else {
                        cnesProcessoHabilitacaoList = new ArrayList<>();
                    }
                    // Gerar dados serviços especializados
                    if (empresaDadosDTO.getServicosEspecializados() != null && CollectionUtils.isNotNullEmpty(empresaDadosDTO.getServicosEspecializados().getDadosServicosEspecializadosList())) {
                        cnesProcessoServicosEspecializadosList = new ArrayList<>();
                        for (CnesProcessoServicosEspecializadosDadosDTO servicosEspecializadosDadosDTO : empresaDadosDTO.getServicosEspecializados().getDadosServicosEspecializadosList()) {
                            cnesProcessoServicosEspecializados = gerarCnesProcessoServicosEspecializados(servicosEspecializadosDadosDTO, cnesProcessoServicosEspecializados);
                            cnesProcessoServicosEspecializadosList.add(cnesProcessoServicosEspecializados);
                        }
                    } else {
                        cnesProcessoServicosEspecializadosList = new ArrayList<>();
                    }
                    Loggable.log.info("Salvando empresa Nome: " + empresaDadosDTO.getNomeFantasia() + " ID: " + empresaDadosDTO.getUnidadeId() + " (CNES)");
                    // Salva dados empresa
                    cnesProcessoEmpresa = BOFactory.getBO(CadastroFacade.class).newTransactionSave(cnesProcessoEmpresa);

                    // Salva dados profissional
                    if (CollectionUtils.isNotNullEmpty(cnesProcessoProfissionalVinculoDTOList)) {
                        for (CnesProcessoProfissionalVinculoDTO cppv : cnesProcessoProfissionalVinculoDTOList) {
                            cnesProcessoProfissional = LoadManager.getInstance(CnesProcessoProfissional.class)
                                    .addParameter(new QueryCustom.QueryCustomParameter(CnesProcessoProfissional.PROP_PROFISSIONAL_ID, cppv.getCnesProcessoProfissional().getProfissionalId()))
                                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(CnesProcessoProfissional.PROP_CNES_PROCESSO_EMPRESA, CnesProcessoEmpresa.PROP_CNES_PROCESSO), getCnesProcesso()))
                                    .start().getVO();

                            if (cnesProcessoProfissional == null) {
                                cnesProcessoProfissional = cppv.getCnesProcessoProfissional();
                                cnesProcessoProfissional.setCnesProcessoEmpresa(cnesProcessoEmpresa);
                                cnesProcessoProfissional = BOFactory.getBO(CadastroFacade.class).newTransactionSave(cnesProcessoProfissional);
                            }

                            // Salva dados vínculos profissional
                            if (CollectionUtils.isNotNullEmpty(cppv.getCnesProcessoProfissionalVinculoList())) {
                                forEach(cppv.getCnesProcessoProfissionalVinculoList()).setCnesProcessoProfissional(cnesProcessoProfissional);
                                for (CnesProcessoProfissionalVinculo vinculo : cppv.getCnesProcessoProfissionalVinculoList()) {
                                    BOFactory.getBO(CadastroFacade.class).newTransactionSave(vinculo);
                                }
                            }
                        }
                        getSession().flush();
                        getSession().clear();
                    }

                    // Salva dados equipes
                    if (CollectionUtils.isNotNullEmpty(cnesProcessoEquipeProfissionaisDTOList)) {
                        for (CnesProcessoEquipeProfissionaisDTO cpep : cnesProcessoEquipeProfissionaisDTOList) {
                            cnesProcessoEquipe = cpep.getCnesProcessoEquipe();
                            cnesProcessoEquipe.setCnesProcessoEmpresa(cnesProcessoEmpresa);
                            cnesProcessoEquipe = BOFactory.getBO(CadastroFacade.class).newTransactionSave(cnesProcessoEquipe);

                            // Salva dados equipes profissionais
                            if (CollectionUtils.isNotNullEmpty(cpep.getCnesProcessoEquipeProfissionalList())) {
                                forEach(cpep.getCnesProcessoEquipeProfissionalList()).setCnesProcessoEquipe(cnesProcessoEquipe);
                                for (CnesProcessoEquipeProfissional equipeProfissional : cpep.getCnesProcessoEquipeProfissionalList()) {
                                    BOFactory.getBO(CadastroFacade.class).newTransactionSave(equipeProfissional);
                                }
                            }
                        }
                        getSession().flush();
                        getSession().clear();
                    }

                    // Salva dados habilitação
                    if (CollectionUtils.isNotNullEmpty(cnesProcessoHabilitacaoList)) {
                        forEach(cnesProcessoHabilitacaoList).setCnesProcessoEmpresa(cnesProcessoEmpresa);
                        for (CnesProcessoHabilitacao habilitacao : cnesProcessoHabilitacaoList) {
                            BOFactory.getBO(CadastroFacade.class).newTransactionSave(habilitacao);
                        }
                        getSession().flush();
                        getSession().clear();
                    }

                    // Salva dados serviços especializados
                    if (CollectionUtils.isNotNullEmpty(cnesProcessoServicosEspecializadosList)) {
                        forEach(cnesProcessoServicosEspecializadosList).setCnesProcessoEmpresa(cnesProcessoEmpresa);
                        for (CnesProcessoServicosEspecializados servicosEspecializados : cnesProcessoServicosEspecializadosList) {
                            BOFactory.getBO(CadastroFacade.class).newTransactionSave(servicosEspecializados);
                        }
                        getSession().flush();
                        getSession().clear();
                    }

                    Loggable.log.info("Finalizando empresa Nome: " + empresaDadosDTO.getNomeFantasia() + " ID: " + empresaDadosDTO.getUnidadeId() + " (CNES)");

                }

                Loggable.log.info("Iniciando Processamento de empresas (CNES)");
                processarEmpresas();
                Loggable.log.info("Iniciando Processamento de profissionais (CNES)");
                processarProfissionais();
                Loggable.log.info("Iniciando Processamento de Equipes (CNES)");
                processarEquipes();
                Loggable.log.info("Iniciando Processamento de Serviços especializados (CNES)");
                processarServicosEspecializados();
                // TODO avaliar como fazer essa atualização das Descrições, visto que os arquivos TXT não enviam mais
                if(BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("tipoImportacaoCNES").equals("XML")) {
                    Loggable.log.info("Iniciando Update das Descrições das Equipes (CNES)");
                    updateTipoEquipe();
                    Loggable.log.info("Iniciando Update das Descrições da Classificação dos Serviços Especializados (CNES)");
                    updateClassificacaoServicosEspecializados();
                }
                Loggable.log.info("FINALIZADO COM SUCESSO (CNES)");
            }
        } catch (JAXBException e) {
            if (e.getCause() != null) {
                throw new ValidacaoException(e.getCause().toString());
            } else {
                throw new ValidacaoException(e.getMessage());
            }
        } catch (IOException ex) {
            throw new ValidacaoException(ex.getMessage());
        } catch (DAOException e) {
            throw new ValidacaoException(e.getMessage());
        }
    }

    private CnesProcessoEmpresa gerarCnesProcessoEmpresa(CnesProcessoEmpresaDadosDTO empresaDadosDTO, CnesProcessoEmpresa cnesProcessoEmpresa) {
        cnesProcessoEmpresa = new CnesProcessoEmpresa();
        cnesProcessoEmpresa.setCnesProcesso(getCnesProcesso());
        cnesProcessoEmpresa.setUnidadeId(empresaDadosDTO.getUnidadeId());
        cnesProcessoEmpresa.setCnes(empresaDadosDTO.getCnes());
        cnesProcessoEmpresa.setCnpjMantenedora(empresaDadosDTO.getCnpjMantenedora());
        cnesProcessoEmpresa.setPessoaFisicaJuridicaIdentificador(empresaDadosDTO.getPessoaFisicaJuridicaIdentificador());
        cnesProcessoEmpresa.setNivelDep(empresaDadosDTO.getNivelDep());
        cnesProcessoEmpresa.setRazaoSocial(empresaDadosDTO.getRazaoSocial());
        cnesProcessoEmpresa.setNomeFantasia(empresaDadosDTO.getNomeFantasia());
        cnesProcessoEmpresa.setLogradouro(empresaDadosDTO.getLogradouro());
        cnesProcessoEmpresa.setNumero(empresaDadosDTO.getNumero());
        cnesProcessoEmpresa.setComplemento(empresaDadosDTO.getComplemento());
        cnesProcessoEmpresa.setBairro(empresaDadosDTO.getBairro());
        cnesProcessoEmpresa.setCep(empresaDadosDTO.getCep());
        cnesProcessoEmpresa.setRegiaoSaude(empresaDadosDTO.getRegiaoSaude());
        cnesProcessoEmpresa.setDistritoSanitario(empresaDadosDTO.getDistritoSanitario());
        cnesProcessoEmpresa.setDistritoAdministrativo(empresaDadosDTO.getDistritoAdministrativo());
        cnesProcessoEmpresa.setTelefone(StringUtil.getStringMaxPrecision(empresaDadosDTO.getTelefone(), 15));
        cnesProcessoEmpresa.setFax(StringUtil.getStringMaxPrecision(empresaDadosDTO.getFax(), 15));
        cnesProcessoEmpresa.setEmail(empresaDadosDTO.getEmail());
        cnesProcessoEmpresa.setCpf(empresaDadosDTO.getCpf());
        cnesProcessoEmpresa.setCnpj(empresaDadosDTO.getCnpj());
        cnesProcessoEmpresa.setCodigoNaturezaJuridica(empresaDadosDTO.getCodigoNaturezaJuridica());
        cnesProcessoEmpresa.setCodigoAtividade(empresaDadosDTO.getCodigoAtividade());
        cnesProcessoEmpresa.setCodigoCliente(empresaDadosDTO.getCodigoCliente());
        cnesProcessoEmpresa.setNumeroAlvara(empresaDadosDTO.getNumeroAlvara());
        cnesProcessoEmpresa.setDataExpedicaoAlvara(empresaDadosDTO.getDataExpedicaoAlvara());
        cnesProcessoEmpresa.setOrgaoExpedidor(empresaDadosDTO.getOrgaoExpedidor());
        cnesProcessoEmpresa.setTipoUnidadeId(empresaDadosDTO.getTipoUnidadeId());
        cnesProcessoEmpresa.setCodigoTurnoAtendimento(empresaDadosDTO.getCodigoTurnoAtendimento());
        cnesProcessoEmpresa.setSiglaEstado(empresaDadosDTO.getSiglaEstado());
        cnesProcessoEmpresa.setCodigoMunicipio(empresaDadosDTO.getCodigoMunicipio());
        cnesProcessoEmpresa.setStatusMovimentacao(empresaDadosDTO.getStatusMovimentacao());
        cnesProcessoEmpresa.setDataAtualizacao(empresaDadosDTO.getDataAtualizacao());
        cnesProcessoEmpresa.setUsuarioAtualizacao(empresaDadosDTO.getUsuarioAtualizacao());
        cnesProcessoEmpresa.setMicroRegiaoSaude(empresaDadosDTO.getMicroRegiaoSaude());

        return cnesProcessoEmpresa;
    }

    private CnesProcessoProfissional gerarCnesProcessoProfissional(CnesProcessoProfissionaisDadosDTO profissionaisDadosDTO, CnesProcessoProfissional cnesProcessoProfissional) {
        cnesProcessoProfissional = new CnesProcessoProfissional();
        cnesProcessoProfissional.setProfissionalId(profissionaisDadosDTO.getProfissionalId());
        cnesProcessoProfissional.setCpfProfissional(profissionaisDadosDTO.getCpfProfissional());
        cnesProcessoProfissional.setPisPasep(profissionaisDadosDTO.getPisPasep());
        cnesProcessoProfissional.setNomeProfissional(profissionaisDadosDTO.getNomeProfissional());
        cnesProcessoProfissional.setNomeMae(profissionaisDadosDTO.getNomeMae());
        cnesProcessoProfissional.setDataNascimento(profissionaisDadosDTO.getDataNascimento());
        cnesProcessoProfissional.setCodigoMunicipio(profissionaisDadosDTO.getCodigoMunicipio());
        cnesProcessoProfissional.setSexo(profissionaisDadosDTO.getSexo());
        cnesProcessoProfissional.setNumeroLivro(profissionaisDadosDTO.getNumeroLivro());
        cnesProcessoProfissional.setNumeroFolha(profissionaisDadosDTO.getNumeroFolha());
        cnesProcessoProfissional.setNumeroTermo(profissionaisDadosDTO.getNumeroTermo());
        cnesProcessoProfissional.setCodigoOrgaoEmisssor(profissionaisDadosDTO.getCodigoOrgaoEmisssor());
        cnesProcessoProfissional.setDataEmissao(profissionaisDadosDTO.getDataEmissao());
        cnesProcessoProfissional.setNumeroIdentidade(StringUtil.getStringMaxPrecision(profissionaisDadosDTO.getNumeroIdentidade(), 15));
        cnesProcessoProfissional.setSiglaEstado(profissionaisDadosDTO.getSiglaEstado());
        cnesProcessoProfissional.setDataEmissaoIdentidade(profissionaisDadosDTO.getDataEmissaoIdentidade());
        cnesProcessoProfissional.setDataEntrada(profissionaisDadosDTO.getDataEntrada());
        cnesProcessoProfissional.setCtpsNumero(profissionaisDadosDTO.getCtpsNumero());
        cnesProcessoProfissional.setSerie(profissionaisDadosDTO.getSerie());
        cnesProcessoProfissional.setSiglaEstadoCtps(profissionaisDadosDTO.getSiglaEstadoCtps());
        cnesProcessoProfissional.setDataEmissaoCtps(profissionaisDadosDTO.getDataEmissaoCtps());
        cnesProcessoProfissional.setLogradouro(profissionaisDadosDTO.getLogradouro());
        cnesProcessoProfissional.setNumero(profissionaisDadosDTO.getNumero());
        cnesProcessoProfissional.setComplemento(profissionaisDadosDTO.getComplemento());
        cnesProcessoProfissional.setBairro(profissionaisDadosDTO.getBairro());
        cnesProcessoProfissional.setCep(profissionaisDadosDTO.getCep());
        cnesProcessoProfissional.setSiglaUf(profissionaisDadosDTO.getSiglaUf());
        cnesProcessoProfissional.setCodigoEscolar(profissionaisDadosDTO.getCodigoEscolar());
        cnesProcessoProfissional.setCodigoCertidao(profissionaisDadosDTO.getCodigoCertidao());
        cnesProcessoProfissional.setCodigoNacionalidade(profissionaisDadosDTO.getCodigoNacionalidade());
        cnesProcessoProfissional.setNomeCartorio(profissionaisDadosDTO.getNomeCartorio());
        cnesProcessoProfissional.setCodigoBanco(profissionaisDadosDTO.getCodigoBanco());
        cnesProcessoProfissional.setNomePais(profissionaisDadosDTO.getNomePais());
        cnesProcessoProfissional.setNumeroAgencia(profissionaisDadosDTO.getNumeroAgencia());
        cnesProcessoProfissional.setContaCorrente(profissionaisDadosDTO.getContaCorrente());
        cnesProcessoProfissional.setCodigoCns(profissionaisDadosDTO.getCodigoCns());
        cnesProcessoProfissional.setDsTercsih(profissionaisDadosDTO.getDsTercsih());
        cnesProcessoProfissional.setStatus(profissionaisDadosDTO.getStatus());
        cnesProcessoProfissional.setStatusMovimentacao(profissionaisDadosDTO.getStatusMovimentacao());
        cnesProcessoProfissional.setDataAtualizacao(profissionaisDadosDTO.getDataAtualizacao());
        cnesProcessoProfissional.setUsuario(profissionaisDadosDTO.getUsuario());
        cnesProcessoProfissional.setCodigoRaca(profissionaisDadosDTO.getCodigoRaca());
        cnesProcessoProfissional.setTelefone(StringUtil.getStringMaxPrecision(profissionaisDadosDTO.getTelefone(), 15));
        cnesProcessoProfissional.setNomePai(profissionaisDadosDTO.getNomePai());
        cnesProcessoProfissional.setCodigoTipoLogradouro(profissionaisDadosDTO.getCodigoTipoLogradouro());
        cnesProcessoProfissional.setPortaria(profissionaisDadosDTO.getPortaria());
        cnesProcessoProfissional.setDataNaturalizacao(profissionaisDadosDTO.getDataNaturalizacao());
        cnesProcessoProfissional.setCodigoPais(profissionaisDadosDTO.getCodigoPais());

        return cnesProcessoProfissional;
    }

    private CnesProcessoProfissionalVinculo gerarCnesProcessoProfissionalVinculo(CnesProcessoVinculosProfissionaisDadosDTO vinculosProfissionaisDadosDTO, CnesProcessoProfissionalVinculo cnesProcessoProfissionalVinculo) {
        cnesProcessoProfissionalVinculo = new CnesProcessoProfissionalVinculo();
        cnesProcessoProfissionalVinculo.setCodigoCbo(vinculosProfissionaisDadosDTO.getCodigoCbo());
        cnesProcessoProfissionalVinculo.setIndicaVinculacao(vinculosProfissionaisDadosDTO.getIndicaVinculacao());
        cnesProcessoProfissionalVinculo.setQuantidadeCargaHorariaOutro(vinculosProfissionaisDadosDTO.getQuantidadeCargaHorariaOutro());
        cnesProcessoProfissionalVinculo.setQuantidadeCargaHorariaAmbulatorial(vinculosProfissionaisDadosDTO.getQuantidadeCargaHorariaAmbulatorial());
        cnesProcessoProfissionalVinculo.setQuantidadeCargaHorariaHospital(vinculosProfissionaisDadosDTO.getQuantidadeCargaHorariaHospital());
        cnesProcessoProfissionalVinculo.setCodigoConselho(vinculosProfissionaisDadosDTO.getCodigoConselho());
        cnesProcessoProfissionalVinculo.setNumeroRegistro(vinculosProfissionaisDadosDTO.getNumeroRegistro());
        cnesProcessoProfissionalVinculo.setVinculoSus(vinculosProfissionaisDadosDTO.getVinculoSus());
        cnesProcessoProfissionalVinculo.setUsuario(vinculosProfissionaisDadosDTO.getUsuario());

        return cnesProcessoProfissionalVinculo;
    }

    private CnesProcessoEquipe gerarCnesProcessoEquipes(CnesProcessoEquipesDadosDTO equipesDadosDTO, CnesProcessoEquipe cnesProcessoEquipe) {
        cnesProcessoEquipe = new CnesProcessoEquipe();
        cnesProcessoEquipe.setCodigoMunicipio(equipesDadosDTO.getCodigoMunicipio());
        cnesProcessoEquipe.setSequencialEquipe(equipesDadosDTO.getSequencialEquipe());

        cnesProcessoEquipe.setUnidadeId(equipesDadosDTO.getUnidadeId());
        cnesProcessoEquipe.setTipoEquipe(equipesDadosDTO.getTipoEquipe());
        cnesProcessoEquipe.setDescricaoEquipe(equipesDadosDTO.getDescricaoEquipe());
        cnesProcessoEquipe.setNomeReferencia(equipesDadosDTO.getNomeReferencia());

        //Define como gerar para as Areas que não possuem mais o tipo do Segmento.
        cnesProcessoEquipe.setCodigoSegmento("".equals(equipesDadosDTO.getCodigoSegmento()) ? SegmentoTerritorial.CODIGO_GERAL : equipesDadosDTO.getCodigoSegmento());
        cnesProcessoEquipe.setDescricaoSegmento("".equals(equipesDadosDTO.getCodigoSegmento()) ? SegmentoTerritorial.DESCRICAO_GERAL : equipesDadosDTO.getDescricaoSegmento());
        cnesProcessoEquipe.setTipoSegmento("".equals(equipesDadosDTO.getCodigoSegmento()) ? SegmentoTerritorial.TIPO_GERAL : equipesDadosDTO.getTipoSegmento());

        cnesProcessoEquipe.setCodigoArea("".equals(equipesDadosDTO.getCodigoArea()) ? EquipeArea.CODIGO_GERAL : equipesDadosDTO.getCodigoArea());
        cnesProcessoEquipe.setDescricaoArea("".equals(equipesDadosDTO.getDescricaoArea())
                ? "Area " + ("".equals(equipesDadosDTO.getCodigoArea()) ? EquipeArea.DESCRICAO_GERAL : equipesDadosDTO.getCodigoArea())
                : equipesDadosDTO.getDescricaoArea());

        cnesProcessoEquipe.setTipoPopulacaoAssistidaQuilombola(equipesDadosDTO.getTipoPopulacaoAssistidaQuilombola());
        cnesProcessoEquipe.setTipoPopulacaoAssistidaAssentado(equipesDadosDTO.getTipoPopulacaoAssistidaAssentado());
        cnesProcessoEquipe.setTipoPopulacaoAssistidaGeral(equipesDadosDTO.getTipoPopulacaoAssistidaGeral());
        cnesProcessoEquipe.setTipoPopulacaoAssistidaEscola(equipesDadosDTO.getTipoPopulacaoAssistidaEscola());
        cnesProcessoEquipe.setTipoPopulacaoAssistidaPronasci(equipesDadosDTO.getTipoPopulacaoAssistidaPronasci());
        cnesProcessoEquipe.setDataAtivacao(equipesDadosDTO.getDataAtivacao());
        cnesProcessoEquipe.setDataDesativacao(equipesDadosDTO.getDataDesativacao());
        cnesProcessoEquipe.setCodigoTipoDesativacao(equipesDadosDTO.getCodigoTipoDesativacao());
        cnesProcessoEquipe.setCodigoMotivoDesativacao(equipesDadosDTO.getCodigoMotivoDesativacao());
        cnesProcessoEquipe.setUsuario(equipesDadosDTO.getUsuario());
        cnesProcessoEquipe.setCodigoEquipe(equipesDadosDTO.getCodigoEquipe());

        return cnesProcessoEquipe;
    }

    private CnesProcessoEquipeProfissional gerarCnesProcessoEquipes(CnesProcessoEquipeProfissionalDadosDTO equipeProfissionalDadosDTO, CnesProcessoEquipeProfissional cnesProcessoEquipeProfissional) {
        cnesProcessoEquipeProfissional = new CnesProcessoEquipeProfissional();
        cnesProcessoEquipeProfissional.setProfissionalId(equipeProfissionalDadosDTO.getProfissionalId());
        cnesProcessoEquipeProfissional.setCodigoCbo(equipeProfissionalDadosDTO.getCodigoCbo());
        cnesProcessoEquipeProfissional.setIndicaVinculacao(equipeProfissionalDadosDTO.getIndicaVinculacao());
        cnesProcessoEquipeProfissional.setTipoSusNaoSus(equipeProfissionalDadosDTO.getTipoSusNaoSus());
        cnesProcessoEquipeProfissional.setCodigoHoraAmbulatorial(equipeProfissionalDadosDTO.getCodigoHoraAmbulatorial());
        cnesProcessoEquipeProfissional.setCodigoHoraHospital(equipeProfissionalDadosDTO.getCodigoHoraHospital());
        cnesProcessoEquipeProfissional.setCodigoHoraOutro(equipeProfissionalDadosDTO.getCodigoHoraOutro());
        cnesProcessoEquipeProfissional.setFlagEquipeMinima(equipeProfissionalDadosDTO.getFlagEquipeMinima());
        cnesProcessoEquipeProfissional.setMicroArea(equipeProfissionalDadosDTO.getMicroArea());
        cnesProcessoEquipeProfissional.setCnesOutraEquipe(equipeProfissionalDadosDTO.getCnesOutraEquipe());
        cnesProcessoEquipeProfissional.setCodigoMunicipioOutraEquipe(equipeProfissionalDadosDTO.getCodigoMunicipioOutraEquipe());
        cnesProcessoEquipeProfissional.setDataEntrada(equipeProfissionalDadosDTO.getDataEntrada());
        cnesProcessoEquipeProfissional.setDataDesligamento(equipeProfissionalDadosDTO.getDataDesligamento());
        cnesProcessoEquipeProfissional.setCnesAtendComplementar1(equipeProfissionalDadosDTO.getCnesAtendComplementar1());
        cnesProcessoEquipeProfissional.setCnesAtendComplementar2(equipeProfissionalDadosDTO.getCnesAtendComplementar2());
        cnesProcessoEquipeProfissional.setCnesAtendComplementar3(equipeProfissionalDadosDTO.getCnesAtendComplementar3());
        cnesProcessoEquipeProfissional.setCnes1CargaHorariaDiferenciadaSistemaPenitenciario(equipeProfissionalDadosDTO.getCnes1CargaHorariaDiferenciadaSistemaPenitenciario());
        cnesProcessoEquipeProfissional.setCnes1CargaHorariaDiferenciadaSistemaHpp(equipeProfissionalDadosDTO.getCnes1CargaHorariaDiferenciadaSistemaHpp());
        cnesProcessoEquipeProfissional.setCargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedica(equipeProfissionalDadosDTO.getCargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedica());
        cnesProcessoEquipeProfissional.setUsuario(equipeProfissionalDadosDTO.getUsuario());

        return cnesProcessoEquipeProfissional;
    }

    private CnesProcessoHabilitacao gerarCnesProcessoHabilitacao(CnesProcessoHabilitacaoDadosDTO habilitacaoDadosDTO, CnesProcessoHabilitacao cnesProcessoHabilitacao) {
        cnesProcessoHabilitacao = new CnesProcessoHabilitacao();
        cnesProcessoHabilitacao.setCodigoHabilitacao(habilitacaoDadosDTO.getCodigoHabilitacao());
        cnesProcessoHabilitacao.setDescricaoHabilitacao(habilitacaoDadosDTO.getDescricaoHabilitacao());
        cnesProcessoHabilitacao.setCompetenciaInicial(habilitacaoDadosDTO.getCompetenciaInicial());
        cnesProcessoHabilitacao.setCompetenciaFinal(habilitacaoDadosDTO.getCompetenciaFinal());
        cnesProcessoHabilitacao.setQuantidadeLeitos(habilitacaoDadosDTO.getQuantidadeLeitos());
        cnesProcessoHabilitacao.setNumeroPortaria(habilitacaoDadosDTO.getNumeroPortaria());
        cnesProcessoHabilitacao.setDataLancamento(habilitacaoDadosDTO.getDataLancamento());
        cnesProcessoHabilitacao.setUsuario(habilitacaoDadosDTO.getUsuario());

        return cnesProcessoHabilitacao;
    }

    private CnesProcessoServicosEspecializados gerarCnesProcessoServicosEspecializados(CnesProcessoServicosEspecializadosDadosDTO servicosEspecializadosDadosDTO, CnesProcessoServicosEspecializados cnesProcessoServicosEspecializados) {
        cnesProcessoServicosEspecializados = new CnesProcessoServicosEspecializados();
        cnesProcessoServicosEspecializados.setCodigoServico(servicosEspecializadosDadosDTO.getCodigoServico());
        cnesProcessoServicosEspecializados.setDescricaoServico(servicosEspecializadosDadosDTO.getDescricaoServico());
        cnesProcessoServicosEspecializados.setCodigoCaracteristica(servicosEspecializadosDadosDTO.getCodigoCaracteristica());
        cnesProcessoServicosEspecializados.setCodigoServicoAmbulatorial(servicosEspecializadosDadosDTO.getCodigoServicoAmbulatorial());
        cnesProcessoServicosEspecializados.setCodigoServicoAmbulatorialSus(servicosEspecializadosDadosDTO.getCodigoServicoAmbulatorialSus());
        cnesProcessoServicosEspecializados.setCodigoServicoHospitalar(servicosEspecializadosDadosDTO.getCodigoServicoHospitalar());
        cnesProcessoServicosEspecializados.setCodigoServicoHospitalarSus(servicosEspecializadosDadosDTO.getCodigoServicoHospitalarSus());
        cnesProcessoServicosEspecializados.setCodigoClassificacao(servicosEspecializadosDadosDTO.getCodigoClassificacao());
        cnesProcessoServicosEspecializados.setCnpjCpf(servicosEspecializadosDadosDTO.getCnpjCpf());
        cnesProcessoServicosEspecializados.setCodigoEnderecoComplementar(servicosEspecializadosDadosDTO.getCodigoEnderecoComplementar());
        cnesProcessoServicosEspecializados.setDescricaoEnderecoComplementar(servicosEspecializadosDadosDTO.getDescricaoEnderecoComplementar());
        cnesProcessoServicosEspecializados.setDescricaoClassificacao(servicosEspecializadosDadosDTO.getDescricaoClassificacao());
        cnesProcessoServicosEspecializados.setUsuario(servicosEspecializadosDadosDTO.getUsuario());

        return cnesProcessoServicosEspecializados;
    }

    private void processarEmpresas() throws ValidacaoException, DAOException {
        List<CnesProcessoEmpresa> cnesProcessoEmpresaList = LoadManager.getInstance(CnesProcessoEmpresa.class)
                .addParameter(new QueryCustom.QueryCustomParameter(CnesProcessoEmpresa.PROP_CNES_PROCESSO, getCnesProcesso()))
                .addSorter(new QueryCustom.QueryCustomSorter(CnesProcessoEmpresa.PROP_NOME_FANTASIA, BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(cnesProcessoEmpresaList)) {
            /** Atualiza o cnes_processo pra nulo. Serve para saber quais empresas serão processados;
             */

            String update = "WITH empwith AS (   SELECT empresa,unidade_id_cnes   FROM   empresa"
                    + "   WHERE unidade_id_cnes is not null and cd_cnes_processo is not null" + "   LIMIT  " + MAX_COUNT + " ) UPDATE empresa emp"
                    + " SET cd_cnes_processo = null  FROM empwith  WHERE emp.empresa = empwith.empresa ";

            updateCnes(update);

            CnesProcessoOcorrenciaDTO ocorrenciaDTO;
            List<Long> codigoEmpresasList;
            List<Empresa> empresasList;
            boolean empresaDuplicada;
            boolean criarNovaEmpresa;
            Empresa empresa;
            Atividade atividade;
            TurnoAtendimento turnoAtendimento;
            Cidade cidade;
            EmpresaMantenedora empresaMantenedora;
            int cont = 1;

            int contadorLog = 0;
            for (CnesProcessoEmpresa cpe : cnesProcessoEmpresaList) {

                if (contadorLog % 100 == 0) {
                    Loggable.log.info("Processando empresas " + contadorLog + " de " + cnesProcessoEmpresaList.size() + " (CNES)");
                }
                contadorLog++;

                Loggable.log.info("Processando empresa NOME: " + cpe.getNomeFantasia() + " CODIGO: " + cpe.getUnidadeId() + " (CNES)");
                codigoEmpresasList = pesquisaPorEmpresas(cpe);

                empresa = null;
                empresaDuplicada = false;
                /** A variável criarNovaEmpresa deve iniciar com true, pois mesmo codigoEmpresasList retornando apenas um registro, não garante que o id_cnes dela seja o mesmo do XML.
                 *  Portanto se codigoEmpresasList retornar apenas um registro, a variável empresaDuplicada será false e será validado o id_cnes da empresa com o do XML.
                 *  Se for igual, apenas edita a empresa. Caso contrário, cria uma nova confirmando duplicidade de registros;
                 *  Mas se codigoEmpresasList retornar mais de um registro, quer dizer que já é confirmado a duplicidade de registros, só basta saber se deve ou não criar uma empresa nova;
                 *  Portanto é feito um select em codigoEmpresasList, onde o id_cnes da empresa seja o mesmo do XML.
                 *  Se retornar registro quer dizer que não deve criar uma empresa nova, apenas editar seus dados, porém gerar ocorrência de duplicidade.
                 *  Mas se não retornar nada, deve ser criado uma nova empresa com ocorrência de duplicidade;
                 */
                criarNovaEmpresa = true;
                if (CollectionUtils.isNotNullEmpty(codigoEmpresasList)) {
                    criarNovaEmpresa = false;
                    if (codigoEmpresasList.size() > 1) {
                        empresaDuplicada = true;
                        empresasList = LoadManager.getInstance(Empresa.class)
                                .addProperties(new HQLProperties(Empresa.class).getProperties())
                                .addProperties(new HQLProperties(EmpresaMaterial.class, Empresa.PROP_EMPRESA_MATERIAL).getProperties())
                                .addParameter(new QueryCustom.QueryCustomParameter(PROP_CODIGO, BuilderQueryCustom.QueryParameter.IN, codigoEmpresasList))
                                .start().getList();

                        empresa = Lambda.selectUnique(empresasList, Lambda.having(Lambda.on(Empresa.class).getUnidadeIdCnes(), Matchers.equalTo(cpe.getUnidadeId())));
                        //Utilizado para se forem criados mais de um registro manual e nenhum inserido pelo xml
                        if (empresa == null) {
                            empresa = empresasList.get(0);
                        }
                    } else {
                        empresa = LoadManager.getInstance(Empresa.class)
                                .addProperties(new HQLProperties(Empresa.class).getProperties())
                                .addProperties(new HQLProperties(EmpresaMaterial.class, Empresa.PROP_EMPRESA_MATERIAL).getProperties())
                                .setId(codigoEmpresasList.get(0)).start().getVO();
                    }
                }

                ocorrenciaDTO = new CnesProcessoOcorrenciaDTO();
                ocorrenciaDTO.setCnesProcesso(getCnesProcesso());
                ocorrenciaDTO.setCnesProcessoEmpresa(cpe);
                ocorrenciaDTO.setOrigem(CnesProcessoOcorrencia.Origem.EMPRESA.value());

                if (empresa == null && !empresaDuplicada) {
                    empresa = new Empresa();
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.INSERCAO.value());
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_estabelecimento_inserido_razao_social_X_cnes_X_cnpj_cpf_X_id_X", Coalesce.asString(cpe.getNomeFantasia(), cpe.getRazaoSocial()),
                            Coalesce.asString(cpe.getCnes(), Bundle.getStringApplication("rotulo_nao_definido")),
                            !"".equals(cpe.getCnpj()) ? Util.getCnpjFormatado(cpe.getCnpj()) : Coalesce.asString(Util.getCpfFormatado(cpe.getCpf()), Bundle.getStringApplication("rotulo_nao_definido")),
                            Coalesce.asString(cpe.getUnidadeId(), Bundle.getStringApplication("rotulo_nao_definido"))));
                } else if (empresaDuplicada || !cpe.getUnidadeId().equals(empresa.getUnidadeIdCnes())) {
                    if (criarNovaEmpresa) {
                        empresa = new Empresa();
                    }
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.DUPLICADO.value());
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_estabelecimento_duplicado_razao_social_X_cnes_X_cnpj_cpf_X_id_X", Coalesce.asString(cpe.getNomeFantasia(), cpe.getRazaoSocial()),
                            Coalesce.asString(cpe.getCnes(), Bundle.getStringApplication("rotulo_nao_definido")),
                            !"".equals(cpe.getCnpj()) ? Util.getCnpjFormatado(cpe.getCnpj()) : Coalesce.asString(Util.getCpfFormatado(cpe.getCpf()), Bundle.getStringApplication("rotulo_nao_definido")),
                            Coalesce.asString(cpe.getUnidadeId(), Bundle.getStringApplication("rotulo_nao_definido"))));
                } else {
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.EDICAO.value());
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_estabelecimento_editado_razao_social_X_cnes_X_cnpj_cpf_X_id_X", Coalesce.asString(cpe.getNomeFantasia(), cpe.getRazaoSocial()),
                            Coalesce.asString(cpe.getCnes(), Bundle.getStringApplication("rotulo_nao_definido")),
                            !"".equals(cpe.getCnpj()) ? Util.getCnpjFormatado(cpe.getCnpj()) : Coalesce.asString(Util.getCpfFormatado(cpe.getCpf()), Bundle.getStringApplication("rotulo_nao_definido")),
                            Coalesce.asString(cpe.getUnidadeId(), Bundle.getStringApplication("rotulo_nao_definido"))));
                }

                empresa.setUnidadeIdCnes(cpe.getUnidadeId());
                empresa.setCnes(cpe.getCnes());
                // Pessoa Física = 1 - Pessoa Jurídica = 3
                if ("1".equals(cpe.getPessoaFisicaJuridicaIdentificador())) {
                    empresa.setCnpj("".equals(cpe.getCpf()) ? null : cpe.getCpf());
                    empresa.setFlagFisicaJuridica(Pessoa.PESSOA_FISICA);
                } else if ("3".equals(cpe.getPessoaFisicaJuridicaIdentificador())) {
                    empresa.setCnpj("".equals(cpe.getCnpj()) ? null : cpe.getCnpj());
                    empresa.setFlagFisicaJuridica(Pessoa.PESSOA_JURIDICA);
                }
                if (empresa.getCnpj() == null && !"".equals(cpe.getCnpjMantenedora())) {
                    empresa.setCnpj(cpe.getCnpjMantenedora());
                    empresa.setFlagFisicaJuridica(Pessoa.PESSOA_JURIDICA);
                }
                if (!"".equals(cpe.getCnpjMantenedora())) {
                    empresaMantenedora = LoadManager.getInstance(EmpresaMantenedora.class)
                            .setId(cpe.getCnpjMantenedora()).start().getVO();
                    if (empresaMantenedora != null) {
                        empresa.setEmpresaMantenedora(empresaMantenedora);
                    }
                }

                if (RepositoryComponentDefault.RAZAO_SOCIAL.equals(tipoDescricaoEstabelecimento)) {
                    empresa.setDescricao(cpe.getRazaoSocial());
                    empresa.setFantasia(cpe.getNomeFantasia());
                } else {
                    empresa.setDescricao(cpe.getNomeFantasia());
                    empresa.setFantasia(cpe.getRazaoSocial());
                }

                if (StringUtils.isNotBlank(cpe.getLogradouro())) empresa.setRua(cpe.getLogradouro());
                if (StringUtils.isNotBlank(cpe.getNumero())) empresa.setNumero(cpe.getNumero());
                if (StringUtils.isNotBlank(cpe.getComplemento())) empresa.setComplemento(cpe.getComplemento());
                if (StringUtils.isNotBlank(cpe.getBairro())) empresa.setBairro(cpe.getBairro());
                if (StringUtils.isNotBlank(cpe.getCep())) empresa.setCep(cpe.getCep());
                if (StringUtils.isNotBlank(cpe.getDistritoAdministrativo())) empresa.setDistritoAdministrador(cpe.getDistritoAdministrativo());
                if (StringUtils.isNotBlank(cpe.getDistritoSanitario())) empresa.setDistritoSanitario(cpe.getDistritoSanitario());
                if (StringUtils.isNotBlank(cpe.getTelefone())) empresa.setTelefone(cpe.getTelefone());
                if (StringUtils.isNotBlank(cpe.getFax())) empresa.setFax(cpe.getFax());
                if (StringUtils.isNotBlank(cpe.getEmail())) empresa.setEmail(cpe.getEmail());
                empresa.setAtivo(RepositoryComponentDefault.SIM);
                if (cpe.getCodigoTurnoAtendimento() != null && !"".equals(cpe.getCodigoTurnoAtendimento())) {
                    turnoAtendimento = LoadManager.getInstance(TurnoAtendimento.class)
                            .setId(new Long(cpe.getCodigoTurnoAtendimento())).start().getVO();
                    if (turnoAtendimento != null) {
                        empresa.setTurnoAtendimento(turnoAtendimento);
                    }
                } else {
                    empresa.setTurnoAtendimento(null);
                }
                if (cpe.getCodigoCliente() != null && !"".equals(cpe.getCodigoCliente())) {
                    empresa.setCodigoFluxoClientela(new Long(cpe.getCodigoCliente()));
                } else {
                    empresa.setCodigoFluxoClientela(null);
                }
                empresa.setCnesProcesso(getCnesProcesso());
                if (!"".equals(Coalesce.asString(cpe.getTipoUnidadeId()))) {
                    atividade = LoadManager.getInstance(Atividade.class)
                            .setId(new Long(cpe.getTipoUnidadeId())).start().getVO();
                    if (atividade != null) {
                        empresa.setAtividade(atividade);
                    }
                }
                if (!"".equals(Coalesce.asString(cpe.getCodigoMunicipio()))) {
                    cidade = LoadManager.getInstance(Cidade.class)
                            .setId(new Long(cpe.getCodigoMunicipio())).start().getVO();
                    if (cidade != null) {
                        empresa.setCidade(cidade);
                    }
                }

                empresa = BOFactory.getBO(CadastroFacade.class).newTransactionSave(empresa);

                ocorrenciaDTO.setEmpresa(empresa);
                BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);

                if (cont == MAX_COUNT) {
                    getSession().flush();
                    getSession().clear();
                    cont = 0;
                }
                cont++;
            }

            // Gera as ocorrências de deleção para confirmação do usuário posteriormente;
            List<Empresa> empresaList = LoadManager.getInstance(Empresa.class)
                    .addProperties(new HQLProperties(Empresa.class).getProperties())
                    .addProperties(new HQLProperties(EmpresaMaterial.class, Empresa.PROP_EMPRESA_MATERIAL).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(PROP_UNIDADE_ID_CNES, BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                    .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CNES_PROCESSO, BuilderQueryCustom.QueryParameter.IS_NULL))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(empresaList)) {
                for (Empresa e : empresaList) {
                    ocorrenciaDTO = new CnesProcessoOcorrenciaDTO();
                    ocorrenciaDTO.setCnesProcesso(getCnesProcesso());
                    ocorrenciaDTO.setOrigem(CnesProcessoOcorrencia.Origem.EMPRESA.value());
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.INATIVACAO.value());
                    ocorrenciaDTO.setEmpresa(e);
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_estabelecimento_inativado_razao_social_X_cnes_X_cnpj_cpf_X_id_X", e.getDescricao(),
                            Coalesce.asString(e.getCnes(), Bundle.getStringApplication("rotulo_nao_definido")),
                            Pessoa.PESSOA_JURIDICA.equals(e.getFlagFisicaJuridica()) ? Util.getCnpjFormatado(e.getCnpj()) : Coalesce.asString(Util.getCpfFormatado(e.getCnpj()), Bundle.getStringApplication("rotulo_nao_definido")),
                            Coalesce.asString(e.getUnidadeIdCnes(), Bundle.getStringApplication("rotulo_nao_definido"))));
                    BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);

                    e.setAtivo(RepositoryComponentDefault.NAO);
                    BOFactory.getBO(CadastroFacade.class).newTransactionSave(e);

                    if (cont == MAX_COUNT) {
                        getSession().flush();
                        getSession().clear();
                        cont = 0;
                    }
                    cont++;
                }
            }

            if (cont <= MAX_COUNT) {
                getSession().flush();
                getSession().clear();
            }
        }
    }

    private List<Long> pesquisaPorEmpresas(CnesProcessoEmpresa cpe) {
        List<Long> empresas = getEmpresaPorUnidadeIdCnes(cpe);
        if (!CollectionUtils.isNotNullEmpty(empresas)) {
            empresas = getEmpresaPorCnes(cpe);
        }
        if (!CollectionUtils.isNotNullEmpty(empresas)) {
            empresas = getEmpresaPorCNPJ(cpe);
        }
        if (CollectionUtils.isNotNullEmpty(empresas) && empresas.size() > 1) {
            List<Long> empresasReferencia = getEmpresasPorReferencia(cpe);
            if (CollectionUtils.isNotNullEmpty(empresasReferencia)) {
                empresas = empresasReferencia;
            }
        }

        return empresas;
    }

    private List<Long> getEmpresasPorReferencia(CnesProcessoEmpresa cpe) {
        Criteria criteria = getSession().createCriteria(Empresa.class)
                .setProjection(Projections.property(PROP_CODIGO))
                .add(Restrictions.eq(PROP_REFERENCIA, cpe.getCodigo().toString()));
        return criteria.list();
    }

    private List<Long> getEmpresaPorUnidadeIdCnes(CnesProcessoEmpresa cpe) {
        Criteria criteria = getSession().createCriteria(Empresa.class)
                .setProjection(Projections.property(PROP_CODIGO))
                .add(Restrictions.eq(PROP_UNIDADE_ID_CNES, cpe.getUnidadeId()));
        return criteria.list();
    }

    private List<Long> getEmpresaPorCnes(CnesProcessoEmpresa cpe) {
        Criteria criteria = getSession().createCriteria(Empresa.class)
                .setProjection(Projections.property(Empresa.PROP_CODIGO))
                .add(Restrictions.eq(Empresa.PROP_CNES, cpe.getCnes()));
        return criteria.list();
    }

    private List<Long> getEmpresaPorCNPJ(CnesProcessoEmpresa cpe) {
        Criteria criteria = getSession().createCriteria(Empresa.class)
                .setProjection(Projections.property(PROP_CODIGO))
                .add(Restrictions.eq(PROP_CNPJ, cpe.getCnpj()));
        return criteria.list();
    }

    private void processarProfissionais() throws ValidacaoException, DAOException {
        List<CnesProcessoProfissional> cnesProcessoProfissionalList = LoadManager.getInstance(CnesProcessoProfissional.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(CnesProcessoProfissional.PROP_CNES_PROCESSO_EMPRESA, CnesProcessoEmpresa.PROP_CNES_PROCESSO), getCnesProcesso()))
                .addSorter(new QueryCustom.QueryCustomSorter(CnesProcessoProfissional.PROP_NOME_PROFISSIONAL, BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(cnesProcessoProfissionalList)) {
            /** Atualiza o cnes_processo pra nulo. Serve para saber quais profissionais serão processados;*/
            String update = "WITH profwith AS ("
                    + "SELECT cd_profissional FROM profissional  "
                    + " WHERE profissional_id_cnes IS NOT NULL AND cd_cnes_processo IS NOT NULL LIMIT " + MAX_COUNT + " ) "
                    + " UPDATE profissional prof "
                    + "SET cd_cnes_processo = null FROM profwith WHERE prof.cd_profissional = profwith.cd_profissional";

            updateCnes(update);

            /** Atualiza o cnes_processo pra nulo. Serve para saber quais vínculos serão processados;*/
            update = "WITH pchwith AS ( select cd_prof_carga_horaria from profissional_carga_horaria "
                    + "WHERE cd_cnes_processo is not null and exists(SELECT 1 FROM profissional p where p.cd_profissional = cd_profissional and p.profissional_id_cnes is not null) "
                    + "limit " + MAX_COUNT + " ) "
                    + "update profissional_carga_horaria pch SET cd_cnes_processo = null from pchwith "
                    + "where pch.cd_prof_carga_horaria  = pchwith.cd_prof_carga_horaria";

            updateCnes(update);

            CnesProcessoOcorrenciaDTO ocorrenciaDTO;
            List<Long> codigoProfissionaisList;
            List<Profissional> profissionaisList;
            boolean profissionalDuplicado;
            boolean criarNovoProfissional;
            Profissional profissional;
            Cidade cidade;
            CepBrasil cepBrasil;
            TipoLogradouroCnes tipoLogradouroCnes;
            int cont = 1;
            int contadorLog = 0;
            for (CnesProcessoProfissional cpp : cnesProcessoProfissionalList) {

                if (contadorLog % 100 == 0) {
                    Loggable.log.info("Processando profissionais cnesProcessoProfissionalList " + contadorLog + " de " + cnesProcessoProfissionalList.size() + " (CNES)");
                }
                contadorLog++;

                codigoProfissionaisList = getSession().createCriteria(Profissional.class)
                        .setProjection(Projections.property(Profissional.PROP_CODIGO))
                        .add(Restrictions.or(
                                Restrictions.eq(Profissional.PROP_CODIGO_CNS, "".equals(cpp.getCodigoCns()) ? null : cpp.getCodigoCns()),
                                Restrictions.eq(Profissional.PROP_CPF, "".equals(cpp.getCpfProfissional()) ? null : cpp.getCpfProfissional()),
                                Restrictions.eq(Profissional.PROP_PROFISSIONAL_ID_CNES, cpp.getProfissionalId())))
                        .list();

                profissional = null;
                profissionalDuplicado = false;
                /** A variável criarNovoProfissional deve iniciar com true, pois mesmo codigoProfissionaisList retornando apenas um registro, não garante que o id_cnes dele seja o mesmo do XML.
                 *  Portanto se codigoProfissionaisList retornar apenas um registro, a variável profissionalDuplicado será false e será validado o id_cnes do profissional com o do XML.
                 *  Se for igual, apenas edita o profissional. Caso contrário, cria uma nova confirmando duplicidade de registros;
                 *  Mas se codigoProfissionaisList retornar mais de um registro, quer dizer que já é confirmado a duplicidade de registros, só basta saber se deve ou não criar um profissional novo;
                 *  Portanto é feito um select em codigoProfissionaisList, onde o id_cnes do profissional seja o mesmo do XML.
                 *  Se retornar registro quer dizer que não deve criar um profissional novo, apenas editar seus dados, porém gerar ocorrência de duplicidade.
                 *  Mas se não retornar nada, deve ser criado um novo profissional com ocorrência de duplicidade;
                 */
                criarNovoProfissional = true;
                if (CollectionUtils.isNotNullEmpty(codigoProfissionaisList)) {
                    criarNovoProfissional = false;
                    if (codigoProfissionaisList.size() > 1) {
                        profissionalDuplicado = true;
                        profissionaisList = LoadManager.getInstance(Profissional.class)
                                .addParameter(new QueryCustom.QueryCustomParameter(PROP_CODIGO, BuilderQueryCustom.QueryParameter.IN, codigoProfissionaisList))
                                .start().getList();
                        profissional = Lambda.selectUnique(profissionaisList, Lambda.having(Lambda.on(Profissional.class).getProfissionalIdCnes(), Matchers.equalTo(cpp.getProfissionalId())));
                        //Utilizado para se forem criados mais de um registro manual e nenhum inserido pelo xml
                        if (profissional == null) {
                            profissional = profissionaisList.get(0);
                        }
                    } else {
                        profissional = LoadManager.getInstance(Profissional.class).setId(codigoProfissionaisList.get(0)).start().getVO();
                    }
                }

                ocorrenciaDTO = new CnesProcessoOcorrenciaDTO();
                ocorrenciaDTO.setCnesProcesso(getCnesProcesso());
                ocorrenciaDTO.setCnesProcessoProfissional(cpp);
                ocorrenciaDTO.setOrigem(CnesProcessoOcorrencia.Origem.PROFISSIONAL.value());

                if (profissional == null && !profissionalDuplicado) {
                    profissional = new Profissional();
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.INSERCAO.value());
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_profissional_inserido_nome_X_cns_X_cpf_X_id_X", cpp.getNomeProfissional(),
                            Coalesce.asString(Util.getCNSFormatado(cpp.getCodigoCns()), Bundle.getStringApplication("rotulo_nao_definido")),
                            Coalesce.asString(Util.getCpfFormatado(cpp.getCpfProfissional()), Bundle.getStringApplication("rotulo_nao_definido")),
                            Coalesce.asString(cpp.getProfissionalId(), Bundle.getStringApplication("rotulo_nao_definido"))));
                } else if (profissionalDuplicado || !cpp.getProfissionalId().equals(profissional.getProfissionalIdCnes())) {
                    if (criarNovoProfissional) {
                        profissional = new Profissional();
                    }
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.DUPLICADO.value());
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_profissional_duplicado_nome_X_cns_X_cpf_X_id_X", cpp.getNomeProfissional(),
                            Coalesce.asString(Util.getCNSFormatado(cpp.getCodigoCns()), Bundle.getStringApplication("rotulo_nao_definido")),
                            Coalesce.asString(Util.getCpfFormatado(cpp.getCpfProfissional()), Bundle.getStringApplication("rotulo_nao_definido")),
                            Coalesce.asString(cpp.getProfissionalId(), Bundle.getStringApplication("rotulo_nao_definido"))));
                } else {
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.EDICAO.value());
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_profissional_editado_nome_X_cns_X_cpf_X_id_X", cpp.getNomeProfissional(),
                            Coalesce.asString(Util.getCNSFormatado(cpp.getCodigoCns()), Bundle.getStringApplication("rotulo_nao_definido")),
                            Coalesce.asString(Util.getCpfFormatado(cpp.getCpfProfissional()), Bundle.getStringApplication("rotulo_nao_definido")),
                            Coalesce.asString(cpp.getProfissionalId(), Bundle.getStringApplication("rotulo_nao_definido"))));
                }

                profissional.setProfissionalIdCnes(cpp.getProfissionalId());
                if(StringUtils.isNotBlank(cpp.getCpfProfissional())) profissional.setCpf(cpp.getCpfProfissional());
                if (cpp.getCodigoCns() == null || "".equals(cpp.getCodigoCns())) {
                    profissional.setNaoPossuiCns(RepositoryComponentDefault.SIM_LONG);
                    profissional.setCodigoCns(null);
                } else {
                    profissional.setCodigoCns(cpp.getCodigoCns());
                }
                if(StringUtils.isNotBlank(cpp.getNomeProfissional())) profissional.setNome(cpp.getNomeProfissional());
                if(StringUtils.isNotBlank(cpp.getNomeMae())) profissional.setNomeMae(cpp.getNomeMae());
                if(StringUtils.isNotBlank(cpp.getNomePai())) profissional.setNomePai(cpp.getNomePai());
                if(cpp.getDataNascimento() != null) profissional.setDataNascimento(cpp.getDataNascimento());
                if (cpp.getSexo() != null) {
                    profissional.setSexo(cpp.getSexo());
                }
                if(StringUtils.isNotBlank(cpp.getTelefone())) profissional.setTelefone(cpp.getTelefone());
                //profissional.setUnidadeFederacaoConselhoRegistro(cpp.getSiglaEstado());
                if(StringUtils.isNotBlank(cpp.getLogradouro())) profissional.setRua(cpp.getLogradouro());
                if(StringUtils.isNotBlank(cpp.getNumero())) profissional.setRuaNumero(cpp.getNumero());
                if(StringUtils.isNotBlank(cpp.getBairro())) profissional.setBairro(cpp.getBairro());
                if(StringUtils.isNotBlank(cpp.getComplemento())) profissional.setComplemento(cpp.getComplemento());
                if(StringUtils.isNotBlank(cpp.getCep())) profissional.setCep(cpp.getCep());
                profissional.setCnesProcesso(getCnesProcesso());
//                profissional.setNumeroRegistro(cpp.getNumeroIdentidade());
//                profissional.setDataRegistro(cpp.getDataEmissaoIdentidade());
                if (!"".equals(Coalesce.asString(cpp.getCodigoMunicipio()))) {
                    cidade = LoadManager.getInstance(Cidade.class)
                            .setId(new Long(cpp.getCodigoMunicipio())).start().getVO();
                    if (cidade != null) {
                        profissional.setCidadeNascimento(cidade);
                        profissional.setCidade(cidade);
                    }
                }
                if (!"".equals(Coalesce.asString(cpp.getCep()))) {
                    cepBrasil = LoadManager.getInstance(CepBrasil.class)
                            .addProperties(new HQLProperties(CepBrasil.class).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(CepBrasil.PROP_CEP, new Long(cpp.getCep())))
                            .start().getVO();

                    if (cepBrasil != null) {
                        profissional.setCidade(cepBrasil.getCidade());
                    }
                }

                if (!"".equals(Coalesce.asString(cpp.getCodigoTipoLogradouro()))) {
                    tipoLogradouroCnes = LoadManager.getInstance(TipoLogradouroCnes.class)
                            .setId(cpp.getCodigoTipoLogradouro()).start().getVO();
                    if (tipoLogradouroCnes != null) {
                        profissional.setTipoLogradouro(tipoLogradouroCnes);
                    }
                }
                profissional = BOFactory.getBO(CadastroFacade.class).newTransactionSave(profissional);

                ocorrenciaDTO.setProfissional(profissional);
                BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);

                processarVinculosProfissionais(cpp.getCodigo(), profissional, cpp.getDataEntrada(), profissional.getProfissionalIdCnes());

                if (cont == MAX_COUNT) {
                    getSession().flush();
                    getSession().clear();
                    cont = 0;
                }
                cont++;
            }

            // Gera as ocorrências de deleção para confirmação do usuário posteriormente;
            List<Profissional> profissionalList = LoadManager.getInstance(Profissional.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(Profissional.PROP_PROFISSIONAL_ID_CNES, BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                    .addParameter(new QueryCustom.QueryCustomParameter(Profissional.PROP_CNES_PROCESSO, BuilderQueryCustom.QueryParameter.IS_NULL))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(profissionalList)) {
                List<EquipeProfissional> equipeProfissionalList = null;
                contadorLog = 0;
                for (Profissional p : profissionalList) {


                    if (contadorLog % 100 == 0) {
                        Loggable.log.info("Gera as ocorrências de deleção para confirmação do usuário posteriormente - Processando profissionais " + contadorLog + " de " + profissionalList.size() + " (CNES)");
                    }
                    contadorLog++;

                    ocorrenciaDTO = new CnesProcessoOcorrenciaDTO();
                    ocorrenciaDTO.setCnesProcesso(getCnesProcesso());
                    ocorrenciaDTO.setOrigem(CnesProcessoOcorrencia.Origem.PROFISSIONAL.value());
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.INATIVACAO.value());
                    ocorrenciaDTO.setProfissional(p);
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_profissional_inativado_nome_X_cns_X_cpf_X_id_X", p.getNome(),
                            Util.getCNSFormatado(p.getCodigoCns()), Util.getCpfFormatado(p.getCpf()),
                            Coalesce.asString(p.getProfissionalIdCnes(), Bundle.getStringApplication("rotulo_nao_definido"))));
                    BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);

                    List<EquipeProfissional> equipeProfissionais = LoadManager.getInstance(EquipeProfissional.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_PROFISSIONAL, p))
                            .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_STATUS, EquipeProfissional.STATUS_ATIVO))
                            .start().getList();

                    if (CollectionUtils.isNotNullEmpty(equipeProfissionalList)) {
                        for (EquipeProfissional ep : equipeProfissionalList) {
                            new RemoverEquipeProfissional(ep).start();
                        }
                    }

                    if (cont == MAX_COUNT) {
                        getSession().flush();
                        getSession().clear();
                        cont = 0;
                    }
                    cont++;
                }
            }

            // Gera as ocorrências de deleção para confirmação do usuário posteriormente;
            List<ProfissionalCargaHoraria> profissionalCargaHorariaList = LoadManager.getInstance(ProfissionalCargaHoraria.class)
                    .addProperties(new HQLProperties(ProfissionalCargaHoraria.class).getProperties())
                    .addProperties(new HQLProperties(Empresa.class, ProfissionalCargaHoraria.PROP_EMPRESA).getProperties())
                    .addProperties(new HQLProperties(Profissional.class, ProfissionalCargaHoraria.PROP_PROFISSIONAL).getProperties())
                    .addProperties(new HQLProperties(TabelaCbo.class, ProfissionalCargaHoraria.PROP_TABELA_CBO).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProfissionalCargaHoraria.PROP_PROFISSIONAL, Profissional.PROP_PROFISSIONAL_ID_CNES), BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                    .addParameter(new QueryCustom.QueryCustomParameter(ProfissionalCargaHoraria.PROP_CNES_PROCESSO, BuilderQueryCustom.QueryParameter.IS_NULL))
                    .addParameter(new QueryCustom.QueryCustomParameter(ProfissionalCargaHoraria.PROP_PROFISSIONAL_ID_CNES, BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(profissionalCargaHorariaList)) {
                List<CnesProcessoOcorrencia> cnesProcessoOcorrenciaList;
                TabelaCbo tabelaCbo;
                contadorLog = 0;
                for (ProfissionalCargaHoraria pch : profissionalCargaHorariaList) {

                    if (contadorLog % 100 == 0) {
                        Loggable.log.info("Gera as ocorrências de deleção para confirmação do usuário posteriormente - Processando profissionais carga horaria " + contadorLog + " de " + profissionalCargaHorariaList.size() + " (CNES)");
                    }
                    contadorLog++;

                    if (pch.getEmpresa().getUnidadeIdCnes() == null) {
                        continue;
                    }
                    ocorrenciaDTO = new CnesProcessoOcorrenciaDTO();
                    ocorrenciaDTO.setCnesProcesso(getCnesProcesso());
                    ocorrenciaDTO.setOrigem(CnesProcessoOcorrencia.Origem.VINCULO_PROFISSIONAL.value());
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.EXCLUSAO.value());
                    ocorrenciaDTO.setProfissional(pch.getProfissional());
                    ocorrenciaDTO.setProfissionalCargaHoraria(pch);
                    tabelaCbo = LoadManager.getInstance(TabelaCbo.class).setId(pch.getTabelaCbo().getCbo()).start().getVO();
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_vinculo_profissional_deletado_cbo_X_profissional_X_empresa_X", tabelaCbo.getDescricaoFormatado(),
                            pch.getProfissional().getNome(), pch.getEmpresa().getDescricao()));

                    BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);
                    gerarProfissionalHistorico(pch, true, tabelaCbo);

                    cnesProcessoOcorrenciaList = LoadManager.getInstance(CnesProcessoOcorrencia.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(CnesProcessoOcorrencia.PROP_CODIGO_PROFISSIONAL_CARGA_HORARIA, pch.getCodigo()))
                            .start().getList();

                    if (CollectionUtils.isNotNullEmpty(cnesProcessoOcorrenciaList)) {
                        for (CnesProcessoOcorrencia cpo : cnesProcessoOcorrenciaList) {
                            cpo.setCodigoProfissionalCargaHoraria(null);
                            BOFactory.getBO(CadastroFacade.class).newTransactionSave(cpo);
                        }
                    }

                    BOFactory.newTransactionDelete(pch);

                    if (cont == MAX_COUNT) {
                        getSession().flush();
                        getSession().clear();
                        cont = 0;
                    }
                    cont++;
                }
            }

            if (cont <= MAX_COUNT) {
                getSession().flush();
                getSession().clear();
            }
        }
    }

    private void processarVinculosProfissionais(Long codigoCnesProcessoProfissional, Profissional profissional, Date dataEntrada, String profissionalIdCnes) throws ValidacaoException, DAOException {
        List<CnesProcessoProfissionalVinculo> cnesProcessoServicosList = LoadManager.getInstance(CnesProcessoProfissionalVinculo.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(CnesProcessoProfissionalVinculo.PROP_CNES_PROCESSO_PROFISSIONAL, CnesProcessoProfissional.PROP_CODIGO), codigoCnesProcessoProfissional))
                .addSorter(new QueryCustom.QueryCustomSorter(CnesProcessoProfissionalVinculo.PROP_CODIGO_CBO, BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(cnesProcessoServicosList)) {
            CnesProcessoOcorrenciaDTO ocorrenciaDTO;
            List<ProfissionalCargaHoraria> profissionalCargaHorariaList;
            ProfissionalCargaHoraria profissionalCargaHoraria = null;
            OrgaoEmissor orgaoEmissor;
            TabelaCbo tabelaCbo;
            Empresa empresa;
            int cont = 1;
            boolean salvarProfissional = false;
            boolean atualizarConselhoClasse = true;
            boolean atualizarNumeroRegistro = true;

            for (CnesProcessoProfissionalVinculo cppv : cnesProcessoServicosList) {
                empresa = getEmpresa(cppv.getUnidadeId());

                ocorrenciaDTO = new CnesProcessoOcorrenciaDTO();
                ocorrenciaDTO.setCnesProcesso(getCnesProcesso());
                ocorrenciaDTO.setProfissional(profissional);
                ocorrenciaDTO.setOrigem(CnesProcessoOcorrencia.Origem.VINCULO_PROFISSIONAL.value());

                if (empresa == null) {
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.ERRO.value());
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("msg_estabelecimento_nao_cadastrado_id_cnes_X", cppv.getUnidadeId()));

                    BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);
                    continue;
                }

                profissionalCargaHorariaList = LoadManager.getInstance(ProfissionalCargaHoraria.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(ProfissionalCargaHoraria.PROP_EMPRESA, empresa))
                        .addParameter(new QueryCustom.QueryCustomParameter(ProfissionalCargaHoraria.PROP_PROFISSIONAL, profissional))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProfissionalCargaHoraria.PROP_TABELA_CBO, TabelaCbo.PROP_CBO), cppv.getCodigoCbo()))
                        .start().getList();

                tabelaCbo = LoadManager.getInstance(TabelaCbo.class).setId(cppv.getCodigoCbo()).start().getVO();
                if (tabelaCbo == null) {
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.ERRO.value());
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("msg_cbo_invalido", cppv.getCodigoCbo()));

                    BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);
                    continue;
                }

                if (CollectionUtils.isNotNullEmpty(profissionalCargaHorariaList)) {
                    if (profissionalCargaHorariaList.size() > 1L) {
                        ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.ERRO.value());
                        ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_erro_edicao_vinculo_profissional_vinculo_duplicado_cbo_X_profissional_X_empresa_X", tabelaCbo.getDescricaoFormatado(), profissional.getNome(), empresa.getDescricao()));

                        BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);
                        continue;
                    } else {
                        profissionalCargaHoraria = profissionalCargaHorariaList.get(0);
                    }
                } else {
                    profissionalCargaHoraria = null;
                }

                if (!"".equals(Coalesce.asString(cppv.getCodigoConselho())) && (profissional.getConselhoClasse() == null || atualizarConselhoClasse)) {
                    orgaoEmissor = LoadManager.getInstance(OrgaoEmissor.class).setId(new Long(cppv.getCodigoConselho())).start().getVO();

                    if (orgaoEmissor == null) {
                        ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.ERRO.value());
                        ocorrenciaDTO.setDescricao(Bundle.getStringApplication("msg_conselho_classe_invalido", new Long(cppv.getCodigoConselho())));

                        BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);
                        continue;
                    } else {
                        atualizarConselhoClasse = false;
                        salvarProfissional = true;
                        profissional.setConselhoClasse(orgaoEmissor);
                    }
                }

                if (!"".equals(Coalesce.asString(cppv.getNumeroRegistro())) && (profissional.getNumeroRegistro() == null || atualizarNumeroRegistro)) {
                    atualizarNumeroRegistro = false;
                    salvarProfissional = true;
                    profissional.setNumeroRegistro(cppv.getNumeroRegistro());
                }

                if (salvarProfissional) {
                    profissional = BOFactory.getBO(CadastroFacade.class).newTransactionSave(profissional);
                }

                if (profissionalCargaHoraria == null) {
                    profissionalCargaHoraria = new ProfissionalCargaHoraria();
                    profissionalCargaHoraria.setDataAtivacao(DataUtil.getDataAtual());
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.INSERCAO.value());
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_vinculo_profissional_inserido_cbo_X_profissional_X_empresa_X", tabelaCbo.getDescricaoFormatado(), profissional.getNome(), empresa.getDescricao()));
                } else {
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.EDICAO.value());
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_vinculo_profissional_editado_cbo_X_profissional_X_empresa_X", tabelaCbo.getDescricaoFormatado(), profissional.getNome(), empresa.getDescricao()));
                }

                profissionalCargaHoraria.setProfissionalIdCnes(profissionalIdCnes);
                profissionalCargaHoraria.setEmpresa(empresa);
                profissionalCargaHoraria.setProfissional(profissional);
                profissionalCargaHoraria.setTabelaCbo(tabelaCbo);
                profissionalCargaHoraria.setTipoSusNaoSus(cppv.getVinculoSus());
                profissionalCargaHoraria.setNumeroRegistro(cppv.getNumeroRegistro());
                profissionalCargaHoraria.setCargaHorariaAmbulatorial(cppv.getQuantidadeCargaHorariaAmbulatorial());
                profissionalCargaHoraria.setCargaHorariaHospitalar(cppv.getQuantidadeCargaHorariaHospital());
                profissionalCargaHoraria.setCargaHorariaOutros(cppv.getQuantidadeCargaHorariaOutro());
                profissionalCargaHoraria.setCnesProcesso(getCnesProcesso());
                profissionalCargaHoraria.setCompetenciaInicio(Data.getDataParaPrimeiroDiaMes(Coalesce.asDate(dataEntrada, DataUtil.getDataAtual())));

                profissionalCargaHoraria = BOFactory.getBO(CadastroFacade.class).newTransactionSave(profissionalCargaHoraria);

                gerarProfissionalHistorico(profissionalCargaHoraria, false, tabelaCbo);

                ocorrenciaDTO.setProfissionalCargaHoraria(profissionalCargaHoraria);
                BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);

                if (cont == MAX_COUNT) {
                    getSession().flush();
                    getSession().clear();
                    cont = 0;
                }
                cont++;
            }
        }
    }

    private Empresa getEmpresa(String unidadeId) {
        Empresa empresa = LoadManager.getInstance(Empresa.class)
                .addParameter(new QueryCustom.QueryCustomParameter(PROP_UNIDADE_ID_CNES, unidadeId))
                .addParameter(new QueryCustom.QueryCustomParameter(PROP_ATIVO, RepositoryComponentDefault.SIM))
                .start().getVO();
        return empresa;
    }

    private void processarEquipes() throws ValidacaoException, DAOException {
        List<CnesProcessoEquipe> cnesProcessoEquipesList = LoadManager.getInstance(CnesProcessoEquipe.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(CnesProcessoEquipe.PROP_CNES_PROCESSO_EMPRESA, CnesProcessoEmpresa.PROP_CNES_PROCESSO), getCnesProcesso()))
                .addSorter(new QueryCustom.QueryCustomSorter(CnesProcessoEquipe.PROP_DESCRICAO_EQUIPE, BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(cnesProcessoEquipesList)) {
            Cidade cidade;
            EquipeArea equipeArea;
            SegmentoTerritorial segmentoTerritorial;
            Group<CnesProcessoEquipe> byMunicipio;
            Group<CnesProcessoEquipe> byCodigoArea;
            Group<CnesProcessoEquipe> byDescricaoArea;

            int contadorLog = 0;

            // Agrupa por Segmento Territorial
            Group<CnesProcessoEquipe> bySegmento = Lambda.group(cnesProcessoEquipesList, by(on(CnesProcessoEquipe.class).getCodigoSegmento()));
            for (Group<CnesProcessoEquipe> segmentoGroup : bySegmento.subgroups()) {
                if (contadorLog % 100 == 0) {
                    Loggable.log.info("Processando Agrupa por Segmento Territorial " + contadorLog + " de " + bySegmento.subgroups().size() + " (CNES)");
                }
                contadorLog++;
                if (!"".equals(segmentoGroup.first().getCodigoSegmento())) {
                    // Agrupa por Município
                    byMunicipio = Lambda.group(segmentoGroup.findAll(), by(on(CnesProcessoEquipe.class).getCodigoMunicipio()));
                    for (Group<CnesProcessoEquipe> municipioGroup : byMunicipio.subgroups()) {
                        if (!"".equals(Coalesce.asString(municipioGroup.first().getCodigoMunicipio()))) {
                            cidade = LoadManager.getInstance(Cidade.class).setId(new Long(municipioGroup.first().getCodigoMunicipio())).start().getVO();
                            if (cidade != null) {
                                segmentoTerritorial = LoadManager.getInstance(SegmentoTerritorial.class)
                                        .addParameter(new QueryCustom.QueryCustomParameter(SegmentoTerritorial.PROP_CODIGO_SEGMENTO, new Long(segmentoGroup.first().getCodigoSegmento())))
                                        .addParameter(new QueryCustom.QueryCustomParameter(SegmentoTerritorial.PROP_TIPO_SEGMENTO, segmentoGroup.first().getTipoSegmento()))
                                        .addParameter(new QueryCustom.QueryCustomParameter(SegmentoTerritorial.PROP_CIDADE, cidade))
                                        .start().getVO();

                                // Cadastra Segmentos Territoriais
                                if (segmentoTerritorial == null) {
                                    segmentoTerritorial = new SegmentoTerritorial();
                                    segmentoTerritorial.setCodigoSegmento(new Long(segmentoGroup.first().getCodigoSegmento()));
                                    segmentoTerritorial.setCidade(cidade);
                                    segmentoTerritorial.setDescricao(segmentoGroup.first().getDescricaoSegmento());
                                    segmentoTerritorial.setTipoSegmento(segmentoGroup.first().getTipoSegmento());

                                    BOFactory.getBO(CadastroFacade.class).newTransactionSave(segmentoTerritorial);
                                }

                                // Agrupa por Código da Área
                                byCodigoArea = Lambda.group(municipioGroup.findAll(), by(on(CnesProcessoEquipe.class).getCodigoArea()));
                                for (Group<CnesProcessoEquipe> codigoAreaGroup : byCodigoArea.subgroups()) {
                                    // Agrupa por Descricao da Área
                                    byDescricaoArea = Lambda.group(codigoAreaGroup.findAll(), by(on(CnesProcessoEquipe.class).getDescricaoArea()));
                                    for (Group<CnesProcessoEquipe> descricaoAreaGroup : byDescricaoArea.subgroups()) {
                                        if (!"".equals(descricaoAreaGroup.first().getDescricaoArea())) {
                                            boolean areaCriada = false;
                                            for (CnesProcessoEquipe cpeArea : descricaoAreaGroup.findAll()) {

                                                List<Equipe> equipeList = LoadManager.getInstance(Equipe.class)
                                                        .addProperty(VOUtils.montarPath(Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_DESCRICAO))
                                                        .addProperty(VOUtils.montarPath(Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_CODIGO_AREA))
                                                        .addProperty(VOUtils.montarPath(Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_SEGMENTO_TERRITORIAL, SegmentoTerritorial.PROP_CODIGO))
                                                        .addProperty(VOUtils.montarPath(Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_SEGMENTO_TERRITORIAL, SegmentoTerritorial.PROP_TIPO_SEGMENTO))
                                                        .addProperty(VOUtils.montarPath(Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_SEGMENTO_TERRITORIAL, SegmentoTerritorial.PROP_DESCRICAO))
                                                        .addProperty(VOUtils.montarPath(Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_SEGMENTO_TERRITORIAL, SegmentoTerritorial.PROP_CODIGO_SEGMENTO))
                                                        .addParameter(new QueryCustom.QueryCustomParameter(Equipe.PROP_EQUIPE_CNES, cpeArea.getCodigoEquipe()))
                                                        .addSorter(new QueryCustom.QueryCustomSorter(Equipe.PROP_ATIVO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                                                        .addSorter(new QueryCustom.QueryCustomSorter(Equipe.PROP_DATA_ATIVACAO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                                                        .start().getList();

                                                // Cadastra Equipe Área
                                                if (!CollectionUtils.isNotNullEmpty(equipeList) && !areaCriada) {
                                                    boolean areaExiste = LoadManager.getInstance(EquipeArea.class)
                                                            .addProperty(EquipeArea.PROP_CODIGO)
                                                            .addParameter(new QueryCustom.QueryCustomParameter(EquipeArea.PROP_SEGMENTO_TERRITORIAL, segmentoTerritorial))
                                                            .addParameter(new QueryCustom.QueryCustomParameter(EquipeArea.PROP_CODIGO_AREA, new Long(cpeArea.getCodigoArea())))
                                                            .addParameter(new QueryCustom.QueryCustomParameter(EquipeArea.PROP_DESCRICAO, cpeArea.getDescricaoArea()))
                                                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeArea.PROP_CIDADE, Cidade.PROP_CODIGO), new Long(cpeArea.getCodigoMunicipio())))
                                                            .start().exists();

                                                    if(!areaExiste) {
                                                        equipeArea = new EquipeArea();
                                                        equipeArea.setCodigoArea(new Long(codigoAreaGroup.first().getCodigoArea()));
                                                        equipeArea.setSegmentoTerritorial(segmentoTerritorial);
                                                        equipeArea.setDescricao(cpeArea.getDescricaoArea());
                                                        equipeArea.setCidade(cidade);
                                                        equipeArea.setGeraSiab(RepositoryComponentDefault.NAO_LONG);
                                                        BOFactory.getBO(CadastroFacade.class).newTransactionSave(equipeArea);
                                                    }
                                                    areaCriada = true;
                                                } else if(CollectionUtils.isNotNullEmpty(equipeList) && equipeList.get(0).getEquipeArea() != null){
                                                    cpeArea.setTipoSegmento(equipeList.get(0).getEquipeArea().getSegmentoTerritorial().getTipoSegmento());
                                                    cpeArea.setCodigoSegmento(Long.toString(equipeList.get(0).getEquipeArea().getSegmentoTerritorial().getCodigoSegmento()));
                                                    cpeArea.setDescricaoArea(equipeList.get(0).getEquipeArea().getDescricaoArea());
                                                    cpeArea.setCodigoArea(equipeList.get(0).getEquipeArea().getCodigoArea().toString());
                                                }

                                            }
                                        }
                                    }
                                }
                            } else {
                                throw new ValidacaoException(Bundle.getStringApplication("msg_cidade_invalida", new Long(municipioGroup.first().getCodigoMunicipio())));
                            }
                        }
                    }
                }
            }

            /** Atualiza o cnes_processo pra nulo. Serve para saber quais equipes serão processados;*/
            String update = "WITH eqwith AS ( SELECT cd_equipe FROM equipe "
                    + "WHERE cd_cnes_processo IS NOT NULL AND cd_equipe_cnes IS NOT NULL "
                    + "LIMIT " + MAX_COUNT + " ) "
                    + "UPDATE equipe equipe "
                    + "SET cd_cnes_processo = NULL FROM eqwith WHERE equipe.cd_equipe = eqwith.cd_equipe ";

            updateCnes(update);

            /** Atualiza o cnes_processo pra nulo. Serve para saber quais equipe profissional serão processados;*/
            update = "WITH epwith AS ( SELECT cd_equipe_profissional FROM equipe_profissional "
                    + "WHERE cd_cnes_processo IS NOT NULL AND EXISTS( SELECT 1 FROM equipe e WHERE e.cd_equipe = cd_equipe AND e.cd_equipe_cnes IS NOT NULL) "
                    + "LIMIT " + MAX_COUNT + " ) "
                    + "UPDATE equipe_profissional ep SET cd_cnes_processo = NULL FROM epwith "
                    + "WHERE ep.cd_equipe_profissional = epwith.cd_equipe_profissional";

            updateCnes(update);

            Empresa empresa;
            Equipe equipe;
            TipoEquipe tipoEquipe;
            MotivoDesativacao motivoDesativacao;
            QueryEquipeCnesDTOParam param;
            CnesProcessoOcorrenciaDTO ocorrenciaDTO;
            int cont = 1;
            contadorLog = 0;

            // Cadastrar Equipes
            for (CnesProcessoEquipe cpe : cnesProcessoEquipesList) {

                if (contadorLog % 100 == 0) {
                    Loggable.log.info("Processando Cadastrar Equipes " + contadorLog + " de " + cnesProcessoEquipesList.size() + " (CNES)");
                }
                contadorLog++;

                param = new QueryEquipeCnesDTOParam();
                param.setCodigoArea(Long.valueOf(cpe.getCodigoArea()));
                param.setCodigoCidade(Long.valueOf(cpe.getCodigoMunicipio()));
                param.setCodigoEquipeCnes(cpe.getCodigoEquipe());
                param.setTipoEquipe(cpe.getTipoEquipe());
                param.setUnidadeIdCnes(cpe.getCnesProcessoEmpresa().getUnidadeId());
                equipe = BOFactory.getBO(BasicoFacade.class).queryEquipeCnes(param);

                ocorrenciaDTO = new CnesProcessoOcorrenciaDTO();
                ocorrenciaDTO.setCnesProcesso(getCnesProcesso());
                ocorrenciaDTO.setCnesProcessoEquipe(cpe);
                ocorrenciaDTO.setOrigem(CnesProcessoOcorrencia.Origem.EQUIPE.value());

                empresa = getEmpresa(cpe.getUnidadeId());

                if (equipe == null) {
                    equipe = new Equipe();
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.INSERCAO.value());
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_equipe_inserida_codigo_area_X_referencia_X_empresa_X", new Long(cpe.getCodigoArea()),
                            Coalesce.asString(cpe.getNomeReferencia(), Bundle.getStringApplication("rotulo_nao_definido")),
                            Coalesce.asString(empresa.getDescricao(), Bundle.getStringApplication("rotulo_nao_definido"))));
                } else {
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.EDICAO.value());
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_equipe_editada_codigo_area_X_referencia_X_empresa_X", new Long(cpe.getCodigoArea()),
                            Coalesce.asString(cpe.getNomeReferencia(), Bundle.getStringApplication("rotulo_nao_definido")),
                            Coalesce.asString(empresa.getDescricao(), Bundle.getStringApplication("rotulo_nao_definido"))));
                }

                if ("".equals(cpe.getCodigoSegmento())) {
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.ERRO.value());
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("msg_equipe_sem_segmento_territorial_referencia_X", Coalesce.asString(cpe.getNomeReferencia(), Bundle.getStringApplication("rotulo_nao_definido"))));

                    BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);
                    continue;
                }

                segmentoTerritorial = LoadManager.getInstance(SegmentoTerritorial.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(SegmentoTerritorial.PROP_CODIGO_SEGMENTO, new Long(cpe.getCodigoSegmento())))
                        .addParameter(new QueryCustom.QueryCustomParameter(SegmentoTerritorial.PROP_TIPO_SEGMENTO, cpe.getTipoSegmento()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SegmentoTerritorial.PROP_CIDADE, Cidade.PROP_CODIGO), new Long(cpe.getCodigoMunicipio())))
                        .start().getVO();

                if (segmentoTerritorial == null) {
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.ERRO.value());
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("msg_segmento_territorial_invalido", new Long(cpe.getCodigoSegmento())));

                    BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);
                    continue;
                }

                equipeArea = LoadManager.getInstance(EquipeArea.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(EquipeArea.PROP_SEGMENTO_TERRITORIAL, segmentoTerritorial))
                        .addParameter(new QueryCustom.QueryCustomParameter(EquipeArea.PROP_CODIGO_AREA, new Long(cpe.getCodigoArea())))
                        .addParameter(new QueryCustom.QueryCustomParameter(EquipeArea.PROP_DESCRICAO, cpe.getDescricaoArea()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeArea.PROP_CIDADE, Cidade.PROP_CODIGO), new Long(cpe.getCodigoMunicipio())))
                        .start().getVO();

                if (equipeArea == null) {
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.ERRO.value());
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("msg_equipe_area_invalida", new Long(cpe.getCodigoArea())));

                    BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);
                    continue;
                }

                equipe.setCnesProcesso(getCnesProcesso());
                equipe.setEquipeCnes(cpe.getCodigoEquipe());
                equipe.setEmpresa(empresa);
                equipe.setEquipeArea(equipeArea);
                equipe.setEmpresa(empresa);
                equipe.setSequenciaEquipe(cpe.getSequencialEquipe());
                equipe.setReferencia(cpe.getNomeReferencia());
                equipe.setDataAtivacao(cpe.getDataAtivacao());
                equipe.setDataDesativacao(cpe.getDataDesativacao());
                equipe.setTipoQuilombo(verificarSimNao(cpe.getTipoPopulacaoAssistidaQuilombola()));
                equipe.setTipoAssentado(verificarSimNao(cpe.getTipoPopulacaoAssistidaAssentado()));
                equipe.setTipoGeral(verificarSimNao(cpe.getTipoPopulacaoAssistidaGeral()));
                equipe.setTipoEscola(verificarSimNao(cpe.getTipoPopulacaoAssistidaEscola()));
                equipe.setTipoPronasci(verificarSimNao(cpe.getTipoPopulacaoAssistidaPronasci()));
                equipe.setTipoDesativacao(cpe.getCodigoTipoDesativacao());
                if (equipe.getDataDesativacao() != null) {
                    equipe.setDataInativacao(cpe.getDataDesativacao());
                    equipe.setAtivo(RepositoryComponentDefault.NAO);

                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.EXCLUSAO.value());
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_equipe_que_sera_deletada_codigo_area_X_referencia_X_empresa_X", new Long(cpe.getCodigoArea()),
                            Coalesce.asString(cpe.getNomeReferencia(), Bundle.getStringApplication("rotulo_nao_definido")),
                            Coalesce.asString(empresa.getDescricao(), Bundle.getStringApplication("rotulo_nao_definido"))));
                } else {
                    equipe.setAtivo(RepositoryComponentDefault.SIM);
                }

                if (!"".equals(Coalesce.asString(cpe.getTipoEquipe()))) {
                    tipoEquipe = LoadManager.getInstance(TipoEquipe.class).setId(cpe.getTipoEquipe()).start().getVO();
                    if (tipoEquipe != null) {
                        equipe.setTipoEquipe(tipoEquipe);
                    }
                }
                if (!"".equals(Coalesce.asString(cpe.getCodigoMotivoDesativacao()))) {
                    motivoDesativacao = LoadManager.getInstance(MotivoDesativacao.class).setId(cpe.getCodigoMotivoDesativacao()).start().getVO();
                    if (motivoDesativacao != null) {
                        equipe.setMotivoDesativacao(motivoDesativacao);
                    } else {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_motivo_desativacao_equipe_invalido", cpe.getCodigoMotivoDesativacao()));
                    }
                }
                equipe = BOFactory.getBO(CadastroFacade.class).newTransactionSave(equipe);

                ocorrenciaDTO.setEquipe(equipe);
                BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);

                processarEquipeProfissional(cpe.getCodigo(), equipe, equipeArea);

                if (cont == MAX_COUNT) {
                    getSession().flush();
                    getSession().clear();
                    cont = 0;
                }
                cont++;
            }

            // Gera as ocorrências de deleção para confirmação do usuário posteriormente;
            List<Equipe> equipeList = LoadManager.getInstance(Equipe.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(Equipe.PROP_EQUIPE_CNES, BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                    .addParameter(new QueryCustom.QueryCustomParameter(Equipe.PROP_CNES_PROCESSO, BuilderQueryCustom.QueryParameter.IS_NULL))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(equipeList)) {
                for (Equipe e : equipeList) {
                    ocorrenciaDTO = new CnesProcessoOcorrenciaDTO();
                    ocorrenciaDTO.setCnesProcesso(getCnesProcesso());
                    ocorrenciaDTO.setOrigem(CnesProcessoOcorrencia.Origem.EQUIPE.value());
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.EXCLUSAO.value());
                    ocorrenciaDTO.setEquipe(e);
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_equipe_que_sera_deletada_codigo_area_X_referencia_X_empresa_X", e.getEquipeArea().getCodigoArea(),
                            Coalesce.asString(e.getReferencia(), Bundle.getStringApplication("rotulo_nao_definido")),
                            Coalesce.asString(e.getEmpresa().getDescricao(), Bundle.getStringApplication("rotulo_nao_definido"))));
                    BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);

                    if (e.getDataDesativacao() == null) {
                        e.setDataDesativacao(DataUtil.getDataAtual());
                    }
                    if (e.getDataInativacao() == null) {
                        e.setDataInativacao(DataUtil.getDataAtual());
                    }
                    e.setAtivo(RepositoryComponentDefault.NAO);
                    BOFactory.getBO(CadastroFacade.class).newTransactionSave(e);

                    if (cont == MAX_COUNT) {
                        getSession().flush();
                        getSession().clear();
                        cont = 0;
                    }
                    cont++;
                }
            }

            // Gera as ocorrências de deleção para confirmação do usuário posteriormente;
            List<EquipeProfissional> equipeProfissionalList = LoadManager.getInstance(EquipeProfissional.class)
                    .addProperties(new HQLProperties(EquipeProfissional.class).getProperties())
                    .addProperties(new HQLProperties(Profissional.class, EquipeProfissional.PROP_PROFISSIONAL).getProperties())
                    .addProperties(new HQLProperties(Equipe.class, EquipeProfissional.PROP_EQUIPE).getProperties())
                    .addProperties(new HQLProperties(EquipeArea.class, VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA)).getProperties())
                    .addProperties(new HQLProperties(Empresa.class, VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_CNES), BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                    .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_CNES_PROCESSO, BuilderQueryCustom.QueryParameter.IS_NULL))
                    .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_DATA_DESLIGAMENTO, BuilderQueryCustom.QueryParameter.IS_NULL))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(equipeProfissionalList)) {
                for (EquipeProfissional ep : equipeProfissionalList) {
                    ocorrenciaDTO = new CnesProcessoOcorrenciaDTO();
                    ocorrenciaDTO.setCnesProcesso(getCnesProcesso());
                    ocorrenciaDTO.setOrigem(CnesProcessoOcorrencia.Origem.EQUIPE_PROFISSIONAL.value());
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.EXCLUSAO.value());
                    ocorrenciaDTO.setEquipeProfissional(ep);
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_equipe_profissional_deletada_codigo_area_X_referencia_X_microarea_X_empresa_X_profissional_X", ep.getEquipe().getEquipeArea().getCodigoArea(),
                            Coalesce.asString(ep.getEquipe().getReferencia(), Bundle.getStringApplication("rotulo_nao_definido")),
                            Coalesce.asLong(ep.getMicroArea()), ep.getEquipe().getEmpresa().getDescricao(), ep.getProfissional().getNome()));
                    BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);

                    new RemoverEquipeProfissional(ep, ep.getDataDesligamento()).start();

                    if (cont == MAX_COUNT) {
                        getSession().flush();
                        getSession().clear();
                        cont = 0;
                    }
                    cont++;
                }
            }

            if (cont <= MAX_COUNT) {
                getSession().flush();
                getSession().clear();
            }
        }
    }

    private void processarEquipeProfissional(Long codigoCnesProcessoEquipe, Equipe equipe, EquipeArea equipeArea) throws ValidacaoException, DAOException {
        List<CnesProcessoEquipeProfissional> cnesProcessoEquipeProfissionalList = LoadManager.getInstance(CnesProcessoEquipeProfissional.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(CnesProcessoEquipeProfissional.PROP_CNES_PROCESSO_EQUIPE, CnesProcessoEquipe.PROP_CODIGO), codigoCnesProcessoEquipe))
                .addSorter(new QueryCustom.QueryCustomSorter(CnesProcessoEquipeProfissional.PROP_CODIGO_CBO, BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(cnesProcessoEquipeProfissionalList)) {
            CnesProcessoOcorrenciaDTO ocorrenciaDTO;
            List<EquipeProfissional> equipeProfissionalList;
            EquipeProfissional equipeProfissional = null;
            EquipeMicroArea equipeMicroArea;
            Profissional profissional;
            OrgaoEmissor orgaoEmissor;
            Empresa empresa;
            int cont = 1;
            int contadorLog = 0;
            for (CnesProcessoEquipeProfissional cpep : cnesProcessoEquipeProfissionalList) {

                if (contadorLog % 100 == 0) {
                    Loggable.log.info("Processando Equipe profissional cnesProcessoEquipeProfissionalList " + contadorLog + " de " + cnesProcessoEquipeProfissionalList.size() + " (CNES)");
                }
                contadorLog++;

                profissional = LoadManager.getInstance(Profissional.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(Profissional.PROP_PROFISSIONAL_ID_CNES, cpep.getProfissionalId()))
                        .start().getVO();

                ocorrenciaDTO = new CnesProcessoOcorrenciaDTO();
                ocorrenciaDTO.setCnesProcesso(getCnesProcesso());
                ocorrenciaDTO.setEquipe(equipe);
                ocorrenciaDTO.setOrigem(CnesProcessoOcorrencia.Origem.EQUIPE_PROFISSIONAL.value());

                if (profissional == null) {
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.ERRO.value());
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("msg_profissional_nao_cadastrado_id_cnes_X", cpep.getProfissionalId()));

                    BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);
                    continue;
                }

                equipeProfissionalList = LoadManager.getInstance(EquipeProfissional.class)
                        .addProperties(new HQLProperties(EquipeProfissional.class).getProperties())
                        .addProperties(new HQLProperties(Profissional.class, EquipeProfissional.PROP_PROFISSIONAL).getProperties())
                        .addProperties(new HQLProperties(Equipe.class, EquipeProfissional.PROP_EQUIPE).getProperties())
                        .addProperties(new HQLProperties(EquipeArea.class, VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA)).getProperties())
                        .addProperties(new HQLProperties(Empresa.class, VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA)).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_EQUIPE, equipe))
                        .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_PROFISSIONAL, profissional))
                        .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_DATA_DESLIGAMENTO, BuilderQueryCustom.QueryParameter.IS_NULL))
                        .start().getList();

                if (CollectionUtils.isNotNullEmpty(equipeProfissionalList)) {
                    equipeProfissional = equipeProfissionalList.get(0);
//                    if(equipeProfissionalList.size() > 1L){
//                        ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.ERRO.value());
//                        ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_erro_edicao_equipe_profissional_profissional_duplicado_equipe_codigo_area_X_referencia_X_microarea_X_empresa_X_profissional_X", equipeArea.getCodigoArea(),
//                                Coalesce.asString(equipe.getReferencia(), Bundle.getStringApplication("rotulo_nao_definido")),
//                                new Long(!"".equals(cpep.getMicroArea()) ? cpep.getMicroArea() : "0"), equipe.getEmpresa().getDescricao(), profissional.getNome()));
//
//                        BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);
//                        continue;
//                    } else {
//                        equipeProfissional = equipeProfissionalList.get(0);
//                    }
                } else {
                    equipeProfissional = null;
                }

                if (equipeProfissional == null) {
                    equipeProfissional = new EquipeProfissional();
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.INSERCAO.value());
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_equipe_profissional_inserida_codigo_area_X_referencia_X_microarea_X_empresa_X_profissional_X", equipeArea.getCodigoArea(),
                            Coalesce.asString(equipe.getReferencia(), Bundle.getStringApplication("rotulo_nao_definido")),
                            new Long(!"".equals(cpep.getMicroArea()) ? cpep.getMicroArea() : "0"), equipe.getEmpresa().getDescricao(), profissional.getNome()));
                } else {
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.EDICAO.value());
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_equipe_profissional_editada_codigo_area_X_referencia_X_microarea_X_empresa_X_profissional_X", equipeArea.getCodigoArea(),
                            Coalesce.asString(equipe.getReferencia(), Bundle.getStringApplication("rotulo_nao_definido")),
                            new Long(!"".equals(cpep.getMicroArea()) ? cpep.getMicroArea() : "0"), equipe.getEmpresa().getDescricao(), profissional.getNome()));
                }

                TabelaCbo tabelaCbo = LoadManager.getInstance(TabelaCbo.class).setId(cpep.getCodigoCbo()).addProperty(VOUtils.montarPath(TabelaCbo.PROP_CBO)).start().getVO();
                equipeProfissional.setTabelaCbo(tabelaCbo);
                equipeProfissional.setProfissional(profissional);
                equipeProfissional.setEquipe(equipe);
                equipeProfissional.setDataEntrada(cpep.getDataEntrada());
                equipeProfissional.setDataDesligamento(cpep.getDataDesligamento());
                equipeProfissional.setFlagEquipeMinima(verificarSimNao(cpep.getFlagEquipeMinima()));
                equipeProfissional.setCnesProcesso(getCnesProcesso());
                equipeProfissional.setStatus(EquipeProfissional.STATUS_ATIVO);

                equipeProfissional = BOFactory.getBO(CadastroFacade.class).newTransactionSave(equipeProfissional);

                // Salva a micro área
                if (!"".equals(cpep.getMicroArea())) {
                    equipeMicroArea = LoadManager.getInstance(EquipeMicroArea.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(EquipeMicroArea.PROP_EQUIPE_AREA, equipeArea))
                            .addParameter(new QueryCustom.QueryCustomParameter(EquipeMicroArea.PROP_MICRO_AREA, new Long(cpep.getMicroArea())))
                            .start().getVO();

                    if (equipeMicroArea == null) {
                        equipeMicroArea = new EquipeMicroArea();
                        equipeMicroArea.setEquipeArea(equipeArea);
                        equipeMicroArea.setMicroArea(new Long(cpep.getMicroArea()));
                    }

                    equipeMicroArea.setEquipeProfissional(equipeProfissional);
                    equipeMicroArea = BOFactory.newTransactionSave(equipeMicroArea);

                    equipeProfissional.setMicroArea(new Long(cpep.getMicroArea()));
                    equipeProfissional.setEquipeMicroArea(equipeMicroArea);
                    equipeProfissional = BOFactory.getBO(CadastroFacade.class).newTransactionSave(equipeProfissional);
                }

                if (equipeProfissional.getDataDesligamento() != null) {
                    equipeProfissional.setStatus(EquipeProfissional.STATUS_EXCLUIDO);
                    new RemoverEquipeProfissional(equipeProfissional, equipeProfissional.getDataDesligamento()).start();

                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.EXCLUSAO.value());
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_equipe_profissional_deletada_codigo_area_X_referencia_X_microarea_X_empresa_X_profissional_X", equipeProfissional.getEquipe().getEquipeArea().getCodigoArea(),
                            Coalesce.asString(equipeProfissional.getEquipe().getReferencia(), Bundle.getStringApplication("rotulo_nao_definido")),
                            Coalesce.asLong(equipeProfissional.getMicroArea()), equipeProfissional.getEquipe().getEmpresa().getDescricao(), equipeProfissional.getProfissional().getNome()));
                }

                ocorrenciaDTO.setEquipeProfissional(equipeProfissional);
                BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);

                if (cont == MAX_COUNT) {
                    getSession().flush();
                    getSession().clear();
                    cont = 0;
                }
                cont++;
            }
        }
    }

    private void processarServicosEspecializados() throws ValidacaoException, DAOException {
        List<CnesProcessoServicosEspecializados> cnesProcessoServicosList = LoadManager.getInstance(CnesProcessoServicosEspecializados.class)
                .addProperties(new HQLProperties(CnesProcessoServicosEspecializados.class).getProperties())
                .addProperties(new HQLProperties(CnesProcessoEmpresa.class, CnesProcessoServicosEspecializados.PROP_CNES_PROCESSO_EMPRESA).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(CnesProcessoServicosEspecializados.PROP_CNES_PROCESSO_EMPRESA, CnesProcessoEmpresa.PROP_CNES_PROCESSO), getCnesProcesso()))
                .addSorter(new QueryCustom.QueryCustomSorter(CnesProcessoServicosEspecializados.PROP_DESCRICAO_SERVICO, BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(cnesProcessoServicosList)) {
            /** Atualiza o cnes_processo pra nulo. Serve para saber quais serviços especializados serão processados;*/
            String update = "WITH escwith AS (    SELECT cd_emp_serv_cla    FROM   empresa_servico_class "
                    + "   WHERE cd_cnes_processo is not null and exists(SELECT 1 FROM empresa e where e.empresa = empresa and e.unidade_id_cnes is not null) "
                    + "   LIMIT  " + MAX_COUNT + "    ) "
                    + "UPDATE empresa_servico_class esc SET cd_cnes_processo = null  FROM   escwith "
                    + "WHERE  esc.cd_emp_serv_cla = escwith.cd_emp_serv_cla";

            updateCnes(update);

            CnesProcessoOcorrenciaDTO ocorrenciaDTO;
            EmpresaServicoClassificacao empresaServicoClassificacao;
            Empresa empresa;
            Empresa empresaTerceiro;
            ProcedimentoServicoCadastro procedimentoServicoCadastro;
            ProcedimentoServicoClassificacao procedimentoServicoClassificacao;
            TipoLogradouroCnes tipoLogradouroCnes;
            LoadManager loadManager;
            Long codigoEmpresa;
            int cont = 1;
            int contadorLog = 0;
            Group<CnesProcessoServicosEspecializados> byEmpresa = Lambda.group(cnesProcessoServicosList, by(on(CnesProcessoServicosEspecializados.class).getCnesProcessoEmpresa().getUnidadeId()));
            for (Group<CnesProcessoServicosEspecializados> sub : byEmpresa.subgroups()) {

                if (contadorLog % 100 == 0) {
                    Loggable.log.info("Processando Servicos Especializados " + contadorLog + " de " + byEmpresa.subgroups().size() + " (CNES)");
                }
                contadorLog++;

                empresa = null;
                codigoEmpresa = getCodigoEmpresaCriteria(sub);

                if (codigoEmpresa != null) {
                    empresa = LoadManager.getInstance(Empresa.class)
                            .addProperties(new HQLProperties(Empresa.class).getProperties())
                            .addProperties(new HQLProperties(EmpresaMaterial.class, Empresa.PROP_EMPRESA_MATERIAL).getProperties())
                            .setId(codigoEmpresa).start().getVO();
                }

                if (empresa != null && empresa.getCodigo() != null) {
                    for (CnesProcessoServicosEspecializados item : sub.findAll()) {
                        ocorrenciaDTO = new CnesProcessoOcorrenciaDTO();
                        ocorrenciaDTO.setCnesProcesso(getCnesProcesso());
                        ocorrenciaDTO.setCnesProcessoServicosEspecializados(item);
                        ocorrenciaDTO.setOrigem(CnesProcessoOcorrencia.Origem.SERVICOS_ESPECIALIZADOS.value());

                        procedimentoServicoCadastro = LoadManager.getInstance(ProcedimentoServicoCadastro.class)
                                .addParameter(new QueryCustom.QueryCustomParameter(ProcedimentoServicoCadastro.PROP_CODIGO, new Long(item.getCodigoServico())))
                                .start().getVO();

                        if (procedimentoServicoCadastro == null) {
                            ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.ERRO.value());
                            ocorrenciaDTO.setDescricao(Bundle.getStringApplication("msg_servico_invalido", item.getDescricaoServico()));

                            BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);
                            continue;
                        }

                        procedimentoServicoClassificacao = LoadManager.getInstance(ProcedimentoServicoClassificacao.class)
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoServicoClassificacao.PROP_ID, ProcedimentoServicoClassificacaoPk.PROP_PROCEDIMENTO_SERVICO_CADASTRO), procedimentoServicoCadastro))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoServicoClassificacao.PROP_ID, ProcedimentoServicoClassificacaoPk.PROP_CODIGO), new Long(item.getCodigoClassificacao())))
                                .start().getVO();

                        if (procedimentoServicoClassificacao == null) {
                            ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.ERRO.value());
                            ocorrenciaDTO.setDescricao(Bundle.getStringApplication("msg_classificacao_invalida", item.getDescricaoClassificacao(), item.getDescricaoServico()));

                            BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);
                            continue;
                        }

                        loadManager = LoadManager.getInstance(EmpresaServicoClassificacao.class)
                                .addParameter(new QueryCustom.QueryCustomParameter(EmpresaServicoClassificacao.PROP_EMPRESA, empresa))
                                .addParameter(new QueryCustom.QueryCustomParameter(EmpresaServicoClassificacao.PROP_PROCEDIMENTO_SERVICO_CLASSIFICACAO, procedimentoServicoClassificacao))
                                .addParameter(new QueryCustom.QueryCustomParameter(EmpresaServicoClassificacao.PROP_CODIGO_CARACTERIZACAO, item.getCodigoCaracteristica()));

                        if (!"".equals(Coalesce.asString(item.getCnpjCpf())) && !"NAO INFORMADO".equals(Coalesce.asString(item.getCnpjCpf()))) {
                            // Isto é necessário pois pode vir um cnes que não esteja cadastrado para nenhum estabelecimento
                            empresaTerceiro = LoadManager.getInstance(Empresa.class)
                                    .addParameter(new QueryCustom.QueryCustomParameter(PROP_CNES, item.getCnpjCpf()))
                                    .setMaxResults(1)
                                    .start().getVO();

                            if (empresaTerceiro != null) {
                                loadManager.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EmpresaServicoClassificacao.PROP_EMPRESA_TERCEIRO, PROP_CNES), item.getCnpjCpf())).setMaxResults(1);
                            } else {
                                loadManager.addParameter(new QueryCustom.QueryCustomParameter(EmpresaServicoClassificacao.PROP_EMPRESA_TERCEIRO, BuilderQueryCustom.QueryParameter.IS_NULL));
                            }
                        } else {
                            loadManager.addParameter(new QueryCustom.QueryCustomParameter(EmpresaServicoClassificacao.PROP_EMPRESA_TERCEIRO, BuilderQueryCustom.QueryParameter.IS_NULL));
                        }

                        List<EmpresaServicoClassificacao> empresaServicoClassificacaoList = loadManager.start().getList();
                        empresaServicoClassificacao = null;
                        if (CollectionUtils.isNotNullEmpty(empresaServicoClassificacaoList)) {
                            List<EmpresaServicoClassificacao> empresaServicoClassificacaoListAux = Lambda.select(empresaServicoClassificacaoList,
                                    Lambda.having(Lambda.on(EmpresaServicoClassificacao.class).getCnesProcesso(), Matchers.notNullValue()));
                            if (CollectionUtils.isNotNullEmpty(empresaServicoClassificacaoListAux)) {
                                empresaServicoClassificacao = empresaServicoClassificacaoListAux.get(0);
                            } else {
                                empresaServicoClassificacao = empresaServicoClassificacaoList.get(0);
                            }
                        }
                        if (empresaServicoClassificacao == null) {
                            empresaServicoClassificacao = new EmpresaServicoClassificacao();
                            ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.INSERCAO.value());
                            ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_servico_especializado_inserido_servico_X_classificacao_X_empresa_X", procedimentoServicoCadastro.getDescricao(), item.getDescricaoClassificacao(),
                                    empresa.getDescricao()));
                        } else {
                            ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.EDICAO.value());
                            ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_servico_especializado_editado_servico_X_classificacao_X_empresa_X", procedimentoServicoCadastro.getDescricao(), item.getDescricaoClassificacao(),
                                    empresa.getDescricao()));
                        }

                        empresaServicoClassificacao.setProcedimentoServicoClassificacao(procedimentoServicoClassificacao);
                        empresaServicoClassificacao.setCodigoCaracterizacao(item.getCodigoCaracteristica());
                        empresaServicoClassificacao.setCnesProcesso(getCnesProcesso());
                        empresaServicoClassificacao.setEmpresa(empresa);

                        if (!"".equals(Coalesce.asString(item.getCnpjCpf())) && !"NAO INFORMADO".equals(Coalesce.asString(item.getCnpjCpf())) && !"".equals(Coalesce.asString(item.getCodigoCaracteristica()))
                                && (EmpresaServicoClassificacao.CARACTERIZACAO_TERCEIRIZADO.equals(item.getCodigoCaracteristica()) || EmpresaServicoClassificacao.CARACTERIZACAO_AMBOS.equals(item.getCodigoCaracteristica()))) {
                            empresaTerceiro = LoadManager.getInstance(Empresa.class)
                                    .addParameter(new QueryCustom.QueryCustomParameter(PROP_CNES, item.getCnpjCpf()))
                                    .setMaxResults(1)
                                    .start().getVO();

                            if (empresaTerceiro != null) {
                                empresaServicoClassificacao.setEmpresaTerceiro(empresaTerceiro);
                                empresaServicoClassificacao.setNomeResponsavel(empresaTerceiro.getDescricao());
                            }
                        }
                        empresaServicoClassificacao.setAmbulatorial(verificarSimNao(item.getCodigoServicoAmbulatorial()));
                        empresaServicoClassificacao.setAmbulatorialSus(verificarSimNao(item.getCodigoServicoAmbulatorialSus()));
                        empresaServicoClassificacao.setHospitalar(verificarSimNao(item.getCodigoServicoHospitalar()));
                        empresaServicoClassificacao.setHospitalarSus(verificarSimNao(item.getCodigoServicoHospitalarSus()));

                        empresaServicoClassificacao = BOFactory.getBO(CadastroFacade.class).newTransactionSave(empresaServicoClassificacao);

                        ocorrenciaDTO.setEmpresaServicoClassificacao(empresaServicoClassificacao);
                        BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);

                        if (cont == MAX_COUNT) {
                            getSession().flush();
                            getSession().clear();
                            cont = 0;
                        }
                        cont++;
                    }
                }
            }

            // Gera as ocorrências de deleção para confirmação do usuário posteriormente;
            List<EmpresaServicoClassificacao> empresaServicoClassificacaoList = LoadManager.getInstance(EmpresaServicoClassificacao.class)
                    .addProperties(new HQLProperties(EmpresaServicoClassificacao.class).getProperties())
                    .addProperties(new HQLProperties(Empresa.class, EmpresaServicoClassificacao.PROP_EMPRESA).getProperties())
                    .addProperties(new HQLProperties(ProcedimentoServicoClassificacao.class, EmpresaServicoClassificacao.PROP_PROCEDIMENTO_SERVICO_CLASSIFICACAO).getProperties())
                    .addProperties(new HQLProperties(ProcedimentoServicoCadastro.class, VOUtils.montarPath(EmpresaServicoClassificacao.PROP_PROCEDIMENTO_SERVICO_CLASSIFICACAO, ProcedimentoServicoClassificacao.PROP_ID, ProcedimentoServicoClassificacaoPk.PROP_PROCEDIMENTO_SERVICO_CADASTRO)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EmpresaServicoClassificacao.PROP_EMPRESA, PROP_UNIDADE_ID_CNES), BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                    .addParameter(new QueryCustom.QueryCustomParameter(EmpresaServicoClassificacao.PROP_CNES_PROCESSO, BuilderQueryCustom.QueryParameter.IS_NULL))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(empresaServicoClassificacaoList)) {
                List<CnesProcessoOcorrencia> cnesProcessoOcorrenciaList;
                for (EmpresaServicoClassificacao esc : empresaServicoClassificacaoList) {
                    ocorrenciaDTO = new CnesProcessoOcorrenciaDTO();
                    ocorrenciaDTO.setCnesProcesso(getCnesProcesso());
                    ocorrenciaDTO.setOrigem(CnesProcessoOcorrencia.Origem.SERVICOS_ESPECIALIZADOS.value());
                    ocorrenciaDTO.setTipo(CnesProcessoOcorrencia.Tipo.EXCLUSAO.value());
                    ocorrenciaDTO.setEmpresa(esc.getEmpresa());
                    ocorrenciaDTO.setEmpresaServicoClassificacao(esc);
                    ocorrenciaDTO.setDescricao(Bundle.getStringApplication("rotulo_servico_especializado_deletado_servico_X_classificacao_X_empresa_X",
                            esc.getProcedimentoServicoClassificacao().getId().getProcedimentoServicoCadastro().getDescricao(), esc.getProcedimentoServicoClassificacao().getDescricao(), esc.getEmpresa().getDescricao()));
                    BOFactory.getBO(BasicoFacade.class).gerarCnesProcessoOcorrencia(ocorrenciaDTO);

                    cnesProcessoOcorrenciaList = LoadManager.getInstance(CnesProcessoOcorrencia.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(CnesProcessoOcorrencia.PROP_CODIGO_EMPRESA_SERVICO_CLASSIFICACAO, esc.getCodigo()))
                            .start().getList();

                    if (CollectionUtils.isNotNullEmpty(cnesProcessoOcorrenciaList)) {
                        for (CnesProcessoOcorrencia cpo : cnesProcessoOcorrenciaList) {
                            cpo.setCodigoEmpresaServicoClassificacao(null);
                            BOFactory.getBO(CadastroFacade.class).newTransactionSave(cpo);
                        }
                    }

                    BOFactory.newTransactionDelete(esc);

                    if (cont == MAX_COUNT) {
                        getSession().flush();
                        getSession().clear();
                        cont = 0;
                    }
                    cont++;
                }
            }

            if (cont <= MAX_COUNT) {
                getSession().flush();
                getSession().clear();
            }
        }
    }

    private Long getCodigoEmpresaCriteria(Group<CnesProcessoServicosEspecializados> sub) {
        return (Long) getSession().createCriteria(Empresa.class)
                .setProjection(Projections.property(PROP_CODIGO))
                .add(Restrictions.eq(PROP_UNIDADE_ID_CNES, sub.first().getCnesProcessoEmpresa().getUnidadeId()))
                .add(Restrictions.eq(PROP_ATIVO, RepositoryComponentDefault.SIM))
                .uniqueResult();
    }

    private void updateCnes(String update) throws ValidacaoException, DAOException {
        Integer results = 0;
        do {
            results = BOFactory.getBO(BasicoFacade.class).updateCnes(update);
        } while (results > 0);
    }

    private void gerarProfissionalHistorico(ProfissionalCargaHoraria pch, boolean delecao, TabelaCbo tabelaCbo) throws DAOException, ValidacaoException {
        ProfissionalHistorico ph = new ProfissionalHistorico();
        ph.setEmpresa(pch.getEmpresa());
        ph.setProfissional(pch.getProfissional());
        ph.setCompetenciaFim(pch.getCompetenciaFim());
        ph.setCompetenciaInicio(pch.getCompetenciaInicio());
        ph.setDataDesligamento(pch.getDataDesativacao());
        ph.setDataEntrada(pch.getDataAtivacao());
        ph.setMotivoDesligamento(pch.getMotivoDesligamento());
        ph.setTabelaCbo(tabelaCbo);
        ph.setTipoSusNaoSus(pch.getTipoSusNaoSus());
        ph.setVinculacaoSubTipo(pch.getVinculacaoSubTipo());
        if (delecao) {
            ph.setDataExclusao(DataUtil.getDataAtual());
            ph.setUsuarioCancelamento(getSessao().getUsuario());
        }

        BOFactory.getBO(CadastroFacade.class).newTransactionSave(ph);
    }

    public CnesProcesso getCnesProcesso() {
        if (cnesProcesso == null) {
            cnesProcesso = LoadManager.getInstance(CnesProcesso.class).setId(this.codigoProcesso).start().getVO();
//            cnesProcesso = (CnesProcesso) getSession().get(CnesProcesso.class, dto.getCodigoCnesProcesso());
        }
        return cnesProcesso;
    }

    private void updateTipoEquipe() throws DAOException, ValidacaoException {
        /** Atualiza as descrições na tabela tipo_equipe utilizando como base a descrição de equipe com mesmo cd_tp_equipe e maior cd_cnes_processo_equipe da tabela cnes_processo_equipe.
         *  Ou seja, utilizará a descrição mais atual enviada através do arquivo XML*/
        String update = "WITH cnesProcessoEquipe AS ( "
                + "SELECT tp_equipe, ds_equipe FROM cnes_processo_equipe cpe "
                + "WHERE cd_cnes_processo_equipe IN (SELECT MAX(cd_cnes_processo_equipe) AS max_cd_cnes_processo_equipe FROM cnes_processo_equipe GROUP BY tp_equipe ) ) "
                + "UPDATE tipo_equipe te "
                + "SET ds_tp_equipe = cpe.ds_equipe FROM cnesProcessoEquipe cpe WHERE te.cd_tp_equipe = cpe.tp_equipe ";
        BOFactory.getBO(BasicoFacade.class).updateCnes(update);
    }

    private void updateClassificacaoServicosEspecializados() throws DAOException, ValidacaoException {
        /** Atualiza as descrições na tabela procedimento_servico_cla utilizando como base a descrição de classificação com mesmo cod_servico e cod_class e maior cd_cnes_processo_serv_especializados da tabela cnes_processo_serv_especializados.
         *  Ou seja, utilizará a descrição mais atual enviada através do arquivo XML*/
        String update = "WITH cnesProcessoServicosEspecializados AS ( "
                + "SELECT CAST (cod_servico AS int4), CAST (cod_class AS int4), ds_class FROM cnes_processo_serv_especializados cpe "
                + "WHERE cd_cnes_processo_serv_especializados IN (SELECT MAX(cd_cnes_processo_serv_especializados) AS max_cd_cnes_processo_serv_especializados FROM cnes_processo_serv_especializados GROUP BY cod_servico, cod_class) ) "
                + "UPDATE procedimento_servico_cla te "
                + "SET ds_classificacao = cpse.ds_class FROM cnesProcessoServicosEspecializados cpse WHERE cd_classificacao = cpse.cod_class AND cd_servico = cpse.cod_servico ";
        BOFactory.getBO(BasicoFacade.class).updateCnes(update);
    }

    private String verificarSimNao(String valueXml) {
        return !"".equals(Coalesce.asString(valueXml)) && RepositoryComponentDefault.SIM_LONG.equals(new Long(valueXml)) ? RepositoryComponentDefault.SIM : RepositoryComponentDefault.NAO;
    }

    public CnesProcesso.Status getStatus() {
        return status;
    }

    private void converteZIPparaDTORoot(File file) throws DAOException, ValidacaoException {

        variacoesNomeArquivo = gerarVariacoes();

        ///MELHORAR PODE TER NO MESMO SERVER TENANTS EXECUTANDO A MESMA OPERACAO, SUGESTÃO CRIAR UM UUID OU INTEIRO DA DATA PARA O NOME DA PASTA.
        ///  ~/tmp/cnes-<UUID>/
        String folderUnzip = "/tmp" + File.separator + "folder" + file.getName();

        /// SE der erro no unzip ??
        FileUtils.unZip(file.getAbsolutePath(), folderUnzip);

        /// SE der erro na leitura dos arquivos ??
        /// Atualmente está mandando a mesma mensagem genérica para todos, melhorar em cada leitura para notificar se houve falha no seu "preenchimento"
        /// SUGESTAO É AVALIAR SE TODOS OS ARQUIVOS NECESSÁRIO ESTÃO DISPONIVEL.

        //ESTABELECIMENTOS
        lerLinhasArquivo(folderUnzip + File.separator, "004.txt");
        //PROFISSIONAIS
        lerLinhasArquivo(folderUnzip + File.separator, "018.txt");
        //CARGA HORÁRIA DO PROFISSIONAL
        lerLinhasArquivo(folderUnzip + File.separator, "021.txt");
        //SERVIÇO ESPECIALIZADO / CLASSIFICAÇÃO
        lerLinhasArquivo(folderUnzip + File.separator, "032.txt");
        //EQUIPES
        lerLinhasArquivo(folderUnzip + File.separator, "037.txt");
        //PROFISSIONAIS DAS EQUIPES
        lerLinhasArquivo(folderUnzip + File.separator, "038.txt");

        finalizaProcesso();
    }

    private void finalizaProcesso() {
        for (Map.Entry<String, CnesProcessoEmpresaDadosDTO> entry : mapEstabelecimentos.entrySet()) {
            cnesProcessoRootDTO.getEmpresas().addDadosEmpresaList(entry.getValue());
        }
    }
    private Date converteStringParaDate (String dataString){

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        LocalDate localDate = LocalDate.parse(dataString, formatter);
        Date date = java.sql.Date.valueOf(localDate);

        return date;
    }

    private void lerLinhasArquivo(String path, String index) throws ValidacaoException {

        String numeroArquivo = index.split("\\.")[0];
        try {
            // SE FORMATACAO DO ARQUIVO NÃO TIVER OK ?
            // SE DER ALGUM ERRO DURANTE A LEITURA, DEVE ABORTAR O PROCESSO.
            switch (numeroArquivo) {
                case "004":
                    preencheEstabelecimentos(path, index);
                    break;
                case "018":
                    preencheProfissionais(path, index);
                    break;
                case "021":
                    preencheCargaHoraria(path, index);
                    break;
                case "032":
                    preencheServicoEspecializado(path, index);
                    break;
                case "037":
                    preencheEquipes(path, index);
                    break;
                case "038":
                    preencheProfissionaisEquipes(path, index);
                    break;
            }

        } catch (IOException ex) {
            Logger.getLogger(GerarInformacoesCnes.class.getName()).log(Level.SEVERE, null, ex);
            throw new ValidacaoException(ex.getMessage());
        } catch (StringIndexOutOfBoundsException ex){
            Logger.getLogger(GerarInformacoesCnes.class.getName()).log(Level.SEVERE, null, ex);
            throw new ValidacaoException(Bundle.getStringApplication("msg_arquivo_cnes_com_formato_nao_padrao", NOME_ARQUIVO_CNES_TXT+index));
        }

    }

    public List<String> gerarVariacoes() {
        List<String> variacoes = new ArrayList<>();
        variacoes.add(NOME_ARQUIVO_CNES_TXT.toLowerCase());
        variacoes.add(NOME_ARQUIVO_CNES_TXT.toUpperCase());
        variacoes.add(primeiraLetraMaiuscula(NOME_ARQUIVO_CNES_TXT));
        return variacoes;
    }

    public static String primeiraLetraMaiuscula(String str) {
        return Character.toUpperCase(str.charAt(0)) + str.substring(1);
    }

    private void loadFile(String path, String index) throws FileNotFoundException {

        File f;
        for (String variation : variacoesNomeArquivo) {
            f = new File(path + variation + index);
            if (f.exists()) {
                reader = new FileReader(path + variation + index);
                buffer = new BufferedReader(reader);
                return;
            }
        }

        throw new FileNotFoundException();
    }


    private void preencheEstabelecimentos(String path, String index) throws IOException, ValidacaoException {


            cnesProcessoRootDTO.setEmpresas(new CnesProcessoEmpresaDTO());
            try {
                loadFile(path,index);
                // a primeira linha é um HEADER no arquivo txt, então é feita a leitura uma vez antes de entrar no while para começar a partir da segunda linha
                //// Se a linha nao for um HEADER ? tem que ser validado.
                //// Se mudar o leiaute ?
                String linha = buffer.readLine();
                while ((linha = buffer.readLine()) != null) {
                    CnesProcessoEmpresaDadosDTO cnesProcessoEmpresaDadosDTO = new CnesProcessoEmpresaDadosDTO();
                    cnesProcessoEmpresaDadosDTO.setUnidadeId(linha.substring(0, 31).trim());
                    cnesProcessoEmpresaDadosDTO.setCnes(linha.substring(31, 38).trim());
                    cnesProcessoEmpresaDadosDTO.setCnpjMantenedora(linha.substring(38, 52).trim());
                    cnesProcessoEmpresaDadosDTO.setPessoaFisicaJuridicaIdentificador(linha.substring(52, 53).trim());
                    cnesProcessoEmpresaDadosDTO.setRazaoSocial(linha.substring(53, 113).trim());
                    cnesProcessoEmpresaDadosDTO.setNomeFantasia(linha.substring(113, 173).trim());
                    cnesProcessoEmpresaDadosDTO.setCpf(linha.substring(173, 184).trim());
                    cnesProcessoEmpresaDadosDTO.setCnpj(linha.substring(184, 198).trim());
                    cnesProcessoEmpresaDadosDTO.setTipoUnidadeId(linha.substring(198, 200).trim());
                    cnesProcessoEmpresaDadosDTO.setSiglaEstado(linha.substring(200, 202).trim());
                    cnesProcessoEmpresaDadosDTO.setCodigoMunicipio(linha.substring(202, 209).trim());
                    cnesProcessoEmpresaDadosDTO.setCodigoNaturezaJuridica(linha.substring(209, 213).trim());
                    if (!linha.substring(213, 223).trim().isEmpty()) {
                        cnesProcessoEmpresaDadosDTO.setDataAtualizacao(converteStringParaDate(linha.substring(213, 223).trim()));
                    }
                    cnesProcessoEmpresaDadosDTO.setUsuarioAtualizacao(linha.substring(223, 235).trim());
                    cnesProcessoEmpresaDadosDTO.setRegiaoSaude(linha.substring(235, 238).trim());

                    mapEstabelecimentos.put(cnesProcessoEmpresaDadosDTO.getUnidadeId(), cnesProcessoEmpresaDadosDTO);
                }
            } catch (FileNotFoundException ex) {
                Logger.getLogger(GerarInformacoesCnes.class.getName()).log(Level.SEVERE, null, ex);
                throw new ValidacaoException(Bundle.getStringApplication("msg_arquivo_cnes_txt_nao_encontrado",NOME_ARQUIVO_CNES_TXT+index,"ESTABELECIMENTOS"));
            } finally {
                if (buffer != null) buffer.close();
                if (reader != null) reader.close();
            }
        }

    private void preencheProfissionais(String path, String index) throws IOException, ValidacaoException {

        try {
            loadFile(path, index);
            // a primeira linha é um HEADER no arquivo txt, então é feita a leitura uma vez antes de entrar no while para começar a partir da segunda linha
            String linha = buffer.readLine();
            while ((linha = buffer.readLine()) != null) {
                CnesProcessoProfissionaisDadosDTO cnesProcessoProfissionaisDadosDTO = new CnesProcessoProfissionaisDadosDTO();
                cnesProcessoProfissionaisDadosDTO.setProfissionalId(linha.substring(0, 16).trim());
                cnesProcessoProfissionaisDadosDTO.setCpfProfissional(linha.substring(16, 27).trim());
                cnesProcessoProfissionaisDadosDTO.setNomeProfissional(linha.substring(27, 87).trim());
                cnesProcessoProfissionaisDadosDTO.setCodigoCns(linha.substring(87, 102).trim());
                if (!linha.substring(102, 112).trim().isEmpty()) {
                    cnesProcessoProfissionaisDadosDTO.setDataAtualizacao(converteStringParaDate(linha.substring(102, 112).trim()));
                }
                cnesProcessoProfissionaisDadosDTO.setUsuario(linha.substring(112, 124).trim());
                cnesProcessoProfissionaisDadosDTO.setVinculosProfissionais(new CnesProcessoVinculosProfissionaisDTO());

                mapProfissionais.put(cnesProcessoProfissionaisDadosDTO.getProfissionalId(), cnesProcessoProfissionaisDadosDTO);
            }
        } catch (FileNotFoundException ex) {
            Logger.getLogger(GerarInformacoesCnes.class.getName()).log(Level.SEVERE, null, ex);
            throw new ValidacaoException(Bundle.getStringApplication("msg_arquivo_cnes_txt_nao_encontrado",NOME_ARQUIVO_CNES_TXT+index,"PROFISSIONAIS"));
        } finally {
            if (buffer != null) buffer.close();
            if (reader != null) reader.close();
        }
    }

    private void preencheCargaHoraria(String path, String index) throws IOException, ValidacaoException {

        try {
            loadFile(path, index);
            //a primeira linha é um HEADER no arquivo txt, então é feita a leitura uma vez antes de entrar no while para começar a partir da segunda linha
            String linha = buffer.readLine();
            while ((linha = buffer.readLine()) != null) {
                String unidadeId = linha.substring(0, 31).trim();
                String profissionalId = linha.substring(31, 47).trim();

                CnesProcessoEmpresaDadosDTO empresaEncontrada = mapEstabelecimentos.get(unidadeId);

                if (empresaEncontrada != null) {

                    CnesProcessoProfissionaisDadosDTO profissionalData = mapProfissionais.get(profissionalId);

                    if (profissionalData != null) {
                        if(empresaEncontrada.getProfissionais() == null){
                            empresaEncontrada.setProfissionais(new CnesProcessoProfissionaisDTO());
                        }

                        CnesProcessoVinculosProfissionaisDadosDTO cnesProcessoVinculosProfissionaisDTO = new CnesProcessoVinculosProfissionaisDadosDTO();
                        cnesProcessoVinculosProfissionaisDTO.setCodigoCbo(linha.substring(47, 53).trim());
                        cnesProcessoVinculosProfissionaisDTO.setIndicaVinculacao(linha.substring(54, 60).trim());
                        cnesProcessoVinculosProfissionaisDTO.setQuantidadeCargaHorariaOutro(Long.parseLong(linha.substring(60, 63).trim()));
                        cnesProcessoVinculosProfissionaisDTO.setQuantidadeCargaHorariaAmbulatorial(Long.parseLong(linha.substring(63, 66).trim()));
                        cnesProcessoVinculosProfissionaisDTO.setCodigoConselho(linha.substring(76, 78).trim());
                        cnesProcessoVinculosProfissionaisDTO.setNumeroRegistro(linha.substring(78, 91).trim());
                        cnesProcessoVinculosProfissionaisDTO.setQuantidadeCargaHorariaHospital(Long.parseLong(linha.substring(132, 135).trim()));
                        cnesProcessoVinculosProfissionaisDTO.setUnidadeId(unidadeId);

                        profissionalData.getVinculosProfissionais().addDadosVinculosProfissionaisList(cnesProcessoVinculosProfissionaisDTO);

                        empresaEncontrada.getProfissionais().addDadosProfissionaisList(profissionalData);

                    }
                }
            }
        } catch (FileNotFoundException ex) {
            Logger.getLogger(GerarInformacoesCnes.class.getName()).log(Level.SEVERE, null, ex);
            throw new ValidacaoException(Bundle.getStringApplication("msg_arquivo_cnes_txt_nao_encontrado",NOME_ARQUIVO_CNES_TXT+index,"VINCULOS DOS PROFISSIONAIS"));
        } finally {
            if (buffer != null) buffer.close();
            if (reader != null) reader.close();
        }
    }

    private void preencheServicoEspecializado(String path, String index) throws IOException, ValidacaoException {
        try{
            loadFile(path, index);
            //a primeira linha é um HEADER no arquivo txt, então é feita a leitura uma vez antes de entrar no while para começar a partir da segunda linha
            String linha = buffer.readLine();
            while ((linha = buffer.readLine()) != null) {
                String unidadeId = linha.substring(0, 31).trim();
                CnesProcessoServicosEspecializadosDadosDTO cnesProcessoServicosEspecializadosDadosDTO = new CnesProcessoServicosEspecializadosDadosDTO();
                cnesProcessoServicosEspecializadosDadosDTO.setCodigoServico(linha.substring(31, 34).trim());
                cnesProcessoServicosEspecializadosDadosDTO.setCodigoCaracteristica(linha.substring(34, 35).trim());
                cnesProcessoServicosEspecializadosDadosDTO.setCodigoServicoAmbulatorial(linha.substring(37, 38).trim());
                cnesProcessoServicosEspecializadosDadosDTO.setCodigoServicoAmbulatorialSus(linha.substring(38, 39).trim());
                cnesProcessoServicosEspecializadosDadosDTO.setCodigoServicoHospitalar(linha.substring(39, 40).trim());
                cnesProcessoServicosEspecializadosDadosDTO.setCodigoServicoHospitalarSus(linha.substring(40, 41).trim());
                cnesProcessoServicosEspecializadosDadosDTO.setUsuario(linha.substring(51, 63).trim());
                cnesProcessoServicosEspecializadosDadosDTO.setCodigoClassificacao(linha.substring(63, 66).trim());
                cnesProcessoServicosEspecializadosDadosDTO.setCnpjCpf(linha.substring(66, 80).trim());
                cnesProcessoServicosEspecializadosDadosDTO.setDescricaoClassificacao("");

                CnesProcessoEmpresaDadosDTO empresaEncontrada = mapEstabelecimentos.get(unidadeId);
                if (empresaEncontrada != null) {
                    if (empresaEncontrada.getServicosEspecializados() == null) {
                        empresaEncontrada.setServicosEspecializados(new CnesProcessoServicosEspecializadosDTO());
                    }
                    empresaEncontrada.getServicosEspecializados().addDadosServicosEspecializadosList(cnesProcessoServicosEspecializadosDadosDTO);
                }
            }
        } catch (FileNotFoundException ex) {
            Logger.getLogger(GerarInformacoesCnes.class.getName()).log(Level.SEVERE, null, ex);
            throw new ValidacaoException(Bundle.getStringApplication("msg_arquivo_cnes_txt_nao_encontrado",NOME_ARQUIVO_CNES_TXT+index,"SERVICOS ESPECIALIZADOS"));
        } finally {
            if (buffer != null) buffer.close();
            if (reader != null) reader.close();
        }
    }


    private void preencheEquipes(String path, String index) throws IOException, ValidacaoException {
        try {
            loadFile(path, index);
            //a primeira linha é um HEADER no arquivo txt, então é feita a leitura uma vez antes de entrar no while para começar a partir da segunda linha
            String linha = buffer.readLine();
            while ((linha = buffer.readLine()) != null) {
                CnesProcessoEquipesDadosDTO cnesProcessoEquipesDadosDTO = new CnesProcessoEquipesDadosDTO();
                cnesProcessoEquipesDadosDTO.setCodigoMunicipio(linha.substring(0, 6).trim());
                cnesProcessoEquipesDadosDTO.setCodigoArea(linha.substring(6, 10).trim());
                cnesProcessoEquipesDadosDTO.setSequencialEquipe(Long.parseLong(linha.substring(10, 18).trim()));
                cnesProcessoEquipesDadosDTO.setUnidadeId(linha.substring(18, 49).trim());
                cnesProcessoEquipesDadosDTO.setTipoEquipe(linha.substring(49, 51).trim());
                cnesProcessoEquipesDadosDTO.setNomeReferencia(linha.substring(51, 111).trim());
                cnesProcessoEquipesDadosDTO.setDescricaoArea("");
                cnesProcessoEquipesDadosDTO.setCodigoSegmento("");
                cnesProcessoEquipesDadosDTO.setTipoSegmento("");
                cnesProcessoEquipesDadosDTO.setDescricaoSegmento("");
                if (!linha.substring(111, 121).trim().isEmpty()) {
                    cnesProcessoEquipesDadosDTO.setDataAtivacao(converteStringParaDate(linha.substring(111, 121).trim()));
                }
                if (!linha.substring(121, 131).trim().isEmpty()) {
                    cnesProcessoEquipesDadosDTO.setDataDesativacao(converteStringParaDate(linha.substring(121, 131).trim()));
                }
                cnesProcessoEquipesDadosDTO.setCodigoMotivoDesativacao(linha.substring(131, 133).trim());
                cnesProcessoEquipesDadosDTO.setCodigoTipoDesativacao(linha.substring(133, 135).trim());
                cnesProcessoEquipesDadosDTO.setUsuario(linha.substring(146, 158).trim());
                cnesProcessoEquipesDadosDTO.setCodigoEquipe(linha.substring(181, 191).trim());

                mapEquipes.put(cnesProcessoEquipesDadosDTO.getCodigoEquipe(), cnesProcessoEquipesDadosDTO);
            }
        } catch (FileNotFoundException ex) {
            Logger.getLogger(GerarInformacoesCnes.class.getName()).log(Level.SEVERE, null, ex);
            throw new ValidacaoException(Bundle.getStringApplication("msg_arquivo_cnes_txt_nao_encontrado",NOME_ARQUIVO_CNES_TXT+index,"EQUIPES"));
        } finally {
            if (buffer != null) buffer.close();
            if (reader != null) reader.close();
        }
    }

    private void preencheProfissionaisEquipes(String path, String index) throws IOException, ValidacaoException {
        try {
            loadFile(path, index);
            //a primeira linha é um HEADER no arquivo txt, então é feita a leitura uma vez antes de entrar no while para começar a partir da segunda linha
            String linha = buffer.readLine();
            while ((linha = buffer.readLine()) != null) {

                String unidadeId = linha.substring(34, 65).trim();
                String codEquipe = linha.substring(123, 133).trim();
                CnesProcessoEquipeProfissionalDadosDTO cnesProcessoEquipeProfissionalDadosDTO = getCnesProcessoEquipeProfissionalDadosDTO(linha);

                //ONDE ESTA VINCULANDO A EQUIPE A EMPRESA ?
                //VINCULAR A EQUIPE NA EMPRESA NA LEITURA DA EQUIPE.
                    CnesProcessoEquipesDadosDTO equipeEncontrada = mapEquipes.get(codEquipe);
                    if (equipeEncontrada != null) {
                        if (equipeEncontrada.getEquipeProfissional() == null) {
                            equipeEncontrada.setEquipeProfissional(new CnesProcessoEquipeProfissionalDTO());
                        }
                        equipeEncontrada.getEquipeProfissional().addDadosProfissionalEquipeList(cnesProcessoEquipeProfissionalDadosDTO);
                    }

                    CnesProcessoEmpresaDadosDTO empresaEncontrada = mapEstabelecimentos.get(unidadeId);
                    if (empresaEncontrada != null) {
                        if (empresaEncontrada.getEquipes() == null) {
                            empresaEncontrada.setEquipes(new CnesProcessoEquipesDTO());
                        }
                        empresaEncontrada.getEquipes().addDadosEquipesList(equipeEncontrada);
                    }
                }
        } catch (FileNotFoundException ex) {
            Logger.getLogger(GerarInformacoesCnes.class.getName()).log(Level.SEVERE, null, ex);
            throw new ValidacaoException(Bundle.getStringApplication("msg_arquivo_cnes_txt_nao_encontrado",NOME_ARQUIVO_CNES_TXT+index,"PROFISSIONAIS DAS EQUIPES"));
        } finally {
            if (buffer != null) buffer.close();
            if (reader != null) reader.close();
        }
    }

}


/////////// LIMPAR

/*
 *********** DELETES PARA TESTES DAS PRIMEIRAS INTEGRAÇÕES EM BASE ZERADA ***********
 */
//delete from evento_sistema;
//delete from profissional_historico;
//delete FROM cnes_processo_ocorrencia;
//update equipe_micro_area set cd_equipe_profissional = null;
//delete FROM equipe_profissional;
//delete FROM equipe_micro_area;
//delete FROM equipe;
//delete FROM equipe_area;
//delete FROM segmento_territorial;
//delete FROM empresa_servico_class;
//delete FROM profissional_carga_horaria;
//delete FROM empresa_material where empresa <> 0;
//delete FROM empresa where empresa <> 0;
//delete FROM profissional where referencia <> '1';
//delete from cnes_processo_serv_especializados;
//delete from cnes_processo_prof_vinculo;
//delete from cnes_processo_profissional;
//delete from cnes_processo_habilitacao;
//delete from cnes_processo_equipe_prof;
//delete from cnes_processo_equipe;
//delete from cnes_processo_empresa;
//delete from cnes_processo;
