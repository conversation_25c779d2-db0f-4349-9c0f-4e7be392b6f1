package br.com.ksisolucoes.bo.basico.usuario;

import br.com.celk.bo.service.rest.fiscalnarua.UsuarioFiscalNaRuaRestDTO;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoUtil;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;

import java.io.Serializable;
import java.util.Date;

public class AtualizarDataTermoUsoFruUsuario extends AbstractCommandTransaction<AtualizarDataTermoUsoFruUsuario> implements Serializable {

    private long id;
    private UsuarioFiscalNaRuaRestDTO userDto;
    private String ip;

    private Usuario usuario;

    public AtualizarDataTermoUsoFruUsuario(long id, UsuarioFiscalNaRuaRestDTO userDto, String ip) {
        this.id = id;
        this.userDto = userDto;
        this.ip = ip;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        validar();
        SessaoUtil.createApplicationSession(ip);
        atualizarUsuario();
    }

    private void validar() throws ValidacaoException {
        if (userDto == null || userDto.getDataAceiteTermoUsoFru() == null) {
            throw new ValidacaoException("Data de aceite não pode estar vazia!");
        }

        if (getDataConverted().after(DataUtil.getDataAtual())) {
            throw new ValidacaoException("Data de aceite não pode ser posterior a hoje!");
        }

        usuario = LoadManager.getInstance(Usuario.class)
                .addProperties(new HQLProperties(Usuario.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(Usuario.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, id))
                .setMaxResults(1)
                .start().getVO();

        if (usuario == null) {
            throw new ValidacaoException("Usuário não existe!");
        }
    }

    public void atualizarUsuario() throws DAOException, ValidacaoException {
        usuario.setDataAceiteTermoUsoFru(getDataConverted());
        BOFactory.save(usuario);
    }

    public Date getDataConverted() {
        Date date = null;
        try {
            date = DataUtil.stringToDateHourMinMs(userDto.getDataAceiteTermoUsoFru());
        } catch (Exception ex) {
            Loggable.log.warn(ex.getMessage(), ex);
        }
        return date;
    }
}
