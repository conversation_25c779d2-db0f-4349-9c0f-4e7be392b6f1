package br.com.ksisolucoes.bo.prontuario.basico.classificacaoatendimento;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.ClassificacaoAtendimento;

public class SaveClassificacaoAtendimento extends SaveVO {

    private ClassificacaoAtendimento classificacaoAtendimento;

    public SaveClassificacaoAtendimento(Object vo) {
        super( vo );
        this.classificacaoAtendimento = (ClassificacaoAtendimento) vo;
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.classificacaoAtendimento.getExibeEncaminhamentoAlta() == null) {
            this.classificacaoAtendimento.setExibeEncaminhamentoAlta(RepositoryComponentDefault.SIM_LONG);
        }
        if (this.classificacaoAtendimento.getDescricao() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("rotulo_digite_descricao"));
        }

        if(this.classificacaoAtendimento.getSituacao() == null){
            this.classificacaoAtendimento.setSituacao(ClassificacaoAtendimento.Situacao.ATIVO.value());

        }

    }
    
}