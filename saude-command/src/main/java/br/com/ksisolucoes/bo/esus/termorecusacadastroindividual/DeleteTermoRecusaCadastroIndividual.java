package br.com.ksisolucoes.bo.esus.termorecusacadastroindividual;

import br.com.ksisolucoes.bo.command.DeleteVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.esus.EsusIntegracaoCds;
import br.com.ksisolucoes.vo.esus.TermoRecusaCadastroIndividual;
import org.hibernate.criterion.Restrictions;

/**
 * Created by laudecir on 13/10/17.
 */
public class DeleteTermoRecusaCadastroIndividual extends DeleteVO<TermoRecusaCadastroIndividual> {

    public DeleteTermoRecusaCadastroIndividual(TermoRecusaCadastroIndividual vo) {
        super(vo);
    }

    @Override
    protected void antesDelete() throws DAOException, ValidacaoException {
        EsusIntegracaoCds esusIntegracaoCds = (EsusIntegracaoCds) getSession().createCriteria(EsusIntegracaoCds.class)
                .add(Restrictions.eq(EsusIntegracaoCds.PROP_TERMO_RECUSA_CADASTRO_INDIVIDUAL, this.vo))
                .uniqueResult();

        if (esusIntegracaoCds != null) {
            BOFactory.delete(esusIntegracaoCds);
        }
    }
}
