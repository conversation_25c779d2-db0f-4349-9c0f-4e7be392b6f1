package br.com.ksisolucoes.bo.controle.usuario;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Permissao;
import br.com.ksisolucoes.vo.controle.Programa;
import java.util.List;
import java.util.Map;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QueryProgramasGrupoDesktop extends CommandQuery<QueryProgramasGrupoDesktop> {

    private Long codigoGrupo;
    private List<Programa> result; 

    public QueryProgramasGrupoDesktop(Long codigoGrupo) {
        this.codigoGrupo = codigoGrupo;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect("programa.codigo", true);
        hql.addToSelect("programa.descricao", true);
        
        hql.setTypeSelect(Programa.class.getName());
        hql.addToFrom("ControleProgramaGrupo controleProgramaGrupo"
                + " left join controleProgramaGrupo.grupo grupo"
                + " left join controleProgramaGrupo.programa programa");
        
        hql.addToWhereWhithAnd("grupo.codigo =", codigoGrupo);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        List<Permissao> permissoes = new QueryPermissoesGrupoDesktop(codigoGrupo).start().getResult();
        for (Permissao permissao : permissoes) {
            this.result.get(this.result.indexOf(permissao.getPrograma())).getPermissoes().add(permissao);
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<Programa> getResult() {
        return result;
    }
    
}
