package br.com.ksisolucoes.bo.entradas.estoque.ordemcompra;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.OrdemCompra;

/**
 *
 * <AUTHOR>
 */
public class SaveOrdemCompra extends SaveVO<OrdemCompra> {

    private OrdemCompra ordemCompra;

    public SaveOrdemCompra(OrdemCompra vo) {
        super(vo);
        this.ordemCompra = vo;
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if(ordemCompra.getEmpresa() == null){
            ordemCompra.setEmpresa((Empresa) getSessao().getEmpresa());
        }
        if(ordemCompra.getUsuario() == null){
            ordemCompra.setUsuario((Usuario) getSessao().getUsuario());
        }
        if(ordemCompra.getDataCadastro() == null){
            ordemCompra.setDataCadastro(DataUtil.getDataAtual());
        }
        if(ordemCompra.getStatus() == null){
            ordemCompra.setStatus(OrdemCompra.Status.PENDENTE.value());
        }
    }
}
