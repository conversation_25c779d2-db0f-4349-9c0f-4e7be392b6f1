package br.com.ksisolucoes.bo.vigilancia.externo;

import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.celk.vigilancia.dto.UsuarioExternoVigilanciaCadastroDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.Usuario.TipoUsuario;
import br.com.ksisolucoes.vo.controle.UsuarioEmpresa;
import br.com.ksisolucoes.vo.controle.base.BaseUsuario;
import br.com.ksisolucoes.vo.controle.base.BaseUsuarioEmpresa;
import br.com.ksisolucoes.vo.controle.web.UsuarioGrupo;
import br.com.ksisolucoes.vo.controle.web.base.BaseUsuarioGrupo;
import br.com.ksisolucoes.vo.vigilancia.externo.UsuarioVigilancia;
import br.com.ksisolucoes.vo.vigilancia.externo.base.BaseUsuarioVigilancia;
import org.hibernate.criterion.Restrictions;

/**
 * <AUTHOR>
 */
public class CadastrarUsuarioExternoVigilancia extends AbstractCommandTransaction<CadastrarUsuarioExternoVigilancia> {

    private UsuarioExternoVigilanciaCadastroDTO dto;
    private Empresa empresa;
    private UsuarioGrupo usuarioGrupo = null;
    private UsuarioEmpresa usuarioEmpresa = null;


    public CadastrarUsuarioExternoVigilancia(UsuarioExternoVigilanciaCadastroDTO dto) {
        this.dto = dto;
        this.empresa = dto.getEmpresaLogada();
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        UsuarioVigilancia usuarioVigilanciaBase = existeUsuarioVigilanciaCpf();

        if (usuarioVigilanciaBase != null && usuarioVigilanciaBase.getCodigo() != null) {
            Usuario usuarioBase;
            if (usuarioVigilanciaBase.getUsuario() != null) {
                usuarioBase = usuarioVigilanciaBase.getUsuario();
            } else {
                usuarioBase = this.getSessao().getUsuario();
                if (!StringUtil.getDigits(dto.getUsuarioVigilancia().getCpf()).equals(usuarioBase.getLogin())) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_registro_nao_pode_ser_excluido"));
                }
                usuarioVigilanciaBase.setUsuario(usuarioBase);
            }
            Usuario usuario = getUsuario(usuarioBase);
            usuarioGrupo = getUsuarioGrupo(usuarioBase);

            usuarioEmpresa = (UsuarioEmpresa) getSession().createCriteria(UsuarioEmpresa.class)
                    .add(Restrictions.eq(VOUtils.montarPath(BaseUsuarioEmpresa.PROP_USUARIO, BaseUsuario.PROP_CODIGO), usuarioBase.getCodigo()))
                    .add(Restrictions.eq(VOUtils.montarPath(BaseUsuarioEmpresa.PROP_EMPRESA), dto.getEmpresaLogada()))
                    .uniqueResult();

            usuario.setStatus(dto.getSituacao());
            usuario.setTelefone(StringUtil.getDigits(dto.getUsuarioVigilancia().getTelefone()));
            usuario.setEmail(dto.getUsuarioVigilancia().getEmail());
            usuarioVigilanciaBase.setNome(dto.getUsuarioVigilancia().getNome());
            usuarioVigilanciaBase.setTelefone(StringUtil.getDigits(dto.getUsuarioVigilancia().getTelefone()));
            usuarioVigilanciaBase.setEmail(dto.getUsuarioVigilancia().getEmail());

            if (dto.getNovaSenha() != null) {
                String senhaCriptografada = Util.criptografarSenha(dto.getNovaSenha());
                usuario.setSenha(senhaCriptografada);
                usuarioVigilanciaBase.setSenha(senhaCriptografada);
            }

            BOFactory.save(criarUsuarioGrupo(usuarioVigilanciaBase.getUsuario()));
            if (Usuario.STATUS_ATIVO.equals(dto.getSituacao())) {
                BOFactory.save(criarUsuarioEmpresa(usuarioVigilanciaBase.getUsuario()));
            } else if (usuarioEmpresa != null) {
                BOFactory.delete(usuarioEmpresa);
            }

            BOFactory.save(usuario);
            BOFactory.save(usuarioVigilanciaBase);
        } else {
            Usuario usuario = dto.getUsuarioVigilancia().getUsuario();
            UsuarioVigilancia saveUV = BOFactory.save(criarUsuarioVigilancia());

            Usuario saveU = BOFactory.save(createOrUpdateUsuario(usuario, saveUV));

            BOFactory.save(criarUsuarioGrupo(saveU));
            if (Usuario.STATUS_ATIVO.equals(dto.getSituacao())) {
                BOFactory.save(criarUsuarioEmpresa(usuario));
            }

            saveUV.setUsuario(usuario);
            BOFactory.save(saveUV);
        }
    }

    public Usuario getUsuario(Usuario usuarioBase) {
        return (Usuario) getSession().createCriteria(Usuario.class)
                .add(Restrictions.eq(VOUtils.montarPath(BaseUsuario.PROP_CODIGO), usuarioBase.getCodigo()))
                .uniqueResult();
    }

    private UsuarioEmpresa criarUsuarioEmpresa(Usuario usuario) {
        if (usuarioEmpresa == null) {
            usuarioEmpresa = new UsuarioEmpresa();
        }

        usuarioEmpresa.setDataAtivacao(DataUtil.getDataAtual());
        usuarioEmpresa.setEmpresa(empresa);
        usuarioEmpresa.setUsuario(usuario);
        usuarioEmpresa.setNivel(dto.getNivel());

        return usuarioEmpresa;
    }

    private UsuarioGrupo criarUsuarioGrupo(Usuario usuario) {
        if (usuarioGrupo == null) {
            usuarioGrupo = new UsuarioGrupo();
        }

        UsuarioVigilancia uv = buscarUsuarioVigilanciaLogado(dto.getUsuarioLogado());

        usuarioGrupo.setUsuario(usuario);

        if (uv.getPerfilUsuarioExternoVigilancia() != null) {
            usuarioGrupo.setGrupo(uv.getPerfilUsuarioExternoVigilancia().getFuncao());
        } else {
            UsuarioGrupo grupoUsuarioLogado = getUsuarioGrupo(dto.getUsuarioLogado());
            usuarioGrupo.setGrupo(grupoUsuarioLogado == null ? null : grupoUsuarioLogado.getGrupo());
        }

        return usuarioGrupo;
    }

    private Usuario createOrUpdateUsuario(Usuario usuario, UsuarioVigilancia usuarioVigilancia) {
        if (usuario == null) {
            usuario = new Usuario();
        }

        usuario.setTipoUsuario(TipoUsuario.USUARIO_VIGILANCIA.value());
        usuario.setEmail(usuarioVigilancia.getEmail());
        usuario.setLogin(usuarioVigilancia.getCpf());
        usuario.setNome(usuarioVigilancia.getNome());
        usuario.setSenha(usuarioVigilancia.getSenha());
        usuario.setTelefone(usuarioVigilancia.getTelefone());
        usuario.setCpf(usuarioVigilancia.getCpf());
        usuario.setStatus(dto.getSituacao());
        usuario.setNivel(Usuario.NIVEL_NORMAL);
        usuario.setFlagUsuarioTemporario(RepositoryComponentDefault.NAO_LONG);
        usuario.setDataRegistro(DataUtil.getDataAtual());

        return usuario;
    }

    private UsuarioVigilancia criarUsuarioVigilancia() throws ValidacaoException {
        String cpf = StringUtil.getDigits(dto.getUsuarioVigilancia().getCpf());
        String telefone = StringUtil.getDigits(dto.getUsuarioVigilancia().getTelefone());
        String senhaCriptografada = null;
        if (dto.getNovaSenha() != null) {
            senhaCriptografada = Util.criptografarSenha(dto.getNovaSenha());
        } else {
            throw new ValidacaoException("Informe a nova Senha");
        }

        UsuarioVigilancia usuarioVigilancia = dto.getUsuarioVigilancia();

        usuarioVigilancia.setDataConfirmacao(DataUtil.getDataAtual());
        usuarioVigilancia.setCpf(cpf);
        usuarioVigilancia.setDataSolicitacao(DataUtil.getDataAtual());
        usuarioVigilancia.setSenha(senhaCriptografada);
        usuarioVigilancia.setTelefone(telefone);
        usuarioVigilancia.setDataConfirmacao(DataUtil.getDataAtual());

        return usuarioVigilancia;
    }

    private UsuarioVigilancia buscarUsuarioVigilanciaLogado(Usuario usuarioLogado) {
        return (UsuarioVigilancia) getSession().createCriteria(UsuarioVigilancia.class)
                .add(Restrictions.eq(VOUtils.montarPath(BaseUsuarioVigilancia.PROP_USUARIO), usuarioLogado))
                .setMaxResults(1)
                .uniqueResult();
    }

    private UsuarioVigilancia existeUsuarioVigilanciaCpf() {
        return (UsuarioVigilancia) getSession().createCriteria(UsuarioVigilancia.class)
                .add(Restrictions.eq(VOUtils.montarPath(BaseUsuarioVigilancia.PROP_CPF), StringUtil.getDigits(dto.getUsuarioVigilancia().getCpf())))
                .setMaxResults(1)
                .uniqueResult();
    }

    private UsuarioGrupo getUsuarioGrupo(Usuario usuario) {
        return (UsuarioGrupo) getSession().createCriteria(UsuarioGrupo.class)
                .add(Restrictions.eq(VOUtils.montarPath(BaseUsuarioGrupo.PROP_USUARIO, BaseUsuario.PROP_CODIGO), usuario.getCodigo()))
                .setMaxResults(1)
                .uniqueResult();
    }
}
