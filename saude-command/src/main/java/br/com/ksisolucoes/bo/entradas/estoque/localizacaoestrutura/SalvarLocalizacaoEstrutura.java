/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.entradas.estoque.localizacaoestrutura;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.materiais.localizacao.interfaces.dto.CadastroEstruturaLocalizacaoDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura;

/**
 * <AUTHOR>
 */
public class SalvarLocalizacaoEstrutura extends AbstractCommandTransaction<SalvarLocalizacaoEstrutura> {

    private CadastroEstruturaLocalizacaoDTO dto;

    public SalvarLocalizacaoEstrutura(CadastroEstruturaLocalizacaoDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        validar(dto);
        validarRecursividade(dto);

        BOFactory.save(dto.getLocalizacaoEstrutura());

    }

    private void validarRecursividade(CadastroEstruturaLocalizacaoDTO object) throws ValidacaoException {
        if (object.getLocalizacaoEstrutura() != null && object.getLocalizacaoEstrutura().getCodigo() != null && object.getLocalizacaoEstrutura().getLocalizacaoEstruturaPai() != null &&
                object.getLocalizacaoEstrutura().getCodigo().equals(object.getLocalizacaoEstrutura().getLocalizacaoEstruturaPai().getCodigo())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_localizacao_estrutura_recursiva",
                    object.getLocalizacaoEstrutura().getCodigo(),
                    object.getLocalizacaoEstrutura().getLocalizacaoEstruturaPai().getCodigo()));
        }

    }

    private void validar(CadastroEstruturaLocalizacaoDTO object) throws ValidacaoException {
        boolean existsRegistro = LoadManager.getInstance(LocalizacaoEstrutura.class)
                .addParameter(new QueryCustom.QueryCustomParameter(LocalizacaoEstrutura.PROP_CODIGO, BuilderQueryCustom.QueryParameter.DIFERENTE, object.getLocalizacaoEstrutura().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(LocalizacaoEstrutura.PROP_LOCALIZACAO, object.getLocalizacaoEstrutura().getLocalizacao()))
                .addParameter(new QueryCustom.QueryCustomParameter(LocalizacaoEstrutura.PROP_LOCALIZACAO_ESTRUTURA_PAI, object.getLocalizacaoEstrutura().getLocalizacaoEstruturaPai()))
                .exists();
        if (existsRegistro) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_existe_localizacao_estrutura_cadastrada"));
        }
    }

}
