package br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.node;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.AbstractCommandValidacaoV2;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.annotations.ValidacaoProntuarioNode;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.ConsultaEncaminhamentoEspecialistaDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.EncaminhamentoEspecialistaRegistroDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;

/**
 *
 * <AUTHOR>
 */
@ValidacaoProntuarioNode(value = NodesAtendimentoRef.ENCAMINHAMENTO_ESPECIALISTA, refClass = ConsultaEncaminhamentoEspecialistaDTO.class)
public class ValidarEncaminhamentoEspecialistaNode extends AbstractCommandValidacaoV2<ConsultaEncaminhamentoEspecialistaDTO> {

    public ValidarEncaminhamentoEspecialistaNode(Atendimento atendimento) {
        super(atendimento);
    }

    @Override
    public ConsultaEncaminhamentoEspecialistaDTO executarValidacao(ConsultaEncaminhamentoEspecialistaDTO object) throws DAOException, ValidacaoException {
        if(object != null) {
            for (EncaminhamentoEspecialistaRegistroDTO dto : object.getEncaminhamentoEspecialistaRegistroDTOList()) {
                if (dto.isObrigatorioAgendamento()) {
                    if (CollectionUtils.isEmpty(dto.getAgendaGradeAtendimentoHorarioList())) {
                        throw new ValidacaoException("No encaminhamento deve ser realizado o agendamento da especialidade " + dto.getEncaminhamentoConsulta().getEncaminhamento().getTipoEncaminhamento().getDescricaoFormatado());
                    }
                }
            }
        }
        return object;
    }

    @Override
    public void processar(ConsultaEncaminhamentoEspecialistaDTO obj) throws DAOException, ValidacaoException {

    }
}
