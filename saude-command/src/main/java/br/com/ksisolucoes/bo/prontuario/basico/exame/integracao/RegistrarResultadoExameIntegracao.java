package br.com.ksisolucoes.bo.prontuario.basico.exame.integracao;

import br.com.celk.integracao.laboratorio.connect.dto.ExameDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import org.hibernate.criterion.Restrictions;

/**
 * <AUTHOR>
 * <PERSON>o em: Apr 15, 2013
 */
public class RegistrarResultadoExameIntegracao extends AbstractCommandTransaction{

    private Long chaveExame;
    private ExameDTO dto;

    public RegistrarResultadoExameIntegracao(Long chaveExame, ExameDTO dto) {
        this.chaveExame = chaveExame;
        this.dto = dto;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
    
        ExameRequisicao exameRequisicao = (ExameRequisicao) getSession().createCriteria(ExameRequisicao.class)
                .add(Restrictions.eq(ExameRequisicao.PROP_CHAVE_INTEGRACAO, chaveExame))
                .uniqueResult();
        if(exameRequisicao != null){
            exameRequisicao.setDescricaoResultado(dto.getDescricaoResultado());
            exameRequisicao.setDataColeta(dto.getDataColeta());
            
            BOFactory.save(exameRequisicao);
        }
    }
}
