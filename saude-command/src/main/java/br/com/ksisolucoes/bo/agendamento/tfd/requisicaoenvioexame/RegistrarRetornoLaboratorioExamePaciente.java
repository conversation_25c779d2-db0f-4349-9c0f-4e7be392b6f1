/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.agendamento.tfd.requisicaoenvioexame;

import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.RequisicaoEnvioExame;
import br.com.ksisolucoes.vo.agendamento.tfd.RequisicaoEnvioOcorrencia;

/**
 *
 * <AUTHOR>
 */
public class RegistrarRetornoLaboratorioExamePaciente extends AbstractCommandTransaction<RegistrarRetornoLaboratorioExamePaciente> {

    private Long codigoRequisicaoEnvioExame;

    public RegistrarRetornoLaboratorioExamePaciente(Long codigoRequisicaoEnvioExame) {
        this.codigoRequisicaoEnvioExame = codigoRequisicaoEnvioExame;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        RequisicaoEnvioExame requisicaoEnvioExame = (RequisicaoEnvioExame) this.getSession().get(RequisicaoEnvioExame.class, this.codigoRequisicaoEnvioExame);

        requisicaoEnvioExame.setStatus(RequisicaoEnvioExame.STATUS_RECEBIDO);
        requisicaoEnvioExame.setDataRetornoExame(Data.getDataAtual());

        BOFactory.getBO(CadastroFacade.class).save(requisicaoEnvioExame);


        RequisicaoEnvioOcorrencia requisicaoEnvioOcorrencia = new RequisicaoEnvioOcorrencia();
        
        requisicaoEnvioOcorrencia.setRequisicaoEnvioExame(requisicaoEnvioExame);
        requisicaoEnvioOcorrencia.setDescricao(Bundle.getStringApplication("rotulo_confirmado_retorno_resultado_exame"));

        BOFactory.getBO(CadastroFacade.class).save(requisicaoEnvioOcorrencia);
        
    }
}
