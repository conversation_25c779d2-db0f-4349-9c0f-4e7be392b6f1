package br.com.ksisolucoes.bo.hospital.exportacao.producaoipe.atendimentocomplementar.bind;

import br.com.celk.services.mobile.integracao.exportarrecurso.IBindVoExport;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.hospital.exportacao.producaoipe.dto.LancamentosAtendimentoComplementarDTO;
import java.util.Date;
import org.apache.camel.dataformat.bindy.annotation.DataField;
import org.apache.camel.dataformat.bindy.annotation.FixedLengthRecord;

/**
 *
 * <AUTHOR>
 */
@FixedLengthRecord(length = 131, crlf = "WINDOWS")
public class LancamentosAtendimentoComplementarBind implements IBindVoExport<LancamentosAtendimentoComplementarDTO> {

    @DataField(pos = 1, length = 2, paddingChar = '0', align = "R", required = true)
    private Long nroLinha;
    @DataField(pos = 2, length = 13, align = "R", paddingChar = '0', required = true)
    private String matricula;
    @DataField(pos = 3, length = 8, align = "R", required = true)
    private String nroContratoSolicitante;
    @DataField(pos = 4, length = 2, align = "R", pattern = "dd", required = true)
    private Date dia;
    @DataField(pos = 5, length = 8, align = "R", paddingChar = '0', required = true)
    private String codHonorario;
    @DataField(pos = 6, length = 5, align = "R", paddingChar = '0', required = true)
    private Double quantidade;
    @DataField(pos = 7, length = 43, align = "L", required = true)
    private String nomeBeneficiario;
    @DataField(pos = 8, length = 50, align = "L", required = true)
    private String arquivoPdf;

    @Override
    public void buildProperties(LancamentosAtendimentoComplementarDTO vo) {
        
        nroLinha = vo.getReferencial();
        matricula = vo.getAtendimentoPrincipal().getNumeroRegistroConvenio();
        nroContratoSolicitante = vo.getTipoPrestadorIpe().getTipo()+vo.getEmpresaPrincipal().getNumeroPrestadorIpe().substring(2);
        dia = vo.getAtendimentoInformacao().getDataChegada();
        codHonorario = vo.getCodigoHonorario();
        quantidade = vo.getItemContaPaciente().getQuantidade();
        nomeBeneficiario = vo.getUsuarioCadsus().getNome();
        
        if(vo.getItemContaPaciente().getCaminhoAnexo() != null){
            arquivoPdf = vo.getItemContaPaciente().getContaPaciente().getCodigo() + "_" + vo.getItemContaPaciente().getCodigo() + "_" 
                    + DataUtil.getFormatarDiaMesAnoUnderline(vo.getItemContaPaciente().getDataLancamento()) + ".pdf";
        }

    }
}