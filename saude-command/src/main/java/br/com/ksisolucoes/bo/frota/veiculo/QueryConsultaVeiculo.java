package br.com.ksisolucoes.bo.frota.veiculo;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.frota.interfaces.dto.QueryConsultaVeiculoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.vo.frota.Veiculo;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaVeiculo extends CommandQueryPager<QueryConsultaVeiculo> {

    private QueryConsultaVeiculoDTOParam param;

    public QueryConsultaVeiculo(QueryConsultaVeiculoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
    
        hql.addToSelectAndGroup(new HQLProperties(Veiculo.class, "veiculo").getProperties());

        hql.setConvertToLeftJoin(true);
        hql.setTypeSelect(Veiculo.class.getName());
        hql.addToFrom("VeiculoEmpresa veiculoEmpresa "
                + " right join veiculoEmpresa.veiculo veiculo "
                + " left join veiculoEmpresa.empresa empresa ");

        hql.addToWhereWhithAnd(hql.getConsultaLiked("veiculo.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("veiculo.placa", param.getPlaca()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("COALESCE(veiculo.placa, '') || ' ' || COALESCE(veiculo.descricao, '')", param.getKeyword()));

        if(param.isFlagValidaVeiculoAtivo()) {
            hql.addToWhereWhithAnd("veiculo.situacao = ", Veiculo.Situacao.ATIVO.value());
        }
        if (param.isFlagValidaEstabelecimento()) {
            hql.addToWhereWhithAnd("(case when veiculoEmpresa is null" +
                "   then true " +
                "   else (case when veiculoEmpresa.empresa = " + SessaoAplicacaoImp.getInstance().getEmpresa().getCodigo() + " then true else false end) end) = true ");
        }

        if (param.getPropSort() != null) {
            hql.addToOrder("veiculo." + param.getPropSort() + " " + (param.isAscending() ? "asc" : "desc"));
        } else {
            hql.addToOrder("veiculo.descricao");
        }
    }

    @Override
    protected HQLHelper customHQLCount(HQLHelper hql) {

        hql.setSelect("count(distinct veiculo.codigo)");
        hql.setGroup("");
        
        return hql;
    }
    

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
