package br.com.ksisolucoes.bo.tfd;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.frota.DiarioBordoVeiculo;
import br.com.ksisolucoes.vo.frota.RoteiroViagem;
import br.com.ksisolucoes.vo.frota.RoteiroViagemPassageiro;
import br.com.ksisolucoes.vo.frota.viagem.solicitacao.SolicitacaoViagem;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Jonas Américo on 28/11/18.
 */
public class ClonarRoteiroViagemTfd extends AbstractCommandTransaction<ClonarRoteiroViagemTfd> {

    private RoteiroViagem roteiroViagem;
    private List<RoteiroViagemPassageiro> roteiroViagemPassageiroList;
    private DiarioBordoVeiculo diarioBordoVeiculo = new DiarioBordoVeiculo();
    private Long codigoClone;


    public ClonarRoteiroViagemTfd(RoteiroViagem roteiroViagem, Long codigoClone) {
        this.roteiroViagem = roteiroViagem;
        this.codigoClone = codigoClone;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        List<RoteiroViagemPassageiro> rvpList = carregarManutencaoPassageiro(codigoClone);

        RoteiroViagemPassageiro roteiroViagemPassageiro;

        for (RoteiroViagemPassageiro viagemPassageiro : rvpList) {
            viagemPassageiro.setRoteiro(roteiroViagem);
            BOFactory.save(viagemPassageiro);
        }
    }

    private List<RoteiroViagemPassageiro> carregarManutencaoPassageiro(Long codigoClone) {
        List<RoteiroViagemPassageiro> lst = LoadManager.getInstance(RoteiroViagemPassageiro.class)
                .addProperties(new HQLProperties(RoteiroViagemPassageiro.class).getProperties())
                .addProperties(new HQLProperties(SolicitacaoViagem.class, RoteiroViagemPassageiro.PROP_SOLICITACAO_VIAGEM).getProperties())
                .addProperties(new HQLProperties(UsuarioCadsus.class, RoteiroViagemPassageiro.PROP_USUARIO_CADSUS).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RoteiroViagemPassageiro.PROP_ROTEIRO, br.com.ksisolucoes.vo.frota.RoteiroViagem.PROP_CODIGO), QueryCustom.QueryCustomParameter.IGUAL, codigoClone))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(RoteiroViagemPassageiro.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME), BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();
        if (lst != null) {
            roteiroViagemPassageiroList = new ArrayList<RoteiroViagemPassageiro>();
            for (RoteiroViagemPassageiro rvp : lst) {
                rvp = VOUtils.cloneObject(rvp);
                rvp.setCodigo(null);
                rvp.setDataCadastro(DataUtil.getDataAtual());
                rvp.setUsuario(SessaoAplicacaoImp.getInstance().getUsuario());
                roteiroViagemPassageiroList.add(rvp);
            }
        }
        return roteiroViagemPassageiroList;
    }

}
