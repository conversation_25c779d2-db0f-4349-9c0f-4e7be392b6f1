/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.prontuario.basico.requisicaopadraoexame;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.RequisicaoPadraoExame;

/**
 *
 * <AUTHOR>
 */
public class SaveRequisicaoPadraoExame extends SaveVO<RequisicaoPadraoExame> {

    public SaveRequisicaoPadraoExame(RequisicaoPadraoExame vo) {
        super(vo);
    }
    
    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(Data.getDataAtual());
        }
        
        this.vo.setDataUsuario(Data.getDataAtual());
        this.vo.setUsuario(SessaoAplicacaoImp.getInstance().<Usuario>getUsuario());
        
    }
    
}
