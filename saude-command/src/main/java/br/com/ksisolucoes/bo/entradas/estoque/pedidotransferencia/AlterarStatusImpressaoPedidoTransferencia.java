/*
 * AlterarStatusImpressaoPedidoTransferencia.java
 *
 * Created on 24 de Agosto de 2006, 15:28
 *
 * To change this template, choose Tools | Template Manager
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.entradas.estoque.pedidotransferencia;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferencia;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AlterarStatusImpressaoPedidoTransferencia extends AbstractCommandTransaction {
    
//    private List<RelatorioPedidoTransferenciaDTO> listPedidoTransferencia;
    
    /** Creates a new instance of AlterarStatusImpressaoPedidoTransferencia */
    public AlterarStatusImpressaoPedidoTransferencia(List listPedidoTransferencia) {
//        this.listPedidoTransferencia = listPedidoTransferencia;
    }
    
    public void execute() throws DAOException, ValidacaoException {
        alterarStatusImpressaoPedidoTransferencia();
    }
    
    private void alterarStatusImpressaoPedidoTransferencia() throws DAOException, ValidacaoException {
        
//        for(RelatorioPedidoTransferenciaDTO pedidoTransferencia : listPedidoTransferencia){
//
//            new UpdateFragmentPedidoTransferencia( PedidoTransferencia.PROP_STATUS_IMPRESSAO,
//                                                   PedidoTransferencia.STATUS_IMPRESSAO_IMPRESSO,
//                                                   pedidoTransferencia.getCodigoEmpresa(),
//                                                   pedidoTransferencia.getNumeroPedidoTransferencia()).start();
//        }
    }
    
}
