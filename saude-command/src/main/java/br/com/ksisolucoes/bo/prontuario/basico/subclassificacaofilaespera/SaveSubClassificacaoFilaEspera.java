package br.com.ksisolucoes.bo.prontuario.basico.subclassificacaofilaespera;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.SubClassificacaoFilaEspera;

/**
 * Created by sulivan on 05/07/19.
 */
public class SaveSubClassificacaoFilaEspera extends SaveVO<SubClassificacaoFilaEspera> {

    public SaveSubClassificacaoFilaEspera(SubClassificacaoFilaEspera vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getUsuario() == null) {
            this.vo.setUsuario(getSessao().<Usuario>getUsuario());
        }
        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(DataUtil.getDataAtual());
        }
    }
}