/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.agendamento.tfd.pedidotfdagendamento;

import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfdAgendamento;

/**
 *
 * <AUTHOR>
 */
public class CancelarPedidoTfdAgendamento extends AbstractCommandTransaction<CancelarPedidoTfdAgendamento> {

    private Long codigoPedidoTfdAgendamento;
    private String motivo;

    public CancelarPedidoTfdAgendamento(Long codigoPedidoTfdAgendamento, String motivo) {
        this.codigoPedidoTfdAgendamento = codigoPedidoTfdAgendamento;
        this.motivo = motivo;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        
        PedidoTfdAgendamento pedidoTfdAgendamento = (PedidoTfdAgendamento) this.getSession().get(PedidoTfdAgendamento.class, this.codigoPedidoTfdAgendamento);
        
        pedidoTfdAgendamento.setStatus(PedidoTfdAgendamento.STATUS_CANCELADO);
        pedidoTfdAgendamento.setObservacao(motivo);
        
        BOFactory.getBO(CadastroFacade.class).save(pedidoTfdAgendamento);
        
    }
    
}
