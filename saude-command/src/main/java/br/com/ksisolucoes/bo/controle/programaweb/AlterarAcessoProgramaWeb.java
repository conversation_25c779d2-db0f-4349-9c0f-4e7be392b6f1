package br.com.ksisolucoes.bo.controle.programaweb;

import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;

/**
 *
 * <AUTHOR>
 */
public class AlterarAcessoProgramaWeb extends AbstractCommandTransaction {
    
    private ProgramaWeb programaWeb;
    private boolean ativar;

    public AlterarAcessoProgramaWeb(ProgramaWeb programaWeb, boolean ativar) {
        this.programaWeb = programaWeb;
        this.ativar = ativar;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        this.programaWeb = (ProgramaWeb) getSession().get(ProgramaWeb.class, this.programaWeb.getCodigo());

        this.programaWeb.setAtivo(ativar? RepositoryComponentDefault.SIM : RepositoryComponentDefault.NAO);
        
        BOFactory.getBO(CadastroFacade.class).save(this.programaWeb);
    }

}
