/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.agendamento.exame;

import br.com.ksisolucoes.agendamento.exame.dto.ExameCadastroAprovacaoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.ExameProcedimentoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.QueryConsultaExamePrestadorCompetenciaDTOParam;
import br.com.ksisolucoes.agendamento.exame.dto.ValidarCotaUnidadeDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import org.hibernate.Criteria;
import org.hibernate.criterion.Property;
import org.hibernate.criterion.Restrictions;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class LoadExamesParaAutorizacao extends AbstractCommandTransaction<LoadExamesParaAutorizacao> {

    private Long codigoExame;
    private ExameCadastroAprovacaoDTO dto;

    public LoadExamesParaAutorizacao(Long codigoExame) {
        this.codigoExame = codigoExame;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        int diaInicioCompetencia = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).<Long>getParametro("diaInicioCompetencia").intValue();
        Date competenciaAtual = Data.competenciaData(diaInicioCompetencia, Data.getDataAtual());

        Exame exame = (Exame) this.getSession().get(Exame.class, this.codigoExame);

        Criteria c = this.getSession().createCriteria(ExameRequisicao.class).add(Restrictions.eq(ExameRequisicao.PROP_EXAME, exame));

        Criteria criteriaProcedimento = c.createCriteria(ExameRequisicao.PROP_EXAME_PROCEDIMENTO);

        criteriaProcedimento.createCriteria(ExameProcedimento.PROP_PROCEDIMENTO, Criteria.LEFT_JOIN);
        criteriaProcedimento.createCriteria(ExameProcedimento.PROP_TIPO_EXAME, Criteria.LEFT_JOIN);
        criteriaProcedimento.createCriteria(ExameProcedimento.PROP_PPI_GRUPO, Criteria.LEFT_JOIN);

        List<ExameRequisicao> exameRequisicaos = c.list();

        List<ExameProcedimentoDTO> exameProcedimentoDTOs = new ArrayList(exameRequisicaos.size());
        for (ExameRequisicao exameRequisicao : exameRequisicaos) {
            ExameProcedimentoDTO dto_ = new ExameProcedimentoDTO();
            dto_.setExameProcedimento(exameRequisicao.getExameProcedimento());
            dto_.setQuantidade(exameRequisicao.getQuantidade());
            dto_.setComplemento(exameRequisicao.getComplemento());
            dto_.setValor(exameRequisicao.getValorProcedimento());
            dto_.setValidarCotaExameUrgente(RepositoryComponentDefault.SIM.equals(exame.getFlagUrgente()) && Exame.STATUS_SOLICITADO.equals(exame.getStatus()));

            if (exame.getAtendimento() != null) {
                Criteria c_ = getSession().createCriteria(ExameRequisicao.class)
                        .add(Restrictions.ne(ExameRequisicao.PROP_CODIGO, exameRequisicao.getCodigo()))
                        .add(Restrictions.eq(ExameRequisicao.PROP_EXAME_PROCEDIMENTO, exameRequisicao.getExameProcedimento()))
                        .createAlias(ExameRequisicao.PROP_EXAME, ExameRequisicao.PROP_EXAME)
                        .add(Restrictions.eq(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_USUARIO_CADSUS), exame.getAtendimento().getUsuarioCadsus()))
                        .add(Restrictions.ne(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_STATUS), Exame.STATUS_CANCELADO))
                        .setProjection(Property.forName(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_DATA_CADASTRO)).max());


                Date dataUltimoExame = (Date) c_.uniqueResult();
                dto_.setDataUltimaExame(dataUltimoExame);
            }

            exameProcedimentoDTOs.add(dto_);
        }

        QueryConsultaExamePrestadorCompetenciaDTOParam queryParam = new QueryConsultaExamePrestadorCompetenciaDTOParam();
        List<Long> codigos = new ArrayList(exameProcedimentoDTOs.size());
        Double valorTotal = 0D;
        for (ExameProcedimentoDTO exameProcedimentoDTO : exameProcedimentoDTOs) {
            codigos.add(exameProcedimentoDTO.getExameProcedimento().getCodigo());
            valorTotal += exameProcedimentoDTO.getValorTotal();
        }
        queryParam.setCodigoExameProcedimentoList(codigos);
        queryParam.setDataCompetencia(competenciaAtual);
        queryParam.setSaldoMinimo(valorTotal);
        QueryConsultaExamePrestadorCompetencia query = new QueryConsultaExamePrestadorCompetencia(queryParam);
        query.start();
        Collection<ExamePrestadorCompetencia> prestadores = query.getResult();

        ExameCadastroAprovacaoDTO dto_ = new ExameCadastroAprovacaoDTO();

        dto_.setAtendimento(exame.getAtendimento());

        dto_.setCodigoUnidade(exame.getEmpresaSolicitante().getCodigo());

        if (exame.getProfissional() != null) {
            dto_.setCodigoProfissional(exame.getProfissional().getCodigo());
        }
        dto_.setNomeProfissional(exame.getNomeProfissional());
            
        if (exame.getTabelaCbo()!= null) {
            dto_.setCodigoCbo(exame.getTabelaCbo().getCbo());
        }

        if (exame.getUsuarioCadsus() != null) {
            dto_.setCodigoPaciente(exame.getUsuarioCadsus().getCodigo());
        }

        dto_.setNomePaciente(exame.getNomePaciente());

        dto_.setExameProcedimentoDTOs(exameProcedimentoDTOs);

        dto_.setUrgente(exame.getFlagUrgente());
        dto_.setMotivo(exame.getDescricaoDadoClinico());

        if (exame.getLocalExame() != null) {
            dto_.setCodigoLocalExame(exame.getLocalExame().getCodigo());
        }

        dto_.setPrestadores(prestadores);

        Long codigoPrestador = null;
        if (dto_.getPrestador() != null) {
            codigoPrestador = dto_.getPrestador().getCodigo();
        } else if (dto_.getCodigoExamePrestadorCompetencia() != null) {
            ExamePrestadorCompetencia examePrestadorCompetencia = LoadManager.getInstance(ExamePrestadorCompetencia.class)
                    .addProperty(ExamePrestadorCompetencia.PROP_CODIGO)
                    .addProperties(new HQLProperties(Empresa.class, ExamePrestadorCompetencia.PROP_EMPRESA).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExamePrestadorCompetencia.PROP_CODIGO), dto_.getCodigoExamePrestadorCompetencia()))
                    .start().getVO();
            if (examePrestadorCompetencia != null) {
                codigoPrestador = examePrestadorCompetencia.getEmpresa().getCodigo();
            }
        }

        pesquisarCotas(dto_, competenciaAtual, exame.getEmpresaSolicitante().getCodigo(), exame.getTipoExame().getCodigo(), dto_.getCodigoProfissional(), dto_.getCodigoCbo(), codigoPrestador);

        this.dto = dto_;
    }

    private void pesquisarCotas(ExameCadastroAprovacaoDTO dto_, Date competenciaAtual, Long codigoUndiade, Long codigoTipoExame, Long codigoProfissional, String codigoCbo, Long codigoPrestador) throws ValidacaoException, DAOException {
        ValidarCotaUnidadeDTO validarCotaUnidadeDTO = BOFactory.getBO(ExameFacade.class).validarCotaUnidade(competenciaAtual, codigoUndiade, codigoTipoExame, codigoProfissional, codigoCbo, codigoPrestador);

        Dinheiro cotaUnidade = new Dinheiro(validarCotaUnidadeDTO.getCotaUnidade());
        Dinheiro cotaUtilizada = new Dinheiro(validarCotaUnidadeDTO.getCotaUtilizada());


        if (validarCotaUnidadeDTO.isValidarCota()) {
            if (cotaUnidade.subtrair(cotaUtilizada).compareTo(Dinheiro.ZERO) <= 0) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_unidade_nao_possui_cota_para_autorizacao_exame"));
            }
            
            if(validarCotaUnidadeDTO.getCotaProfissional() != null){
                Dinheiro cotaProfissional = new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaProfissional()));
                Dinheiro cotaProfissionalUtilizada = new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaProfissionalUtilizada()));
                if(cotaProfissional.subtrair(cotaProfissionalUtilizada).doubleValue() <= 0D ){
                    throw new ValidacaoException(Bundle.getStringApplication("msg_profissional_nao_possui_cota_para_autorizacao_exame"));
                }
            }else if(validarCotaUnidadeDTO.getCotaCbo()!= null){
                Dinheiro cotaCbo = new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaCbo()));
                Dinheiro cotaCboUtilizada = new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaCboUtilizada()));
                if(cotaCbo.subtrair(cotaCboUtilizada).doubleValue() <= 0D ){
                    throw new ValidacaoException(Bundle.getStringApplication("msg_saldo_cbo_atingido_apenas_exames_urgentes_podem_ser_adicionados"));
                }
            }
        }

        dto_.setCotaUnidade(cotaUnidade.doubleValue());
        dto_.setCotaUtilizada(cotaUtilizada.doubleValue());
        dto_.setCotaProfissional(validarCotaUnidadeDTO.getCotaProfissional());
        dto_.setCotaProfissionalUtilizada(validarCotaUnidadeDTO.getCotaProfissionalUtilizada());
        dto_.setCotaCbo(validarCotaUnidadeDTO.getCotaCbo());
        dto_.setCotaCboUtilizada(validarCotaUnidadeDTO.getCotaCboUtilizada());
        
    }

    public ExameCadastroAprovacaoDTO getResult() {
        return this.dto;
    }
}
