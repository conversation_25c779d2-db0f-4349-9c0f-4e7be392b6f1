package br.com.ksisolucoes.bo.prontuario.basico.atendimentorecemnascido;

import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoRecemNascido;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class SaveAtendimentoRecemNascido extends SaveVO<AtendimentoRecemNascido> {

    public SaveAtendimentoRecemNascido(AtendimentoRecemNascido vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        this.vo.setColirio(StringUtils.trimToNull(StringUtil.getDigits(this.vo.getColirio())));
        this.vo.setVitaminaK(StringUtils.trimToNull(StringUtil.getDigits(this.vo.getVitaminaK())));
        this.vo.setOxigenioInicio(StringUtils.trimToNull(StringUtil.getDigits(this.vo.getOxigenioInicio())));
        this.vo.setOxigenioTermino(StringUtils.trimToNull(StringUtil.getDigits(this.vo.getOxigenioTermino())));
    }

}
