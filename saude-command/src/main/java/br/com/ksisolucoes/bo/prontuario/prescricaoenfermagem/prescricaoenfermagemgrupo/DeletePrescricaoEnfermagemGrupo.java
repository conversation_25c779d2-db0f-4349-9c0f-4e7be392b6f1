package br.com.ksisolucoes.bo.prontuario.prescricaoenfermagem.prescricaoenfermagemgrupo;

import br.com.ksisolucoes.bo.command.DeleteVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.prescricaoenfermagem.PrescricaoEnfermagem;
import br.com.ksisolucoes.vo.prontuario.prescricaoenfermagem.PrescricaoEnfermagemGrupo;
import java.util.List;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class DeletePrescricaoEnfermagemGrupo extends DeleteVO<PrescricaoEnfermagemGrupo> {

    public DeletePrescricaoEnfermagemGrupo(PrescricaoEnfermagemGrupo vo) {
        super(vo);
    }

    @Override
    protected void antesDelete() throws DAOException, ValidacaoException {
        List<PrescricaoEnfermagem> list = getSession().createCriteria(PrescricaoEnfermagem.class)
                .add(Restrictions.eq(PrescricaoEnfermagem.PROP_PRESCRICAO_ENFERMAGEM_GRUPO, vo)).list();

        for (PrescricaoEnfermagem prescricaoEnfermagem : list) {
            BOFactory.delete(prescricaoEnfermagem);
        }
    }
}
