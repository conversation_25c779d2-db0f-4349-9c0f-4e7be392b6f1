package br.com.ksisolucoes.bo.entradas.recebimento.registronotafiscal;

import br.com.ksisolucoes.bo.entradas.recebimento.interfaces.facade.RegistroNotaFiscalFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroNotaFiscal;
import java.util.LinkedHashSet;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CadastrarNotaFiscal extends AbstractCommandTransaction {

    private RegistroNotaFiscal registroNotaFiscal;
    private List<RegistroItemNotaFiscal> itens;

    public CadastrarNotaFiscal(RegistroNotaFiscal registroNotaFiscal, List<RegistroItemNotaFiscal> itens) {
        this.registroNotaFiscal = registroNotaFiscal;
        this.itens = itens;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        registroNotaFiscal.setItemNotaFiscalSet(new LinkedHashSet<RegistroItemNotaFiscal>(itens));
        
        registroNotaFiscal.setEmpresa(getSessao().<Empresa>getEmpresa());
        
        registroNotaFiscal.setValorIpi(0D);
        registroNotaFiscal.setValorMercadoria(0D);
        
        this.registroNotaFiscal = BOFactory.getBO(RegistroNotaFiscalFacade.class).save(registroNotaFiscal);
    }

    public RegistroNotaFiscal getRegistroNotaFiscal() {
        return registroNotaFiscal;
    }

}
