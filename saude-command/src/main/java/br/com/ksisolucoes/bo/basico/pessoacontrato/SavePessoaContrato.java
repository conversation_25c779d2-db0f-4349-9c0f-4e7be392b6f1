package br.com.ksisolucoes.bo.basico.pessoacontrato;
 import br.com.celk.util.DataUtil;
 import br.com.ksisolucoes.vo.basico.PessoaContrato;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;

/**
 *
 * <AUTHOR>
 */
public class SavePessoaContrato extends SaveVO<PessoaContrato> {

    public SavePessoaContrato(PessoaContrato vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(DataUtil.getDataAtual());
        }
        if (this.vo.getUsuario() == null) {
            this.vo.setUsuario(getSessao().<Usuario>getUsuario());
        }
    }

}
