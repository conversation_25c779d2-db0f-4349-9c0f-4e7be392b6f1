package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentoprojetohidrossanitario;

import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoProjetoHidrossanitarioDeclaratorioDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaSolicitacaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.EloRequerimentoVigilanciaSetorVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioDeclaratorio;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.util.ArrayList;

/**
 * Created by sulivan on 29/12/18.
 */
public class SalvarRequerimentoProjetoHidrossanitarioDeclaratorio extends AbstractCommandTransaction<SalvarRequerimentoProjetoHidrossanitarioDeclaratorio> {

    private final RequerimentoProjetoHidrossanitarioDeclaratorioDTO dto;
    private RequerimentoVigilancia requerimentoVigilancia;
    private ConfiguracaoVigilancia configuracaoVigilancia;
    private RequerimentoProjetoHidrossanitarioDeclaratorio requerimentoProjetoHidrossanitarioDeclaratorio;

    public SalvarRequerimentoProjetoHidrossanitarioDeclaratorio(RequerimentoProjetoHidrossanitarioDeclaratorioDTO dto) {
        this.dto = dto;
        this.requerimentoProjetoHidrossanitarioDeclaratorio = dto.getRequerimentoProjetoHidrossanitarioDeclaratorio();
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (CollectionUtils.isEmpty(dto.getTipoProjetoRequerimentoVigilanciaList())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_adicione_pelo_menos_um_tipo_projeto"));
        }
        if (Coalesce.asDouble(requerimentoProjetoHidrossanitarioDeclaratorio.getAreaTotalConstrucao()) <= 0D) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_area_total_construcao"));
        }

        boolean isNew = false;
        if (requerimentoProjetoHidrossanitarioDeclaratorio.getRequerimentoVigilancia().getCodigo() == null) {
            isNew = true;
            requerimentoProjetoHidrossanitarioDeclaratorio.getRequerimentoVigilancia().setSituacao(RequerimentoVigilancia.Situacao.PENDENTE.value());
            requerimentoProjetoHidrossanitarioDeclaratorio.getRequerimentoVigilancia().setTipoDocumento(TipoSolicitacao.TipoDocumento.ANALISE_PROJETO_HIDROSSANITARIO_DECLARATORIO.value());
        }
        requerimentoVigilancia = BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilancia(new RequerimentoVigilanciaSolicitacaoDTO(requerimentoProjetoHidrossanitarioDeclaratorio.getRequerimentoVigilancia()));

        requerimentoVigilancia = VigilanciaHelper.atualizarGestaoRequerimento(requerimentoVigilancia, dto.getRequerimentoVigilanciaFiscalList(), dto.getEloRequerimentoVigilanciaSetorVigilanciaList(), isNew);

        BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(requerimentoProjetoHidrossanitarioDeclaratorio.getCodigo() != null ? Bundle.getStringApplication("msg_requerimento_editado") : Bundle.getStringApplication("msg_requerimento_cadastrado"), requerimentoVigilancia, null);

        requerimentoProjetoHidrossanitarioDeclaratorio.setRequerimentoVigilancia(requerimentoVigilancia);

        if (requerimentoProjetoHidrossanitarioDeclaratorio != null && requerimentoProjetoHidrossanitarioDeclaratorio.getNumeroProjetoAprovado() != null) {
            requerimentoProjetoHidrossanitarioDeclaratorio.setNumeroProjetoAprovado(StringUtil.getDigits(requerimentoProjetoHidrossanitarioDeclaratorio.getNumeroProjetoAprovado()));
        }

        ajustarSetorResponsavelRequerimentoExterno();

        BOFactory.save(requerimentoProjetoHidrossanitarioDeclaratorio);

        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaAnexo(requerimentoVigilancia, dto.getRequerimentoVigilanciaAnexoDTOList(), dto.getRequerimentoVigilanciaAnexoExcluidoDTOList(), false);
        BOFactory.getBO(VigilanciaFacade.class).salvarEloRequerimentoVigilanciaSetorVigilancia(requerimentoVigilancia, dto.getEloRequerimentoVigilanciaSetorVigilanciaList(), dto.getEloRequerimentoVigilanciaSetorVigilanciaExcluirList());
        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaFiscais(requerimentoVigilancia, dto.getRequerimentoVigilanciaFiscalList(), dto.getRequerimentoVigilanciaFiscalListExcluir());
        BOFactory.getBO(VigilanciaFacade.class).salvarTipoProjetoRequerimentoVigilancia(requerimentoVigilancia, dto.getTipoProjetoRequerimentoVigilanciaList(), dto.getTipoProjetoRequerimentoVigilanciaExcluirList());
        BOFactory.getBO(VigilanciaFacade.class).salvarEloRequerimentoVigilanciaResponsavelTecnico(requerimentoVigilancia, dto.getEloRequerimentoVigilanciaResponsavelTecnicoList(), dto.getEloRequerimentoVigilanciaResponsavelTecnicoExcluirList());
        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaInscricaoImobiliaria(requerimentoVigilancia, dto.getRequerimentoVigilanciaInscricaoImobList(), dto.getRequerimentoVigilanciaInscricaoImobExcluirList());
        BOFactory.getBO(VigilanciaFacade.class).salvarAnexosPranchaProjetoHidroDeclaratorio(requerimentoProjetoHidrossanitarioDeclaratorio, dto.getAnexosPranchaList(), dto.getAnexosPranchaExcluirList());

        if (isNew) {
            BOFactory.getBO(VigilanciaFacade.class).enviarEmailNovoRequerimentoVigilancia(this.requerimentoVigilancia);
        }
    }

    private void ajustarSetorResponsavelRequerimentoExterno() throws ValidacaoException {
        if (requerimentoProjetoHidrossanitarioDeclaratorio.getCodigo() == null && RequerimentoVigilancia.Origem.EXTERNO.value().equals(requerimentoVigilancia.getOrigem())) {
            configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
            if (configuracaoVigilancia == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existe_configuracao"));
            } else if (configuracaoVigilancia.getSetorVigilanciaAnaliseProjetos() != null) {
                EloRequerimentoVigilanciaSetorVigilancia elo = new EloRequerimentoVigilanciaSetorVigilancia();
                elo.setSetorVigilancia(configuracaoVigilancia.getSetorVigilanciaAnaliseProjetos());
                if (br.com.ksisolucoes.util.CollectionUtils.isEmpty(dto.getEloRequerimentoVigilanciaSetorVigilanciaList())) {
                    dto.setEloRequerimentoVigilanciaSetorVigilanciaList(new ArrayList<EloRequerimentoVigilanciaSetorVigilancia>());
                }
                dto.getEloRequerimentoVigilanciaSetorVigilanciaList().add(elo);
            }
        }
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }
}