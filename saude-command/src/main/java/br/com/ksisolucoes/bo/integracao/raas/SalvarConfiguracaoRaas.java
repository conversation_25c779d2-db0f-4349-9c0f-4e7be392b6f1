package br.com.ksisolucoes.bo.integracao.raas;

import br.com.ksisolucoes.bo.siab.raas.ConfiguracaoRaasDto;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atendimento.raas.RaasUnidade;

/**
 *
 * <AUTHOR>
 */
public class SalvarConfiguracaoRaas extends AbstractCommandTransaction {

    private ConfiguracaoRaasDto dto;

    public SalvarConfiguracaoRaas(ConfiguracaoRaasDto dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        dto.getRaasConfiguracao().setCodigo(1L);
        BOFactory.save(dto.getRaasConfiguracao());
        VOUtils.persistirListaVosModificados(RaasUnidade.class, dto.getListaRaasUnidade(), null);
    }

}
