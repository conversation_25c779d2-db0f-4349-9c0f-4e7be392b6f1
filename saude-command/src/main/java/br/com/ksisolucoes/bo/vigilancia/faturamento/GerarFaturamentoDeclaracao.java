package br.com.ksisolucoes.bo.vigilancia.faturamento;

import br.com.celk.vigilancia.helper.FaturamentoVigilanciaHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.LancamentoAtividadesVigilanciaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia;
import br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilancia;
import br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilanciaItem;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaAtividades;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class GerarFaturamentoDeclaracao extends AbstractCommandTransaction {
    private LancamentoAtividadesVigilanciaDTO lancamentoAtividadesVigilanciaDTO;
    private RequerimentoVigilancia requerimentoVigilancia;
    private Date dataFaturamento;
    private boolean declaracaoCartorio;

    public GerarFaturamentoDeclaracao(RequerimentoVigilancia requerimentoVigilancia, boolean declaracaoCartorio) {
        if(requerimentoVigilancia != null && requerimentoVigilancia.getDataFinalizacao() != null) {
            this.dataFaturamento = requerimentoVigilancia.getDataFinalizacao();
        } else if(requerimentoVigilancia != null && requerimentoVigilancia.getDataRequerimento() != null){
            this.dataFaturamento = requerimentoVigilancia.getDataRequerimento();
        } else {
            try {
                throw new ValidacaoException("Impossível gerar o Lançamento da Atividade pois não foi informado a Data da Atividade");
            } catch (ValidacaoException e) {
                 br.com.ksisolucoes.util.log.Loggable.log.error(e);
            }
        }
        this.requerimentoVigilancia= requerimentoVigilancia;
        this.declaracaoCartorio = declaracaoCartorio;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if(declaracaoCartorio) {
            FaturamentoVigilanciaHelper.validarConfiguracaoVigilanciaAtividades(ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.DECLARACAO_CARTORIO_DEFERIMENTO);
        } else { //DECLARAÇÃO VISA
            FaturamentoVigilanciaHelper.validarConfiguracaoVigilanciaAtividades(ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.DECLARACAO_VISA_DEFERIMENTO);
        }

        lancamentoAtividadesVigilanciaDTO = new LancamentoAtividadesVigilanciaDTO();

        LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia = gerarLancamentoAtividadeVigilancia();

        List<LancamentoAtividadesVigilanciaItem> lancamentoAtividadesVigilanciaItemList = new ArrayList();

        gerarAtividadePadrao(lancamentoAtividadesVigilancia, lancamentoAtividadesVigilanciaItemList);

        lancamentoAtividadesVigilanciaDTO.setFaturavel(false); //não gera item de conta (BPA)
        lancamentoAtividadesVigilanciaDTO.setLancamentoAtividadesVigilancia(lancamentoAtividadesVigilancia);
        lancamentoAtividadesVigilanciaDTO.setLancamentoAtividadesVigilanciaItemList(lancamentoAtividadesVigilanciaItemList);
        BOFactory.getBO(VigilanciaFacade.class).salvarLancamentoAtividadesVigilancia(lancamentoAtividadesVigilanciaDTO);
    }

    private void gerarAtividadePadrao(LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia, List<LancamentoAtividadesVigilanciaItem> lancamentoAtividadesVigilanciaItemList) throws DAOException {

        LancamentoAtividadesVigilanciaItem lancamentoAtividadesVigilanciaItem = new LancamentoAtividadesVigilanciaItem();
        AtividadesVigilancia atividadesVigilanciaDefault;
        if (declaracaoCartorio) {
            atividadesVigilanciaDefault = FaturamentoVigilanciaHelper.getAtividadeVigilanciaDefault(ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.DECLARACAO_CARTORIO_DEFERIMENTO);
        } else { //DECLARAÇÃO VISA
            atividadesVigilanciaDefault = FaturamentoVigilanciaHelper.getAtividadeVigilanciaDefault(ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.DECLARACAO_VISA_DEFERIMENTO);
        }
        lancamentoAtividadesVigilanciaItem.setAtividadesVigilancia(atividadesVigilanciaDefault);
        lancamentoAtividadesVigilanciaItem.setPontuacao(atividadesVigilanciaDefault.getPontuacao());
        lancamentoAtividadesVigilanciaItem.setLancamentoAtividadesVigilancia(lancamentoAtividadesVigilancia);
        lancamentoAtividadesVigilanciaItem.setQuantidade(1L);
        lancamentoAtividadesVigilanciaItemList.add(lancamentoAtividadesVigilanciaItem);
    }

    private LancamentoAtividadesVigilancia gerarLancamentoAtividadeVigilancia() {
        LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia = new LancamentoAtividadesVigilancia();
        lancamentoAtividadesVigilancia.setProfissional(SessaoAplicacaoImp.getInstance().getUsuario().getProfissional());
        lancamentoAtividadesVigilancia.setDataAtividade(dataFaturamento);
        lancamentoAtividadesVigilancia.setRequerimentoVigilancia(requerimentoVigilancia);
        if(declaracaoCartorio){
            lancamentoAtividadesVigilancia.setTipoAtividade(ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.DECLARACAO_CARTORIO_DEFERIMENTO.descricao());
        }else {
            lancamentoAtividadesVigilancia.setTipoAtividade(ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.DECLARACAO_VISA_DEFERIMENTO.descricao());
        }
        lancamentoAtividadesVigilancia.setEstabelecimento(requerimentoVigilancia.getEstabelecimento());
        lancamentoAtividadesVigilancia.setCnpjCpfPessoa(requerimentoVigilancia.getEstabelecimento() != null ? requerimentoVigilancia.getEstabelecimento().getCnpjCpf() : null);
        lancamentoAtividadesVigilancia.setAtividadeEstabelecimento(FaturamentoVigilanciaHelper.getAtividadePrincipalEstabelecimento(requerimentoVigilancia.getEstabelecimento()));
        lancamentoAtividadesVigilancia.setFlagTipo(LancamentoAtividadesVigilancia.TipoPessoa.ESTABELECIMENTO.value());

        return lancamentoAtividadesVigilancia;
    }
}
