package br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.validator;

import br.com.ksisolucoes.bo.prontuario.basico.atendimento.GerarAtendimentoRecemNascido;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.GerarProximoAtendimento;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.INodeValidator;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.IValidator;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.CadastroRecemNascidoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.CadastroRecemNascidoItemDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.GerarAtendimentoRecemNascidoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.GerarProximoAtendimentoDTO;
import br.com.ksisolucoes.bo.prontuario.web.helper.ProntuarioConsultaHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoRecemNascido;
import br.com.ksisolucoes.vo.prontuario.basico.ObstetriciaExameGeral;
import br.com.ksisolucoes.vo.prontuario.encaminhamento.EloNaturezaTipoEncaminhamento;
import br.com.ksisolucoes.vo.prontuario.encaminhamento.EncaminhamentoTipo;
import java.util.List;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class ValidatorCadastroRecemNascido implements IValidator<CadastroRecemNascidoDTO> {

    private INodeValidator nodeValidator;

    public ValidatorCadastroRecemNascido(INodeValidator nodeValidator) {
        this.nodeValidator = nodeValidator;
    }

    @Override
    public CadastroRecemNascidoDTO executarValidacao(CadastroRecemNascidoDTO object) throws DAOException, ValidacaoException {
        return object;
    }

    @Override
    public void processar(CadastroRecemNascidoDTO object) throws DAOException, ValidacaoException {
        if (object != null) {
            Session session = HibernateSessionFactory.getSession();

            for (CadastroRecemNascidoItemDTO dto : object.getCadastroRecemNascidoItemDTOdtos()) {
                Long countAtendimentoRN = (Long) session.createCriteria(Atendimento.class)
                        .setProjection(Projections.rowCount())
                        .add(Restrictions.eq(VOUtils.montarPath(Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), dto.getUsuarioCadsus().getCodigo()))
                        .add(Restrictions.eq(Atendimento.PROP_STATUS, Atendimento.STATUS_FINALIZADO))
                        .uniqueResult();

                if (countAtendimentoRN == 0) {
                    GerarAtendimentoRecemNascidoDTO dtoRecemNascido = new GerarAtendimentoRecemNascidoDTO();
                    dtoRecemNascido.setRecemNascido(dto.getUsuarioCadsus());
                    dtoRecemNascido.setAtendimentoOrigem(nodeValidator.getAtendimento());
                    dtoRecemNascido.setPediatra(object.getAtendimentoRecemNascido().getPediatra());

                    new GerarAtendimentoRecemNascido(dtoRecemNascido, Atendimento.STATUS_FINALIZADO).start();
                }
            }

            String conclusao = (String) session.createCriteria(ObstetriciaExameGeral.class)
                    .setProjection(Projections.property(ObstetriciaExameGeral.PROP_CONCLUSAO))
                    .createCriteria(ObstetriciaExameGeral.PROP_ATENDIMENTO)
                    .add(Restrictions.eq(Atendimento.PROP_ATENDIMENTO_PRINCIPAL, nodeValidator.getAtendimento().getAtendimentoPrincipal()))
                    .uniqueResult();

            if (StringUtils.trimToNull(conclusao) != null && !new ProntuarioConsultaHelper().possuiProximoAtendimento(nodeValidator.getAtendimento())) {

                List<UsuarioCadsus> lstUsuarioCadsus = session.createCriteria(UsuarioCadsus.class)
                        .add(Restrictions.eq(UsuarioCadsus.PROP_ATENDIMENTO_ORIGEM, nodeValidator.getAtendimento().getAtendimentoPrincipal()))
                        .add(Restrictions.not(Restrictions.eq(UsuarioCadsus.PROP_CODIGO, nodeValidator.getAtendimento().getUsuarioCadsus().getCodigo())))
                        .add(Restrictions.eq(UsuarioCadsus.PROP_RECEM_NASCIDO, RepositoryComponentDefault.SIM))
                        .list();

                Long count = (Long) session.createCriteria(AtendimentoRecemNascido.class)
                        .setProjection(Projections.count(AtendimentoRecemNascido.PROP_CODIGO))
                        .createCriteria(AtendimentoRecemNascido.PROP_ATENDIMENTO)
                        .add(Restrictions.in(Atendimento.PROP_USUARIO_CADSUS, lstUsuarioCadsus))
                        .add(Restrictions.eq(Atendimento.PROP_ATENDIMENTO_PRINCIPAL, nodeValidator.getAtendimento().getAtendimentoPrincipal()))
                        .uniqueResult();


                if (CollectionUtils.isNotNullEmpty(lstUsuarioCadsus) && lstUsuarioCadsus.size() == count) {
                    Criteria criteriaElo = session.createCriteria(EloNaturezaTipoEncaminhamento.class)
                            .add(Restrictions.eq(EloNaturezaTipoEncaminhamento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO, nodeValidator.getAtendimento().getNaturezaProcuraTipoAtendimento()));
                    criteriaElo.createCriteria(EloNaturezaTipoEncaminhamento.PROP_ENCAMINHAMENTO_TIPO)
                            .add(Restrictions.eq(EncaminhamentoTipo.PROP_TIPO, EncaminhamentoTipo.Tipo.POS_OBSTETRICO.value()));
                    criteriaElo.createCriteria(EloNaturezaTipoEncaminhamento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO);
                    criteriaElo.createCriteria(EloNaturezaTipoEncaminhamento.PROP_TIPO_ATENDIMENTO);

                    EloNaturezaTipoEncaminhamento elo = (EloNaturezaTipoEncaminhamento) criteriaElo.uniqueResult();

                    if (elo == null) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_encaminhamento_pos_obstetrico_nao_foi_definido_para_tipo_atendimento"));
                    }

                    GerarProximoAtendimentoDTO dto = new GerarProximoAtendimentoDTO();
                    dto.setAtendimentoOrigem(nodeValidator.getAtendimento());
                    dto.setNaturezaProcura(elo.getNaturezaProcuraTipoAtendimento().getNaturezaProcura());
                    dto.setTipoAtendimento(elo.getTipoAtendimento());

                    Atendimento atendimentoGerado = (Atendimento) new GerarProximoAtendimento(dto).start().getNovoAtendimento();

                    nodeValidator.getAtendimento().setAtendimentoProximo(atendimentoGerado);
                    BOFactory.save(nodeValidator.getAtendimento());
                }

            }
        }
    }
}