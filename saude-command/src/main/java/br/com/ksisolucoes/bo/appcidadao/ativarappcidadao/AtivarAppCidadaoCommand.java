package br.com.ksisolucoes.bo.appcidadao.ativarappcidadao;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.appcidadao.DTO.AtivacaoPacienteDto;
import br.com.ksisolucoes.bo.appcidadao.DTO.MunicipioUrlDto;
import br.com.ksisolucoes.bo.appcidadao.builder.AtivacaoPacienteBuilder;
import br.com.ksisolucoes.bo.appcidadao.service.RequestService;
import br.com.ksisolucoes.bo.appcidadao.utils.AppCidadaoUtils;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;

import java.util.logging.Logger;

public class AtivarAppCidadaoCommand extends AbstractCommandTransaction<AtivacaoPacienteDto> {

    private final AtivacaoPacienteDto ativacaoPacienteDto;

    public AtivarAppCidadaoCommand(UsuarioCadsus usuarioCadsus) throws ValidacaoException, DAOException {
        if (ValidaAtivarAppCidadao.valida(usuarioCadsus)) {
            usuarioCadsus.setAppCidadaoAtivo(true);
            BOFactory.save(usuarioCadsus);
        }

        this.ativacaoPacienteDto = new AtivacaoPacienteBuilder().builder()
                .setIdPaciente(usuarioCadsus.getCodigo())
                .setNome(usuarioCadsus.getNome())
                .setCpf(usuarioCadsus.getCpf())
                .setCns(usuarioCadsus.getCnsSemPontos())
                .setMunicipio(new MunicipioUrlDto(AppCidadaoUtils.getHost()))
                .build();
    }

    @Override
    public void execute() throws DAOException {
        try {
            final String servidor = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MOBILE).getParametro("URLComunicaçãoAuthCidadão");
            final String URL = servidor + "/paciente";
            new RequestService().post(URL, AppCidadaoUtils.convertDtoToJson(ativacaoPacienteDto));
        } catch (ValidacaoException validacaoException) {
            throw new DAOException(validacaoException.getMessage());
        } catch (Exception e) {
            Logger.getGlobal().throwing("AtivarAppCidadaoCommand", "execute", e);
            throw new DAOException("Ocorreu um erro ao tentar ativar o App Cidadão");
        }
    }
}
