package br.com.ksisolucoes.bo.controle.web;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.web.ProgramaPaginaPermissao;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryPaginaPermissoesWeb extends CommandQuery<QueryPaginaPermissoesWeb> {

    private Map<ProgramaWeb, List<ProgramaPaginaPermissao>> permissoesMap;
    
    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("programaWebPagina.programaWeb");
        hql.addToSelect("programaPaginaPermissao");
        hql.addToSelect("programaPaginaPermissao.programaPagina");
        hql.addToSelect("programaPaginaPermissao.permissaoWeb");
        
        hql.addToFrom("ProgramaPaginaPermissao programaPaginaPermissao, "
                + " EloProgramaWebPagina programaWebPagina"
                + " right join programaPaginaPermissao.permissaoWeb permissaoWeb ");
        
        hql.addToWhereWhithAnd("programaWebPagina.programaPagina = programaPaginaPermissao.programaPagina");

        hql.addToOrder("permissaoWeb.descricao asc");
    }

    @Override
    protected void result(Object result) throws ValidacaoException, DAOException {
        permissoesMap = new HashMap<ProgramaWeb, List<ProgramaPaginaPermissao>>();
        
        List<Object[]> l = (List) result;
        
        for (Object[] objects : l) {
            ProgramaWeb programaWeb = (ProgramaWeb)objects[0];
            ProgramaPaginaPermissao paginaPermissao = (ProgramaPaginaPermissao)objects[1];

            if (!permissoesMap.containsKey(programaWeb)) {
                permissoesMap.put(programaWeb, new ArrayList<ProgramaPaginaPermissao>());
            }
            
            permissoesMap.get(programaWeb).add(paginaPermissao);
        }
        
    }

    public Map<ProgramaWeb, List<ProgramaPaginaPermissao>> getPermissoesMap() {
        return permissoesMap;
    }

}
