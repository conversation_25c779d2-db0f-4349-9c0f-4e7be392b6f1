/*
 * ProcessarReplicacaoProdutoUnidade.java
 *
 * Created on 30 de Agosto de 2006, 14:17
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.entradas.estoque.produtounidade;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.entradas.SuprimentosPadraoInterface;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ProcessarAdicionarRemoverProdutoUnidade extends AbstractCommandTransaction {
    
    private List< Empresa > empresasDestino;
    private List< Produto > produtos;
    
    private Long tipoProcessamento = -1L;
    
    /** Creates a new instance of ProcessarReplicacaoProdutoUnidade */
    public ProcessarAdicionarRemoverProdutoUnidade( List< Empresa > empresasDestino, List< Produto > produtos, Long tipoProcessamento ) {
        this.empresasDestino = empresasDestino;
        this.produtos = produtos;
        this.tipoProcessamento = tipoProcessamento;
    }
    
    public void execute() throws DAOException, ValidacaoException {
        if ( SuprimentosPadraoInterface.PROCESSAR_ADICIONAR.equals( this.tipoProcessamento ) ){
            new ProcessarAdicionarProdutoUnidade( this.empresasDestino, this.produtos ).start();
        } else if ( SuprimentosPadraoInterface.PROCESSAR_REMOVER.equals( this.tipoProcessamento ) ){
            new ProcessarRemoverProdutoUnidade( this.empresasDestino, this.produtos ).start();
        }
    }
}
