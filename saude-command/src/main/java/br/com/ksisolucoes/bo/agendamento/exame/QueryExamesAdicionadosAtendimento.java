package br.com.ksisolucoes.bo.agendamento.exame;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.exame.interfaces.dto.SolicitacaoExameDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryExamesAdicionadosAtendimento extends CommandQuery<QueryExamesAdicionadosAtendimento> {

    private Atendimento atendimento;
    private List<SolicitacaoExameDTO> result;

    public QueryExamesAdicionadosAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        ConsultaExamesAdicionadosAtendimento.initHqlLacen(hql, atendimento);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    public List<SolicitacaoExameDTO> getResult() {
        return result;
    }

}