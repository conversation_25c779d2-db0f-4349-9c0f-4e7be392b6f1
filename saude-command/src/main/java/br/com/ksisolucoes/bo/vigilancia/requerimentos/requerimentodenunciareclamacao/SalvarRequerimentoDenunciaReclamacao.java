package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentodenunciareclamacao;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoDenunciaReclamacaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaSolicitacaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Projections;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.vigilancia.EloRequerimentoVigilanciaSetorVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia;
import br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaAtividades;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaEnum;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SalvarRequerimentoDenunciaReclamacao extends AbstractCommandTransaction<SalvarRequerimentoDenunciaReclamacao> {

    private final RequerimentoDenunciaReclamacaoDTO dto;
    private RequerimentoVigilancia requerimentoVigilancia;
    private boolean isNew = false;

    public SalvarRequerimentoDenunciaReclamacao(RequerimentoDenunciaReclamacaoDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        ConfiguracaoVigilancia configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        Denuncia denuncia = dto.getDenuncia();
        RequerimentoVigilancia requerimentoVigilancia = denuncia.getRequerimentoVigilancia();

        if (ConfiguracaoVigilanciaEnum.TipoGestaoRequerimento.FISCAL.value().equals(configuracaoVigilancia.getFlagTipoGestaoRequerimento()) && !VigilanciaHelper.isRequerimentoAnaliseProjeto(requerimentoVigilancia)) {
            if(CollectionUtils.isNotNullEmpty(dto.getRequerimentoVigilanciaFiscalList())) {
                requerimentoVigilancia.setSituacaoAprovacao(RequerimentoVigilancia.SituacaoAprovacao.APROVADO.value());
            } else {
                requerimentoVigilancia.setSituacaoAprovacao(RequerimentoVigilancia.SituacaoAprovacao.AGUARDANDO_INFORMAR_FISCAL.value());
            }
        } else {
            requerimentoVigilancia.setSituacaoAprovacao(RequerimentoVigilancia.SituacaoAprovacao.APROVADO.value());
        }


        if (requerimentoVigilancia.getCodigo() == null) {
            requerimentoVigilancia.setTipoDocumento(TipoSolicitacao.TipoDocumento.DENUNCIA_RECLAMACAO.value());
            requerimentoVigilancia.setSituacao(RequerimentoVigilancia.Situacao.PENDENTE.value());

            isNew = true;
        }

        requerimentoVigilancia.setVigilanciaEndereco(denuncia.getEnderecoDenunciado());

        if (isAnonimo(denuncia)) {
            requerimentoVigilancia.setRgCpfSolicitante(null);
            requerimentoVigilancia.setCpfSolicitante(null);
            requerimentoVigilancia.setCnpjCpf(null);
            denuncia.setCnpjCpfDenunciante(null);
        } else {
            requerimentoVigilancia.setNomeSolicitante(denuncia.getDenunciante());
            requerimentoVigilancia.setTelefoneSolicitante(denuncia.getTelefoneDenunciante());
            requerimentoVigilancia.setEnderecoSolicitante(denuncia.getLogradouroDenunciante());

            if (Pessoa.PESSOA_FISICA.equals(denuncia.getTipoPessoaDenunciante())) {
                requerimentoVigilancia.setCnpjCpf(denuncia.getCnpjCpfDenunciante());
                requerimentoVigilancia.setCpfSolicitante(denuncia.getCnpjCpfDenunciante());
                requerimentoVigilancia.setTipoPessoa(RequerimentoVigilancia.TipoPessoa.FISICA.value());
            } else {
                requerimentoVigilancia.setCnpjCpf(denuncia.getCnpjCpfDenunciante());
                requerimentoVigilancia.setCnpjSolicitante(denuncia.getCnpjCpfDenunciante());
                requerimentoVigilancia.setTipoPessoa(RequerimentoVigilancia.TipoPessoa.JURIDICA.value());
            }
        }

        if (denuncia.getEstabelecimentoDenunciado() != null) {
            String nome = denuncia.getEstabelecimentoDenunciado().getRazaoSocial();
            if (denuncia.getEstabelecimentoDenunciado().getFantasia() != null) {
                nome += " - " + denuncia.getEstabelecimentoDenunciado().getFantasia();
            }

            if (denuncia.getDenunciado() == null) {
                denuncia.setDenunciado(nome);
            }

            requerimentoVigilancia.setNome(nome);
            requerimentoVigilancia.setEstabelecimento(denuncia.getEstabelecimentoDenunciado());

        } else if (denuncia.getVigilanciaPessoaDenunciada() != null) {
            String nome = denuncia.getVigilanciaPessoaDenunciada().getNome();

            if (denuncia.getVigilanciaPessoaDenunciada().getNomeFantasia() != null) {
                nome += " - " + denuncia.getVigilanciaPessoaDenunciada().getNomeFantasia();
            }

            if (denuncia.getDenunciado() == null) {
                denuncia.setDenunciado(nome);
            }

            requerimentoVigilancia.setNome(nome);
            requerimentoVigilancia.setVigilanciaPessoa(denuncia.getVigilanciaPessoaDenunciada());

        }

        this.requerimentoVigilancia = BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilancia(new RequerimentoVigilanciaSolicitacaoDTO(requerimentoVigilancia));

        if (isNew) {

            BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoProtocoloSemBPA(this.requerimentoVigilancia, ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.DENUNCIA_CADASTRO);

            BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(Bundle.getStringApplication("msg_requerimento_cadastrado"), this.requerimentoVigilancia, null);

        } else if (RequerimentoVigilancia.Origem.EXTERNO.value().equals(this.requerimentoVigilancia.getOrigem())) {

            Long codigoLanctoAtivVigilancia = (Long) getSession().createCriteria(LancamentoAtividadesVigilancia.class)
                    .setProjection(Projections.max(LancamentoAtividadesVigilancia.PROP_CODIGO))
                    .add(Restrictions.eq(LancamentoAtividadesVigilancia.PROP_REQUERIMENTO_VIGILANCIA, this.requerimentoVigilancia))
                    .uniqueResult();

            if (codigoLanctoAtivVigilancia == null) {
                BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoProtocoloSemBPA(this.requerimentoVigilancia, ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.DENUNCIA_CADASTRO);
            }

        }

        denuncia.setRequerimentoVigilancia(this.requerimentoVigilancia);

        BOFactory.save(denuncia);

        if (CollectionUtils.isNotNullEmpty(dto.getEloRequerimentoVigilanciaSetorVigilanciaList())) {

            List<EloRequerimentoVigilanciaSetorVigilancia> eloList = new ArrayList<>();

            EloRequerimentoVigilanciaSetorVigilancia newElo;
            for (EloRequerimentoVigilanciaSetorVigilancia elo : dto.getEloRequerimentoVigilanciaSetorVigilanciaList()) {
                newElo = new EloRequerimentoVigilanciaSetorVigilancia();
                newElo.setSetorVigilancia(elo.getSetorVigilancia());

                eloList.add(newElo);
            }

            dto.getEloRequerimentoVigilanciaSetorVigilanciaList().clear();
            dto.getEloRequerimentoVigilanciaSetorVigilanciaList().addAll(eloList);
        }

        BOFactory.getBO(VigilanciaFacade.class).salvarEloRequerimentoVigilanciaSetorVigilancia(this.requerimentoVigilancia, dto.getEloRequerimentoVigilanciaSetorVigilanciaList(), dto.getEloRequerimentoVigilanciaSetorVigilanciaExcluirList());
        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaFiscais(this.requerimentoVigilancia, dto.getRequerimentoVigilanciaFiscalList(), dto.getRequerimentoVigilanciaFiscalListExcluir());

        if(isNew) {
            BOFactory.getBO(VigilanciaFacade.class).enviarEmailNovoRequerimentoVigilancia(this.requerimentoVigilancia);
        }

        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaAnexo(requerimentoVigilancia, dto.getRequerimentoVigilanciaAnexoDTOList(), dto.getRequerimentoVigilanciaAnexoExcluidoDTOList(), false);
    }

    private boolean isAnonimo(Denuncia denuncia) {
        return denuncia.getFlagAnonimo() != null && Denuncia.TipoSigilo.ANONIMO.value().equals(denuncia.getFlagAnonimo());
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }
}