package br.com.ksisolucoes.bo.vigilancia.externo;

import br.com.celk.bo.service.sms.interfaces.facade.SmsFacade;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.smsappservice.dto.SmsDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.CadastroUsuarioVigilanciaDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Email;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.service.sms.SmsMensagem;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissional;
import br.com.ksisolucoes.vo.vigilancia.externo.PerfilUsuarioExternoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.externo.UsuarioVigilancia;
import org.hibernate.criterion.Restrictions;
import org.hibernate.type.StringType;

/**
 * <AUTHOR>
 */
public class CadastrarNovoUsuarioExternoVigilancia extends AbstractCommandTransaction {

    private CadastroUsuarioVigilanciaDTO dto;
    private Long codigoUsuarioVigilancia;
    private UsuarioVigilancia usuarioVigilancia;

    public CadastrarNovoUsuarioExternoVigilancia(CadastroUsuarioVigilanciaDTO dto) {
        this.dto = dto;
        this.usuarioVigilancia = dto.getUsuarioVigilancia();
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        String cpf = null;
        String cnpj = null;
        validar();
        if (dto.getUsuarioVigilancia().getCpf() != null) {
            cpf = StringUtil.getDigits(dto.getUsuarioVigilancia().getCpf());
        }
        if (dto.getUsuarioVigilancia().getCnpj() != null) {
            cnpj = StringUtil.getDigits(dto.getUsuarioVigilancia().getCnpj());
        }

        String telefone = StringUtil.getDigits(dto.getUsuarioVigilancia().getTelefone());
        String cep = StringUtil.getDigits(dto.getUsuarioVigilancia().getCep());
        String chaveValidacao = StringUtil.randomStringOfLength(10);
        String senhaCriptografada = Util.criptografarSenha(dto.getUsuarioVigilancia().getSenha());

        usuarioVigilancia.setDataConfirmacao(null);
        usuarioVigilancia.setPerfilUsuarioExternoVigilancia(dto.getUsuarioVigilancia().getPerfilUsuarioExternoVigilancia());
        usuarioVigilancia.setRua(dto.getUsuarioVigilancia().getRua());
        usuarioVigilancia.setBairro(dto.getUsuarioVigilancia().getBairro());
        usuarioVigilancia.setCidade(dto.getUsuarioVigilancia().getCidade());
        usuarioVigilancia.setCep(cep);
        usuarioVigilancia.setChaveVerificacao(chaveValidacao);
        usuarioVigilancia.setCnpj(cnpj);
        usuarioVigilancia.setCpf(cpf);
        usuarioVigilancia.setDataSolicitacao(DataUtil.getDataAtual());
        usuarioVigilancia.setEmail(dto.getUsuarioVigilancia().getEmail());
        if (Coalesce.asString(cpf).isEmpty()) {
            usuarioVigilancia.setNome(dto.getUsuarioVigilancia().getRazaoSocial());
        } else {
            usuarioVigilancia.setNome(dto.getUsuarioVigilancia().getNome());
        }
        usuarioVigilancia.setNumero(dto.getUsuarioVigilancia().getNumero());
        usuarioVigilancia.setRazaoSocial(dto.getUsuarioVigilancia().getRazaoSocial());
        usuarioVigilancia.setSenha(senhaCriptografada);
        usuarioVigilancia.setTelefone(telefone);


        UsuarioVigilancia saveUV = BOFactory.save(usuarioVigilancia);
        codigoUsuarioVigilancia = saveUV.getCodigo();

        getSession().flush();//Utilizado para nao enviar o email em caso de erro devido a concorrencia.
        try {
            Email.create()
                    .assunto("Cadastro Vigilância Sanitária")
                    .para(usuarioVigilancia.getEmail())
                    .mensagem("Bem vindo ao sistema de Vigilância Sanitária!\n\n"
                            + "Para utilizar os serviços da Vigilância Sanitária, você deve liberar sua conta através link abaixo\n\n"
                            + ""
                            + "" + dto.getUrl() + "?USUP=" + codigoUsuarioVigilancia + "&SENP=" + chaveValidacao)
                    .send();

        } catch (Throwable ex) {
            Loggable.log.warn("Email: " + usuarioVigilancia.getEmail() + " - " + ex.getMessage());
            throw new ValidacaoException("Não foi possível enviar o e-mail. Verifique se o e-mail definido está correto e tente novamente.");
        }

        try {
            enviaSms();
        } catch (Throwable ex) {
            Loggable.log.warn("Envio de SMS: " + ex.getMessage());
        }

    }

    private void enviaSms() throws ValidacaoException, DAOException {
        String mensagem = Bundle.getStringApplication("msg_envio_sms_cadastro_externo_vigilancia");
        SmsDTOParam param = new SmsDTOParam();
        param.setMessage(mensagem);
        param.setPhone(usuarioVigilancia.getTelefone());
        param.setOrigem(SmsMensagem.OrigemSms.PROCESSO_DESCONHECIDO);
        param.setNomePaciente(usuarioVigilancia.getNome());

        BOFactory.getBO(SmsFacade.class).enviarSms(param);
    }

    private void validar() throws ValidacaoException {
        if (PerfilUsuarioExternoVigilancia.TipoPessoa.PESSOA_JURIDICA.value().equals(dto.getUsuarioVigilancia().getPerfilUsuarioExternoVigilancia().getTipoPessoa())
                && dto.getUsuarioVigilancia().getCnpj() != null) {
            Empresa empresaCadastrada = (Empresa) getSession().createCriteria(Empresa.class)
                    .add(Restrictions.eq(VOUtils.montarPath(Empresa.PROP_TIPO_UNIDADE), Empresa.TIPO_ESTABELECIMENTO_VIGILANCIA_EXTERNO))
                    .add(Restrictions.eq(VOUtils.montarPath(Empresa.PROP_CNPJ), StringUtil.getDigits(dto.getUsuarioVigilancia().getCnpj())))
                    .setMaxResults(1)
                    .uniqueResult();
            if (empresaCadastrada != null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_existe_empresa_cnpj_contate_usuario_administrador_ou_vigilancia_sanitaria", Util.getCnpjFormatado(dto.getUsuarioVigilancia().getCnpj())));
            }
        }

        UsuarioVigilancia uv = null;
        if (Coalesce.asString(dto.getUsuarioVigilancia().getCpf()).isEmpty()) {
            uv = (UsuarioVigilancia) getSession().createCriteria(UsuarioVigilancia.class)
                    .add(Restrictions.eq(VOUtils.montarPath(UsuarioVigilancia.PROP_CNPJ), StringUtil.getDigits(dto.getUsuarioVigilancia().getCnpj())))
                    .setMaxResults(1)
                    .uniqueResult();
        } else {
            uv = (UsuarioVigilancia) getSession().createCriteria(UsuarioVigilancia.class)
                    .add(Restrictions.eq(VOUtils.montarPath(UsuarioVigilancia.PROP_CPF), StringUtil.getDigits(dto.getUsuarioVigilancia().getCpf())))
                    .setMaxResults(1)
                    .uniqueResult();
        }

        if (uv != null && uv.getUsuario() != null) {
            Usuario usuario = (Usuario) getSession().createCriteria(Usuario.class)
                    .add(org.hibernate.criterion.Restrictions.eq(VOUtils.montarPath(Usuario.PROP_CODIGO), uv.getUsuario().getCodigo()))
                    .setMaxResults(1)
                    .uniqueResult();
            if (Usuario.STATUS_ATIVO.equals(usuario.getStatus())) {
                if (Coalesce.asString(dto.getUsuarioVigilancia().getCpf()).isEmpty()) {
                    if (uv.getCpf() != null && uv.getCpf().equals(usuario.getLogin())) {
                        StringBuilder cpfFormat = new StringBuilder(uv.getCpf().substring(0, 3)).append(".").append(uv.getCpf().substring(3, 4)).append("**.***-**");
                        throw new ValidacaoException(Bundle.getStringApplication("msg_existe_cnpj_cadastrado_login", Util.getCnpjFormatado(uv.getCnpj()), cpfFormat) + Bundle.getStringApplication("msgUtilizarEsqueciSenha"));
                    }
                    throw new ValidacaoException(Bundle.getStringApplication("msg_existe_cnpj_cadastrado", uv.getCnpj(), usuario.getLogin()));
                } else {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_existe_cpf_cadastrado", uv.getCpf()));
                }
            } else {
                // Se o usuário estiver INATIVO deve apenas reativar, para isso substitui-se o registro com as novas informações.
                usuarioVigilancia = uv;
            }
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(dto.getUsuarioVigilancia().getPerfilUsuarioExternoVigilancia().getValidarCadastro())) {
            if (PerfilUsuarioExternoVigilancia.TipoPessoa.APESSOA_FISICA.value().equals(dto.getUsuarioVigilancia().getPerfilUsuarioExternoVigilancia().getTipoPessoa())) {

                VigilanciaProfissional vigilanciaProfissional = (VigilanciaProfissional) getSession().createCriteria(VigilanciaProfissional.class)
                        .add(Restrictions.sqlRestriction("regexp_replace({alias}.cpf, '[^\\d]+', '', 'g') = ?", StringUtil.getDigits(dto.getUsuarioVigilancia().getCpf()), StringType.INSTANCE))
                        .setMaxResults(1).uniqueResult();

                if (vigilanciaProfissional == null) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_validacao_profissional_nao_cadastrado_vigilancia"));
                } else {
                    usuarioVigilancia.setVigilanciaProfissional(vigilanciaProfissional);
                }
            } else if (PerfilUsuarioExternoVigilancia.TipoPessoa.PESSOA_JURIDICA.value().equals(dto.getUsuarioVigilancia().getPerfilUsuarioExternoVigilancia().getTipoPessoa())) {

                Estabelecimento estabelecimento = (Estabelecimento) getSession().createCriteria(Estabelecimento.class)
                        .add(Restrictions.eq(Estabelecimento.PROP_CNPJ_CPF, StringUtil.getDigits(dto.getUsuarioVigilancia().getCnpj())))
                        .setMaxResults(1).uniqueResult();

                if (estabelecimento == null) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_validacao_estabelecimento_nao_cadastrado_vigilancia"));
                } else {
                    usuarioVigilancia.setEstabelecimento(estabelecimento);
                }
            }
        }
    }

    public Long getCodigoUsuarioVigilancia() {
        return codigoUsuarioVigilancia;
    }

}
