package br.com.ksisolucoes.bo.vigilancia.processoadministrativo;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.vigilancia.financeiro.CancelarVigilanciaFinanceiro;
import br.com.ksisolucoes.bo.vigilancia.interfaces.AutosHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.integracao.boleto.Boleto;
import br.com.ksisolucoes.vo.integracao.boleto.BoletoOcorrencia;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidade;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoAutenticacao;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoOcorrencia;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoRecurso;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class FinalizarProcessoAdministrativo extends AbstractCommandTransaction<FinalizarProcessoAdministrativo> {

    private Long codigoVigilanciaFinanceiro;
    private boolean viaPagamentoEfetuado;
    private ProcessoAdministrativo processoAdministrativo;
//    private AutoPenalidade autoPenalidade;

    public FinalizarProcessoAdministrativo(ProcessoAdministrativo processoAdministrativo, boolean viaPagamentoEfetuado, Long codigoVigilanciaFinanceiro) {
        this.viaPagamentoEfetuado = viaPagamentoEfetuado;
        this.processoAdministrativo = processoAdministrativo;
        this.codigoVigilanciaFinanceiro = codigoVigilanciaFinanceiro;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        processoAdministrativo = HibernateUtil.rechargeVO(ProcessoAdministrativo.class, processoAdministrativo.getCodigo(), processoAdministrativo.getVersion());

        validarFinalizacaoProcesso();

        processoAdministrativo.setSituacao(ProcessoAdministrativo.Situacao.CONCLUIDO.value());
        processoAdministrativo.setDataConclusao(DataUtil.getDataAtual());
        processoAdministrativo = BOFactory.save(processoAdministrativo);

        ProcessoAdministrativoAutenticacao processoAdministrativoAutenticacao = null;
        if (ProcessoAdministrativo.Tipo.AUTO_INFRACAO.value().equals(processoAdministrativo.getTipo())) {
            processoAdministrativoAutenticacao = processoAdministrativo.getAutoInfracao().getProcessoAdministrativoAutenticacao();
        } else if (ProcessoAdministrativo.Tipo.AUTO_MULTA.value().equals(processoAdministrativo.getTipo())) {
            processoAdministrativoAutenticacao = processoAdministrativo.getAutoMulta().getProcessoAdministrativoAutenticacao();
        }
        if (processoAdministrativoAutenticacao != null) {
            processoAdministrativoAutenticacao.setSituacao(ProcessoAdministrativoAutenticacao.Situacao.INVALIDO.value());
            BOFactory.save(processoAdministrativoAutenticacao);
        }

        ProcessoAdministrativoOcorrencia ocorrencia = new ProcessoAdministrativoOcorrencia();
        ocorrencia.setProcessoAdministrativo(processoAdministrativo);
        ocorrencia.setDataOcorrencia(DataUtil.getDataAtual());
        ocorrencia.setUsuario(getSessao().getUsuario());
        ocorrencia.setTipo(ProcessoAdministrativoOcorrencia.Tipo.CONCLUSAO.value());
        String descricaoOcorrencia = Bundle.getStringApplication("rotulo_processo_administrativo_finalizado");
        if (viaPagamentoEfetuado) {
            descricaoOcorrencia = descricaoOcorrencia.concat(" (Pagamento da Taxa Efetuado)");
        }
        ocorrencia.setDescricao(descricaoOcorrencia);
        BOFactory.save(ocorrencia);

        finalizarAutoPenalidade();
        if (!viaPagamentoEfetuado) {
            cancelarFinanceiroAutos();
        }
        BOFactory.getBO(VigilanciaFacade.class).enviarEmailProcessoAdministrativo(processoAdministrativo);

    }

    private void cancelarFinanceiroAutos() {
        if (codigoVigilanciaFinanceiro != null) {
            VigilanciaFinanceiro vigilanciaFinanceiro = (VigilanciaFinanceiro) getSession().get(VigilanciaFinanceiro.class, codigoVigilanciaFinanceiro);
            try {
                vigilanciaFinanceiro.setDescricaoMotivoCancelamento("Arquivamento do Processo Administrativo Nº" + processoAdministrativo.getNumeroProcessoFormatado());
                new CancelarVigilanciaFinanceiro(vigilanciaFinanceiro, false, true).atualizarFinanceiro();
            } catch (ValidacaoException | DAOException e) {
                Loggable.log.error(e.getMessage());
                gerarBoletoOcorrencia(getSessao().getUsuario(), vigilanciaFinanceiro.getBoleto(), e.getMessage());
            }
        }
    }

    public BoletoOcorrencia gerarBoletoOcorrencia(Usuario usuario, Boleto boletoSave, String msgBanco) {
        if (boletoSave == null || usuario == null) return null;
        BoletoOcorrencia boletoOcorrencia = new BoletoOcorrencia();
        boletoOcorrencia.setBoleto(boletoSave);
        boletoOcorrencia.setData(DataUtil.getDataAtual());
        boletoOcorrencia.setDescricao("Erro ao cancelar boleto na finalização do Processo Administrativo");
        boletoOcorrencia.setMotivo("Retorno do Banco: " + msgBanco);
        boletoOcorrencia.setUsuario(usuario);

        return salvarBoletoOcorrencia(boletoOcorrencia);
    }

    private void finalizarAutoPenalidade() throws ValidacaoException, DAOException {
        List<AutoPenalidade> autoPenalidadeList = AutosHelper.getListaAutoPenalidade(processoAdministrativo);
        if (autoPenalidadeList != null && !autoPenalidadeList.isEmpty()) {
            for (AutoPenalidade ap : autoPenalidadeList) {
                BOFactory.getBO(VigilanciaFacade.class).finalizarAutoPenalidade(ap, false);
            }
        }
    }

    private void validarFinalizacaoProcesso() throws DAOException, ValidacaoException {
        if (!viaPagamentoEfetuado) {
            ConfiguracaoVigilancia configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
            if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagTerceiraInstanciaProcessoAdministrativo())) {
                if (processoAdministrativo.getPrazoTerceiraInstancia() != null) {
                    boolean emPeriodoRecurso = AutosHelper.emPeriodoRecurso(processoAdministrativo.getPrazoTerceiraInstancia());
                    if (emPeriodoRecurso) {
                        List list = getSession().createCriteria(ProcessoAdministrativoRecurso.class)
                                .add(Restrictions.in(ProcessoAdministrativoRecurso.PROP_TIPO, Arrays.asList(ProcessoAdministrativoRecurso.Tipo.DEFESA_PENALIDADE_3_INSTANCIA.value(), ProcessoAdministrativoRecurso.Tipo.DEFESA_MULTA_3_INSTANCIA.value())))
                                .add(Restrictions.eq(ProcessoAdministrativoRecurso.PROP_PROCESSO_ADMINISTRATIVO, processoAdministrativo))
                                .list();
                        if (CollectionUtils.isEmpty(list)) {
                            throw new ValidacaoException(Bundle.getStringApplication("msg_validacao_finalizar_recurso_terceira_instancia"));
                        }
                    }
                }
            }
        }
    }

    public BoletoOcorrencia salvarBoletoOcorrencia(BoletoOcorrencia boletoOcorrencia) {
        return (BoletoOcorrencia) getSession().save(boletoOcorrencia);
    }
}
