/*
 * Created on 06/09/2004
 *
 */
package br.com.ksisolucoes.bo.entradas.estoque.movimentoestoque;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoque;

/**
 * <AUTHOR>
 *
 */
public class GerarMovimentoEstoque extends AbstractCommandTransaction {

    private static final long serialVersionUID = 1L;
    private MovimentoEstoque movimentoEstoque;
    
    public GerarMovimentoEstoque(MovimentoEstoque movimentoEstoque) {
        this.movimentoEstoque = movimentoEstoque;
    }
    
    public void execute() throws DAOException, ValidacaoException {
        this.gerarMovimentoEstoque(this.movimentoEstoque);
    }

    /**
     * Funo para gerar um movimento de estoque.
     * 
     *@param movimentoEstoque
     */
    public void gerarMovimentoEstoque(MovimentoEstoque movimentoEstoque) throws DAOException, ValidacaoException {
        new SaveMovimentoEstoque(movimentoEstoque).start();
    }
    
}
