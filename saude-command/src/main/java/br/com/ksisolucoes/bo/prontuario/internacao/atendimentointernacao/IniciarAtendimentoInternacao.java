package br.com.ksisolucoes.bo.prontuario.internacao.atendimentointernacao;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoHistorico;
import br.com.ksisolucoes.vo.prontuario.internacao.AtendimentoInternacao;
import org.hibernate.criterion.Restrictions;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class IniciarAtendimentoInternacao extends AbstractCommandTransaction{

    private Atendimento atendimento;
    private AtendimentoInternacao atendimentoInternacao;

    public IniciarAtendimentoInternacao(Atendimento atendimento) {
        this.atendimento = atendimento;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        String isHospital = getParametroContainer(Modulos.GERAL).getParametro("Hospital");
        String permiteAtendimentoParalelo = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("permiteAtendimentoParalelo");
        
        if(atendimento.getProfissional()!= null && RepositoryComponentDefault.SIM.equals(isHospital) && RepositoryComponentDefault.NAO.equals(permiteAtendimentoParalelo)){
            List<Atendimento> atendimentosPendentes = getSession().createCriteria(Atendimento.class)
                .add(Restrictions.eq(Atendimento.PROP_STATUS, Atendimento.STATUS_ATENDENDO))
                .add(Restrictions.ne(Atendimento.PROP_CODIGO, atendimento.getCodigo()))
                .add(Restrictions.eq(Atendimento.PROP_EMPRESA, atendimento.getEmpresa()))
                .add(Restrictions.eq(Atendimento.PROP_PROFISSIONAL, atendimento.getProfissional()))
                .add(Restrictions.eq(Atendimento.PROP_USUARIO_ATENDENDO, getSessao().<Usuario>getUsuario()))
                .list();
            if(CollectionUtils.isNotNullEmpty(atendimentosPendentes)){
                Atendimento atendimentoPendente = atendimentosPendentes.get(0);
                throw new ValidacaoException(Bundle.getStringApplication("msg_profissional_atendendo_X",atendimentoPendente.getProfissional().getNome(), atendimentoPendente.getUsuarioCadsus().getNomeSocial(), atendimentoPendente.getEmpresa().getDescricao()));
            }
        }

        if (atendimento.getDataAtendimento() == null) {
            atendimento.setDataAtendimento(Data.getDataAtual());
        }
        
        this.atendimento.setStatus(Atendimento.STATUS_ATENDENDO);
        this.atendimento.setUsuarioAtendendo(getSessao().<Usuario>getUsuario());

        this.atendimento = (Atendimento) BOFactory.getBO(CadastroFacade.class).save(atendimento);

        
        atendimentoInternacao = (AtendimentoInternacao) getSession().createCriteria(AtendimentoInternacao.class)
                .add(Restrictions.eq(AtendimentoInternacao.PROP_ATENDIMENTO, atendimento)).uniqueResult();
        
        if(atendimentoInternacao == null){
            atendimentoInternacao = new AtendimentoInternacao();
    
            atendimentoInternacao.setAtendimento(atendimento);
        }

        atendimentoInternacao = BOFactory.getBO(CadastroFacade.class).save(atendimentoInternacao);


        /*Adicionar atendimento historico*/
        AtendimentoHistorico atendimentoHistorico = new AtendimentoHistorico();
        atendimentoHistorico.setAtendimento(atendimento);
        atendimentoHistorico.setDataHistorico(Data.getDataAtual());
        atendimentoHistorico.setUsuario((Usuario) sessao.getUsuario());
        atendimentoHistorico.setProfissional((Profissional) atendimento.getProfissional());
        atendimentoHistorico.setDescricaoHistorico(Bundle.getStringApplication("rotulo_iniciar_atendimento_internacao"));

        getSession().save(atendimentoHistorico);


        
    }

    public AtendimentoInternacao getAtendimentoInternacao() {
        return atendimentoInternacao;
    }

}
