package br.com.ksisolucoes.bo.siab.arquivo;

import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.dbf.IDBFFieldsEnum;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.siab.SiabSsa2;
import com.linuxense.javadbf.DBFField;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public enum SiabSsa2DBFFields implements Serializable, IDBFFieldsEnum {

    COD_SEG("COD_SEG", VOUtils.montarPath(SiabSsa2.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_SEGMENTO_TERRITORIAL, SegmentoTerritorial.PROP_CODIGO), DBFField.FIELD_TYPE_C, 2, true),
    COD_AREA("COD_AREA", VOUtils.montarPath(SiabSsa2.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_CODIGO), DBFField.FIELD_TYPE_C, 4, true),
    COD_MICROA("COD_MICROA", VOUtils.montarPath(SiabSsa2.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_MICRO_AREA), DBFField.FIELD_TYPE_C, 2, true),
    COD_UB("COD_UB", VOUtils.montarPath(SiabSsa2.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA, Empresa.PROP_CNES), DBFField.FIELD_TYPE_C, 7),
    COD_ZONA("COD_ZONA", VOUtils.montarPath(SiabSsa2.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_SEGMENTO_TERRITORIAL, SegmentoTerritorial.PROP_TIPO_SEGMENTO), DBFField.FIELD_TYPE_C, 2, true),
    TIPO_EQP("TIPO_EQP", VOUtils.montarPath(SiabSsa2.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_TIPO_EQUIPE, TipoEquipe.PROP_CODIGO), DBFField.FIELD_TYPE_C, 2, true),
    ID_MODELO("ID_MODELO", VOUtils.montarPath(SiabSsa2.PROP_ID_MODELO), DBFField.FIELD_TYPE_C, 1),
    MES("MES", VOUtils.montarPath(SiabSsa2.PROP_MES), DBFField.FIELD_TYPE_C, 2, true),
    NFAMICAD("NFAMICAD", SiabSsa2.PROP_TOTAL_FAMILIAS_CADASTRADAS, DBFField.FIELD_TYPE_C, 6),
    NVISITAS("NVISITAS", SiabSsa2.PROP_VISITAS_DOMICILIARES, DBFField.FIELD_TYPE_C, 6),
    NGESCAD("NGESCAD", SiabSsa2.PROP_GESTANTES_CADASTRADAS, DBFField.FIELD_TYPE_C, 6),
    NGESM20("NGESM20", SiabSsa2.PROP_GESTANTES_MENORES20_ANOS, DBFField.FIELD_TYPE_C, 6),
    NGESAC("NGESAC", SiabSsa2.PROP_GESTANTES_ACOMPANHADAS, DBFField.FIELD_TYPE_C, 6),
    NGESVAC("NGESVAC", SiabSsa2.PROP_GESTANTES_COM_VACINAS_EM_DIA, DBFField.FIELD_TYPE_C, 6),
    NGESPRE1("NGESPRE1", SiabSsa2.PROP_GESTANTES_COM_PRE_NATAL_DO_MES, DBFField.FIELD_TYPE_C, 6),
    NGESPRE2("NGESPRE2", SiabSsa2.PROP_GESTANTES_COM_PRE_NATAL_PRIMEIRO_TRI, DBFField.FIELD_TYPE_C, 6),
    C_4MESES("C_4MESES", SiabSsa2.PROP_CRIANCAS_COM_ATE4_MESES, DBFField.FIELD_TYPE_C, 6),
    C_MAMAND("C_MAMAND", SiabSsa2.PROP_CRIANCAS_COM_ATE4_MESES_ALEITAMENTO_EXCLUSIVO, DBFField.FIELD_TYPE_C, 6),
    C_MISTO("C_MISTO", SiabSsa2.PROP_CRIANCAS_COM_ATE4_MESES_ALEITAMENTO_MISTO, DBFField.FIELD_TYPE_C, 6),
    C_0A11("C_0A11", SiabSsa2.PROP_CRIANCAS0_A11_MESES, DBFField.FIELD_TYPE_C, 6),
    C_VACDIA("C_VACDIA", SiabSsa2.PROP_CRIANCAS0_A11_MESES_COM_VACINAS_EM_DIA, DBFField.FIELD_TYPE_C, 6),
    C_0A112P("C_0A112P", SiabSsa2.PROP_CRIANCAS0_A11_MESES_PESADAS, DBFField.FIELD_TYPE_C, 6),
    C_0A11GP("C_0A11GP", SiabSsa2.PROP_CRIANCAS0_A11_MESES_DESNUTRIDAS, DBFField.FIELD_TYPE_C, 6),
    C_1223("C_1223", SiabSsa2.PROP_CRIANCAS12_A23_MESES, DBFField.FIELD_TYPE_C, 6),
    C_VACINA("C_VACINA", SiabSsa2.PROP_CRIANCAS12_A23_MESES_COM_VACINAS_EM_DIA, DBFField.FIELD_TYPE_C, 6),
    C_12232P("C_12232P", SiabSsa2.PROP_CRIANCAS12_A23_MESES_PESADAS, DBFField.FIELD_TYPE_C, 6),
    C_1223GP("C_1223GP", SiabSsa2.PROP_CRIANCAS12_A23_MESES_DESNUTRIDAS, DBFField.FIELD_TYPE_C, 6),
    C_DIARRE("C_DIARRE", SiabSsa2.PROP_CRIANCAS_MENORES2_ANOS_TIVERAM_DIARREIA, DBFField.FIELD_TYPE_C, 6),
    C_DIASRO("C_DIASRO", SiabSsa2.PROP_CRIANCAS_MENORES2_ANOS_TIVERAM_DIARREIA_USARAM_TRO, DBFField.FIELD_TYPE_C, 6),
    C_IRA("C_IRA", SiabSsa2.PROP_CRIANCAS_MENORES2_ANOS_TIVERAM_INFECCAO_RESPIRATORIA_AGUDA, DBFField.FIELD_TYPE_C, 6),
    NASCVIVO("NASCVIVO", SiabSsa2.PROP_NASCIDOS_VIVOS_NO_MES, DBFField.FIELD_TYPE_C, 6),
    PESADOS("PESADOS", SiabSsa2.PROP_RECEM_NASCIDOS_PESADOS_AO_NASCER, DBFField.FIELD_TYPE_C, 6),
    PESO2500("PESO2500", SiabSsa2.PROP_RECEM_NASCIDOS_PESADOS_AO_NASCER_COM_MENOS2500G, DBFField.FIELD_TYPE_C, 6),
    O_DIA0A28("O_DIA0A28", SiabSsa2.PROP_OBITOS_MENORES28_DIAS_POR_DIARREIA, DBFField.FIELD_TYPE_C, 6),
    O_IRA0A28("O_IRA0A28", SiabSsa2.PROP_OBITOS_MENORES28_DIAS_POR_INFECCAO_RESPIRATORIA, DBFField.FIELD_TYPE_C, 6),
    O_CAU0A28("O_CAU0A28", SiabSsa2.PROP_OBITOS_MENORES28_DIAS_POR_OUTRAS_CAUSAS, DBFField.FIELD_TYPE_C, 6),
    O_DIA28A1("O_DIA28A1", SiabSsa2.PROP_OBITOS28_DIAS_A1_ANO_POR_DIARREIA, DBFField.FIELD_TYPE_C, 6),
    O_IRA28A1("O_IRA28A1", SiabSsa2.PROP_OBITOS28_DIAS_A1_ANO_POR_INFECCAO_RESPIRATORIA, DBFField.FIELD_TYPE_C, 6),
    O_CAU28A1("O_CAU28A1", SiabSsa2.PROP_OBITOS28_DIAS_A1_ANO_POR_OUTRAS_CAUSAS, DBFField.FIELD_TYPE_C, 6),
    OBITODIA("OBITODIA", SiabSsa2.PROP_OBITOS_ATE1_ANO_POR_DIARREIA, DBFField.FIELD_TYPE_C, 6),
    OBITOIRA("OBITOIRA", SiabSsa2.PROP_OBITOS_ATE1_ANO_POR_INFECCAO_RESPIRATORIA, DBFField.FIELD_TYPE_C, 6),
    OBITOCAU("OBITOCAU", SiabSsa2.PROP_OBITOS_ATE1_ANO_POR_OUTRAS_CAUSAS, DBFField.FIELD_TYPE_C, 6),
    O_MUL10A14("O_MUL10A14", SiabSsa2.PROP_OBITOS_MULHERES10_A14_ANOS, DBFField.FIELD_TYPE_C, 6),
    OBITOMUL("OBITOMUL", SiabSsa2.PROP_OBITOS_MULHERES15_A49_ANOS, DBFField.FIELD_TYPE_C, 6),
    OBITOADOL("OBITOADOL", SiabSsa2.PROP_OBITOS_ADOLESCENTES10_A19_ANOS_POR_VIOLENCIA, DBFField.FIELD_TYPE_C, 6),
    OBITOOUT("OBITOOUT", SiabSsa2.PROP_OUTROS_OBITOS, DBFField.FIELD_TYPE_C, 6),
    D_DIABETE("D_DIABETE", SiabSsa2.PROP_DIABETICOS_CADASTRADOS, DBFField.FIELD_TYPE_C, 6),
    D_DIAAC("D_DIAAC", SiabSsa2.PROP_DIABETICOS_ACOMPANHADOS, DBFField.FIELD_TYPE_C, 6),
    D_HIPERTEN("D_HIPERTEN", SiabSsa2.PROP_HIPERTENSOS_CADASTRADOS, DBFField.FIELD_TYPE_C, 6),
    D_HIPERAC("D_HIPERAC", SiabSsa2.PROP_HIPERTENSOS_ACOMPANHADOS, DBFField.FIELD_TYPE_C, 6),
    D_TUBERCUL("D_TUBERCUL", SiabSsa2.PROP_PESSOAS_COM_TUBERCULOSE_CADASTRADAS, DBFField.FIELD_TYPE_C, 6),
    D_TUBERAC("D_TUBERAC", SiabSsa2.PROP_PESSOAS_COM_TUBERCULOSE_ACOMPANHADAS, DBFField.FIELD_TYPE_C, 6),
    D_HANSEN("D_HANSEN", SiabSsa2.PROP_PESSOAS_COM_HANSENIASE_CADASTRADAS, DBFField.FIELD_TYPE_C, 6),
    D_HANSEAC("D_HANSEAC", SiabSsa2.PROP_PESSOAS_COM_HANSENIASE_ACOMPANHADAS, DBFField.FIELD_TYPE_C, 6),
    H_0A5PNEU("H_0A5PNEU", SiabSsa2.PROP_HOSPITALIZADOS_MENORES5_ANOS_POR_PNEUMONIA, DBFField.FIELD_TYPE_C, 6),
    H_0A5DES("H_0A5DES", SiabSsa2.PROP_HOSPITALIZADOS_MENORES5_ANOS_POR_DESIDRATACAO, DBFField.FIELD_TYPE_C, 6),
    H_ALCOOL("H_ALCOOL", SiabSsa2.PROP_HOSPITALIZADOS_POR_ABUSO_ALCOOL, DBFField.FIELD_TYPE_C, 6),
    H_PSIQUI("H_PSIQUI", SiabSsa2.PROP_INTERNACOES_EM_HOSPITAL_PSIQUIATRICO, DBFField.FIELD_TYPE_C, 6),
    H_DIABETE("H_DIABETE", SiabSsa2.PROP_HOSPITALIZADOS_POR_COMPLICACOES_DIABETES, DBFField.FIELD_TYPE_C, 6),
    H_OUTCAU("H_OUTCAU", SiabSsa2.PROP_HOSPITALIZADOS_POR_OUTRAS_CAUSAS, DBFField.FIELD_TYPE_C, 6),
    SIGAB("SIGAB", SiabSsa2.PROP_SIGAB, DBFField.FIELD_TYPE_C, 1),
    ;

    private String columnName;
    private String propertyName;
    private byte propertyType;
    private int columnLength;
    private boolean completeWithZero;
    private boolean coalesce = true;

    private SiabSsa2DBFFields(String columnName, String propertyName, byte propertyType, int columnLength) {
        this.columnName = columnName;
        this.propertyName = propertyName;
        this.propertyType = propertyType;
        this.columnLength = columnLength;
    }
    
    private SiabSsa2DBFFields(String columnName, String propertyName, byte propertyType, int columnLength, boolean completeWithZero) {
        this.columnName = columnName;
        this.propertyName = propertyName;
        this.propertyType = propertyType;
        this.columnLength = columnLength;
        this.completeWithZero = completeWithZero;
    }

    @Override
    public String columnName() {
        return columnName;
    }

    @Override
    public String propertyName() {
        return propertyName;
    }

    @Override
    public byte propertyType() {
        return propertyType;
    }

    @Override
    public int columnLength() {
        return columnLength;
    }

    @Override
    public boolean completeWithZero() {
        return completeWithZero;
    }

    @Override
    public boolean coalesce() {
        return coalesce;
    }
}
