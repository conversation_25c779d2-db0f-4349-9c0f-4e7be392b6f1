package br.com.ksisolucoes.bo.vigilancia.financeiro;

import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.VeiculoEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import ch.lambdaj.Lambda;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SalvarEmissaoBoletoRequerimentoExternoVeiculos extends AbstractCommandTransaction<SalvarEmissaoBoletoRequerimentoExternoVeiculos> {

    private boolean autorizacaoSanitaria;
    private VigilanciaFinanceiroBoletoDTO dto;
    private RequerimentoVigilancia requerimentoVigilancia;
    private List<VigilanciaFinanceiro> vigilanciaFinanceiroList;
    private ConfiguracaoVigilanciaFinanceiro configuracaoVigilanciaFinanceiro;
    private  List<VeiculoEstabelecimentoDTO> veiculoEstabelecimentoDTOList;

    public SalvarEmissaoBoletoRequerimentoExternoVeiculos(RequerimentoVigilancia requerimentoVigilancia, List<VeiculoEstabelecimentoDTO> veiculoEstabelecimentoDTOList) {
        this.requerimentoVigilancia = requerimentoVigilancia;
        this.veiculoEstabelecimentoDTOList = veiculoEstabelecimentoDTOList;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        // gera boleto/memorando unico
        configuracaoVigilanciaFinanceiro = VigilanciaHelper.getConfiguracaoVigilanciaFinanceiro();
        if(ConfiguracaoVigilanciaFinanceiro.TipoCobrancaLicencaTransporte.POR_VEICULO.value().equals(configuracaoVigilanciaFinanceiro.getTipoCobrancaLicencaTransporte())) {
            if(CollectionUtils.isEmpty(veiculoEstabelecimentoDTOList)){
                throw new ValidacaoException("Obrigatório ao menos um veículo");
            }
            VigilanciaFinanceiroBoletoDTO vigilanciaFinanceiroBoletoDTO = new VigilanciaFinanceiroBoletoDTO();
            vigilanciaFinanceiroBoletoDTO.setRequerimentoVigilancia(requerimentoVigilancia);
            vigilanciaFinanceiroBoletoDTO.setEmissaoBoletoMultiploDTOList(getBoletosVeiculos(calcularDataVencto()));
            this.vigilanciaFinanceiroList = BOFactory.getBO(VigilanciaFacade.class).salvarEmissaoBoletoRequerimentoPorVeiculo(vigilanciaFinanceiroBoletoDTO);
        } else {
            this.dto = getBoletoDto();
            this.vigilanciaFinanceiroList = BOFactory.getBO(VigilanciaFacade.class).salvarEmissaoBoletoRequerimento(this.dto, false);
        }
    }


    public List<EmissaoBoletoMultiploDTO> getBoletosVeiculos(Date dataVencimento) {
        List<EmissaoBoletoMultiploDTO> returnList  = new ArrayList<>();
        if(CollectionUtils.isNotNullEmpty(veiculoEstabelecimentoDTOList)) {
            for (VeiculoEstabelecimentoDTO veiculoEstabelecimentoDTO : veiculoEstabelecimentoDTOList) {
                EmissaoBoletoMultiploDTO dto = new EmissaoBoletoMultiploDTO();
                VeiculoEstabelecimento veiculoEstabelecimento = veiculoEstabelecimentoDTO.getVeiculoEstabelecimento();
                dto.setRequerimentoVigilancia(requerimentoVigilancia);
                dto.setVeiculoEstabelecimento(veiculoEstabelecimentoDTO.getVeiculoEstabelecimento());
                dto.setInformacoesPagador(veiculoEstabelecimento.getDescricaoCompleta());
                dto.setIsentoSimNao(RepositoryComponentDefault.NAO_LONG);
                dto.setDataVencimento(Data.adjustRangeHour(dataVencimento).getDataFinal());

                returnList.add(dto);
            }
        }
        return returnList;
    }

    private VigilanciaFinanceiroBoletoDTO getBoletoDto() throws ValidacaoException {
        ValorTaxaRequerimentoVigilanciaDTO dtoTaxa = null;
        VigilanciaFinanceiroBoletoDTO dtoFinanceiro = null;
        ValorTaxaRequerimentoVigilanciaDTOParam param = new ValorTaxaRequerimentoVigilanciaDTOParam();
        param.setRequerimentoVigilancia(requerimentoVigilancia);
        param.setVeiculoEstabelecimentoList(Lambda.extract(veiculoEstabelecimentoDTOList, Lambda.on(VeiculoEstabelecimentoDTO.class).getVeiculoEstabelecimento()));
        try {
            dtoTaxa = BOFactory.getBO(VigilanciaFacade.class).valorTaxaRequerimentoVigilancia(param);
        } catch (DAOException | ValidacaoException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        if(dtoTaxa != null) {

            dtoFinanceiro = new VigilanciaFinanceiroBoletoDTO();
            dtoFinanceiro.setRequerimentoVigilancia(requerimentoVigilancia);
            dtoFinanceiro.setValorBoleto(getValorBoleto(dtoTaxa.getTotalTaxa()));
            dtoFinanceiro.setQuantidadeTaxa(dtoTaxa.getQuantidadeTaxa());
            dtoFinanceiro.setDataVencimento(calcularDataVencto());
            if(dtoTaxa.getTaxaVigente() != null) {
                dtoFinanceiro.setValorTaxa(new BigDecimal(dtoTaxa.getTaxaVigente().getValorIndice()));
            }
        }
        return dtoFinanceiro;
    }

    private Date calcularDataVencto() throws ValidacaoException {
        Long quantidadeDiasVencimentoBoleto = configuracaoVigilanciaFinanceiro.getQuantidadeDiasVencimentoBoleto();

        if (Coalesce.asLong(quantidadeDiasVencimentoBoleto) > 0) {
            Date dtVencto = Data.addDias(DataUtil.getDataAtual(), quantidadeDiasVencimentoBoleto.intValue());

            while (!Data.isDiaUtil(dtVencto)) {
                dtVencto = Data.addDias(dtVencto, 1);
            }

            return dtVencto;
        } else {
            throw new ValidacaoException("Informe na Configuração (686) a quantidade de dias para vencimento do boleto");
        }
    }

    private Double getValorBoleto(BigDecimal totalTaxa) {
        return new Dinheiro(totalTaxa).doubleValue();
    }


    public List<VigilanciaFinanceiro> getVigilanciaFinanceiroList() {
        return vigilanciaFinanceiroList;
    }
}
