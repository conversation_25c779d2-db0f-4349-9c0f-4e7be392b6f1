package br.com.ksisolucoes.bo.vigilancia.agravo.registroagravohivgestante;

import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoHivGestanteDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.agravo.helper.FichaInvestigacaoHelper;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.ClassificacaoCids;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoHivGestante;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class SalvarFichaInvestigacaoAgravoHivGestante extends AbstractCommandTransaction {

    private FichaInvestigacaoAgravoHivGestanteDTO fichaInvestigacaoAgravoHivGestanteDTO;

    public SalvarFichaInvestigacaoAgravoHivGestante(FichaInvestigacaoAgravoHivGestanteDTO fichaInvestigacaoAgravoHivGestanteDTO) {
        this.fichaInvestigacaoAgravoHivGestanteDTO = fichaInvestigacaoAgravoHivGestanteDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        InvestigacaoAgravoHivGestante investigacaoAgravo = fichaInvestigacaoAgravoHivGestanteDTO.getInvestigacaoAgravoHivGestante();
        RegistroAgravo registroAgravo = FichaInvestigacaoHelper.getInstance().getRegistroAgravo(fichaInvestigacaoAgravoHivGestanteDTO.getRegistroAgravo().getCodigo());
        UsuarioCadsus usuarioCadsus = FichaInvestigacaoHelper.getInstance().getUsuarioCadSus(registroAgravo.getUsuarioCadsus().getCodigo());
        usuarioCadsus.setTabelaCbo(investigacaoAgravo.getOcupacao());
        investigacaoAgravo.setRegistroAgravo(registroAgravo);
        Profissional profissional = FichaInvestigacaoHelper.getInstance().getProfissional(getSessao(), registroAgravo);
        Empresa empresa = FichaInvestigacaoHelper.getInstance().getEmpresa(getSessao());

        registroAgravo.setProfissionalInvestigacao(profissional);
        registroAgravo.setUnidadeProfissionalInvestigacao(empresa);
        registroAgravo.setStatus(RegistroAgravo.Status.EM_INVESTIGACAO.value());
        registroAgravo = FichaInvestigacaoHelper.getInstance().getStatusEncerramentoFicha(fichaInvestigacaoAgravoHivGestanteDTO.isEncerrarFicha(),registroAgravo);

        if (RegistroAgravo.Status.EM_INVESTIGACAO.value().equals(registroAgravo.getStatus())) {
            registroAgravo.setDataEncerramento(null);
        }

        if (!isTemFichas(investigacaoAgravo, registroAgravo)) {
            BOFactory.save(investigacaoAgravo);
            BOFactory.save(usuarioCadsus);
            BOFactory.save(registroAgravo);
        } else {
            throw new ValidacaoException(Bundle.getStringApplication("msg_ja_existe_um_registro_cadastrado_com_o_mesmo_registro_agravo"));
        }
    }

    private boolean isTemFichas(InvestigacaoAgravoHivGestante investigacaoAgravo, RegistroAgravo registroAgravo) {
        LoadManager loadManager = LoadManager.getInstance(InvestigacaoAgravoHivGestante.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(InvestigacaoAgravoHivGestante.PROP_REGISTRO_AGRAVO,RegistroAgravo.PROP_CODIGO),registroAgravo.getCodigo()));
        if (investigacaoAgravo.getCodigo() != null) {
            loadManager.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(InvestigacaoAgravoHivGestante.PROP_CODIGO), BuilderQueryCustom.QueryParameter.DIFERENTE, investigacaoAgravo.getCodigo()));
        }
        return loadManager.start().exists();
    }

}
