package br.com.ksisolucoes.bo.tipoexame;

import br.com.ksisolucoes.agendamento.dto.QueryConsultaTipoExameDTOParam;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaTipoExame extends CommandQueryPager<QueryConsultaTipoExame> {

    private QueryConsultaTipoExameDTOParam param;

    public QueryConsultaTipoExame(QueryConsultaTipoExameDTOParam param) {
        this.param = param;
    }
    
    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("te.codigo", true);
        hql.addToSelect("te.descricao", true);
        hql.setTypeSelect(TipoExame.class.getName());
        if(param.getCdClassificacao() != null) {
            hql.addToFrom("TipoExame te " +
                    "left join te.tipoProcedimento tipoProcedimento ");
            hql.addToWhereWhithAnd("tipoProcedimento.tipoProcedimentoClassificacao.codigo =", param.getCdClassificacao());

        } else {
            hql.addToFrom("TipoExame te");
        }
        hql.addToWhereWhithAnd("te.codigo = ", param.getCodigo());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("te.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("te.codigo || ' ' || te.descricao",param.getKeyword()));
        if (param.getPropSort() != null) {
            hql.addToOrder("te."+param.getPropSort()+" "+ (param.isAscending()?"asc":"desc"));
        }else{
            hql.addToOrder("te.descricao");
        }
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list =  hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
