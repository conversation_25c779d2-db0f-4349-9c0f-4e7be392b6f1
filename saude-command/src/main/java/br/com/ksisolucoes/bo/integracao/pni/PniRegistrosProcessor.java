package br.com.ksisolucoes.bo.integracao.pni;

import br.com.ksisolucoes.bo.integracao.pni.bind.GeracaoPniRegistrosBind;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.vacina.pni.PniRegistros;
import java.util.ArrayList;
import java.util.List;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;

/**
 *
 * <AUTHOR>
 */
public class PniRegistrosProcessor implements Processor {

    private final GeracaoPniRegistrosBind geracaoPniRegistrosBind;

    public PniRegistrosProcessor(GeracaoPniRegistrosBind geracaoPniRegistrosBind) {
        this.geracaoPniRegistrosBind = geracaoPniRegistrosBind;
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        List<PniRegistros> list = (List<PniRegistros>) exchange.getIn().getBody();
        List<GeracaoPniRegistrosBind> result = new ArrayList<GeracaoPniRegistrosBind>();
    
        if(CollectionUtils.isNotNullEmpty(list)){                    
            for(PniRegistros pniRegistros : list){
                GeracaoPniRegistrosBind pniRegistrosBind = VOUtils.cloneObject((GeracaoPniRegistrosBind) geracaoPniRegistrosBind);
                pniRegistrosBind.buildProperties(pniRegistros);
                result.add((GeracaoPniRegistrosBind) pniRegistrosBind);
            }
        }
        exchange.getOut().setBody(result);    
    }
}