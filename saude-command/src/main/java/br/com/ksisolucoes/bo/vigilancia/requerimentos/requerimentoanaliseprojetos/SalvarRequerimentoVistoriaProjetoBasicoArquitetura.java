package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentoanaliseprojetos;

import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaSolicitacaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVistoriaProjetoBasicoArquiteturaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.EloRequerimentoVigilanciaSetorVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.util.ArrayList;

/**
 *
 * <AUTHOR>
 */
public class SalvarRequerimentoVistoriaProjetoBasicoArquitetura extends AbstractCommandTransaction<SalvarRequerimentoVistoriaProjetoBasicoArquitetura> {

    private final RequerimentoVistoriaProjetoBasicoArquiteturaDTO dto;
    private RequerimentoVigilancia requerimentoVigilancia;
    private ConfiguracaoVigilancia configuracaoVigilancia;

    public SalvarRequerimentoVistoriaProjetoBasicoArquitetura(RequerimentoVistoriaProjetoBasicoArquiteturaDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        boolean isNew = false;
        if (dto.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getCodigo() == null) {
            isNew = true;
            dto.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().setSituacao(RequerimentoVigilancia.Situacao.PENDENTE.value());
            dto.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().setTipoDocumento(TipoSolicitacao.TipoDocumento.VISTORIA_LAUDO_CONFORMIDADE_PBA.value());
        }
        if (dto.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getVigilanciaEndereco() == null
                && dto.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getVigilanciaPessoa() != null
                && dto.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getVigilanciaPessoa().getVigilanciaEndereco() != null) {
            dto.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().setVigilanciaEndereco(dto.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getVigilanciaPessoa().getVigilanciaEndereco());
        }
        requerimentoVigilancia = BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilancia(new RequerimentoVigilanciaSolicitacaoDTO(dto.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia()));

        requerimentoVigilancia = VigilanciaHelper.atualizarGestaoRequerimento(requerimentoVigilancia, dto.getRequerimentoVigilanciaFiscalList(), dto.getEloRequerimentoVigilanciaSetorVigilanciaList(), isNew);

        BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(dto.getRequerimentoVistoriaProjetoBasicoArquitetura().getCodigo() != null ? Bundle.getStringApplication("msg_requerimento_editado") : Bundle.getStringApplication("msg_requerimento_cadastrado"), requerimentoVigilancia, null);

        dto.getRequerimentoVistoriaProjetoBasicoArquitetura().setRequerimentoVigilancia(requerimentoVigilancia);

        ajustarSetorResponsavelRequerimentoExterno();

        BOFactory.save(dto.getRequerimentoVistoriaProjetoBasicoArquitetura());

        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaAnexo(requerimentoVigilancia, dto.getRequerimentoVigilanciaAnexoDTOList(), dto.getRequerimentoVigilanciaAnexoExcluidoDTOList(), false);
        BOFactory.getBO(VigilanciaFacade.class).salvarEloRequerimentoVigilanciaSetorVigilancia(requerimentoVigilancia, dto.getEloRequerimentoVigilanciaSetorVigilanciaList(), dto.getEloRequerimentoVigilanciaSetorVigilanciaExcluirList());
        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaFiscais(requerimentoVigilancia, dto.getRequerimentoVigilanciaFiscalList(), dto.getRequerimentoVigilanciaFiscalListExcluir());
        BOFactory.getBO(VigilanciaFacade.class).salvarEloRequerimentoVigilanciaResponsavelTecnico(requerimentoVigilancia, dto.getEloRequerimentoVigilanciaResponsavelTecnicoList(), dto.getEloRequerimentoVigilanciaResponsavelTecnicoExcluirList());
        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaInscricaoImobiliaria(requerimentoVigilancia, dto.getRequerimentoVigilanciaInscricaoImobList(), dto.getRequerimentoVigilanciaInscricaoImobExcluirList());

        if(isNew) {
            BOFactory.getBO(VigilanciaFacade.class).enviarEmailNovoRequerimentoVigilancia(this.requerimentoVigilancia);
        }
    }

    private void ajustarSetorResponsavelRequerimentoExterno() throws ValidacaoException{
        if (dto.getRequerimentoVistoriaProjetoBasicoArquitetura().getCodigo() == null && RequerimentoVigilancia.Origem.EXTERNO.value().equals(requerimentoVigilancia.getOrigem())) {
            configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
            if (configuracaoVigilancia == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existe_configuracao"));
            } else if (configuracaoVigilancia.getSetorVigilanciaAnaliseProjetos() != null) {
                EloRequerimentoVigilanciaSetorVigilancia elo = new EloRequerimentoVigilanciaSetorVigilancia();
                elo.setSetorVigilancia(configuracaoVigilancia.getSetorVigilanciaAnaliseProjetos());
                if(CollectionUtils.isEmpty(dto.getEloRequerimentoVigilanciaSetorVigilanciaList())) {
                    dto.setEloRequerimentoVigilanciaSetorVigilanciaList(new ArrayList<EloRequerimentoVigilanciaSetorVigilancia>());
                }
                dto.getEloRequerimentoVigilanciaSetorVigilanciaList().add(elo);
            }
        }
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }
}