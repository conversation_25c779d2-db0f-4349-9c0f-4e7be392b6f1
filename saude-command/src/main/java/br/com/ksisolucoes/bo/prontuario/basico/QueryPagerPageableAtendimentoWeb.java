package br.com.ksisolucoes.bo.prontuario.basico;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.AtendimentoHelper;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtendimentoWebDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtendimentoWebDTOParam;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class QueryPagerPageableAtendimentoWeb extends CommandQueryPager {

    private AtendimentoWebDTOParam param;
    private String parametroTempo;

    public QueryPagerPageableAtendimentoWeb(AtendimentoWebDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        try {
            List<Long> atendimentos;
            if (param.getCodigoAtendimento() == null) {
                atendimentos = BOFactory.getBO(AtendimentoFacade.class).consultaAtendimentosEloTipoAtendimento(param);
            } else { //Recarregar um único registro da tela de consulta.
                atendimentos = new ArrayList<>();
                atendimentos.add(param.getCodigoAtendimento());
            }

            hql.addToFrom("Atendimento a");
            hql.setTypeSelect(AtendimentoWebDTO.class.getName());

            if (CollectionUtils.isNotNullEmpty(atendimentos) || RepositoryComponentDefault.SIM_LONG.equals(param.getExibirReclassificacao())) {
                hql.addToSelect("(select agah.dataAgendamento from AgendaGradeAtendimentoHorario agah where agah.atendimentoGerado.codigo = a.codigo or agah.atendimentoGerado.codigo = a.atendimentoPrincipal.codigo)", "agendaGradeAtendimentoHorario.dataAgendamento");
                hql.addToSelect("(select agah.flagEncaixe from AgendaGradeAtendimentoHorario agah where agah.atendimentoGerado.codigo = a.codigo or agah.atendimentoGerado.codigo = a.atendimentoPrincipal.codigo)", "agendaGradeAtendimentoHorario.flagEncaixe");
                hql.addToSelect("(select count(*) from Atendimento at, AtendimentoSepse asep where at.codigo = a.codigo and at.atendimentoPrincipal = asep.atendimento)", "countSepse");
                hql.addToSelect("a.senha", "atendimento.senha");
                hql.addToSelect("a.tipoSenha", "atendimento.tipoSenha");
                hql.addToSelect("a.atendimentoPrincipal.codigo", "atendimento.atendimentoPrincipal.codigo");
                hql.addToSelect("a.atendimentoPrincipal.senha", "atendimento.atendimentoPrincipal.senha");
                hql.addToSelect("a.atendimentoPrincipal.tipoSenha", "atendimento.atendimentoPrincipal.tipoSenha");
                hql.addToSelect("a.codigo", "atendimento.codigo");
                hql.addToSelect("a.version", "atendimento.version");
                hql.addToSelect("a.dataChegada", "atendimento.dataChegada");
                hql.addToSelect("a.dataCadastro", "atendimento.dataCadastro");
                hql.addToSelect("a.dataReclassificacao", "atendimento.dataReclassificacao");
                hql.addToSelect("a.dataAtendimento", "atendimento.dataAtendimento");
                hql.addToSelect("a.status", "atendimento.status");
                hql.addToSelect("a.flagPermiteReclassificacao", "atendimento.flagPermiteReclassificacao");
                hql.addToSelect("a.correcao", "atendimento.correcao");
                hql.addToSelect("a.nomePaciente", "atendimento.nomePaciente");
                hql.addToSelect("a.prioridade", "atendimento.prioridade");
                hql.addToSelect("a.usuarioCadsus.codigo", "atendimento.usuarioCadsus.codigo");
                hql.addToSelect("a.usuarioCadsus.nome", "atendimento.usuarioCadsus.nome");
                hql.addToSelect("a.usuarioCadsus.apelido", "atendimento.usuarioCadsus.apelido");
                hql.addToSelect("a.usuarioCadsus.utilizaNomeSocial", "atendimento.usuarioCadsus.utilizaNomeSocial");
                hql.addToSelect("a.usuarioCadsus.dataNascimento", "atendimento.usuarioCadsus.dataNascimento");
                hql.addToSelect("a.usuarioCadsus.sexo", "atendimento.usuarioCadsus.sexo");
                hql.addToSelect("a.usuarioCadsus.situacao", "atendimento.usuarioCadsus.situacao");
                hql.addToSelect("a.usuarioCadsus.referencia", "atendimento.usuarioCadsus.referencia");
                hql.addToSelect("a.usuarioCadsus.motivoExclusao", "atendimento.usuarioCadsus.motivoExclusao");
                hql.addToSelect("a.usuarioCadsus.foto.codigo", "atendimento.usuarioCadsus.foto.codigo");
                hql.addToSelect("a.usuarioCadsus.foto.nomeArquivo", "atendimento.usuarioCadsus.foto.nomeArquivo");
                hql.addToSelect("a.usuarioCadsus.foto.caminho", "atendimento.usuarioCadsus.foto.caminho");
                hql.addToSelect("a.naturezaProcuraTipoAtendimento.codigo", "atendimento.naturezaProcuraTipoAtendimento.codigo");
                hql.addToSelect("a.naturezaProcuraTipoAtendimento.naturezaProcura.codigo", "atendimento.naturezaProcuraTipoAtendimento.naturezaProcura.codigo");
                hql.addToSelect("a.naturezaProcuraTipoAtendimento.naturezaProcura.descricao", "atendimento.naturezaProcuraTipoAtendimento.naturezaProcura.descricao");
                hql.addToSelect("a.naturezaProcuraTipoAtendimento.tipoAtendimento.codigo", "atendimento.naturezaProcuraTipoAtendimento.tipoAtendimento.codigo");
                hql.addToSelect("a.naturezaProcuraTipoAtendimento.tipoAtendimento.descricao", "atendimento.naturezaProcuraTipoAtendimento.tipoAtendimento.descricao");
                hql.addToSelect("a.naturezaProcuraTipoAtendimento.tipoAtendimento.tipoClassificacao", "atendimento.naturezaProcuraTipoAtendimento.tipoAtendimento.tipoClassificacao");
                hql.addToSelect("a.naturezaProcuraTipoAtendimento.tipoAtendimento.corListaAtendimento", "atendimento.naturezaProcuraTipoAtendimento.tipoAtendimento.corListaAtendimento");
                hql.addToSelect("a.profissional.codigo", "atendimento.profissional.codigo");
                hql.addToSelect("a.profissional.nome", "atendimento.profissional.nome");
                hql.addToSelect("a.profissionalResponsavel.codigo", "atendimento.profissionalResponsavel.codigo");
                hql.addToSelect("a.profissionalResponsavel.nome", "atendimento.profissionalResponsavel.nome");
                hql.addToSelect("a.classificacaoRisco.codigo", "atendimento.classificacaoRisco.codigo");
                hql.addToSelect("a.classificacaoRisco.descricao", "atendimento.classificacaoRisco.descricao");
                hql.addToSelect("a.classificacaoRisco.nivelGravidade", "atendimento.classificacaoRisco.nivelGravidade");
                hql.addToSelect("a.classificacaoRisco.tempoMaximo", "atendimento.classificacaoRisco.tempoMaximo");
                hql.addToSelect("a.tipoProcedimentoAtendimento.codigo", "atendimento.tipoProcedimentoAtendimento.codigo");
                hql.addToSelect("a.tipoProcedimentoAtendimento.descricao", "atendimento.tipoProcedimentoAtendimento.descricao");
                hql.addToSelect("a.correcao", "atendimento.correcao");
                hql.addToSelect("a.paralelo", "atendimento.paralelo");
                hql.addToSelect("a.descricaoSubClassificacaoRisco", "atendimento.descricaoSubClassificacaoRisco");
                hql.addToSelect("a.motivoConsulta", "atendimento.motivoConsulta");

                hql.addToSelect("a.convenio.codigo", "atendimento.convenio.codigo");
                hql.addToSelect("a.convenio.descricao", "atendimento.convenio.descricao");
                hql.addToSelect("a.leitoQuarto.quartoInternacao.codigo", "atendimento.leitoQuarto.quartoInternacao.codigo");//1
                hql.addToSelect("a.leitoQuarto.quartoInternacao.descricao", "atendimento.leitoQuarto.quartoInternacao.descricao");//2
                hql.addToSelect("a.leitoQuarto.quartoInternacao.referencia", "atendimento.leitoQuarto.quartoInternacao.referencia");
                hql.addToSelect("a.leitoQuarto.codigo", "atendimento.leitoQuarto.codigo");//3
                hql.addToSelect("a.leitoQuarto.descricao", "atendimento.leitoQuarto.descricao");//4
                //As colunas definidas acima devem ficar na ordem comentada, senao quebra devido ao "setConvertToLeftJoin(true)"
                hql.addToSelect("a.empresa.codigo", "atendimento.empresa.codigo");
                hql.addToSelect("a.empresa.referencia", "atendimento.empresa.referencia");
                hql.addToSelect("a.empresa.descricao", "atendimento.empresa.descricao");
                hql.addToSelect("a.naturezaProcuraTipoAtendimento.imprimeTermoAutorizacao", "atendimento.naturezaProcuraTipoAtendimento.imprimeTermoAutorizacao");

                hql.addToSelect("a.dataChamada", "atendimento.dataChamada");
                hql.addToSelect("a.profissionalChamada.codigo", "atendimento.profissionalChamada.codigo");
                hql.addToSelect("a.profissionalChamada.nome", "atendimento.profissionalChamada.nome");

                if(RepositoryComponentDefault.SIM_LONG.equals(param.getExibirReclassificacao())) {
                    hql.addToWhereWhithAnd("a.flagPermiteReclassificacao = 1");
                    if (param.getNomePaciente() != null) {
                        hql.addToWhereWhithAnd("(" + hql.getConsultaLiked(" a.nomePaciente", param.getNomePaciente(),true)
                                + " OR " + hql.getConsultaLiked(" a.usuarioCadsus.nome", param.getNomePaciente(), true)
                                + " OR (a.usuarioCadsus.utilizaNomeSocial = 1 AND " + hql.getConsultaLiked("a.usuarioCadsus.apelido", param.getNomePaciente(),true) + "))");
                    }
                    hql.addToWhereWhithAnd("a.status = ", Atendimento.STATUS_AGUARDANDO);
                    if (param.getExibirAtendimentosAntigos() != null && RepositoryComponentDefault.NAO_LONG.equals(param.getExibirAtendimentosAntigos())) {
                        hql.addToWhereWhithAnd("a.dataCadastro >= ", Data.removeDias(DataUtil.getDataAtual(), 1));
                    }
                } else {
                    hql.addToWhereWhithAnd("a.codigo in ", atendimentos);
                }
                if (param.getConfigureParam().getSorter() != null) {
                    for (Map.Entry<String, String> entry : param.getConfigureParam().getSorter().entrySet()) {
                        hql.addToOrder("a." + entry.getKey() + " " + entry.getValue());
                    }
                } else {
                    hql.addToOrder("a.naturezaProcuraTipoAtendimento.tipoAtendimento.codigo");
                    hql.addToOrder("a.profissional.codigo");
                    hql.addToOrder("a.dataChegada");
                }

                hql.setConvertToLeftJoin(true);
            } else {
                hql.addToSelect("1", "atendimento.codigo");
                hql.addToWhereWhithAnd("1 = 2");
            }
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        getParametroTempo();
        for (AtendimentoWebDTO atendimentoWebDTO : (List<AtendimentoWebDTO>) list) {
            atendimentoWebDTO.setTempoAtendimento(AtendimentoHelper.getTempoAtendimento(parametroTempo, atendimentoWebDTO.getAtendimento().getDataObservacao(),
                    atendimentoWebDTO.getAtendimento().getStatus(),
                    atendimentoWebDTO.getAtendimento().getDataChegada(),
                    atendimentoWebDTO.getAtendimento().getDataCadastro()));
        }

    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    private void getParametroTempo() {
        try {
            parametroTempo = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("CalculoTempo");
        } catch (DAOException ex) {
            Logger.getLogger(QueryPagerPageableAtendimentoWeb.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
}
