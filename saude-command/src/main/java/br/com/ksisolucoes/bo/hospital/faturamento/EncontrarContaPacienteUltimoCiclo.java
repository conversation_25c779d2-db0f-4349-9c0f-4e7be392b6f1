package br.com.ksisolucoes.bo.hospital.faturamento;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Order;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import java.util.Arrays;
import org.hibernate.Criteria;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.hibernate.sql.JoinType;

/**
 * <AUTHOR>
 */
public class EncontrarContaPacienteUltimoCiclo extends AbstractCommandTransaction<EncontrarContaPacienteUltimoCiclo> {

    private Atendimento atendimento;
    private ContaPaciente contaPaciente;

    public EncontrarContaPacienteUltimoCiclo(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (atendimento.getConvenio() == null || atendimento.getUsuarioCadsus() == null || atendimento.getSequencialCiclo() == null) {
            Atendimento a = (Atendimento) this.getSession().createCriteria(Atendimento.class)
                    .add(Restrictions.eq(Atendimento.PROP_CODIGO, atendimento.getCodigo()))
                    .uniqueResult();

            if (atendimento.getConvenio() == null) {
                atendimento.setConvenio(a.getConvenio());
            }
            if (atendimento.getUsuarioCadsus() == null) {
                atendimento.setUsuarioCadsus(a.getUsuarioCadsus());
            }
            if (atendimento.getSequencialCiclo() == null) {
                atendimento.setSequencialCiclo(a.getSequencialCiclo());
            }
        }

        Criteria critAtendPrincipal = this.getSession().createCriteria(Atendimento.class);
        critAtendPrincipal.add(Restrictions.idEq(atendimento.getCodigo()));
        critAtendPrincipal.setProjection(Projections.property(Atendimento.PROP_ATENDIMENTO_PRINCIPAL));
        Atendimento atPrincipal = (Atendimento) critAtendPrincipal.uniqueResult();

        contaPaciente = (ContaPaciente) this.getSession().createCriteria(ContaPaciente.class)
//                .setProjection(Projections.distinct(Projections.property(ContaPaciente.PROP_CONTA_PACIENTE_PRINCIPAL)))
                .add(Restrictions.in(ContaPaciente.PROP_STATUS, Arrays.asList(ContaPaciente.Status.ABERTA.value())))
                .add(Restrictions.eq(ContaPaciente.PROP_CONVENIO, atendimento.getConvenio()))
                .createCriteria(ContaPaciente.PROP_ATENDIMENTO_INFORMACAO, JoinType.LEFT_OUTER_JOIN)
                .add(Restrictions.eq(AtendimentoInformacao.PROP_ATENDIMENTO_PRINCIPAL, atPrincipal))
                .addOrder(Order.desc(AtendimentoInformacao.PROP_SEQUENCIA_CICLO))
                .setMaxResults(1)
                .uniqueResult();

    }

    public ContaPaciente getContaPaciente() {
        return contaPaciente;
    }
}
