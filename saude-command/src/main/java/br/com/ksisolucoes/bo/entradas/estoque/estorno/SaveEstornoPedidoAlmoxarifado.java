package br.com.ksisolucoes.bo.entradas.estoque.estorno;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.EstornoPedidoAlmoxarifado;

/**
 * <AUTHOR>
 */
public class SaveEstornoPedidoAlmoxarifado extends SaveVO<EstornoPedidoAlmoxarifado> {

    private EstornoPedidoAlmoxarifado estornoPedidoAlmoxarifado;

    public SaveEstornoPedidoAlmoxarifado(EstornoPedidoAlmoxarifado vo) {
        super(vo);
        this.estornoPedidoAlmoxarifado = vo;
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (estornoPedidoAlmoxarifado.getUsuario() == null) {
            estornoPedidoAlmoxarifado.setUsuario((Usuario) getSessao().getUsuario());
        }
        if (estornoPedidoAlmoxarifado.getDataCadastro() == null) {
            estornoPedidoAlmoxarifado.setDataCadastro(DataUtil.getDataAtual());
        }
    }
}
