package br.com.ksisolucoes.bo.agendamento;

import br.com.celk.bo.service.sms.interfaces.SubstituirVariaveisMensagemSMSFactory;
import br.com.celk.bo.service.sms.interfaces.facade.SmsFacade;
import br.com.celk.sms.restservice.response.MensagemSmsDTO;
import br.com.celk.whatsapp.WhatsAppHelper;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.smsappservice.dto.SmsLoteDTOParam;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.cadsus.TipoOcorrencia;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamentoOcorrencia;
import br.com.ksisolucoes.vo.service.sms.SmsControleIntegracao;
import br.com.ksisolucoes.vo.service.sms.SmsMensagem;
import org.hibernate.criterion.Restrictions;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class EnviarSmsAvisoAgendamentoRemanejado extends AbstractCommandTransaction<EnviarSmsAvisoAgendamentoRemanejado> {

    private final List<AgendaGradeAtendimentoHorario> agahList;

    public EnviarSmsAvisoAgendamentoRemanejado(List<AgendaGradeAtendimentoHorario> agahList) {
        this.agahList = agahList;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        String mensagemAvisoRemanejamento = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("mensagemAvisoRemanejamento");
        if (mensagemAvisoRemanejamento == null || mensagemAvisoRemanejamento.length() == 0) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_parametro_X_nao_definido", "mensagemAvisoRemanejamento"));
        }
        WhatsAppHelper.logarProcessoWhatsApp(WhatsAppHelper.TipoMensagemWhatsApp.REAGENDAMENTO, agahList.size());
        enviarLoteSMS(getLoteMensagens(mensagemAvisoRemanejamento));
    }

    private void enviarLoteSMS(SmsLoteDTOParam smsLoteDTOParam) throws DAOException, ValidacaoException {
        Map<Long, Long> mensagemHorario = BOFactory.getBO(SmsFacade.class).enviarSmsLoteComRetornoHorarios(Thread.currentThread().getName().concat(TenantContext.getRealContext()), smsLoteDTOParam);
        List<Long> codigosMensagem = new ArrayList<>(mensagemHorario.keySet());
        List<SmsMensagem> lstSmsMensagem = (List<SmsMensagem>) getSession().createCriteria(SmsMensagem.class)
                .add(Restrictions.in(VOUtils.montarPath(SmsMensagem.PROP_CODIGO), codigosMensagem))
                .list();
        for (SmsMensagem smsMensagem : lstSmsMensagem) {
            Long codHorario = mensagemHorario.get(smsMensagem.getCodigo());
            AgendaGradeAtendimentoHorario horario = agahList.stream().filter(h -> h.getCodigo().equals(codHorario)).findFirst().orElse(null);
            if (horario.getSolicitacaoAgendamento() == null || horario.getSolicitacaoAgendamento().getCodigo() == null) {
                BOFactory.getBO(UsuarioCadsusFacade.class).gerarOcorrenciaUsuarioCadsus(horario.getUsuarioCadsus(), TipoOcorrencia.TIPO_AGENDAMENTO,
                    Bundle.getStringApplication("msg_sms_aviso_remanejamento_agendamento_encaminhado_processamento_telefone_X", horario.getUsuarioCadsus().getCelular()));
            } else {
                BOFactory.getBO(AgendamentoFacade.class).gerarOcorrenciaSolicitacaoAgendamento(SolicitacaoAgendamentoOcorrencia.TipoOcorrencia.SOLICITACAO,
                    Bundle.getStringApplication("msg_sms_aviso_remanejamento_agendamento_encaminhado_processamento_telefone_X", horario.getUsuarioCadsus().getCelular()),
                    horario.getSolicitacaoAgendamento());
            }
            gerarIntegracaoControleSms(horario, smsMensagem);
        }
    }

    private SmsLoteDTOParam getLoteMensagens(String mensagemAvisoRemanejamento) throws DAOException, ValidacaoException {
        SmsLoteDTOParam smsLoteDTOParam = new SmsLoteDTOParam();
        smsLoteDTOParam.setOrigem(SmsMensagem.OrigemSms.PROCESSO_DESCONHECIDO);
        for (AgendaGradeAtendimentoHorario agah : agahList) {
            SubstituirVariaveisMensagemSMSFactory mensagemSMS = new SubstituirVariaveisMensagemSMSFactory(agah);
            mensagemSMS.replaceVariables(mensagemAvisoRemanejamento);
            MensagemSmsDTO mensagemDTO = new MensagemSmsDTO();
            WhatsAppHelper.setMensagemWhatsAppDTO(agah, mensagemDTO, MensagemSmsDTO.TipoMensagemWhatsApp.REAGENDAMENTO);
            mensagemDTO.setMsg(mensagemSMS.getMensagem());
            mensagemDTO.setMobile(agah.getUsuarioCadsus().getCelular());
            mensagemDTO.setCodigoAgendaGradeAtendimentoHorario(agah.getCodigo());
            smsLoteDTOParam.getLstUsuarioCadsus().add(agah.getUsuarioCadsus());
            smsLoteDTOParam.getLstMensagemDTO().add(mensagemDTO);
        }
        return smsLoteDTOParam;
    }

    private void gerarIntegracaoControleSms(AgendaGradeAtendimentoHorario agah, SmsMensagem smsMensagem) throws DAOException, ValidacaoException {
        atualizarIntegracaoComReenvio(agah);
        SmsControleIntegracao smsControleIntegracao = new SmsControleIntegracao();
        smsControleIntegracao.setAgendaGradeAtendimentoHorario(agah);
        smsControleIntegracao.setStatusSms(SmsControleIntegracao.StatusSms.ENCAMINHADO.value());
        smsControleIntegracao.setTipoMensagem(SmsControleIntegracao.TipoMensagem.REMANEJAMENTO_AGENDAMENTO.value());
        smsControleIntegracao.setSmsMensagem(smsMensagem);
        BOFactory.save(smsControleIntegracao);
    }

    private void atualizarIntegracaoComReenvio(AgendaGradeAtendimentoHorario agah) throws DAOException, ValidacaoException {
        List<SmsControleIntegracao> smsControleIntegracaoList = getSession().createCriteria(SmsControleIntegracao.class)
                .add(Restrictions.eq(VOUtils.montarPath(SmsControleIntegracao.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO), agah))
                .add(Restrictions.eq(VOUtils.montarPath(SmsControleIntegracao.PROP_TIPO_MENSAGEM), SmsControleIntegracao.TipoMensagem.REMANEJAMENTO_AGENDAMENTO.value()))
                .add(Restrictions.eq(VOUtils.montarPath(SmsControleIntegracao.PROP_STATUS_SMS), SmsControleIntegracao.StatusSms.REENVIAR.value()))
                .list();
        for (SmsControleIntegracao sms : smsControleIntegracaoList) {
            sms.setStatusSms(SmsControleIntegracao.StatusSms.ENVIADO.value());
            BOFactory.save(sms);
        }
    }
}

