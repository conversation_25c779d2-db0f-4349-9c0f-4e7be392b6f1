/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.prontuario.basico.atendimentoitem;

import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoItem;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SaveAtendimentoItemList extends AbstractCommandTransaction{
        private List<AtendimentoItem> itens;
        private Atendimento atendimento;

    public SaveAtendimentoItemList(List<AtendimentoItem> itens, Atendimento atendimento) {
        this.itens = itens;
        this.atendimento = atendimento;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if(CollectionUtils.isNotNullEmpty(itens)){
            for (AtendimentoItem atendimentoEnfermagemItem : itens) {
                if ((atendimentoEnfermagemItem.getStatus().equals(AtendimentoItem.STATUS_APLICANDO_MEDICO) || atendimentoEnfermagemItem.getStatus().equals(AtendimentoItem.STATUS_APLICADO_DIRETO))
                        && atendimentoEnfermagemItem.getProfissionalAplicacao()==null) {
                    atendimentoEnfermagemItem.setProfissionalAplicacao(atendimento.getProfissional());
                    if (atendimentoEnfermagemItem.getDataHoraAplicacao()==null) {
                        atendimentoEnfermagemItem.setDataHoraAplicacao(Data.getDataAtual());
                    }
                }
                atendimentoEnfermagemItem.setAtendimento(atendimento);
                atendimentoEnfermagemItem.setProfissional(atendimento.getProfissional());

                BOFactory.getBO(CadastroFacade.class).save(atendimentoEnfermagemItem);
            }

            BOFactory.getBO(HospitalFacade.class).gerarItemContaFromAtendimentoItem(itens);
        }
    }

    public List<AtendimentoItem> getItens() {
        return itens;
    }

}
