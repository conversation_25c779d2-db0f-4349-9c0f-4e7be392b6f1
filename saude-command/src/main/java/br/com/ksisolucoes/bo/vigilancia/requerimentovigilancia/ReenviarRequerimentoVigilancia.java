package br.com.ksisolucoes.bo.vigilancia.requerimentovigilancia;

import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaEnum;

/**
 *
 * <AUTHOR>
 */
public class ReenviarRequerimentoVigilancia extends AbstractCommandTransaction {

    private RequerimentoVigilancia rv;
    private ConfiguracaoVigilancia cv;

    public ReenviarRequerimentoVigilancia(RequerimentoVigilancia rv) {
        this.rv = rv;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        rv = (RequerimentoVigilancia) getSession().get(RequerimentoVigilancia.class, rv.getCodigo());
        cv = BOFactory.getBO(VigilanciaFacade.class).carregarConfiguracaoVigilancia();
        if (ConfiguracaoVigilanciaEnum.TipoFluxoExterno.DOCUMENTACAO_PAGAMENTO.value().equals(cv.getTipoFluxoExterno())) {
            if (RequerimentoVigilancia.SituacaoAprovacao.PAGAMENTO_DEVOLVIDO.value().equals(rv.getSituacaoAprovacao())) {
                rv.setSituacaoAprovacao(RequerimentoVigilancia.SituacaoAprovacao.COMPROVANTE_PAGAMENTO.value());
            } else {
                rv.setSituacaoAprovacao(null);
            }
        } else {
            rv.setSituacao(RequerimentoVigilancia.Situacao.PENDENTE.value());
            rv.setSituacaoAprovacao(RequerimentoVigilancia.SituacaoAprovacao.APROVADO.value());
            rv.setSituacaoOcorrencia(RequerimentoVigilancia.SituacaoOcorrencia.RETORNO.value());
        }
        rv.setDataAprovacao(null);
        rv.setMotivoAprovacao(null);
        rv = BOFactory.save(rv);
        
        BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(Bundle.getStringApplication("msg_requerimento_protocolo_reenviado"), rv, null);
    }
}