/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.tfd;

import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.agendamento.exame.dto.ExameCadastroAprovacaoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.ExameProcedimentoDTO;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.ProcedimentoSolicitadoTfd;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.ExameBpai;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import java.util.ArrayList;
import java.util.List;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class GerarExameBpaiLaudoTfd extends AbstractCommandTransaction<GerarExameBpaiLaudoTfd> {

    private Long codigoLaudtoTfd;
    private final Long codigoExame;

    public GerarExameBpaiLaudoTfd(Long codigoLaudtoTfd, Long codigoExame) {
        this.codigoLaudtoTfd = codigoLaudtoTfd;
        this.codigoExame = codigoExame;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        LaudoTfd laudoTfd = (LaudoTfd) this.getSession().get(LaudoTfd.class, this.codigoLaudtoTfd);

        Exame exame = (Exame) this.getSession().get(Exame.class, this.codigoExame);

        ExameBpai exameBpai = new ExameBpai();
        exameBpai.setCidPrincipal(laudoTfd.getCid());
        exameBpai.setDescricaoDiagnostico(laudoTfd.getCid().getDescricao());

        exameBpai.setResumoAnamnese(StringUtil.removeBarraString(laudoTfd.getHistoricoDoenca()));
        exameBpai.setJustificativaProcedimento(laudoTfd.getJustificativaTfd());
        exameBpai.setLaudoTfd(laudoTfd);
        
        exameBpai.setExame(exame);

        BOFactory.getBO(CadastroFacade.class).save(exameBpai);

        laudoTfd.setExameBpai(exameBpai);
        BOFactory.getBO(CadastroFacade.class).save(laudoTfd);
    }

}
