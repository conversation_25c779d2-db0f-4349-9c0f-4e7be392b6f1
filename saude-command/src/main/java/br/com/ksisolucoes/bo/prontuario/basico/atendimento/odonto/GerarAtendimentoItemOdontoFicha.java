package br.com.ksisolucoes.bo.prontuario.basico.atendimento.odonto;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoItem;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;

/**
 *
 * <AUTHOR>
 */
public class GerarAtendimentoItemOdontoFicha extends AbstractCommandTransaction {
    
    private Atendimento atendimento; 
    private ProcedimentoCompetencia procedimentoCompetencia;

    public GerarAtendimentoItemOdontoFicha(Atendimento atendimento, ProcedimentoCompetencia procedimentoCompetencia) {
        this.atendimento = atendimento;
        this.procedimentoCompetencia = procedimentoCompetencia;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        AtendimentoItem atendimentoItem = new AtendimentoItem();

        atendimentoItem.setAtendimento(atendimento);
        atendimentoItem.setItem(1L);
        atendimentoItem.setProcedimentoCompetencia(procedimentoCompetencia);
        atendimentoItem.setDataHora(DataUtil.getDataAtual());
        atendimentoItem.setQuantidade(1D);
        atendimentoItem.setStatus(AtendimentoItem.STATUS_APLICADO);
        atendimentoItem.setOrigem(AtendimentoItem.ORIGEM_ODONTOLOGIA);
        atendimentoItem.setTipoOrigem(AtendimentoItem.TIPO_ORIGEM_TRATAMENTO_ODONTOLOGIA);
        atendimentoItem.setCompetenciaAtendimento(atendimento.getCompetenciaAtendimento());
        atendimentoItem.setProfissional(atendimento.getProfissional());                
        atendimentoItem.setProfissionalAplicacao(atendimento.getProfissional());                

        BOFactory.save(atendimentoItem);
    }
    
}
