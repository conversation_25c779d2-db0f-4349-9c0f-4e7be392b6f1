package br.com.ksisolucoes.bo.vigilancia.fiscalnarua;

import br.com.celk.util.FiscalNaRuaUtil;
import br.com.celk.vigilancia.fiscalnarua.RequerimentoVigilanciaDTO;
import br.com.celk.vigilancia.fiscalnarua.SqsSincronizadorFiscalRuaDto;
import br.com.celk.vigilancia.fiscalnarua.TipoMensagemSqsFiscalRua;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.util.List;

public class ReenviarRequerimentoTipoSolicitacaoFiscalRua extends AbstractCommandTransaction {

    private TipoSolicitacao tipoSolicitacao;
    private final Usuario usuarioExecucao;

    public ReenviarRequerimentoTipoSolicitacaoFiscalRua(TipoSolicitacao tipoSolicitacao, Usuario usuarioExecucao) {
        this.tipoSolicitacao = tipoSolicitacao;
        this.usuarioExecucao = usuarioExecucao;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (FiscalNaRuaUtil.habilitaAplicativoFiscalNaRua() &&
                RepositoryComponentDefault.SIM_LONG.equals(tipoSolicitacao.getAtivo()) &&
                RepositoryComponentDefault.SIM_LONG.equals(tipoSolicitacao.getFlagEnviarAppFiscalRua())
        ) {
            StringBuilder sb = new StringBuilder(Bundle.getStringApplication("msg_processo_requerimento_vigilancia"));
            sb.append(System.lineSeparator());
            List<RequerimentoVigilancia> listRequerimentoVigilancia = FiscalNaRuaUtil.listRequerimentoVigilancia(tipoSolicitacao);
            for (RequerimentoVigilancia rv : listRequerimentoVigilancia) {
                BOFactory.getBO(VigilanciaFacade.class)
                        .enviarMensagemSqsSincronizadorFiscalRua(new SqsSincronizadorFiscalRuaDto(new RequerimentoVigilanciaDTO(rv)), TipoMensagemSqsFiscalRua.REQUERIMENTO);
            }

            sb.append("- Requerimentos Vigilância: " + listRequerimentoVigilancia.size());
            sb.append(System.lineSeparator());

            FiscalNaRuaUtil.enviarMsg(sb, usuarioExecucao, Bundle.getStringApplication("msg_processo_requerimento_vigilancia"), Bundle.getStringApplication("msg_termino_processo_requerimento_vigilancia"));
        }
    }
}
