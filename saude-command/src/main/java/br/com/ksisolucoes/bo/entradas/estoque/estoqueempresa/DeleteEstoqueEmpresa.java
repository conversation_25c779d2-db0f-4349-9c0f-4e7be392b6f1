/*
 * Created on 26/08/2004
 *
 */
package br.com.ksisolucoes.bo.entradas.estoque.estoqueempresa;

import br.com.ksisolucoes.bo.command.DeleteVO;


/**
 * <AUTHOR>
 *
 */
public class DeleteEstoqueEmpresa extends DeleteVO {
    
    /**
     * Construtor default
     */
    public DeleteEstoqueEmpresa(Object vo) {
        super( vo );
    }
    
    /* (non-Javadoc)
     * @see br.com.ksisolucoes.command.AbstractCommand#getInterfaceChave()
     */
}