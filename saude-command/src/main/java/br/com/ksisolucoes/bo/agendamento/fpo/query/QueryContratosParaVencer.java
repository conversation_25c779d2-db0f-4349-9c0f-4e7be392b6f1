package br.com.ksisolucoes.bo.agendamento.fpo.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.ExamePrestadorContrato;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestador;
import org.hibernate.Query;

import java.util.List;
import java.util.Map;


public class QueryContratosParaVencer extends CommandQuery<QueryContratosParaVencer> {

    private List<ExamePrestadorContrato> result;
    private List<Long> empresasUsuario;
    private Long fpoPrestadorDiasAvisoVecimento;

    public QueryContratosParaVencer(List<Long> empresasUsuario, Long fpoPrestadorDiasAvisoVecimento) {
        this.empresasUsuario = empresasUsuario;
        this.fpoPrestadorDiasAvisoVecimento = fpoPrestadorDiasAvisoVecimento;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.addToSelect("examePrestadorContrato.codigo", ExamePrestadorContrato.PROP_CODIGO);
        hql.addToSelect("examePrestadorContrato.numeroContrato", ExamePrestadorContrato.PROP_NUMERO_CONTRATO);
        hql.addToSelect("examePrestadorContrato.dataValidade", ExamePrestadorContrato.PROP_DATA_VALIDADE);
        hql.addToSelect("prestador.codigo", VOUtils.montarPath(ExamePrestadorContrato.PROP_EXAME_PRESTADOR, ExamePrestador.PROP_PRESTADOR, Empresa.PROP_CODIGO));
        hql.addToSelect("prestador.descricao", VOUtils.montarPath(ExamePrestadorContrato.PROP_EXAME_PRESTADOR, ExamePrestador.PROP_PRESTADOR, Empresa.PROP_DESCRICAO));
        hql.setTypeSelect(ExamePrestadorContrato.class.getName());
        hql.addToFrom("ExamePrestadorContrato examePrestadorContrato"
                + " left join examePrestadorContrato.examePrestador examePrestador"
                + " left join examePrestador.prestador prestador");
        hql.addToWhereWhithAnd("prestador.codigo in :empresasUsuario");
        hql.addToWhereWhithAnd("examePrestadorContrato.flagVencimentoContratoNotificado is null");
        hql.addToWhereWhithAnd("(examePrestadorContrato.dataValidade - current_date) = :fpoPrestadorDiasAvisoVecimento");
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setParameterList("empresasUsuario", empresasUsuario);
        query.setParameter("fpoPrestadorDiasAvisoVecimento", fpoPrestadorDiasAvisoVecimento.doubleValue());
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<ExamePrestadorContrato> getResult() {
        return result;
    }
}
