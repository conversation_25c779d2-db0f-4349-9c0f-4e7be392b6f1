package br.com.ksisolucoes.bo.prontuario.basico.conduta;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Conduta;

public class SaveConduta extends SaveVO {

    private Conduta encaminhamento;

    public SaveConduta(Object vo) {
        super( vo );
        this.encaminhamento = (Conduta) vo;
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.encaminhamento.getDescricao() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("rotulo_digite_descricao"));
        }

        if (this.encaminhamento.getFlagRetorno() == null) {
            this.encaminhamento.setFlagRetorno(RepositoryComponentDefault.NAO_LONG);
        }
    }
    
}