package br.com.ksisolucoes.bo.consorcio.tipoconta.dominio;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.QueryConsultaDominioTipoContaDTOParam;
import br.com.ksisolucoes.bo.consorcio.tipoconta.TipoContaHelper;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.DominioTipoConta;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaDominioTipoConta extends CommandQueryPager<QueryConsultaDominioTipoConta> {

    private QueryConsultaDominioTipoContaDTOParam param;

    public QueryConsultaDominioTipoConta(QueryConsultaDominioTipoContaDTOParam param) {
        this.param = param;
    }
    
    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("dominioTipoConta.codigo", "codigo");
        hql.addToSelect("dominioTipoConta.referencia", "referencia");
        hql.addToSelect("dominioTipoConta.descricaoTipoConta", "descricaoTipoConta");
        hql.addToSelect("tipoConta.codigo", "tipoConta.codigo");
        
        hql.setTypeSelect(DominioTipoConta.class.getName());
        hql.addToFrom("DominioTipoConta dominioTipoConta");
        
        hql.addToWhereWhithAnd(hql.getConsultaLiked("dominioTipoConta.keyword", param.getKeyword()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("dominioTipoConta.descricaoTipoConta", param.getDescricao()));
        hql.addToWhereWhithAnd("tipoConta.codigo = ", param.getCodigo());
        
        if(param.getPropSort() != null){
            hql.addToOrder("dominioTipoConta."+param.getPropSort()+" "+ (param.isAscending()?"asc":"desc"));
        }else{
            hql.addToOrder("dominioTipoConta.descricaoTipoConta");
        }
        
        if (param.isValidarVisivelConsorciado()) {
            TipoContaHelper.validarVisivelConsorciado(hql, "tipoConta", getSessao().<Empresa>getEmpresa());
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>)result);
    }

}
