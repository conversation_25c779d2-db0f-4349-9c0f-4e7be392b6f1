package br.com.ksisolucoes.bo.prontuario.basico.atendimento;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.agendamento.exame.dto.ConfirmacaoChegadaAihDTO;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;
import br.com.ksisolucoes.vo.prontuario.hospital.OcorrenciaAih;

/**
 * <AUTHOR>
 */
public class ConfirmarChegadaAih extends AbstractCommandTransaction {

    private Atendimento atendimento;
    private ConfirmacaoChegadaAihDTO dto;
    private boolean novoQuarto = false;
    public ConfirmarChegadaAih(ConfirmacaoChegadaAihDTO dto) {
        this.dto = dto;
    }

    public ConfirmarChegadaAih(ConfirmacaoChegadaAihDTO dto, boolean novoQuarto) {
        super();
        this.dto = dto;
        this.novoQuarto = novoQuarto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        Aih aih = (Aih) getSession().get(Aih.class, dto.getAih().getCodigo());

        aih.setStatus(Aih.Status.CONFIRMADA.value());
        aih.setDataEntrada(DataUtil.getDataAtual());
        aih.setUsuarioEntrada(getSessao().<Usuario>getUsuario());

        if(novoQuarto){
            LeitoQuarto leitoQuarto = dto.getLeitoQuarto();
            leitoQuarto.setSituacao(LeitoQuarto.Situacao.OCUPADO.value());
            leitoQuarto.setUsuarioCadsus(aih.getUsuarioCadSus());
            aih.setLeitoQuarto(leitoQuarto);
            if (dto.getLeitoQuartoOcupado() != null && dto.getLeitoQuartoOcupado().equals(dto.getLeitoQuarto())) {
                leitoQuarto.setAihSecundaria(aih);
            } else {
                leitoQuarto.setAih(aih);
            }
            BOFactory.save(leitoQuarto);
        }else{
            aih.getLeitoQuarto().setSituacao(LeitoQuarto.Situacao.OCUPADO.value());
            aih.getLeitoQuarto().setUsuarioCadsus(aih.getUsuarioCadSus());
            aih.getLeitoQuarto().setAih(aih);
            BOFactory.save( aih.getLeitoQuarto());
        }

        BOFactory.save(aih);


        String motivo = "";

        if(dto.getDescricaoOcorrencia() != null) {
            motivo += "Observação: " + dto.getDescricaoOcorrencia();
        }

        OcorrenciaAih ocorrenciaAih = new OcorrenciaAih();
        ocorrenciaAih.setDataCadastro(DataUtil.getDataAtual());
        ocorrenciaAih.setUsuarioCadastro(getSessao().<Usuario>getUsuario());
        ocorrenciaAih.setDescricao("Confirmada a entrada/chegada do paciente no estabelecimento " + getSessao().<Empresa>getEmpresa().getDescricaoFormatado() + ". " + motivo );
        ocorrenciaAih.setAih(aih);
        BOFactory.save(ocorrenciaAih);

    }

   public Atendimento getAtendimento() {
        return atendimento;
    }
}
