package br.com.ksisolucoes.bo.entradas.estoque.estoqueempresa;

import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.EstoqueEmpresaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresaPK;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class GerarEstoqueEmpresaProduto extends AbstractCommandTransaction<GerarEstoqueEmpresaProduto> {

    private Produto produto;

    public GerarEstoqueEmpresaProduto(Produto produto) {
        this.produto = produto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        produto = (Produto) getSession().get(Produto.class, produto.getCodigo());

        List<Empresa> empresasList = BOFactory.getBO(EstoqueEmpresaFacade.class).queryEmpresasEstoquePorGrupoSubGrupo(produto.getSubGrupo().getRoGrupoProduto(), produto.getSubGrupo());
        for (Empresa empresa : empresasList) {
            EstoqueEmpresa estoqueEmpresa = new EstoqueEmpresa(new EstoqueEmpresaPK(produto, empresa));
            estoqueEmpresa.setFlagAtivo(RepositoryComponentDefault.SIM);
            BOFactory.save(estoqueEmpresa);
        }
    }

}
