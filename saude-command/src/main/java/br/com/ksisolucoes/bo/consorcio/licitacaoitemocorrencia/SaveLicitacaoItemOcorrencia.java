package br.com.ksisolucoes.bo.consorcio.licitacaoitemocorrencia;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.LicitacaoItemOcorrencia;
import br.com.ksisolucoes.vo.controle.Usuario;

/**
 * <AUTHOR>
 */
public class SaveLicitacaoItemOcorrencia extends SaveVO<LicitacaoItemOcorrencia> {

    public SaveLicitacaoItemOcorrencia(LicitacaoItemOcorrencia vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getUsuario() == null) {
            this.vo.setUsuario(getSessao().<Usuario>getUsuario());
        }
        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(Data.getDataAtual());
        }
    }

}
