<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_produto_solicitado" pageWidth="779" pageHeight="595" orientation="Landscape" columnWidth="779" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="1fa22be9-74b8-40b8-8edd-5069f410ab93">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.zoom" value="2.143588810000003"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.vo.entradas.dispensacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="formaApresentacao" class="java.lang.String"/>
	<field name="codigo" class="java.lang.Long"/>
	<field name="dataMovimento" class="java.util.Date"/>
	<field name="usuario" class="br.com.ksisolucoes.vo.controle.Usuario"/>
	<field name="dataReceita" class="java.util.Date"/>
	<field name="quantidade" class="java.lang.Double"/>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<field name="numeroBaixa" class="java.lang.Long"/>
	<field name="quantidadeOriginal" class="java.lang.Double"/>
	<field name="empresaUnidade" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<variable name="TOTAL" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="28" splitType="Stretch">
			<textField>
				<reportElement uuid="527e2c5b-030f-4fcf-8762-fe8082654e2c" x="0" y="0" width="374" height="14"/>
				<textElement>
					<font fontName="Arial" isBold="true" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_entregas")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement uuid="2cfd2561-3083-4e06-8d46-cff14007a0ef" x="0" y="27" width="779" height="1"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField>
				<reportElement uuid="0264286f-d599-4c4d-8d09-c27a087b7d65" x="52" y="15" width="91" height="12"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="ed736e91-c1f9-4589-94ff-97904ca3391a" x="144" y="15" width="175" height="12"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_usuario")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="a73dffbb-2efd-4706-b6bb-6b7669830641" x="321" y="15" width="65" height="12"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_prescricao_abv")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="eb5ffbaf-ef1a-42cd-98f5-00a9b0279cbc" x="386" y="15" width="57" height="12"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quantidade")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="c44f322c-a2cd-42d5-a0c0-1d4f4190006f" x="626" y="15" width="152" height="12"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_profissional")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="61c2fc48-6422-4657-9cd7-a291d25cfbbf" x="0" y="15" width="52" height="12"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_numero")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="848b8a34-b32c-40a6-91fe-eb9b109d0fa6" x="447" y="15" width="65" height="12"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quantidade_mensal_abv2")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="720c9435-5f89-472d-832c-143a3266158c" x="517" y="15" width="107" height="12"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade_dispensadora_abv")]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy HH:mm:ss" isBlankWhenNull="true">
				<reportElement uuid="4fe98ac3-5f9c-4ac6-92b8-18b0146b8269" x="52" y="1" width="91" height="12"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataMovimento}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement uuid="65870cae-8897-4823-a2a0-03409e2f9299" x="144" y="1" width="175" height="12"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuario}.getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement uuid="33318287-16a9-45be-bea3-249f05db9c6c" x="321" y="1" width="65" height="12"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataReceita}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement uuid="7d7af544-1319-41fc-b704-3f5bc23c914d" x="386" y="1" width="57" height="12"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidade}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement uuid="6324f6ef-937e-4d9b-8926-7ea193145d91" x="626" y="1" width="152" height="12"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profissional}.getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="0000000000" isBlankWhenNull="true">
				<reportElement uuid="57efc0d7-bbc0-4be4-8c9a-932672543cb5" x="0" y="1" width="52" height="12"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{numeroBaixa}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement uuid="9738e033-66d4-44a1-badf-0cd12c75bf72" x="447" y="1" width="65" height="12"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidadeOriginal}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement uuid="4bb424ff-084b-464a-b06f-3e776fb0cf81" x="517" y="1" width="107" height="12"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresaUnidade}.getDescricao()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<lastPageFooter>
		<band height="14">
			<textField>
				<reportElement uuid="af7e70b9-5160-49ff-8cc3-27b66107d3e8" x="331" y="0" width="42" height="12"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement uuid="236048b3-5bc1-48e0-9d69-f270a75df290" x="374" y="0" width="69" height="12"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{TOTAL}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement uuid="3d36f3ae-531a-4ca4-ab87-5c3aa6dadd5a" x="331" y="0" width="112" height="1"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<line>
				<reportElement uuid="2944613a-703e-4cea-9c9e-c5f69a073bbd" x="0" y="12" width="779" height="1"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Dashed"/>
				</graphicElement>
			</line>
		</band>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
