package br.com.ksisolucoes.report.entrada.dispensacao;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioDispensacaoRelacaoFaltasDTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.query.QueryRelatorioDispensacaoRelacaoFaltas;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioDispensacaoRelacaoFaltas extends AbstractReport<RelatorioDispensacaoRelacaoFaltasDTOParam> {

    public RelatorioDispensacaoRelacaoFaltas(RelatorioDispensacaoRelacaoFaltasDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        if(RelatorioDispensacaoRelacaoFaltasDTOParam.FormaApresentacao.PRODUTO.toString().equals(this.getParam().getFormaApresentacao())
                && RelatorioDispensacaoRelacaoFaltasDTOParam.TipoRelatorio.RESUMIDO.toString().equals(this.getParam().getTipoRelatorio())){
            this.getParam().setFormaApresentacao("");
        }
        addParametro("FORMA_APRESENTACAO", this.getParam().getFormaApresentacao());
        return new QueryRelatorioDispensacaoRelacaoFaltas();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relatorio_dispensacao_relacao_faltas");
    }

    @Override
    public String getXML() {
        if (RelatorioDispensacaoRelacaoFaltasDTOParam.TipoRelatorio.DETALHADO.toString().equals(this.param.getTipoRelatorio())) {
            return "/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_dispensacao_relacao_faltas_detalhado.jrxml";
        }
        return "/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_dispensacao_relacao_faltas_resumido.jrxml";

    }

}
