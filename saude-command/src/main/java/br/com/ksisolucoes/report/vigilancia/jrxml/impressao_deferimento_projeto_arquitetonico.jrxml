<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="impressao_deferimento_projeto_arquitetonico" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="a1b57cfd-9055-4cac-88e7-9178d405e0e1">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.6963904734899484"/>
	<property name="ireport.x" value="0"/>
    <property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="net.sf.jasperreports.engine.*"/>
    <import value="java.util.*"/>
    <import value="br.com.ksisolucoes.util.Util"/>
	<import value="br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper"/>
	<import value="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoArquitetonicoSanitario"/>
    <import value="br.com.celk.util.DataUtil"/>
    <import value="br.com.celk.util.Coalesce"/>
    <import value="br.com.ksisolucoes.util.Data"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<import value="org.apache.commons.lang.StringUtils"/>
	<subDataset name="ds-instalacoes" uuid="d229c731-6933-4d07-89c8-4a242d506247">
		<field name="tipo" class="java.lang.String"/>
		<field name="descricao" class="java.lang.String"/>
	</subDataset>
	<subDataset name="fiscais" uuid="45d81b48-d166-44a5-ba83-723bf5901748">
		<field name="nome" class="java.lang.String"/>
		<field name="referenciaRegistroFormatado" class="java.lang.String"/>
	</subDataset>
	<parameter name="cidade" class="java.lang.String"/>
	<parameter name="uf" class="java.lang.String"/>
	<parameter name="urlQRcode" class="java.lang.String"/>
	<parameter name="gerarDocumentoComAssinaturaFiscal" class="java.lang.Boolean"/>
	<field name="requerimentoProjetoArquitetonicoParecer" class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoArquitetonicoParecer">
		<fieldDescription><![CDATA[requerimentoProjetoArquitetonicoParecer]]></fieldDescription>
	</field>
	<field name="responsaveisTecnicos" class="java.lang.String">
		<fieldDescription><![CDATA[responsaveisTecnicos]]></fieldDescription>
	</field>
	<field name="nomePessoa" class="java.lang.String"/>
	<field name="nomeFantasia" class="java.lang.String"/>
	<field name="endereco" class="java.lang.String"/>
	<field name="bairro" class="java.lang.String"/>
	<field name="cep" class="java.lang.String"/>
	<field name="complemento" class="java.lang.String"/>
	<field name="fone" class="java.lang.String"/>
	<field name="atividadePrincipal" class="java.lang.String"/>
	<field name="cpfCnpj" class="java.lang.String"/>
	<field name="requerimentoVigilancia" class="br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia"/>
	<field name="descricaoTipoProjeto" class="java.lang.String"/>
	<field name="enderecoObra" class="java.lang.String"/>
	<field name="fiscais" class="java.util.List"/>
	<field name="descricaoRodape" class="java.lang.String"/>
	<group name="parecer" isStartNewPage="true">
		<groupExpression><![CDATA[$F{requerimentoProjetoArquitetonicoParecer}]]></groupExpression>
	</group>
	<group name="geral">
		<groupExpression><![CDATA[null]]></groupExpression>
	</group>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<detail>
		<band height="48">
			<rectangle>
				<reportElement positionType="Float" mode="Transparent" x="2" y="15" width="550" height="28" uuid="8c0e8d40-18de-41ed-a843-d08a9f2fe6e7"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="15" width="70" height="28" uuid="691a64b5-e8c4-4883-b20c-c53e9f5d41a5"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["PROCESSO: "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="74" y="15" width="76" height="28"
                               uuid="5aefd5fd-7da6-4207-b8de-9f304994b3b7"/>
                <box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
                    <font fontName="Arial" size="10" isBold="false"/>
				</textElement>
                <textFieldExpression>
                    <![CDATA[$F{requerimentoVigilancia}.getProtocoloFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="310" y="15" width="36" height="28" uuid="8719de2e-4728-46c4-a41c-63e505d07ec2"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["DATA: "]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="346" y="15" width="60" height="28" uuid="0f76f183-71eb-414c-99d8-1a69fc53f362"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{requerimentoProjetoArquitetonicoParecer}.getDataParecer()]]></textFieldExpression>
			</textField>
		</band>
        <band height="150">
			<rectangle>
                <reportElement positionType="Float" mode="Transparent" x="2" y="10" width="550" height="140"
                               uuid="5e7ef14b-efe8-4569-ba4d-ef6bbf8c64b9"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" mode="Opaque" x="167" y="1" width="221"
                               height="14" uuid="3ecd994b-007e-44c3-a945-baf0ad2518d7"/>
                <box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
                <textFieldExpression><![CDATA["DADOS DO PROJETO"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="4" y="15" width="92" height="14"
                               uuid="152ffd82-00bc-4ec2-88cf-ab72245beb47"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
                <textFieldExpression>
                    <![CDATA[Bundle.getStringApplication("rotulo_razao_social").toUpperCase() + ": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="97" y="15" width="453" height="14"
                               uuid="dcd94a61-6134-47f2-bfef-3d2435837653"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
                    <font fontName="Arial" size="10" isBold="false"/>
				</textElement>
                <textFieldExpression><![CDATA[$F{nomePessoa}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="4" y="32" width="92" height="14"
                               uuid="3d1d50f2-b716-4a63-a05d-f0fcc1dbf74f"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
                <textFieldExpression>
                    <![CDATA[Bundle.getStringApplication("rotulo_fantasia").toUpperCase() + ": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="97" y="32" width="294" height="14"
                               uuid="64cbf08f-2440-4b64-9ad0-086f7bb4e8fc"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
                <textElement verticalAlignment="Middle">
                    <font fontName="Arial" size="10" isBold="false"/>
				</textElement>
                <textFieldExpression><![CDATA[$F{nomeFantasia}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="391" y="32" width="64" height="14"
                               uuid="46c67ec8-b71d-4720-9863-8f7bfc3cb64e"/>
                <box leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
                    <font fontName="Arial" size="10" isBold="true"/>
				</textElement>
                <textFieldExpression>
                    <![CDATA[Bundle.getStringApplication("rotulo_cnpj_cpf").toUpperCase() + ": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="455" y="32" width="95" height="14"
                               uuid="38d1d284-3416-4d50-863f-69d45c4fe28f"/>
                <box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
                <textElement verticalAlignment="Middle">
                    <font fontName="Arial" size="10" isBold="false" isStrikeThrough="false"/>
				</textElement>
                <textFieldExpression><![CDATA[$F{cpfCnpj}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="4" y="49" width="92" height="14"
                               uuid="a87a4ebe-89fd-46d1-86e5-3a684a6999cd"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
                <textElement textAlignment="Right" verticalAlignment="Middle">
                    <font fontName="Arial" size="10" isBold="true"/>
				</textElement>
                <textFieldExpression>
                    <![CDATA[Bundle.getStringApplication("rotulo_fone").toUpperCase() + ": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="97" y="49" width="96" height="14"
                               uuid="51348877-7b1b-484f-88f0-844851d42686"/>
                <box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
                <textElement verticalAlignment="Middle">
                    <font fontName="Arial" size="10" isBold="false"/>
				</textElement>
                <textFieldExpression><![CDATA[$F{fone}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="4" y="65" width="92" height="14"
                               uuid="658c3dfd-acb6-42b7-aa7e-34635dd6edbf"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
                <textElement textAlignment="Right" verticalAlignment="Middle">
                    <font fontName="Arial" size="10" isBold="true"/>
				</textElement>
                <textFieldExpression>
                    <![CDATA[Bundle.getStringApplication("rotulo_endereco").toUpperCase() + ": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="97" y="65" width="453" height="14"
                               uuid="4dc895ed-848e-4d70-a202-580f7f8b6551"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{enderecoObra}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="4" y="81" width="92" height="14"
                               uuid="bd757d7d-b1e8-4862-914f-7eab88bebaaf"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
                <textFieldExpression><![CDATA["Nº: "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="97" y="81" width="95" height="14"
                               uuid="2c78e67a-afcf-4dd3-a77b-af137500c18e"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
                <textFieldExpression>
                    <![CDATA[$F{requerimentoProjetoArquitetonicoParecer}.getRequerimentoProjetoArquitetonicoSanitario().getObraNumeroEndereco()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="195" y="81" width="61" height="14"
                               uuid="6549dc45-c0f3-4e70-b79d-d31207b3d9c5"/>
                <box leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
                <textFieldExpression><![CDATA["QUADRA: "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="256" y="81" width="106" height="14"
                               uuid="4efe05ab-552a-4176-8564-ef6b3be4f914"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
                <textElement verticalAlignment="Middle">
                    <font fontName="Arial" size="10" isBold="false"/>
				</textElement>
                <textFieldExpression>
                    <![CDATA[$F{requerimentoProjetoArquitetonicoParecer}.getRequerimentoProjetoArquitetonicoSanitario().getObraQuadra()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="377" y="81" width="78" height="14"
                               uuid="5d99c08b-44ee-4dac-a1d7-cf33223195e1"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
                    <font fontName="Arial" size="10" isBold="true"/>
				</textElement>
                <textFieldExpression><![CDATA["Nº AO LADO: "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="455" y="81" width="95" height="14"
                               uuid="791122f9-c2fd-4fa8-b4d5-9717bb8ee336"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
                    <font fontName="Arial" size="10" isBold="false"/>
				</textElement>
                <textFieldExpression>
                    <![CDATA[$F{requerimentoProjetoArquitetonicoParecer}.getRequerimentoProjetoArquitetonicoSanitario().getObraNumeroLado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="5" y="97" width="91" height="14"
                               uuid="190ff7b0-8f56-47eb-924a-61cc51913137"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
                <textFieldExpression><![CDATA["LOTE: "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="97" y="97" width="453" height="14"
                               uuid="0a0b4810-1b9e-440a-a21c-725e17154dc9"/>
                <box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
                <textElement verticalAlignment="Middle">
                    <font fontName="Arial" size="10" isBold="false" isStrikeThrough="false"/>
				</textElement>
                <textFieldExpression>
                    <![CDATA[$F{requerimentoProjetoArquitetonicoParecer}.getRequerimentoProjetoArquitetonicoSanitario().getObraLote()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="5" y="112" width="91" height="14"
                               uuid="241d42f9-45c1-4edc-9c85-bfbd66959d4f"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
                <textElement textAlignment="Right" verticalAlignment="Middle">
                    <font fontName="Arial" size="10" isBold="true"/>
				</textElement>
                <textFieldExpression><![CDATA["COMPLEMENTO: "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="97" y="112" width="453" height="14"
                               uuid="1c166d19-d800-4d95-a311-82ebb90bfaa2"/>
                <box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
                <textElement verticalAlignment="Middle">
                    <font fontName="Arial" size="10" isBold="false"/>
				</textElement>
                <textFieldExpression>
                    <![CDATA[$F{requerimentoProjetoArquitetonicoParecer}.getRequerimentoProjetoArquitetonicoSanitario().getObraComplemento()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="5" y="128" width="91" height="14"
                               uuid="957b68ff-8ab3-4b3d-a4ea-84965a91c629"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
                    <font fontName="Arial" size="10" isBold="true" isStrikeThrough="false"/>
				</textElement>
                <textFieldExpression><![CDATA["LOT / COND: "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="97" y="128" width="453" height="14"
                               uuid="2af63d12-e826-4b5e-8379-eaed7d31dbf2"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
                <textFieldExpression>
                    <![CDATA[$F{requerimentoProjetoArquitetonicoParecer}.getRequerimentoProjetoArquitetonicoSanitario().getObraNumeroLoteamento()]]></textFieldExpression>
			</textField>
		</band>
		<band height="46">
			<printWhenExpression><![CDATA[StringUtils.trimToNull($F{responsaveisTecnicos}) != null]]></printWhenExpression>
			<elementGroup>
				<rectangle>
					<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="2" y="8" width="550" height="35" isPrintWhenDetailOverflows="true" uuid="fd8bb9f9-8c3e-477b-bc48-96904a5d87fc"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField-9" isPrintRepeatedValues="false" x="10" y="25" width="540" height="18" uuid="1d414da6-a28c-478f-aac8-61d39bd5698e"/>
					<box topPadding="1" leftPadding="2" bottomPadding="4" rightPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{responsaveisTecnicos}]]></textFieldExpression>
				</textField>
			</elementGroup>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" mode="Opaque" x="196" y="0" width="162" height="14" uuid="fc347faa-d159-4cf2-9fb0-ee4099112093"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["RESPONSABILIDADE TÉCNICA"]]></textFieldExpression>
			</textField>
		</band>
        <band height="214">
			<rectangle>
                <reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="2" y="11" width="550"
                               height="203" isPrintWhenDetailOverflows="true"
                               uuid="76c28201-fa6d-4658-b837-f5441f5dfa25"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" mode="Opaque" x="219" y="3" width="117" height="14" uuid="81f7f2af-7d98-40dd-9cd9-331aec056873"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
                <textFieldExpression>
                    <![CDATA[Bundle.getStringApplication("rotulo_aprovacao").toUpperCase() + ": "]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="5" y="23" width="81" height="14"
                               uuid="1f016b0f-9347-4274-a9f5-ec3a91189268"/>
                <box leftPadding="2">
                    <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Left" verticalAlignment="Middle">
                    <font fontName="Arial" size="10" isBold="true"/>
                </textElement>
                <textFieldExpression>
                    <![CDATA[Bundle.getStringApplication("rotulo_assunto").toUpperCase() + ": "]]></textFieldExpression>
            </textField>
            <textField isStretchWithOverflow="true" isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="5" y="37" width="457" height="14"
                               uuid="59266ba4-7211-4e6c-b3c3-d49c5d8240e1"/>
                <box topPadding="1" leftPadding="2" bottomPadding="4" rightPadding="2">
                    <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement verticalAlignment="Middle" markup="none">
                    <font fontName="Arial" size="10" isBold="false"/>
                </textElement>
                <textFieldExpression>
                    <![CDATA[$F{requerimentoProjetoArquitetonicoParecer}.getAssunto()]]></textFieldExpression>
            </textField>
            <textField isStretchWithOverflow="true" isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="6" y="61" width="537" height="14"
                               uuid="322c0f1e-33b8-48d0-b7ab-afa6bf8f3e7f"/>
                <box leftPadding="2">
                    <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Left" verticalAlignment="Middle">
                    <font fontName="Arial" size="10" isBold="true"/>
                </textElement>
                <textFieldExpression>
                    <![CDATA[Bundle.getStringApplication("emRelacaoDocumentacaoApresentada").toUpperCase() + ": "]]></textFieldExpression>
            </textField>
            <textField isStretchWithOverflow="true" isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="6" y="75" width="538" height="14"
                               uuid="3711606e-f9cf-436f-abbc-79631c852378"/>
				<box topPadding="1" leftPadding="2" bottomPadding="4" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
                <textElement verticalAlignment="Top" markup="none">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
                <textFieldExpression>
                    <![CDATA[$F{requerimentoProjetoArquitetonicoParecer}.getEmRelacaoDocApresentada()]]></textFieldExpression>
			</textField>
            <textField isStretchWithOverflow="true" isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="6" y="99" width="537" height="14"
                               uuid="c306bdab-9e1a-4c9b-a722-e4da79874aa4"/>
                <box leftPadding="2">
                    <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Left" verticalAlignment="Middle">
                    <font fontName="Arial" size="10" isBold="true"/>
                </textElement>
                <textFieldExpression>
                    <![CDATA[Bundle.getStringApplication("emRelacaoAdequacaoProjeto").toUpperCase() + ": "]]></textFieldExpression>
            </textField>
            <textField isStretchWithOverflow="true" isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="6" y="113" width="538" height="14"
                               uuid="a69b6d56-e147-454e-b6ba-fd3bdfaf66f6"/>
                <box topPadding="1" leftPadding="2" bottomPadding="4" rightPadding="2">
                    <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement verticalAlignment="Top" markup="none">
                    <font fontName="Arial" size="10" isBold="false"/>
                </textElement>
                <textFieldExpression>
                    <![CDATA[$F{requerimentoProjetoArquitetonicoParecer}.getEmRelacaoAdequacaoProjeto()]]></textFieldExpression>
            </textField>
            <textField isStretchWithOverflow="true" isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="6" y="138" width="537" height="14"
                               uuid="9f639ca4-af2d-4aa1-b1d5-b98520474d00"/>
                <box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
                <textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
                <textFieldExpression>
                    <![CDATA[Bundle.getStringApplication("rotulo_notas_obrigatorias").toUpperCase() + ": "]]></textFieldExpression>
			</textField>
            <textField isStretchWithOverflow="true" isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="6" y="152" width="537" height="14"
                               uuid="5f344f8d-eac2-4659-abab-5d80ea11fb23"/>
                <box topPadding="1" leftPadding="2" bottomPadding="4" rightPadding="2">
                    <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement verticalAlignment="Top" markup="none">
                    <font fontName="Arial" size="10" isBold="false"/>
                </textElement>
                <textFieldExpression>
                    <![CDATA[$F{requerimentoProjetoArquitetonicoParecer}.getObrigacoes()]]></textFieldExpression>
            </textField>
            <textField isStretchWithOverflow="true" isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="6" y="178" width="537" height="14"
                               uuid="9204150c-71d5-454d-96dc-95feffa303d0"/>
                <box leftPadding="2">
                    <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Left" verticalAlignment="Middle">
                    <font fontName="Arial" size="10" isBold="true"/>
                </textElement>
                <textFieldExpression>
                    <![CDATA[Bundle.getStringApplication("rotulo_aprovado").toUpperCase() + ": "]]></textFieldExpression>
            </textField>
            <textField isStretchWithOverflow="true" isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="6" y="192" width="537" height="14"
                               uuid="5660fecb-c284-4f1a-9d2e-0c816a53cf27"/>
                <box topPadding="1" leftPadding="2" bottomPadding="4" rightPadding="2">
                    <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement verticalAlignment="Top" markup="none">
                    <font fontName="Arial" size="10" isBold="false"/>
                </textElement>
                <textFieldExpression>
                    <![CDATA[$F{requerimentoProjetoArquitetonicoParecer}.getObservacao()]]></textFieldExpression>
            </textField>
		</band>
		<band height="96" splitType="Prevent">
			<printWhenExpression><![CDATA[VigilanciaHelper.exibirLinhaAssinatura()]]></printWhenExpression>
            <rectangle>
                <reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="2" y="4" width="550" height="92"
                               isPrintWhenDetailOverflows="true" uuid="77fad0ed-9546-4bcb-8d2a-45acb7877719"/>
                <graphicElement>
                    <pen lineWidth="0.5"/>
                </graphicElement>
            </rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="6" width="82" height="14" uuid="c4713c45-621b-4f16-a809-3c95af4585b7"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_local_data").toUpperCase() + ": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="86" y="6" width="464" height="14" uuid="d8714513-cb93-4b88-a55b-e96cd1b1e376"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{cidade} + ", " + DataUtil.getDataFormatadaMesString(DataUtil.getDataAtual()).toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="21" width="284" height="14" isPrintWhenDetailOverflows="true" uuid="f96ea651-6907-445e-9fe2-ee62f059003e">
					<printWhenExpression><![CDATA[$P{gerarDocumentoComAssinaturaFiscal}]]></printWhenExpression>
				</reportElement>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["AUTORIDADES SANITÁRIAS RESPONSÁVEIS PELA ANÁLISE:"]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement x="5" y="35" width="540" height="61" isRemoveLineWhenBlank="true" uuid="128ae0d6-b49f-4177-8d62-de6de1dcbf8b">
					<printWhenExpression><![CDATA[$P{gerarDocumentoComAssinaturaFiscal}]]></printWhenExpression>
				</reportElement>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Horizontal">
					<datasetRun subDataset="fiscais" uuid="402a39e3-905f-4fd4-956d-c0d9af0504ab">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource( $F{fiscais} )]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="61" width="270">
						<line>
							<reportElement x="6" y="34" width="262" height="1" uuid="f2b2941e-c829-4918-bf2b-bfa5d2922f38"/>
							<graphicElement>
								<pen lineWidth="0.5"/>
							</graphicElement>
						</line>
						<textField isBlankWhenNull="true">
							<reportElement mode="Transparent" x="6" y="34" width="262" height="15" isPrintWhenDetailOverflows="true" uuid="8988c55b-019a-457a-8b91-2bc582645e94"/>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{nome}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement stretchType="RelativeToTallestObject" x="6" y="49" width="262" height="12" uuid="b513b3d3-0d28-47c9-8a8a-4f4ced77b2bc"/>
							<box topPadding="1" leftPadding="0" bottomPadding="0" rightPadding="0">
								<topPen lineWidth="0.0"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Top">
								<font fontName="Arial" size="8" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{referenciaRegistroFormatado}]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
		</band>
		<band height="63">
			<printWhenExpression><![CDATA[!VigilanciaHelper.exibirLinhaAssinatura()]]></printWhenExpression>
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="2" y="3" width="550" height="60" isPrintWhenDetailOverflows="true" uuid="aa677f10-8bb5-4a0b-8066-f4a37bd50450"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="3" width="82" height="14" uuid="113751d1-5597-47f4-8fe0-5809fbe46657"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_local_data").toUpperCase() + ": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="86" y="3" width="464" height="14" uuid="f2dd74a4-d988-41a8-9c9e-4ce905900f8b"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{cidade} + ", " + DataUtil.getDataFormatadaMesString(DataUtil.getDataAtual()).toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="17" width="284" height="14" isPrintWhenDetailOverflows="true" uuid="9ba55b12-b0bc-4b3e-a4da-0185c9922913">
					<printWhenExpression><![CDATA[$P{gerarDocumentoComAssinaturaFiscal}]]></printWhenExpression>
				</reportElement>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["AUTORIDADES SANITÁRIAS RESPONSÁVEIS PELA ANÁLISE:"]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement stretchType="RelativeToTallestObject" x="5" y="31" width="540" height="30" isRemoveLineWhenBlank="true" uuid="e5ae21dc-6ea1-4fc1-ac9b-2e419fd9fe19">
					<printWhenExpression><![CDATA[$P{gerarDocumentoComAssinaturaFiscal}]]></printWhenExpression>
				</reportElement>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Horizontal">
					<datasetRun subDataset="fiscais" uuid="a4794188-1c00-4244-8a83-584521104ac8">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource( $F{fiscais} )]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="30" width="270">
						<textField isBlankWhenNull="true">
							<reportElement mode="Transparent" x="6" y="3" width="262" height="15" isPrintWhenDetailOverflows="true" uuid="b09c55e3-dd3b-417b-822a-28a4c688f260"/>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{nome}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement stretchType="RelativeToTallestObject" x="6" y="18" width="262" height="12" uuid="6d3b1b34-df24-4e0a-986b-785b95d31687"/>
							<box topPadding="1" leftPadding="0" bottomPadding="0" rightPadding="0">
								<topPen lineWidth="0.0"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Top">
								<font fontName="Arial" size="8" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{referenciaRegistroFormatado}]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
		</band>
		<band height="71" splitType="Prevent">
			<line>
				<reportElement x="69" y="60" width="1" height="10" uuid="09d63ea3-a1e7-4b18-bb4d-48504736b358"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="3" y="69" width="10" height="1" uuid="eb8a9149-9fe9-4629-a342-2bfc03533bb9"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="2" y="60" width="1" height="10" uuid="927b5110-fa87-4c14-a675-39575a0cc93a"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</line>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="80" y="3" width="473" height="11" uuid="c1bff3f1-e871-451d-a1db-7e843fe67610"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Acompanhe a situação do requerimento direto do seu dispositivo móvel através do QRcode ao lado"]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="2" y="2" width="1" height="10" uuid="4d6f0cb5-cf31-4985-be7d-90450cb995d7"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</line>
			<image scaleImage="RealSize">
				<reportElement x="6" y="6" width="60" height="60" uuid="c991063f-56af-466b-ae18-6ad213739094"/>
				<imageExpression><![CDATA[com.google.zxing.client.j2se.MatrixToImageWriter.toBufferedImage(
    new com.google.zxing.qrcode.QRCodeWriter().encode(
            $P{urlQRcode},
            com.google.zxing.BarcodeFormat.QR_CODE, 600, 600))]]></imageExpression>
			</image>
			<line>
				<reportElement x="59" y="69" width="10" height="1" uuid="250d98c0-7b96-42dc-bdc8-e703b8bad30a"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="59" y="2" width="10" height="1" uuid="d1ad31c6-9c67-4553-8b1f-767f422ecf3d"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="69" y="2" width="1" height="10" uuid="ada1db2b-ab67-4bd8-8153-5696ea2f8bc2"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="2" y="2" width="10" height="1" uuid="d7a86f56-dcc6-4050-924d-5a9a8780d984"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</line>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-9" x="81" y="15" width="472" height="56" uuid="8893e831-2f32-4090-91c7-63b6822236fa"/>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoRodape}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
