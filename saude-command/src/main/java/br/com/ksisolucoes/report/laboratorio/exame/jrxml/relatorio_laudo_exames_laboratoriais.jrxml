<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_laudo_exames_laboratoriais" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="812a5b70-40d5-4117-bc4f-81ca6e8acef4">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.8181818181818181"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.FormasUsoHelper"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="caminhoImagem" class="java.lang.String" isForPrompting="false"/>
	<field name="exameRequisicao" class="br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao"/>
	<group name="Exame" isStartNewPage="true">
		<groupExpression><![CDATA[$F{exameRequisicao}.getExame()]]></groupExpression>
		<groupHeader>
			<band height="114">
				<textField isBlankWhenNull="true">
					<reportElement uuid="2ab87f75-6c1b-4f8f-8448-76e1b22ae861" key="textField-9" positionType="Float" x="428" y="17" width="29" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_sexo")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement uuid="91f122e0-d979-4d05-9ef9-4b958d326746" key="textField-9" x="0" y="2" width="33" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement uuid="78186ffb-8f76-4b97-89a8-300d1b379d81" key="textField-8" x="89" y="17" width="149" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[Data.formatar($F{exameRequisicao}.getExame().getUsuarioCadsus().getDataNascimento())]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement uuid="893b8d5e-a078-4b15-9efb-e1723c7018e6" key="textField-9" x="0" y="32" width="23" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_mae")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement uuid="439733db-7d6d-4b12-9176-201f16f53e55" key="textField-10" x="25" y="32" width="345" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getUsuarioCadsus().getNomeMae()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement uuid="a93f00ca-f36a-4956-a25b-3059e6827e33" key="textField-7" x="0" y="17" width="89" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_nascimento")+": "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement uuid="c5c20e7f-b3fc-4ca6-9326-d6114c2f9e55" key="textField-10" x="35" y="2" width="519" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getNomePaciente()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement uuid="b0f68a33-29f8-458f-8e72-d561ff3fc2c5" key="textField-10" positionType="Float" x="459" y="17" width="48" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getUsuarioCadsus().getSexoFormatado()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement uuid="2d88cf1c-0caf-4284-9161-44b71173190e" key="textField-10" positionType="Float" x="0" y="97" width="555" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getTipoExame().getDescricao()]]></textFieldExpression>
				</textField>
				<line>
					<reportElement uuid="0564ffb4-fe4a-4c2e-a510-8ebfddb2526f" positionType="Float" x="11" y="111" width="532" height="1"/>
				</line>
				<line>
					<reportElement uuid="0e67c792-d4aa-4642-838e-1b3e7be29a6b" positionType="Float" x="0" y="94" width="555" height="1"/>
				</line>
				<textField isBlankWhenNull="true">
					<reportElement uuid="893b8d5e-a078-4b15-9efb-e1723c7018e6" key="textField-9" x="0" y="47" width="30" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_setor")+": "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement uuid="439733db-7d6d-4b12-9176-201f16f53e55" key="textField-10" x="32" y="47" width="243" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getAtendimento().getEmpresa().getDescricao()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement uuid="893b8d5e-a078-4b15-9efb-e1723c7018e6" key="textField-9" x="277" y="47" width="38" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quarto")+": "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement uuid="439733db-7d6d-4b12-9176-201f16f53e55" key="textField-10" x="317" y="47" width="112" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getAtendimento().getLeitoQuarto().getQuartoInternacao().getDescricao()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement uuid="893b8d5e-a078-4b15-9efb-e1723c7018e6" key="textField-9" x="429" y="47" width="28" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_leito")+": "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement uuid="439733db-7d6d-4b12-9176-201f16f53e55" key="textField-10" x="459" y="47" width="48" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getAtendimento().getLeitoQuarto().getDescricao()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement uuid="c8e8994f-3f1b-4a3b-a8a3-5633b6acb6cc" key="textField-10" positionType="Float" x="459" y="32" width="95" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getAtendimento().getConvenio().getDescricao()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement uuid="f941d631-d83d-4e63-93e8-4eceb43dd92c" key="textField-9" positionType="Float" x="400" y="32" width="57" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_convenio")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement uuid="f0b92eb5-7050-41e3-a7b4-c1e877f8cab4" key="textField-7" x="282" y="17" width="33" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_idade")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement uuid="f56c2a89-37ff-43db-8d22-1ef1eca40bba" key="textField-8" x="317" y="17" width="111" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[Data.getDescricaoIdade($F{exameRequisicao}.getExame().getUsuarioCadsus().getDataNascimento())]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement uuid="893b8d5e-a078-4b15-9efb-e1723c7018e6" key="textField-9" x="0" y="62" width="115" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_numero_requisicao")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement uuid="893b8d5e-a078-4b15-9efb-e1723c7018e6" key="textField-9" x="247" y="77" width="68" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_emissao")+": "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement uuid="439733db-7d6d-4b12-9176-201f16f53e55" key="textField-10" x="117" y="62" width="121" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{exameRequisicao}.getNumeroLaudo()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
					<reportElement uuid="439733db-7d6d-4b12-9176-201f16f53e55" key="textField-10" x="101" y="77" width="137" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getDataSolicitacao()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement uuid="893b8d5e-a078-4b15-9efb-e1723c7018e6" key="textField-9" x="0" y="77" width="100" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_requisicao")+": "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
					<reportElement uuid="439733db-7d6d-4b12-9176-201f16f53e55" key="textField-10" x="317" y="77" width="83" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{exameRequisicao}.getDataResultado()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="30" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="962d3e65-0f74-46c1-ad98-938a47998a9c" key="textField-10" x="11" y="1" width="532" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{exameRequisicao}.getExameProcedimento().getDescricaoProcedimento()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="a3089e6c-b5cf-4d34-aa12-ff9e258f08f7" key="textField-10" positionType="Float" x="23" y="14" width="520" height="12" isRemoveLineWhenBlank="true"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{exameRequisicao}.getDescricaoResultado()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement uuid="0564ffb4-fe4a-4c2e-a510-8ebfddb2526f" positionType="FixRelativeToBottom" x="11" y="28" width="532" height="1"/>
			</line>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="64">
			<image>
				<reportElement uuid="db8d3302-8721-40ac-b181-21d0d5b4bb00" x="428" y="0" width="114" height="64"/>
				<imageExpression><![CDATA[$P{caminhoImagem}]]></imageExpression>
			</image>
		</band>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
