package br.com.ksisolucoes.report.vigilancia.cva;

import br.com.celk.vigilancia.dto.RelatorioSolicitacaoAgendamentoDTOParam;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.vigilancia.query.QueryRelatorioSolicitacaoAgendadaCva;
import br.com.ksisolucoes.report.vigilancia.query.QueryRelatorioSolicitacaoPendenteCva;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioSolicitacaoPendenteCva extends AbstractReport<RelatorioSolicitacaoAgendamentoDTOParam>{
    
    RelatorioSolicitacaoAgendamentoDTOParam param;

    public RelatorioSolicitacaoPendenteCva(RelatorioSolicitacaoAgendamentoDTOParam param) {
        super(param);
        this.param = param;
    }

    @Override
    public ITransferDataReport getQuery() {
        this.addParametro("formaApresentacao", this.getParam().getFormaApresentacao());
        return new QueryRelatorioSolicitacaoPendenteCva();
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_solicitacao_pendente_cva.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_solicitacao_pendente");
    }
}
