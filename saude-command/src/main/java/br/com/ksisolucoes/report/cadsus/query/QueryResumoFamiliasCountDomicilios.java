package br.com.ksisolucoes.report.cadsus.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.QueryResumoFamiliasDTO;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.QueryResumoFamiliasDTOParam;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryResumoFamiliasCountDomicilios extends CommandQuery<QueryResumoFamiliasCountDomicilios> implements ITransferDataReport<QueryResumoFamiliasDTOParam, QueryResumoFamiliasDTO> {

    private QueryResumoFamiliasDTOParam param;
    private Collection<QueryResumoFamiliasDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("count(distinct enderecoDomicilio.codigo)", "quantidade");
        
        hql.setTypeSelect(QueryResumoFamiliasDTO.class.getName());
        hql.addToFrom("UsuarioCadsus usuarioCadsus "
                + " left join usuarioCadsus.enderecoDomicilio enderecoDomicilio"
                + " left join enderecoDomicilio.equipeMicroArea equipeMicroArea "
                + " left join equipeMicroArea.empresa empresa"
                + " left join equipeMicroArea.equipeProfissional equipeProfissional"
                + " left join equipeProfissional.profissional profissional"
                + " left join equipeMicroArea.equipeArea equipeArea"
                + " left join equipeArea.segmentoTerritorial segmentoTerritorial"
                );
        
        hql.addToWhereWhithAnd("usuarioCadsus.situacao not in ", Arrays.asList(UsuarioCadsus.SITUACAO_EXCLUIDO, UsuarioCadsus.SITUACAO_INATIVO));

        hql.addToWhereWhithAnd("empresa in ", param.getEstabelecimento());
        hql.addToWhereWhithAnd("profissional = ", this.param.getProfissional());
        hql.addToWhereWhithAnd("equipeArea = ", this.param.getArea());
        hql.addToWhereWhithAnd("equipeMicroArea = ", this.param.getEquipeMicroArea());
        hql.addToWhereWhithAnd("segmentoTerritorial = ", this.param.getSegmento());
        
    }

    @Override
    public void setDTOParam(QueryResumoFamiliasDTOParam param) {
        this.param = param;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection<QueryResumoFamiliasDTO> getResult() {
        return this.result;
    }
}
