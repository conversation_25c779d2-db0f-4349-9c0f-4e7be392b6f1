<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_anexo_xviii" pageWidth="776" pageHeight="538" orientation="Landscape" columnWidth="710" leftMargin="33" rightMargin="33" topMargin="21" bottomMargin="22">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<import value="br.com.ksisolucoes.vo.entradas.estoque.*"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="FORMA_APRESENTACAO" class="java.lang.String" isForPrompting="false"/>
	<parameter name="tipoDocumentoDispensacaoMedicamento" class="java.lang.Long" isForPrompting="false"/>
	<field name="dataLancamento" class="java.util.Date">
		<fieldDescription><![CDATA[dataLancamento]]></fieldDescription>
	</field>
	<field name="descricaoTipoDocumento" class="java.lang.String">
		<fieldDescription><![CDATA[descricaoTipoDocumento]]></fieldDescription>
	</field>
	<field name="estoqueFisico" class="java.lang.Double">
		<fieldDescription><![CDATA[estoqueFisico]]></fieldDescription>
	</field>
	<field name="flagInventario" class="java.lang.String">
		<fieldDescription><![CDATA[flagInventario]]></fieldDescription>
	</field>
	<field name="flagTipoMovimento" class="java.lang.String">
		<fieldDescription><![CDATA[flagTipoMovimento]]></fieldDescription>
	</field>
	<field name="nomePessoa" class="java.lang.String">
		<fieldDescription><![CDATA[nomePessoa]]></fieldDescription>
	</field>
	<field name="quantidade" class="java.lang.Double">
		<fieldDescription><![CDATA[quantidade]]></fieldDescription>
	</field>
	<field name="codigoProduto" class="java.lang.String">
		<fieldDescription><![CDATA[codigoProduto]]></fieldDescription>
	</field>
	<field name="descricaoProduto" class="java.lang.String">
		<fieldDescription><![CDATA[descricaoProduto]]></fieldDescription>
	</field>
	<field name="codigoTipoDocumento" class="java.lang.Long">
		<fieldDescription><![CDATA[codigoTipoDocumento]]></fieldDescription>
	</field>
	<field name="numeroDocumento" class="java.lang.Long"/>
	<field name="descricaoUnidade" class="java.lang.String"/>
	<field name="saldoAnterior" class="java.lang.Double"/>
	<field name="codigoDcb" class="java.lang.String"/>
	<field name="farmaceutico" class="java.lang.String"/>
	<field name="nomeDcb" class="java.lang.String"/>
	<group name="PRODUTO_QUEBRA" isStartNewPage="true">
		<groupExpression><![CDATA[$F{codigoProduto}]]></groupExpression>
		<groupHeader>
			<band height="39" splitType="Stretch">
				<textField isBlankWhenNull="true">
					<reportElement key="textField-26" x="0" y="10" width="710" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Justified" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication( "rotulo_codigo_dcb" ) + ": " + $F{codigoDcb} + " - " + Bundle.getStringApplication( "rotulo_descricao_dcb_abv" ) + ": " + $F{nomeDcb}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-29" x="517" y="10" width="193" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication( "rotulo_saldo_anterior" ) + ": " + Valor.adicionarFormatacaoMonetaria( $F{saldoAnterior} )]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField-26" x="0" y="24" width="710" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Justified" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication( "rotulo_nome_medicamento" ) + ": " + $F{descricaoProduto}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<group name="DETALHES" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band height="30" splitType="Stretch">
				<textField isBlankWhenNull="false">
					<reportElement key="textField-13" x="65" y="0" width="265" height="30"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font size="8"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication( "rotulo_historico" ).toUpperCase()]]></textFieldExpression>
				</textField>
				<elementGroup>
					<textField isBlankWhenNull="false">
						<reportElement key="textField-2" x="0" y="0" width="65" height="30"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
							<font size="8"/>
						</textElement>
						<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication( "rotulo_data" ).toUpperCase()]]></textFieldExpression>
					</textField>
				</elementGroup>
				<elementGroup>
					<textField isBlankWhenNull="false">
						<reportElement key="textField-6" x="330" y="0" width="150" height="15"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
							<font size="8"/>
						</textElement>
						<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication( "rotulo_movimento" ).toUpperCase()]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="false">
						<reportElement key="textField-7" x="330" y="15" width="50" height="15"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
							<font size="8"/>
						</textElement>
						<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication( "rotulo_entrada" )]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="false">
						<reportElement key="textField-8" x="380" y="15" width="50" height="15"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
							<font size="8"/>
						</textElement>
						<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication( "rotulo_saida" )]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="false">
						<reportElement key="textField-9" x="430" y="15" width="50" height="15"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
							<font size="8"/>
						</textElement>
						<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication( "rotulo_perdas" )]]></textFieldExpression>
					</textField>
				</elementGroup>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-10" x="480" y="0" width="50" height="30"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font size="8"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication( "rotulo_estoque" ).toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-11" x="530" y="0" width="90" height="30"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font size="8"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication( "rotulo_assinatura_responsavel_tecnico_abv" ).toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-12" x="620" y="0" width="90" height="30"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font size="8"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication( "rotulo_observacoes" ).toUpperCase()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="40" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement key="textField-1" mode="Transparent" x="0" y="22" width="710" height="18" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="Arial" size="16" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication( "rotulo_livro_registro_especifico" ).toUpperCase()]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Page" isBlankWhenNull="false">
				<reportElement key="textField-24" x="602" y="0" width="108" height="18"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none"/>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication( "rotulo_ksi_folha" ) + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Page" isBlankWhenNull="false">
				<reportElement key="textField-28" x="0" y="0" width="710" height="18"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none"/>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication( "rotulo_empresa" ) + ": " + $F{descricaoUnidade}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="15" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement key="textField-14" x="65" y="0" width="265" height="15"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{codigoTipoDocumento}.equals( $P{tipoDocumentoDispensacaoMedicamento} ) ?
$F{nomePessoa} :
$F{numeroDocumento} == null ?
$F{descricaoTipoDocumento} :
$F{descricaoTipoDocumento} + "     NF: " + $F{numeroDocumento}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-18" x="330" y="0" width="50" height="15"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[RepositoryComponentDefault.NAO.equals( $F{flagInventario} ) ?
MovimentoEstoque.ENTRADA.equals( $F{flagTipoMovimento} ) ?
Coalesce.asDouble( $F{quantidade}, null ) :
null :
null]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-19" x="380" y="0" width="50" height="15"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[RepositoryComponentDefault.NAO.equals( $F{flagInventario} ) ?
MovimentoEstoque.SAIDA.equals( $F{flagTipoMovimento} ) ?
Coalesce.asDouble( $F{quantidade}, null ) :
null :
null]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-20" x="430" y="0" width="50" height="15"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[RepositoryComponentDefault.SIM.equals( $F{flagInventario} ) ?
Coalesce.asDouble( $F{quantidade}, null ) :
null]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-21" x="480" y="0" width="50" height="15"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{estoqueFisico}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-22" x="530" y="0" width="90" height="15"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{farmaceutico}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-23" x="620" y="0" width="90" height="15"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-25" x="0" y="0" width="65" height="15"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Data.formatar( $F{dataLancamento} )]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
