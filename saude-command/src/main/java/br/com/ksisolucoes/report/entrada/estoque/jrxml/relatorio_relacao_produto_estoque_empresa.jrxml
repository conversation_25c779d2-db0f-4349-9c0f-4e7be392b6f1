<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_produto_estoque_empresa" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="782" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="152576ff-7200-4144-aca3-ce23fc96aa91">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.771561000000003"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault.TipoPreco"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.report.ReportProperties"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="ParamEstoqueFisico" class="java.lang.String" isForPrompting="false"/>
	<parameter name="FlagEstoqueNaoConforme" class="java.lang.String" isForPrompting="false"/>
	<parameter name="ListarPreco" class="java.lang.String" isForPrompting="false"/>
	<parameter name="TotalizarEstoque" class="java.lang.String" isForPrompting="false"/>
	<parameter name="tipoEstoque" class="java.lang.String" isForPrompting="false"/>
	<parameter name="pontoReposicao" class="java.lang.String" isForPrompting="false"/>
	<parameter name="ExibirLote" class="java.lang.String"/>
	<parameter name="utilizaLocalizacaoEstoque" class="java.lang.Boolean"/>
	<parameter name="AGRUPAR_EMPRESA" class="java.lang.String"/>
	<parameter name="tipoPreco" class="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault.TipoPreco"/>
	<field name="produtoDescricao" class="java.lang.String">
		<fieldDescription><![CDATA[produtoDescricao]]></fieldDescription>
	</field>
	<field name="subGrupoCodigo" class="java.lang.Long">
		<fieldDescription><![CDATA[subGrupoCodigo]]></fieldDescription>
	</field>
	<field name="totalPreco" class="java.lang.Double">
		<fieldDescription><![CDATA[totalPreco]]></fieldDescription>
	</field>
	<field name="subGrupoDescricaoFormatado" class="java.lang.String">
		<fieldDescription><![CDATA[subGrupoDescricaoFormatado]]></fieldDescription>
	</field>
	<field name="produtoCodigo" class="java.lang.String">
		<fieldDescription><![CDATA[produtoCodigo]]></fieldDescription>
	</field>
	<field name="unidadeUnidade" class="java.lang.String">
		<fieldDescription><![CDATA[unidadeUnidade]]></fieldDescription>
	</field>
	<field name="grupoDescricao" class="java.lang.String">
		<fieldDescription><![CDATA[grupoDescricao]]></fieldDescription>
	</field>
	<field name="estoqueFisico" class="java.lang.Double">
		<fieldDescription><![CDATA[estoqueFisico]]></fieldDescription>
	</field>
	<field name="empresaCodigo" class="java.lang.String">
		<fieldDescription><![CDATA[empresaCodigo]]></fieldDescription>
	</field>
	<field name="empresaDescricao" class="java.lang.String">
		<fieldDescription><![CDATA[empresaDescricao]]></fieldDescription>
	</field>
	<field name="quantidadeIdeal" class="java.lang.Double">
		<fieldDescription><![CDATA[quantidadeIdeal]]></fieldDescription>
	</field>
	<field name="estoqueMinimo" class="java.lang.Double">
		<fieldDescription><![CDATA[estoqueMinimo]]></fieldDescription>
	</field>
	<field name="estoqueNaoConforme" class="java.lang.Double">
		<fieldDescription><![CDATA[estoqueNaoConforme]]></fieldDescription>
	</field>
	<field name="empresaDescricaoFormatado" class="java.lang.String">
		<fieldDescription><![CDATA[empresaDescricaoFormatado]]></fieldDescription>
	</field>
	<field name="grupoDescricaoFormatado" class="java.lang.String">
		<fieldDescription><![CDATA[grupoDescricaoFormatado]]></fieldDescription>
	</field>
	<field name="grupoCodigo" class="java.lang.Long">
		<fieldDescription><![CDATA[grupoCodigo]]></fieldDescription>
	</field>
	<field name="produtoDescricaoFormatado" class="java.lang.String">
		<fieldDescription><![CDATA[produtoDescricaoFormatado]]></fieldDescription>
	</field>
	<field name="quantidadeMultipla" class="java.lang.Double">
		<fieldDescription><![CDATA[quantidadeMultipla]]></fieldDescription>
	</field>
	<field name="preco" class="java.lang.Double">
		<fieldDescription><![CDATA[preco]]></fieldDescription>
	</field>
	<field name="subGrupoDescricao" class="java.lang.String">
		<fieldDescription><![CDATA[subGrupoDescricao]]></fieldDescription>
	</field>
	<field name="grupoEstoque" class="java.lang.String"/>
	<field name="dataValidade" class="java.util.Date"/>
	<field name="codigoDeposito" class="java.lang.Long"/>
	<field name="descricaoDeposito" class="java.lang.String"/>
	<field name="descricaoDepositoFormatado" class="java.lang.String"/>
	<field name="mascaraLocalizacaoEstrutura" class="java.lang.String"/>
	<field name="dataProximaValidade" class="java.lang.Boolean">
		<fieldDescription><![CDATA[dataProximaValidade]]></fieldDescription>
	</field>
	<field name="dataVencido" class="java.lang.Boolean">
		<fieldDescription><![CDATA[dataVencido]]></fieldDescription>
	</field>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="preco" class="java.lang.Double">
		<variableExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{AGRUPAR_EMPRESA})
? ($F{preco} == null ? 0.00 : $F{preco})
: ($F{estoqueFisico} == null ? 0.00 : ($F{totalPreco} / $F{estoqueFisico}))]]></variableExpression>
	</variable>
	<variable name="somaTotalPrecoEmpresa" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[$F{totalPreco}]]></variableExpression>
	</variable>
	<variable name="somaTotalPrecoGeral" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{totalPreco}]]></variableExpression>
	</variable>
	<variable name="somaEstoqueFisicoEmpresa" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueFisico}]]></variableExpression>
	</variable>
	<variable name="somaEstoqueFisicoGeral" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueFisico}]]></variableExpression>
	</variable>
	<variable name="somaEstoqueNaoConformeEmpresa" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueNaoConforme}]]></variableExpression>
	</variable>
	<variable name="somaEstoqueNaoConformeGeral" class="java.lang.Double" resetType="Group" resetGroup="GrupoPrincipal" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueNaoConforme}]]></variableExpression>
	</variable>
	<variable name="somaEstoqueMinimoGeral" class="java.lang.Double" resetType="Group" resetGroup="GrupoPrincipal" calculation="Sum">
		<variableExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ExibirLote})
?
$V{estoqueMinimo}
:
$F{estoqueMinimo}]]></variableExpression>
	</variable>
	<variable name="somaEstoqueMinimoEmpresa" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ExibirLote})
?
$V{estoqueMinimo}
:
$F{estoqueMinimo}]]></variableExpression>
	</variable>
	<variable name="somaTotalQtdMultEmpresa" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidadeMultipla}]]></variableExpression>
	</variable>
	<variable name="somaTotalQtdMultGeral" class="java.lang.Double" resetType="Group" resetGroup="GrupoPrincipal" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidadeMultipla}]]></variableExpression>
	</variable>
	<variable name="somaTotalQtdIdealGeral" class="java.lang.Double" resetType="Group" resetGroup="GrupoPrincipal" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidadeIdeal}]]></variableExpression>
	</variable>
	<variable name="somaTotalQtdIdealEmpresa" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidadeIdeal}]]></variableExpression>
	</variable>
	<variable name="estoqueFisico" class="java.lang.Double" resetType="Group" resetGroup="Produto" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueFisico} == null ? 0.00 : $F{estoqueFisico}]]></variableExpression>
	</variable>
	<variable name="estoqueMinimo" class="java.lang.Double" resetType="Group" resetGroup="Produto" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueMinimo} == null ? 0.00 : $F{estoqueMinimo}]]></variableExpression>
	</variable>
	<variable name="somaTotalPrecoDeposito" class="java.lang.Double" resetType="Group" resetGroup="Deposito" calculation="Sum">
		<variableExpression><![CDATA[$F{totalPreco}]]></variableExpression>
	</variable>
	<variable name="totalPrecoProduto" class="java.lang.Double" resetType="Group" resetGroup="Produto" calculation="Sum">
		<variableExpression><![CDATA[$F{totalPreco}]]></variableExpression>
	</variable>
	<group name="GrupoPrincipal">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band splitType="Stretch"/>
		</groupHeader>
		<groupFooter>
			<band height="13" splitType="Stretch">
				<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-63" mode="Transparent" x="350" y="1" width="43" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="937a1af4-6873-4b68-9cd5-bdb869dabc79">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{somaEstoqueFisicoGeral}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-65" mode="Transparent" x="231" y="1" width="117" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="c31d7436-28b2-4ced-a866-4b37d7f9eec5">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication( "rotulo_total_geral" ) + " =>"]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-5" mode="Opaque" x="268" y="1" width="43" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="c7a7d1b5-2391-4a65-91e7-d91102f8d091">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} )]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-68" mode="Transparent" x="394" y="1" width="43" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="ef925eca-ab2b-41e3-b19d-a0261941df42">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} )&&
RepositoryComponentDefault.NAO.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{somaEstoqueMinimoGeral}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-7" mode="Opaque" x="394" y="1" width="43" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="*************-4b37-9cca-e41183627f77">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} )]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<line>
					<reportElement key="line-9" mode="Opaque" x="312" y="1" width="41" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="ff61a2fb-9c24-4424-8f84-9623357ee784">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} )]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<line>
					<reportElement key="line-12" mode="Opaque" x="353" y="1" width="40" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="7901cceb-4e0b-41aa-8d4f-ae0886d2496d">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} )]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-68" mode="Transparent" x="735" y="2" width="43" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="cec4e937-a15a-42f7-a86a-e3cfba3a0c44">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{somaTotalPrecoGeral}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-65" mode="Transparent" x="612" y="2" width="117" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="022d7846-2ad8-43ef-a855-8766cb791aa2">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{TotalizarEstoque})&&
RepositoryComponentDefault.SIM.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication( "rotulo_total_geral" ) + " =>"]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-7" mode="Opaque" x="735" y="2" width="43" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="2e5b2232-574f-4214-b106-108e6e169e94">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<line>
					<reportElement key="line-9" mode="Opaque" x="653" y="2" width="41" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="b25d46ea-8b71-4330-876f-98bed838cfc1">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<line>
					<reportElement key="line-12" mode="Opaque" x="694" y="2" width="40" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="118a48c6-6835-4f54-a82a-31796f9a54de">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="GrupoEmpresa" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{empresaCodigo}]]></groupExpression>
		<groupHeader>
			<band height="16" splitType="Stretch">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{AGRUPAR_EMPRESA})]]></printWhenExpression>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-43" mode="Opaque" x="0" y="1" width="782" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="9f6a968b-fd4d-4cbf-9c6a-8fc8da04b408"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*empresa*/
$F{empresaCodigo}!=null
?
    $V{BUNDLE}.getStringApplication("rotulo_empresa") + ":" + " " + $F{empresaDescricao} + " (" + $F{empresaCodigo}.trim() + ") "
:
    $V{BUNDLE}.getStringApplication("rotulo_empresa") + ":" + $V{BUNDLE}.getStringApplication("rotulo_sem_cadastro")]]></textFieldExpression>
				</textField>
				<rectangle radius="10">
					<reportElement mode="Transparent" x="0" y="1" width="782" height="15" uuid="c2b9b0dd-bdfa-4756-80c7-e27c796d6032"/>
				</rectangle>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="13" splitType="Stretch">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{AGRUPAR_EMPRESA})]]></printWhenExpression>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-62" mode="Transparent" x="350" y="3" width="43" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="ce16c0bb-02ca-40d3-a0b6-095aa3162934">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{somaEstoqueFisicoEmpresa}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-64" mode="Transparent" x="231" y="3" width="117" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="1ed3297c-1a4c-4630-91d0-4bf48d35a656">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication( "rotulo_total_por_empresa" ) + " =>"]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-1" mode="Opaque" x="268" y="2" width="43" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="40f5745e-b369-4c46-afb1-f6ede5aa614b">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} )]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-69" mode="Transparent" x="394" y="3" width="43" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="139fc68c-5646-4dc4-afbe-bf8a028a9b02">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} )&&
RepositoryComponentDefault.NAO.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{somaEstoqueMinimoEmpresa}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-8" mode="Opaque" x="394" y="2" width="43" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="efbacc6a-dfe1-4806-b998-6f815c4dc03a">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} )]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<line>
					<reportElement key="line-10" mode="Opaque" x="312" y="2" width="41" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="4bb962a0-1931-478c-ad10-f05532ab35de">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} )]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<line>
					<reportElement key="line-11" mode="Opaque" x="353" y="2" width="40" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="02ff442a-345f-4168-85b2-696662c07614">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} )]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<line>
					<reportElement key="line-10" mode="Opaque" x="653" y="2" width="41" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="fc6f6438-7816-4ff9-aed1-523215596e57">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<line>
					<reportElement key="line-11" mode="Opaque" x="694" y="2" width="40" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="2b31fb39-712e-4add-8302-5fa8734f413b">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<line>
					<reportElement key="line-8" mode="Opaque" x="735" y="2" width="43" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="7e9311a9-2320-4707-8d62-3753676f3c3d">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-69" mode="Transparent" x="735" y="3" width="43" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="d0924b0c-ced0-406d-99b3-43d5e7d00371">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{somaTotalPrecoEmpresa}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-64" mode="Transparent" x="612" y="3" width="117" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="5eb6f933-8605-46b1-aeb3-7a939c3c443e">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{TotalizarEstoque})&&
RepositoryComponentDefault.SIM.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication( "rotulo_total_por_empresa" ) + " =>"]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="Deposito" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{codigoDeposito}]]></groupExpression>
		<groupHeader>
			<band height="23">
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-5" mode="Transparent" x="156" y="13" width="51" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="81937823-dc0c-4606-805f-482656d28b61">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ExibirLote}) && $P{utilizaLocalizacaoEstoque}]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*localização*/$V{BUNDLE}.getStringApplication("rotulo_localizacao")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-54" mode="Transparent" x="733" y="13" width="46" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="da66bb10-5d97-414a-8ad4-4217c29dc0cf">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{ListarPreco} )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*TotalPreco*/$V{BUNDLE}.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-50" mode="Transparent" x="441" y="13" width="126" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="10945487-6ee9-4684-afd7-27a36b9a79a1"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*grupo*/$V{BUNDLE}.getStringApplication("rotulo_grupo")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-57" mode="Transparent" x="688" y="13" width="44" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="be12db31-8aa6-4ae7-99bc-b4cebf137178">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{ListarPreco} )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*preco medio/custo*/
$P{tipoPreco}.getDescricaoAbreviada()]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-5" mode="Transparent" x="1" y="13" width="142" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="81937823-dc0c-4606-805f-482656d28b61"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*produto*/$V{BUNDLE}.getStringApplication("rotulo_produto")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-7" mode="Transparent" x="211" y="13" width="57" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="17056c6b-484f-49f0-9394-0a2b33766a31">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ExibirLote})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*grupoEstoque*/$V{BUNDLE}.getStringApplication("rotulo_lote")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-6" mode="Transparent" x="336" y="13" width="12" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="2e1d6c30-ee9e-4b7a-ad66-57e022d564c1"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*unidade*/$V{BUNDLE}.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-51" mode="Transparent" x="570" y="13" width="112" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="cf70e64b-11ae-4e38-ae73-1bbb56f75a53"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*subgrupo*/$V{BUNDLE}.getStringApplication("rotulo_subgrupo")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-7" mode="Transparent" x="269" y="13" width="60" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="09161928-ee64-4c0a-a569-823eb7678f20">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ExibirLote})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*dataValidade*/$V{BUNDLE}.getStringApplication("rotulo_data_validade_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-71" mode="Transparent" x="394" y="13" width="43" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="7dbb7f34-fa18-46d0-ae8b-11bf6ffaf2f2">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*esto.Minimo*/$V{BUNDLE}.getStringApplication("rotulo_estoque_minimo_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-7" mode="Transparent" x="350" y="13" width="43" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="c7e84e18-126b-429b-ab43-9cac38643aeb"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*estoque fisico*/$P{tipoEstoque}/*$V{BUNDLE}.getStringApplication("rotulo_estoque_fisico_abv")*/]]></textFieldExpression>
				</textField>
				<line>
					<reportElement mode="Transparent" x="0" y="22" width="782" height="1" uuid="af88aff3-1f10-4b38-8fdd-16d5da327f8b"/>
				</line>
				<textField evaluationTime="Group" evaluationGroup="Deposito" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-43" mode="Transparent" x="0" y="0" width="782" height="14" forecolor="#000000" backcolor="#CCCCCC" uuid="516e78d4-67ed-458d-8bb8-94c0f7a1d2d6"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_deposito")+": "+$F{descricaoDepositoFormatado}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="13" width="782" height="1" uuid="c874c955-27a2-42d8-badf-bc5c07e37a2a"/>
				</line>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="13">
				<line>
					<reportElement key="line-11" mode="Opaque" x="694" y="1" width="40" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="704faa6f-0010-44b0-961e-a0450d852dfb">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-64" mode="Transparent" x="612" y="2" width="117" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="5a283659-f274-4796-9993-561e742bf533">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication( "rotulo_total_por_deposito" ) + " =>"]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-10" mode="Opaque" x="653" y="1" width="41" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="39a09d7e-823e-439f-9372-9fb6490db8e6">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<textField evaluationTime="Group" evaluationGroup="Deposito" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-69" mode="Transparent" x="735" y="2" width="43" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="26390a3f-401d-444c-9e98-049d8ad4901b">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{somaTotalPrecoDeposito}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-8" mode="Opaque" x="735" y="1" width="43" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="aba802a3-6fb5-4596-849e-13f567741ae8">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="Produto">
		<groupExpression><![CDATA[$F{produtoCodigo}]]></groupExpression>
		<groupFooter>
			<band height="10">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{ExibirLote})]]></printWhenExpression>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="1" y="0" width="208" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="8a961c0c-7d84-4f9b-9527-24180bcc698f"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{produtoDescricaoFormatado}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-46" mode="Transparent" x="336" y="0" width="12" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="cd1a4de0-1438-4816-9752-b8ba6abd5991"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{unidadeUnidade}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-47" mode="Transparent" x="350" y="0" width="43" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="c1f2d530-d09c-438a-b03d-3387df4e9dc6"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{estoqueFisico}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-48" mode="Transparent" x="441" y="0" width="126" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="f1e7beb9-6149-4d9c-9bc4-0f167b5a2acd"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{grupoDescricaoFormatado}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-49" mode="Transparent" x="570" y="0" width="112" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="10cc19ac-7470-42fc-bcc0-73d987d8742c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{subGrupoDescricaoFormatado}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-55" mode="Transparent" x="733" y="0" width="46" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="5b9d1b75-fbd2-4366-8c82-6bc3ab06d042">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{ListarPreco} )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalPrecoProduto}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.0000;-#,##0.0000" isBlankWhenNull="true">
					<reportElement key="textField-56" mode="Transparent" x="688" y="0" width="44" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="c8d0689b-b91f-490d-845a-eb10aa8a2eff">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{ListarPreco} )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{preco}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-70" mode="Transparent" x="394" y="0" width="43" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="17a38d57-8532-43ff-ae01-ff2cc36e9f9e">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ExibirLote})
?
$V{estoqueMinimo}
:
$F{estoqueMinimo}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="10" splitType="Stretch">
			<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ExibirLote}) && !$P{utilizaLocalizacaoEstoque}]]></printWhenExpression>
			<rectangle>
				<reportElement mode="Opaque" x="1" y="0" width="779" height="10" isPrintWhenDetailOverflows="true" forecolor="#FEFEFE" backcolor="#C45958" uuid="cbf29ab2-79ce-4090-aeb5-e18fb0bd2415">
					<printWhenExpression><![CDATA[$F{dataVencido}]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Opaque" x="1" y="0" width="779" height="10" isPrintWhenDetailOverflows="true" forecolor="#FEFEFE" backcolor="#FDED8D" uuid="dcb9605f-2259-4933-9d48-dfcab7c93bca">
					<printWhenExpression><![CDATA[$F{dataProximaValidade}]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="1" y="0" width="208" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="73fcae4b-43e8-4a25-ba3f-5a9d62aaa9aa"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produtoDescricaoFormatado}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-46" mode="Transparent" x="336" y="0" width="12" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="ae1c375c-41eb-43b6-a94f-163b67e08337"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unidadeUnidade}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-47" mode="Transparent" x="350" y="0" width="43" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="a1c636a6-3618-4890-92e4-71ace2840736"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{estoqueFisico} == null ? 0.00 : $F{estoqueFisico}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-48" mode="Transparent" x="441" y="0" width="126" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="0d08038c-eb09-4f45-a820-328869fdfd0a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{grupoDescricaoFormatado}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-49" mode="Transparent" x="570" y="0" width="112" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="a371f9c4-6832-4f8f-9ca7-c898375e6a98"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{subGrupoDescricaoFormatado}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-55" mode="Transparent" x="733" y="0" width="46" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="c86f82fa-fcb6-4cd9-bf8c-9ce2d9b47353">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{ListarPreco} )]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{totalPreco}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.0000;-#,##0.0000" isBlankWhenNull="true">
				<reportElement key="textField-56" mode="Transparent" x="688" y="0" width="44" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="b07561a2-28bd-41aa-ad67-c0bc5dfb68f4">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{ListarPreco} )]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{preco}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-70" mode="Transparent" x="394" y="0" width="43" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="938556b6-0dc6-43dd-a842-a21349090339">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{ListarPreco})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{estoqueMinimo} == null ? 0.00 : $F{estoqueMinimo}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-46" mode="Transparent" x="211" y="0" width="57" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="0a47e7b0-ea6a-4d77-bda4-7b7825e11039"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{grupoEstoque}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-46" mode="Transparent" x="270" y="0" width="60" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="86b7ed94-60a1-442b-a3da-94e8efcdec3d"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.formatar($F{dataValidade})]]></textFieldExpression>
			</textField>
		</band>
		<band height="10">
			<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ExibirLote}) && $P{utilizaLocalizacaoEstoque}]]></printWhenExpression>
			<rectangle>
				<reportElement mode="Opaque" x="0" y="0" width="779" height="10" isPrintWhenDetailOverflows="true" forecolor="#FEFEFE" backcolor="#C45958" uuid="678417aa-7568-4595-abf8-d1414f42ab73">
					<printWhenExpression><![CDATA[$F{dataVencido}]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Opaque" x="0" y="0" width="779" height="10" isPrintWhenDetailOverflows="true" forecolor="#FEFEFE" backcolor="#FDED8D" uuid="93f88e37-4021-4ff4-91a8-29dfaa743cc3">
					<printWhenExpression><![CDATA[$F{dataProximaValidade}]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-45" positionType="Float" mode="Transparent" x="1" y="0" width="142" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="73fcae4b-43e8-4a25-ba3f-5a9d62aaa9aa"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produtoDescricaoFormatado}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-46" mode="Transparent" x="336" y="0" width="12" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="ae1c375c-41eb-43b6-a94f-163b67e08337"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unidadeUnidade}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-47" mode="Transparent" x="350" y="0" width="43" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="a1c636a6-3618-4890-92e4-71ace2840736"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{estoqueFisico} == null ? 0.00 : $F{estoqueFisico}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-48" mode="Transparent" x="441" y="0" width="126" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="0d08038c-eb09-4f45-a820-328869fdfd0a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{grupoDescricaoFormatado}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-49" mode="Transparent" x="570" y="0" width="112" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="a371f9c4-6832-4f8f-9ca7-c898375e6a98"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{subGrupoDescricaoFormatado}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-55" mode="Transparent" x="733" y="0" width="46" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="c86f82fa-fcb6-4cd9-bf8c-9ce2d9b47353">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{ListarPreco} )]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{totalPreco}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.0000;-#,##0.0000" isBlankWhenNull="true">
				<reportElement key="textField-56" mode="Transparent" x="688" y="0" width="44" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="b07561a2-28bd-41aa-ad67-c0bc5dfb68f4">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{ListarPreco} )]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{preco}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-70" mode="Transparent" x="394" y="0" width="43" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="938556b6-0dc6-43dd-a842-a21349090339">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{ListarPreco})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{estoqueMinimo} == null ? 0.00 : $F{estoqueMinimo}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-46" mode="Transparent" x="211" y="0" width="57" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="0a47e7b0-ea6a-4d77-bda4-7b7825e11039"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{grupoEstoque}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-46" mode="Transparent" x="270" y="0" width="60" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="86b7ed94-60a1-442b-a3da-94e8efcdec3d"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.formatar($F{dataValidade})]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-46" mode="Transparent" x="156" y="0" width="51" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="0a47e7b0-ea6a-4d77-bda4-7b7825e11039">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ExibirLote}) && $P{utilizaLocalizacaoEstoque}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mascaraLocalizacaoEstrutura}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
