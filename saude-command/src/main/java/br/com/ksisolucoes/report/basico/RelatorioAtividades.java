/*
 * Created on 28/07/2004
 *
 * To change the template for this generated file go to
 * Window - Preferences - Java - Code Generation - Code and Comments
 */
package br.com.ksisolucoes.report.basico;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomSorter;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.util.Collection;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.vo.basico.Atividade;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Generation - Code and Comments
 */
public class RelatorioAtividades extends AbstractReport{
    
 
    @Override
    public Collection getCollection() throws ValidacaoException  {
        return LoadManager.getInstance(Atividade.class)
                .setLazyMode(true)
                .addSorter(new QueryCustomSorter(Atividade.PROP_DESCRICAO))
                .start().getList();
    }

    public String getTitulo() {
        return br.com.ksisolucoes.util.Bundle.getStringApplication("rotulo_relatorio_atividade");
    }

    public String getXML() {
        return "/br/com/ksisolucoes/report/basico/jrxml/atividades.jrxml";
    }
}
