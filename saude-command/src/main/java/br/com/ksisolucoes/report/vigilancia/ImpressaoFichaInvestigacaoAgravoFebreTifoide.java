/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.report.vigilancia.query.QueryFichaBotulismo;
import br.com.celk.report.vigilancia.query.QueryFichaFebreTifoide;
import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaInvestigacaoAgravoDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;

import java.util.LinkedHashMap;

public class ImpressaoFichaInvestigacaoAgravoFebreTifoide extends AbstractReport<FichaInvestigacaoAgravoDTOParam> {

    private QueryFichaFebreTifoide query;

    public ImpressaoFichaInvestigacaoAgravoFebreTifoide(FichaInvestigacaoAgravoDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        if(query == null){
            query = new QueryFichaFebreTifoide();
        }
        return query;
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> columnsMap = new LinkedHashMap<>(((QueryFichaFebreTifoide)getQuery()).getMapeamentoPlanilhaBase());


        columnsMap.put("dataInvestigacao", "_31_data_investigacao");
        columnsMap.put("ocupacaoCbo", "_31_ocupacao");

        columnsMap.put("contatoCompativel", "_33_contato_compativel");
        columnsMap.put("contatoCompativelOutros", "_33_contato_compativel_outros");
        columnsMap.put("nomeContato", "_34_nome_contato");
        columnsMap.put("telefoneContato", "_35_contato_telefone_contato");
        columnsMap.put("sugestaoVinculo", "_36_sujestao_vinculo");
        columnsMap.put("sugestaoVinculoOutros", "_36_sujestao_vinculo_outros");

        columnsMap.put("sinaisSintomasAssintomatico", "_37_sinais_sintomas_assintomatico");
        columnsMap.put("sinaisSintomasEsplenomegalia", "_37_sinais_sintomas_esplenomegalia");
        columnsMap.put("sinaisSintomasFebre", "_37_sinais_sintomas_febre");
        columnsMap.put("sinaisSintomasRoseolaTifica", "_37_sinais_sintomas_roseola_tifica");
        columnsMap.put("sinaisSintomasCefaleia", "_37_sinais_sintomas_cefaleia");
        columnsMap.put("sinaisSintomasNauseas", "_37_sinais_sintomas_nauseas");
        columnsMap.put("sinaisSintomasDiarreia", "_37_sinais_sintomas_diarreia");
        columnsMap.put("sinaisSintomasVomitos", "_37_sinais_sintomas_vomitos");
        columnsMap.put("sinaisSintomasConstipacao", "_37_sinais_sintomas_constipacao");
        columnsMap.put("sinaisSintomasDorAbdominal", "_37_sinais_sintomas_dor_abdominal");
        columnsMap.put("sinaisSintomasAstenia", "_37_sinais_sintomas_astenia");
        columnsMap.put("sinaisSintomasDissociacaoPulsoTemperatura", "_37_sinais_sintomas_dissociacao");
        columnsMap.put("sinaisSintomasTosse", "_37_sinais_sintomas_tosse");

        columnsMap.put("complicacoesEnterorragia", "_38_complicacoes_enterorragia");
        columnsMap.put("complicacoesPerfuracoesInstestinais", "_38_complicacoes_perfuracoes");
        columnsMap.put("complicacoesOutros", "_38_complicacoes_outros");

        columnsMap.put("tipoAtendimento", "_39_tipo_atendimento");
        columnsMap.put("dataAtendimento", "_40_data_atendimento");
        columnsMap.put("estadoAtendimento", "_41_estado");
        columnsMap.put("cidade", "_42_cidade");
        columnsMap.put("ibgeAtendimento", "_42_ibge");
        columnsMap.put("unidadeSaudeAtendimento", "_43_hospital");
        columnsMap.put("unidadeSaudeAtendimentoCnes", "_43_cnes");

        columnsMap.put("materialColetadoSangue", "_44_material_coletado_sangue");
        columnsMap.put("materialColetadoFezes", "_44_material_coletado_fezes");
        columnsMap.put("materialColetadoUrina", "_44_material_coletado_urina");

        columnsMap.put("usoAntibiotico", "_45_uso_antibiotico");

        columnsMap.put("data1ColetaHemocultura", "_46_data_1_coleta_hemocultura");
        columnsMap.put("resultado1AmostraHemocultura", "_46_resultado_1_coleta_hemocultura");
        columnsMap.put("resultado1AmostraHemoculturaOutros", "_46_resultado_1_coleta_hemocultura_outros");
        columnsMap.put("data2ColetaHemocultura", "_46_data_2_coleta_hemocultura");
        columnsMap.put("resultado2AmostraHemocultura", "_46_resultado_2_coleta_hemocultura");
        columnsMap.put("resultado2AmostraHemoculturaOutros", "_46_resultado_2_coleta_hemocultura_outros");
        columnsMap.put("data3ColetaHemocultura", "_46_data_3_coleta_hemocultura");
        columnsMap.put("resultado3AmostraHemocultura", "_46_resultado_3_coleta_hemocultura");
        columnsMap.put("resultado3AmostraHemoculturaOutros", "_46_resultado_3_coleta_hemocultura_outros");

        columnsMap.put("data1ColetaCoprocultura", "_46_data_1_coleta_coprocultura");
        columnsMap.put("resultado1AmostraCoprocultura", "_46_resultado_1_coleta_coprocultura");
        columnsMap.put("resultado1AmostraCoproculturaOutros", "_46_resultado_1_coleta_coprocultura_outros");
        columnsMap.put("data2ColetaCoprocultura", "_46_data_2_coleta_coprocultura");
        columnsMap.put("resultado2AmostraCoprocultura", "_46_resultado_2_coleta_coprocultura");
        columnsMap.put("resultado2AmostraCoproculturaOutros", "_46_resultado_2_coleta_coprocultura_outros");
        columnsMap.put("data3ColetaCoprocultura", "_46_data_3_coleta_coprocultura");
        columnsMap.put("resultado3AmostraCoprocultura", "_46_resultado_3_coleta_coprocultura");
        columnsMap.put("resultado3AmostraCoproculturaOutros", "_46_resultado_3_coleta_coprocultura_outros");

        columnsMap.put("data1ColetaUrocultura", "_46_data_1_coleta_urocultura");
        columnsMap.put("resultado1AmostraUrocultura", "_46_resultado_1_coleta_urocultura");
        columnsMap.put("resultado1AmostraUroculturaOutros", "_46_resultado_1_coleta_urocultura_outros");
        columnsMap.put("data2ColetaUrocultura", "_46_data_2_coleta_urocultura");
        columnsMap.put("resultado2AmostraUrocultura", "_46_resultado_2_coleta_urocultura");
        columnsMap.put("resultado2AmostraUroculturaOutros", "_46_resultado_2_coleta_urocultura_outros");
        columnsMap.put("data3ColetaUrocultura", "_46_data_3_coleta_urocultura");
        columnsMap.put("resultado3AmostraUrocultura", "_46_resultado_3_coleta_urocultura");
        columnsMap.put("resultado3AmostraUroculturaOutros", "_46_resultado_3_coleta_urocultura_outros");

        columnsMap.put("data1ColetaOutros", "_46_data_1_coleta_outros");
        columnsMap.put("resultado1AmostraOutros", "_46_resultado_1_coleta_outros");
        columnsMap.put("resultado1AmostraOutrosOutros", "_46_resultado_1_coleta_outros_outros");
        columnsMap.put("data2ColetaOutros", "_46_data_2_coleta_outros");
        columnsMap.put("resultado2AmostraOutros", "_46_resultado_2_coleta_outros");
        columnsMap.put("resultado2AmostraOutrosOutros", "_46_resultado_2_coleta_outros_outros");
        columnsMap.put("data3ColetaOutros", "_46_data_3_coleta_outros");
        columnsMap.put("resultado3AmostraOutros", "_46_resultado_3_coleta_outros");
        columnsMap.put("resultado3AmostraOutrosOutros", "_46_resultado_3_coleta_outros_outros");

        columnsMap.put("antibioticosTratamentoCloranfenicol", "_47_antibioticos_tratamento_cloranfenicol");
        columnsMap.put("antibioticosTratamentoQuinilona", "_47_antibioticos_tratamento_quinilona");
        columnsMap.put("antibioticosTratamentoAmpicilina", "_47_antibioticos_tratamento_ampicilina");
        columnsMap.put("antibioticosTratamentoSulfametoxanol", "_47_antibioticos_tratamento_sulfametoxanol");
        columnsMap.put("antibioticosTratamentoOutro", "_47_antibioticos_tratamento_outro");

        columnsMap.put("tempoUso", "_47_tempo_uso");

        columnsMap.put("classificacaoFinal", "_48_classificacao_final");
        columnsMap.put("criterioConfirmacaoDescarte", "_49_criterio_confirmacao_descarte");

        columnsMap.put("casoAutoctone", "_50_caso_autoctone");
        columnsMap.put("estado", "_51_estado");
        columnsMap.put("paisLocalInfeccao", "_52_pais");
        columnsMap.put("cidadeLocalInfeccao", "_53_cidade");
        columnsMap.put("ibge", "_53_ibge");
        columnsMap.put("distritoLocalInfeccao", "_54_distrito");
        columnsMap.put("bairroLocalInfeccao", "_55_bairro");

        columnsMap.put("doencaRelacionadaTrabalho", "_56_doenca_relacionada");

        columnsMap.put("evolucaoCaso", "_57_evolucao_caso");
        columnsMap.put("dataObito", "_58_data_obito");

        columnsMap.put("observacao", "_observacao");

        return columnsMap;
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/pdf/ficha_investigacao_febre_tifoide.pdf";
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_febre_tifoide");
    }

}
