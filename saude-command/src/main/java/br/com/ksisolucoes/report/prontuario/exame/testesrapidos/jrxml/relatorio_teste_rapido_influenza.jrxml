<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_teste_rapido_influenza" pageWidth="556" pageHeight="631" columnWidth="556" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="812a5b70-40d5-4117-bc4f-81ca6e8acef4">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.50262960180317"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.FormasUsoHelper"/>
	<import value="br.com.celk.util.DataUtil"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus">
		<fieldDescription><![CDATA[usuarioCadsus]]></fieldDescription>
	</field>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional">
		<fieldDescription><![CDATA[profissional]]></fieldDescription>
	</field>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="atendimento" class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"/>
	<field name="enderecoUsuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus"/>
	<field name="testeRapidoConjunto" class="br.com.ksisolucoes.vo.prontuario.basico.TesteRapidoConjunto">
		<fieldDescription><![CDATA[testeRapidoConjunto]]></fieldDescription>
	</field>
	<field name="testeRapidoRealizado" class="br.com.ksisolucoes.vo.prontuario.basico.TesteRapidoRealizado">
		<fieldDescription><![CDATA[testeRapidoRealizado]]></fieldDescription>
	</field>
	<group name="Exame" isStartNewPage="true">
		<groupHeader>
			<band height="134">
				<textField isBlankWhenNull="true">
					<reportElement key="textField-10" positionType="Float" x="181" y="89" width="96" height="12" uuid="b0f68a33-29f8-458f-8e72-d561ff3fc2c5">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getSexoFormatado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-8" x="2" y="89" width="143" height="12" uuid="78186ffb-8f76-4b97-89a8-300d1b379d81">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[Data.formatar($F{usuarioCadsus}.getDataNascimento())]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="349" y="74" width="1" height="29" uuid="e57a2bc0-a544-43bf-a524-bb95eed0e8d2"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-9" mode="Opaque" x="-1" y="0" width="557" height="15" backcolor="#F6F4F2" uuid="541d9f2e-4a88-4ae1-a4ea-96489b8b6c04"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_paciente").toUpperCase()]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="555" y="0" width="1" height="134" uuid="4d1dc53e-3bde-4bcf-a1a9-103c9ca97f00"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="2" y="16" width="452" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_numero_de_registro").toUpperCase()]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="-1" y="0" width="1" height="134" uuid="e57a2bc0-a544-43bf-a524-bb95eed0e8d2"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="-1" y="44" width="557" height="1" uuid="9228121a-ceb0-4907-8533-7786499316cb"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="2" y="105" width="276" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_endereco").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="2" y="76" width="143" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_de_nascimento").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="181" y="76" width="96" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_sexo").toUpperCase()]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="178" y="75" width="1" height="29" uuid="e57a2bc0-a544-43bf-a524-bb95eed0e8d2"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="-1" y="74" width="557" height="1" uuid="9228121a-ceb0-4907-8533-7786499316cb"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="-1" y="103" width="557" height="1" uuid="9228121a-ceb0-4907-8533-7786499316cb"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="2" y="46" width="551" height="12" uuid="23e48269-d179-4889-8b11-f84dcfc4dfa8">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome_paciente").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="2" y="60" width="551" height="12" uuid="011f547a-c19c-4023-a41c-856ccdca0472"/>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNomeSocial()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="352" y="76" width="152" height="12" uuid="01c964f0-ca2e-4cbd-9f8d-802be712bcbe">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_coleta_amostra").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="2" y="119" width="551" height="12" uuid="eb227d93-832e-463b-9494-50922da85510"/>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{enderecoUsuarioCadsus}.getEnderecoFormatadoComCidade()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="352" y="89" width="98" height="12" uuid="0c5f19f8-aaf2-4d11-a6a1-aaddd8480d2b"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{atendimento}.getDataAtendimentoFormatada()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<title>
		<band height="29" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="1" y="1" width="554" height="15" uuid="d62257e9-7896-462e-a266-23053fdb9cfc">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_modelo_laudo_resultados_testes_rapidos_para_influenza").toUpperCase()]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="81" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" mode="Opaque" x="-1" y="5" width="557" height="15" backcolor="#F6F4F2" uuid="541d9f2e-4a88-4ae1-a4ea-96489b8b6c04"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_instituicao").toUpperCase()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="-1" y="5" width="1" height="76" uuid="e57a2bc0-a544-43bf-a524-bb95eed0e8d2"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="555" y="5" width="1" height="76" uuid="4d1dc53e-3bde-4bcf-a1a9-103c9ca97f00"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="2" y="21" width="427" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome").toUpperCase()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="429" y="20" width="1" height="30" uuid="e57a2bc0-a544-43bf-a524-bb95eed0e8d2"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="432" y="21" width="121" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cnes").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="2" y="35" width="427" height="12" uuid="4b749928-4e7f-4aee-a05c-7780d31f4dc5"/>
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresa}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="432" y="35" width="121" height="12" uuid="0b009b16-cbae-40f3-b354-b91e6c85c0cf"/>
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresa}.getCnes()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="-1" y="50" width="557" height="1" uuid="a29aeb31-e1b4-4fa6-b0a9-ba2899d8cfa7"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="2" y="52" width="427" height="12" uuid="d7d23a6f-9a67-416d-9f04-9073ce5eb757">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_endereco").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="2" y="66" width="427" height="12" uuid="d73c5a8b-206d-4403-b923-65b64a1fa947"/>
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresa}.getEnderecoBairroFormatado()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="429" y="51" width="1" height="30" uuid="9f62c659-a711-4d1a-af52-9f50d87b03c4"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="432" y="52" width="121" height="12" uuid="4cca0728-8550-4464-9fae-15f6640357d5">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_telefone").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="432" y="66" width="121" height="12" uuid="134a6cc5-c0a6-4f93-86cc-b0d9417c3a43"/>
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresa}.getTelefoneFormatado()]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="387" splitType="Stretch">
			<textField>
				<reportElement x="6" y="18" width="126" height="12" uuid="5e97fe9a-538c-41ac-8325-d6fed07d1b58"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_teste_rapido_influenza")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" mode="Opaque" x="-1" y="0" width="557" height="15" backcolor="#F6F4F2" uuid="541d9f2e-4a88-4ae1-a4ea-96489b8b6c04"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_modelo_laudo_resultados_testes_rapidos_para_influenza").toUpperCase()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="555" y="0" width="1" height="386" uuid="4d1dc53e-3bde-4bcf-a1a9-103c9ca97f00"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="-1" y="0" width="1" height="386" uuid="4d1dc53e-3bde-4bcf-a1a9-103c9ca97f00"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="-1" y="386" width="557" height="1" uuid="9228121a-ceb0-4907-8533-7786499316cb"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField>
				<reportElement x="133" y="18" width="276" height="11" uuid="353382f3-91ce-410d-bc0f-a0d8976fcf58"/>
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{testeRapidoRealizado}.getTipoTesteRapido().getDescricao()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="430" y="37" width="122" height="12" uuid="87318eb0-40c7-4233-bef6-949a61c3f09d"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_valor_referencia")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="431" y="51" width="122" height="12" uuid="abf31b02-ac96-4dff-88ab-2684a8dd8ab6"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nao_reagente")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="6" y="51" width="126" height="12" uuid="a48a0549-e54b-444d-8497-8f2d93dda1aa"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome_conjunto_diagnostico")+": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="133" y="51" width="126" height="12" uuid="992a019c-48d2-40c2-91b7-2fab84841787"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{testeRapidoConjunto}.getNomeConjunto()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="109" y="77" width="23" height="12" uuid="a70d24c1-06e7-4838-8336-d87da3cbc4a6"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_lote")+": "]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="101" y="90" width="31" height="12" uuid="026aec25-2efa-4fde-a45e-96e862e7a30e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_metodo")+": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="133" y="78" width="126" height="12" uuid="7a319702-e103-4b8c-b890-67a99470c795"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{testeRapidoRealizado}.getLote() != null ? $F{testeRapidoRealizado}.getLote() : ""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="133" y="91" width="126" height="12" uuid="ffc5a92a-a07a-419c-b2ac-6c9f749fbda6"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{testeRapidoConjunto}.getMetodo()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="6" y="116" width="83" height="12" uuid="dc2a038c-6527-42a7-b5fa-9b6a52246795"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_resultado_teste")+": "]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="6" y="139" width="543" height="1" uuid="456d4931-d7b3-45fe-847f-034f431564ce"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<textField>
				<reportElement x="6" y="149" width="158" height="12" uuid="c856d7de-2727-4da8-8563-2890b911c9ee"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_interpretacao_resultado").toUpperCase()+": "]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="275" width="555" height="1" uuid="d418c85a-c227-4f74-93c4-9de6b758cc72"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="6" y="279" width="97" height="12" uuid="c3936b7b-d12e-49a0-98bb-fccf396381e2">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_responsavel")+": "]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="6" y="366" width="150" height="1" uuid="0aa106d7-5577-41d8-89ad-40f52e086a49"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="6" y="369" width="203" height="9" uuid="9609192c-695e-4d2e-9869-d56299333d3f">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["("+Bundle.getStringApplication("rotulo_carimbo_assinatura")+")"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="255" y="355" width="24" height="12" uuid="c0916b9b-12b1-4688-b6b8-ae780989d653">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data")+": "]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="280" y="355" width="100" height="12" uuid="6913a23c-efe6-41a5-9767-5a9d54cbd224"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[___ / ___ / ______]]></text>
			</staticText>
			<line>
				<reportElement x="205" y="160" width="198" height="1" uuid="7ac8b1c8-451f-404e-b9d8-c0d04ab4eeb2"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement x="88" y="116" width="24" height="12" uuid="aa842d4a-d40e-4080-9a28-1b4542db1e5f"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<text><![CDATA[Teste]]></text>
			</staticText>
			<line>
				<reportElement x="113" y="127" width="154" height="1" uuid="3eaad264-7c01-48c6-bfe2-064a60efd141"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement x="269" y="116" width="222" height="12" uuid="a70b0d23-27f8-4e6b-a9b0-000fe5abba36"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<text><![CDATA[para Influenza(reagente / não reagente).]]></text>
			</staticText>
			<staticText>
				<reportElement x="164" y="149" width="40" height="12" uuid="82dcf98e-a483-4e50-b199-db2db6188a99"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Amostra]]></text>
			</staticText>
			<staticText>
				<reportElement x="405" y="149" width="144" height="12" uuid="7d0e9dab-8438-422f-a7f9-e6b316a76b8c"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[para Influenza (reag. / não reag.).]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="132" y="65" width="126" height="12" uuid="afb82e50-f037-4388-9491-b713b219f730"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{testeRapidoConjunto}.getFabricante()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="90" y="64" width="42" height="12" uuid="0db5c1fc-9534-4a09-a627-fabd4efa274d"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_fabricante")+": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="113" y="116" width="154" height="12" uuid="e03059ee-2efc-4010-ab24-496ef79f2759"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{testeRapidoRealizado}.getDescricaoResultadoExame()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="205" y="149" width="198" height="12" uuid="b6ad51b3-5716-47c5-9d64-6766bdc8772b"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{testeRapidoRealizado}.getDescricaoResultadoExame()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="6" y="37" width="28" height="12" uuid="b4146c1a-b80a-40e0-b3f4-8cf0cc124282"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[LOTE:]]></text>
			</staticText>
			<textField>
				<reportElement x="35" y="37" width="120" height="12" uuid="e51ca3d6-1787-49d7-b39b-edfcb49608f7"/>
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{testeRapidoRealizado}.getLote() != null ? $F{testeRapidoRealizado}.getLote() : ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="326" y="49" width="95" height="12" uuid="b85d7250-8ffe-4b07-88e1-e6f6a35e0fce"/>
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.formatar($F{testeRapidoRealizado}.getDataValidade())]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="273" y="50" width="53" height="12" uuid="27397a3a-1e4e-48b7-b5ee-2748ccf1c1dd"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[VALIDADE:]]></text>
			</staticText>
		</band>
	</detail>
</jasperReport>
