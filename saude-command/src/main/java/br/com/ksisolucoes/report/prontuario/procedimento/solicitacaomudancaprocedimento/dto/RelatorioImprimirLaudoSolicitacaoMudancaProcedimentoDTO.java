package br.com.ksisolucoes.report.prontuario.procedimento.solicitacaomudancaprocedimento.dto;

import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.Raca;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.SolicitacaoMudancaProcedimento;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImprimirLaudoSolicitacaoMudancaProcedimentoDTO implements Serializable {

    private SolicitacaoMudancaProcedimento solicitacaoMudancaProcedimento;
    private Atendimento atendimento;
    private UsuarioCadsus usuarioCadsus;
    private EnderecoUsuarioCadsus enderecoUsuarioCadsus;
    private Procedimento procedimentoAnterior;
    private Procedimento procedimentoNovo;
    private Cidade cidade;
    private Estado estado;
    private Cid cidPrincipal;
    private Cid cidSecundario;
    private Cid cidCausasAssociadas;
    private Profissional profissional;
    private Aih aih;
    private Raca raca;

    public SolicitacaoMudancaProcedimento getSolicitacaoMudancaProcedimento() {
        return solicitacaoMudancaProcedimento;
    }

    public void setSolicitacaoMudancaProcedimento(SolicitacaoMudancaProcedimento solicitacaoMudancaProcedimento) {
        this.solicitacaoMudancaProcedimento = solicitacaoMudancaProcedimento;
    }

    public Atendimento getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return enderecoUsuarioCadsus;
    }

    public void setEnderecoUsuarioCadsus(EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
        this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
    }

    public Procedimento getProcedimentoAnterior() {
        return procedimentoAnterior;
    }

    public void setProcedimentoAnterior(Procedimento procedimentoAnterior) {
        this.procedimentoAnterior = procedimentoAnterior;
    }

    public Procedimento getProcedimentoNovo() {
        return procedimentoNovo;
    }

    public void setProcedimentoNovo(Procedimento procedimentoNovo) {
        this.procedimentoNovo = procedimentoNovo;
    }

    public Cidade getCidade() {
        return cidade;
    }

    public void setCidade(Cidade cidade) {
        this.cidade = cidade;
    }

    public Cid getCidPrincipal() {
        return cidPrincipal;
    }

    public void setCidPrincipal(Cid cidPrincipal) {
        this.cidPrincipal = cidPrincipal;
    }

    public Cid getCidSecundario() {
        return cidSecundario;
    }

    public void setCidSecundario(Cid cidSecundario) {
        this.cidSecundario = cidSecundario;
    }

    public Cid getCidCausasAssociadas() {
        return cidCausasAssociadas;
    }

    public void setCidCausasAssociadas(Cid cidCausasAssociadas) {
        this.cidCausasAssociadas = cidCausasAssociadas;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public Aih getAih() {
        return aih;
    }

    public void setAih(Aih aih) {
        this.aih = aih;
    }

    public Raca getRaca() {
        return raca;
    }

    public void setRaca(Raca raca) {
        this.raca = raca;
    }

    public Estado getEstado() {
        return estado;
    }

    public void setEstado(Estado estado) {
        this.estado = estado;
    }
}
