<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="sub_relatorio_despacho_item" pageWidth="520" pageHeight="802" columnWidth="520" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="267a0f01-0439-409c-9e8c-7b950bb600cb">
	<property name="ireport.zoom" value="1.9487171000000025"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<field name="autoIntimacaoExigencia" class="br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacaoExigencia"/>
	<field name="prazo" class="java.lang.Long"/>
	<field name="motivo" class="java.lang.String"/>
	<field name="status" class="java.lang.String"/>
	<field name="observacaoFiscal" class="java.lang.String"/>
	<field name="descricao" class="java.lang.String"/>
	<detail>
		<band height="16">
			<textField isBlankWhenNull="true">
				<reportElement key="" x="0" y="0" width="78" height="16" isPrintWhenDetailOverflows="true" uuid="1fcd88c4-cd27-4004-8fbc-37e09eb08140"/>
				<box topPadding="1" leftPadding="0" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="10" isBold="true" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_prazo").toUpperCase() + ": "]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement x="80" y="0" width="440" height="16" isPrintWhenDetailOverflows="true" uuid="558c4dde-87cf-4e81-adea-61260efcef2a"/>
				<box topPadding="1"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Arial" size="10" isBold="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Coalesce.asLong($F{prazo}) + " dias (" + Data.formatar($F{autoIntimacaoExigencia}.getDataCumprimentoPrazo()) + ")"]]></textFieldExpression>
			</textField>
		</band>
		<band height="18" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="80" y="0" width="440" height="18" isPrintWhenDetailOverflows="true" uuid="56c512c0-7561-4feb-98c3-99794432a41f"/>
				<box topPadding="1" bottomPadding="3">
					<bottomPen lineWidth="0.5" lineStyle="Dashed" lineColor="#C5C5C5"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Top" markup="styled">
					<font fontName="Arial" size="10" isBold="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" x="0" y="0" width="78" height="18" isPrintWhenDetailOverflows="true" uuid="e07c67e1-39f4-42e2-b684-8ae08b3824f0"/>
				<box topPadding="1" bottomPadding="3" rightPadding="2"/>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="10" isBold="true" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_descricao").toUpperCase() + ":  "]]></textFieldExpression>
			</textField>
		</band>
		<band height="18">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="80" y="0" width="440" height="18" isPrintWhenDetailOverflows="true" uuid="53dae146-7435-4d33-882b-b1155236e590"/>
				<box topPadding="1" bottomPadding="3">
					<bottomPen lineWidth="0.5" lineStyle="Dashed" lineColor="#C5C5C5"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Top" markup="styled">
					<font fontName="Arial" size="10" isBold="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{motivo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" x="0" y="0" width="78" height="18" isPrintWhenDetailOverflows="true" uuid="7d7e4ec2-4951-49f1-80a1-fd4fc7fed4cc"/>
				<box topPadding="1" leftPadding="0" bottomPadding="3" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="10" isBold="true" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_motivo").toUpperCase() + ":  "]]></textFieldExpression>
			</textField>
		</band>
		<band height="18">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="80" y="0" width="440" height="18" isPrintWhenDetailOverflows="true" uuid="02b47d69-65d9-42fb-82c2-81aab21a28e4"/>
				<box topPadding="1" bottomPadding="3">
					<pen lineWidth="0.25" lineStyle="Dashed"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5" lineColor="#C5C5C5"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Top" markup="styled">
					<font fontName="Arial" size="10" isBold="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{observacaoFiscal}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" x="0" y="0" width="78" height="18" isPrintWhenDetailOverflows="true" uuid="1d477f75-d630-4bcf-a134-94f62f3fdeb9"/>
				<box topPadding="1" leftPadding="0" bottomPadding="3" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="10" isBold="true" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_observacao").toUpperCase() + ":  "]]></textFieldExpression>
			</textField>
		</band>
		<band height="10"/>
	</detail>
</jasperReport>
