<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_resumo_encaminhamentos" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="782" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="tipoResumo" class="java.lang.String"/>
	<parameter name="formaApresentacao" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="codigoTipoResumo" class="java.lang.String"/>
	<field name="descricaoTipoResumo" class="java.lang.String"/>
	<field name="codigoFormaApresentacao" class="java.lang.String"/>
	<field name="descricaoFormaApresentacao" class="java.lang.String"/>
	<field name="tfd" class="java.lang.Long"/>
	<field name="regional" class="java.lang.Long"/>
	<field name="autorizado" class="java.lang.Long"/>
	<field name="negado" class="java.lang.Long"/>
	<field name="diasMediaEspera" class="java.lang.Long"/>
	<field name="cancelado" class="java.lang.Long"/>
	<field name="inconclusivo" class="java.lang.Long"/>
	<field name="totalTfd" class="java.lang.Long"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="TFD_UNIDADE" class="java.lang.Long" resetType="Group" resetGroup="unidadeOrigem" calculation="Sum">
		<variableExpression><![CDATA[$F{tfd}]]></variableExpression>
	</variable>
	<variable name="REGIONAL_UNIDADE" class="java.lang.Long" resetType="Group" resetGroup="unidadeOrigem" calculation="Sum">
		<variableExpression><![CDATA[$F{regional}]]></variableExpression>
	</variable>
	<variable name="AUTORIZADO_UNIDADE" class="java.lang.Long" resetType="Group" resetGroup="unidadeOrigem" calculation="Sum">
		<variableExpression><![CDATA[$F{autorizado}]]></variableExpression>
	</variable>
	<variable name="NEGADO_UNIDADE" class="java.lang.Long" resetType="Group" resetGroup="unidadeOrigem" calculation="Sum">
		<variableExpression><![CDATA[$F{negado}]]></variableExpression>
	</variable>
	<variable name="INCONCLUSIVO_UNIDADE" class="java.lang.Long" resetType="Group" resetGroup="unidadeOrigem" calculation="Sum">
		<variableExpression><![CDATA[$F{inconclusivo}]]></variableExpression>
	</variable>
	<variable name="CANCELADO_UNIDADE" class="java.lang.Long" resetType="Group" resetGroup="unidadeOrigem" calculation="Sum">
		<variableExpression><![CDATA[$F{cancelado}]]></variableExpression>
	</variable>
	<variable name="TFD_UNIDADE_1" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{tfd}]]></variableExpression>
	</variable>
	<variable name="REGIONAL_UNIDADE_1" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{regional}]]></variableExpression>
	</variable>
	<variable name="AUTORIZADO_UNIDADE_1" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{autorizado}]]></variableExpression>
	</variable>
	<variable name="NEGADO_UNIDADE_1" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{negado}]]></variableExpression>
	</variable>
	<variable name="INCONCLUSIVO_UNIDADE_1" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{inconclusivo}]]></variableExpression>
	</variable>
	<variable name="CANCELADO_UNIDADE_1" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{cancelado}]]></variableExpression>
	</variable>
	<variable name="TOTAL_TFD_UNIDADE" class="java.lang.Long" resetType="Group" resetGroup="unidadeOrigem" calculation="Sum">
		<variableExpression><![CDATA[$F{totalTfd}]]></variableExpression>
	</variable>
	<variable name="TOTAL_TFD_UNIDADE_1" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{totalTfd}]]></variableExpression>
	</variable>
	<group name="TIPO_RESUMO">
		<groupFooter>
			<band height="16">
				<textField>
					<reportElement x="175" y="4" width="115" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_total_geral")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="343" y="4" width="47" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{REGIONAL_UNIDADE_1}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="238" y="2" width="467" height="1"/>
				</line>
				<textField>
					<reportElement x="292" y="4" width="49" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{TFD_UNIDADE_1}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="571" y="4" width="50" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{CANCELADO_UNIDADE_1}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="392" y="4" width="60" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{AUTORIZADO_UNIDADE_1}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="503" y="4" width="65" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{INCONCLUSIVO_UNIDADE_1}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="453" y="4" width="50" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{NEGADO_UNIDADE_1}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="622" y="4" width="50" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{TOTAL_TFD_UNIDADE_1}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="unidadeOrigem">
		<groupExpression><![CDATA[$F{codigoFormaApresentacao}]]></groupExpression>
		<groupHeader>
			<band height="34">
				<printWhenExpression><![CDATA[! $P{tipoResumo}.equals($P{formaApresentacao})]]></printWhenExpression>
				<textField>
					<reportElement x="571" y="21" width="50" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_cancelado")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="21" width="288" height="11"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[br.com.ksisolucoes.vo.basico.Empresa.REF.equals($P{tipoResumo}) ? Bundle.getStringApplication("rotulo_empresa"):
br.com.ksisolucoes.vo.cadsus.Profissional.REF.equals($P{tipoResumo}) ? Bundle.getStringApplication("rotulo_profissional"):
br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento.REF.equals($P{tipoResumo}) ? Bundle.getStringApplication("rotulo_tipo_procedimento"):
br.com.ksisolucoes.vo.cadsus.UsuarioCadsus.REF.equals($P{tipoResumo}) ? Bundle.getStringApplication("rotulo_paciente"): ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="453" y="21" width="50" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_negado")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="503" y="21" width="65" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_inconclusivo")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="292" y="21" width="49" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_agenda_envio_abv")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="32" width="782" height="1"/>
				</line>
				<textField>
					<reportElement x="707" y="21" width="75" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_dias_media_espera")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="2" width="782" height="12"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[(br.com.ksisolucoes.vo.basico.Empresa.REF.equals($P{formaApresentacao}) ? Bundle.getStringApplication("rotulo_empresa"):
br.com.ksisolucoes.vo.cadsus.Profissional.REF.equals($P{formaApresentacao}) ? Bundle.getStringApplication("rotulo_profissional"):
br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento.REF.equals($P{formaApresentacao}) ? Bundle.getStringApplication("rotulo_tipo_procedimento"):
br.com.ksisolucoes.vo.cadsus.UsuarioCadsus.REF.equals($P{formaApresentacao}) ? Bundle.getStringApplication("rotulo_paciente"): "")+": "+
br.com.ksisolucoes.util.Util.getDescricaoFormatado($F{codigoFormaApresentacao},$F{descricaoFormaApresentacao})]]></textFieldExpression>
				</textField>
				<rectangle radius="10">
					<reportElement mode="Transparent" x="0" y="1" width="782" height="14"/>
				</rectangle>
				<textField>
					<reportElement x="343" y="21" width="47" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_regional")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="392" y="21" width="60" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_autorizado")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="622" y="21" width="50" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_total_tfd")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="14">
				<printWhenExpression><![CDATA[! $P{tipoResumo}.equals($P{formaApresentacao})]]></printWhenExpression>
				<line>
					<reportElement x="263" y="1" width="442" height="1"/>
				</line>
				<textField>
					<reportElement x="175" y="3" width="115" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="292" y="3" width="49" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{TFD_UNIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="343" y="3" width="47" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{REGIONAL_UNIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="392" y="3" width="60" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{AUTORIZADO_UNIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="453" y="3" width="50" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{NEGADO_UNIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="503" y="3" width="65" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{INCONCLUSIVO_UNIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="571" y="3" width="50" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{CANCELADO_UNIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="622" y="3" width="50" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{TOTAL_TFD_UNIDADE}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="14" splitType="Stretch">
			<printWhenExpression><![CDATA[$P{tipoResumo}.equals($P{formaApresentacao})]]></printWhenExpression>
			<textField>
				<reportElement x="0" y="0" width="288" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[br.com.ksisolucoes.vo.basico.Empresa.REF.equals($P{tipoResumo}) ? Bundle.getStringApplication("rotulo_empresa"):
br.com.ksisolucoes.vo.cadsus.Profissional.REF.equals($P{tipoResumo}) ? Bundle.getStringApplication("rotulo_profissional"):
br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento.REF.equals($P{tipoResumo}) ? Bundle.getStringApplication("rotulo_tipo_procedimento"):
br.com.ksisolucoes.vo.cadsus.UsuarioCadsus.REF.equals($P{tipoResumo}) ? Bundle.getStringApplication("rotulo_paciente"): ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="571" y="0" width="50" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_cancelado")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="292" y="0" width="49" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_agenda_envio_abv")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="453" y="0" width="50" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_negado")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="503" y="0" width="65" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_inconclusivo")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="707" y="0" width="75" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_dias_media_espera")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="12" width="782" height="1"/>
			</line>
			<textField>
				<reportElement x="343" y="0" width="47" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_regional")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="392" y="0" width="60" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_autorizado")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="622" y="0" width="50" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_total_tfd")]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="10" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="0" width="288" height="10"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[br.com.ksisolucoes.util.Util.getDescricaoFormatado($F{codigoTipoResumo},$F{descricaoTipoResumo})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="571" y="0" width="50" height="10"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{cancelado}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="292" y="0" width="49" height="10"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{tfd}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="453" y="0" width="50" height="10"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{negado}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="503" y="0" width="65" height="10"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{inconclusivo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="707" y="0" width="75" height="10"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{diasMediaEspera}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="343" y="0" width="47" height="10"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{regional}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="392" y="0" width="60" height="10"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{autorizado}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="622" y="0" width="50" height="10"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{totalTfd}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
