package br.com.ksisolucoes.report.prontuario.basico.query;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QuerySorter;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioResumoEncaminhamentosDTO;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioResumoEncaminhamentosDTOParam;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoEncaminhamento;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import org.hibernate.Query;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioresumoEncaminhamentos extends CommandQuery implements ITransferDataReport<RelatorioResumoEncaminhamentosDTOParam, RelatorioResumoEncaminhamentosDTO> {

    private RelatorioResumoEncaminhamentosDTOParam param;
    private List<RelatorioResumoEncaminhamentosDTO> list;

    @Override
    public void setDTOParam(RelatorioResumoEncaminhamentosDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(RelatorioResumoEncaminhamentosDTO.class.getName());

        String whereFormaApresentacao = "";
        if (!param.getFormaApresentacao().equals(param.getTipoResumo())) {
            if (Empresa.REF.equals(param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("unidadeEncaminhamento.descricao", "descricaoFormaApresentacao");
                hql.addToSelectAndGroup("unidadeEncaminhamento.referencia", "codigoFormaApresentacao");
                hql.addToGroup("unidadeEncaminhamento.codigo");

                hql.addToSelectAndGroup("unidadeEncaminhamento.cnes", "cnes");

                whereFormaApresentacao = " enc1.unidadeEncaminhamento.codigo = unidadeEncaminhamento.codigo ";
            } else if (Profissional.REF.equals(param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("profissional.nome", "descricaoFormaApresentacao");
                hql.addToSelectAndGroup("profissional.referencia", "codigoFormaApresentacao");
                hql.addToGroup("profissional.codigo");

                whereFormaApresentacao = " profissional.codigo = enc1.profissional.codigo ";
            } else if (TipoEncaminhamento.REF.equals(param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("tipoEncaminhamento.descricao", "descricaoFormaApresentacao");
                hql.addToSelectAndGroup("cast(tipoEncaminhamento.codigo as string)", "codigoFormaApresentacao");
                hql.addToGroup("tipoEncaminhamento.codigo");

                whereFormaApresentacao = " tipoEncaminhamento = enc1.tipoEncaminhamento ";
            } else if (UsuarioCadsus.REF.equals(param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("usuarioCadsus.nome", "descricaoFormaApresentacao");
                hql.addToSelectAndGroup("cast(usuarioCadsus.codigo as string)", "codigoFormaApresentacao");
                hql.addToGroup("usuarioCadsus.codigo");

                whereFormaApresentacao = " usuarioCadsus = enc1.usuarioCadsus ";
            } else if (TabelaCbo.REF.equals(param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("tabelaCbo.descricao", "descricaoFormaApresentacao");
                hql.addToSelectAndGroup("cast(tabelaCbo.cbo as string)", "codigoFormaApresentacao");
                hql.addToGroup("tabelaCbo.cbo");

                whereFormaApresentacao = " tabelaCbo.cbo = enc1.atendimento.tabelaCbo.cbo ";
            }
        }

        String whereTipoResumo = "";
        if (Empresa.REF.equals(param.getTipoResumo())) {
            hql.addToSelectAndGroupAndOrder("unidadeEncaminhamento.descricao", "descricaoTipoResumo");
            hql.addToSelectAndGroup("unidadeEncaminhamento.referencia", "codigoTipoResumo");
            hql.addToGroup("unidadeEncaminhamento.codigo");

            hql.addToSelectAndGroup("unidadeEncaminhamento.cnes", "cnes");

            whereTipoResumo = " enc1.unidadeEncaminhamento.codigo = unidadeEncaminhamento.codigo ";
        } else if (Profissional.REF.equals(param.getTipoResumo())) {
            hql.addToSelectAndGroupAndOrder("profissional.nome", "descricaoTipoResumo");
            hql.addToSelectAndGroup("profissional.referencia", "codigoTipoResumo");
            hql.addToGroup("profissional.codigo");

            whereTipoResumo = " profissional.codigo = enc1.profissional.codigo ";
        } else if (TipoEncaminhamento.REF.equals(param.getTipoResumo())) {
            hql.addToSelectAndGroupAndOrder("tipoEncaminhamento.descricao", "descricaoTipoResumo");
            hql.addToSelectAndGroup("cast(tipoEncaminhamento.codigo as string)", "codigoTipoResumo");
            hql.addToGroup("tipoEncaminhamento.codigo");

            whereTipoResumo = " tipoEncaminhamento = enc1.tipoEncaminhamento ";
        } else if (UsuarioCadsus.REF.equals(param.getTipoResumo())) {
            hql.addToSelectAndGroupAndOrder("usuarioCadsus.nome", "descricaoTipoResumo");
            hql.addToSelectAndGroup("cast(usuarioCadsus.codigo as string)", "codigoTipoResumo");
            hql.addToGroup("usuarioCadsus.codigo");

            whereTipoResumo = " usuarioCadsus = enc1.usuarioCadsus ";
        } else if (TabelaCbo.REF.equals(param.getTipoResumo())) {
            hql.addToSelectAndGroupAndOrder("tabelaCbo.descricao", "descricaoTipoResumo");
            hql.addToSelectAndGroup("cast(tabelaCbo.cbo as string)", "codigoTipoResumo");
            hql.addToGroup("tabelaCbo.cbo");

            whereTipoResumo = " tabelaCbo.cbo = enc1.atendimento.tabelaCbo.cbo ";
        }

        HQLHelper subTotalEncaminhamentos = new HQLHelper();
        subTotalEncaminhamentos.addToSelect("count(*)");
        subTotalEncaminhamentos.addToFrom("Encaminhamento enc1");
        subTotalEncaminhamentos.addToWhereWhithAnd(whereFormaApresentacao);
        subTotalEncaminhamentos.addToWhereWhithAnd(whereTipoResumo);
        addHqlWhereParam(subTotalEncaminhamentos, "enc1");
        hql.addSubToSelect(subTotalEncaminhamentos, "totalEncaminhamentos");

        HQLHelper subTotalNaoAprovados = new HQLHelper();
        subTotalNaoAprovados.addToSelect("count(*)");
        subTotalNaoAprovados.addToFrom("Encaminhamento enc1");
        subTotalNaoAprovados.addToWhereWhithAnd("enc1.status = :statusNaoAprovado");
        subTotalNaoAprovados.addToWhereWhithAnd(whereFormaApresentacao);
        subTotalNaoAprovados.addToWhereWhithAnd(whereTipoResumo);
        addHqlWhereParam(subTotalNaoAprovados, "enc1");
        hql.addSubToSelect(subTotalNaoAprovados, "naoAprovados");

        HQLHelper subTotalCancelados = new HQLHelper();
        subTotalCancelados.addToSelect("count(*)");
        subTotalCancelados.addToFrom("Encaminhamento enc1");
        subTotalCancelados.addToWhereWhithAnd("enc1.status = :statusCancelado");
        subTotalCancelados.addToWhereWhithAnd(whereFormaApresentacao);
        subTotalCancelados.addToWhereWhithAnd(whereTipoResumo);
        addHqlWhereParam(subTotalCancelados, "enc1");
        hql.addSubToSelect(subTotalCancelados, "cancelados");

        HQLHelper subTotalAgendado = new HQLHelper();
        subTotalAgendado.addToSelect("count(*)");
        subTotalAgendado.addToFrom("Encaminhamento enc1");
        subTotalAgendado.addToWhereWhithAnd("enc1.status = :statusTotalAgendado");
        subTotalAgendado.addToWhereWhithAnd(whereFormaApresentacao);
        subTotalAgendado.addToWhereWhithAnd(whereTipoResumo);
        addHqlWhereParam(subTotalAgendado, "enc1");
        hql.addSubToSelect(subTotalAgendado, "totalAgendado");

//        HQLHelper subTotalAtendidos = new HQLHelper();
//        subTotalAtendidos.addToSelect("count(*)");
//        subTotalAtendidos.addToFrom("Encaminhamento enc1");
//        subTotalAtendidos.addToWhereWhithAnd("enc1.status in (:statusConcluido)");
//        subTotalAtendidos.addToWhereWhithAnd("coalesce(enc1.compareceuAtendimento,'"+RepositoryComponentDefault.SIM+"') = '"+RepositoryComponentDefault.SIM+"'");
//        subTotalAtendidos.addToWhereWhithAnd(whereFormaApresentacao);
//        subTotalAtendidos.addToWhereWhithAnd(whereTipoResumo);
//        addHqlWhereParam(subTotalAtendidos, "enc1");
//        hql.addSubToSelect(subTotalAtendidos, "totalAtendido");
//
//        HQLHelper subTotalFaltas = new HQLHelper();
//        subTotalFaltas.addToSelect("count(*)");
//        subTotalFaltas.addToFrom("Encaminhamento enc1");
//        subTotalFaltas.addToWhereWhithAnd("enc1.status in (:statusConcluido)");
//        subTotalFaltas.addToWhereWhithAnd("coalesce(enc1.compareceuAtendimento,'"+RepositoryComponentDefault.SIM+"') = '"+RepositoryComponentDefault.NAO+"'");
//        subTotalFaltas.addToWhereWhithAnd(whereFormaApresentacao);
//        subTotalFaltas.addToWhereWhithAnd(whereTipoResumo);
//        addHqlWhereParam(subTotalFaltas, "enc1");
//        hql.addSubToSelect(subTotalFaltas, "totalFaltas");

        HQLHelper subTotalDiasEspera = new HQLHelper();
        subTotalDiasEspera.addToSelect("day(sum(encaminhamentoAgendamento1.dataAgendamento - enc1.dataCadastro))");
        subTotalDiasEspera.addToFrom("Encaminhamento enc1 "
                + " left join enc1.encaminhamentoAgendamento encaminhamentoAgendamento1");
        subTotalDiasEspera.addToWhereWhithAnd("enc1.status in (:statusList)");
        subTotalDiasEspera.addToWhereWhithAnd(whereFormaApresentacao);
        subTotalDiasEspera.addToWhereWhithAnd(whereTipoResumo);
        addHqlWhereParam(subTotalDiasEspera, "enc1");
        hql.addSubToSelect(subTotalDiasEspera, "totalDiasEspera");

        hql.addToFrom("Encaminhamento enc "
                + " left join enc.unidadeEncaminhamento unidadeEncaminhamento  "
                + " left join enc.profissional profissional "
                + " left join enc.tipoEncaminhamento tipoEncaminhamento "
                + " left join enc.usuarioCadsus usuarioCadsus  "
                + " left join enc.atendimento atendimento"
                + " left join atendimento.tabelaCbo tabelaCbo");

        addHqlWhereParam(hql, "enc");

   }

    private void addHqlWhereParam(HQLHelper hql, String aliasEncaminhamento){
        hql.addToWhereWhithAnd(aliasEncaminhamento+".unidadeEncaminhamento in", param.getUnidadeOrigem());
        hql.addToWhereWhithAnd(aliasEncaminhamento+".dataCadastro ", Data.adjustRangeHour(param.getPeriodo()));
        hql.addToWhereWhithAnd(aliasEncaminhamento+".profissional in", param.getProfissional());
        hql.addToWhereWhithAnd(aliasEncaminhamento+".usuarioCadsus in", param.getPaciente());
        hql.addToWhereWhithAnd(aliasEncaminhamento+".tipoEncaminhamento  in", param.getTipoEncaminhamento());
        hql.addToWhereWhithAnd(aliasEncaminhamento+".atendimento.tabelaCbo in ", param.getTabelasCbo());

        if (RepositoryComponentDefault.SIM_LONG.equals(param.getApenasCaps())) {
            hql.addToWhereWhithAnd(aliasEncaminhamento+".unidadeEncaminhamento.atividade.codigo = ", RepositoryComponentDefault.ATIVIDADE_CENTRO_ATENCAO_PSICOSSOCIAL);
        }

        if (RepositoryComponentDefault.SIM.equals(param.getRetorno())){
            hql.addToWhereWhithAnd(aliasEncaminhamento+".flagRetorno = ", RepositoryComponentDefault.SIM);
        } else if (RepositoryComponentDefault.NAO.equals(param.getRetorno())){
            hql.addToWhereWhithAnd(aliasEncaminhamento+".flagRetorno <> ", RepositoryComponentDefault.SIM);
        }
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if(CollectionUtils.isNotNullEmpty(list)){
            Collections.sort(list, new Comparator<RelatorioResumoEncaminhamentosDTO>() {

                @Override
                public int compare(RelatorioResumoEncaminhamentosDTO o1, RelatorioResumoEncaminhamentosDTO o2) {
                    int retorno = 0;
                    if(!param.getFormaApresentacao().equals(param.getTipoResumo())){
                        retorno = o1.getCodigoFormaApresentacao().compareTo(o2.getCodigoFormaApresentacao());
                    }
                    if(retorno == 0){
//                        if(o1.getCodigoTipoResumo().compareTo(o2.getCodigoTipoResumo()) == 0 ){
                            int fatorMultiplicacao = 1;
                            if(QuerySorter.DECRESCENTE.equals(param.getTipoOrdenacao())){
                                fatorMultiplicacao = -1;
                            }
                            if(RepositoryComponentDefault.TOTAL_ENCAMINHAMENTO.equals(param.getOrdenacao())){
                                retorno = fatorMultiplicacao * o1.getTotalEncaminhamentos().compareTo(o2.getTotalEncaminhamentos());
                            }else if(RepositoryComponentDefault.DIAS_MEDIA_ESPERA.equals(param.getOrdenacao())){
                                retorno = fatorMultiplicacao * o1.getDiasMediaEspera().compareTo(o2.getDiasMediaEspera());
                            }else {
                                retorno = fatorMultiplicacao * o1.getPorcentagemFalta().compareTo(o2.getPorcentagemFalta());
                            }
//                        }
                    }else{
                        return 0;
                    }
                    return retorno;
                }
            });
        }
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setParameter("statusCancelado", Encaminhamento.STATUS_CANCELADO);
        query.setParameter("statusNaoAprovado", Encaminhamento.STATUS_NAO_AUTORIZADO);
//        query.setParameterList("statusConcluido", Arrays.asList(Encaminhamento.STATUS_CONCLUIDO, Encaminhamento.STATUS_CONCLUIDO_COM_RETORNO));
        query.setParameterList("statusList", Arrays.asList(Encaminhamento.STATUS_PENDENTE, Encaminhamento.STATUS_AUTORIZADO, Encaminhamento.STATUS_AGENDADO,Encaminhamento.STATUS_CONCLUIDO, Encaminhamento.STATUS_CONCLUIDO_COM_RETORNO));
        query.setParameter("statusTotalAgendado", Encaminhamento.STATUS_AGENDADO);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        list = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection getResult() {
        return list;
    }

}
