/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.report.vigilancia.query.QueryFichaDoencaLyme;
import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaInvestigacaoAgravoDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;

import java.util.LinkedHashMap;

public class ImpressaoFichaInvestigacaoDoencaLyme extends AbstractReport<FichaInvestigacaoAgravoDTOParam> {

    private QueryFichaDoencaLyme query;

    public ImpressaoFichaInvestigacaoDoencaLyme(FichaInvestigacaoAgravoDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        if(query == null){
            query = new QueryFichaDoencaLyme();
        }
        return query;
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> columnsMap = new LinkedHashMap<>(((QueryFichaDoencaLyme)getQuery()).getMapeamentoPlanilhaBase());

        columnsMap.put("ataInvestigacao", "_31_data_investigacao");
        columnsMap.put("ocupacaoCbo", "_32_ocupacao");

        columnsMap.put("situacaoRiscoPicadaCarrapato", "_32_situacao_risco_picada_carrapato");
        columnsMap.put("dataPicadaCarrapato", "_32_situacao_risco_picada_carrapato_data");
        columnsMap.put("situacaoRiscoPresencaCarrapato", "_32_situacao_risco_presenca_carrapato");
        columnsMap.put("dataPresencaCarrapato", "_32_situacao_risco_presenca_carrapato_data");
        columnsMap.put("situacaoRiscoAreasMata", "_32_situacao_risco_areas_mata");
        columnsMap.put("dataAreasMata", "_32_situacao_risco_areas_matas_data");
        columnsMap.put("situacaoRiscoContatoAnimais", "_32_situacao_risco_contato_animais");
        columnsMap.put("dataContatoAnimais", "_32_situacao_risco_contato_animais_data");
        columnsMap.put("contatoAnimaisSilvestres", "_32_situacao_risco_contato_animais_silvestres");
        columnsMap.put("contatoAnimaisDomesticos", "_32_situacao_risco_contato_animais_domesticos");
        columnsMap.put("animaisDomesticosDesc", "_32_situacao_risco_contato_animais_domesticos_desc");
        columnsMap.put("situacaoRiscoAnimaisDoentes", "_32_situacao_risco_contato_animais_doentes");
        columnsMap.put("dataAnimaisDoentes", "_32_situacao_risco_contato_animais_doentes_data");
        columnsMap.put("situacaoRiscoMorteAnimal", "_32_situacao_risco_morte_animal");
        columnsMap.put("dataMorteAnimal", "_32_situacao_risco_morte_animal_data");
        columnsMap.put("situacaoRiscoCasosHumanos", "_32_situacao_risco_casos_humanos");
        columnsMap.put("dataCasosHumanos", "_32_situacao_risco_casos_humanos_data");

        columnsMap.put("sinaisSintomasFebre", "_34_sinais_sintomas_febre");
        columnsMap.put("sinaisSintomasMialgia", "_34_sinais_sintomas_mialgia");
        columnsMap.put("sinaisSintomasAstralgia", "_34_sinais_sintomas_astralgia");
        columnsMap.put("sinaisSintomasCefaleia", "_34_sinais_sintomas_cefaleia");
        columnsMap.put("sinaisSintomasMalEstar", "_34_sinais_sintomas_mal_estar");
        columnsMap.put("sinaisSintomasPetequias", "_34_sinais_sintomas_petequias");
        columnsMap.put("sinaisSintomasRigidezNuca", "_34_sinais_sintomas_rigidez_nuca");
        columnsMap.put("sinaisSintomasFadiga", "_34_sinais_sintomas_fadiga");
        columnsMap.put("sinaisSintomasEritmeCronico", "_34_sinais_sintomas_eritme_cronico");
        columnsMap.put("sinaisSintomasAumentoGanglios", "_34_sinais_sintomas_aumento_ganglios");

        columnsMap.put("manifestacoesNaurologicasMeningite", "_35_manifestacoes_neurologicas_meningite");
        columnsMap.put("manifestacoesNaurologicasNeuriteCraniana", "_35_manifestacoes_neurologicas_craniana");
        columnsMap.put("manifestacoesNaurologicasPeriferica", "_35_manifestacoes_neurologicas_periferica");

        columnsMap.put("manifestacoesCardiacasCardiomegalia", "_36_manifestacoes_cardiacas_cardiomegalia");
        columnsMap.put("manifestacoesCardiacasArritmia", "_36_manifestacoes_cardiacas_arritmia");

        columnsMap.put("casosAnterioresLyme", "_37_casos_anteriores_lyme");

        columnsMap.put("hospitalizacao", "_38_hospitalizacao");
        columnsMap.put("dataInternacao", "_39_data_internacao");
        columnsMap.put("dataAlta", "_40_data_alta");
        columnsMap.put("estado", "_41_estado");
        columnsMap.put("hospital", "_52_hospital");

        columnsMap.put("sorologiaElisaData", "_43_sorologia_elisa_data");
        columnsMap.put("sorologiaElisaResultadoIgm", "_44_sorologia_elisa_resultado_igm");
        columnsMap.put("sorologiaElisaResultadoIgmDesc", "_44_sorologia_elisa_resultado_igm_desc");
        columnsMap.put("sorologiaElisaResultadoIgg", "_44_sorologia_elisa_resultado_igg");
        columnsMap.put("sorologiaElisaResultadoIggDesc", "_44_sorologia_elisa_resultado_igg_desc");

        columnsMap.put("sorologiaWesternBlotData", "_45_sorologia_western_blot_data");
        columnsMap.put("sorologiaWesternBlotResultadoIgm", "_45_sorologia_western_blot_resultado_igm");
        columnsMap.put("sorologiaWesternBlotResultadoIgmDesc", "_45_sorologia_western_blot_resultado_igm_desc");
        columnsMap.put("sorologiaWesternBlotIgg", "_45_sorologia_western_blot_resultado_igg");
        columnsMap.put("sorologiaWesternBlotIggDesc", "_45_sorologia_western_blot_resultado_igg_desc");

        columnsMap.put("casoAutoctone", "_49_caso_autoctone");
        columnsMap.put("estado", "_50_estado");
        columnsMap.put("cidadeLocalInfeccao", "_51_cidade");
        columnsMap.put("localProvavelInfeccao", "_52_local_provavel_infeccao");
        columnsMap.put("ambienteInfeccao", "_53_ambiente_infeccao");
        columnsMap.put("doencaRelacionadaTrabalho", "_53_doenca_relacionada_trabalho");

        columnsMap.put("classificacaoFinal", "_47_classificacao_final");
        columnsMap.put("criterioConfirmacaoDescarte", "_48_criterio_confirmacao_descarte");
        columnsMap.put("evolucaoCaso", "_54_evolucao_caso");
        columnsMap.put("dataObito", "_55_data_obito");

        columnsMap.put("observacao", "_observacao");

        return columnsMap;
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/pdf/ficha_investigacao_doenca_lyme.pdf";
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_doenca_lyme");
    }

}
