<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_bmpo_completo" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="842" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="1705cf08-7389-4d67-aadc-4fc5d7bcec41">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="receita" class="java.lang.String"/>
	<field name="bean" class="java.lang.Object">
		<fieldDescription><![CDATA[bean]]></fieldDescription>
	</field>
	<field name="balancoCompletoMedicamentoList" class="java.util.List">
		<fieldDescription><![CDATA[balancoCompletoMedicamentoList]]></fieldDescription>
	</field>
	<field name="balancoAquisicaoMedicamentoList" class="java.util.List"/>
	<group name="BMPO" isStartNewColumn="true" isStartNewPage="true">
		<groupExpression><![CDATA[1]]></groupExpression>
		<groupHeader>
			<band height="10" splitType="Stretch">
				<subreport isUsingCache="false">
					<reportElement key="subreport-2" x="0" y="0" width="842" height="10" uuid="4c32502b-2900-40ab-8a0b-d932e4933951"/>
					<subreportParameter name="receita">
						<subreportParameterExpression><![CDATA[$P{receita}]]></subreportParameterExpression>
					</subreportParameter>
					<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource( Arrays.asList( $F{bean} ) )]]></dataSourceExpression>
					<subreportExpression><![CDATA["br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_anexo_xxi.jasper"]]></subreportExpression>
				</subreport>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<group name="COMPLETO" isStartNewPage="true">
		<groupExpression><![CDATA[1]]></groupExpression>
		<groupHeader>
			<band height="10" splitType="Stretch">
				<printWhenExpression><![CDATA[$F{balancoCompletoMedicamentoList}.size() > 0]]></printWhenExpression>
				<subreport isUsingCache="false">
					<reportElement key="subreport-1" x="0" y="0" width="842" height="10" uuid="4513c577-77f3-4bb5-a885-81901ff4cd16"/>
					<subreportParameter name="anoExercicio">
						<subreportParameterExpression><![CDATA[((br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioBMPOParam)$F{bean}).getAnoExercicio()]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="tipoPeriodo">
						<subreportParameterExpression><![CDATA[((br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioBMPOParam)$F{bean}).getTipoPeriodo()]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="cnpj">
						<subreportParameterExpression><![CDATA[((br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioBMPOParam)$F{bean}).getEmpresa().getCnpj()]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="licencaFuncionamento">
						<subreportParameterExpression><![CDATA[((br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioBMPOParam)$F{bean}).getEmpresa().getEmpresaMaterial().getNumLicencaFuncionamento()]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="razaoSocialEmpresa">
						<subreportParameterExpression><![CDATA[((br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioBMPOParam)$F{bean}).getEmpresa().getDescricao()]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="periodicidade">
						<subreportParameterExpression><![CDATA[((br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioBMPOParam)$F{bean}).getPeriodicidade()]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="numeroAutorizacaoFuncionamento">
						<subreportParameterExpression><![CDATA[((br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioBMPOParam)$F{bean}).getEmpresa().getEmpresaMaterial().getNumeroAutorizacaoFuncionamento()]]></subreportParameterExpression>
					</subreportParameter>
					<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource( $F{balancoCompletoMedicamentoList} )]]></dataSourceExpression>
					<subreportExpression><![CDATA["br/com/ksisolucoes/report/entrada/estoque/jrxml/sub_relatorio_balanco_completo_medicamentos.jasper"]]></subreportExpression>
				</subreport>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band splitType="Stretch"/>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
