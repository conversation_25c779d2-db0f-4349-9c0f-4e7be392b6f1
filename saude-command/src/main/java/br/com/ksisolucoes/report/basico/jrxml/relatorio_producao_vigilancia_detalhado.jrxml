<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_equipes" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="11f7aedf-8e58-4668-8dea-f121a31c2b60">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="7"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.ksisolucoes.vo.basico.TipoEquipe"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.basico.Equipe"/>
	<import value="br.com.ksisolucoes.report.ReportProperties"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="formaApresentacao" class="java.lang.Long"/>
	<parameter name="tipoRelatorio" class="java.lang.Integer"/>
	<field name="lancamentoAtividadesVigilanciaItem" class="br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilanciaItem"/>
	<field name="pontuacaoTotal" class="java.lang.Double"/>
	<field name="protocoloFormatado" class="java.lang.String"/>
	<field name="tipoRequerimento" class="java.lang.String"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="FA" class="br.com.ksisolucoes.bo.vigilancia.interfaces.dto.LancamentoAtividadesVigilanciaDTOParam.FormaApresentacao"/>
	<variable name="sumQuantidadeFA" class="java.lang.Long" resetType="Group" resetGroup="FA" calculation="Sum">
		<variableExpression><![CDATA[$F{lancamentoAtividadesVigilanciaItem}.getQuantidade()]]></variableExpression>
	</variable>
	<variable name="totalQuantidade" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{lancamentoAtividadesVigilanciaItem}.getQuantidade()]]></variableExpression>
	</variable>
	<variable name="sumQuantidadePontuacaoFA" class="java.lang.Double" resetType="Group" resetGroup="FA" calculation="Sum">
		<variableExpression><![CDATA[$F{pontuacaoTotal}]]></variableExpression>
	</variable>
	<variable name="totalQuantidadePontuacao" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{pontuacaoTotal}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<group name="FA" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{lancamentoAtividadesVigilanciaItem}.getLancamentoAtividadesVigilancia().getProfissional() != null && $F{lancamentoAtividadesVigilanciaItem}.getLancamentoAtividadesVigilancia().getProfissional().getNome() != null
    ? $F{lancamentoAtividadesVigilanciaItem}.getLancamentoAtividadesVigilancia().getProfissional().getNome()
    : "Não informado"]]></groupExpression>
		<groupHeader>
			<band height="21">
				<rectangle radius="8">
					<reportElement stretchType="RelativeToBandHeight" x="1" y="2" width="534" height="18" uuid="6e975e1f-6a2f-4f18-bd9e-7f3952237014"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy">
					<reportElement x="4" y="5" width="525" height="13" uuid="c8ddfca8-42d9-430f-abf7-fdbb24f819d7"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{lancamentoAtividadesVigilanciaItem}.getLancamentoAtividadesVigilancia().getProfissional() != null && $F{lancamentoAtividadesVigilanciaItem}.getLancamentoAtividadesVigilancia().getProfissional().getNome() != null
    ? $F{lancamentoAtividadesVigilanciaItem}.getLancamentoAtividadesVigilancia().getProfissional().getNome()
    : "Não informado"]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<group name="RESUMO" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{lancamentoAtividadesVigilanciaItem}.getAtividadesVigilancia().getDescricao()]]></groupExpression>
		<groupHeader>
			<band height="15">
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="1" y="1" width="528" height="14" forecolor="#000000" backcolor="#FFFFFF" uuid="7612f7de-d250-4b4b-a09d-55d60c18c779"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="true" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{lancamentoAtividadesVigilanciaItem}.getAtividadesVigilancia().getDescricao()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="DetailHeaderDetalhado" isReprintHeaderOnEachPage="true">
		<groupHeader>
			<band height="12">
				<line>
					<reportElement x="0" y="11" width="535" height="1" uuid="ccf10788-1043-4ed4-bcb5-0dcdf16777bb"/>
				</line>
				<textField isBlankWhenNull="true">
					<reportElement x="1" y="1" width="47" height="10" uuid="53f941e9-e5f2-470e-b2e5-1dd96fba7dbd"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Data"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="48" y="1" width="215" height="10" uuid="e4cf8ca3-fc0c-475f-a231-03fbf6f133fc"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Estabelecimento"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="472" y="1" width="30" height="10" uuid="b4588b90-fc15-42ca-9c59-724fd76249d3"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Qtde"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="441" y="1" width="30" height="10" uuid="caa158e5-f7f9-4eef-8b24-c479bf8be182"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Pontos"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="502" y="1" width="33" height="10" uuid="baab2b17-20d9-4d86-ac7b-5c8c06deb29c"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Tot. Pontos"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="267" y="1" width="120" height="10" uuid="e9a59731-40e9-4b70-96e6-5ffb0eac4dec"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Requerimento"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="389" y="1" width="50" height="10" uuid="25409341-8c71-4b11-91c8-1dd5a18dcbb7"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Protocolo"]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="24">
				<line>
					<reportElement x="441" y="2" width="94" height="1" uuid="7e547cca-6f84-4730-8637-87df7fce9e78"/>
				</line>
				<textField isBlankWhenNull="true">
					<reportElement positionType="Float" x="405" y="6" width="66" height="10" uuid="f23b2c2a-7171-444f-805f-bdde723e14f6"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Total: "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" x="472" y="6" width="30" height="10" uuid="f23ce7a6-6690-40de-908a-4ec85bc03586"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{sumQuantidadeFA}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" x="502" y="6" width="33" height="10" uuid="5b46e042-65e9-4b2f-8b45-97b96e98ee3d"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{sumQuantidadePontuacaoFA}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="10" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="1" y="0" width="47" height="10" uuid="69107d50-efe4-4078-9f9a-040d9fee4cdd"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{lancamentoAtividadesVigilanciaItem}.getLancamentoAtividadesVigilancia().getDataAtividade()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement x="48" y="0" width="215" height="10" uuid="255f4a32-3df6-4eaa-9f28-b89a3c6d2bce"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{lancamentoAtividadesVigilanciaItem}.getLancamentoAtividadesVigilancia().getNomePessoa()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="472" y="0" width="30" height="10" uuid="88b64143-a6ea-463a-ba81-eb18b9735bcd"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{lancamentoAtividadesVigilanciaItem}.getQuantidade()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="441" y="0" width="30" height="10" uuid="59a4f47c-f01f-4c0e-8d19-01a62e365508"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{lancamentoAtividadesVigilanciaItem}.getPontuacao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="502" y="0" width="33" height="10" uuid="844e7577-1dc0-4719-8b26-dbf45633a926"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{pontuacaoTotal}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement x="389" y="0" width="50" height="10" uuid="04477b12-47a5-43bf-bcaa-170721ebe312"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{protocoloFormatado}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement x="267" y="0" width="120" height="10" uuid="4de83eb1-5858-4218-85c8-2ad671fe4de2"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipoRequerimento}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<summary>
		<band height="14" splitType="Stretch">
			<line>
				<reportElement positionType="Float" x="441" y="0" width="94" height="1" uuid="05e8191f-ff0a-45f6-b5d1-3dabf11b54ef"/>
			</line>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" x="472" y="4" width="30" height="10" uuid="72446072-ae8e-4188-b4b3-4c24934e4b07"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalQuantidade}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="405" y="4" width="66" height="10" uuid="23ea1acd-4053-42b2-9fa9-abae77fd5489"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Total Geral: "]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement positionType="Float" x="502" y="4" width="33" height="10" uuid="6f5f8391-7ebe-4585-8275-188479d17e7f"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalQuantidadePontuacao}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
