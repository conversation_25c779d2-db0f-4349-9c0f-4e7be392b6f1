<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_impressao_ficha_clinica_odontologica" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="a1b57cfd-9055-4cac-88e7-9178d405e0e1">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.7715610000000162"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="640"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<subDataset name="dataset1" uuid="f18f6fe8-e01e-4efb-b21b-080711fc4bcc"/>
	<parameter name="dataAtual" class="java.util.Date"/>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="tabelaCbo" class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"/>
	<field name="enderecoUsuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus"/>
	<field name="atendimentoOdontoFicha" class="br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOdontoFicha"/>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="atendimentoOdontoPlano" class="br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOdontoPlano"/>
	<field name="dente" class="br.com.ksisolucoes.vo.prontuario.basico.Dente"/>
	<field name="situacaoDente" class="br.com.ksisolucoes.vo.prontuario.basico.SituacaoDente"/>
	<field name="cidade" class="br.com.ksisolucoes.vo.basico.Cidade"/>
	<field name="tipoLogradouroCadsus" class="br.com.ksisolucoes.vo.cadsus.TipoLogradouroCadsus"/>
	<group name="odonto_ficha">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band height="274">
				<elementGroup>
					<rectangle radius="5">
						<reportElement key="rectangle-2" stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="7" width="555" height="264" forecolor="#000000" backcolor="#FFFFFF" uuid="c2ff62a7-50ac-4a90-961d-e9366a430ae2"/>
						<graphicElement fill="Solid">
							<pen lineWidth="0.5" lineStyle="Solid"/>
						</graphicElement>
					</rectangle>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" mode="Opaque" x="6" y="1" width="57" height="12" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
						<box leftPadding="2">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_anamnese")]]></textFieldExpression>
					</textField>
				</elementGroup>
				<elementGroup>
					<rectangle radius="5">
						<reportElement key="rectangle-2" positionType="Float" mode="Transparent" x="4" y="145" width="547" height="50" forecolor="#000000" backcolor="#FFFFFF" uuid="c2ff62a7-50ac-4a90-961d-e9366a430ae2"/>
						<graphicElement fill="Solid">
							<pen lineWidth="0.5" lineStyle="Solid"/>
						</graphicElement>
					</rectangle>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="Float" mode="Opaque" x="34" y="154" width="102" height="10" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
						<box leftPadding="4">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_problemas_cardiacos")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="Float" mode="Opaque" x="418" y="154" width="116" height="10" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
						<box leftPadding="4">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_problemas_pulmonares")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="274" y="168" width="10" height="10" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
						<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
							<pen lineWidth="1.0"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Arial" size="6" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[($F{atendimentoOdontoFicha}.getTontura().equals(1L)? "X":"")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="146" y="168" width="10" height="10" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
						<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
							<pen lineWidth="1.0"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Arial" size="6" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[($F{atendimentoOdontoFicha}.getAnemia().equals(1L)? "X":"")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="146" y="182" width="10" height="10" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
						<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
							<pen lineWidth="1.0"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Arial" size="6" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[($F{atendimentoOdontoFicha}.getHepatite().equals(1L)? "X":"")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="Float" mode="Opaque" x="287" y="182" width="103" height="10" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
						<box leftPadding="4">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_sifilis")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="403" y="168" width="10" height="10" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
						<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
							<pen lineWidth="1.0"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Arial" size="6" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[($F{atendimentoOdontoFicha}.getHipertensao().equals(1L)? "X":"")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="Float" mode="Opaque" x="34" y="182" width="102" height="10" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
						<box leftPadding="4">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_febre_reumatica")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="Float" mode="Opaque" x="159" y="168" width="106" height="10" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
						<box leftPadding="4">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_anemia")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="21" y="182" width="10" height="10" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
						<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
							<pen lineWidth="1.0"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Arial" size="6" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[($F{atendimentoOdontoFicha}.getFebreReumatica().equals(1L)? "X":"")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="274" y="182" width="10" height="10" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
						<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
							<pen lineWidth="1.0"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Arial" size="6" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[($F{atendimentoOdontoFicha}.getSifilis().equals(1L)? "X":"")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="21" y="168" width="10" height="10" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
						<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
							<pen lineWidth="1.0"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Arial" size="6" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[($F{atendimentoOdontoFicha}.getDiabete().equals(1L)? "X":"")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="403" y="154" width="10" height="10" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
						<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
							<pen lineWidth="1.0"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Arial" size="6" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[($F{atendimentoOdontoFicha}.getProblemasPulmonar().equals(1L)? "X":"")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="Float" mode="Opaque" x="34" y="168" width="102" height="10" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
						<box leftPadding="4">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_diabete")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="146" y="154" width="10" height="10" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
						<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
							<pen lineWidth="1.0"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Arial" size="6" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[($F{atendimentoOdontoFicha}.getProblemasFigado().equals(1L)? "X":"")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="Float" mode="Opaque" x="418" y="168" width="116" height="10" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
						<box leftPadding="4">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_hipertensao_arterial")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="274" y="154" width="10" height="10" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
						<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
							<pen lineWidth="1.0"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Arial" size="6" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[($F{atendimentoOdontoFicha}.getProblemasRins().equals(1L)? "X":"")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="Float" mode="Opaque" x="11" y="138" width="60" height="12" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
						<box leftPadding="2">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_ja_teve_ou_tem")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="Float" mode="Opaque" x="287" y="154" width="103" height="10" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
						<box leftPadding="4">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_problemas_rins")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="Float" mode="Opaque" x="418" y="182" width="116" height="10" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
						<box leftPadding="4">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tuberculose")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="Float" mode="Opaque" x="159" y="182" width="106" height="10" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
						<box leftPadding="4">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_hepatite")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="Float" mode="Opaque" x="287" y="168" width="103" height="10" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
						<box leftPadding="4">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tonturas")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="403" y="182" width="10" height="10" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
						<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
							<pen lineWidth="1.0"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Arial" size="6" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[($F{atendimentoOdontoFicha}.getTuberculose().equals(1L)? "X":"")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="Float" mode="Opaque" x="159" y="154" width="106" height="10" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
						<box leftPadding="4">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_problemas_figado")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="21" y="154" width="10" height="10" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
						<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
							<pen lineWidth="1.0"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Arial" size="6" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[($F{atendimentoOdontoFicha}.getProblemasCardiacos().equals(1L)? "X":"")]]></textFieldExpression>
					</textField>
				</elementGroup>
				<elementGroup>
					<rectangle radius="5">
						<reportElement key="rectangle-2" mode="Transparent" x="4" y="20" width="270" height="27" forecolor="#000000" backcolor="#FFFFFF" uuid="c2ff62a7-50ac-4a90-961d-e9366a430ae2"/>
						<graphicElement fill="Solid">
							<pen lineWidth="0.5" lineStyle="Solid"/>
						</graphicElement>
					</rectangle>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" mode="Opaque" x="11" y="14" width="159" height="12" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
						<box leftPadding="2">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_atualmente_tratamento_medico")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" x="16" y="29" width="48" height="11" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Arial" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[($F{atendimentoOdontoFicha}.getTratamentoMedico().equals(1L)? Bundle.getStringApplication("rotulo_sim"):Bundle.getStringApplication("rotulo_nao"))]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" x="64" y="29" width="204" height="11" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe">
							<printWhenExpression><![CDATA[$F{atendimentoOdontoFicha}.getTratamentoMedicoQual() != null]]></printWhenExpression>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Arial" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_qual")+" "+$F{atendimentoOdontoFicha}.getTratamentoMedicoQual()]]></textFieldExpression>
					</textField>
				</elementGroup>
				<elementGroup>
					<rectangle radius="5">
						<reportElement key="rectangle-2" mode="Transparent" x="281" y="20" width="270" height="27" forecolor="#000000" backcolor="#FFFFFF" uuid="c2ff62a7-50ac-4a90-961d-e9366a430ae2"/>
						<graphicElement fill="Solid">
							<pen lineWidth="0.5" lineStyle="Solid"/>
						</graphicElement>
					</rectangle>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" mode="Opaque" x="288" y="14" width="186" height="12" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
						<box leftPadding="2">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_doente_ou_hospitalizada")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" x="292" y="29" width="48" height="11" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Arial" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[($F{atendimentoOdontoFicha}.getDoenteHospitalizado().equals(1L)? Bundle.getStringApplication("rotulo_sim"):Bundle.getStringApplication("rotulo_nao"))]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" x="340" y="29" width="209" height="11" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe">
							<printWhenExpression><![CDATA[$F{atendimentoOdontoFicha}.getDoenteHospitalizadoMotivo() != null]]></printWhenExpression>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Arial" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{atendimentoOdontoFicha}.getDoenteHospitalizadoMotivo() != null ? Bundle.getStringApplication("rotulo_motivo") + ": " + $F{atendimentoOdontoFicha}.getDoenteHospitalizadoMotivo() : null]]></textFieldExpression>
					</textField>
				</elementGroup>
				<elementGroup>
					<rectangle radius="5">
						<reportElement key="rectangle-2" stretchType="RelativeToTallestObject" mode="Transparent" x="4" y="55" width="547" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="c2ff62a7-50ac-4a90-961d-e9366a430ae2"/>
						<graphicElement fill="Solid">
							<pen lineWidth="0.5" lineStyle="Solid"/>
						</graphicElement>
					</rectangle>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement key="textField-9" x="63" y="61" width="486" height="14" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe">
							<printWhenExpression><![CDATA[$F{atendimentoOdontoFicha}.getUsaMedicamentoQual() != null]]></printWhenExpression>
						</reportElement>
						<box bottomPadding="2" rightPadding="2">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Arial" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_qual")+" "+$F{atendimentoOdontoFicha}.getUsaMedicamentoQual()]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" mode="Opaque" x="11" y="49" width="136" height="12" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
						<box leftPadding="2">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_usando_medicamento")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" x="15" y="61" width="48" height="11" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Arial" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[($F{atendimentoOdontoFicha}.getUsaMedicamento().equals(1L)? Bundle.getStringApplication("rotulo_sim"):Bundle.getStringApplication("rotulo_nao"))]]></textFieldExpression>
					</textField>
				</elementGroup>
				<elementGroup>
					<rectangle radius="5">
						<reportElement key="rectangle-2" positionType="Float" mode="Transparent" x="4" y="109" width="270" height="27" forecolor="#000000" backcolor="#FFFFFF" uuid="c2ff62a7-50ac-4a90-961d-e9366a430ae2"/>
						<graphicElement fill="Solid">
							<pen lineWidth="0.5" lineStyle="Solid"/>
						</graphicElement>
					</rectangle>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="Float" mode="Opaque" x="11" y="102" width="259" height="12" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
						<box leftPadding="2">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_sangrou_anormal_extacao_dentaria")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="16" y="117" width="48" height="11" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Arial" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[($F{atendimentoOdontoFicha}.getSangrouExtracaoFerimento().equals(1L)? Bundle.getStringApplication("rotulo_sim"):Bundle.getStringApplication("rotulo_nao"))]]></textFieldExpression>
					</textField>
				</elementGroup>
				<elementGroup>
					<rectangle radius="5">
						<reportElement key="rectangle-2" positionType="Float" mode="Transparent" x="281" y="109" width="270" height="27" forecolor="#000000" backcolor="#FFFFFF" uuid="c2ff62a7-50ac-4a90-961d-e9366a430ae2"/>
						<graphicElement fill="Solid">
							<pen lineWidth="0.5" lineStyle="Solid"/>
						</graphicElement>
					</rectangle>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="340" y="117" width="209" height="11" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe">
							<printWhenExpression><![CDATA[$F{atendimentoOdontoFicha}.getAlergicoAnestesiaQual() != null]]></printWhenExpression>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Arial" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{atendimentoOdontoFicha}.getAlergicoAnestesiaQual() != null ? Bundle.getStringApplication("rotulo_qual")+" "+$F{atendimentoOdontoFicha}.getAlergicoAnestesiaQual() : null]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="Float" mode="Opaque" x="288" y="102" width="141" height="12" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
						<box leftPadding="2">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_alergico_anestesia")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="292" y="117" width="48" height="11" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Arial" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[($F{atendimentoOdontoFicha}.getAlergicoAnestesia().equals(1L)
    ?
        Bundle.getStringApplication("rotulo_sim")
    :
        $F{atendimentoOdontoFicha}.getAlergicoAnestesia().equals(2L)
            ?
                Bundle.getStringApplication("rotulo_nao_sabe")
            :
                Bundle.getStringApplication("rotulo_nao"))]]></textFieldExpression>
					</textField>
				</elementGroup>
				<elementGroup>
					<rectangle radius="5">
						<reportElement key="rectangle-2" positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="4" y="204" width="547" height="27" forecolor="#000000" backcolor="#FFFFFF" uuid="c2ff62a7-50ac-4a90-961d-e9366a430ae2"/>
						<graphicElement fill="Solid">
							<pen lineWidth="0.5" lineStyle="Solid"/>
						</graphicElement>
					</rectangle>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="62" y="213" width="487" height="17" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe">
							<printWhenExpression><![CDATA[$F{atendimentoOdontoFicha}.getEnfermidadeNaoMensionadaQual() != null]]></printWhenExpression>
						</reportElement>
						<box bottomPadding="2" rightPadding="2">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Arial" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_qual")+" "+$F{atendimentoOdontoFicha}.getEnfermidadeNaoMensionadaQual()]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="15" y="213" width="47" height="11" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Arial" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[($F{atendimentoOdontoFicha}.getEnfermidadeNaoMencionada().equals(1L)? Bundle.getStringApplication("rotulo_sim"):Bundle.getStringApplication("rotulo_nao"))]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="Float" mode="Opaque" x="10" y="198" width="181" height="12" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
						<box leftPadding="2">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_enfermidade_nao_mencionada")]]></textFieldExpression>
					</textField>
				</elementGroup>
				<elementGroup>
					<rectangle radius="5">
						<reportElement key="rectangle-2" positionType="Float" mode="Transparent" x="4" y="239" width="270" height="27" forecolor="#000000" backcolor="#FFFFFF" uuid="c2ff62a7-50ac-4a90-961d-e9366a430ae2"/>
						<graphicElement fill="Solid">
							<pen lineWidth="0.5" lineStyle="Solid"/>
						</graphicElement>
					</rectangle>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="Float" mode="Opaque" x="10" y="232" width="97" height="12" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
						<box leftPadding="2">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_mulheres_esta_gravida")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="16" y="249" width="25" height="11" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Arial" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[($F{atendimentoOdontoFicha}.getGravida().equals(1L)? Bundle.getStringApplication("rotulo_sim"):Bundle.getStringApplication("rotulo_nao"))]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="55" y="249" width="65" height="11" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe">
							<printWhenExpression><![CDATA[$F{atendimentoOdontoFicha}.getGravidaMesGestacao() != null]]></printWhenExpression>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Arial" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_mes_gestacao") + ": "]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="120" y="249" width="45" height="11" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Arial" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[Data.getDescricaoMes($F{atendimentoOdontoFicha}.getGravidaMesGestacao().intValue() - 1)]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="259" y="249" width="15" height="11" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Arial" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{atendimentoOdontoFicha}.getSemanaGestacao()]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="178" y="249" width="80" height="11" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe">
							<printWhenExpression><![CDATA[$F{atendimentoOdontoFicha}.getSemanaGestacao() != null]]></printWhenExpression>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Arial" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_semana_gestacao") + ": "]]></textFieldExpression>
					</textField>
				</elementGroup>
				<elementGroup>
					<rectangle radius="5">
						<reportElement key="rectangle-2" positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="4" y="81" width="547" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="c2ff62a7-50ac-4a90-961d-e9366a430ae2"/>
						<graphicElement fill="Solid">
							<pen lineWidth="0.5" lineStyle="Solid"/>
						</graphicElement>
					</rectangle>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="63" y="86" width="486" height="15" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe">
							<printWhenExpression><![CDATA[$F{atendimentoOdontoFicha}.getAlergicoMedicamentoQual() != null]]></printWhenExpression>
						</reportElement>
						<box bottomPadding="2" rightPadding="2">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Arial" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{atendimentoOdontoFicha}.getAlergicoMedicamentoQual() != null ? Bundle.getStringApplication("rotulo_qual")+" "+$F{atendimentoOdontoFicha}.getAlergicoMedicamentoQual() : ""]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="Float" mode="Opaque" x="11" y="76" width="257" height="12" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
						<box leftPadding="2">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_alergico_reacao_anormal_medicamento")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="Float" x="15" y="86" width="48" height="11" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Arial" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[($F{atendimentoOdontoFicha}.getAlergicoMedicamento().equals(1L)
    ?
        Bundle.getStringApplication("rotulo_sim")
    :
        $F{atendimentoOdontoFicha}.getAlergicoMedicamento().equals(2L)
            ?
                Bundle.getStringApplication("rotulo_nao_sabe")
            :
                Bundle.getStringApplication("rotulo_nao"))]]></textFieldExpression>
					</textField>
				</elementGroup>
			</band>
		</groupHeader>
	</group>
	<group name="odontograma" isStartNewPage="true">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band height="144" splitType="Prevent">
				<rectangle radius="5">
					<reportElement key="rectangle-2" mode="Transparent" x="0" y="2" width="555" height="138" forecolor="#000000" backcolor="#FFFFFF" uuid="c2ff62a7-50ac-4a90-961d-e9366a430ae2"/>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</rectangle>
				<image scaleImage="FillFrame">
					<reportElement x="51" y="18" width="448" height="105" uuid="87a6d54e-27a3-4c36-9487-583f96a16e31"/>
					<imageExpression><![CDATA["/br/com/ksisolucoes/imagens/odonto/odontograma.png"]]></imageExpression>
				</image>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-13" mode="Transparent" x="212" y="4" width="124" height="12" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
					<box leftPadding="4">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_odontograma")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="96" splitType="Prevent">
				<rectangle radius="10">
					<reportElement positionType="Float" x="-1" y="4" width="557" height="90" uuid="f973d9cb-5a1f-42f7-9a06-53cdbd1b76e0"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField-13" positionType="Float" mode="Transparent" x="-1" y="15" width="557" height="17" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
					<box leftPadding="30" rightPadding="30">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("termo_declaracao_relatorio_ficha_clinica_odontologico")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-13" positionType="Float" mode="Transparent" x="152" y="64" width="250" height="13" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
					<box leftPadding="4">
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_assinatura_paciente_responsavel")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-9" positionType="Float" x="152" y="80" width="250" height="11" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{cidade}.getDescricao()+", "+Data.formatar($P{dataAtual})]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="cabecalho_plano_tratamento" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band height="39" splitType="Stretch">
				<textField isBlankWhenNull="true">
					<reportElement key="textField-13" mode="Transparent" x="264" y="24" width="285" height="12" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
					<box leftPadding="0">
						<pen lineWidth="0.0"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_observacao")]]></textFieldExpression>
				</textField>
				<rectangle radius="5">
					<reportElement key="rectangle-2" mode="Transparent" x="0" y="3" width="117" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="c2ff62a7-50ac-4a90-961d-e9366a430ae2"/>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</rectangle>
				<line>
					<reportElement x="117" y="12" width="439" height="1" uuid="5d61e493-c810-424c-9bb4-94497dbc0728"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-13" mode="Transparent" x="127" y="24" width="132" height="12" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
					<box leftPadding="0">
						<pen lineWidth="0.0"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_situacao")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-13" mode="Transparent" x="7" y="6" width="103" height="12" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
					<box leftPadding="2">
						<pen lineWidth="0.0"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_plano_de_tratamento")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-13" mode="Transparent" x="39" y="24" width="83" height="12" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
					<box leftPadding="0">
						<pen lineWidth="0.0"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_face")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-13" mode="Transparent" x="4" y="24" width="30" height="12" uuid="8de76cfb-6921-42cb-b289-da9d058e090d"/>
					<box leftPadding="0">
						<pen lineWidth="0.0"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dente")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="114" splitType="Stretch">
			<rectangle radius="5">
				<reportElement key="rectangle-2" mode="Transparent" x="0" y="35" width="555" height="77" forecolor="#000000" backcolor="#FFFFFF" uuid="686fa24f-d548-415b-a7b8-77d9095b2ed3"/>
				<graphicElement fill="Solid">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<rectangle radius="5">
				<reportElement key="rectangle-2" mode="Transparent" x="0" y="10" width="410" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="686fa24f-d548-415b-a7b8-77d9095b2ed3"/>
				<graphicElement fill="Solid">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-13" mode="Opaque" x="7" y="3" width="92" height="12" uuid="eb0b239b-8c12-4280-bfa5-0b58271826d9"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade_saude")]]></textFieldExpression>
			</textField>
			<rectangle radius="5">
				<reportElement key="rectangle-2" mode="Transparent" x="420" y="10" width="135" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="686fa24f-d548-415b-a7b8-77d9095b2ed3"/>
				<graphicElement fill="Solid">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-13" mode="Opaque" x="426" y="3" width="105" height="12" uuid="eb0b239b-8c12-4280-bfa5-0b58271826d9"/>
				<box leftPadding="4">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_inicio_tratamento")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="437" y="13" width="99" height="11" uuid="716be8c8-012c-4c6a-992d-0bcd9552253a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.formatar($F{atendimentoOdontoFicha}.getDataInicioTratamento())]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="15" y="13" width="365" height="11" uuid="716be8c8-012c-4c6a-992d-0bcd9552253a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresa}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="90" y="76" width="60" height="11" uuid="716be8c8-012c-4c6a-992d-0bcd9552253a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.getDescricaoIdadeSimples($F{usuarioCadsus}.getDataNascimento())]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="89" y="65" width="45" height="11" uuid="aa62a152-7931-4184-af14-bf728d062f8d"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_idade")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="162" y="65" width="42" height="11" uuid="aa62a152-7931-4184-af14-bf728d062f8d"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_telefone")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="374" y="66" width="67" height="11" uuid="aa62a152-7931-4184-af14-bf728d062f8d"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_ocupacao")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="8" y="76" width="60" height="11" uuid="716be8c8-012c-4c6a-992d-0bcd9552253a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.formatar($F{usuarioCadsus}.getDataNascimento())]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="8" y="53" width="347" height="11" uuid="716be8c8-012c-4c6a-992d-0bcd9552253a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="246" y="88" width="60" height="11" uuid="0a2865fe-fbc4-4b43-a091-6342ee2c7b32"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_bairro")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="163" y="76" width="68" height="11" uuid="716be8c8-012c-4c6a-992d-0bcd9552253a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getTelefoneFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="488" y="53" width="53" height="11" uuid="716be8c8-012c-4c6a-992d-0bcd9552253a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getSexoFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="8" y="98" width="223" height="11" uuid="716be8c8-012c-4c6a-992d-0bcd9552253a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{tipoLogradouroCadsus}.getDescricao() != null ? $F{tipoLogradouroCadsus}.getDescricao() : "" )+" "
    +($F{enderecoUsuarioCadsus}.getLogradouro() != null ? $F{enderecoUsuarioCadsus}.getLogradouro()+", " : "")
        +($F{enderecoUsuarioCadsus}.getNumeroLogradouro() != null ? $F{enderecoUsuarioCadsus}.getNumeroLogradouro() : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="7" y="42" width="31" height="11" uuid="716be8c8-012c-4c6a-992d-0bcd9552253a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="247" y="99" width="223" height="11" uuid="716be8c8-012c-4c6a-992d-0bcd9552253a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{enderecoUsuarioCadsus}.getBairro()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="7" y="88" width="60" height="11" uuid="0a2865fe-fbc4-4b43-a091-6342ee2c7b32"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_endereco")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="487" y="42" width="25" height="11" uuid="f40f1a45-d437-4b14-9851-921068ecef69"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_sexo")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="7" y="65" width="82" height="11" uuid="0a2865fe-fbc4-4b43-a091-6342ee2c7b32"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_nascimento")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="246" y="65" width="44" height="11" uuid="ebb98a98-17c8-4da3-92d4-5debe418e091"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_celular")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-13" mode="Opaque" x="7" y="28" width="47" height="12" uuid="eb0b239b-8c12-4280-bfa5-0b58271826d9"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_paciente")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="375" y="76" width="181" height="11" uuid="716be8c8-012c-4c6a-992d-0bcd9552253a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tabelaCbo}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="247" y="76" width="90" height="11" uuid="716be8c8-012c-4c6a-992d-0bcd9552253a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getCelularFormatado()]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="15" splitType="Prevent">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-9" x="42" y="1" width="80" height="12" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimentoOdontoPlano}.getDescricaoFaceFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-9" x="6" y="1" width="28" height="12" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dente}.getNome()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-9" x="129" y="1" width="130" height="12" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{situacaoDente}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-9" x="267" y="1" width="282" height="12" uuid="653319f4-5ddc-43b7-8247-e5fa2a8fa9fe"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimentoOdontoPlano}.getObservacao()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
