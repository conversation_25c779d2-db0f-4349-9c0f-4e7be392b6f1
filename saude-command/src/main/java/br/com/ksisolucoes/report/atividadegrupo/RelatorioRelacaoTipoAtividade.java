package br.com.ksisolucoes.report.atividadegrupo;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.atividadegrupo.interfaces.dto.RelatorioRelacaoTipoAtividadeDTOParam;
import br.com.ksisolucoes.report.atividadegrupo.query.QueryRelatorioRelacaoTipoAtividade;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoTipoAtividade extends AbstractReport<RelatorioRelacaoTipoAtividadeDTOParam> {

    public RelatorioRelacaoTipoAtividade(RelatorioRelacaoTipoAtividadeDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/atividadegrupo/jrxml/relatorio_relacao_tipo_atividade.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_tipo_atividade");
    }

    @Override
    public ITransferDataReport getQuery() {
        addParametro("formaApresentacao", getParam().getFormaApresentacao());
        return new QueryRelatorioRelacaoTipoAtividade();
    }

}
