<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_boletim_procedimento" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="782" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="ecb58f21-fa88-4821-8c55-fb4ba574ac87">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.6105100000000008"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<style name="Crosstab Data Text" hAlign="Center"/>
	<parameter name="FORMA_APRESENTACAO" class="java.lang.String"/>
	<parameter name="AGRUPAR" class="java.lang.String" isForPrompting="false"/>
	<field name="descricaoFormatado" class="java.lang.String"/>
	<field name="quantidade" class="java.lang.Long"/>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="tabelaCbo" class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"/>
	<field name="procedimento" class="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"/>
	<field name="codigoProcedimento" class="java.lang.String"/>
	<field name="data" class="java.util.Date"/>
	<field name="convenio" class="br.com.ksisolucoes.vo.prontuario.basico.Convenio"/>
	<variable name="formaApresentacao" class="java.lang.String">
		<variableExpression><![CDATA[$P{FORMA_APRESENTACAO}.equals(Bundle.getStringApplication("rotulo_profissional"))
?
    $F{profissional}.getDescricaoFormatado()
:
    $P{FORMA_APRESENTACAO}.equals(Bundle.getStringApplication("rotulo_usuario_cadsus"))
    ?
        $F{usuarioCadsus}.getDescricaoFormatado()
    :
        $P{FORMA_APRESENTACAO}.equals(Bundle.getStringApplication("rotulo_cbo"))
        ?
            $F{tabelaCbo}.getCbo()!=null
            ?
                $F{tabelaCbo}.getDescricaoFormatado()
            :
                Bundle.getStringApplication("rotulo_sem_cadastro")
        :
            $P{FORMA_APRESENTACAO}.equals(Bundle.getStringApplication("rotulo_convenio"))
            ?
                $F{convenio}.getDescricaoFormatado()
            :
                ""]]></variableExpression>
	</variable>
	<group name="crosstab">
		<groupHeader>
			<band/>
		</groupHeader>
		<groupFooter>
			<band height="72">
				<crosstab>
					<reportElement uuid="9758ee39-4129-41a0-b8e7-11848e2500f9" key="crosstab" x="0" y="0" width="782" height="72"/>
					<crosstabParameter name="formaApresentacao">
						<parameterValueExpression><![CDATA[$P{FORMA_APRESENTACAO}]]></parameterValueExpression>
					</crosstabParameter>
					<crosstabParameter name="agrupar">
						<parameterValueExpression><![CDATA[$P{AGRUPAR}]]></parameterValueExpression>
					</crosstabParameter>
					<crosstabHeaderCell>
						<cellContents mode="Transparent">
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField isBlankWhenNull="false">
								<reportElement uuid="abfd090b-dfab-45df-b319-2fde1c950486" key="textField-1" x="0" y="0" width="81" height="13"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Middle">
									<font fontName="Arial" size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$P{formaApresentacao}]]></textFieldExpression>
							</textField>
							<textField isBlankWhenNull="false">
								<reportElement uuid="26bead88-dfbb-48f0-bb1c-39d24d0e39a7" key="textField-1" x="147" y="0" width="154" height="13"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Middle">
									<font fontName="Arial" size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_procedimento")]]></textFieldExpression>
							</textField>
							<textField isBlankWhenNull="false">
								<reportElement uuid="3527ac19-9070-4cd1-8aa4-c2cd48620e1c" key="textField-1" x="81" y="0" width="65" height="13"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Arial" size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_codigo")]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabHeaderCell>
					<rowGroup name="formaApresentacao" width="81" totalPosition="End">
						<bucket class="java.lang.String">
							<bucketExpression><![CDATA[$V{formaApresentacao}]]></bucketExpression>
						</bucket>
						<crosstabRowHeader>
							<cellContents mode="Transparent">
								<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textField isStretchWithOverflow="true" isBlankWhenNull="false">
									<reportElement uuid="87bd267e-bed2-46c7-b584-179af1ba16d7" key="textField" style="Crosstab Data Text" x="0" y="1" width="79" height="20"/>
									<box>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
									<textElement textAlignment="Left">
										<font fontName="Arial" size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$V{formaApresentacao}]]></textFieldExpression>
								</textField>
							</cellContents>
						</crosstabRowHeader>
						<crosstabTotalRowHeader>
							<cellContents backcolor="#FFFFFF" mode="Opaque">
								<box>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textField isBlankWhenNull="false">
									<reportElement uuid="95ecba6b-889f-4787-b482-2ef2b877f08a" key="textField-1" x="0" y="1" width="301" height="13"/>
									<box>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
									<textElement textAlignment="Right" verticalAlignment="Middle">
										<font fontName="Arial" size="7" isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_geral")]]></textFieldExpression>
								</textField>
							</cellContents>
						</crosstabTotalRowHeader>
					</rowGroup>
					<rowGroup name="codigoProcedimento" width="65" totalPosition="End">
						<bucket class="java.lang.String">
							<bucketExpression><![CDATA[$F{codigoProcedimento}]]></bucketExpression>
						</bucket>
						<crosstabRowHeader>
							<cellContents backcolor="#FFFFFF" mode="Opaque">
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textField isBlankWhenNull="false">
									<reportElement uuid="989e2426-7a47-4990-b92d-e31b9c92de2a" key="textField" style="Crosstab Data Text" x="0" y="0" width="65" height="13"/>
									<box>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
									<textElement verticalAlignment="Middle">
										<font fontName="Arial" size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$V{codigoProcedimento}]]></textFieldExpression>
								</textField>
							</cellContents>
						</crosstabRowHeader>
						<crosstabTotalRowHeader>
							<cellContents backcolor="#FFFFFF" mode="Opaque">
								<box>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textField isBlankWhenNull="false">
									<reportElement uuid="07fd8f2d-f730-46ed-833e-f1a8326feb3f" key="textField-1" x="0" y="6" width="220" height="13"/>
									<box>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
									<textElement textAlignment="Right" verticalAlignment="Middle">
										<font fontName="Arial" size="7" isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
								</textField>
							</cellContents>
						</crosstabTotalRowHeader>
					</rowGroup>
					<rowGroup name="descricaoFormatado" width="155" totalPosition="End">
						<bucket class="java.lang.String">
							<bucketExpression><![CDATA[$F{descricaoFormatado}]]></bucketExpression>
						</bucket>
						<crosstabRowHeader>
							<cellContents backcolor="#FFFFFF" mode="Opaque">
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textField isStretchWithOverflow="true" isBlankWhenNull="false">
									<reportElement uuid="27dffdbf-0cff-4975-902c-ff7e0b2be1a9" key="textField" style="Crosstab Data Text" x="0" y="0" width="155" height="13"/>
									<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
									<textElement textAlignment="Left" verticalAlignment="Middle">
										<font fontName="Arial" size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$V{descricaoFormatado}]]></textFieldExpression>
								</textField>
							</cellContents>
						</crosstabRowHeader>
						<crosstabTotalRowHeader>
							<cellContents/>
						</crosstabTotalRowHeader>
					</rowGroup>
					<columnGroup name="dia" height="13" totalPosition="End" headerPosition="Center">
						<bucket class="java.util.Date">
							<bucketExpression><![CDATA[$F{data}]]></bucketExpression>
						</bucket>
						<crosstabColumnHeader>
							<cellContents backcolor="#FFFFFF" mode="Opaque">
								<box>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textField isBlankWhenNull="false">
									<reportElement uuid="0d1a0038-4f38-47d4-b738-07fac01e1f63" key="textField" style="Crosstab Data Text" x="0" y="0" width="18" height="13"/>
									<box>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
									<textElement verticalAlignment="Middle">
										<font fontName="Arial" size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dia").equals($P{agrupar})
?
	Data.formatarDia($V{dia})
:
	Bundle.getStringApplication("rotulo_competencia").equals($P{agrupar})
	?
		Data.formatarMes($V{dia})
	:
		null]]></textFieldExpression>
								</textField>
							</cellContents>
						</crosstabColumnHeader>
						<crosstabTotalColumnHeader>
							<cellContents backcolor="#FFFFFF" mode="Opaque">
								<box>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textField isBlankWhenNull="false">
									<reportElement uuid="6202fdec-b6fb-4f4a-8f50-8e9055734633" key="textField-1" x="2" y="0" width="37" height="13"/>
									<box>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font fontName="Arial" size="7" isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
								</textField>
							</cellContents>
						</crosstabTotalColumnHeader>
					</columnGroup>
					<measure name="quantidadeMeasure" class="java.lang.Long" calculation="Sum">
						<measureExpression><![CDATA[$F{quantidade}]]></measureExpression>
					</measure>
					<crosstabCell width="18" height="13">
						<cellContents mode="Transparent">
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField isBlankWhenNull="false">
								<reportElement uuid="1b5dabd4-943a-4f46-bb96-d5afd8126286" key="textField" style="Crosstab Data Text" x="0" y="0" width="18" height="13"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement verticalAlignment="Middle">
									<font fontName="Arial" size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{quantidadeMeasure}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabCell>
					<crosstabCell width="18" height="24" rowTotalGroup="codigoProcedimento">
						<cellContents backcolor="#FFFFFF" mode="Opaque">
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField isBlankWhenNull="false">
								<reportElement uuid="f3b41614-d3c7-4bf9-8216-f955344c400d" key="textField" style="Crosstab Data Text" x="0" y="6" width="18" height="13" forecolor="#000000"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement verticalAlignment="Middle">
									<font fontName="Arial" size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{quantidadeMeasure}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabCell>
					<crosstabCell width="39" height="13" columnTotalGroup="dia">
						<cellContents backcolor="#FFFFFF" mode="Opaque">
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField isBlankWhenNull="false">
								<reportElement uuid="50b4d3e6-6692-4dce-a419-311560fe30d4" key="textField" style="Crosstab Data Text" x="2" y="0" width="37" height="13"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement verticalAlignment="Middle">
									<font fontName="Arial" size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{quantidadeMeasure}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabCell>
					<crosstabCell width="39" height="24" rowTotalGroup="codigoProcedimento" columnTotalGroup="dia">
						<cellContents backcolor="#FFFFFF" mode="Opaque">
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField isBlankWhenNull="false">
								<reportElement uuid="8d64354e-1a69-496e-b6a9-f7dd687217e2" key="textField" style="Crosstab Data Text" x="2" y="6" width="37" height="13"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement verticalAlignment="Middle">
									<font fontName="Arial" size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{quantidadeMeasure}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabCell>
					<crosstabCell width="18" height="0" rowTotalGroup="descricaoFormatado">
						<cellContents backcolor="#BFE1FF" mode="Opaque">
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</cellContents>
					</crosstabCell>
					<crosstabCell width="39" height="0" rowTotalGroup="descricaoFormatado" columnTotalGroup="dia">
						<cellContents backcolor="#BFE1FF" mode="Opaque">
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</cellContents>
					</crosstabCell>
					<crosstabCell width="18" height="22" rowTotalGroup="formaApresentacao">
						<cellContents backcolor="#FFFFFF" mode="Transparent">
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField isBlankWhenNull="false">
								<reportElement uuid="117b586e-4979-4594-b838-e14adf2bd025" key="textField-1" style="Crosstab Data Text" mode="Transparent" x="0" y="1" width="18" height="13" forecolor="#000000"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement verticalAlignment="Middle">
									<font fontName="Arial" size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{quantidadeMeasure}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabCell>
					<crosstabCell width="39" height="22" rowTotalGroup="formaApresentacao" columnTotalGroup="dia">
						<cellContents backcolor="#FFFFFF" mode="Transparent">
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField isBlankWhenNull="false">
								<reportElement uuid="c0435a9c-49fb-4a63-8ac9-e6a32014a20b" key="textField" style="Crosstab Data Text" x="2" y="1" width="37" height="13" forecolor="#000000"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement verticalAlignment="Middle">
									<font fontName="Arial" size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{quantidadeMeasure}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabCell>
					<whenNoDataCell>
						<cellContents mode="Transparent">
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</cellContents>
					</whenNoDataCell>
				</crosstab>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="15" splitType="Stretch">
			<textField isBlankWhenNull="false">
				<reportElement uuid="d3235267-ba32-443c-a3c4-7b0bd6f97d60" key="textField" x="304" y="0" width="127" height="15"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement/>
				<textFieldExpression><![CDATA[$P{AGRUPAR}.equals(Bundle.getStringApplication("rotulo_dia"))
?
	Bundle.getStringApplication("rotulo_dias_uteis")
:
	Bundle.getStringApplication("rotulo_competencia")]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band splitType="Stretch"/>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
