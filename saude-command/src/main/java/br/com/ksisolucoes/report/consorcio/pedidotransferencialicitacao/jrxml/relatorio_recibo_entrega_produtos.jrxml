<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_recibo_entrega_produtos" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="0e1167b4-a5ae-486b-b46c-52ab9980f3c2">
	<property name="ireport.zoom" value="3.2153832150000143"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="395"/>
	<import value="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAnamnese"/>
	<import value="br.com.ksisolucoes.report.encaminhamento.dto.RelacaoAgendamentoTfdDTOParam.FormaApresentacao"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="formaApresentacao" class="br.com.ksisolucoes.report.encaminhamento.dto.RelacaoAgendamentoTfdDTOParam.FormaApresentacao"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="codigo" class="java.lang.Long"/>
	<field name="dataEntrega" class="java.util.Date"/>
	<field name="responsavelEntrega" class="java.lang.String"/>
	<field name="empresaConsorciado" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="produto" class="br.com.ksisolucoes.vo.entradas.estoque.Produto"/>
	<field name="quantidade" class="java.lang.Long"/>
	<field name="lote" class="java.lang.String"/>
	<field name="dataValidade" class="java.util.Date"/>
	<field name="romaneio" class="java.lang.Long"/>
	<field name="valor" class="java.lang.Double"/>
	<field name="empresaAlmoxarifado" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="precoUnitario" class="java.lang.Double"/>
	<field name="fabricante" class="java.lang.String"/>
	<field name="pedidoTransferenciaLicitacaoEntrega" class="br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacaoEntrega"/>
	<field name="tipoConta" class="java.lang.String"/>
	<variable name="total" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{valor}]]></variableExpression>
	</variable>
	<group name="total">
		<groupFooter>
			<band height="13">
				<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement x="316" y="1" width="50" height="12" uuid="7ea9ea2a-**************-98685212f7b8"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{total}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="291" y="1" width="75" height="1" uuid="3344cee9-6015-4822-83d1-7e6b6129d9ca"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField>
					<reportElement x="262" y="1" width="54" height="12" uuid="7ee469c0-3143-435f-85c5-4c7091875682"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")+":"]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="213" splitType="Stretch">
			<rectangle radius="5">
				<reportElement x="1" y="9" width="556" height="172" uuid="30ff4716-0c00-4b67-a75a-cfe5429e3048"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="5" y="19" width="40" height="14" uuid="7ee469c0-3143-435f-85c5-4c7091875682"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_pedido")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="47" y="19" width="128" height="14" uuid="7ea9ea2a-**************-98685212f7b8"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{codigo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="434" y="19" width="85" height="14" uuid="7ea9ea2a-**************-98685212f7b8"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataEntrega}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="352" y="19" width="80" height="14" uuid="7ee469c0-3143-435f-85c5-4c7091875682"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_entrega")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="75" y="107" width="151" height="14" uuid="7ea9ea2a-**************-98685212f7b8"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresaConsorciado}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="107" width="70" height="14" uuid="7ee469c0-3143-435f-85c5-4c7091875682"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_consorciado")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="17" y="2" width="86" height="14" uuid="7ee469c0-3143-435f-85c5-4c7091875682"/>
				<box leftPadding="2"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_do_pedido")]]></textFieldExpression>
			</textField>
			<rectangle radius="5">
				<reportElement x="0" y="192" width="556" height="21" uuid="30ff4716-0c00-4b67-a75a-cfe5429e3048"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement mode="Opaque" x="17" y="183" width="30" height="14" uuid="7ee469c0-3143-435f-85c5-4c7091875682"/>
				<box leftPadding="2"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_itens")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="3" y="199" width="220" height="14" uuid="7ee469c0-3143-435f-85c5-4c7091875682"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_produto")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="222" y="199" width="18" height="14" uuid="7ee469c0-3143-435f-85c5-4c7091875682"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_un")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="240" y="199" width="30" height="14" uuid="7ee469c0-3143-435f-85c5-4c7091875682"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quantidade_abv")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="366" y="199" width="40" height="14" uuid="7ee469c0-3143-435f-85c5-4c7091875682"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_lote")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="406" y="199" width="40" height="14" uuid="7ee469c0-3143-435f-85c5-4c7091875682"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_validade")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="267" y="19" width="58" height="14" uuid="7ea9ea2a-**************-98685212f7b8"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{romaneio}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="193" y="19" width="72" height="14" uuid="7ee469c0-3143-435f-85c5-4c7091875682"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_guia_saida")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="316" y="199" width="50" height="14" uuid="7ee469c0-3143-435f-85c5-4c7091875682"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_valor")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="123" width="54" height="14" uuid="b4fe1bf1-f104-4dc1-9e7e-8cbeea2de786"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Endereço:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="59" y="123" width="493" height="14" uuid="58cd4d75-55bd-4704-9b3f-26c4e5aa1e38"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresaConsorciado}.getEnderecoCidadeBairroFormatado()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="139" width="36" height="14" uuid="f50f020b-9f08-4b8f-ba8d-2c445c68af75"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["CNPJ:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="41" y="139" width="511" height="14" uuid="c311f7d1-843d-4f1c-949d-8a1f79ee25cc"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresaConsorciado}.getCnpjFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="59" y="51" width="494" height="14" uuid="eed7fca8-83f6-46e1-b5a8-f607d31fc190"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresaAlmoxarifado}.getEnderecoCidadeBairroFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="41" y="67" width="511" height="14" uuid="3d22c2e0-00e0-439d-ad8e-d48670aac87e"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresaAlmoxarifado}.getCnpjFormatado()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="35" width="58" height="14" uuid="acc52a32-4a5d-4f28-b8ed-9b6466853f7c"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Consórcio:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="63" y="35" width="490" height="14" uuid="14e19a08-b0a1-4041-b05d-396f7085ce45"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresaAlmoxarifado}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="51" width="54" height="14" uuid="43e89723-297f-4dba-8099-c3bae2d9a3b5"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Endereço:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="67" width="35" height="14" uuid="19d00b83-c47b-44dc-95a6-ef5044e9515f"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["CNPJ:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="270" y="199" width="46" height="14" uuid="19629dc3-c446-4beb-9d10-f5fe81e12071"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["P. Unitário"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="153" y="83" width="399" height="14" uuid="7ea9ea2a-**************-98685212f7b8"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{responsavelEntrega}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="83" width="148" height="14" uuid="7ee469c0-3143-435f-85c5-4c7091875682"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_responsavel_pela_separacao")+":"]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="5" y="97" width="546" height="1" uuid="e3527a00-9241-48f2-a685-0cf492ecce63"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField>
				<reportElement x="447" y="199" width="107" height="14" uuid="e3b6bc5b-f6e0-47bf-8d38-4fac02b4f128"/>
				<box leftPadding="6"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_fabricante")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="153" width="54" height="14" uuid="ca80fc51-a185-40d2-8bca-8675d73eff29"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["N°Guia:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="58" y="153" width="246" height="14" uuid="677dd5ee-0d11-41ab-9512-0092ba85bcba"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{romaneio}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="167" width="84" height="14" uuid="2133c08d-e164-475e-8f04-8ddcc33da281"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Tipo de Conta:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="89" y="167" width="246" height="14" uuid="f4a3462a-e852-4a79-b040-68dd2b57aab0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipoConta}]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="10" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="2" y="0" width="220" height="10" uuid="7ea9ea2a-**************-98685212f7b8"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produto}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="222" y="0" width="18" height="10" uuid="7ea9ea2a-**************-98685212f7b8"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produto}.getUnidade().getUnidade()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="240" y="0" width="30" height="10" uuid="7ea9ea2a-**************-98685212f7b8"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidade}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="366" y="0" width="40" height="10" uuid="7ea9ea2a-**************-98685212f7b8"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{lote}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="406" y="0" width="40" height="10" uuid="7ea9ea2a-**************-98685212f7b8"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataValidade}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement x="316" y="0" width="50" height="10" uuid="7ea9ea2a-**************-98685212f7b8"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{valor}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.0000;-###0.0000" isBlankWhenNull="true">
				<reportElement x="270" y="0" width="46" height="10" uuid="6fca1191-b86a-4bca-a516-49da2547df1c"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{precoUnitario}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="446" y="0" width="107" height="10" uuid="24e76a44-7f47-4940-980b-534ce6eed029"/>
				<box leftPadding="6"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{fabricante}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="56" splitType="Stretch">
			<line>
				<reportElement x="24" y="33" width="220" height="1" uuid="3344cee9-6015-4822-83d1-7e6b6129d9ca"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField>
				<reportElement x="24" y="34" width="220" height="14" uuid="7ee469c0-3143-435f-85c5-4c7091875682"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_ass_resp_expedicao")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="305" y="33" width="220" height="14" uuid="7ee469c0-3143-435f-85c5-4c7091875682"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_ass_resp_coleta")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="305" y="32" width="220" height="1" uuid="3344cee9-6015-4822-83d1-7e6b6129d9ca"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
		</band>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
