package br.com.ksisolucoes.report.prontuario.exame;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.ImpressaoRequisicaoCargaViralHIVDTOParam;
import br.com.ksisolucoes.report.prontuario.exame.query.QueryImpressaoExameCargaViralHIV;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoExameCargaViralHIV extends AbstractReport<ImpressaoRequisicaoCargaViralHIVDTOParam> {

    public RelatorioImpressaoExameCargaViralHIV(ImpressaoRequisicaoCargaViralHIVDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryImpressaoExameCargaViralHIV();
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_exame_carga_viral_hiv.jrxml";
    }

    @Override
    public String getTitulo() {
        return "";
    }
}