/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.vigilancia;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioRequerimentoVigilanciaComprovanteDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.ReportConfig;
import br.com.ksisolucoes.report.embededreportconfig.PublicReportBuildInParametersConfig;
import br.com.ksisolucoes.report.vigilancia.query.QueryRelatorioRequerimentoVigilanciaComprovante;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoLicencaTransporte;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Objects;

import static br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao.TipoDocumento;
import static br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao.TipoDocumento.*;

/**
 * <AUTHOR>
 */
public class RelatorioRequerimentoVigilanciaComprovante extends AbstractReport<RelatorioRequerimentoVigilanciaComprovanteDTOParam> {

    private Long tipoDocumento;
    public RelatorioRequerimentoVigilanciaComprovante(RelatorioRequerimentoVigilanciaComprovanteDTOParam param) {
        super(param);
        this.tipoDocumento = getTipoDocumento(param.getRequerimentoVigilancia());
    }

    @Override
    public String getXML() {
        if (this.getSessao() != null) {
            addParametro("USUARIO", this.getSessao().<Usuario>getUsuario().getNome());
        }

        String realContext = TenantContext.getRealContext();
        if (realContext.equals("localhost")) {
            realContext = realContext.concat(":8080/vigilancia");
        } else {
            realContext = realContext.concat("/vigilancia");
        }
        String sb = "Ou acesse o link " + realContext + ", clique no botão consulta do requerimento e informe a senha " + param.getRequerimentoVigilancia().getCodigo() + " e acompanhe o andamento de sua solicitação.";
        String msgValorLegal = getValorLegalPorTipoRequerimento();

        addParametro("MENSAGEM_VALOR_LEGAL_REQUERIMENTO", msgValorLegal);
        addParametro("MENSAGEM_CHAVE_CONSULTA_REQUERIMENTO", sb);
        addParametro("PROTOCOLO", param.getRequerimentoVigilancia().getProtocoloFormatado());
        addParametro("origem", param.getOrigem());
        addParametro("SETOR", param.getRequerimentoVigilancia().getSetorFormatado());
        addParametro("renovacaoLicencaTransporte", isRequerimentoLicencaTransporteRenovacao(param.getRequerimentoVigilancia().getCodigo()) ? RepositoryComponentDefault.SIM : RepositoryComponentDefault.NAO);

        if (param.getQRCodeParam() != null) {
            addParametro("urlQRcode", param.getQRCodeParam().generateURL());
        }

        return "/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_requerimento_vigilancia_comprovante.jrxml";
    }

    @NotNull
    private String getValorLegalPorTipoRequerimento() {
        ArrayList<TipoDocumento> documentosTipoAlvara = getDocumentosTipoAlvara();
        ArrayList<TipoDocumento> documentosTipoAlteracao = getDocumentosTipoAlteracao();
        ArrayList<TipoDocumento> documentosTipoBaixaPropriedade = getDocumentosTipoBaixaPropriedade();
        ArrayList<TipoDocumento> documentosTipoResponsTec = getDocumentosTipoResponsTec();
        ArrayList<TipoDocumento> documentosTipoDeclaracao = getDocumentosTipoDeclaracao();

        String valorLegal = "";
        if (TipoDocumento.valueOf(tipoDocumento) != null) {
            String descricaoTipoDocum = "";

            if(documentosTipoAlvara.contains(TipoDocumento.valueOf(tipoDocumento)))
                descricaoTipoDocum = "O alvará solicitado";
            else if(documentosTipoAlteracao.contains(TipoDocumento.valueOf(tipoDocumento)))
                descricaoTipoDocum = "A alteração social solicitada";
            else if(documentosTipoBaixaPropriedade.contains(TipoDocumento.valueOf(tipoDocumento)))
                descricaoTipoDocum = "A baixa solicitada";
            else if(documentosTipoResponsTec.contains(TipoDocumento.valueOf(tipoDocumento)))
                descricaoTipoDocum = "O ajuste de RT solicitado";
            else if(documentosTipoDeclaracao.contains(TipoDocumento.valueOf(tipoDocumento)))
                descricaoTipoDocum = "A declaração Solicitada";
            else if (Objects.equals(TipoDocumento.valueOf(tipoDocumento), AUTORIZACAO_SANITARIA))
                descricaoTipoDocum = "A autorização solicitada";
            else if (Objects.equals(TipoDocumento.valueOf(tipoDocumento), VISTORIA_HABITESE_SANITARIO))
                descricaoTipoDocum = "O habite-se solicitado";
            else if (Objects.equals(TipoDocumento.valueOf(tipoDocumento), VISTORIA_LAUDO_CONFORMIDADE_PBA))
                descricaoTipoDocum = "O laudo solicitado";
            else if (Objects.equals(TipoDocumento.valueOf(tipoDocumento), TREINAMENTOS_ALIMENTO))
                descricaoTipoDocum = "O credenciamento solicitado";
            else if (Objects.equals(TipoDocumento.valueOf(tipoDocumento), LICENCA_TRANSPORTE))
                descricaoTipoDocum = "A licença solicitada";

            valorLegal = Bundle.getStringApplication("msg_valor_legal_do_requerimento", descricaoTipoDocum).toUpperCase();
        }
        return valorLegal;
    }

    @NotNull
    private ArrayList<TipoDocumento> getDocumentosTipoDeclaracao() {
        ArrayList<TipoDocumento> documentosTipoDeclaracao = new ArrayList<>();
        documentosTipoDeclaracao.add(DECLARACAO_CARTORIO);
        documentosTipoDeclaracao.add(DECLARACAO_VISA_PRODUTOS);
        documentosTipoDeclaracao.add(DECLARACAO_VISA_ISENCAO_TAXAS);
        documentosTipoDeclaracao.add(DECLARACAO_VISA_OUTROS);
        documentosTipoDeclaracao.add(CERTIDAO_NADA_CONSTA);
        documentosTipoDeclaracao.add(RESTITUICAO_TAXA);
        return documentosTipoDeclaracao;
    }

    @NotNull
    private ArrayList<TipoDocumento> getDocumentosTipoResponsTec() {
        ArrayList<TipoDocumento> documentos = new ArrayList<>();
        documentos.add(BAIXA_RESPONSABILIDADE_TECNICA);
        documentos.add(ENTRADA_RESPONSABILIDADE_TECNICA);
        return documentos;
    }

    @NotNull
    private ArrayList<TipoDocumento> getDocumentosTipoBaixaPropriedade() {
        ArrayList<TipoDocumento> documentos = new ArrayList<>();
        documentos.add(BAIXA_ESTABELECIMENTO);
        documentos.add(BAIXA_VEICULO);
        return documentos;
    }

    @NotNull
    private ArrayList<TipoDocumento> getDocumentosTipoAlteracao() {
        ArrayList<TipoDocumento> documentos = new ArrayList<>();
        documentos.add(ALTERACAO_RESPONSABILIDADE_LEGAL);
        documentos.add(ALTERACAO_ATIVIDADE_ECONOMICA);
        documentos.add(ALTERACAO_ENDERECO);
        documentos.add(ALTERACAO_RAZAO_SOCIAL);
        return documentos;
    }

    @NotNull
    private ArrayList<TipoDocumento> getDocumentosTipoAlvara() {
        ArrayList<TipoDocumento> documentos = new ArrayList<>();
        documentos.add(ALVARA_INICIAL);
        documentos.add(ALVARA_REVALIDACAO);
        documentos.add(LICENCA_SANITARIA);
        documentos.add(REVALIDACAO_LICENCA_SANITARIA);
        documentos.add(ALVARA_PARTICIPANTE_EVENTO);
        documentos.add(ALVARA_CADASTRO_EVENTO);
        documentos.add(ANALISE_PROJETOS);
        documentos.add(PROJETO_BASICO_ARQUITETURA);
        documentos.add(ANALISE_PROJETO_HIDROSSANITARIO);
        documentos.add(PROJETO_ARQUITETONICO_SANITARIO);
        return documentos;
    }

    @Override
    public String getTitulo() {
        if (DENUNCIA_RECLAMACAO.value().equals(tipoDocumento)) {
            return Bundle.getStringApplication("rotulo_comprovante_requerimento_denuncia");
        }
        return Bundle.getStringApplication("rotulo_comprovante_requerimento");
    }
    @Override
    public ReportConfig getReportBuildInParametersConfig() {
        if (this.getSessao() != null) {
            return super.getReportBuildInParametersConfig();
        }

        ConfiguracaoVigilancia configuracaoVigilancia = null;
        try {
            configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        } catch (ValidacaoException e) {
            Loggable.log.warn(e.getMessage(), e);
            return super.getReportBuildInParametersConfig();
        }

        return new PublicReportBuildInParametersConfig("", configuracaoVigilancia.getEmpresa().getDescricaoFormatado(), configuracaoVigilancia.getLinha1Cabecalho(), configuracaoVigilancia.getLinha2Cabecalho(), getTitulo());
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioRequerimentoVigilanciaComprovante();
    }

    private boolean isRequerimentoLicencaTransporteRenovacao(Long codigoRequerimento) {
        return LoadManager.getInstance(RequerimentoLicencaTransporte.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RequerimentoLicencaTransporte.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_CODIGO), codigoRequerimento))
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoLicencaTransporte.PROP_TIPO_LICENCA, RequerimentoLicencaTransporte.TipoLicenca.RENOVACAO.value()))
                .exists();
    }

    private Long getTipoDocumento(RequerimentoVigilancia requerimentoVigilancia) {
        Long tipoDocumento = param.getRequerimentoVigilancia().getTipoSolicitacao().getTipoDocumento();
        if (tipoDocumento == null) {
            RequerimentoVigilancia result = LoadManager.getInstance(RequerimentoVigilancia.class)
                    .setId(requerimentoVigilancia.getCodigo())
                    .start().getVO();
            tipoDocumento = result.getTipoDocumento();
        }

        return tipoDocumento;
    }


}
