package br.com.ksisolucoes.report.consorcio.pagamento.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioResumoRecibosMunicipioDTO;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioResumoRecibosMunicipioDTOParam;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.consorcio.ReciboConsorcio;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryResumoRecibosMunicipio extends CommandQuery<QueryResumoRecibosMunicipio> implements ITransferDataReport<RelatorioResumoRecibosMunicipioDTOParam, RelatorioResumoRecibosMunicipioDTO> {

    private RelatorioResumoRecibosMunicipioDTOParam param;
    private List<RelatorioResumoRecibosMunicipioDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("sum(reciboConsorcio.valor)", "valorTotal");
        hql.addToSelectAndGroup("consorciado.codigo", "consorciado.codigo");
        hql.addToSelectAndGroupAndOrder("consorciado.descricao", "consorciado.descricao");

        hql.setTypeSelect(RelatorioResumoRecibosMunicipioDTO.class.getName());
        hql.addToFrom("ReciboConsorcio reciboConsorcio" +
                " left join reciboConsorcio.empresa consorciado ");

        hql.addToWhereWhithAnd("reciboConsorcio.status = ", ReciboConsorcio.Status.EMITIDO.value());
        if (RepositoryComponentDefault.SIM_LONG.equals(param.getApenasRecibosGuias())) {
            hql.addToWhereWhithAnd(" exists (SELECT 1 FROM ReciboConsorcioGuia rcg where rcg.reciboConsorcio.codigo = reciboConsorcio.codigo)");
        }
        hql.addToWhereWhithAnd("consorciado = ", param.getConsorciado());

        if (param.getCompetencia() != null) {
            hql.addToWhereWhithAnd("reciboConsorcio.dataRecibo ", Data.getPeriodMonthDate(param.getCompetencia()));
        }
        if (param.getPeriodo() != null) {
            hql.addToWhereWhithAnd("reciboConsorcio.dataRecibo ", this.param.getPeriodo());
        }


        hql.addToOrder("consorciado.descricao");
    }

    @Override
    public void setDTOParam(RelatorioResumoRecibosMunicipioDTOParam param) {
        this.param = param;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    public List<RelatorioResumoRecibosMunicipioDTO> getResult() {
        return result;
    }

}
