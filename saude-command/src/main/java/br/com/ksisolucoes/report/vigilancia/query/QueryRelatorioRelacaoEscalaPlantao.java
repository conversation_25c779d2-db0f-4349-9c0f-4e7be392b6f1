package br.com.ksisolucoes.report.vigilancia.query;

import br.com.celk.vigilancia.dto.RelacaoEscalaPlantaoDTO;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QueryRelatorioRelacaoEscalaPlantaoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;

import java.util.Collection;
import java.util.List;

import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class QueryRelatorioRelacaoEscalaPlantao extends CommandQuery<QueryRelatorioRelacaoEscalaPlantao> implements ITransferDataReport<QueryRelatorioRelacaoEscalaPlantaoDTOParam, RelacaoEscalaPlantaoDTO> {

    private QueryRelatorioRelacaoEscalaPlantaoDTOParam param;
    private List<RelacaoEscalaPlantaoDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        RelacaoEscalaPlantaoDTO proxy = on(RelacaoEscalaPlantaoDTO.class);

        hql.setTypeSelect(RelacaoEscalaPlantaoDTO.class.getName());

        hql.addToSelectAndGroup("escalaPlantao.codigo", "escalaPlantao.codigo");
        hql.addToSelectAndGroup("escalaPlantao.dataFinal", "escalaPlantao.dataFinal");
        hql.addToSelectAndGroup("escalaPlantao.dataInicial", "escalaPlantao.dataInicial");
        hql.addToSelectAndGroup("escalaPlantao.descricaoDiaInicio", "escalaPlantao.descricaoDiaInicio");
        hql.addToSelectAndGroup("escalaPlantao.descricaoDiaFim", "escalaPlantao.descricaoDiaFim");
        hql.addToSelectAndGroup("escalaPlantao.horaInicial", "escalaPlantao.horaInicial");
        hql.addToSelectAndGroup("escalaPlantao.horaFinal", "escalaPlantao.horaFinal");
        hql.addToSelectAndGroup("profissional.codigo", "profissional.codigo");
        hql.addToSelectAndGroup("profissional.nome", "profissional.nome");

        hql.addToFrom("EscalaPlantao escalaPlantao"
                + " left join escalaPlantao.profissional profissional");

        hql.addToWhereWhithAnd("profissional =", this.param.getProfissional());

        if (this.param.getFormaApresentacao().equals(QueryRelatorioRelacaoEscalaPlantaoDTOParam.FormaApresentacao.PROFISSIONAL)) {
            hql.addToOrder("profissional.nome" + QueryCustom.QueryCustomSorter.CRESCENTE_NULLS_FIRST);
        } else if (this.param.getFormaApresentacao().equals(QueryRelatorioRelacaoEscalaPlantaoDTOParam.FormaApresentacao.DIA)) {
            hql.addToOrder("escalaPlantao.dataInicial" + QueryCustom.QueryCustomSorter.CRESCENTE_NULLS_FIRST);
        }

    }

    @Override
    public Collection getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List) result);
    }

    @Override
    public void setDTOParam(QueryRelatorioRelacaoEscalaPlantaoDTOParam param) {
        this.param = param;
    }

}
