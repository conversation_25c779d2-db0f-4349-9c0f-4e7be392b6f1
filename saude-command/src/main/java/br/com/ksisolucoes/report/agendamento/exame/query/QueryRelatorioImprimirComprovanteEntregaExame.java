package br.com.ksisolucoes.report.agendamento.exame.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioImpressaoComprovanteEntregaExameDTO;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioImpressaoComprovanteEntregaExameDTOParam;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEndereco;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import java.util.List;
import java.util.Map;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioImprimirComprovanteEntregaExame extends CommandQuery<QueryRelatorioImprimirComprovanteEntregaExame> implements ITransferDataReport<RelatorioImpressaoComprovanteEntregaExameDTOParam, RelatorioImpressaoComprovanteEntregaExameDTO> {

    private RelatorioImpressaoComprovanteEntregaExameDTOParam param;
    private List<RelatorioImpressaoComprovanteEntregaExameDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("profissional.codigo", "exame.profissional.codigo");
        hql.addToSelect("profissional.referencia", "exame.profissional.referencia");
        hql.addToSelect("profissional.nome", "exame.profissional.nome");

        hql.addToSelect("tipoExame.codigo", "exame.tipoExame.codigo");
        hql.addToSelect("tipoExame.descricao", "exame.tipoExame.descricao");

        hql.addToSelect("exame.dataSolicitacao", "exame.dataSolicitacao");
        hql.addToSelect("exame.codigo", "exame.codigo");

        hql.addToSelect("atendimento.empresa.codigo", "exame.atendimento.empresa.codigo");
        hql.addToSelect("atendimento.empresa.referencia", "exame.atendimento.empresa.referencia");
        hql.addToSelect("atendimento.empresa.descricao", "exame.atendimento.empresa.descricao");
        hql.addToSelect("atendimento.empresa.telefone", "exame.atendimento.empresa.telefone");
        hql.addToSelect("atendimento.codigo", "exame.atendimento.codigo");

        hql.addToSelect("usuarioCadsus.codigo", "exame.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "exame.usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.sexo", "exame.usuarioCadsus.sexo");
        hql.addToSelect("usuarioCadsus.dataNascimento", "exame.usuarioCadsus.dataNascimento");
        hql.addToSelect("usuarioCadsus.cpf", "exame.usuarioCadsus.cpf");
        hql.addToSelect("usuarioCadsus.rg", "exame.usuarioCadsus.rg");
        hql.addToSelect("usuarioCadsus.nomeMae", "exame.usuarioCadsus.nomeMae");
        hql.addToSelect("usuarioCadsus.telefone", "exame.usuarioCadsus.telefone");
        hql.addToSelect("enderecoUsuarioCadsus.codigo", "exame.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.codigo");
        hql.addToSelect("enderecoUsuarioCadsus.logradouro", "exame.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("enderecoUsuarioCadsus.bairro", "exame.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.bairro");
        hql.addToSelect("enderecoUsuarioCadsus.cep", "exame.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.cep");
        hql.addToSelect("cidade.codigo", "exame.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.cidade.codigo");
        hql.addToSelect("cidade.descricao", "exame.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.cidade.descricao");
        hql.addToSelect("estado.sigla", "exame.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.cidade.estado.sigla");

        hql.addToSelect("(select min(ucc.numeroCartao) from UsuarioCadsusCns ucc where ucc.usuarioCadsus = usuarioCadsus and ucc.excluido = 0 )","usuarioCadsusCns.numeroCartao");

        hql.setTypeSelect(RelatorioImpressaoComprovanteEntregaExameDTO.class.getName());
        hql.addToFrom("Exame exame"
                + " left join exame.tipoExame tipoExame"
                + " left join exame.profissional profissional"
                + " left join exame.usuarioCadsus usuarioCadsus"
                + " left join exame.atendimento atendimento,"
                + " UsuarioCadsusEndereco usuarioCadsusEndereco"
                + " left join usuarioCadsusEndereco.id.endereco enderecoUsuarioCadsus"
                + " left join enderecoUsuarioCadsus.tipoLogradouro tipoLogradouro"
                + " left join enderecoUsuarioCadsus.cidade cidade"
                + " left join cidade.estado estado");

        hql.addToWhereWhithAnd("usuarioCadsusEndereco.id.usuarioCadsus = usuarioCadsus");
        hql.addToWhereWhithAnd("coalesce(usuarioCadsusEndereco.status, " + UsuarioCadsusEndereco.STATUS_ABERTO + ") = ", UsuarioCadsusEndereco.STATUS_ABERTO);

        hql.addToWhereWhithAnd("exame = ", param.getExame());
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        for (RelatorioImpressaoComprovanteEntregaExameDTO dto : result) {
            List<ExameRequisicao> examesReq = LoadManager.getInstance(ExameRequisicao.class)
                .addProperties(new HQLProperties(ExameRequisicao.class).getProperties())
                .addProperties(new HQLProperties(ExameProcedimento.class, VOUtils.montarPath(ExameRequisicao.PROP_EXAME_PROCEDIMENTO)).getProperties())
                .addProperties(new HQLProperties(TipoExame.class, VOUtils.montarPath(ExameRequisicao.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_TIPO_EXAME)).getProperties())
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_CODIGO), dto.getExame().getCodigo()))
                .start().getList();

            dto.setExameRequisicaoList(examesReq);
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioImpressaoComprovanteEntregaExameDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelatorioImpressaoComprovanteEntregaExameDTOParam param) {
        this.param = param;
    }

}
