package br.com.ksisolucoes.report.prontuario.procedimento.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioAtendimentoCidNotificadoDTO;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioAtendimentoCidNotificadoDTOParam;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioPrecedimentoAtendimentoCidNotificado extends CommandQuery<QueryRelatorioPrecedimentoAtendimentoCidNotificado> implements ITransferDataReport<RelatorioAtendimentoCidNotificadoDTOParam, RelatorioAtendimentoCidNotificadoDTO> {

    private RelatorioAtendimentoCidNotificadoDTOParam param;
    private List<RelatorioAtendimentoCidNotificadoDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(RelatorioAtendimentoCidNotificadoDTO.class.getName());

        hql.addToSelect("a.codigo", "atendimento.codigo");
        hql.addToSelect("a.dataAtendimento", "atendimento.dataAtendimento");
        hql.addToSelect("a.empresa.codigo", "empresa.codigo");
        hql.addToSelect("a.empresa.descricao", "empresa.descricao");
        hql.addToSelect("a.profissional.codigo", "profissional.codigo");
        hql.addToSelect("a.profissional.nome", "profissional.nome");
        hql.addToSelect("a.usuarioCadsus.nome", "usuarioCadsus.nome");
        hql.addToSelect("a.usuarioCadsus.dataNascimento", "usuarioCadsus.dataNascimento");
        hql.addToSelect("cp.codigo", "cid.codigo");
        hql.addToSelect("cp.descricao", "cid.descricao");
        hql.addToSelect("cc.codigo", "classificacaoCid.codigo");
        hql.addToSelect("cc.descricao", "classificacaoCid.descricao");

        hql.addToFrom(Atendimento.class.getName(), "a "
                + " join a.cidPrincipal cp"
                + " join cp.cidClassificacao cc");

        if(RelatorioAtendimentoCidNotificadoDTOParam.FormaApresentacao.FAIXA_ETARIA.equals(this.param.getFormaApresentacao())) {
            hql.addToSelect("faixaEtaria.codigo", "faixaEtariaItem.id.faixaEtaria.codigo");
            hql.addToSelect("faixaEtariaItem.id.sequencia", "faixaEtariaItem.id.sequencia");
            hql.addToSelect("faixaEtariaItem.descricao", "faixaEtariaItem.descricao");
            
            hql.addToFrom("FaixaEtariaItem faixaEtariaItem "
                    + " join faixaEtariaItem.id.faixaEtaria faixaEtaria ");


            hql.addToWhereWhithAnd("faixaEtaria = ", param.getFaixaEtaria());
            hql.addToWhereWhithAnd("cast(extract(years from age(current_date, a.usuarioCadsus.dataNascimento)) * 12 + extract(months from age(current_date, a.usuarioCadsus.dataNascimento)) as long) between faixaEtariaItem.idadeInicial and faixaEtariaItem.idadeFinal ");

            if (this.param.getFaixaEtariaItem() != null) {
                hql.addToWhereWhithAnd("faixaEtariaItem = ", this.param.getFaixaEtariaItem());
            }

        }

        hql.addToWhereWhithAnd("a.status in", Arrays.asList(Atendimento.STATUS_FINALIZADO, Atendimento.STATUS_FECHADO_SEM_ATENDIMENTO));
        hql.addToWhereWhithAnd("a.profissional ", this.param.getProfissional());
        hql.addToWhereWhithAnd("a.empresa ", this.param.getEmpresas());
        hql.addToWhereWhithAnd("a.cidPrincipal ", this.param.getCid());
        hql.addToWhereWhithAnd("cp.cidClassificacao ", this.param.getClassificacaoCids());
        hql.addToWhereWhithAnd("a.dataAtendimento ", Data.adjustRangeHour(param.getPeriodo()));

        hql.addToOrder(getOrder());
    }

    private String getOrder() {
        
        HQLHelper hql = new HQLHelper();

        if (RelatorioAtendimentoCidNotificadoDTOParam.FormaApresentacao.CLASSIFICACAO_CIDS.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("cp.cidClassificacao.descricao");
        } else if (RelatorioAtendimentoCidNotificadoDTOParam.FormaApresentacao.CID.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("a.cidPrincipal.descricao");
        } else if (RelatorioAtendimentoCidNotificadoDTOParam.FormaApresentacao.PROFISSIONAL.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("a.profissional.nome");
        } else if (RelatorioAtendimentoCidNotificadoDTOParam.FormaApresentacao.UNIDADE.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("a.empresa.descricao");
        }

        hql.addToOrder("a.dataAtendimento desc");
        
        return hql.getOrder().toString();
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection getResult() {
        return this.result;
    }

    @Override
    public void setDTOParam(RelatorioAtendimentoCidNotificadoDTOParam param) {
        this.param = param;
    }
}
