package br.com.ksisolucoes.report.hospital.tiss.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.hospital.interfaces.dto.tiss.GuiaResumoInternacaoDTO;
import static br.com.ksisolucoes.report.hospital.interfaces.dto.tiss.GuiaResumoInternacaoDTO.ProcedimentoExameRealizado;
import static br.com.ksisolucoes.report.hospital.interfaces.dto.tiss.GuiaResumoInternacaoDTO.ProfissionalExecutante;
import br.com.ksisolucoes.report.hospital.interfaces.dto.tiss.RelatorioImpressaoGuiasTissDTOParam;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import ch.lambdaj.Lambda;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QueryGuiaResumoInternacao extends CommandQuery {

    private final RelatorioImpressaoGuiasTissDTOParam param;
    private List<GuiaResumoInternacaoDTO> result;

    public QueryGuiaResumoInternacao(RelatorioImpressaoGuiasTissDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(GuiaResumoInternacaoDTO.class.getName());
        hql.setDistinct(true);

        hql.addToSelect("view.numeroGuiaPrincipal", "numeroGuiaPrincipal");
        hql.addToSelect("view.numeroGuiaPrestador", "numeroGuiaPrestador");
        hql.addToSelect("view.dataAutorizacao", "dataAutorizacao");
        hql.addToSelect("view.senhaAutorizacao", "senha");
        hql.addToSelect("view.validadeSenha", "dataValidadeSenha");
        hql.addToSelect("view.numeroGuiaOperadora", "numeroGuiaOperadora");
        hql.addToSelect("view.carteiraBeneficiario", "numeroCarteira");
        hql.addToSelect("view.validadeCarteiraBeneficiario", "validadeCarteira");
        hql.addToSelect("view.nomePaciente", "nomeBeneficiario");
        hql.addToSelect("view.cnsPaciente", "cartaoNacionalSaude");
        hql.addToSelect("view.atendimentoRn", "atendimentoRn");
        hql.addToSelect("view.codigoPrestadorExecutanteNaOperadora", "codigoOperadoraExecutante");
        hql.addToSelect("view.nomeContratadoExecutante", "nomeContratadoExecutante");
        hql.addToSelect("view.cnesContratadoExecutante", "codigoCNES");
        hql.addToSelect("view.caraterAtendimentoTiss", "caraterAtendimento");
        hql.addToSelect("view.tipoFaturamento", "tipoFaturamento");
        hql.addToSelect("view.dataInicioFaturamento", "dataInicioFaturamento");
        hql.addToSelect("view.horaInicioFaturamento", "horaInicioFaturamento");
        hql.addToSelect("view.dataFinalFaturamento", "dataFimFaturamento");
        hql.addToSelect("view.dataFinalFaturamento", "horaFimFaturamento");
        hql.addToSelect("view.tipoInternacao", "tipoInternacao");
        hql.addToSelect("view.regimeInternacao", "regimeInternacao");
        hql.addToSelect("view.diagnosticoPrincipal", "cidPrincipal");
//        hql.addToSelect("", "cidOpcional2");
//        hql.addToSelect("", "cidOpcional3");
//        hql.addToSelect("", "cidOpcional4");
        hql.addToSelect("view.indicadorAcidente", "indicacaoAcidente");
        hql.addToSelect("view.motivoEncerramento", "motivoEncerramentoInternacao");
        hql.addToSelect("view.declaracaoNascido", "numeroDeclaracaoNascidoVivo");
//        hql.addToSelect("", "cidObito");
        hql.addToSelect("view.declaracaoObito", "numeroDeclaracaoObito");
        hql.addToSelect("view.indicadorObitoRn", "indicadorRn");
        hql.addToSelect("coalesce(view.totalDiaria, 0)", "totalDiarias");
        hql.addToSelect("coalesce(view.totalTaxaAluguel, 0)", "totalTaxasAlugueis");
        hql.addToSelect("coalesce(view.totalMaterial, 0)", "totalMateriais");
        hql.addToSelect("coalesce(view.totalOpme, 0)", "totalOPME");
        hql.addToSelect("coalesce(view.totalMedicamento, 0)", "totalMedicamentos");
        hql.addToSelect("coalesce(view.totalGasMedicinal, 0)", "totalGasesMedicinais");
        hql.addToSelect("coalesce(view.valorTotalProcedimento, 0)", "totalProcedimentos");

        hql.addToFrom("ViewContasTiss view");

        hql.addToWhereWhithAnd("view.codigoContaPaciente = ", param.getCodigoContaPaciente());
        hql.addToWhereWhithAnd("view.statusItemContaPaciente = ", ItemContaPaciente.Status.CONFIRMADO.value());
        hql.addToWhereWhithAnd("view.tipoItemContaPaciente in ", Arrays.asList(ItemContaPaciente.Tipo.PROCEDIMENTO.value(), ItemContaPaciente.Tipo.EXAME.value()));
    }

    @Override
    protected void customQuery(Query query) {
        super.customQuery(query);
        query.setMaxResults(1);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        super.customProcess(session);

        Convenio proxy = on(Convenio.class);
        Convenio convenio = LoadManager.getInstance(Convenio.class)
                .addProperty(path(proxy.getCodigo()))
                .addProperty(path(proxy.getRegistroAnsDestino()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getCodigo()), param.getCodigoConvenio()))
                .setMaxResults(1)
                .start().getVO();

        for (GuiaResumoInternacaoDTO item : result) {
            if (convenio != null) {
                item.setRegistroANS(convenio.getRegistroAnsDestino());
            }

            { // Procedimentos e Exames Realizados
                QueryProcedimentosExamesRealizados query = new QueryProcedimentosExamesRealizados();
                query.start();
                item.setListProcedimentosExamesRealizados(query.getResult());
                item.setListProfissionaisExecutantes(query.getProfissionaisExecutantes());
            }
        }
    }

    private class QueryProcedimentosExamesRealizados extends CommandQuery {

        private List<ProcedimentoExameRealizado> result;
        private List profissionaisExecutantes = new ArrayList();

        @Override
        protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
            hql.setTypeSelect(ProcedimentoExameRealizado.class.getName());

            hql.addToSelect("view.codigoItemContaPaciente", "codigoItemContaPaciente");
            hql.addToSelect("view.dataInicialExecucaoProcedimento", "data");
            hql.addToSelect("view.horaInicialRealizado", "horaInicial");
            hql.addToSelect("view.horaFinalRealizado", "horaFinal");
            hql.addToSelect("view.tabelaReferenciaItemDespesa", "tabela");
            hql.addToSelect("view.codigoProcedimentoRealizado", "codigo");
            hql.addToSelect("view.descricaoProcedimentoRealizado", "descricao");
            hql.addToSelect("view.quantidadeProcedimento", "qtde");
            hql.addToSelect("view.viaAcesso", "via");
            hql.addToSelect("view.tecnicaUtilizada", "tec");
            hql.addToSelect("view.reducaoAcrescimoDespesa", "fatorReducaoAcrescimo");
            hql.addToSelect("coalesce(view.valorUnitarioProcedimento, 0)", "valorUnitario");
            hql.addToSelect("coalesce(view.totalProcedimento, 0)", "valorTotal");

            hql.addToFrom("ViewContasTiss view");
            hql.addToWhereWhithAnd("view.codigoContaPaciente = ", param.getCodigoContaPaciente());
            hql.addToWhereWhithAnd("view.statusItemContaPaciente = ", ItemContaPaciente.Status.CONFIRMADO.value());
            hql.addToWhereWhithAnd("view.tipoItemContaPaciente in ", Arrays.asList(ItemContaPaciente.Tipo.PROCEDIMENTO.value(), ItemContaPaciente.Tipo.EXAME.value()));
        }

        @Override
        protected void result(HQLHelper hql, Object result) {
            this.result = hql.getBeanList((List) result);
        }

        @Override
        protected void customProcess(Session session) throws ValidacaoException, DAOException {
            super.customProcess(session);

            Long seqRef = 0L;
            for (ProcedimentoExameRealizado item : result) {
                { // Profissionais Executantes
                    seqRef++;
                    QueryProfissionaisExecutantes query = new QueryProfissionaisExecutantes(item.getCodigoItemContaPaciente());
                    query.start();
                    List<ProfissionalExecutante> list = query.getResult();
                    if (!list.isEmpty()) {
                        Lambda.forEach(list).setSeqRef(seqRef);
                        this.profissionaisExecutantes.addAll(list);
                    }
                }
            }
        }

        @Override
        public List<ProcedimentoExameRealizado> getResult() {
            return result;
        }

        public List getProfissionaisExecutantes() {
            return this.profissionaisExecutantes;
        }
    }

    private class QueryProfissionaisExecutantes extends CommandQuery {

        private final Long codigoItemContaPaciente;
        private List<ProfissionalExecutante> result;

        public QueryProfissionaisExecutantes(Long codigoItemContaPaciente) {
            this.codigoItemContaPaciente = codigoItemContaPaciente;
        }

        @Override
        protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
            hql.setTypeSelect(ProfissionalExecutante.class.getName());

            hql.addToSelect("profissional.grauParticipacao", "grauPart");
            hql.addToSelect("profissional.cpfProfissionalExecutante", "cpf");
//            hql.addToSelect("view.codigoContratadoExecutante", "codigoOperadora");
            hql.addToSelect("profissional.nomeProfissionalExecutante", "nomeProfissional");
            hql.addToSelect("profissional.conselhoProfissionalExecutante", "conselhoProfissional");
            hql.addToSelect("profissional.numeroConselhoProfissionalExecutante", "numeroConselho");
            hql.addToSelect("profissional.ufConselhoProfissionalExecutante", "uf");
            hql.addToSelect("profissional.cboProfissionalExecutante", "codigoCBO");

            hql.addToFrom("ViewContasTissProfissional profissional");

            hql.addToWhereWhithAnd("profissional.codigoContaPaciente = ", param.getCodigoContaPaciente());
            hql.addToWhereWhithAnd("profissional.codigoItemContaPaciente = ", this.codigoItemContaPaciente);
            hql.addToWhereWhithAnd("profissional.statusItemContaPaciente = ", ItemContaPaciente.Status.CONFIRMADO.value());
            hql.addToWhereWhithAnd("profissional.tipoItemContaPaciente in ", Arrays.asList(ItemContaPaciente.Tipo.PROCEDIMENTO.value(), ItemContaPaciente.Tipo.EXAME.value()));
        }

        @Override
        protected void result(HQLHelper hql, Object result) {
            this.result = hql.getBeanList((List) result);
        }

        @Override
        public List<ProfissionalExecutante> getResult() {
            return result;
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List) result);
    }

    @Override
    public List<GuiaResumoInternacaoDTO> getResult() {
        return result;
    }
}
