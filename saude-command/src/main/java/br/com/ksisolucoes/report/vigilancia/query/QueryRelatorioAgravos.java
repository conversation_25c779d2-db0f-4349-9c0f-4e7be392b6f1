package br.com.ksisolucoes.report.vigilancia.query;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioAgravosDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioAgravosDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Session;

import java.util.*;

import static br.com.ksisolucoes.util.VOUtils.montarPath;

/**
 * <AUTHOR>
 */
public class QueryRelatorioAgravos extends CommandQuery<QueryRelatorioAgravos> implements ITransferDataReport<RelatorioAgravosDTOParam, RelatorioAgravosDTO> {

    private RelatorioAgravosDTOParam param;
    private List<RelatorioAgravosDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {

        hql.setTypeSelect(RelatorioAgravosDTO.class.getName());

        // Cuidar ordem dos campos, pois possui ordenação pelo nº da coluna.
        hql.addToSelect("unidade.codigo", "unidade.codigo");
        hql.addToSelect("coalesce(unidade.descricao, registroAgravo.descricaoEmpresaIntegracao)", "unidade.descricao");

        hql.addToSelect("usuarioCadsus.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.celular", "usuarioCadsus.celular");
        hql.addToSelect("usuarioCadsus.telefone", "usuarioCadsus.telefone");
        hql.addToSelect("usuarioCadsus.telefone2", "usuarioCadsus.telefone2");
        hql.addToSelect("usuarioCadsus.telefone3", "usuarioCadsus.telefone3");
        hql.addToSelect("usuarioCadsus.telefone4", "usuarioCadsus.telefone4");
        hql.addToSelect("usuarioCadsus.dataNascimento", "usuarioCadsus.dataNascimento");
        hql.addToSelect("registroAgravo.codigo", "registroAgravo.codigo");
        hql.addToSelect("registroAgravo.dataRegistro", "registroAgravo.dataRegistro");
        hql.addToSelect("registroAgravo.dataCadastro", "registroAgravo.dataCadastro");
        hql.addToSelect("registroAgravo.gestante", "registroAgravo.gestante");
        hql.addToSelect("registroAgravo.status", "registroAgravo.status");
        hql.addToSelect("registroAgravo.dataPrimeirosSintomas", "registroAgravo.dataPrimeirosSintomas");
        hql.addToSelect("cid.codigo", "cid.codigo");
        hql.addToSelect("cid.descricao", "cid.descricao");
        hql.addToSelect("euc.bairro", "bairro");
        hql.addToSelect("cidClassificacao.codigo", "cid.cidClassificacao.codigo");
        hql.addToSelect("cidClassificacao.prazoEncerramento", "cid.cidClassificacao.prazoEncerramento");
        hql.addToSelect("cidClassificacao.descricao", "cid.cidClassificacao.descricao");

        if (RelatorioAgravosDTOParam.FormaApresentacao.UNIDADE.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("2");
        } else if (RelatorioAgravosDTOParam.FormaApresentacao.CID.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("cid.cidClassificacao.descricao");
        } else if (RelatorioAgravosDTOParam.FormaApresentacao.BAIRRO.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("euc.bairro");
        }

        hql.addToWhereWhithAnd("registroAgravo.codigo = ", this.param.getCodigoNotificacao());
        hql.addToWhereWhithAnd("usuarioCadsus = ", this.param.getUsuarioCadsus());
        hql.addToWhereWhithAnd("unidade = ", this.param.getUnidade());
        hql.addToWhereWhithAnd("cid IN ", this.param.getListCid());
        hql.addToWhereWhithAnd("cid.cidClassificacao = ", this.param.getClassificacaoCids());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("euc.bairro", this.param.getBairro()));

        if (CollectionUtils.isNotNullEmpty(this.param.getStatusList())) {
            hql.addToWhereWhithAnd("registroAgravo.status IN ", this.param.getStatusList());
        } else {
            if (RelatorioAgravosDTOParam.Status.TODOS.value().equals(this.param.getStatus())) {
                hql.addToWhereWhithAnd("registroAgravo.status <> ", RegistroAgravo.Status.CANCELADO.value());
            } else {
                hql.addToWhereWhithAnd("registroAgravo.status = ", this.param.getStatus());
            }
        }

        hql.addToWhereWhithAnd("registroAgravo.dataRegistro", this.param.getPeriodo());

        if (param.getGestante() != null) {
            hql.addToWhereWhithAnd("registroAgravo.gestante = ", this.param.getGestante());
        }

        buildFilterUnidadeReferencia(hql, "registroAgravo");

        if (RelatorioAgravosDTOParam.Ordenacao.DATA_REGISTRO.value().equals(this.param.getOrdenacao())) {
            hql.addToOrder("registroAgravo.dataRegistro " + this.param.getTipoOrdenacao().getCommand());
        } else if (RelatorioAgravosDTOParam.Ordenacao.PACIENTE.value().equals(this.param.getOrdenacao())) {
            hql.addToOrder("usuarioCadsus.nome " + this.param.getTipoOrdenacao().getCommand());
        } else if (RelatorioAgravosDTOParam.Ordenacao.UNIDADE.value().equals(this.param.getOrdenacao())) {
            hql.addToOrder("2 " + this.param.getTipoOrdenacao().getCommand());
        }

        hql.addToFrom("RegistroAgravo registroAgravo"
                + " left join registroAgravo.empresa unidade"
                + " left join registroAgravo.cid cid"
                + " left join cid.cidClassificacao cidClassificacao"
                + " left join registroAgravo.usuarioCadsus usuarioCadsus"
                + " left join usuarioCadsus.enderecoDomicilio ed"
                + " left join ed.enderecoUsuarioCadsus euc");

    }

    @Override
    public Collection getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List) result);
    }

    @Override
    public void setDTOParam(RelatorioAgravosDTOParam param) {
        this.param = param;
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        List<RelatorioAgravosDTO> aux = new ArrayList<>();
        if (CollectionUtils.isNotNullEmpty(result)) {
            for (RelatorioAgravosDTO relatorioAgravosDTO : result) {
                if (CollectionUtils.isNotNullEmpty(this.param.getStatusList())) {
                    if (relatorioAgravosDTO.getCid().getCidClassificacao() != null && relatorioAgravosDTO.getRegistroAgravo().getDataRegistro() != null && relatorioAgravosDTO.getCid().getCidClassificacao().getPrazoEncerramento() != null) {
                        Date dateLimite = Data.addDias(relatorioAgravosDTO.getRegistroAgravo().getDataRegistro(), relatorioAgravosDTO.getCid().getCidClassificacao().getPrazoEncerramento().intValue());
                        if (dateLimite == null || (param.getDataLimiteEncerramento() != null && DataUtil.dataCompareTo(param.getDataLimiteEncerramento(), dateLimite) < 0)) {
                            continue;
                        }
                        relatorioAgravosDTO.setDataLimiteEncerramento(dateLimite);
                        aux.add(relatorioAgravosDTO);
                    }
                } else {
                    if (relatorioAgravosDTO.getCid().getCidClassificacao() != null && relatorioAgravosDTO.getRegistroAgravo().getDataRegistro() != null && relatorioAgravosDTO.getCid().getCidClassificacao().getPrazoEncerramento() != null) {
                        Date dateLimite = Data.addDias(relatorioAgravosDTO.getRegistroAgravo().getDataRegistro(), relatorioAgravosDTO.getCid().getCidClassificacao().getPrazoEncerramento().intValue());
                        if (param.getDataLimiteEncerramento() != null && DataUtil.dataCompareTo(param.getDataLimiteEncerramento(), dateLimite) < 0) {
                            continue;
                        }
                        relatorioAgravosDTO.setDataLimiteEncerramento(dateLimite);
                    }
                    aux.add(relatorioAgravosDTO);
                }
                relatorioAgravosDTO.setUnidadeReferencia(param.getUnidadeReferencia());
            }
        }
        result = aux;
    }

    public void buildFilterUnidadeReferencia(HQLHelper hql, String alias) {
        if (param.getUnidadeReferencia() == null) return;

        String listaEquipesArea = findEmpresaReferencia(param.getUnidadeReferencia().getCodigo());

        if (!listaEquipesArea.isEmpty()) {
            StringBuilder hqlEquipeReferencia = new StringBuilder()
                    .append(" SELECT ra.codigo ")
                    .append(" FROM " + RegistroAgravo.class.getName() + " ra ")
                    .append(" join ra.usuarioCadsus usuario ")
                    .append(" join usuario.equipe e ")
                    .append(" WHERE e.equipeArea in (" + listaEquipesArea + ")");

            StringBuilder hqlEnderecoDomicilio = new StringBuilder()
                    .append(" SELECT ra.codigo ")
                    .append(" FROM " + RegistroAgravo.class.getName() + " ra ")
                    .append(" join ra.usuarioCadsus usuario ")
                    .append(" left join usuario.enderecoDomicilio endomici ")
                    .append(" left join endomici.equipeMicroArea micarea ")
                    .append(" left join micarea.equipeArea equiarea ")
                    .append(" WHERE equiarea.codigo in (" + listaEquipesArea + ")");

            StringBuilder hqlEnderecoEstruturado = new StringBuilder()
                    .append(" SELECT ra.codigo ")
                    .append(" FROM " + RegistroAgravo.class.getName() + " ra ")
                    .append(" join ra.usuarioCadsus usuario ")
                    .append(" join usuario.enderecoUsuarioCadsus endusa ")
                    .append(" left join endusa.enderecoEstruturado endestrut ")
                    .append(" left join endestrut.equipeMicroArea micarea ")
                    .append(" left join micarea.equipeArea equiarea ")
                    .append(" WHERE equiarea.codigo in (" + listaEquipesArea + ")");

            hql.addToWhereWhithAnd(" ( " +
                    alias + ".codigo in (" + hqlEquipeReferencia.toString() + ") " +
                    " or " + alias + ".codigo in (" + hqlEnderecoDomicilio.toString() + ") " +
                    " or " + alias + ".codigo in (" + hqlEnderecoEstruturado.toString() + ") " +
                    " ) ");
        }

    }

    private String findEmpresaReferencia(Long codigoEmpresa) {
        List<Equipe> listaEquipes = LoadManager.getInstance(Equipe.class)
                .addProperty(montarPath(Equipe.PROP_EMPRESA, Empresa.PROP_CODIGO))
                .addProperty(montarPath(Equipe.PROP_EMPRESA, Empresa.PROP_DESCRICAO))
                .addProperty(montarPath(Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(montarPath(Equipe.PROP_EMPRESA, Empresa.PROP_CODIGO), codigoEmpresa))
                .start().getList();

        if (listaEquipes == null) return "";
        LinkedHashSet<String> listaEquipesArea = new LinkedHashSet<>();
        for (Equipe listaEquipe : listaEquipes) {
            if (listaEquipe.getEquipeArea() != null)
                listaEquipesArea.add(String.valueOf(listaEquipe.getEquipeArea().getCodigo()));
        }

        return StringUtils.join(listaEquipesArea, ',');
    }
}
