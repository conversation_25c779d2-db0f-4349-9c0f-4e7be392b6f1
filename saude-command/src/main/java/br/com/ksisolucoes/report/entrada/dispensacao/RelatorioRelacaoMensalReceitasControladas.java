/*
 * RelatorioRMNRA.java
 *
 * Created on 09 de Outubro de 2006, 09:08
 *
 * To change this template, choose Tools | Template Manager
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.entrada.dispensacao;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioRelacaoMensalReceitasControladasDTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.query.QueryRelatorioRelacaoMensalReceitasControladas;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import java.text.ParseException;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoMensalReceitasControladas extends AbstractReport<RelatorioRelacaoMensalReceitasControladasDTOParam> {

    public RelatorioRelacaoMensalReceitasControladas(RelatorioRelacaoMensalReceitasControladasDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        try {
            addParametro("ANO_EXERCICIO", new Long(Data.getAno(this.param.getDataInicial())));
            addParametro("MES_EXERCICIO", new Long(Data.getMes(this.param.getDataInicial())));
            addParametro("MES_EXERCICIO_FORMATADO", Data.formatToMonth(this.param.getDataInicial()));
            addParametro("EMPRESA", this.param.getEmpresa());
            addParametro("RESPONSAVEL", this.param.getProfissionalResponsavel());
            addParametro("FORMA_APRESENTACAO", this.param.getFormaApresentacao());
        } catch (ParseException ex) {
            Logger.getLogger(RelatorioRelacaoMensalReceitasControladas.class.getName()).log(Level.SEVERE, null, ex);
        }

        return "/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_receitas_controladas.jrxml";
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioRelacaoMensalReceitasControladas();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_mensal_receitas_controladas");
    }
}
