package br.com.ksisolucoes.report.vacina.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.vacina.dto.RelatorioRecebimentoVacinasDTOParam;
import br.com.ksisolucoes.vo.vacina.ItemEntradaVacina;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioRecebimentoVacinas extends CommandQuery<QueryRelatorioRecebimentoVacinas> implements ITransferDataReport<RelatorioRecebimentoVacinasDTOParam, ItemEntradaVacina> {

    private RelatorioRecebimentoVacinasDTOParam param;
    private List<ItemEntradaVacina> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect("empresa.codigo", "entradaVacina.empresa.codigo");
        hql.addToSelect("empresa.referencia", "entradaVacina.empresa.referencia");
        hql.addToSelect("empresa.descricao", "entradaVacina.empresa.descricao");
        hql.addToSelect("entradaVacina.codigo", "entradaVacina.codigo");
        hql.addToSelect("entradaVacina.nota", "entradaVacina.nota");
        hql.addToSelect("fornecedor.codigo", "entradaVacina.fornecedor.codigo");
        hql.addToSelect("fornecedor.descricao", "entradaVacina.fornecedor.descricao");
        hql.addToSelect("produto.codigo", "produto.codigo");
        hql.addToSelect("produto.referencia", "produto.referencia");
        hql.addToSelect("produto.descricao", "produto.descricao");
        hql.addToSelect("unidade.codigo", "produto.unidade.codigo");
        hql.addToSelect("unidade.unidade", "produto.unidade.unidade");
        hql.addToSelect("entradaVacina.dataCadastro", "entradaVacina.dataCadastro");
        hql.addToSelect("entradaVacina.dataEmissao", "entradaVacina.dataEmissao");
        hql.addToSelect("entradaVacina.dataPortaria", "entradaVacina.dataPortaria");
        hql.addToSelect("entradaVacina.status", "entradaVacina.status");
        hql.addToSelect("itemEntradaVacina.dataCadastro", "dataCadastro");
        hql.addToSelect("itemEntradaVacina.quantidadeEntrada", "quantidadeEntrada");
        hql.addToSelect("itemEntradaVacina.lote", "lote");
        hql.addToSelect("itemEntradaVacina.valorTotal", "valorTotal");
        
        hql.setTypeSelect(ItemEntradaVacina.class.getName());
        hql.addToFrom("ItemEntradaVacina itemEntradaVacina"
                + " left join itemEntradaVacina.entradaVacina entradaVacina"
                + " left join entradaVacina.fornecedor fornecedor"
                + " left join entradaVacina.empresa empresa"
                + " left join itemEntradaVacina.produto produto"
                + " left join produto.unidade unidade"
                + " left join produto.subGrupo subGrupo"
                + " left join subGrupo.roGrupoProduto grupoProduto");
        
        hql.addToWhereWhithAnd("empresa in ", param.getEmpresas());
        hql.addToWhereWhithAnd("fornecedor = ", param.getFornecedor());
        hql.addToWhereWhithAnd("grupoProduto = ", param.getGrupoProduto());
        hql.addToWhereWhithAnd("subGrupo = ", param.getSubGrupo());
        hql.addToWhereWhithAnd("produto = ", param.getProduto());
        hql.addToWhereWhithAnd("entradaVacina.nota = ", param.getNota());
        
        hql.addToWhereWhithAnd("entradaVacina."+param.getTipoData(), param.getPeriodo());
        if (param.getSituacao()!=null) {
            hql.addToWhereWhithAnd("entradaVacina.status = ", param.getSituacao().getValue());
        }
        
        hql.addToOrder("empresa.descricao");
        
        if (RelatorioRecebimentoVacinasDTOParam.FormaApresentacao.DATA_EMISSAO.equals(param.getFormaApresentacao())) {
            hql.addToOrder("entradaVacina.dataEmissao");
        } else if (RelatorioRecebimentoVacinasDTOParam.FormaApresentacao.DATA_PORTARIA.equals(param.getFormaApresentacao())) {
            hql.addToOrder("entradaVacina.dataPortaria");
        } else if (RelatorioRecebimentoVacinasDTOParam.FormaApresentacao.FORNECEDOR.equals(param.getFormaApresentacao())) {
            hql.addToOrder("fornecedor.descricao");
        } else if (RelatorioRecebimentoVacinasDTOParam.FormaApresentacao.PRODUTO.equals(param.getFormaApresentacao())) {
            hql.addToOrder("produto.descricao");
        } else if (RelatorioRecebimentoVacinasDTOParam.FormaApresentacao.SUBGRUPO.equals(param.getFormaApresentacao())) {
            hql.addToSelect("grupoProduto.codigo", "produto.subGrupo.roGrupoProduto.codigo");
            hql.addToSelect("subGrupo.id.codigo", "produto.subGrupo.id.codigo");
            hql.addToSelect("subGrupo.id.codigoGrupoProduto", "produto.subGrupo.id.codigoGrupoProduto");
            hql.addToSelectAndOrder("grupoProduto.descricao", "produto.subGrupo.roGrupoProduto.descricao");
            hql.addToSelectAndOrder("subGrupo.descricao", "produto.subGrupo.descricao");
        }
        
        if (RelatorioRecebimentoVacinasDTOParam.Ordenacao.DATA_CADASTRO.equals(param.getOrdenacao())) {
            hql.addToOrder("entradaVacina.dataCadastro");
        } else if (RelatorioRecebimentoVacinasDTOParam.Ordenacao.DATA_EMISSAO.equals(param.getOrdenacao())) {
            hql.addToOrder("entradaVacina.dataEmissao");
        } else if (RelatorioRecebimentoVacinasDTOParam.Ordenacao.DATA_PORTARIA.equals(param.getOrdenacao())) {
            hql.addToOrder("entradaVacina.dataPortaria");
        } else if (RelatorioRecebimentoVacinasDTOParam.Ordenacao.NOTA.equals(param.getOrdenacao())) {
            hql.addToOrder("entradaVacina.nota");
        } else if (RelatorioRecebimentoVacinasDTOParam.Ordenacao.PRODUTO.equals(param.getOrdenacao())) {
            hql.addToOrder("produto.descricao");
        }
    }
    
    @Override
    public void setDTOParam(RelatorioRecebimentoVacinasDTOParam param) {
        this.param = param;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>)result);
    }

    @Override
    public List<ItemEntradaVacina> getResult() {
        return result;
    }

}
