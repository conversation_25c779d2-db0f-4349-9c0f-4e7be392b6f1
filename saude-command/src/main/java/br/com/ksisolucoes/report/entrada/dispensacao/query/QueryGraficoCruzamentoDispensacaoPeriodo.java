package br.com.ksisolucoes.report.entrada.dispensacao.query;

import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioTopGraficoCruzamentoDispensacaoPeriodoDTO;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioGraficoCruzamentoDispensacaoPeriodoDTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioGraficoDemonstrativoDispensacaoDTO;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioGraficoDemonstrativoDispensacaoDTOParam;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.bo.BusinessObjectConstants;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.hibernate.Query;
import org.hibernate.Session;
import org.josql.QueryResults;

public class QueryGraficoCruzamentoDispensacaoPeriodo extends CommandQuery<QueryGraficoCruzamentoDispensacaoPeriodo> {
    
    private List<RelatorioGraficoDemonstrativoDispensacaoDTO> listDispensacao = new ArrayList<RelatorioGraficoDemonstrativoDispensacaoDTO>();
    
    private Map< Long, Object > topGraficoCruzamentoDispensacaoPeriodo = new HashMap< Long, Object >();

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        /*
         * VALIDACAO DE CAMPOS OBRIGATORIOS
         * --------------------------------
         * Validacao para que a chave seja totalmente preenchida bem como
         * seus parametros.
         *---------------------------------------------------------------------*/
        RetornoValidacao retornoValidacao = new RetornoValidacao();
        
        if ( this.bean.getDataInicial1() == null || this.bean.getDataFinal1() == null ){
            retornoValidacao.add( Bundle.getStringBO( BusinessObjectConstants.MENSAGEM_ARGUMENTO_NULO, Bundle.getStringApplication( "rotulo_periodo_1", this.sessao.getLocale() ).toLowerCase(), this.sessao.getLocale() ), Bundle.getStringApplication( "rotulo_periodo_1", this.sessao.getLocale() ) );
        }
        
        if ( this.bean.getDataInicial2() == null || this.bean.getDataFinal2() == null ){
            retornoValidacao.add( Bundle.getStringBO( BusinessObjectConstants.MENSAGEM_ARGUMENTO_NULO, Bundle.getStringApplication( "rotulo_periodo_2", this.sessao.getLocale() ).toLowerCase(), this.sessao.getLocale() ), Bundle.getStringApplication( "rotulo_periodo_2", this.sessao.getLocale() ) );
        }
        
        if ( !retornoValidacao.isValido() ){
            throw new ValidacaoException( retornoValidacao );
        }
        
        /*---------------------------------------------------------------------*/
        
        /*
         * CONTROLE SOBRE TOPs POR EMPRESA
         * -------------------------------
         * Consulta para averiguar quais formas de apresentao foram top para
         * o periodo informado. Nesta rotina,  utilizado o JoSQL, onde o agrupamento
         *  feito em cima da empresa, trazendo, por exemplo, os X maiores grupos
         * para o perodo, e ento remontado o resultado no estilo Map< Codigo, Chaves >.
         *---------------------------------------------------------------------*/
        // <editor-fold defaultstate="collapsed" desc=" QUERY PARA PEGAR O MAIOR VALOR POR PERIODO E POR EMPRESA ">
        
        QueryTopGraficoCruzamentoDispensacaoPeriodo q = new QueryTopGraficoCruzamentoDispensacaoPeriodo();
        q.setBean(this.bean);
        try {
            List< RelatorioTopGraficoCruzamentoDispensacaoPeriodoDTO > dispensacaoMedicamento = q.start().getListDispensacao();
            QueryResults qr = null;
            
            org.josql.Query query = new org.josql.Query();
            String sql =
                    " SELECT * " +
                    " FROM "  + RelatorioTopGraficoCruzamentoDispensacaoPeriodoDTO.class.getName() +
                    " GROUP BY empresa" +
                    " ORDER BY empresa, valor desc" +
                    " LIMIT 1, :limite ";
            query.setVariable( "limite", this.bean.getQuantidade() );
            query.parse(sql);
            
            qr = query.execute( dispensacaoMedicamento );
            Map< List, List< RelatorioTopGraficoCruzamentoDispensacaoPeriodoDTO > > _topGraficoDispensacao= qr.getGroupByResults();
            for ( List _list : _topGraficoDispensacao.keySet() ){
                List< Long > list = _list;
                List< RelatorioTopGraficoCruzamentoDispensacaoPeriodoDTO > _fas = _topGraficoDispensacao.get( list );
                List< Serializable > fas = new ArrayList< Serializable >();
                for ( RelatorioTopGraficoCruzamentoDispensacaoPeriodoDTO dto : _fas ){
                    fas.add( dto.getCodigoFormaApresentacao() );
                }
                
                this.topGraficoCruzamentoDispensacaoPeriodo.put( list.get(0), fas );
            }
        } catch (Exception ex) {
            Loggable.log.debug( ex.getMessage(), ex );
        }
        // </editor-fold>
        
        // <editor-fold defaultstate="collapsed" desc=" QUERY PARA TOTAL DAS DISPENSACOES ">
        /*
         * QUERY PARA TOTAL DAS DISPENSAOES
         * --------------------------
         *---------------------------------------------------------------------*/
        
        HQLHelper hql = new HQLHelper();
        
        hql.setTypeSelect(RelatorioGraficoDemonstrativoDispensacaoDTO.class.getName());
        
        hql.addToSelect(" e.codigo ");
        hql.addToSelect(" e.referencia ");
        hql.addToSelect(" e.descricao ");
        
        String calculoTotalizacao = null;
        
        if(this.bean.getTipoPreco().equals(EstoqueEmpresa.PROP_PRECO_MEDIO)){
            calculoTotalizacao = "sum( dmi.precoMedio * dmi.quantidadeDispensada )";
        }else{
            calculoTotalizacao = " sum( dmi.precoCusto * dmi.quantidadeDispensada )";
        }
        
        hql.addToSelect( calculoTotalizacao + " as total" );
        
        hql.addToSelect( "min( dm.dataDispensacao )");
        
        hql.addToFrom(" DispensacaoMedicamentoItem dmi " +
                " right join dmi.id.dispensacaoMedicamento dm " +
                " left join dm.usuarioCadsusDestino ucd" +
                " left join dm.profissional  prof " +
                " left join dm.empresaOrigem emo , Empresa e " );
        
        
        /*******************Filtro do Periodo********************/
        
        hql.addToWhereWhithAnd("( ( dm.dataDispensacao  between :dataInicial1 and :dataFinal1 ) or " +
                "( dm.dataDispensacao between :dataInicial2 and :dataFinal2 ) )");
        
        hql.addToWhereWhithAnd( "dm.id.empresa.codigo = e.codigo" );
        hql.addToWhereWhithAnd( "dm.id.empresa in ( :empresas )" );
        
        hql.addToWhereWhithAnd("dmi.precoMedio <> null");
        hql.addToWhereWhithAnd("dmi.quantidadeDispensada <> null");
        
        hql.addToGroup( "e.codigo" );
        hql.addToGroup( "e.referencia" );
        hql.addToGroup( "e.descricao" );
        
        hql.addToOrder( "e.codigo" );
        hql.addToOrder( "e.descricao" );
        
        
        /*---------------------------------------------------------------------*/
        // </editor-fold>
        
        /*
         * FORMA DE APRESENTAO
         * ---------------------
         *---------------------------------------------------------------------*/
        if ( this.bean.getFormaApresentacao() != null ){
            String chave = null;
            
            // <editor-fold defaultstate="collapsed" desc=" Filtro por Grupo de Produto ">
            if ( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO).equals( this.bean.getFormaApresentacao() ) ){
                hql.addToSelect("gp.descricao");
                
                chave = "gp.codigo";
                
                hql.addToFrom("GrupoProduto gp");
                
                // hql.addToWhereWhithAnd("dmi.produto.codigo = p.codigo");
                hql.addToWhereWhithAnd("gp.codigo = dmi.produto.subGrupo.id.codigoGrupoProduto");
                
                if ( this.bean.getFiltroFormaApresentacao() != null &&
                        this.bean.getFiltroFormaApresentacao().size() > 0 ){
                    hql.addToWhereWhithAnd( "gp in ( :fas )" );
                }
                
                hql.addToGroup("gp.descricao");
                hql.addToOrder("gp.descricao");
                
                // </editor-fold>
                
                // <editor-fold defaultstate="collapsed" desc=" Filtro por Profissional ">
            } else if ( DispensacaoMedicamento.PROP_PROFISSIONAL.equals( this.bean.getFormaApresentacao() ) ){
                
                hql.addToSelect(" pro.nome ");
                chave =  "pro.codigo";
                hql.addToFrom("Profissional pro");
                
                hql.addToWhereWhithAnd("prof.codigo = pro.codigo");
                
                if ( this.bean.getFiltroFormaApresentacao() != null &&
                        this.bean.getFiltroFormaApresentacao().size() > 0 ){
                    hql.addToWhereWhithAnd( "pro  in ( :fas )" );
                }
                
                hql.addToGroup(" pro.nome ");
                hql.addToOrder(" pro.nome ");
                // </editor-fold>
                
                // <editor-fold defaultstate="collapsed" desc=" Filtro por Empresa ">
            } else if (  DispensacaoMedicamento.PROP_EMPRESA_ORIGEM.equals( this.bean.getFormaApresentacao() ) ){
                hql.addToSelect("emp.descricao");
                chave = "emp.codigo";
                
                hql.addToFrom("Empresa emp");
                
                hql.addToWhereWhithAnd( "emo.codigo  = emp.codigo" );
                if ( this.bean.getFiltroFormaApresentacao() != null &&
                        this.bean.getFiltroFormaApresentacao().size() > 0 ){
                    hql.addToWhereWhithAnd( "emp in ( :fas )" );
                    
                }
                
                hql.addToGroup(" emp.descricao ");
                hql.addToOrder(" emp.descricao ");
                // </editor-fold>

                // <editor-fold defaultstate="collapsed" desc=" Filtro por SubGrupo ">
            } else if (Produto.PROP_SUB_GRUPO.equals( this.bean.getFormaApresentacao()) ){
                hql.addToSelect("sg.descricao");

                chave = "sg.id.codigo";

                hql.addToFrom("SubGrupo sg");

                hql.addToWhereWhithAnd("sg.id.codigo = dmi.produto.subGrupo.id.codigo");

                if ( this.bean.getFiltroFormaApresentacao() != null &&
                        this.bean.getFiltroFormaApresentacao().size() > 0 ){
                    hql.addToWhereWhithAnd( "sg.roGrupoProduto in ( :fas )" );
                }

                hql.addToGroup("sg.descricao");
                hql.addToOrder("sg.descricao");

            }
            // </editor-fold>
            /*---------------------------------------------------------------------*/
            
        /*
         * CONTROLE SOBRE TOPs POR EMPRESA
         * -------------------------------
         * Consulta para averiguar quais formas de apresentao foram top para
         * o periodo informado.
         *---------------------------------------------------------------------*/
            if ( chave != null &&
                    this.topGraficoCruzamentoDispensacaoPeriodo.keySet().size() > 0 ){
                String str = " ( ";
                for (Iterator it = this.topGraficoCruzamentoDispensacaoPeriodo.keySet().iterator(); it.hasNext();) {
                    Long codigoEmpresa = (Long) it.next();
                    
                    str += "( e.codigo = :codigoEmpresa" + codigoEmpresa + " and " + chave + " in ( :codigoEmpresaIn" + codigoEmpresa + " ) ) ";
                    
                    if ( it.hasNext() ){
                        str += " or ";
                    }
                }
                str += " ) ";
                hql.addToWhereWhithAnd( str );
            }
        }
        //Ordenacao...
        
        hql.addToGroup( "month( dm.dataDispensacao )" );
        hql.addToGroup( "year( dm.dataDispensacao )" );
        
        hql.addToOrder("min( dm.dataDispensacao )");
        //Ordena pela 3 coluna do select....que  o valor
        hql.addToOrder("3 desc");
        
        /*--------------------------------------------------------------------*/
        
        Query query = session.createQuery(hql.getQuery());
        
        /*
         * PARAMETROS
         * ----------
         *---------------------------------------------------------------------*/
        // parametro data inicial e data final dos periodos
        hql.setParameterValue( query, "dataInicial1", this.bean.getDataInicial1() );
        hql.setParameterValue( query, "dataFinal1", this.bean.getDataFinal1());
        hql.setParameterValue( query, "dataInicial2", this.bean.getDataInicial2() );
        hql.setParameterValue( query, "dataFinal2", this.bean.getDataFinal2());
        
        if ( this.bean.getEmpresas() != null &&
                this.bean.getEmpresas().size() > 0 ){
            hql.setParameterValue( query, "empresas", this.bean.getEmpresas() );
        } else{
            hql.setParameterValue( query, "empresas", new Empresa( this.sessao.getCodigoEmpresa() ) );
        }
        
        
        
        if ( this.bean.getFiltroFormaApresentacao() != null &&
                this.bean.getFiltroFormaApresentacao().size() > 0 ){
            hql.setParameterValue( query, "fas", this.bean.getFiltroFormaApresentacao() );
        }
        
        if ( this.bean.getFormaApresentacao() != null ){
            for (Iterator it = this.topGraficoCruzamentoDispensacaoPeriodo.keySet().iterator(); it.hasNext();) {
                Long codigoEmpresa = (Long) it.next();
                
                hql.setParameterValue(query, "codigoEmpresa" + codigoEmpresa, codigoEmpresa);
                
                hql.setParameterValue(query, "codigoEmpresaIn" + codigoEmpresa, this.topGraficoCruzamentoDispensacaoPeriodo.get( codigoEmpresa ));
                
            }
        }
        /*
         * EXECUTA A QUERY
         * ---------------
         * Caso o tipo de dado for em percentual, ser calculado o total geral da consulta,
         * utilizado para o calculo da mesma.
         *---------------------------------------------------------------------*/
        
        this.listDispensacao.addAll( query.list() );
        
        if ( this.bean.getTipoDado().equals( RelatorioGraficoDemonstrativoDispensacaoDTOParam.TIPO_PERCENTUAL ) ){
            
            Map< Empresa, Double > totais = new HashMap< Empresa, Double >();
            Double total = null;
            Empresa empresa = null;
            int i = 0;
            for ( RelatorioGraficoDemonstrativoDispensacaoDTO  dto : this.listDispensacao ){
                if ( dto.getEmpresa().equals( empresa ) ){
                    total += Coalesce.asDouble( dto.getValor() );
                } else{
                    if ( empresa != null ){
                        totais.put( empresa, total );
                    }
                    
                    total = 0D;
                    
                    empresa = dto.getEmpresa();
                    total += Coalesce.asDouble( dto.getValor() );
                    
                }
                
                i++;
            }
            
            if ( empresa != null ){
                totais.put( empresa, total );
            }
            
            for ( RelatorioGraficoDemonstrativoDispensacaoDTO dto : this.listDispensacao ){
                dto.setValorTotal( totais.get( dto.getEmpresa() ) );
            }
        }

    }
    
    public List<RelatorioGraficoDemonstrativoDispensacaoDTO> getListDispensacao()   {
        return listDispensacao;
    }
    
    /**
     * Holds value of property bean.
     */
    private RelatorioGraficoCruzamentoDispensacaoPeriodoDTOParam bean;
    
    /**
     * Getter for property bean.
     * @return Value of property bean.
     */
    public RelatorioGraficoCruzamentoDispensacaoPeriodoDTOParam getBean() {
        
        return this.bean;
    }
    
    /**
     * Setter for property bean.
     * @param bean New value of property bean.
     */
    public void setBean(RelatorioGraficoCruzamentoDispensacaoPeriodoDTOParam bean) {
        
        this.bean = bean;
    }
    
    
}
