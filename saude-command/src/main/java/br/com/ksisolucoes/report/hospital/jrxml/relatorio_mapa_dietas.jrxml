<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_atendimentos" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="9155b179-bb03-426d-a648-bc310bac77b6">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.4157650000000053"/>
	<property name="ireport.x" value="722"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.report.hospital.interfaces.dto.RelatorioMapaDietasDTOParam.FormaApresentacao"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.report.hospital.interfaces.dto.RelatorioMapaDietasDTO"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.Convenio"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="formaApresentacao" class="br.com.ksisolucoes.report.hospital.interfaces.dto.RelatorioMapaDietasDTOParam.FormaApresentacao"/>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="atendimentoInformacao" class="br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao"/>
	<field name="eloTipoDietaReceituario" class="br.com.ksisolucoes.vo.prontuario.basico.EloTipoDietaReceituario"/>
	<field name="tipoDieta" class="br.com.ksisolucoes.vo.prontuario.hospital.TipoDieta"/>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="leitoQuarto" class="br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto"/>
	<field name="quartoInternacao" class="br.com.ksisolucoes.vo.prontuario.hospital.QuartoInternacao"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<group name="fa Group" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[FormaApresentacao.SETOR.equals($P{formaApresentacao})
?
$F{empresa}
:
$F{tipoDieta}]]></groupExpression>
		<groupHeader>
			<band height="29" splitType="Stretch">
				<rectangle radius="5">
					<reportElement key="rectangle-1" x="0" y="4" width="802" height="13" uuid="062d9628-de29-4ab9-acc6-622fd2b1f86b"/>
				</rectangle>
				<textField evaluationTime="Group" evaluationGroup="fa Group" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-1" x="0" y="4" width="802" height="13" uuid="b9688033-a55f-4fb7-894e-a2be7b80d264"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[FormaApresentacao.SETOR.value().equals($P{formaApresentacao}.value())
?
Bundle.getStringApplication("rotulo_empresa") +": "+ $F{empresa}.getDescricao()
:
Bundle.getStringApplication("rotulo_tipo_dieta") +": "+ $F{tipoDieta}.getDescricao()]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="0" y="18" width="105" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="d6cac8f7-7f34-49d5-914a-98848eeb1a3c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Setor*/$V{BUNDLE}.getStringApplication("rotulo_setor")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="106" y="18" width="71" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="d6cac8f7-7f34-49d5-914a-98848eeb1a3c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Leito*/$V{BUNDLE}.getStringApplication("rotulo_leito")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="179" y="18" width="166" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="d6cac8f7-7f34-49d5-914a-98848eeb1a3c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Paciente*/$V{BUNDLE}.getStringApplication("rotulo_paciente")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="520" y="18" width="282" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="d6cac8f7-7f34-49d5-914a-98848eeb1a3c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Observação*/$V{BUNDLE}.getStringApplication("rotulo_observacao")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="28" width="802" height="1" uuid="aea060cb-9f73-42da-a184-43e78ff6256f"/>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="398" y="18" width="120" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="d6cac8f7-7f34-49d5-914a-98848eeb1a3c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*TipoDieta*/$V{BUNDLE}.getStringApplication("rotulo_tipo_dieta")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="344" y="18" width="50" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="a2e1d361-9d4a-4e84-92bb-ae2bf871357b"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Idade*/$V{BUNDLE}.getStringApplication("rotulo_idade")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<rectangle>
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" isPrintRepeatedValues="false" x="0" y="0" width="802" height="13" backcolor="#DFDFDF" uuid="5fece0e9-30ce-4b0d-944a-d5b2bf320528">
					<printWhenExpression><![CDATA[$V{COLUMN_COUNT}%2==1]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="0" width="105" height="10" uuid="a24a2001-9865-4bf3-b80e-6dc4bf4d64d5"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresa}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="179" y="0" width="166" height="10" uuid="a24a2001-9865-4bf3-b80e-6dc4bf4d64d5"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="106" y="0" width="71" height="10" uuid="934378a6-3bd0-4488-b68d-1b435ef0812d"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{quartoInternacao}.getDescricao() == null
?
""
:
$F{quartoInternacao}.getDescricao())
+ " - " +
($F{leitoQuarto}.getDescricao() == null
?
""
:
$F{leitoQuarto}.getDescricao())]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="520" y="0" width="282" height="10" uuid="a24a2001-9865-4bf3-b80e-6dc4bf4d64d5"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{eloTipoDietaReceituario}.getObservacaoDieta()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="398" y="0" width="120" height="10" uuid="a24a2001-9865-4bf3-b80e-6dc4bf4d64d5"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["Sem Dieta Prescrita".equals($F{tipoDieta}.getDescricao()) ? "" : $F{tipoDieta}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="344" y="0" width="50" height="10" uuid="d5302b56-2e60-428b-af4f-bded3daae541"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDescricaoIdadeSimples()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
