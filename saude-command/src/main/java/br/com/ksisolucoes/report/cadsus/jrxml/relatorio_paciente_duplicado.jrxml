<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_paciente_duplicado" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="782" leftMargin="30" rightMargin="30" topMargin="30" bottomMargin="30" uuid="27ad25c5-2b72-4b05-9f9e-24ba901169d3">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.9487171000000014"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.cadsus.ViewPacientesDuplicados"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<subDataset name="pacienteList" uuid="47dfc3aa-1266-4619-ba3e-823a39fe3f8c">
		<field name="nomeFormatado" class="java.lang.String"/>
		<field name="cns" class="java.lang.String"/>
		<field name="cpf" class="java.lang.String"/>
		<field name="dataNascimento" class="java.util.Date"/>
		<field name="nomeMae" class="java.lang.String"/>
	</subDataset>
	<field name="pacienteList" class="java.util.List"/>
	<field name="tipoDocumento" class="java.lang.Long"/>
	<field name="informacao" class="java.lang.String"/>
	<field name="cpfFormatado" class="java.lang.String"/>
	<field name="cnsFormatado" class="java.lang.String"/>
	<variable name="count" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[1]]></variableExpression>
	</variable>
	<group name="tipoDocumento">
		<groupExpression><![CDATA[$F{tipoDocumento}]]></groupExpression>
		<groupHeader>
			<band height="26">
				<rectangle radius="10">
					<reportElement x="0" y="4" width="782" height="18" uuid="c9e70bb1-75e3-431e-b3a5-555f6940f52c"/>
				</rectangle>
				<textField>
					<reportElement x="0" y="7" width="782" height="13" uuid="e0d998e8-8e27-4cf5-b548-1e4f02869b24"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[(ViewPacientesDuplicados.TIPO_DOCUMENTO_CNS.equals($F{tipoDocumento}))
    ?
        Bundle.getStringApplication("rotulo_cns").toUpperCase()
    :
        (ViewPacientesDuplicados.TIPO_DOCUMENTO_RG.equals($F{tipoDocumento}))
            ?
                Bundle.getStringApplication("rotulo_rg").toUpperCase()
            :
                (ViewPacientesDuplicados.TIPO_DOCUMENTO_CPF.equals($F{tipoDocumento}))
                    ?
                        Bundle.getStringApplication("rotulo_cpf").toUpperCase()
                    :
                        (ViewPacientesDuplicados.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO.equals($F{tipoDocumento}))
                            ?
                                Bundle.getStringApplication("rotulo_certidao_nascimento").toUpperCase()
                            :
                                Bundle.getStringApplication("rotulo_certidao_casamento").toUpperCase()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="documento">
		<groupExpression><![CDATA[$F{informacao}]]></groupExpression>
		<groupHeader>
			<band height="32">
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="0" y="20" width="170" height="12" uuid="be128153-8056-4e72-add2-de125ca67f9b"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="503" y="20" width="95" height="12" uuid="72b51dcc-66d8-4564-bd06-ea4b8e42f992"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_nascimento")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="31" width="782" height="1" uuid="884e8dca-de53-43f5-8554-bced89b711ae"/>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="603" y="20" width="96" height="12" uuid="bb058ec8-0be2-4e9d-805a-4f05121ac593"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cns")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="5" width="782" height="12" uuid="2eb1b62c-5397-4012-bc76-8838b1f98fb9"/>
					<textElement>
						<font fontName="Arial" size="10" isBold="true" isItalic="true" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA[(ViewPacientesDuplicados.TIPO_DOCUMENTO_CNS.equals($F{tipoDocumento}))
    ?
        Bundle.getStringApplication("rotulo_cns").toUpperCase() + ": " + $F{cnsFormatado}
    :
        (ViewPacientesDuplicados.TIPO_DOCUMENTO_RG.equals($F{tipoDocumento}))
            ?
                Bundle.getStringApplication("rotulo_rg").toUpperCase() + ": " + $F{informacao}
            :
                (ViewPacientesDuplicados.TIPO_DOCUMENTO_CPF.equals($F{tipoDocumento}))
                    ?
                        Bundle.getStringApplication("rotulo_cpf").toUpperCase() + ": " + $F{cpfFormatado}
                    :
                        (ViewPacientesDuplicados.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO.equals($F{tipoDocumento}))
                            ?
                                Bundle.getStringApplication("rotulo_certidao_nascimento").toUpperCase() + ": " + $F{informacao}
                            :
                                Bundle.getStringApplication("rotulo_certidao_casamento").toUpperCase() + ": " + $F{informacao}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="702" y="20" width="80" height="12" uuid="fc72cffa-8544-4c96-8481-161ab69c2334"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cpf")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="259" y="17" width="170" height="12" uuid="1da964bb-9c3d-41aa-9c90-4c722f0763b0"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_mae")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="17" splitType="Stretch">
			<componentElement>
				<reportElement x="0" y="0" width="782" height="17" uuid="c01c08ee-6c19-46af-88e9-555aa7550124"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="pacienteList" uuid="056e713c-454d-4367-8051-c493a4fe3e7f">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{pacienteList})]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="17" width="782">
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement x="0" y="2" width="258" height="12" uuid="7ad0b991-5601-465a-b537-5d72087eaca7"/>
							<textElement>
								<font fontName="Arial" size="9"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{nomeFormatado}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
							<reportElement x="503" y="2" width="95" height="12" uuid="1713199e-1e08-46d6-89dc-09af35ad2dca"/>
							<textElement>
								<font fontName="Arial" size="9"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{dataNascimento}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
							<reportElement x="603" y="2" width="96" height="12" uuid="0c408be3-afb1-428f-aee6-567726102eee"/>
							<textElement>
								<font fontName="Arial" size="9"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{cns}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
							<reportElement x="702" y="2" width="80" height="12" uuid="a64fa9bf-380b-4178-b47b-c8485fa317fa"/>
							<textElement>
								<font fontName="Arial" size="9"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{cpf}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement x="259" y="2" width="243" height="12" uuid="4f577429-74c0-4e7e-a4ee-cb2b1f55cd9a"/>
							<textElement>
								<font fontName="Arial" size="9"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{nomeMae}]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
