<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_atendimentos" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="b6a58d48-66c1-4074-a38b-11281439b877">
	<property name="ireport.zoom" value="1.7715610000000075"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<import value="br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioRelacaoEntradaPacienteDTOParam"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="br.com.ksisolucoes.vo.basico.Cidade"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.Convenio"/>
	<import value="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"/>
	<import value="br.com.ksisolucoes.vo.basico.Empresa"/>
	<import value="br.com.celk.util.DataUtil"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="agruparUnidade" class="java.lang.String"/>
	<parameter name="formaApresentacao" class="java.lang.Long"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="atendimento" class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento">
		<fieldDescription><![CDATA[atendimento]]></fieldDescription>
	</field>
	<variable name="qtdTipoAtendimento" class="java.lang.Long" resetType="Group" resetGroup="Forma Apresentacao" calculation="DistinctCount">
		<variableExpression><![CDATA[$F{atendimento}.getCodigo()]]></variableExpression>
	</variable>
	<variable name="numeroAtendimentosGeral" class="java.lang.Long" calculation="DistinctCount">
		<variableExpression><![CDATA[$F{atendimento}.getCodigo()]]></variableExpression>
	</variable>
	<group name="Geral">
		<groupFooter>
			<band height="17">
				<textField isBlankWhenNull="true">
					<reportElement x="499" y="0" width="56" height="17" uuid="4bbcfc6a-8679-4e4b-88a1-99ab1fb2dd1b">
						<printWhenExpression><![CDATA[$P{formaApresentacao} != RelatorioRelacaoEntradaPacienteDTOParam.FormaApresentacao.GERAL.value()]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[RelatorioRelacaoEntradaPacienteDTOParam.FormaApresentacao.GERAL.value().equals($P{formaApresentacao})
    ?
        ""
            :
                RelatorioRelacaoEntradaPacienteDTOParam.FormaApresentacao.TIPO_ATENDIMENTO.value().equals($P{formaApresentacao})
                    ?
                        $V{numeroAtendimentosGeral}
                            :
                                RelatorioRelacaoEntradaPacienteDTOParam.FormaApresentacao.DATA.value().equals($P{formaApresentacao})
                                    ?
                                        $V{numeroAtendimentosGeral}
                                            :
                                                ""]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="379" y="0" width="113" height="17" forecolor="#000000" backcolor="#FFFFFF" uuid="e393cf67-4bdb-4493-a8d5-2de7f5e592dd">
						<printWhenExpression><![CDATA[$P{formaApresentacao} != RelatorioRelacaoEntradaPacienteDTOParam.FormaApresentacao.GERAL.value() ? true : false]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[RelatorioRelacaoEntradaPacienteDTOParam.FormaApresentacao.GERAL.value().equals($P{formaApresentacao})
    ?
        ""
            :
                RelatorioRelacaoEntradaPacienteDTOParam.FormaApresentacao.TIPO_ATENDIMENTO.value().equals($P{formaApresentacao})
                    ?
                        "Total Geral: "
                            :
                                RelatorioRelacaoEntradaPacienteDTOParam.FormaApresentacao.DATA.value().equals($P{formaApresentacao})
                                    ?
                                        "Total Geral: "
                                            :
                                                ""]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="Forma Apresentacao">
		<groupExpression><![CDATA[/*Forma Apresentação*/
RelatorioRelacaoEntradaPacienteDTOParam.FormaApresentacao.GERAL.value().equals($P{formaApresentacao})
    ?
        "Geral"
            :
                RelatorioRelacaoEntradaPacienteDTOParam.FormaApresentacao.TIPO_ATENDIMENTO.value().equals($P{formaApresentacao})
                    ?
                        "Tipo Atendimento: " + $F{atendimento}.getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricao()
                            :
                                RelatorioRelacaoEntradaPacienteDTOParam.FormaApresentacao.DATA.value().equals($P{formaApresentacao})
                                    ?
                                        "Data: " +  DataUtil.getFormatarDiaMesAno($F{atendimento}.getDataChegada())
                                            :
                                                null]]></groupExpression>
		<groupHeader>
			<band height="34">
				<textField>
					<reportElement positionType="Float" mode="Transparent" x="0" y="5" width="555" height="12" uuid="2e241219-4590-44d3-b132-7759c844dacb"/>
					<textElement>
						<font isBold="true" isItalic="true" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Forma Apresentação*/
RelatorioRelacaoEntradaPacienteDTOParam.FormaApresentacao.GERAL.value().equals($P{formaApresentacao})
    ?
        "Geral"
            :
                RelatorioRelacaoEntradaPacienteDTOParam.FormaApresentacao.TIPO_ATENDIMENTO.value().equals($P{formaApresentacao})
                    ?
                        "Tipo Atendimento: " + $F{atendimento}.getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricao()
                            :
                                RelatorioRelacaoEntradaPacienteDTOParam.FormaApresentacao.DATA.value().equals($P{formaApresentacao})
                                    ?
                                        "Data: " +  DataUtil.getFormatarDiaMesAno($F{atendimento}.getDataChegada())
                                            :
                                                ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="301" y="22" width="85" height="12" isPrintWhenDetailOverflows="true" uuid="b18507f1-2e9a-46e2-8e1d-8988d29cf453"/>
					<textElement>
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*paciente*/
Bundle.getStringApplication("rotulo_paciente")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="429" y="20" width="126" height="12" isPrintWhenDetailOverflows="true" uuid="7f7e6a14-1c23-40b5-a255-10573793caf4"/>
					<textElement>
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*tipoAtendimento*/
Bundle.getStringApplication("rotulo_tipo_atendimento")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement positionType="Float" x="227" y="21" width="63" height="12" isPrintWhenDetailOverflows="true" uuid="56f8e47a-2aac-4017-934f-47e0d03e603a"/>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Data de Entrada/hora"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="390" y="21" width="39" height="13" isPrintWhenDetailOverflows="true" uuid="8da378d7-b429-4aa8-b642-57be408a05ac"/>
					<textElement>
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*idade*/
Bundle.getStringApplication("rotulo_idade")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement positionType="Float" x="0" y="33" width="555" height="1" uuid="1cf24d05-d4ca-4cb9-b76e-7a3859cc0f4f"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField>
					<reportElement positionType="Float" x="0" y="21" width="96" height="13" isPrintWhenDetailOverflows="true" uuid="e7c12b46-31d4-43a9-9fce-6b2ae8c6a136"/>
					<textElement>
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*usuario*/Bundle.getStringApplication("rotulo_usuario")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="96" y="21" width="125" height="12" isPrintWhenDetailOverflows="true" uuid="77f533e3-b7eb-4fb9-a975-64d817a018a5"/>
					<textElement>
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*unidade*/
Bundle.getStringApplication("rotulo_unidade")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="21">
				<textField isBlankWhenNull="true">
					<reportElement x="499" y="3" width="56" height="18" uuid="0904d26e-ac42-4d82-a0df-468e4fae40b4"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{qtdTipoAtendimento}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="380" y="3" width="113" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="d95dbcea-3540-4e25-af7d-14525142164c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA["Total: "]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="359" y="1" width="196" height="1" uuid="ae0baf9f-2a4c-4ba8-984c-a9212e213e6a"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="28">
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="227" y="0" width="63" height="15" isPrintWhenDetailOverflows="true" uuid="a17dbbe6-9d44-4431-8587-1d075c3c81ad"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimento}.getDataHoraChegada()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="301" y="0" width="85" height="15" isPrintWhenDetailOverflows="true" uuid="b47b928c-bed7-4f6e-b528-26c52e795dbe"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimento}.getUsuarioCadsus().getNome()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="390" y="0" width="39" height="15" isPrintWhenDetailOverflows="true" uuid="af4828f6-9e93-4964-a520-43092436b366"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimento}.getUsuarioCadsus().getIdade()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="429" y="0" width="126" height="15" isPrintWhenDetailOverflows="true" uuid="1f945d59-8ade-40cf-af4f-3891b161bf34"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimento}.getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="0" width="96" height="15" isPrintWhenDetailOverflows="true" uuid="2a25aa20-5cb3-47a9-b3b0-b9baa50b78ed"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimento}.getUsuario().getNome()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="96" y="0" width="125" height="15" isPrintWhenDetailOverflows="true" uuid="968b0959-4d2c-4750-80cf-7e3abb6dcfe7"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimento}.getEmpresa().getDescricao()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
