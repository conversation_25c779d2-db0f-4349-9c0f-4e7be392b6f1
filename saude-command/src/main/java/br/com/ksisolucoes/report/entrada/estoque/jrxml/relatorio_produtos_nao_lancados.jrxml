<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_produtos_nao_lancados" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="c5cdbac1-c9f5-48bb-9c92-a7fc77a9e7bd">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.853116706110003"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.vo.entradas.estoque.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.report.ReportProperties"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<parameter name="ParamFormaApresentacao" class="java.lang.String" isForPrompting="false"/>
	<parameter name="utilizaLocalizacaoEstoque" class="java.lang.Boolean"/>
	<field name="class" class="java.lang.Object"/>
	<field name="codigoGrupoProduto" class="java.lang.Long"/>
	<field name="codigoLocalizacao" class="java.lang.Long"/>
	<field name="codigoProduto" class="java.lang.String"/>
	<field name="codigoSubGrupo" class="java.lang.Long"/>
	<field name="descricaoGrupoFormatado" class="java.lang.String"/>
	<field name="descricaoGrupoProduto" class="java.lang.String"/>
	<field name="descricaoLocalizacao" class="java.lang.String"/>
	<field name="descricaoProduto" class="java.lang.String"/>
	<field name="descricaoProdutoFormatado" class="java.lang.String"/>
	<field name="descricaoSubGrupo" class="java.lang.String"/>
	<field name="descricaoSubGrupoFormatado" class="java.lang.String"/>
	<field name="estoqueFisico" class="java.lang.Double"/>
	<field name="grupoEstoque" class="java.lang.String"/>
	<field name="descricaoDepositoFormatado" class="java.lang.String"/>
	<field name="localizacaoEstrutura" class="br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura"/>
	<group name="GrupoGrupo" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{codigoSubGrupo}]]></groupExpression>
		<groupHeader>
			<band height="28" splitType="Stretch">
				<rectangle radius="5">
					<reportElement uuid="653ccc1b-29d0-4c19-80f9-101e82f258cd" key="rectangle-1" mode="Opaque" x="0" y="15" width="535" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</rectangle>
				<textField evaluationTime="Group" evaluationGroup="GrupoGrupo" pattern="" isBlankWhenNull="false">
					<reportElement uuid="b7454f5a-c82e-43d2-b75b-b4131c7798dd" key="textField-43" mode="Opaque" x="0" y="0" width="535" height="14" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*grupo*/ /*subgrupo*/
$F{codigoGrupoProduto} == null  ? Bundle.getStringApplication("rotulo_grupo")  + ": " + Bundle.getStringApplication("rotulo_sem_cadastro"):
Bundle.getStringApplication("rotulo_grupo") + ":" + " " + $F{descricaoGrupoProduto} + " - " + ($F{codigoSubGrupo} == null ? Bundle.getStringApplication("rotulo_subgrupo") + ":" + Bundle.getStringApplication("rotulo_sem_cadastro"):
Bundle.getStringApplication("rotulo_subgrupo") + ":" + " " + $F{descricaoSubGrupo})]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="19bcf9f0-1912-4a5e-95f7-666f66711dcf" key="textField-45" mode="Opaque" x="6" y="16" width="170" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*produto*/Bundle.getStringApplication("rotulo_produto")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="b0dc48f9-71bd-4536-8273-eb8b51dd5a57" key="textField-51" mode="Opaque" x="296" y="16" width="91" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*grupo*/Bundle.getStringApplication("rotulo_grupo")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="0bbbd168-36e0-41e9-9aaf-f6d4ef729b5c" key="textField-52" mode="Opaque" x="471" y="16" width="58" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*localizacao*/Bundle.getStringApplication("rotulo_localizacao")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="7c88826b-9a4f-4314-b4ad-5be7985b2dc0" key="textField-76" mode="Opaque" x="387" y="16" width="84" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*subGrupo*/Bundle.getStringApplication("rotulo_sub_grupo")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="bc825889-39e7-4112-a355-5b50d156188c" key="textField-51" mode="Opaque" x="220" y="16" width="29" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*lote*/Bundle.getStringApplication("rotulo_lote")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="aa6586e9-7651-419a-83c0-ae5a269e2335" key="textField-51" mode="Opaque" x="249" y="16" width="39" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*estoqueFisico*/Bundle.getStringApplication("rotulo_estoque_fisico_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="422b9e70-d2a3-4169-805b-29af86aedc5e" key="textField-51" mode="Opaque" x="176" y="16" width="44" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*deposito*/Bundle.getStringApplication("rotulo_deposito")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="15" splitType="Stretch">
			<rectangle radius="5">
				<reportElement uuid="83995ad6-f0fe-4f3c-92e0-9d3c32a5b97d" key="rectangle-2" mode="Opaque" x="0" y="0" width="535" height="15" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
				</reportElement>
				<graphicElement fill="Solid">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="1ad614b1-4d5c-425c-9830-0e90b7c08746" key="textField-59" mode="Opaque" x="5" y="3" width="171" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[/*produto*/Bundle.getStringApplication("rotulo_produto")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="8c361c81-6df9-46ef-b507-371f2597f177" key="textField-64" mode="Opaque" x="296" y="3" width="91" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[/*grupo*/Bundle.getStringApplication("rotulo_grupo")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="99bd8834-7b18-4895-8a4b-36f02a0f8b57" key="textField-65" mode="Opaque" x="471" y="3" width="58" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[/*localizacao*/Bundle.getStringApplication("rotulo_localizacao")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="fbf1e1bb-68a5-4785-a5e2-aa4eec92ba28" key="textField-74" mode="Opaque" x="387" y="3" width="84" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[/*subGrupo*/Bundle.getStringApplication("rotulo_sub_grupo")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="89726d33-bb38-407d-95cb-2a059217a0d4" key="textField-51" mode="Opaque" x="220" y="3" width="29" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[/*lote*/Bundle.getStringApplication("rotulo_lote")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="8b74a7ab-355e-453e-a325-f5dfc6dd5bde" key="textField-51" mode="Opaque" x="251" y="3" width="37" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[/*estoqueFisico*/Bundle.getStringApplication("rotulo_estoque_fisico_abv")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="791529ae-5a77-495b-a357-d14afba58237" key="textField-51" mode="Opaque" x="176" y="3" width="44" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[/*deposito*/Bundle.getStringApplication("rotulo_deposito")]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="10" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="64958a82-afb1-40eb-91f6-d092db0e8dcf" key="textField-5" mode="Opaque" x="6" y="0" width="170" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoProdutoFormatado}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="08617fad-92b7-4bd0-8fdd-04785422ae52" key="textField-57" mode="Opaque" x="296" y="0" width="91" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoGrupoFormatado}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="3aa403c2-72e6-4e76-a21a-94bc2520a02e" key="textField-58" mode="Opaque" x="471" y="0" width="58" height="10" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[$P{utilizaLocalizacaoEstoque}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{localizacaoEstrutura}.getMascara()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="82c42810-0bb0-45c1-9853-a7538af3b522" key="textField-77" mode="Opaque" x="387" y="0" width="84" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoSubGrupoFormatado}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="f4cf8a44-fb24-4dfe-a9e1-87d78e62785b" key="textField-57" mode="Opaque" x="220" y="0" width="29" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{grupoEstoque}]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement uuid="73878c8a-6868-417b-b3f2-99702bd5ab68" key="textField-57" mode="Opaque" x="249" y="0" width="39" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{estoqueFisico}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="840d3d65-2796-4773-966f-279bc6bbd55f" key="textField-57" mode="Opaque" x="176" y="0" width="44" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoDepositoFormatado}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
