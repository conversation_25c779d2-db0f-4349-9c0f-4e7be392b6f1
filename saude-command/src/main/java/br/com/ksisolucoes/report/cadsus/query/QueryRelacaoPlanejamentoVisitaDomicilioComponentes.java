package br.com.ksisolucoes.report.cadsus.query;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.QueryPlanejamentoVisitasDTOParam;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEsus;
import org.hibernate.Query;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryRelacaoPlanejamentoVisitaDomicilioComponentes extends CommandQuery<QueryRelacaoPlanejamentoVisita> implements ITransferDataReport<QueryPlanejamentoVisitasDTOParam, UsuarioCadsusEsus> {

    private Collection<UsuarioCadsusEsus> result;
    Long codigoDomicilios;

    public QueryRelacaoPlanejamentoVisitaDomicilioComponentes(Long codigoDomiciliosList) {
        this.codigoDomicilios = codigoDomiciliosList;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(UsuarioCadsusEsus.class.getName());

        hql.addToSelect("usuarioCadsusEsus.temDiabetes", "temDiabetes");
        hql.addToSelect("usuarioCadsusEsus.temHipertensao", "temHipertensao");
        hql.addToSelect("usuarioCadsusEsus.estaGestante", "estaGestante");
        hql.addToSelect("usuarioCadsusEsus.estaAcamado", "estaAcamado");
        hql.addToSelect("usuarioCadsusEsus.temHanseniase", "temHanseniase");
        hql.addToSelect("usuarioCadsusEsus.temTuberculose", "temTuberculose");
        hql.addToSelect("usuarioCadsusEsus.sofrimentoPsiquicoGrave", "sofrimentoPsiquicoGrave");
        hql.addToSelect("usuarioCadsusEsus.fezTratamentoPsiquiatrico", "fezTratamentoPsiquiatrico");

        hql.addToSelect("usuarioCadsus.nome", "usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.dataNascimento", "usuarioCadsus.dataNascimento");
        hql.addToSelect("usuarioCadsus.celular", "usuarioCadsus.celular");
        hql.addToSelect("usuarioCadsus.telefone", "usuarioCadsus.telefone");
        hql.addToSelect("usuarioCadsus.telefone2", "usuarioCadsus.telefone2");
        hql.addToSelect("usuarioCadsus.telefone3", "usuarioCadsus.telefone3");
        hql.addToSelect("usuarioCadsus.telefone4", "usuarioCadsus.telefone4");

        hql.addToSelect("enderecoDomicilio.codigo", "usuarioCadsus.enderecoDomicilio.codigo");

        StringBuilder from = new StringBuilder("UsuarioCadsusEsus usuarioCadsusEsus ");
        from.append("left join usuarioCadsusEsus.usuarioCadsus usuarioCadsus ");
        from.append("left join usuarioCadsus.enderecoDomicilio enderecoDomicilio ");
        hql.addToFrom(from.toString());

        hql.addToWhereWhithAnd("enderecoDomicilio.codigo = ", codigoDomicilios);
        hql.addToWhereWhithAnd("usuarioCadsus.situacao in ", Arrays.asList(UsuarioCadsus.SITUACAO_ATIVO, UsuarioCadsus.SITUACAO_PROVISORIO));
        hql.addToWhereWhithAnd("(( usuarioCadsusEsus.temDiabetes = " + RepositoryComponentDefault.SIM_LONG + " OR "
                + "usuarioCadsusEsus.temHipertensao = " + RepositoryComponentDefault.SIM_LONG + " OR "
                + "usuarioCadsusEsus.estaGestante = " + RepositoryComponentDefault.SIM_LONG + " OR "
                + "usuarioCadsusEsus.estaAcamado = " + RepositoryComponentDefault.SIM_LONG + " OR "
                + "usuarioCadsusEsus.temHanseniase = " + RepositoryComponentDefault.SIM_LONG + " OR "
                + "usuarioCadsusEsus.temTuberculose = " + RepositoryComponentDefault.SIM_LONG + " OR "
                + "usuarioCadsusEsus.sofrimentoPsiquicoGrave = " + RepositoryComponentDefault.SIM_LONG + " ) OR ( "
                + "usuarioCadsusEsus.fezTratamentoPsiquiatrico = " + RepositoryComponentDefault.SIM_LONG + " ) OR ( "
                + "usuarioCadsus.dataNascimento BETWEEN  :dataInicial AND :dataFinal ))");

    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        super.setParameters(hql, query);
        query.setDate("dataFinal", Data.removeMeses(DataUtil.getDataAtual(), ((Long) BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("planejamentoVisitaCriancaFaixaEtariaInicial")).intValue()));
        query.setDate("dataInicial", Data.removeMeses(DataUtil.getDataAtual(), ((Long) BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("planejamentoVisitaCriancaFaixaEtariaFinal")).intValue()));
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public void setDTOParam(QueryPlanejamentoVisitasDTOParam param) {
    }

    @Override
    public Collection<UsuarioCadsusEsus> getResult() {
        return this.result;
    }
}
