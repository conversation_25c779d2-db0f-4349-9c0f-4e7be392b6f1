<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="sub_relatorio_impressao_comprovante_agendamento_sem_solicitacao" columnCount="2" printOrder="Horizontal" pageWidth="500" pageHeight="120" columnWidth="250" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="4e077fa1-2d97-4446-a877-3e86fd65d886">
	<property name="ireport.zoom" value="1.7715610000000022"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<field name="dataHoraAgendamentoFormatado" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="13">
			<textField>
				<reportElement x="0" y="-1" width="296" height="14" uuid="be047246-596c-4c16-a6bd-897af80d348d"/>
				<box leftPadding="1" rightPadding="1">
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataHoraAgendamentoFormatado}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
