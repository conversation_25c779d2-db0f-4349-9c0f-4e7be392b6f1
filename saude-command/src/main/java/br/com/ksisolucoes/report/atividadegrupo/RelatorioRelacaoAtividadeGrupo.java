package br.com.ksisolucoes.report.atividadegrupo;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.atividadegrupo.interfaces.dto.RelatorioRelacaoAtividadeGrupoDTO;
import br.com.ksisolucoes.report.atividadegrupo.interfaces.dto.RelatorioRelacaoAtividadeGrupoDTOParam;
import br.com.ksisolucoes.report.atividadegrupo.query.QueryRelatorioRelacaoAtividadeGrupo;
import br.com.ksisolucoes.report.atividadegrupo.query.QueryRelatorioRelacaoAtividadeGrupoXlsCsv;
import br.com.ksisolucoes.util.Bundle;
import ch.lambdaj.Lambda;

import java.util.LinkedHashMap;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoAtividadeGrupo extends AbstractReport<RelatorioRelacaoAtividadeGrupoDTOParam> {

    public RelatorioRelacaoAtividadeGrupo(RelatorioRelacaoAtividadeGrupoDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/atividadegrupo/jrxml/relatorio_relacao_atividade_grupo.jrxml";
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> columns = new LinkedHashMap<>();
        RelatorioRelacaoAtividadeGrupoDTO proxy = Lambda.on(RelatorioRelacaoAtividadeGrupoDTO.class);

        columns.put((Bundle.getStringApplication("rotulo_unidade")), proxy.getEmpresa().getDescricao());
        columns.put((Bundle.getStringApplication("rotulo_cnes")), proxy.getEmpresa().getCnes());
        columns.put((Bundle.getStringApplication("rotulo_ine")), proxy.getEquipeCnesIneList());
        columns.put((Bundle.getStringApplication("rotulo_nome_equipe")), proxy.getNomeEquipeList());
        columns.put((Bundle.getStringApplication("rotulo_data")), proxy.getAtividadeGrupo().getDataInicio());
        columns.put((Bundle.getStringApplication("rotulo_turno")), proxy.getAtividadeGrupo().getDescricaoTurno());
        columns.put((Bundle.getStringApplication("rotulo_codigo_atividade")), proxy.getAtividadeGrupo().getCodigo());
        columns.put((Bundle.getStringApplication("rotulo_situacao")), proxy.getAtividadeGrupo().getDescricaoSituacao());
        columns.put((Bundle.getStringApplication("rotulo_publico_alvo")), proxy.getDescricaoPublicoAlvo());
        columns.put((Bundle.getStringApplication("rotulo_temas")), proxy.getDescricaoTemaAtividade());
        columns.put((Bundle.getStringApplication("rotulo_praticas")), proxy.getDescricaoEsusPraticaSaudeList());
        columns.put((Bundle.getStringApplication("rotulo_profissionais")), proxy.getProfissionaisList());
        columns.put((Bundle.getStringApplication("rotulo_tipo_atividade")), proxy.getTipoAtividadeGrupo().getDescricao());
        columns.put((Bundle.getStringApplication("rotulo_nr_inep")), proxy.getAtividadeGrupo().getNumeroInep());
        columns.put((Bundle.getStringApplication("rotulo_assunto")), proxy.getAtividadeGrupo().getAssunto());
        columns.put((Bundle.getStringApplication("rotulo_local_atividade")), proxy.getLocalAtividadeGrupo().getDescricao());
        if (param.isExibirParticipantes()) {
            columns.put((Bundle.getStringApplication("rotulo_nome_participantes")), proxy.getUsuarioCadsus().getNome());
            columns.put((Bundle.getStringApplication("rotulo_cns")), proxy.getUsuarioCadsus().getCns());
            columns.put((Bundle.getStringApplication("rotulo_cpf")), proxy.getUsuarioCadsus().getCpf());
            columns.put((Bundle.getStringApplication("rotulo_data_nascimento")), proxy.getUsuarioCadsus().getDataNascimento());
            columns.put((Bundle.getStringApplication("rotulo_sexo")), proxy.getUsuarioCadsus().getSexo());
            columns.put((Bundle.getStringApplication("rotulo_imc")), proxy.getIndiceImc().getSituacao());
            columns.put((Bundle.getStringApplication("rotulo_peso")), proxy.getAtividadeGrupoPaciente().getPeso());
            columns.put((Bundle.getStringApplication("rotulo_altura")), proxy.getAtividadeGrupoPaciente().getAltura());
            columns.put((Bundle.getStringApplication("rotulo_pas")), proxy.getAtividadeGrupoPaciente().getPressaoArterialSistolica());
            columns.put((Bundle.getStringApplication("rotulo_pad")), proxy.getAtividadeGrupoPaciente().getPressaoArterialDiastolica());
            columns.put((Bundle.getStringApplication("rotulo_avaliacao_alterada")), proxy.getDescricaoAvaliacaoAlterada());
        } else {
            columns.put((Bundle.getStringApplication("rotulo_presentes")), proxy.getTotalPresentes());
            columns.put((Bundle.getStringApplication("rotulo_ausentes")), proxy.getTotalAusentes());
            columns.put((Bundle.getStringApplication("rotulo_nr_part")), proxy.getTotalParticipantes());
        }

        return columns;
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_atividades_grupo");
    }

    @Override
    public ITransferDataReport getQuery() {
        addParametro("formaApresentacao", getParam().getFormaApresentacao());

        if (this.param.getTipoArquivo().equals(TipoRelatorio.XLS2) || this.param.getTipoArquivo().equals(TipoRelatorio.CSV)) {
            return new QueryRelatorioRelacaoAtividadeGrupoXlsCsv();
        }
        return new QueryRelatorioRelacaoAtividadeGrupo();
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return getParam().getTipoArquivo();
    }

}