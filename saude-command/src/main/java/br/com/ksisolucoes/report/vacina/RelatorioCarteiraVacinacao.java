package br.com.ksisolucoes.report.vacina;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.vacina.dto.RelatorioCarteiraVacinacaoDTOParam;
import br.com.ksisolucoes.report.vacina.query.QueryRelatorioCarteiraVacinacao;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioCarteiraVacinacao extends AbstractReport<RelatorioCarteiraVacinacaoDTOParam> {

    public RelatorioCarteiraVacinacao(RelatorioCarteiraVacinacaoDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/vacina/jrxml/relatorio_carteira_vacinacao.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_caderneta_vacinacao");
    }

    @Override
    public ITransferDataReport getQuery() {
        if(param != null && param.getUsuarioCadsus() != null && param.isCabecalhoAppCidadao()){
            addParametro("PACIENTE_APP_CIDADAO", param.getUsuarioCadsus().getNome());
            addParametro("EXIBIR_APP_CIDADAO", param.isCabecalhoAppCidadao());
        }
        addParametro("usuarioCadsus", param.getUsuarioCadsus());
        return new QueryRelatorioCarteiraVacinacao();
    }
}
