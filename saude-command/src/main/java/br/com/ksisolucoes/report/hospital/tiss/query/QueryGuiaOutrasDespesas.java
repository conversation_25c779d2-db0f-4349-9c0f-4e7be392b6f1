package br.com.ksisolucoes.report.hospital.tiss.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.hospital.interfaces.dto.tiss.GuiaOutrasDespesasDTO;
import static br.com.ksisolucoes.report.hospital.interfaces.dto.tiss.GuiaOutrasDespesasDTO.DespesaRealizada;
import br.com.ksisolucoes.report.hospital.interfaces.dto.tiss.RelatorioImpressaoGuiasTissDTOParam;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import static ch.lambdaj.Lambda.on;
import java.util.Arrays;
import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QueryGuiaOutrasDespesas extends CommandQuery<GuiaOutrasDespesasDTO> {

    private RelatorioImpressaoGuiasTissDTOParam param;
    private List<GuiaOutrasDespesasDTO> result;

    public QueryGuiaOutrasDespesas(RelatorioImpressaoGuiasTissDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(GuiaOutrasDespesasDTO.class.getName());
        hql.setDistinct(true);

        hql.addToSelect("view.numeroGuiaPrestador", "numeroGuiaPrestador");
        hql.addToSelect("view.codigoPrestadorExecutanteNaOperadora", "codigoPrestadorExecutanteNaOperadora");
        hql.addToSelect("view.nomeContratadoExecutante", "nomeContratanteExecutante");
        hql.addToSelect("view.cnesContratadoExecutante", "codigoCNES");
        hql.addToSelect("coalesce(view.totalTaxaAluguel, 0)", "totalTaxasAlugueis");
        hql.addToSelect("coalesce(view.totalMaterial, 0)", "totalMateriais");
        hql.addToSelect("coalesce(view.totalOpme, 0)", "totalOPME");
        hql.addToSelect("coalesce(view.totalMedicamento, 0)", "totalMedicamentos");
        hql.addToSelect("coalesce(view.totalGasMedicinal, 0)", "totalGasesMedicinais");
        hql.addToSelect("coalesce(view.totalDiaria, 0)", "totalDiarias");

        hql.addToFrom("ViewContasTiss view");

        hql.addToWhereWhithAnd("view.tipoItemContaPaciente in ", Arrays.asList(ItemContaPaciente.Tipo.OUTRAS_DESPESAS.value(), ItemContaPaciente.Tipo.MATERIAL_MEDICAMENTO.value()));
        hql.addToWhereWhithAnd("view.statusItemContaPaciente = ", ItemContaPaciente.Status.CONFIRMADO.value());
        hql.addToWhereWhithAnd("view.codigoContaPaciente = ", param.getCodigoContaPaciente());
    }

    @Override
    protected void customQuery(Query query) {
        super.customQuery(query);
        query.setMaxResults(1);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        super.customProcess(session);

        Convenio proxy = on(Convenio.class);
        Convenio convenio = LoadManager.getInstance(Convenio.class)
                .addProperty(path(proxy.getCodigo()))
                .addProperty(path(proxy.getRegistroAnsDestino()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getCodigo()), param.getCodigoConvenio()))
                .setMaxResults(1)
                .start().getVO();

        for (GuiaOutrasDespesasDTO item : result) {
            if (convenio != null) {
                item.setRegistroANS(convenio.getRegistroAnsDestino());
            }

            QueryDespesasRealizadas query = new QueryDespesasRealizadas();
            query.start();
            item.setListDespesasRealizadas(query.getResult());
        }
    }

    private class QueryDespesasRealizadas extends CommandQuery {

        private List<DespesaRealizada> result;

        @Override
        protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
            hql.setTypeSelect(DespesaRealizada.class.getName());

            hql.addToSelect("view.codigoDespesa", "codigoDespesa");
            hql.addToSelect("view.dataInicialDespesa", "data");
            hql.addToSelect("view.horaInicialRealizado", "horaInicial");
            hql.addToSelect("view.horaFinalRealizado", "horaFinal");
            hql.addToSelect("view.tabelaReferenciaItemDespesa", "tabela");
            hql.addToSelect("view.codigoProcedimentoDespesa", "codigo");
            hql.addToSelect("view.descricaoProcedimentoDespesa", "descricao");
            hql.addToSelect("view.quantidadeExecutadaDespesa", "qtde");
            hql.addToSelect("view.unidadeMedidaDespesa", "unidadeMedida");
            hql.addToSelect("view.reducaoAcrescimoDespesa", "fatorReducaoAcrescimo");
            hql.addToSelect("coalesce(view.valorUnitarioDespesa, 0)", "valorUnitario");
            hql.addToSelect("coalesce(view.valorTotalDespesa, 0)", "valorTotal");

            hql.addToFrom("ViewContasTiss view");
            hql.addToWhereWhithAnd("view.codigoContaPaciente = ", param.getCodigoContaPaciente());
            hql.addToWhereWhithAnd("view.tipoItemContaPaciente in ", Arrays.asList(ItemContaPaciente.Tipo.OUTRAS_DESPESAS.value(), ItemContaPaciente.Tipo.MATERIAL_MEDICAMENTO.value()));
            hql.addToWhereWhithAnd("view.statusItemContaPaciente = ", ItemContaPaciente.Status.CONFIRMADO.value());
        }

        @Override
        protected void result(HQLHelper hql, Object result) {
            this.result = hql.getBeanList((List) result);
        }

        @Override
        public List<DespesaRealizada> getResult() {
            return result;
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List) result);
    }

    @Override
    public List<GuiaOutrasDespesasDTO> getResult() {
        return result;
    }
}
