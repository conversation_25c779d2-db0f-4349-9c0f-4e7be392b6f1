<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_acompanhamento_solicitacoes" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="782" leftMargin="30" rightMargin="30" topMargin="30" bottomMargin="30">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo"/>
	<import value="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<import value="br.com.ksisolucoes.vo.basico.Empresa"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="formaApresentacao" class="br.com.ksisolucoes.report.agendamento.dto.RelatorioAcompanhamentoSolicitacoesDTOParam.FormaApresentacao"/>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="tipoProcedimento" class="br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento"/>
	<field name="diasMediaEspera" class="java.lang.Long"/>
	<field name="tipoProcedimentoClassificacao" class="br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimentoClassificacao"/>
	<field name="totalFila" class="java.lang.Long"/>
	<field name="totalRegulacao" class="java.lang.Long"/>
	<field name="devolvidoRegulacao" class="java.lang.Long"/>
	<field name="vagasMes" class="java.lang.Long"/>
	<field name="tempoPrevisto" class="java.lang.Long"/>
	<variable name="count" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[1]]></variableExpression>
	</variable>
	<variable name="TOTAL_DIAS_MEDIA_ESPERA" class="java.lang.Long" resetType="Group" resetGroup="FormaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{diasMediaEspera}]]></variableExpression>
		<initialValueExpression><![CDATA[0L]]></initialValueExpression>
	</variable>
	<variable name="TOTAL_G_DIAS_MEDIA_ESPERA" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{diasMediaEspera}]]></variableExpression>
		<initialValueExpression><![CDATA[0L]]></initialValueExpression>
	</variable>
	<variable name="TOTAL_AGUARDANDO" class="java.lang.Long" resetType="Group" resetGroup="FormaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{totalFila}+$F{totalRegulacao}+$F{devolvidoRegulacao}]]></variableExpression>
	</variable>
	<variable name="TOTAL_G_AGUARDANDO" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{totalFila}+$F{totalRegulacao}+$F{devolvidoRegulacao}]]></variableExpression>
	</variable>
	<variable name="TOTAL_DEV_REGULACAO" class="java.lang.Long" resetType="Group" resetGroup="FormaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{devolvidoRegulacao}]]></variableExpression>
	</variable>
	<variable name="TOTAL_G_DEV_REGULACAO" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{devolvidoRegulacao}]]></variableExpression>
	</variable>
	<variable name="TOTAL_REGULACAO" class="java.lang.Long" resetType="Group" resetGroup="FormaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{totalRegulacao}]]></variableExpression>
	</variable>
	<variable name="TOTAL_G_REGULACAO" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{totalRegulacao}]]></variableExpression>
	</variable>
	<variable name="TOTAL_G_FILA" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{totalFila}]]></variableExpression>
	</variable>
	<variable name="TOTAL_FILA" class="java.lang.Long" resetType="Group" resetGroup="FormaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{totalFila}]]></variableExpression>
	</variable>
	<variable name="TOTAL_VAGAS_MES" class="java.lang.Long" resetType="Group" resetGroup="FormaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{vagasMes}]]></variableExpression>
		<initialValueExpression><![CDATA[0L]]></initialValueExpression>
	</variable>
	<variable name="TOTAL_G_VAGAS_MES" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{vagasMes}]]></variableExpression>
		<initialValueExpression><![CDATA[0L]]></initialValueExpression>
	</variable>
	<group name="TOTAL">
		<groupFooter>
			<band height="16">
				<line>
					<reportElement x="370" y="2" width="409" height="1"/>
				</line>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="322" y="4" width="120" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_total_geral")+":"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="444" y="4" width="30" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{TOTAL_G_FILA}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="475" y="4" width="45" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{TOTAL_G_REGULACAO}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="524" y="4" width="46" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{TOTAL_G_DEV_REGULACAO}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="573" y="4" width="55" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{TOTAL_G_AGUARDANDO}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="686" y="4" width="46" height="12">
						<printWhenExpression><![CDATA[!$P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.dto.RelatorioAcompanhamentoSolicitacoesDTOParam.FormaApresentacao.UNIDADE_SOLICITANTE)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{TOTAL_G_VAGAS_MES}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="FormaApresentacao" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.dto.RelatorioAcompanhamentoSolicitacoesDTOParam.FormaApresentacao.UNIDADE_SOLICITANTE)?
$F{empresa}:$F{tipoProcedimento}]]></groupExpression>
		<groupHeader>
			<band height="55">
				<printWhenExpression><![CDATA[! $P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.dto.RelatorioAcompanhamentoSolicitacoesDTOParam.FormaApresentacao.GERAL)]]></printWhenExpression>
				<rectangle radius="10">
					<reportElement mode="Transparent" x="0" y="6" width="782" height="13"/>
				</rectangle>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="0" y="6" width="782" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.dto.RelatorioAcompanhamentoSolicitacoesDTOParam.FormaApresentacao.UNIDADE_SOLICITANTE)
?
    Bundle.getStringApplication("rotulo_unidade_solicitante")+": "+$F{empresa}.getDescricaoFormatado()
:
        ""]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="54" width="782" height="1"/>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="444" y="20" width="30" height="34"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_fila")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="122" y="20" width="320" height="34"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_tipo_procedimento_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="0" y="20" width="120" height="34"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_grupo_procedimento")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="475" y="20" width="45" height="34"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_regulacao")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="573" y="20" width="55" height="34"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_total_aguardando")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="524" y="20" width="46" height="34"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_devolucao_regulacao")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="630" y="20" width="55" height="34"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_dias_media_espera_3_meses")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="686" y="20" width="46" height="34">
						<printWhenExpression><![CDATA[!$P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.dto.RelatorioAcompanhamentoSolicitacoesDTOParam.FormaApresentacao.UNIDADE_SOLICITANTE)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_vagas_no_mes")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="733" y="20" width="46" height="34">
						<printWhenExpression><![CDATA[!$P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.dto.RelatorioAcompanhamentoSolicitacoesDTOParam.FormaApresentacao.UNIDADE_SOLICITANTE)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_tempo_previsto_dias")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="17">
				<printWhenExpression><![CDATA[! $P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.dto.RelatorioAcompanhamentoSolicitacoesDTOParam.FormaApresentacao.GERAL)]]></printWhenExpression>
				<line>
					<reportElement x="370" y="2" width="409" height="1"/>
				</line>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="382" y="4" width="60" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_total")+":"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="444" y="4" width="30" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{TOTAL_FILA}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="475" y="4" width="45" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{TOTAL_REGULACAO}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="524" y="4" width="46" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{TOTAL_DEV_REGULACAO}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="573" y="4" width="55" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{TOTAL_AGUARDANDO}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="686" y="4" width="46" height="12">
						<printWhenExpression><![CDATA[!$P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.dto.RelatorioAcompanhamentoSolicitacoesDTOParam.FormaApresentacao.UNIDADE_SOLICITANTE)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{TOTAL_VAGAS_MES}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="35" splitType="Stretch">
			<printWhenExpression><![CDATA[$P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.dto.RelatorioAcompanhamentoSolicitacoesDTOParam.FormaApresentacao.GERAL)]]></printWhenExpression>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="0" y="0" width="120" height="34"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Bottom">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_grupo_procedimento")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="34" width="782" height="1"/>
			</line>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="630" y="0" width="55" height="34"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_dias_media_espera_3_meses")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="444" y="0" width="30" height="34"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_fila")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="122" y="0" width="320" height="34"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Bottom">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_tipo_procedimento_abv")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="475" y="0" width="45" height="34"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_regulacao")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="524" y="0" width="46" height="34"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_devolucao_regulacao")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="573" y="0" width="55" height="34"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_total_aguardando")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="686" y="0" width="46" height="34">
					<printWhenExpression><![CDATA[!$P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.dto.RelatorioAcompanhamentoSolicitacoesDTOParam.FormaApresentacao.UNIDADE_SOLICITANTE)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_vagas_no_mes")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="733" y="0" width="46" height="34">
					<printWhenExpression><![CDATA[!$P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.dto.RelatorioAcompanhamentoSolicitacoesDTOParam.FormaApresentacao.UNIDADE_SOLICITANTE)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_tempo_previsto_dias")]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="12" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="0" y="0" width="120" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{tipoProcedimentoClassificacao}.getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="630" y="0" width="55" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{diasMediaEspera}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="122" y="0" width="320" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{tipoProcedimento}.getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="444" y="0" width="30" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{totalFila}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="475" y="0" width="45" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{totalRegulacao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="524" y="0" width="46" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{devolvidoRegulacao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="573" y="0" width="55" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{totalFila}+$F{totalRegulacao}+$F{devolvidoRegulacao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="686" y="0" width="46" height="12">
					<printWhenExpression><![CDATA[!$P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.dto.RelatorioAcompanhamentoSolicitacoesDTOParam.FormaApresentacao.UNIDADE_SOLICITANTE)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{vagasMes}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="733" y="0" width="46" height="12">
					<printWhenExpression><![CDATA[!$P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.dto.RelatorioAcompanhamentoSolicitacoesDTOParam.FormaApresentacao.UNIDADE_SOLICITANTE)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{tempoPrevisto}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
