/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.agendamento.exame;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioImpressaoComprovanteAgendamentoExameForaRedeDTOParam;
import br.com.ksisolucoes.report.agendamento.exame.query.QueryRelatorioImprimirComprovanteAgendamentoExameForaRede;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.basico.Parametro;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoComprovanteAgendamentoExameForaRede extends AbstractReport<RelatorioImpressaoComprovanteAgendamentoExameForaRedeDTOParam>{

    public RelatorioImpressaoComprovanteAgendamentoExameForaRede(RelatorioImpressaoComprovanteAgendamentoExameForaRedeDTOParam param){
        super(param);
    }

    @Override
    public ITransferDataReport getQuery(){
        try {
            Parametro parametro = CargaBasicoPadrao.getInstance().getParametroPadrao();

            addParametro("caminhoLogo", br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp.getInstance().getReportSessaoAplicacao().getCaminhoImagemPadrao());

            addParametro("observacaoGeral", BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("observacaoGeral"));
            addParametro("observacaoAutorizado", BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("observacaoAutorizado"));
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        
        return new QueryRelatorioImprimirComprovanteAgendamentoExameForaRede();
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/agendamento/exame/jrxml/relatorio_impressao_comprovante_agendamento_exame_fora_rede.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_comprovante_de_agendamento");
    }

}
