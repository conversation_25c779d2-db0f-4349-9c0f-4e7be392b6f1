package br.com.ksisolucoes.report.agendamento;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.agendamento.dto.RelacaoPacientesNaoCompareceramDTOParam;
import br.com.ksisolucoes.report.agendamento.query.QueryRelacaoPacientesNaoCompareceram;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelacaoPacientesNaoCompareceram extends AbstractReport<RelacaoPacientesNaoCompareceramDTOParam> {

    public RelacaoPacientesNaoCompareceram(RelacaoPacientesNaoCompareceramDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/agendamento/jrxml/relacao_pacientes_nao_compareceram.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_pacientes_nao_compareceram");
    }

    @Override
    public ITransferDataReport getQuery() {
        addParametro("formaApresentacao", getParam().getFormaApresentacao());
        return new QueryRelacaoPacientesNaoCompareceram();
    }

}
