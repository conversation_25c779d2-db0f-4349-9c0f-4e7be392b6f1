package br.com.ksisolucoes.report.prontuario.basico.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioPerfilAtendimentoHospitalDTO;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioPerfilAtendimentoHospitalDTOParam;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;
import br.com.ksisolucoes.vo.prontuario.hospital.QuartoInternacao;
import java.util.List;
import org.hibernate.Hibernate;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.hibernate.type.DoubleType;

public class QueryTaxaOcupacaoHospitalar extends CommandQuery {

    private RelatorioPerfilAtendimentoHospitalDTOParam param;
    private List<RelatorioPerfilAtendimentoHospitalDTO> result;

    public QueryTaxaOcupacaoHospitalar(RelatorioPerfilAtendimentoHospitalDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
                     
        hql.setUseSQL(true);
        
        hql.addToSelect("cast(count(ai.cd_atend_inf) as numeric) / (select count(*) from leito_quarto lq left join quarto_internacao qi on(lq.cd_quarto_internacao = qi.cd_quarto_internacao) where qi.tp_quarto = :internacao and (lq.dt_exclusao > ts.s or lq.dt_exclusao is null) and lq.dt_cadastro <= ts.s) * 100","quantidadeDouble");
        hql.addToFrom("(select s from generate_series(cast(:dataInicial as timestamp),cast(:dataFinal as timestamp),'1 day') AS s) ts"
                + " left join atendimento_informacao ai on (ai.data_chegada between ts.s and ts.s + interval '1 day' or"
                + " data_saida between ts.s and ts.s + interval '1 day' or "
                + " (ai.data_chegada <= ts.s and coalesce(data_saida, ts.s + interval '1 day') >= ts.s + interval '1 day '))"
                + " and ai.status_atendimento <> :status"
                + " and ai.cd_leito is not null"
                + " group by ts.s"
                + " order by ts.s");
            
        hql.setTypeSelect(RelatorioPerfilAtendimentoHospitalDTO.class.getName());
//select
//ts.s,
//cast
//(
//   count(*) as numeric
//)
/// (select count(*) from leito_quarto) * 100 as quantidadeDouble
//from
//(
//   select
//   s
//   from generate_series(cast({d '2013-06-01'} as timestamp),cast({ts '2013-06-30 23:59:59'} as timestamp),'1 day') AS s
//)
//ts
//left join atendimento_informacao ai on (ai.data_chegada between ts.s
//and ts.s + interval '1 day' or data_saida between ts.s
//and ts.s + interval '1 day' or
//(
//   ai.data_chegada <= ts.s
//   and coalesce(data_saida, ts.s + interval '1 day') >= ts.s + interval '1 day '
//))
//and ai.status_atendimento <> 2
//and ai.cd_leito is not null
//group by ts.s
//order by ts.s;
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setParameter("dataInicial", Data.adjustRangeHour(param.getPeriodo()).getDataInicial());
        query.setParameter("dataFinal", Data.adjustRangeHour(param.getPeriodo()).getDataFinal());
        query.setParameter("status", AtendimentoInformacao.StatusAtendimento.CANCELADO.value());
        query.setParameter("internacao", QuartoInternacao.TipoQuarto.INTERNACAO.value());
    }
    
    @Override
    protected void customQuery(Query query) {
        SQLQuery sql = (SQLQuery) query;
        sql.addScalar("quantidadeDouble", DoubleType.INSTANCE);
        sql.setResultTransformer(Transformers.aliasToBean(RelatorioPerfilAtendimentoHospitalDTO.class));
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = (List<RelatorioPerfilAtendimentoHospitalDTO>) result;
    }

    @Override
    public List<RelatorioPerfilAtendimentoHospitalDTO> getResult() {
        return result;
    }
}