/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.prontuario.basico.query;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QuerySorter;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomSorter;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.PendenciasPacienteDTO;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.agendamento.TipoAtendimentoAgenda;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOdontologico;
import br.com.ksisolucoes.vo.prontuario.basico.EncaminhamentoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcura;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoEncaminhamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimentoClassificacao;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaPendenciasPaciente extends CommandQuery{
    private UsuarioCadsus usuarioCadsus;
    private List<PendenciasPacienteDTO> pendenciasList = new ArrayList<PendenciasPacienteDTO>();

    public QueryConsultaPendenciasPaciente(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {

        carregarAtendimentos();
        carregarAtendimentosOdontologicos();
        carregarExames();
        carregarEncaminhamentos();
        carregarAgendamentos();
//        carregarPequenasCirurgias();

        CollectionUtils.BeanComparator beanComparator = new CollectionUtils.BeanComparator(PendenciasPacienteDTO.PROP_DATA);
        beanComparator.setTipoOrdenacao(CollectionUtils.TipoOrdenacao.DESC);

        Collections.sort(pendenciasList, beanComparator);
    }

    private void carregarAtendimentos() throws DAOException, ValidacaoException{
        List<Atendimento> clinica = LoadManager.getInstance(Atendimento.class)
                .addProperties(new HQLProperties(Atendimento.class).getIdProperties())
                .addProperty(VOUtils.montarPath(Atendimento.PROP_EMPRESA,Empresa.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(Atendimento.PROP_EMPRESA,Empresa.PROP_REFERENCIA))
                .addProperty(VOUtils.montarPath(Atendimento.PROP_EMPRESA,Empresa.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(Atendimento.PROP_DATA_CHEGADA))
                .addProperty(VOUtils.montarPath(Atendimento.PROP_STATUS))
                .addProperty(VOUtils.montarPath(Atendimento.PROP_PROFISSIONAL,Profissional.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(Atendimento.PROP_PROFISSIONAL,Profissional.PROP_REFERENCIA))
                .addProperty(VOUtils.montarPath(Atendimento.PROP_PROFISSIONAL,Profissional.PROP_NOME))
                .addProperty(VOUtils.montarPath(Atendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO,NaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA,NaturezaProcura.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(Atendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO,NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO,TipoAtendimento.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(Atendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO,NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO,TipoAtendimento.PROP_TIPO_ATENDIMENTO))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(Atendimento.PROP_USUARIO_CADSUS), usuarioCadsus))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(Atendimento.PROP_STATUS), QueryParameter.MENOR,Atendimento.STATUS_FINALIZADO))
                .start().getList();

        if(!clinica.isEmpty()){
            PendenciasPacienteDTO dto = new PendenciasPacienteDTO();
            dto.setUnidade(clinica.get(0).getEmpresa());
            if(clinica.get(0).getProfissional() != null){
                dto.setNomeProfissional(clinica.get(0).getProfissional().getNome());
            }
            dto.setData(clinica.get(0).getDataChegada());
            dto.setDescricao(clinica.get(0).getNaturezaProcuraTipoAtendimento().getNaturezaProcura().getDescricao()+" / "+clinica.get(0).getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricao());
            dto.setSituacao(clinica.get(0).getDescricaoStatus());
            dto.setTipoAtendimento(clinica.get(0).getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getTipoAtendimento());
            dto.setCodigoManager(clinica.get(0).getCodigoManager());

            pendenciasList.add(dto);
        }
    }

    private void carregarAtendimentosOdontologicos() throws DAOException, ValidacaoException{
        List<AtendimentoOdontologico> atendimentos = LoadManager.getInstance(AtendimentoOdontologico.class)
                .addProperties(new HQLProperties(AtendimentoOdontologico.class).getIdProperties())
                .addProperty(VOUtils.montarPath(AtendimentoOdontologico.PROP_ATENDIMENTO,Atendimento.PROP_EMPRESA,Empresa.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(AtendimentoOdontologico.PROP_ATENDIMENTO,Atendimento.PROP_EMPRESA,Empresa.PROP_REFERENCIA))
                .addProperty(VOUtils.montarPath(AtendimentoOdontologico.PROP_ATENDIMENTO,Atendimento.PROP_EMPRESA,Empresa.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(AtendimentoOdontologico.PROP_ATENDIMENTO,Atendimento.PROP_DATA_ATENDIMENTO))
                .addProperty(VOUtils.montarPath(AtendimentoOdontologico.PROP_ATENDIMENTO,Atendimento.PROP_STATUS))
                .addProperty(VOUtils.montarPath(AtendimentoOdontologico.PROP_ATENDIMENTO,Atendimento.PROP_PROFISSIONAL,Profissional.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(AtendimentoOdontologico.PROP_ATENDIMENTO,Atendimento.PROP_PROFISSIONAL,Profissional.PROP_REFERENCIA))
                .addProperty(VOUtils.montarPath(AtendimentoOdontologico.PROP_ATENDIMENTO,Atendimento.PROP_PROFISSIONAL,Profissional.PROP_NOME))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(AtendimentoOdontologico.PROP_ATENDIMENTO,Atendimento.PROP_USUARIO_CADSUS), usuarioCadsus))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(AtendimentoOdontologico.PROP_ATENDIMENTO,Atendimento.PROP_STATUS), Atendimento.STATUS_FINALIZADO))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(AtendimentoOdontologico.PROP_ATENDIMENTO,Atendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO,NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO,TipoAtendimento.PROP_TIPO_ATENDIMENTO), TipoAtendimento.TiposAtendimento.ODONTOLOGIA.value()))
                .start().getList();

        if(!atendimentos.isEmpty()){
            PendenciasPacienteDTO dto = new PendenciasPacienteDTO();
            dto.setUnidade(atendimentos.get(0).getAtendimento().getEmpresa());
            if(atendimentos.get(0).getAtendimento().getProfissional()!= null){
                dto.setNomeProfissional(atendimentos.get(0).getAtendimento().getProfissional().getNome());
            }
            dto.setData(atendimentos.get(0).getAtendimento().getDataAtendimento());
            dto.setDescricao(Bundle.getStringApplication("rotulo_tratamento_odontologico"));
            dto.setSituacao(Bundle.getStringApplication("rotulo_tratamento_inacabado"));
            dto.setTipo(PendenciasPacienteDTO.TIPO_ODONTOLOGIA);
            dto.setCodigoManager(atendimentos.get(0).getCodigoManager());

            pendenciasList.add(dto);
        }
    }

    private void carregarExames() throws DAOException, ValidacaoException{
        List<Exame> exames = LoadManager.getInstance(Exame.class)
                .addProperties(new HQLProperties(Exame.class).getIdProperties())
                .addProperty(VOUtils.montarPath(Exame.PROP_EMPRESA_SOLICITANTE,Empresa.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(Exame.PROP_EMPRESA_SOLICITANTE,Empresa.PROP_REFERENCIA))
                .addProperty(VOUtils.montarPath(Exame.PROP_EMPRESA_SOLICITANTE,Empresa.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(Exame.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(Exame.PROP_PROFISSIONAL, Profissional.PROP_REFERENCIA))
                .addProperty(VOUtils.montarPath(Exame.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                .addProperty(VOUtils.montarPath(Exame.PROP_DATA_SOLICITACAO))
                .addProperty(VOUtils.montarPath(Exame.PROP_STATUS))
                .addProperty(VOUtils.montarPath(Exame.PROP_TIPO_EXAME, TipoExame.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(Exame.PROP_TIPO_EXAME, TipoExame.PROP_DESCRICAO))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(Exame.PROP_USUARIO_CADSUS,UsuarioCadsus.PROP_CODIGO), usuarioCadsus.getCodigo()))
                .addInterceptor(new LoadInterceptorPendenciasExame())
                .addSorter(new QueryCustomSorter(VOUtils.montarPath(Exame.PROP_DATA_SOLICITACAO),QuerySorter.DECRESCENTE))
                .start().getList();

        for (Exame exame : exames) {
            PendenciasPacienteDTO dto = new PendenciasPacienteDTO();
            dto.setUnidade(exame.getEmpresaSolicitante());
            dto.setNomeProfissional(exame.getProfissional().getNome());
            dto.setData(exame.getDataSolicitacao());
            dto.setDescricao(Bundle.getStringApplication("rotulo_exame")+"/"+exame.getTipoExame().getDescricao());
            dto.setSituacao(exame.getDescricaoStatus());
            dto.setTipo(PendenciasPacienteDTO.TIPO_EXAME);
            dto.setCodigoManager(exame.getCodigoManager());

            pendenciasList.add(dto);
        }
    }

    private void carregarEncaminhamentos() throws DAOException, ValidacaoException{
        List<Encaminhamento> agendas = LoadManager.getInstance(Encaminhamento.class)
                .addProperties(new HQLProperties(Encaminhamento.class).getIdProperties())
                .addProperties(VOUtils.montarPath(Encaminhamento.PROP_UNIDADE_ENCAMINHAMENTO, Empresa.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(Encaminhamento.PROP_UNIDADE_ENCAMINHAMENTO, Empresa.PROP_REFERENCIA))
                .addProperties(VOUtils.montarPath(Encaminhamento.PROP_UNIDADE_ENCAMINHAMENTO, Empresa.PROP_DESCRICAO))
                .addProperties(VOUtils.montarPath(Encaminhamento.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(Encaminhamento.PROP_PROFISSIONAL, Profissional.PROP_REFERENCIA))
                .addProperties(VOUtils.montarPath(Encaminhamento.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                .addProperties(VOUtils.montarPath(Encaminhamento.PROP_DATA_CADASTRO))
                .addProperties(VOUtils.montarPath(Encaminhamento.PROP_ENCAMINHAMENTO_AGENDAMENTO, EncaminhamentoAgendamento.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(Encaminhamento.PROP_ENCAMINHAMENTO_AGENDAMENTO, EncaminhamentoAgendamento.PROP_DATA_AGENDAMENTO))
                .addProperties(VOUtils.montarPath(Encaminhamento.PROP_STATUS))
                .addProperties(VOUtils.montarPath(Encaminhamento.PROP_TIPO_ORIGEM))
                .addProperties(VOUtils.montarPath(Encaminhamento.PROP_TIPO))
                .addProperties(VOUtils.montarPath(Encaminhamento.PROP_TIPO_ENCAMINHAMENTO, TipoEncaminhamento.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(Encaminhamento.PROP_TIPO_ENCAMINHAMENTO, TipoEncaminhamento.PROP_DESCRICAO))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(Encaminhamento.PROP_USUARIO_CADSUS,UsuarioCadsus.PROP_CODIGO), usuarioCadsus.getCodigo()))
                .addParameter(new QueryCustomParameter(Encaminhamento.PROP_STATUS, QueryParameter.IN, Arrays.asList(Encaminhamento.STATUS_PENDENTE, Encaminhamento.STATUS_AUTORIZADO, Encaminhamento.STATUS_CONCLUIDO_COM_RETORNO)))
                .addSorter(new QueryCustomSorter(VOUtils.montarPath(Encaminhamento.PROP_DATA_CADASTRO),QuerySorter.DECRESCENTE))
                .start().getList();

        for (Encaminhamento encaminhamento : agendas) {
            PendenciasPacienteDTO dto = new PendenciasPacienteDTO();
            dto.setUnidade(encaminhamento.getUnidadeEncaminhamento());
            dto.setNomeProfissional(encaminhamento.getProfissional().getNome());
            dto.setData(encaminhamento.getDataCadastro());
            dto.setDataAgendamento(encaminhamento.getEncaminhamentoAgendamento() != null ? encaminhamento.getEncaminhamentoAgendamento().getDataAgendamento() : null);
            dto.setDescricao(Bundle.getStringApplication("rotulo_encaminhamento")+"/"+encaminhamento.getTipoEncaminhamento().getDescricao());
            dto.setSituacao(encaminhamento.getDescricaoStatus());
            dto.setStatus(encaminhamento.getStatus());
            dto.setTipoOrigem(encaminhamento.getTipoOrigem());
            dto.setEncaminhamentoTipo(encaminhamento.getTipo());
            dto.setTipo(PendenciasPacienteDTO.TIPO_AGENDA_ESPECIALIZADA);
            dto.setCodigoManager(encaminhamento.getCodigoManager());

            pendenciasList.add(dto);
        }
    }

    private void carregarAgendamentos() throws DAOException, ValidacaoException{
        List<AgendaGradeAtendimentoHorario> horariosList = LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
                .addProperties(new HQLProperties(AgendaGradeAtendimentoHorario.class).getProperties())
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO,AgendaGradeAtendimento.PROP_TIPO_ATENDIMENTO_AGENDA,TipoAtendimentoAgenda.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_LOCAL_AGENDAMENTO,Empresa.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_LOCAL_AGENDAMENTO,Empresa.PROP_REFERENCIA))
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_LOCAL_AGENDAMENTO,Empresa.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_TIPO_PROCEDIMENTO_CLASSIFICACAO,TipoProcedimentoClassificacao.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_PROFISSIONAL, Profissional.PROP_REFERENCIA))
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                .addParameter(new QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_STATUS, AgendaGradeAtendimentoHorario.STATUS_AGENDADO))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_USUARIO_CADSUS), usuarioCadsus))
                .start().getList();

        for (AgendaGradeAtendimentoHorario agah : horariosList) {
            PendenciasPacienteDTO dto = new PendenciasPacienteDTO();
            dto.setUnidade(agah.getLocalAgendamento());
            if(agah.getProfissional() != null){
                dto.setNomeProfissional(agah.getProfissional().getNome());
            }
            dto.setCodigoManager(agah.getCodigoManager());
            dto.setData(agah.getDataAgendamento());
            dto.setTipo(PendenciasPacienteDTO.TIPO_AGENDAMENTO);
            dto.setSituacao(Bundle.getStringApplication("rotulo_agendado"));
            dto.setDescricao(agah.getTipoProcedimento().getTipoProcedimentoClassificacao().getDescricao()+"/"
                    +agah.getTipoProcedimento().getDescricao());

            pendenciasList.add(dto);
        }
    }

//    private void carregarPequenasCirurgias() throws DAOException, ValidacaoException{
//        List<PequenaCirurgia> pequenasCirurgias = LoadManager.getInstance(PequenaCirurgia.class)
//                .addProperty(VOUtils.montarPath(PequenaCirurgia.PROP_CODIGO))
//                .addProperty(VOUtils.montarPath(PequenaCirurgia.PROP_ATENDIMENTO, Atendimento.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
//                .addProperty(VOUtils.montarPath(PequenaCirurgia.PROP_ATENDIMENTO, Atendimento.PROP_PROFISSIONAL, Profissional.PROP_NOME))
//                .addProperty(VOUtils.montarPath(PequenaCirurgia.PROP_ATENDIMENTO, Atendimento.PROP_EMPRESA, Empresa.PROP_CODIGO))
//                .addProperty(VOUtils.montarPath(PequenaCirurgia.PROP_ATENDIMENTO, Atendimento.PROP_EMPRESA, Empresa.PROP_DESCRICAO))
//                .addProperty(VOUtils.montarPath(PequenaCirurgia.PROP_ATENDIMENTO, Atendimento.PROP_DATA_ATENDIMENTO))
//                .addParameter(new QueryCustomParameter(PequenaCirurgia.PROP_DATA_AGENDA, QueryParameter.IS_NULL))
//                .addParameter(new QueryCustomParameter(VOUtils.montarPath(PequenaCirurgia.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS), usuarioCadsus))
//                .start().getList();
//
//        if (CollectionUtils.isNotNullEmpty(pequenasCirurgias)) {
//            for (PequenaCirurgia pequenaCirurgia : pequenasCirurgias) {
//                PendenciasPacienteDTO dto = new PendenciasPacienteDTO();
//                dto.setUnidade(pequenaCirurgia.getAtendimento().getRoEmpresa());
//                dto.setNomeProfissional(pequenaCirurgia.getAtendimento().getProfissional().getNome());
//                dto.setCodigoManager(pequenaCirurgia.getCodigoManager());
//                dto.setData(pequenaCirurgia.getAtendimento().getDataAtendimento());
//                dto.setTipo(PendenciasPacienteDTO.TIPO_PEQUENA_CIRURGIA);
//                dto.setSituacao(Bundle.getStringApplication("rotulo_aguardando_agendamento"));
//                dto.setDescricao(Bundle.getStringApplication("rotulo_pequena_cirurgia"));
//
//                pendenciasList.add(dto);
//            }
//        }
//    }

    public List<PendenciasPacienteDTO> getPendenciasList() {
        return pendenciasList;
    }
}
