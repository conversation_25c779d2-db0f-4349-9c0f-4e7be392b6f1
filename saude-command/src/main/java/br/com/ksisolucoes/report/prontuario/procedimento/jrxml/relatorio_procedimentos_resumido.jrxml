<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_procedimentos_resumido" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="695487af-2d27-41ec-b51d-120a320256a2">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="48"/>
	<import value="br.com.ksisolucoes.util.basico.CargaBasicoPadrao"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<import value="br.com.ksisolucoes.vo.basico.Empresa"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="FORMA_APRESENTACAO" class="java.lang.String"/>
	<parameter name="AGRUPAR_UNIDADE" class="java.lang.String"/>
	<parameter name="VISUALIZAR_VALOR" class="java.lang.Long"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<field name="quantidade" class="java.lang.Double"/>
	<field name="procedimento" class="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"/>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="tabelaCbo" class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"/>
	<field name="convenio" class="br.com.ksisolucoes.vo.prontuario.basico.Convenio"/>
	<field name="procedimentoSubGrupo" class="br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoSubGrupo"/>
	<field name="cidade" class="br.com.ksisolucoes.vo.basico.Cidade"/>
	<field name="equipeProfissional" class="br.com.ksisolucoes.vo.basico.EquipeProfissional"/>
	<field name="valorProcedimento" class="java.lang.Double"/>
	<field name="empresaSolicitante" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="valorDiferenciado" class="java.lang.Double"/>
	<field name="valorTotalProcedimento" class="java.lang.Double"/>
	<field name="valorTotalDiferenciado" class="java.lang.Double"/>
	<field name="valorTotalItens" class="java.lang.Double"/>
	<variable name="totalQuantidadeFA" class="java.lang.Double" resetType="Group" resetGroup="fa" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<variable name="totalQuantidade" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<variable name="totalGrupo" class="java.lang.Double" resetType="Group" resetGroup="totalGrupoSubGrupo" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<variable name="totalPrecoPr" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}.doubleValue()*$F{valorProcedimento}.doubleValue()]]></variableExpression>
	</variable>
	<variable name="totalPrecoPrUnitario" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{valorProcedimento}]]></variableExpression>
	</variable>
	<variable name="totalGrupoPrecoPrUnit" class="java.lang.Double" resetType="Group" resetGroup="totalGrupoSubGrupo" calculation="Sum">
		<variableExpression><![CDATA[$F{valorProcedimento}]]></variableExpression>
	</variable>
	<variable name="totalGrupoPrecoPrTotal" class="java.lang.Double" resetType="Group" resetGroup="totalGrupoSubGrupo" calculation="Sum">
		<variableExpression><![CDATA[$F{valorProcedimento}.doubleValue()*$F{quantidade}.doubleValue()]]></variableExpression>
	</variable>
	<variable name="totalUnidadeFA" class="java.lang.Double" resetType="Group" resetGroup="fa" calculation="Sum">
		<variableExpression><![CDATA[$F{valorProcedimento}]]></variableExpression>
	</variable>
	<variable name="totalValorDiferenciado" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{valorDiferenciado}]]></variableExpression>
	</variable>
	<variable name="totalValorTotal" class="java.lang.Double" resetType="Group" resetGroup="totalGrupoSubGrupo" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotalItens}]]></variableExpression>
	</variable>
	<variable name="valorTotalItens" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotalItens}]]></variableExpression>
	</variable>
	<variable name="valorTotalFa" class="java.lang.Double" resetType="Group" resetGroup="fa" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotalItens}]]></variableExpression>
	</variable>
	<group name="padrao">
		<groupExpression><![CDATA[$F{procedimentoSubGrupo}.getRoGrupo().getDescricao()]]></groupExpression>
		<groupFooter>
			<band height="24">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($P{VISUALIZAR_VALOR}) && !$P{FORMA_APRESENTACAO}.equals("Geral")]]></printWhenExpression>
				<textField pattern="###0.00">
					<reportElement x="228" y="1" width="74" height="11" uuid="2965faef-6275-459b-88ee-7a3f18aad4cb"/>
					<box rightPadding="1"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalQuantidade}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="187" y="0" width="368" height="1" uuid="3220866d-7a1a-42d4-9e66-4ed9b4ea2fef"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField>
					<reportElement x="128" y="1" width="90" height="11" uuid="30a0ae34-94cf-4a72-a760-67ba48bafc70"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_geral")]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00">
					<reportElement x="460" y="1" width="74" height="11" uuid="d021f7cd-4ace-428c-9276-5c602ad11024"/>
					<box rightPadding="1"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[( $V{valorTotalItens} != null ? $V{valorTotalItens} : "0,00" )]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="Empresa">
		<groupExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{AGRUPAR_UNIDADE})
    ?
        null
    :
        $F{empresa}]]></groupExpression>
		<groupHeader>
			<band height="16">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{AGRUPAR_UNIDADE})]]></printWhenExpression>
				<rectangle radius="10">
					<reportElement mode="Transparent" x="0" y="1" width="555" height="14" uuid="a1f483f5-a57a-4602-bf27-59c3a3d0a547"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField>
					<reportElement x="0" y="1" width="555" height="13" uuid="2078e9f3-3872-45a4-8e31-da8d41c18a59"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" isBold="true" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{AGRUPAR_UNIDADE})
    ?
        Bundle.getStringApplication("rotulo_unidade") +": "+ Bundle.getStringApplication("rotulo_todas")
    :
        Bundle.getStringApplication("rotulo_unidade") +": "+ $F{empresa}.getDescricaoFormatado() + " - Cnes: " + Coalesce.asString($F{empresa}.getCnes(),"")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="totalGrupoSubGrupo">
		<groupExpression><![CDATA[$F{procedimentoSubGrupo}.getRoGrupo().getDescricao()]]></groupExpression>
		<groupFooter>
			<band height="12">
				<printWhenExpression><![CDATA[Bundle.getStringApplication("rotulo_grupo_subgrupo2").equals($P{FORMA_APRESENTACAO})&&RepositoryComponentDefault.SIM_LONG.equals($P{VISUALIZAR_VALOR})]]></printWhenExpression>
				<textField pattern="###0.00">
					<reportElement x="228" y="1" width="74" height="11" uuid="432aa63f-269d-4362-9338-c72b502607a4"/>
					<box rightPadding="1"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalGrupo}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="18" y="1" width="199" height="11" uuid="444433a4-5ef4-4c89-9948-731884df8905"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_grupo")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="279" y="0" width="276" height="1" uuid="817c4ad3-41ba-4c4b-8788-2fef8b67e4f3"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="###0.00">
					<reportElement x="444" y="1" width="90" height="11" uuid="17564ece-bb06-458f-b7ad-d615ff675bd0"/>
					<box rightPadding="1"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[( $V{totalValorTotal} != null ? $V{totalValorTotal} : "0,00" )]]></textFieldExpression>
				</textField>
			</band>
			<band height="12">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO_LONG.equals($P{VISUALIZAR_VALOR}) && !$P{FORMA_APRESENTACAO}.equals("Geral")]]></printWhenExpression>
				<line>
					<reportElement x="187" y="0" width="368" height="1" uuid="7e00e7c3-ec66-4a60-99cb-32fb6ac57ce5"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="###0.00">
					<reportElement x="464" y="1" width="91" height="11" uuid="30ed7fd0-f694-4d40-bc55-ce95be6f38f9"/>
					<box rightPadding="1"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalQuantidade}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="371" y="1" width="90" height="11" uuid="229d7c0b-1f51-452b-b052-14c94fa64460"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_geral")]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="fa" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[Bundle.getStringApplication("rotulo_geral").equals($P{FORMA_APRESENTACAO})
?
    null
:
    (Bundle.getStringApplication("rotulo_profissional").equals($P{FORMA_APRESENTACAO})
    ?
        $F{profissional}
    :
        (Bundle.getStringApplication("rotulo_usuario_cadsus").equals($P{FORMA_APRESENTACAO})
        ?
            $F{usuarioCadsus}
        :
            (Bundle.getStringApplication("rotulo_cbo").equals($P{FORMA_APRESENTACAO})
            ?
                $F{tabelaCbo}.getCbo() != null
                ?
                    $F{tabelaCbo}
                :
                    null
            :
                (Bundle.getStringApplication("rotulo_convenio").equals($P{FORMA_APRESENTACAO})
                ?
                    $F{convenio}
                :
                    (Bundle.getStringApplication("rotulo_municipio").equals($P{FORMA_APRESENTACAO})
                    ?
                        $F{cidade}.getCodigo()
                    :
                        (Bundle.getStringApplication("rotulo_grupo_subgrupo2").equals($P{FORMA_APRESENTACAO})
                        ?
                            $F{procedimentoSubGrupo}.getDescricaoFormatado()
                        :
                            (Bundle.getStringApplication("rotulo_equipe").equals($P{FORMA_APRESENTACAO})
                            ?
                                $F{equipeProfissional}.getEquipe().getReferencia()
                            :
                            (Bundle.getStringApplication("rotulo_unidade_origem").equals($P{FORMA_APRESENTACAO})
                            ?
                                $F{empresaSolicitante}.getDescricao()
                            :
                        null ) ) ) ) ) )))]]></groupExpression>
		<groupHeader>
			<band height="24">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO_LONG.equals($P{VISUALIZAR_VALOR})]]></printWhenExpression>
				<textField>
					<reportElement x="0" y="0" width="555" height="13" uuid="1139007c-d976-4cc1-8888-764a07a27a11"/>
					<textElement>
						<font fontName="Arial" isBold="true" isItalic="true" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_geral").equals($P{FORMA_APRESENTACAO}) ? ""
            :
            (Bundle.getStringApplication("rotulo_profissional").equals($P{FORMA_APRESENTACAO})
                ?
                    Bundle.getStringApplication("rotulo_profissional") + ": " + $F{profissional}.getDescricaoFormatado() + " : " + Bundle.getStringApplication("rotulo_ine") + " : " + ($F{equipeProfissional}.getEquipe().getEquipeCnes()!=null ? $F{equipeProfissional}.getEquipe().getEquipeCnes(): "Profissional sem equipe")
                :
                (Bundle.getStringApplication("rotulo_unidade_origem").equals($P{FORMA_APRESENTACAO})
                ?
                    Bundle.getStringApplication("rotulo_unidade_origem") + ": " + $F{empresaSolicitante}.getDescricao()
                :
                (Bundle.getStringApplication("rotulo_usuario_cadsus").equals($P{FORMA_APRESENTACAO})
                    ?
                        Bundle.getStringApplication("rotulo_usuario_cadsus") + ": " + $F{usuarioCadsus}.getDescricaoFormatado()
                    :
                    (Bundle.getStringApplication("rotulo_cbo").equals($P{FORMA_APRESENTACAO})
                        ?
                            $F{tabelaCbo}.getCbo()!=null
                                ?
                                    Bundle.getStringApplication("rotulo_cbo")+": "+$F{tabelaCbo}.getDescricaoFormatado()
                                :
                                    Bundle.getStringApplication("rotulo_cbo")+": "+$F{tabelaCbo}.getDescricao()
                        :
                        (Bundle.getStringApplication("rotulo_convenio").equals($P{FORMA_APRESENTACAO})
                            ?
                                Bundle.getStringApplication("rotulo_convenio")+": "+($F{convenio}.getDescricaoFormatado()!=null?$F{convenio}.getDescricaoFormatado():"Sem convênio")
                            :
                            (Bundle.getStringApplication("rotulo_municipio").equals($P{FORMA_APRESENTACAO})
                                ?
                                    Bundle.getStringApplication("rotulo_municipio")+": "+($F{cidade}.getDescricao())
                                :

                                (Bundle.getStringApplication("rotulo_equipe").equals($P{FORMA_APRESENTACAO})
                                ?
                                    $F{equipeProfissional} != null && $F{equipeProfissional}.getEquipe() != null && $F{equipeProfissional}.getEquipe().getReferencia() != null ?
                                        Bundle.getStringApplication("rotulo_equipe")+": "+($F{equipeProfissional}.getEquipe().getReferencia())+ " : " + Bundle.getStringApplication("rotulo_ine") + " : " +($F{equipeProfissional}.getEquipe().getEquipeCnes() != null ? $F{equipeProfissional}.getEquipe().getEquipeCnes() : " Não informado")
                                    : "Não informado"
                                :


                                (Bundle.getStringApplication("rotulo_grupo_subgrupo2").equals($P{FORMA_APRESENTACAO})
                                    ?
                                        Bundle.getStringApplication("rotulo_grupo")+": "+($F{procedimentoSubGrupo}.getRoGrupo().getDescricao()!=null?$F{procedimentoSubGrupo}.getRoGrupo().getDescricao():"Grupo não definido")
                                            +" / "+Bundle.getStringApplication("rotulo_sub_grupo")+": "+($F{procedimentoSubGrupo}.getDescricaoFormatado()!=null?$F{procedimentoSubGrupo}.getDescricaoFormatado():"SubGrupo não definido")
                                    :
                                    ""
                                    )
                                )
                            )
                        )
                    )
                )
            )
        )]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="13" width="461" height="11" uuid="56c39162-4e35-4f50-88d4-d3933f1cf120"/>
					<box leftPadding="1"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_procedimento")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="465" y="13" width="90" height="11" uuid="1ac07e0d-e259-4880-a71d-5a0694bd6653"/>
					<box rightPadding="1"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quantidade")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="23" width="555" height="1" uuid="488afd62-69c2-41cc-8335-c9e6296a04b5"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
			</band>
			<band height="24">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($P{VISUALIZAR_VALOR})]]></printWhenExpression>
				<textField>
					<reportElement x="0" y="0" width="555" height="13" uuid="b9fd2140-fa9e-4bba-a922-7c8dd8b8836a"/>
					<textElement>
						<font fontName="Arial" isBold="true" isItalic="true" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_geral").equals($P{FORMA_APRESENTACAO}) ? ""
            :
            (Bundle.getStringApplication("rotulo_profissional").equals($P{FORMA_APRESENTACAO})
                ?
                    Bundle.getStringApplication("rotulo_profissional") + ": " + $F{profissional}.getDescricaoFormatado() + ($F{profissional}.getCodigoCns() != null ? " - Cns: " + $F{profissional}.getCodigoCns() : "")
                :
                (Bundle.getStringApplication("rotulo_usuario_cadsus").equals($P{FORMA_APRESENTACAO})
                    ?
                        Bundle.getStringApplication("rotulo_usuario_cadsus") + ": " + $F{usuarioCadsus}.getDescricaoFormatado()
                    :
                    (Bundle.getStringApplication("rotulo_unidade_origem").equals($P{FORMA_APRESENTACAO})
                    ?
                        Bundle.getStringApplication("rotulo_unidade_origem") + ": " + $F{empresaSolicitante}.getDescricao()
                    :
                    (Bundle.getStringApplication("rotulo_cbo").equals($P{FORMA_APRESENTACAO})
                        ?
                            $F{tabelaCbo}.getCbo()!=null
                                ?
                                    Bundle.getStringApplication("rotulo_cbo")+": "+$F{tabelaCbo}.getDescricaoFormatado()
                                :
                                    Bundle.getStringApplication("rotulo_cbo")+": "+$F{tabelaCbo}.getDescricao()
                        :
                        (Bundle.getStringApplication("rotulo_convenio").equals($P{FORMA_APRESENTACAO})
                            ?
                                Bundle.getStringApplication("rotulo_convenio")+": "+($F{convenio}.getDescricaoFormatado()!=null?$F{convenio}.getDescricaoFormatado():"Sem convênio")
                            :
                            (Bundle.getStringApplication("rotulo_municipio").equals($P{FORMA_APRESENTACAO})
                                ?
                                    Bundle.getStringApplication("rotulo_municipio")+": "+($F{cidade}.getDescricao())
                                :

                                (Bundle.getStringApplication("rotulo_equipe").equals($P{FORMA_APRESENTACAO})
                                ?
                                    $F{equipeProfissional} != null && $F{equipeProfissional}.getEquipe() != null && $F{equipeProfissional}.getEquipe().getReferencia() != null ?
                                        Bundle.getStringApplication("rotulo_equipe")+": "+($F{equipeProfissional}.getEquipe().getReferencia()) + " : " + ($F{equipeProfissional}.getEquipe().getEquipeCnes())
                                    : "Não informado"
                                :


                                (Bundle.getStringApplication("rotulo_grupo_subgrupo2").equals($P{FORMA_APRESENTACAO})
                                    ?
                                        Bundle.getStringApplication("rotulo_grupo")+": "+($F{procedimentoSubGrupo}.getRoGrupo().getDescricao()!=null?$F{procedimentoSubGrupo}.getRoGrupo().getDescricao():"Grupo não definido")
                                            +" / "+Bundle.getStringApplication("rotulo_sub_grupo")+": "+($F{procedimentoSubGrupo}.getDescricaoFormatado()!=null?$F{procedimentoSubGrupo}.getDescricaoFormatado():"SubGrupo não definido")
                                    :
                                    ""
                                    )
                                )
                            )
                        )
                    )
                )
            )
        )]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="240" y="13" width="62" height="11" uuid="9afd4f8f-f569-4802-b126-4125f8090ad7"/>
					<box rightPadding="1"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quantidade")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="13" width="233" height="11" uuid="0b9927e0-eded-440d-a4e5-52daf7ebf019"/>
					<box leftPadding="1"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_procedimento")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="314" y="12" width="72" height="11" uuid="a11e658d-3dc0-4274-91d7-f022202f17b0"/>
					<box rightPadding="1"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_valor_sus")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="23" width="555" height="1" uuid="76751f24-9be9-42e1-b800-d53128cc4930"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField>
					<reportElement x="410" y="13" width="50" height="11" uuid="e6f4b767-e6ad-4569-9b09-0b3af25eaf3d"/>
					<box rightPadding="1"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_valor_diferenciado_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="481" y="13" width="51" height="11" uuid="814f0fbf-cc4f-47a6-90cc-6bf34ceffadd"/>
					<box rightPadding="1"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_valor_total_abv2")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="14">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO_LONG.equals($P{VISUALIZAR_VALOR})]]></printWhenExpression>
				<textField pattern="###0.00">
					<reportElement x="465" y="1" width="90" height="11" uuid="0ba4f578-53bb-44d6-8090-2cea172faf20"/>
					<box rightPadding="1"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[( $V{totalQuantidadeFA}!=null? $V{totalQuantidadeFA} : "0,00" )]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="371" y="1" width="90" height="11" uuid="19da400a-63b1-444b-a2e7-3424c1b3bd71"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="415" y="0" width="140" height="1" uuid="8812d696-e2c5-4f0b-b366-275576fd302f"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="233" y="13" width="322" height="1" uuid="0c17131c-7349-40ae-b9f7-6febb10aa974"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
			</band>
			<band height="11">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($P{VISUALIZAR_VALOR})]]></printWhenExpression>
				<textField pattern="###0.00">
					<reportElement x="228" y="0" width="74" height="11" uuid="ef4b73c8-334a-49b0-b43e-2ab4da074540"/>
					<box rightPadding="1"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[( $V{totalQuantidadeFA}!=null? $V{totalQuantidadeFA} : "0,00" )]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="187" y="0" width="31" height="11" uuid="58514d79-aaac-4e1f-965a-905f8e9f0e9b"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="###0.00">
					<reportElement x="483" y="0" width="51" height="11" uuid="ff1af5c0-e224-4148-9bb2-0d99bae87b42"/>
					<box rightPadding="1"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[( $V{valorTotalFa} != null ? $V{valorTotalFa} : "0,00" )]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="procedimento">
		<groupExpression><![CDATA[$F{empresa}]]></groupExpression>
		<groupHeader>
			<band height="12">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO_LONG.equals($P{VISUALIZAR_VALOR})]]></printWhenExpression>
				<textField pattern="###0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="465" y="1" width="90" height="11" uuid="d2125510-abd4-47ac-800b-248c3529479e">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO_LONG.equals($P{VISUALIZAR_VALOR})]]></printWhenExpression>
					</reportElement>
					<box rightPadding="1"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{quantidade}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="0" y="1" width="461" height="11" uuid="2e4a3049-a73f-460d-84c5-6d67459ff8c2"/>
					<box leftPadding="1"/>
					<textElement>
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{procedimento}.getDescricaoFormatado()]]></textFieldExpression>
				</textField>
			</band>
			<band height="13">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($P{VISUALIZAR_VALOR})]]></printWhenExpression>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="0" y="1" width="242" height="11" uuid="01960590-508d-4268-be3c-d05444333e5d"/>
					<box leftPadding="1"/>
					<textElement>
						<font fontName="Arial" size="8"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{procedimento}.getDescricaoFormatado()]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00">
					<reportElement x="242" y="1" width="60" height="11" uuid="672a519d-37ce-47aa-8942-871a989f047e"/>
					<box rightPadding="1"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{quantidade}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00">
					<reportElement x="314" y="1" width="70" height="11" uuid="30839c25-f2f6-4acb-9143-61ea95c7a78b"/>
					<box rightPadding="1"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{valorProcedimento}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00" isBlankWhenNull="false">
					<reportElement x="410" y="1" width="49" height="11" uuid="5f73866c-9b2e-4317-97b6-e8390e329e7f"/>
					<box rightPadding="1"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{valorDiferenciado} != null
?
$F{valorDiferenciado}
:
0.00]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00" isBlankWhenNull="false">
					<reportElement x="481" y="1" width="51" height="11" isRemoveLineWhenBlank="true" uuid="ce6649e1-f0a3-4cf5-8c1f-8c47ea534497"/>
					<box rightPadding="1"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{valorTotalItens}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
