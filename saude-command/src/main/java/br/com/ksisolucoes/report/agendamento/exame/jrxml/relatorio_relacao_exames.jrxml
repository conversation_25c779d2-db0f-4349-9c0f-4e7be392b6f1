<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_exames" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="782" leftMargin="30" rightMargin="30" topMargin="30" bottomMargin="30" uuid="1f0ca790-68de-470a-bf61-0b41027e7d35">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.610510000000001"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo"/>
	<import value="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<import value="br.com.ksisolucoes.vo.basico.Empresa"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="formaApresentacao" class="br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioRelacaoExamesDTOParam.FormaApresentacao"/>
	<field name="exame" class="br.com.ksisolucoes.vo.prontuario.basico.Exame"/>
	<field name="totalProcedimentos" class="java.lang.Long"/>
	<field name="idade" class="java.lang.Long"/>
	<field name="descricaoConvenio" class="java.lang.String"/>
	<field name="valorTotalProcedimentos" class="java.lang.Double"/>
	<variable name="count" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[1]]></variableExpression>
	</variable>
	<variable name="TOTAL_PROCEDIMENTOS" class="java.lang.Long" resetType="Group" resetGroup="FormaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{totalProcedimentos}]]></variableExpression>
	</variable>
	<variable name="TOTAL_G_PROCEDIMENTOS" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{totalProcedimentos}]]></variableExpression>
	</variable>
	<variable name="valorTotalProcedimentos_1" class="java.lang.Double" resetType="Group" resetGroup="FormaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotalProcedimentos}]]></variableExpression>
	</variable>
	<variable name="valorTotalProcedimentos_2" class="java.lang.Double" resetType="Group" resetGroup="TOTAL" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotalProcedimentos}]]></variableExpression>
	</variable>
	<group name="TOTAL">
		<groupFooter>
			<band height="15">
				<textField isStretchWithOverflow="true" pattern="#,##0.00;#,##0.00-" isBlankWhenNull="true">
					<reportElement key="textField-4" x="441" y="3" width="61" height="12" uuid="72b7eb82-ae3f-4b1d-8d3c-99997dd54b10"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TOTAL_G_PROCEDIMENTOS}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="438" y="1" width="116" height="1" uuid="3ed80c0e-dd0e-4065-9aca-0b02ea563e77"/>
				</line>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="318" y="3" width="120" height="12" uuid="f3a85d7e-fd00-4ecb-81ec-fe234df224bb"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_geral_exames")]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00;#,##0.00-">
					<reportElement x="502" y="3" width="45" height="12" uuid="ac401d12-7d09-4e9b-b658-537d92db0d93"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorTotalProcedimentos_2}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="FormaApresentacao" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioRelacaoExamesDTOParam.FormaApresentacao.UNIDADE_SOLICITANTE)
?
    $F{exame}.getEmpresaSolicitante()
:
    $P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioRelacaoExamesDTOParam.FormaApresentacao.UNIDADE_EXECUTANTE)
    ?
        $F{exame}.getLocalExame()
    :
        $P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioRelacaoExamesDTOParam.FormaApresentacao.PROFISSIONAL_SOLICITANTE)
        ?
            $F{exame}.getProfissional()
        :
            $P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioRelacaoExamesDTOParam.FormaApresentacao.TIPO_EXAME)
            ?
                $F{exame}.getTipoExame()
            :
                $P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioRelacaoExamesDTOParam.FormaApresentacao.PACIENTE)
                ?
                    $F{exame}.getNomePaciente()
                :
                    null]]></groupExpression>
		<groupHeader>
			<band height="34">
				<printWhenExpression><![CDATA[!$P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioRelacaoExamesDTOParam.FormaApresentacao.GERAL)]]></printWhenExpression>
				<rectangle radius="10">
					<reportElement mode="Transparent" x="0" y="6" width="782" height="13" uuid="9a736b87-bcbf-41ed-aa36-d77618238c35"/>
				</rectangle>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="0" y="6" width="782" height="13" uuid="6d5efa6f-b032-4f7b-9f85-3d425c48220a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioRelacaoExamesDTOParam.FormaApresentacao.UNIDADE_SOLICITANTE)
?
    Bundle.getStringApplication("rotulo_unidade_solicitante")+": "+$F{exame}.getEmpresaSolicitante().getDescricaoFormatadaCnesDescricao()
:
    $P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioRelacaoExamesDTOParam.FormaApresentacao.UNIDADE_EXECUTANTE)
    ?
        Bundle.getStringApplication("rotulo_unidade_executante")+": "+$F{exame}.getLocalExame().getDescricaoFormatadaCnesDescricao()
    :
        $P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioRelacaoExamesDTOParam.FormaApresentacao.PROFISSIONAL_SOLICITANTE)
        ?
            Bundle.getStringApplication("rotulo_profissional_solicitante")+": "+$F{exame}.getProfissional().getNome()
        :
            $P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioRelacaoExamesDTOParam.FormaApresentacao.TIPO_EXAME)
            ?
                Bundle.getStringApplication("rotulo_tipo_exame")+": "+$F{exame}.getTipoExame().getDescricaoFormatado()
            :
                $P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioRelacaoExamesDTOParam.FormaApresentacao.PACIENTE)
                ?
                    Bundle.getStringApplication("rotulo_paciente")+": "+$F{exame}.getNomePaciente()
                :
                    ""]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="32" width="782" height="1" uuid="93d64167-0e24-4529-b2dd-95228b62ae58"/>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="457" y="21" width="45" height="12" uuid="419c81ae-0a4b-4223-aaf8-203cfafbeb42"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quantidade_procedimento_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="172" y="21" width="28" height="12" uuid="23a519bc-2df3-4ce0-9141-a04105bad957"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_idade")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="201" y="21" width="76" height="12" uuid="f0673ba2-be6b-4083-88e7-a80aa1aa69da"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade_solicitante_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="277" y="21" width="50" height="12" uuid="660fd761-5bc4-4785-b82a-d3e86c23bdf6"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="327" y="21" width="130" height="12" uuid="24252ae4-f248-4c98-b760-9ec902a5f7cf"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tipo_exame_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="549" y="21" width="53" height="12" uuid="69da22ac-b482-4d77-bcc2-45cbd90ef09a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_situacao")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="602" y="21" width="47" height="12" uuid="9fbf7be9-36b1-4a70-a8c8-d2f6ea5701a4"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_convenio")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="649" y="21" width="132" height="12" uuid="f3ef57d7-19a1-461b-823c-a4efc014437c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade_executante")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="0" y="21" width="32" height="12" uuid="63ba4489-dd78-46b7-b8fc-60400ee91e43"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_exame")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="502" y="21" width="45" height="12" uuid="c6665c54-cbbe-40b5-9cab-c906d877301b"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_valor_total_abv2")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="41" y="21" width="130" height="12" uuid="63ba4489-dd78-46b7-b8fc-60400ee91e43"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_paciente")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="15">
				<printWhenExpression><![CDATA[!$P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioRelacaoExamesDTOParam.FormaApresentacao.GERAL)]]></printWhenExpression>
				<line>
					<reportElement x="430" y="1" width="124" height="1" uuid="6bf5f9b8-08c3-493d-bace-acaa23614d16"/>
				</line>
				<textField isStretchWithOverflow="true" pattern="#,##0.00;#,##0.00-" isBlankWhenNull="true">
					<reportElement key="textField-4" x="440" y="3" width="62" height="12" uuid="21d3c078-1a0e-4898-8309-15a601e2ca1a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TOTAL_PROCEDIMENTOS}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="319" y="3" width="119" height="12" uuid="19766847-44f5-4a8c-b958-2a9d2008e905"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_exames")]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00;#,##0.00-">
					<reportElement x="502" y="3" width="45" height="12" uuid="7b080584-dcf4-4937-8263-460d2e501ad0"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorTotalProcedimentos_1}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="14" splitType="Stretch">
			<printWhenExpression><![CDATA[$P{formaApresentacao}.equals(br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioRelacaoExamesDTOParam.FormaApresentacao.GERAL)]]></printWhenExpression>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="41" y="1" width="130" height="12" uuid="5bac499c-78e8-47aa-ac68-0bc77da6c6fa"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_paciente")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="12" width="782" height="1" uuid="6f7deeef-9da4-4850-af09-8b3eeb6e1804"/>
			</line>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="457" y="1" width="45" height="12" uuid="20e0cdda-5fef-47c8-b11a-426f424c4089"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quantidade_procedimento_abv")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="172" y="1" width="28" height="12" uuid="6e499bd6-27d6-47dd-846d-ebb79641016f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_idade")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="201" y="1" width="76" height="12" uuid="d4271fcd-bff5-4944-86a1-c19464fd7363"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade_solicitante_abv")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="277" y="1" width="50" height="12" uuid="523cb556-9484-484f-8509-3c0018302fb9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="327" y="1" width="130" height="12" uuid="d4fea2e8-66f2-4e56-8cc5-49fe00b4b710"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tipo_exame_abv")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="549" y="1" width="53" height="12" uuid="a4bc74ac-51c2-4cf3-9bce-4434b79ada40"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_situacao")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="602" y="1" width="47" height="12" uuid="05c73ae6-63a2-4506-a870-4770adb8f0c5"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_convenio")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="649" y="1" width="132" height="12" uuid="caa012f1-6bfc-442f-a507-aed91793338a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade_executante")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="0" y="1" width="32" height="12" uuid="c22d9a86-2cb1-42a2-823e-e98ded8b9d57"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_exame")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="502" y="1" width="45" height="12" uuid="cf2dd00a-43db-46ce-b8e9-aa5ebde0bb13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_valor_total_abv2")]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="12" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="41" y="0" width="130" height="12" uuid="92e5087f-7c97-4226-b1ed-a01bf823ad6f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{exame}.getNomePaciente()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00;#,##0.00-" isBlankWhenNull="true">
				<reportElement key="textField-4" x="457" y="0" width="45" height="12" uuid="c7dd5b83-4337-4a49-9575-69ed266c9df1"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{totalProcedimentos}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="172" y="0" width="28" height="12" uuid="79e5e68f-b593-4966-ad50-cef949ebad0a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{idade}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="201" y="0" width="76" height="12" uuid="7dc1f2db-835c-46aa-80e1-d894383ea151"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{exame}.getEmpresaSolicitante().getDescricaoFormatadaCnesSigla()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="277" y="0" width="50" height="12" uuid="a8e63d50-0f10-4e69-a03a-fd986dd21f37"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.formatar($F{exame}.getDataSolicitacao())]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="327" y="0" width="130" height="12" uuid="cbbf8077-95cf-4889-bc09-cc40886b5037"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{exame}.getTipoExame().getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="549" y="0" width="53" height="12" uuid="f98a7bf4-127c-49e6-a2f7-6f26f8b61448"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{exame}.getDescricaoStatus()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="602" y="0" width="47" height="12" uuid="eef804b4-541c-4f69-ab1b-9c8278a397dc"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoConvenio}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="649" y="0" width="132" height="12" uuid="a3599456-431a-4e75-aed9-2e1d46a9e806"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{exame}.getLocalExame().getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="0" y="0" width="32" height="12" uuid="bbc06b6c-20ee-44a0-9f31-5517b01d7f9a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{exame}.getCodigo()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00;#,##0.00-" isBlankWhenNull="true">
				<reportElement key="textField-4" x="502" y="0" width="45" height="12" uuid="537a3a5b-a7d9-4356-88b7-c5740ed78c84"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{valorTotalProcedimentos}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
