package br.com.ksisolucoes.report.prontuario.basico;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.basico.query.QueryImpressaoPlanoEnfermagem;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoPlanoEnfermagem extends AbstractReport<Long> {

    private String planoEnfermagem;

    public RelatorioImpressaoPlanoEnfermagem(Long codigoAtendimento, String planoEnfermagem) {
        super(codigoAtendimento);
        this.planoEnfermagem = planoEnfermagem;
    }

    @Override
    public String getXML() {
        addParametro("planoEnfermagem", planoEnfermagem);
        return "/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_impressao_plano_enfermagem.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_plano_enfermagem");
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryImpressaoPlanoEnfermagem();
    }
}
