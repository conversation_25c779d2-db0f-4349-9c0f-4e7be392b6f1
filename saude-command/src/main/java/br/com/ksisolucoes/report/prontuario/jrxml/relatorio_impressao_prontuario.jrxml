<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_impressao_prontuario" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="3b1582db-a51d-45c8-bdae-3a1b497e06e8">
	<property name="ireport.zoom" value="2.593742460100007"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="9"/>
	<import value="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<subDataset name="ds-ocorrencias" uuid="b1a74d6b-afc2-4f69-8f6a-50847c5e2b09">
		<field name="dataOcorrencia" class="java.util.Date"/>
		<field name="descricao" class="java.lang.String"/>
	</subDataset>
	<parameter name="ocorrencias" class="java.util.List"/>
	<parameter name="imprimirOcorrencias" class="java.lang.Boolean"/>
	<parameter name="exibirCapa" class="java.lang.Boolean"/>
	<parameter name="nomeUnidadeSaude" class="java.lang.String"/>
	<parameter name="descricaoPeriodo" class="java.lang.String"/>
	<parameter name="descricaoCidadeData" class="java.lang.String"/>
	<parameter name="descricaoProfissional" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa">
		<fieldDescription><![CDATA[empresa]]></fieldDescription>
	</field>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional">
		<fieldDescription><![CDATA[profissional]]></fieldDescription>
	</field>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus">
		<fieldDescription><![CDATA[usuarioCadsus]]></fieldDescription>
	</field>
	<field name="atendimento" class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"/>
	<field name="tipoAtendimento" class="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"/>
	<field name="atendimentoProntuario" class="br.com.ksisolucoes.vo.prontuario.basico.AtendimentoProntuario"/>
	<field name="cns" class="java.lang.String"/>
	<field name="usuarioCadsusOcorrenciaList" class="java.util.List"/>
	<field name="enderecoUsuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus"/>
	<group name="geral">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band height="29">
				<printWhenExpression><![CDATA[$P{exibirCapa} &&
$V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<subreport isUsingCache="true" runToBottom="false">
					<reportElement x="0" y="0" width="555" height="28" isPrintWhenDetailOverflows="true" uuid="3a84ae2b-560e-4d9c-988c-a61f90df407a"/>
					<subreportParameter name="descricaoCidadeData">
						<subreportParameterExpression><![CDATA[$P{descricaoCidadeData}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="descricaoPeriodo">
						<subreportParameterExpression><![CDATA[$P{descricaoPeriodo}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="descricaoProfissional">
						<subreportParameterExpression><![CDATA[$P{descricaoProfissional}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="nomeUnidadeSaude">
						<subreportParameterExpression><![CDATA[$P{nomeUnidadeSaude}]]></subreportParameterExpression>
					</subreportParameter>
					<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.JREmptyDataSource(1)]]></dataSourceExpression>
					<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_impressao_capa_prontuario.jasper"]]></subreportExpression>
				</subreport>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="49">
				<printWhenExpression><![CDATA[$P{imprimirOcorrencias}]]></printWhenExpression>
				<componentElement>
					<reportElement x="0" y="31" width="555" height="14" uuid="a51ce1d5-1847-47d9-8e57-8c413de76745"/>
					<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
						<datasetRun subDataset="ds-ocorrencias" uuid="*************-4197-9aa0-0c42a5fbde6e">
							<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($P{ocorrencias})]]></dataSourceExpression>
						</datasetRun>
						<jr:listContents height="14" width="555">
							<textField>
								<reportElement x="3" y="0" width="100" height="14" isPrintWhenDetailOverflows="true" uuid="16a5589d-2d7e-4b9e-8abd-9033c00c0da7"/>
								<textFieldExpression><![CDATA[$F{dataOcorrencia}]]></textFieldExpression>
							</textField>
							<textField isStretchWithOverflow="true">
								<reportElement x="105" y="0" width="450" height="13" isPrintWhenDetailOverflows="true" uuid="f23de395-b518-4ed5-8898-0c0ea3846b0d"/>
								<textFieldExpression><![CDATA[$F{descricao}]]></textFieldExpression>
							</textField>
						</jr:listContents>
					</jr:list>
				</componentElement>
				<textField isBlankWhenNull="true">
					<reportElement x="3" y="19" width="100" height="12" isPrintWhenDetailOverflows="true" uuid="e9249b12-5345-4446-84ed-9fb2375c64a7"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*data*/
Bundle.getStringApplication("rotulo_data")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="105" y="19" width="182" height="12" isPrintWhenDetailOverflows="true" uuid="fac76082-0171-41d1-aa06-e87831b3ccd7"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*ocorrencia*/
Bundle.getStringApplication("rotulo_ocorrencia")]]></textFieldExpression>
				</textField>
				<rectangle>
					<reportElement mode="Opaque" x="0" y="3" width="555" height="15" backcolor="#AFAFAF" uuid="2a760f23-8255-4e58-9e33-41b1d3a36d2a"/>
					<graphicElement>
						<pen lineWidth="0.0"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="true">
					<reportElement mode="Opaque" x="0" y="3" width="555" height="15" isPrintWhenDetailOverflows="true" backcolor="#D3D3D3" uuid="313abe6b-ba77-4180-b13f-49bef4c6d330"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*ocorrencias*/
Bundle.getStringApplication("rotulo_ocorrencias")]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="usuarioCadsus" isStartNewPage="true">
		<groupExpression><![CDATA[$F{usuarioCadsus}]]></groupExpression>
		<groupHeader>
			<band height="81">
				<rectangle radius="10">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="7" width="555" height="74" uuid="55bcae86-4c8a-4b88-ab29-0ea677754279"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="true">
					<reportElement x="8" y="30" width="62" height="12" uuid="607bdb8a-7e20-4730-9202-f497a043eaef"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Sexo*/
Bundle.getStringApplication("rotulo_sexo")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="205" y="30" width="82" height="12" uuid="70327581-36fd-4f2b-8235-b73a353c6596"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Data Nascimento*/
Bundle.getStringApplication("rotulo_data_nascimento")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="392" y="30" width="50" height="12" uuid="248abd8f-7347-471c-9c75-25e3fc70ded0"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Idade*/
Bundle.getStringApplication("rotulo_idade")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="72" y="18" width="480" height="12" uuid="7824d823-542b-4edf-ab0d-e714bdc813ec"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="72" y="30" width="120" height="12" uuid="b63259ae-6c63-44c0-8185-adea3f32a199"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getSexoFormatado() != null ? $F{usuarioCadsus}.getSexoFormatado().toUpperCase() : ""]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
					<reportElement x="289" y="30" width="101" height="12" uuid="18ebf7d8-9aa6-4f7b-832b-4e2123971146"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDataNascimento()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="444" y="30" width="108" height="12" uuid="e6122ae1-e3ab-43a1-9a95-f393d6a968d9"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDescricaoIdade()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="8" y="18" width="62" height="12" uuid="a7c4677e-67ff-4bc0-aea5-f92c39d2d928"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Paciente*/
Bundle.getStringApplication("rotulo_paciente")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement mode="Opaque" x="22" y="1" width="95" height="12" uuid="5552ae68-4e80-40b4-8224-4566d485398a"/>
					<box leftPadding="0"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_paciente")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="72" y="42" width="480" height="12" uuid="e871003a-c453-452e-99fc-fbeafd38fca9"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{enderecoUsuarioCadsus}.getEnderecoFormatadoComCidade()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="8" y="42" width="62" height="12" uuid="39050405-e2d9-4c7f-873c-adf0978f6325"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Endereco*/
Bundle.getStringApplication("rotulo_endereco")+": "]]></textFieldExpression>
				</textField>
				<elementGroup>
					<textField pattern="(##) ####-#####" isBlankWhenNull="true">
						<reportElement positionType="FixRelativeToBottom" stretchType="RelativeToTallestObject" x="289" y="66" width="101" height="12" uuid="311c4003-4cbb-457d-8eeb-693719d85fb9"/>
						<textElement>
							<font fontName="Arial" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{usuarioCadsus}.getTelefone()]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement positionType="FixRelativeToBottom" stretchType="RelativeToTallestObject" x="8" y="54" width="62" height="12" uuid="a7c4677e-67ff-4bc0-aea5-f92c39d2d928"/>
						<textElement textAlignment="Right">
							<font fontName="Arial" size="9" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[/*Nome mãe*/
Bundle.getStringApplication("rotulo_nome_mae")+": "]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement positionType="FixRelativeToBottom" stretchType="RelativeToTallestObject" x="72" y="66" width="133" height="12" uuid="39a72bce-e5fd-40c0-a4b7-8b5b73d31e54"/>
						<textElement>
							<font fontName="Arial" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{cns}]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement positionType="FixRelativeToBottom" stretchType="RelativeToTallestObject" x="392" y="66" width="50" height="12" uuid="a23732a4-fe78-4a47-87da-1582f933827f"/>
						<textElement textAlignment="Right">
							<font fontName="Arial" size="9" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[/*Celular*/
Bundle.getStringApplication("rotulo_celular")+": "]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement positionType="FixRelativeToBottom" stretchType="RelativeToTallestObject" x="8" y="66" width="62" height="12" uuid="5d4c4793-5645-4c41-9cc4-0323b59e0771"/>
						<textElement textAlignment="Right">
							<font fontName="Arial" size="9" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[/*CNS*/
Bundle.getStringApplication("rotulo_cns")+": "]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement positionType="FixRelativeToBottom" stretchType="RelativeToTallestObject" x="72" y="54" width="480" height="12" uuid="7824d823-542b-4edf-ab0d-e714bdc813ec"/>
						<textElement>
							<font fontName="Arial" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNomeMae()]]></textFieldExpression>
					</textField>
					<textField pattern="(##) ####-#####" isBlankWhenNull="true">
						<reportElement positionType="FixRelativeToBottom" stretchType="RelativeToTallestObject" x="444" y="66" width="108" height="12" uuid="829bb796-8584-4332-bb14-dbccc23f1240"/>
						<textElement>
							<font fontName="Arial" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{usuarioCadsus}.getCelular()]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement positionType="FixRelativeToBottom" stretchType="RelativeToTallestObject" x="205" y="66" width="82" height="12" uuid="36287631-da1d-4432-ae0a-3e27e236b5c5"/>
						<textElement textAlignment="Right">
							<font fontName="Arial" size="9" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[/*Telefone*/
Bundle.getStringApplication("rotulo_telefone")+": "]]></textFieldExpression>
					</textField>
				</elementGroup>
			</band>
		</groupHeader>
	</group>
	<group name="atendimentoPrincipal" keepTogether="true">
		<groupExpression><![CDATA[$F{atendimento}.getAtendimentoPrincipal()]]></groupExpression>
		<groupHeader>
			<band height="54">
				<rectangle radius="10">
					<reportElement mode="Transparent" x="0" y="7" width="555" height="47" uuid="87d16266-7093-413a-9341-c544340c81ae"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="true">
					<reportElement x="462" y="31" width="40" height="12" uuid="a7c4677e-67ff-4bc0-aea5-f92c39d2d928"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Número*/
Bundle.getStringApplication("rotulo_numero")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="504" y="31" width="50" height="12" uuid="b63259ae-6c63-44c0-8185-adea3f32a199"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{atendimento}.getAtendimentoPrincipal().getCodigo()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="278" y="31" width="83" height="12" uuid="a7c4677e-67ff-4bc0-aea5-f92c39d2d928"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Data atendimento*/
Bundle.getStringApplication("rotulo_data_atendimento")+": "]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy HH:mm z" isBlankWhenNull="true">
					<reportElement x="363" y="31" width="99" height="12" uuid="b63259ae-6c63-44c0-8185-adea3f32a199"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{atendimento}.getAtendimentoPrincipal().getDataAtendimento()]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement x="72" y="19" width="262" height="12" uuid="90a5c41c-4c18-4068-be21-2a50147dc6df"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{empresa}.getDescricao()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="8" y="19" width="62" height="12" uuid="1c71e683-ab91-4167-a902-3b31f23036e7"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Empresa*/
Bundle.getStringApplication("rotulo_empresa")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="338" y="19" width="46" height="12" uuid="70ef624b-a1f3-4ed3-9f8e-6b72ffde33be"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Telefone*/
Bundle.getStringApplication("rotulo_telefone")+": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement x="386" y="19" width="76" height="12" uuid="90a5c41c-4c18-4068-be21-2a50147dc6df"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{empresa}.getTelefoneFormatado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="8" y="31" width="81" height="12" uuid="70ef624b-a1f3-4ed3-9f8e-6b72ffde33be"/>
					<textElement textAlignment="Left">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Tipo atendimento*/
Bundle.getStringApplication("rotulo_tipo_atendimento")+": "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement x="90" y="31" width="188" height="12" uuid="1ea087fc-695f-4ba8-8de4-7ee8129211f6"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{tipoAtendimento}.getTipoAtendimentoPrincipal().getDescricao()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement mode="Opaque" x="22" y="1" width="66" height="12" uuid="6080c0b1-12b8-42e8-ac13-ed6aa1e2608d"/>
					<box leftPadding="0"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_atendimento")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="atendimentosImportados">
		<groupExpression><![CDATA[null]]></groupExpression>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<detail>
		<band height="91">
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" x="3" y="16" width="198" height="12" uuid="1ea087fc-695f-4ba8-8de4-7ee8129211f6"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profissional}.getDescricaoProfissionalComNumeroRegistroFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="3" y="4" width="62" height="12" uuid="a92ac42c-4744-4f04-a66d-9bfbbb81fb4c"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Profissional*/
Bundle.getStringApplication("rotulo_profissional")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="3" y="35" width="40" height="12" uuid="a92ac42c-4744-4f04-a66d-9bfbbb81fb4c"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Tipo*/
Bundle.getStringApplication("rotulo_tipo")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="3" y="47" width="189" height="12" uuid="18ebf7d8-9aa6-4f7b-832b-4e2123971146"/>
				<textElement markup="html">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[br.com.ksisolucoes.vo.prontuario.basico.AtendimentoProntuario.TipoRegistro.valeuOf($F{atendimentoProntuario}.getTipoRegistro()).descricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="211" y="35" width="76" height="12" uuid="a92ac42c-4744-4f04-a66d-9bfbbb81fb4c"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Data Registro*/
Bundle.getStringApplication("rotulo_data_registro")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy HH:mm z" isBlankWhenNull="true">
				<reportElement x="211" y="47" width="92" height="12" uuid="18ebf7d8-9aa6-4f7b-832b-4e2123971146"/>
				<textElement markup="html">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimentoProntuario}.getData()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" evaluationTime="Auto" isBlankWhenNull="true">
				<reportElement positionType="Float" x="5" y="74" width="470" height="12" uuid="18ebf7d8-9aa6-4f7b-832b-4e2123971146"/>
				<textElement rotation="None" markup="html">
					<font fontName="Arial" size="9" isBold="false" isUnderline="false" isStrikeThrough="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimentoProntuario}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="303" y="47" width="247" height="12" uuid="1ea087fc-695f-4ba8-8de4-7ee8129211f6"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipoAtendimento}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="303" y="35" width="81" height="12" uuid="70ef624b-a1f3-4ed3-9f8e-6b72ffde33be"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Tipo atendimento*/
Bundle.getStringApplication("rotulo_tipo_atendimento")]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Auto" isBlankWhenNull="true">
				<reportElement positionType="Float" x="4" y="62" width="81" height="12" uuid="70ef624b-a1f3-4ed3-9f8e-6b72ffde33be"/>
				<textElement textAlignment="Left" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isUnderline="false" isStrikeThrough="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Descrição*/
Bundle.getStringApplication("rotulo_descricao")+": "]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="FixRelativeToBottom" x="0" y="88" width="555" height="1" uuid="09b43489-1d4f-4ce6-98cd-6bf88429b499"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement x="209" y="4" width="62" height="12" uuid="840ad2a8-f816-496a-81d3-396babc9287c"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*CBO*/
Bundle.getStringApplication("rotulo_cbo")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="209" y="16" width="339" height="12" uuid="f8cb3f9e-c879-45dc-8126-45fbcacad247"/>
				<textElement markup="none">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimentoProntuario}.getTabelaCbo().getDescricaoFormatado()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
