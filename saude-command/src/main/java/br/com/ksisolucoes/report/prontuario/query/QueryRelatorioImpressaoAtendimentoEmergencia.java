package br.com.ksisolucoes.report.prontuario.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.QueryImpressaoDispensacaoPrescricaoDTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.query.QueryImpressaoDispensacaoPrescricao;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioImpressaoAtendimentoDTOParam;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioImpressaoAtendimentoEmergenciaDTO;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.util.ArrayList;
import java.util.List;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioImpressaoAtendimentoEmergencia extends CommandQuery<QueryRelatorioImpressaoAtendimentoEmergencia> implements ITransferDataReport<RelatorioImpressaoAtendimentoDTOParam, RelatorioImpressaoAtendimentoEmergenciaDTO> {

    private RelatorioImpressaoAtendimentoDTOParam param;
    private List<RelatorioImpressaoAtendimentoEmergenciaDTO> result;

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        RelatorioImpressaoAtendimentoEmergenciaDTO dto = new RelatorioImpressaoAtendimentoEmergenciaDTO();
        QueryRelatorioImpressaoAtendimentoInternacao qriai = new QueryRelatorioImpressaoAtendimentoInternacao();
        qriai.setDTOParam(param);
        dto.setListEvolucao(qriai.start().getResult());
        
        QueryImpressaoDispensacaoPrescricaoDTOParam dispensacaoPrescricaoDTOParam = new QueryImpressaoDispensacaoPrescricaoDTOParam();
        dispensacaoPrescricaoDTOParam.setNumeroAtendimentoPrincipal(param.getAtendimento().getCodigo());
        
        QueryImpressaoDispensacaoPrescricao qidp = new QueryImpressaoDispensacaoPrescricao();
        qidp.setDTOParam(dispensacaoPrescricaoDTOParam);
        dto.setListDispensacao(qidp.start().getResult());
        
        result = new ArrayList<RelatorioImpressaoAtendimentoEmergenciaDTO>();
        result.add(dto);
    }

    @Override
    public List<RelatorioImpressaoAtendimentoEmergenciaDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelatorioImpressaoAtendimentoDTOParam param) {
        this.param = param;
    }

}
