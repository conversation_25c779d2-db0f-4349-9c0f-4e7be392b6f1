<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relacao_planejamento_visita" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="904ef39c-8037-443b-8222-af49929dd456">
	<property name="ireport.zoom" value="2.196150000000001"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="br.com.celk.util.Coalesce"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<import value="org.apache.commons.lang.StringUtils"/>
	<subDataset name="membros" uuid="398515d0-c34c-4987-b89c-d0dda0578348">
		<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
		<field name="descricaoCondicao" class="java.lang.String"/>
	</subDataset>
	<subDataset name="membros2" uuid="79642798-9b9f-4ba5-9ac7-83dbc14c7c24">
		<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
		<field name="descricaoCondicao" class="java.lang.String"/>
	</subDataset>
	<parameter name="PLANEJAMENTO_VISITA" class="br.com.ksisolucoes.vo.cadsus.PlanejamentoVisita"/>
	<parameter name="IMPRIMIR_ENDERECO" class="java.lang.Boolean"/>
	<parameter name="IMPRIMIR_DOENCA" class="java.lang.Boolean"/>
	<field name="enderecoDomicilio" class="br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio"/>
	<field name="nomeResponsavel" class="java.lang.String"/>
	<field name="lstUsuarioCadsusEsus" class="java.util.List"/>
	<field name="telefoneResponsavel" class="java.lang.String"/>
	<group name="FADefault" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band height="82">
				<rectangle radius="10">
					<reportElement x="0" y="8" width="802" height="70" uuid="64066800-f512-409a-b446-aac3c7e3b5eb"/>
				</rectangle>
				<textField isBlankWhenNull="true">
					<reportElement mode="Opaque" x="13" y="3" width="60" height="11" uuid="84a51ab8-8716-44de-a4b2-079fccff6b61"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true" isStrikeThrough="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_visita")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement mode="Transparent" x="12" y="18" width="55" height="11" uuid="e286abcf-6ca9-46ba-acbd-4a811eeae3a9"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true" isStrikeThrough="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_profissional")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="73" y="18" width="256" height="11" uuid="80ac0748-ae43-42fa-845c-bfae2c58b1f0"/>
					<textElement>
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{PLANEJAMENTO_VISITA}.getProfissional().getNome()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement mode="Transparent" x="12" y="34" width="55" height="11" uuid="93ce3a93-0371-420f-9b77-82c26e54622d"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true" isStrikeThrough="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_area")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement mode="Transparent" x="12" y="50" width="55" height="11" uuid="3734e94b-6f22-454c-ba72-fe80d2b66af1"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true" isStrikeThrough="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_observacao")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="73" y="34" width="170" height="11" uuid="b2f572cf-28e0-4b3c-b298-2212dcc38725"/>
					<textElement>
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{PLANEJAMENTO_VISITA}.getEquipeMicroArea().getEquipeArea().getDescricao()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="73" y="50" width="720" height="11" uuid="565e4d4c-eee7-4eba-84e8-9f75866fdaff"/>
					<textElement markup="html">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{PLANEJAMENTO_VISITA}.getObservacao()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement mode="Transparent" x="335" y="18" width="45" height="11" uuid="45932218-3edf-4d4a-a24b-fbeff33ac0b0"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true" isStrikeThrough="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="385" y="18" width="256" height="11" uuid="50c6d5a4-1b32-4b1c-99f3-86e052b9c124"/>
					<textElement>
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{PLANEJAMENTO_VISITA}.getEmpresa().getDescricao()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement mode="Transparent" x="250" y="34" width="45" height="11" uuid="3a5b50d7-a84e-43e1-b193-def91e69fa87"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true" isStrikeThrough="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_micro_area")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="299" y="34" width="30" height="11" uuid="db3922c3-0ca7-4824-a4d6-adec07da14a2"/>
					<textElement>
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{PLANEJAMENTO_VISITA}.getEquipeMicroArea().getMicroArea()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement mode="Transparent" x="335" y="34" width="45" height="11" uuid="1056e50f-e2b9-4c35-97b5-8add7757793a"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true" isStrikeThrough="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_visita")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement mode="Transparent" x="432" y="34" width="5" height="11" uuid="5bd8fd60-52e2-496b-8cfe-08e1228cf8db"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true" isStrikeThrough="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_aa")]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
					<reportElement x="385" y="34" width="45" height="11" uuid="6959204b-8bc1-41f7-8738-1f8fc3241b92"/>
					<textElement>
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{PLANEJAMENTO_VISITA}.getDataVisitaInicio()]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
					<reportElement x="441" y="34" width="45" height="11" uuid="58c5d390-cc32-4f0a-abfe-5875232fe64c"/>
					<textElement>
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{PLANEJAMENTO_VISITA}.getDataVisitaFim()]]></textFieldExpression>
				</textField>
			</band>
			<band height="12">
				<printWhenExpression><![CDATA[!$P{IMPRIMIR_ENDERECO}]]></printWhenExpression>
				<line>
					<reportElement x="219" y="0" width="1" height="12" uuid="68745ac5-e1ce-40dd-a7c2-358bd151228c"/>
				</line>
				<line>
					<reportElement x="601" y="0" width="1" height="12" uuid="4b6c9d33-e191-40e9-9ef0-469b324c2f60"/>
				</line>
				<textField>
					<reportElement x="35" y="0" width="50" height="11" uuid="555d7541-bbea-4925-8885-091f3ded3505"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_domicilio")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="86" y="0" width="1" height="12" uuid="c4caccf2-c2c2-42b2-9065-6c66f2e9c16a"/>
				</line>
				<textField>
					<reportElement x="605" y="0" width="195" height="11" uuid="a6dda87b-82cb-40cc-b2fe-f54e1fe9a8e0"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_assinatura")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="0" width="35" height="11" uuid="f90945a6-0498-477b-905a-731547374d8e"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_familia")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="88" y="0" width="130" height="11" uuid="8ac47443-f513-44aa-95c2-732f2c059b39"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Responsavel*/
Bundle.getStringApplication("rotulo_responsavel")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="459" y="0" width="1" height="12" uuid="862c5b1c-1cd4-49e0-8602-110ab3a51c34"/>
				</line>
				<textField>
					<reportElement x="298" y="0" width="160" height="11" uuid="3c76c6da-9152-4e20-bed9-9ef7a9769ce2"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Motivo da Visita*/
Bundle.getStringApplication("rotulo_motivo_visita")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="463" y="0" width="65" height="11" uuid="f24f65f4-a9dd-4df1-b861-************"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_visita")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="530" y="0" width="1" height="12" uuid="046e3dce-be37-4ecd-ab1c-d7579c08a71d"/>
				</line>
				<textField>
					<reportElement x="534" y="0" width="65" height="11" uuid="7578110f-1ba3-4890-91f1-1204dd118af3"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_turno_visita")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="221" y="0" width="72" height="11" uuid="0c4eec92-8bf1-4cd1-bf8f-d56552c0b90d"/>
					<textElement textAlignment="Left">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_telefone")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="295" y="0" width="1" height="12" uuid="cfcefc7e-dc74-4f3f-8bbe-e3959f33e6a2"/>
				</line>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="370">
				<printWhenExpression><![CDATA[$P{IMPRIMIR_ENDERECO}]]></printWhenExpression>
				<textField>
					<reportElement x="759" y="2" width="40" height="11" uuid="ae4251ef-856e-42b3-809b-396a92c7e146"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{FA_COUNT}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="716" y="2" width="40" height="11" uuid="2bae9521-d953-4c9f-ab88-b051edb9372b"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="688" y="29" width="1" height="340" uuid="62d1b9a5-cfdd-4380-a18b-9213b3808d6a"/>
				</line>
				<line>
					<reportElement x="437" y="29" width="1" height="340" uuid="*************-4c7e-8348-27db493fe7ba"/>
				</line>
				<line>
					<reportElement x="0" y="46" width="802" height="1" uuid="b7373881-7175-422f-b74a-3e9c77c448ac"/>
				</line>
				<line>
					<reportElement x="570" y="29" width="1" height="340" uuid="838b686e-5fe7-4bde-879a-ce9ab1b0455b"/>
				</line>
				<line>
					<reportElement x="0" y="63" width="802" height="1" uuid="a58c533a-9d5a-46cb-9cbf-c110d422091b"/>
				</line>
				<line>
					<reportElement x="0" y="29" width="802" height="1" uuid="d5fc7c8e-9845-4c87-80d5-d63895dded02"/>
				</line>
				<line>
					<reportElement x="0" y="80" width="802" height="1" uuid="aba104a5-af01-480a-a85b-8ff4116de483"/>
				</line>
				<line>
					<reportElement x="0" y="97" width="802" height="1" uuid="97161ca2-6da8-4e7c-872b-4ffd73143d6c"/>
				</line>
				<line>
					<reportElement x="0" y="131" width="802" height="1" uuid="290f8b5e-dc7a-4526-87e4-58c51adb3183"/>
				</line>
				<line>
					<reportElement x="0" y="114" width="802" height="1" uuid="80f336b2-5282-491f-bdb0-2957951601a8"/>
				</line>
				<line>
					<reportElement x="0" y="148" width="802" height="1" uuid="2461be9f-f4af-472d-a7db-950ce0869aef"/>
				</line>
				<line>
					<reportElement x="0" y="165" width="802" height="1" uuid="7d6d3cc4-9d47-4d6c-bf62-425d706d92a6"/>
				</line>
				<line>
					<reportElement x="0" y="182" width="802" height="1" uuid="9c2ccae5-be3d-4692-b3d9-98f04aeda151"/>
				</line>
				<line>
					<reportElement x="0" y="199" width="802" height="1" uuid="2e26471c-aeec-4a6d-add4-56f5d8b17131"/>
				</line>
				<line>
					<reportElement x="0" y="0" width="802" height="1" uuid="fb44fa67-4ef7-47e8-b53f-ca21c6313e0c"/>
				</line>
				<line>
					<reportElement x="251" y="29" width="1" height="340" uuid="f7088f50-41cb-4939-9881-3c8861252444"/>
				</line>
				<line>
					<reportElement stretchType="RelativeToBandHeight" x="251" y="1" width="1" height="28" uuid="10f317b4-c3c8-470c-b632-87f38df9f8c1"/>
				</line>
				<line>
					<reportElement stretchType="RelativeToBandHeight" x="437" y="1" width="1" height="28" uuid="10f317b4-c3c8-470c-b632-87f38df9f8c1"/>
				</line>
				<line>
					<reportElement stretchType="RelativeToBandHeight" x="570" y="1" width="1" height="28" uuid="10f317b4-c3c8-470c-b632-87f38df9f8c1"/>
				</line>
				<line>
					<reportElement stretchType="RelativeToBandHeight" x="688" y="1" width="1" height="28" uuid="10f317b4-c3c8-470c-b632-87f38df9f8c1"/>
				</line>
				<line>
					<reportElement stretchType="RelativeToBandHeight" x="502" y="1" width="1" height="28" uuid="9b6fb60f-2ce8-442a-bc09-dc0eeee8f854"/>
				</line>
				<line>
					<reportElement x="502" y="29" width="1" height="340" uuid="84a9c226-d211-4e41-bfa3-3ba85621be61"/>
				</line>
				<line>
					<reportElement x="0" y="216" width="802" height="1" uuid="9adba0eb-48eb-4768-a915-2491ed7667ae"/>
				</line>
				<line>
					<reportElement x="0" y="233" width="802" height="1" uuid="e305c497-fb10-45ef-9b7f-41e561289eb0"/>
				</line>
				<line>
					<reportElement x="0" y="250" width="802" height="1" uuid="c22f4bed-f0e7-4915-bf3a-e50324f3f2d8"/>
				</line>
				<line>
					<reportElement x="0" y="267" width="802" height="1" uuid="9259e57a-23ff-4c6c-a476-39e8101d44d8"/>
				</line>
				<line>
					<reportElement x="0" y="284" width="802" height="1" uuid="8c3a3869-2b22-4074-963e-970de2fa9c92"/>
				</line>
				<line>
					<reportElement x="0" y="301" width="802" height="1" uuid="21a4ee5b-a0e1-40a6-8097-f094ef3848f0"/>
				</line>
				<line>
					<reportElement x="0" y="318" width="802" height="1" uuid="c342d1c2-cf9b-4674-877d-dcd24dfd237f"/>
				</line>
				<line>
					<reportElement x="0" y="335" width="802" height="1" uuid="c3c04a52-fb87-49af-a180-4a88c77a332a"/>
				</line>
				<line>
					<reportElement x="0" y="352" width="802" height="1" uuid="4a5fc3ed-642a-41a3-9981-d0135324985c"/>
				</line>
				<line>
					<reportElement x="0" y="369" width="802" height="1" uuid="adb4b047-56e0-4ca9-957b-735e50a5ca2f"/>
				</line>
				<line>
					<reportElement stretchType="RelativeToBandHeight" x="368" y="1" width="1" height="28" uuid="eb64973a-aa71-43a3-b23d-a02b670e0cc6"/>
				</line>
				<line>
					<reportElement x="368" y="30" width="1" height="340" uuid="5594bb28-2e23-4d09-a899-2f208e82f12c"/>
				</line>
			</band>
			<band height="370">
				<printWhenExpression><![CDATA[!$P{IMPRIMIR_ENDERECO}]]></printWhenExpression>
				<textField>
					<reportElement x="759" y="2" width="40" height="11" uuid="52e3e568-0934-4fb4-b170-08888800c266"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{FA_COUNT}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="716" y="2" width="40" height="11" uuid="ab94d171-1cc3-493a-91de-422e9681e213"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="219" y="30" width="1" height="339" uuid="b19bdccc-33fb-42ce-a134-4ddc1e74fb62"/>
				</line>
				<line>
					<reportElement x="0" y="46" width="802" height="1" uuid="9fd10bc2-0ca4-4d87-8eff-1c56198f8064"/>
				</line>
				<line>
					<reportElement x="0" y="63" width="802" height="1" uuid="32ca32b1-8b6d-4f14-943e-566dda970aaf"/>
				</line>
				<line>
					<reportElement x="0" y="29" width="802" height="1" uuid="d58f204e-6531-44af-9b21-aa2437caf98d"/>
				</line>
				<line>
					<reportElement x="0" y="80" width="802" height="1" uuid="f0817ba7-5542-4e13-bf49-02b8793d89e9"/>
				</line>
				<line>
					<reportElement x="0" y="97" width="802" height="1" uuid="f52fb496-283e-4ece-8cc3-e5e32bb8efa5"/>
				</line>
				<line>
					<reportElement x="0" y="131" width="802" height="1" uuid="474dae7b-ccac-4493-bf4b-6c06bbc32e3a"/>
				</line>
				<line>
					<reportElement x="0" y="114" width="802" height="1" uuid="8e59e175-86f5-41fe-b1cc-13d76e68ab28"/>
				</line>
				<line>
					<reportElement x="0" y="148" width="802" height="1" uuid="77e2361c-0c89-4702-935b-043e36c8a6d8"/>
				</line>
				<line>
					<reportElement x="0" y="165" width="802" height="1" uuid="e2430750-e5c9-4817-b76d-6bfc73364a11"/>
				</line>
				<line>
					<reportElement x="0" y="182" width="802" height="1" uuid="053a661f-9bc5-4fa8-8428-1f23474b179c"/>
				</line>
				<line>
					<reportElement x="0" y="199" width="802" height="1" uuid="a583a59c-d1e4-4f20-8d52-6f1a471d2f8e"/>
				</line>
				<line>
					<reportElement x="0" y="0" width="802" height="1" uuid="14e1e8ee-a786-4332-a674-085e4e7075bb"/>
				</line>
				<line>
					<reportElement x="86" y="30" width="1" height="339" uuid="2f5bb9b0-ecd6-4134-b3ed-720f2d5cdab4"/>
				</line>
				<line>
					<reportElement x="459" y="29" width="1" height="339" uuid="1aa35411-2f9d-408b-b324-b75400ba1662"/>
				</line>
				<line>
					<reportElement x="601" y="29" width="1" height="339" uuid="ea10f2a0-729e-4004-8222-abf16a9b3215"/>
				</line>
				<line>
					<reportElement x="86" y="0" width="1" height="29" uuid="2053e918-bba6-4ca3-b783-f4458822dd31"/>
				</line>
				<line>
					<reportElement x="219" y="1" width="1" height="29" uuid="2053e918-bba6-4ca3-b783-f4458822dd31"/>
				</line>
				<line>
					<reportElement x="459" y="1" width="1" height="29" uuid="2053e918-bba6-4ca3-b783-f4458822dd31"/>
				</line>
				<line>
					<reportElement x="601" y="0" width="1" height="29" uuid="2053e918-bba6-4ca3-b783-f4458822dd31"/>
				</line>
				<line>
					<reportElement x="530" y="1" width="1" height="29" uuid="4b727eba-25e9-4639-bdc4-84b6024682c5"/>
				</line>
				<line>
					<reportElement x="530" y="29" width="1" height="339" uuid="483249a6-fa50-40ad-a152-c120717bc84d"/>
				</line>
				<line>
					<reportElement x="0" y="216" width="802" height="1" uuid="44754071-1c49-4b0f-84b8-24fcbc0c9bc9"/>
				</line>
				<line>
					<reportElement x="0" y="233" width="802" height="1" uuid="891350bd-e68b-4937-87b5-4e3513d83d0e"/>
				</line>
				<line>
					<reportElement x="0" y="250" width="802" height="1" uuid="0047ad9f-67a9-4bbb-939c-d13231f9ba9f"/>
				</line>
				<line>
					<reportElement x="0" y="267" width="802" height="1" uuid="f08d4288-b5d1-4122-896e-68ac5498e51a"/>
				</line>
				<line>
					<reportElement x="0" y="284" width="802" height="1" uuid="9e602f86-c6ae-4771-94e5-acbd8dd167ce"/>
				</line>
				<line>
					<reportElement x="0" y="301" width="802" height="1" uuid="3ca7dac2-1cc0-4a89-a4ac-544d17b564b2"/>
				</line>
				<line>
					<reportElement x="0" y="318" width="802" height="1" uuid="c2154d43-087a-41e9-9c20-0b85673c7dfd"/>
				</line>
				<line>
					<reportElement x="0" y="335" width="802" height="1" uuid="6b991f96-c7a5-4abd-a581-1eded1336bb8"/>
				</line>
				<line>
					<reportElement x="0" y="352" width="802" height="1" uuid="1e9e2ba5-8905-4fb0-bfe2-27f3a48729de"/>
				</line>
				<line>
					<reportElement x="0" y="369" width="802" height="1" uuid="f838891b-a8ab-4734-b81b-9fe59a85cb52"/>
				</line>
				<line>
					<reportElement x="295" y="30" width="1" height="339" uuid="fa527797-ce0d-4eda-aca5-9984d5088a31"/>
				</line>
				<line>
					<reportElement x="295" y="1" width="1" height="29" uuid="35117fd1-5fac-46c3-ba1c-9742b26bc13f"/>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="FA" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$P{PLANEJAMENTO_VISITA}.getCodigo()]]></groupExpression>
		<groupHeader>
			<band height="12">
				<printWhenExpression><![CDATA[$P{IMPRIMIR_ENDERECO}]]></printWhenExpression>
				<textField>
					<reportElement x="0" y="0" width="35" height="11" uuid="985826df-b42d-4bcf-9c57-0f7e63ed7560"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_familia")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="85" y="0" width="165" height="11" uuid="bbc19e09-5458-4cd3-9e87-a164c4143f53"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Rua*/
Bundle.getStringApplication("rotulo_rua")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="572" y="0" width="115" height="11" uuid="7ddaf32b-6025-4bc6-8916-e56f0049695c"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Motivo da Visita*/
Bundle.getStringApplication("rotulo_motivo_visita")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="439" y="0" width="62" height="11" uuid="a4c0356d-a7ca-4e0c-bc2b-b4f8ff1f54a9"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_visita")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="253" y="0" width="114" height="11" uuid="3bcf6a11-da2f-4ee9-b9d7-d195af997d9c"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Responsavel*/
Bundle.getStringApplication("rotulo_responsavel")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="690" y="0" width="110" height="11" uuid="bed951bd-5f6a-45e5-8dad-a0f49f48a32d"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_assinatura")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="688" y="0" width="1" height="12" uuid="2f23dc84-f6da-4e5b-a5f4-f33e81324d6d"/>
				</line>
				<line>
					<reportElement x="570" y="0" width="1" height="12" uuid="136e00e4-8729-4f6c-9cea-c92ddb43ebbf"/>
				</line>
				<line>
					<reportElement x="368" y="0" width="1" height="12" uuid="59aae79e-059e-425d-aeb9-dc7dfa50d062"/>
				</line>
				<textField>
					<reportElement x="35" y="0" width="50" height="11" uuid="acc8bd46-47b5-41e7-b6fb-833b18e6f51c"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_domicilio")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="251" y="0" width="1" height="12" uuid="7a6cdcca-da4a-4e97-b624-8a13831d273f"/>
				</line>
				<textField>
					<reportElement x="504" y="0" width="65" height="11" uuid="3dc279de-33bb-4e9f-a07f-e7fe48e57e20"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_turno_visita")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="502" y="0" width="1" height="12" uuid="c5e98b2c-6d71-49d5-ab36-f92523b72fdb"/>
				</line>
				<textField>
					<reportElement x="370" y="0" width="66" height="11" uuid="ebcf38c6-e418-4372-9ba2-2410177ba8ec"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_telefone")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement stretchType="RelativeToBandHeight" x="437" y="0" width="1" height="12" uuid="52615983-5c38-4590-88dc-482f0f4e399c"/>
				</line>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<detail>
		<band height="17" splitType="Stretch">
			<printWhenExpression><![CDATA[$P{IMPRIMIR_ENDERECO}]]></printWhenExpression>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="3" width="35" height="11" uuid="c2f57e7f-b180-4855-9012-7fa5d4f14afa"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{enderecoDomicilio}.getNumeroFamilia()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="85" y="3" width="165" height="11" uuid="0304db16-c378-43e7-8139-ac3751cbacb7"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{enderecoDomicilio}.getEnderecoUsuarioCadsus().getRuaFormatadaComComplemento()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement stretchType="RelativeToBandHeight" x="368" y="0" width="1" height="17" uuid="233d7ae9-d4ef-4071-b4d7-8d727d61862a"/>
			</line>
			<line>
				<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="570" y="0" width="1" height="17" uuid="9b55df5b-e480-478c-8e35-5fda1ca7f958"/>
			</line>
			<line>
				<reportElement stretchType="RelativeToBandHeight" x="688" y="0" width="1" height="17" uuid="f35db658-0684-439b-a4f1-926a4aa2fc5b"/>
			</line>
			<line>
				<reportElement x="0" y="0" width="802" height="1" uuid="fef5e112-1233-4a99-8f58-a7e37190258c"/>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement x="35" y="3" width="50" height="11" uuid="e7093569-50f0-43d1-a7b1-302fadb52acb"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{enderecoDomicilio}.getCodigo()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement stretchType="RelativeToBandHeight" x="251" y="0" width="1" height="17" uuid="b2069ceb-cc53-467b-bc3b-35dd6330eaf4"/>
			</line>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="253" y="3" width="114" height="11" uuid="511887f0-19b5-4ece-b69f-cb52db4b2f98"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nomeResponsavel}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement stretchType="RelativeToBandHeight" x="502" y="0" width="1" height="17" uuid="a7548155-8963-4a01-938a-b2986e3dbff0"/>
			</line>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="370" y="3" width="66" height="11" uuid="d921c3be-95f6-411d-8cf5-8116357119f5"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{telefoneResponsavel}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement stretchType="RelativeToBandHeight" x="437" y="0" width="1" height="17" uuid="ea79af42-4ae9-46ac-ac1f-884fc8c9b7dd"/>
			</line>
		</band>
		<band height="18">
			<printWhenExpression><![CDATA[!$P{IMPRIMIR_ENDERECO}]]></printWhenExpression>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="3" width="35" height="11" uuid="702ed930-ae56-48dd-b8f3-697dd77774a8"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{enderecoDomicilio}.getNumeroFamilia()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="35" y="3" width="50" height="11" uuid="33690a5a-4f4e-4945-b087-587e7c5212cc"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{enderecoDomicilio}.getCodigo()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="88" y="3" width="130" height="11" uuid="faa34eb7-e899-468c-b307-9eb863f48968"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nomeResponsavel}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="0" width="802" height="1" uuid="dcfc382a-2bcb-4949-b88e-97dc1bfb11f4"/>
			</line>
			<line>
				<reportElement stretchType="RelativeToBandHeight" x="86" y="1" width="1" height="17" uuid="10f317b4-c3c8-470c-b632-87f38df9f8c1"/>
			</line>
			<line>
				<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="459" y="1" width="1" height="17" uuid="23cd9ac0-0002-4706-957a-3839b35f0fcc"/>
			</line>
			<line>
				<reportElement stretchType="RelativeToBandHeight" x="219" y="1" width="1" height="17" uuid="202890b2-8e36-42e2-90f3-078f84541412"/>
			</line>
			<line>
				<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="601" y="1" width="1" height="17" uuid="c6d2c8ea-6d90-4672-bc40-787875dabb4c"/>
			</line>
			<line>
				<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="530" y="0" width="1" height="17" uuid="4076d638-73b3-4ecd-ad91-74de2596301d"/>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement x="221" y="3" width="72" height="11" uuid="451d6b7a-7419-47bf-a69b-6b65adaadc36"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{telefoneResponsavel}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement stretchType="RelativeToBandHeight" x="295" y="1" width="1" height="17" uuid="488b88ae-b0aa-4618-b491-3c0a9da6a528"/>
			</line>
		</band>
		<band height="26">
			<printWhenExpression><![CDATA[$P{IMPRIMIR_DOENCA} && !$F{lstUsuarioCadsusEsus}.isEmpty() && !$P{IMPRIMIR_ENDERECO}]]></printWhenExpression>
			<textField>
				<reportElement x="441" y="1" width="359" height="11" uuid="7c8b05f2-9dab-4f2d-afe7-124a3b5bf323"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_condicao")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="95" y="1" width="266" height="11" uuid="469f8e12-263c-461e-b042-6de46999b2c3"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_componente")]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement x="95" y="12" width="707" height="13" uuid="07cf9af7-9ab2-45ea-bc1d-ddb5c1568581"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="membros" uuid="08907c1f-aea4-47e0-8072-b8be84ad4698">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{lstUsuarioCadsusEsus})]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="13" width="707">
						<textField isBlankWhenNull="true">
							<reportElement x="0" y="1" width="266" height="11" uuid="5aac2c73-8118-4ac6-8f24-0b9358472d48"/>
							<textElement>
								<font fontName="Arial" size="8"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome()]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement x="346" y="0" width="359" height="11" uuid="1d76cd7e-cacc-4f6e-924a-94dd9d8fb6f3"/>
							<textElement>
								<font fontName="Arial" size="8"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{descricaoCondicao}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement x="272" y="0" width="70" height="11" uuid="91273822-242b-4b2b-96f3-e54f147045f8"/>
							<textElement textAlignment="Right">
								<font fontName="Arial" size="8"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{usuarioCadsus}.getIdade()]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
			<line>
				<reportElement x="0" y="0" width="802" height="1" uuid="2d2fbd77-739e-435f-be65-35e7110f57b9"/>
			</line>
			<line>
				<reportElement stretchType="RelativeToTallestObject" x="86" y="0" width="1" height="26" uuid="10f317b4-c3c8-470c-b632-87f38df9f8c1"/>
			</line>
			<textField>
				<reportElement x="367" y="1" width="70" height="11" uuid="948b449e-22b4-4a80-b32d-4ef1f0335053"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_idade")]]></textFieldExpression>
			</textField>
		</band>
		<band height="26">
			<printWhenExpression><![CDATA[$P{IMPRIMIR_DOENCA} && !$F{lstUsuarioCadsusEsus}.isEmpty() && $P{IMPRIMIR_ENDERECO}]]></printWhenExpression>
			<textField>
				<reportElement x="253" y="1" width="233" height="11" uuid="469f8e12-263c-461e-b042-6de46999b2c3"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_componente")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="572" y="1" width="230" height="11" uuid="7c8b05f2-9dab-4f2d-afe7-124a3b5bf323"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_condicao")]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement x="253" y="12" width="549" height="13" uuid="40e7a062-f3b1-4278-8a1e-8042d18ed52d"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="membros" uuid="52b87b98-bcf2-4000-88c5-a34f92ea5c98">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{lstUsuarioCadsusEsus})]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="13" width="549">
						<textField isBlankWhenNull="true">
							<reportElement x="0" y="1" width="233" height="11" uuid="5aac2c73-8118-4ac6-8f24-0b9358472d48"/>
							<textElement>
								<font fontName="Arial" size="8"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome()]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement x="319" y="1" width="230" height="11" uuid="1d76cd7e-cacc-4f6e-924a-94dd9d8fb6f3"/>
							<textElement>
								<font fontName="Arial" size="8"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{descricaoCondicao}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement x="233" y="1" width="85" height="11" uuid="4ad42b4a-1b50-4b93-8560-d93a041ab1f8"/>
							<textElement>
								<font fontName="Arial" size="8"/>
							</textElement>
							<textFieldExpression><![CDATA[UsuarioCadsusHelper.getPatientPhone($F{usuarioCadsus})]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
			<line>
				<reportElement stretchType="RelativeToTallestObject" x="251" y="0" width="1" height="26" uuid="5010f486-dff4-42c0-b1d6-ecb4b207ae73"/>
			</line>
			<line>
				<reportElement x="0" y="0" width="802" height="1" uuid="05577797-917e-4a39-bf2e-867f3f70653b"/>
			</line>
			<textField>
				<reportElement x="486" y="1" width="85" height="11" uuid="7f45adcd-5d5d-4f2a-af0f-e38f11d585bb"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_telefone")]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
