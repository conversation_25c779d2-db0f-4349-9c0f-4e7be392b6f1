<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_detalhamento_pagamentos" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="51e5d1c3-b8e0-4efc-ae93-bf6f690b0c0b">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.7715610000000026"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.report.ReportProperties"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="agruparConsorciado" class="java.lang.Boolean"/>
	<parameter name="UTILIZA_DESCONTO_GLOSA" class="java.lang.String"/>
	<field name="empresaPrestador" class="br.com.ksisolucoes.vo.basico.Empresa">
		<fieldDescription><![CDATA[pagamentoGuiaProcedimento.consorcioPrestador.empresaPrestador]]></fieldDescription>
	</field>
	<field name="dataPagamento" class="java.util.Date">
		<fieldDescription><![CDATA[pagamentoGuiaProcedimento.dataPagamento]]></fieldDescription>
	</field>
	<field name="chavePagamento" class="java.lang.Long">
		<fieldDescription><![CDATA[pagamentoGuiaProcedimento.chave]]></fieldDescription>
	</field>
	<field name="documento" class="java.lang.String">
		<fieldDescription><![CDATA[pagamentoGuiaProcedimento.documento]]></fieldDescription>
	</field>
	<field name="valorPagamento" class="java.lang.Double"/>
	<field name="valorTotalImpostos" class="java.lang.Double"/>
	<field name="valorDescontoGlosa" class="java.lang.Double"/>
	<field name="consorciado" class="br.com.ksisolucoes.vo.basico.Empresa">
		<fieldDescription><![CDATA[consorcioGuiaProcedimento.subConta.conta.consorciado]]></fieldDescription>
	</field>
	<field name="numeroGuia" class="java.lang.Long">
		<fieldDescription><![CDATA[consorcioGuiaProcedimento.codigo]]></fieldDescription>
	</field>
	<field name="pagamentoGuiaProcedimento" class="br.com.ksisolucoes.vo.consorcio.PagamentoGuiaProcedimento"/>
	<field name="consorcioGuiaProcedimento" class="br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento"/>
	<field name="tipoConta" class="br.com.ksisolucoes.vo.consorcio.TipoConta">
		<fieldDescription><![CDATA[consorcioGuiaProcedimento.subConta.tipoConta]]></fieldDescription>
	</field>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="subTotalImposto" class="java.lang.Double" resetType="Group" resetGroup="Consorciado" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotalImpostos}]]></variableExpression>
	</variable>
	<variable name="subTotalPagamento" class="java.lang.Double" resetType="Group" resetGroup="Consorciado" calculation="Sum">
		<variableExpression><![CDATA[$F{valorPagamento}]]></variableExpression>
	</variable>
	<variable name="subTotalDescontoGlosa" class="java.lang.Double" resetType="Group" resetGroup="Consorciado" calculation="Sum">
		<variableExpression><![CDATA[$F{valorDescontoGlosa}]]></variableExpression>
	</variable>
	<variable name="totalImposto" class="java.lang.Double" resetType="Group" resetGroup="Pagamento" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotalImpostos}]]></variableExpression>
	</variable>
	<variable name="totalPagamento" class="java.lang.Double" resetType="Group" resetGroup="Pagamento" calculation="Sum">
		<variableExpression><![CDATA[$F{valorPagamento}]]></variableExpression>
	</variable>
	<variable name="totalDescontoGlosa" class="java.lang.Double" resetType="Group" resetGroup="Pagamento" calculation="Sum">
		<variableExpression><![CDATA[$F{valorDescontoGlosa}]]></variableExpression>
	</variable>
	<variable name="totalGeralImposto" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotalImpostos}]]></variableExpression>
	</variable>
	<variable name="totalGeralPagamento" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{valorPagamento}]]></variableExpression>
	</variable>
	<variable name="totalGeralDescontoGlosa" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{valorDescontoGlosa}]]></variableExpression>
	</variable>
	<variable name="nGuiasConsorciado" class="java.lang.Long" resetType="Group" resetGroup="Consorciado" calculation="Count">
		<variableExpression><![CDATA[$F{numeroGuia}]]></variableExpression>
	</variable>
	<variable name="nGuiasPagamento" class="java.lang.Long" resetType="Group" resetGroup="Pagamento" calculation="Count">
		<variableExpression><![CDATA[$F{numeroGuia}]]></variableExpression>
	</variable>
	<group name="Pagamento">
		<groupExpression><![CDATA[$F{chavePagamento}]]></groupExpression>
		<groupHeader>
			<band height="93">
				<rectangle radius="10">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="7" width="802" height="83" isRemoveLineWhenBlank="true" uuid="f9fca057-8381-4ca9-a3fd-47ed0e0119e4">
						<printWhenExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getBancoPrestador().getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<rectangle radius="10">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="7" width="802" height="53" isRemoveLineWhenBlank="true" uuid="20901737-719e-44b0-a61a-7a5c286c39fc">
						<printWhenExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getBancoPrestador().getCodigo() == null]]></printWhenExpression>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<rectangle radius="10">
					<reportElement mode="Transparent" x="3" y="36" width="795" height="20" uuid="3bf6f3dc-224a-4b0f-91f7-627d6af558fb"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="389" y="40" width="56" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="8d25b333-5e93-4f83-9b7a-58daf310f819"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_iss_porcento")+": "]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="85" y="40" width="45" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="c9fabd33-5375-40fe-a05a-b7533de429ca"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getValorTotalBruto()]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="446" y="40" width="41" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="f799506b-bc27-44f3-abed-5f060056eabe"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getValorTotalImpostoIss()]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="131" y="40" width="118" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="3fe5f369-eba5-4222-a391-fb0ce7a1547c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_imposto_renda_porcento")+": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="286" y="40" width="61" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="3efef65f-06b9-4713-a218-15e4c7e95f91"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_inss_porcento")+": "]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="249" y="40" width="37" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="f685b8ba-5c1d-48ad-8041-ac15ac1b4269"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getValorTotalImpostoRenda()]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="564" y="40" width="93" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="19040509-f4e5-447a-8004-6af9913ff902"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_valor_pagamento")+": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="55" y="15" width="357" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="83dd9e0d-2da5-4270-8f8f-c58020585fab"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{empresaPrestador}.getDescricao()]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="347" y="40" width="42" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="c629719c-a339-490d-a6b6-1be3dbe34e26"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getValorTotalImpostoInss()]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="457" y="15" width="55" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="45024440-d161-4fb0-a5db-551d0f4f22c9"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_documento")+": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="512" y="15" width="50" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="51be5bad-1663-400d-ad61-815cbfaf5d42"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{documento}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="4" y="15" width="50" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="3c0aae83-c1bd-45fc-82b8-fc0cc56d07d7"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_prestador")+": "]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="657" y="40" width="57" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="75c427f2-2ebb-4765-b5d2-ae2226b06301"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getValorTotalPagamento()]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Opaque" x="15" y="0" width="155" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="ba6425a2-c7a2-477a-9a93-1454fbe81d10"/>
					<box leftPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_pagamento")+": "+$F{chavePagamento}+" | "+Data.formatar($F{dataPagamento})]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Opaque" x="12" y="28" width="50" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="fc71930d-c7f3-4cd8-9ec1-943e4e849695"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_impostos")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="10" y="40" width="75" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="fdcb9283-952b-4517-8763-2f2f6f0803f7"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_valor_guias")+": "]]></textFieldExpression>
				</textField>
				<elementGroup>
					<rectangle radius="10">
						<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="3" y="66" width="795" height="20" isRemoveLineWhenBlank="true" uuid="2ecfa222-5393-49d3-99b4-6ce19429c8c8">
							<printWhenExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getBancoPrestador().getCodigo() != null]]></printWhenExpression>
						</reportElement>
						<graphicElement>
							<pen lineWidth="0.5"/>
						</graphicElement>
					</rectangle>
					<textField pattern="" isBlankWhenNull="true">
						<reportElement key="textField-45" mode="Transparent" x="244" y="71" width="42" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="5878e36e-c448-4b38-8769-c51bee0dd02b">
							<printWhenExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getBancoPrestador().getCodigo() != null]]></printWhenExpression>
						</reportElement>
						<box topPadding="1" leftPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
							<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
							<paragraph lineSpacing="Single"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_agencia")+": "]]></textFieldExpression>
					</textField>
					<textField pattern="" isBlankWhenNull="true">
						<reportElement key="textField-45" mode="Transparent" x="11" y="71" width="35" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="9bb4db0a-0938-403f-b498-10d52e41371c">
							<printWhenExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getBancoPrestador().getCodigo() != null]]></printWhenExpression>
						</reportElement>
						<box topPadding="1" leftPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
							<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
							<paragraph lineSpacing="Single"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_banco")+": "]]></textFieldExpression>
					</textField>
					<textField pattern="" isBlankWhenNull="true">
						<reportElement key="textField-45" mode="Transparent" x="698" y="71" width="95" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="cb590098-8f10-4c43-addd-097fa500dd6b">
							<printWhenExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getBancoPrestador().getCodigo() != null]]></printWhenExpression>
						</reportElement>
						<box topPadding="1" leftPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
							<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
							<paragraph lineSpacing="Single"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getCpfCnpjFormatado()]]></textFieldExpression>
					</textField>
					<textField pattern="" isBlankWhenNull="true">
						<reportElement key="textField-45" mode="Transparent" x="433" y="71" width="62" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="5ce02dab-533f-4f34-954a-a28c7a617771">
							<printWhenExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getBancoPrestador().getCodigo() != null]]></printWhenExpression>
						</reportElement>
						<box topPadding="1" leftPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
							<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
							<paragraph lineSpacing="Single"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_titular_conta")+": "]]></textFieldExpression>
					</textField>
					<textField pattern="" isBlankWhenNull="true">
						<reportElement key="textField-45" mode="Transparent" x="495" y="71" width="150" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="6decea80-da2e-499e-9699-4fe8aacc7434">
							<printWhenExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getBancoPrestador().getCodigo() != null]]></printWhenExpression>
						</reportElement>
						<box topPadding="1" leftPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
							<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
							<paragraph lineSpacing="Single"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getNomeTitularConta()]]></textFieldExpression>
					</textField>
					<textField pattern="" isBlankWhenNull="true">
						<reportElement key="textField-45" mode="Transparent" x="379" y="71" width="53" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="461e907a-5528-4bfb-9c8c-535aba60ca67">
							<printWhenExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getBancoPrestador().getCodigo() != null]]></printWhenExpression>
						</reportElement>
						<box topPadding="1" leftPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
							<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
							<paragraph lineSpacing="Single"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getNumeroConta()]]></textFieldExpression>
					</textField>
					<textField pattern="" isBlankWhenNull="true">
						<reportElement key="textField-45" mode="Transparent" x="286" y="71" width="45" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="5f9bc880-5d5f-4b58-88d5-5813faf4e658">
							<printWhenExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getBancoPrestador().getCodigo() != null]]></printWhenExpression>
						</reportElement>
						<box topPadding="1" leftPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
							<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
							<paragraph lineSpacing="Single"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getAgencia()]]></textFieldExpression>
					</textField>
					<textField pattern="" isBlankWhenNull="true">
						<reportElement key="textField-45" mode="Opaque" x="13" y="59" width="78" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="bde7cc0e-4392-4e83-9675-ffbbd194005f">
							<printWhenExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getBancoPrestador().getCodigo() != null]]></printWhenExpression>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
							<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
							<paragraph lineSpacing="Single"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_bancarios")]]></textFieldExpression>
					</textField>
					<textField pattern="" isBlankWhenNull="true">
						<reportElement key="textField-45" mode="Transparent" x="648" y="71" width="50" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="1c74e180-21c8-4909-9eb9-175afd7444d8">
							<printWhenExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getBancoPrestador().getCodigo() != null]]></printWhenExpression>
						</reportElement>
						<box topPadding="1" leftPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
							<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
							<paragraph lineSpacing="Single"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cpf_cnpj")+": "]]></textFieldExpression>
					</textField>
					<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
						<reportElement key="textField-45" mode="Transparent" x="46" y="71" width="196" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="6fe86d4d-6346-4a8b-9073-10f7dc7f8c24">
							<printWhenExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getBancoPrestador().getCodigo() != null]]></printWhenExpression>
						</reportElement>
						<box topPadding="1" leftPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
							<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
							<paragraph lineSpacing="Single"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getBancoPrestador().getDescricaoFormatado()]]></textFieldExpression>
					</textField>
					<textField pattern="" isBlankWhenNull="true">
						<reportElement key="textField-45" mode="Transparent" x="331" y="71" width="48" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="bc9187d1-475e-4f6d-83cf-e904bbcfe1c1">
							<printWhenExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getBancoPrestador().getCodigo() != null]]></printWhenExpression>
						</reportElement>
						<box topPadding="1" leftPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
							<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
							<paragraph lineSpacing="Single"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_numero_conta")+": "]]></textFieldExpression>
					</textField>
				</elementGroup>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="487" y="40" width="35" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="9801a6bd-c938-47f9-b0cc-a91b75020349">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{UTILIZA_DESCONTO_GLOSA})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_glosa")+": "]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="522" y="40" width="42" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="85338876-fa45-4362-bfad-b1b56275158f">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{UTILIZA_DESCONTO_GLOSA})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{pagamentoGuiaProcedimento}.getValorTotalDescontoGlosa()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="15">
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="672" y="2" width="58" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="b42950fd-a7d8-46b2-8188-ce739d6e70cf"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalImposto}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="730" y="2" width="70" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="a5b23673-ebb3-4940-b716-d6f86618674a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalPagamento}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="614" y="2" width="58" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="2a738fd4-f8cc-4cb0-946b-e8332e89ec63"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalImposto} + $V{totalPagamento} + $V{totalDescontoGlosa}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="503" y="2" width="111" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="78dc376b-735d-41c6-b7da-b3ecde6f3366"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_pagamento")+":"]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="331" y="1" width="469" height="1" uuid="638a11c0-3a30-4144-a5ae-4b7c4ad4e6c4"/>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="331" y="2" width="135" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="05542cf7-f452-4885-bdfe-518db0d84a29"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_numero_guias_pagamento")+":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="466" y="2" width="27" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="d4612b93-fe72-417b-a25a-d82b507aba00"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{nGuiasPagamento}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="Consorciado">
		<groupExpression><![CDATA[$P{agruparConsorciado}
?
    $F{consorciado}.getDescricao()
:
    null]]></groupExpression>
		<groupHeader>
			<band height="17">
				<printWhenExpression><![CDATA[$P{agruparConsorciado}]]></printWhenExpression>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="0" y="1" width="614" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="474da9ac-12f6-4959-b7b4-e9c923ca5000"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="true" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_consorciado")+": "+$F{consorciado}.getDescricao()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="14">
				<printWhenExpression><![CDATA[$P{agruparConsorciado}]]></printWhenExpression>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="672" y="2" width="58" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="80ef75b7-362d-4eb3-8b74-35de0d9b2d38"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{subTotalImposto}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="730" y="2" width="70" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="cfce3952-db96-4aa0-ac4d-add7cb18bd97"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{subTotalPagamento}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="614" y="2" width="58" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="c8e91fef-17c9-4071-8d2a-fcb58cfcfdfc"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{subTotalImposto} + $V{subTotalPagamento} + $V{subTotalDescontoGlosa}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="503" y="2" width="111" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="1baff6c5-7364-4337-8c75-6af2f48d8970"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_consorciado")+":"]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="331" y="1" width="469" height="1" uuid="9bb6cb96-e8a4-43db-9753-41b66c2cbedc"/>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="331" y="2" width="135" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="68a9089b-dec5-48f6-a57a-27106ef379a6"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_numero_guias_consorciado")+":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="466" y="2" width="27" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="17e61e6e-872d-451a-9f79-041ebadce55b"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{nGuiasConsorciado}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="CABECALHO" isReprintHeaderOnEachPage="true">
		<groupHeader>
			<band height="16">
				<line>
					<reportElement x="0" y="14" width="802" height="1" uuid="72cbb25d-99c9-4fb7-b8e1-71cf55d9a8da"/>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="614" y="2" width="58" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="36be133d-d6a6-402b-8ba9-e4a50eb8b859"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_vl_guia")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="3" y="2" width="55" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="9b1b4b2b-c353-4ba4-b0b2-e64decf710eb"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_cadastro")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="672" y="2" width="58" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="db63b7ca-2ef9-4d02-a802-65dd57319659"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_imposto")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="730" y="2" width="70" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="ec3eecd8-1963-4128-939a-2dcf0d5c6204"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_vl_pagamento")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="125" y="2" width="160" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="539da854-35f9-487a-9807-341a741245d0"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_consorciado")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="63" y="2" width="55" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="215fb643-f9c0-4705-be9b-7289599719e6"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_n_guia")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="447" y="2" width="160" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="3235f174-4ef0-4737-a46e-067e234248ec"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome_paciente")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="286" y="2" width="160" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="c77038b4-1a13-4f7d-93fb-f8ad8afb7101"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tipo_conta")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="12" splitType="Stretch">
			<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="614" y="0" width="58" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="bfa46749-3e71-4c6c-9601-bc9f09e2103c"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{valorTotalImpostos} + $F{valorPagamento} + Coalesce.asDouble($F{valorDescontoGlosa})]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="3" y="0" width="55" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="296d3444-b63d-4e29-87da-3995b3016ff0"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{consorcioGuiaProcedimento}.getDataCadastro()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="672" y="0" width="58" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="b9c178cd-b255-4d69-8d39-e81f7fb9f8bb"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{valorTotalImpostos}]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="730" y="0" width="70" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="7c9c703c-879c-4c8d-a383-f6e867da60b2"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{valorPagamento}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="125" y="0" width="160" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="fdcc3a1c-00bc-4163-9a8a-bf0274ede60c"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{consorciado}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="63" y="0" width="55" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="1b27a6b5-769d-4cb1-ba98-c7262f38b208"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{numeroGuia}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="447" y="0" width="160" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="99d57362-83c1-4a70-8ced-b130ec48a166"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{consorcioGuiaProcedimento}.getNomePaciente()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="286" y="0" width="160" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="a4147abb-0669-42f4-a385-be3848a04607"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipoConta}.getDescricao()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
