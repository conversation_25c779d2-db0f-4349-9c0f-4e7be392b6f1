<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="sub_relatorio_perfil_usuarios_atendidos_micro_area_hospital" columnCount="4" printOrder="Horizontal" pageWidth="535" pageHeight="842" columnWidth="133" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="5372adf0-287f-4153-87dd-cfcf56c800b3">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="4.515791440430219"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="titulo" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="descricao" class="java.lang.String"/>
	<field name="quantidadeDouble" class="java.lang.Double"/>
	<field name="totalDouble" class="java.lang.Double"/>
	<variable name="quantidadeDoubleSum" class="java.lang.Double" resetType="Column" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidadeDouble}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="12" splitType="Stretch">
			<textField>
				<reportElement uuid="35a50afc-ddc0-4cc1-9dbe-80c952bf5724" x="3" y="0" width="71" height="11"/>
				<textElement>
					<font size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_m_area")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="c7c1f55c-ce57-4e18-bc99-d986e1d6b8db" x="74" y="0" width="29" height="11"/>
				<textElement textAlignment="Right">
					<font size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quantidade_abv")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement uuid="f56757ec-aa36-465d-a074-b4db2294466e" x="0" y="11" width="133" height="1"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<line>
				<reportElement uuid="48f254a8-9ab6-4d22-af2f-4e8e318a747b" x="133" y="0" width="1" height="12">
					<printWhenExpression><![CDATA[$V{COLUMN_NUMBER}!=4]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField>
				<reportElement uuid="9b000c96-4630-4425-a455-b261140663f6" x="106" y="0" width="26" height="11"/>
				<textElement textAlignment="Right">
					<font size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["%"]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="10" splitType="Stretch">
			<textField>
				<reportElement uuid="f4da9132-ddf4-4b8d-a36a-1bbb6c79d6c9" x="3" y="0" width="71" height="10"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
			<textField pattern="0">
				<reportElement uuid="fc3fa4e7-8d52-495f-a117-83dac7897490" x="74" y="0" width="29" height="10"/>
				<textElement textAlignment="Right">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidadeDouble}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement uuid="30d8ab7a-09eb-4771-a3b1-a76dbfcf864a" x="133" y="0" width="1" height="10">
					<printWhenExpression><![CDATA[$V{COLUMN_NUMBER}!=4]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField pattern="###0.00;-###0.00">
				<reportElement uuid="17ac0f25-35ea-463c-9bb1-e5d2b963fd8b" x="106" y="0" width="26" height="10"/>
				<textElement textAlignment="Right">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{quantidadeDouble}*100)/$F{totalDouble}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="10" splitType="Stretch">
			<line>
				<reportElement uuid="56fa4ca8-b3ac-4bb2-b2c7-729b83f26449" x="133" y="0" width="1" height="10">
					<printWhenExpression><![CDATA[$V{COLUMN_NUMBER}!=4]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField pattern="###0.00;-###0.00">
				<reportElement uuid="2ca36589-74ba-4a06-a100-641d8500e00b" x="106" y="0" width="26" height="10">
					<printWhenExpression><![CDATA[$V{COLUMN_NUMBER}==4]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[($V{quantidadeDoubleSum}*100)/$F{totalDouble}]]></textFieldExpression>
			</textField>
			<textField pattern="###0">
				<reportElement uuid="f7cb4ea4-a1e0-4277-9bc6-819083be57c5" x="74" y="0" width="29" height="10">
					<printWhenExpression><![CDATA[$V{COLUMN_NUMBER}==4]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{quantidadeDoubleSum}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6a050178-9625-45ee-8a15-b61c719a791e" x="3" y="0" width="71" height="10">
					<printWhenExpression><![CDATA[$V{COLUMN_NUMBER}==4]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
			</textField>
		</band>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
