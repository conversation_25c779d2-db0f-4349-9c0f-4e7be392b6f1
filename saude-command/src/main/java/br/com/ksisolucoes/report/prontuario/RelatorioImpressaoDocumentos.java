/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.prontuario;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.query.QueryRelatorioImpressaoDocumentos;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoDocumentos extends AbstractReport<List<Long>>{

    private boolean papelTimbrado;

    public RelatorioImpressaoDocumentos(List<Long> param, boolean papelTimbrado) {
        super(param);
        this.papelTimbrado = papelTimbrado;
    }

    @Override
    public String getXML() {
        if(papelTimbrado){
            addParametro("EXIBIR_CABECALHO", Boolean.FALSE);
            addParametro("EXIBIR_RODAPE", Boolean.FALSE);
        }
        return "/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_documentos.jrxml";
    }

    @Override
    public String getTitulo() {
        return "";
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioImpressaoDocumentos();
    }

    @Override
    protected void customDTOParam(List<Long> param) throws ValidacaoException {
        if (param == null || param.isEmpty()) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_e_necessario_informar_parametros_para_a_emissao_relatorio"));
        }
    }

}

