package br.com.ksisolucoes.report.prontuario.basico.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioImprimirComprovanteAgendamentoDTO;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioImprimirComprovanteAgendamentoDTOParam;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.TipoDocumentoUsuario;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioImprimirComprovanteAgendamento extends CommandQuery<QueryRelatorioImprimirComprovanteAgendamento> implements ITransferDataReport<RelatorioImprimirComprovanteAgendamentoDTOParam, RelatorioImprimirComprovanteAgendamentoDTO> {

    private RelatorioImprimirComprovanteAgendamentoDTOParam param;
    private List<RelatorioImprimirComprovanteAgendamentoDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("agendaGradeAtendimentoHorario", new HQLProperties(AgendaGradeAtendimentoHorario.class, "agendaGradeAtendimentoHorario").getProperties());
        hql.addToSelect("agendaGradeAtendimentoHorario", new HQLProperties(SolicitacaoAgendamento.class, "agendaGradeAtendimentoHorario.solicitacaoAgendamento").getProperties());
        hql.addToSelect("agendaGradeAtendimentoHorario", new HQLProperties(Empresa.class, "agendaGradeAtendimentoHorario.localAgendamento").getProperties());
        hql.addToSelect("agendaGradeAtendimentoHorario.localAgendamento.cidade.estado.sigla", "agendaGradeAtendimentoHorario.localAgendamento.cidade.estado.sigla");
        hql.addToSelect("agendaGradeAtendimentoHorario.empresaOrigem.cnes", "agendaGradeAtendimentoHorario.empresaOrigem.cnes");
        hql.addToSelect("agendaGradeAtendimentoHorario.agendaGradeAtendimento.agendaGrade.agenda.recomendacoes", "agendaGradeAtendimentoHorario.agendaGradeAtendimento.agendaGrade.agenda.recomendacoes");
        hql.addToSelect("agendaGradeAtendimentoHorario.profissional.codigo", "agendaGradeAtendimentoHorario.profissional.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.profissional.referencia", "agendaGradeAtendimentoHorario.profissional.referencia");
        hql.addToSelect("agendaGradeAtendimentoHorario.profissional.nome", "agendaGradeAtendimentoHorario.profissional.nome");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.usuarioCadsus.nomeMae", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.usuarioCadsus.nomeMae");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.usuarioCadsus.dataNascimento", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.usuarioCadsus.dataNascimento");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.usuarioCadsus.sexo", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.usuarioCadsus.sexo");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.tipoProcedimento.codigo", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.tipoProcedimento.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.tipoProcedimento.descricao", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.tipoProcedimento.descricao");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.tipoProcedimento.flagFichaCadastro", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.tipoProcedimento.flagFichaCadastro");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.tipoProcedimento.tipoProcedimentoClassificacao.codigo", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.tipoProcedimento.tipoProcedimentoClassificacao.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.tipoProcedimento.tipoProcedimentoClassificacao.descricao", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.tipoProcedimento.tipoProcedimentoClassificacao.descricao");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.dataNascimento", "agendaGradeAtendimentoHorario.usuarioCadsus.dataNascimento");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.nomeMae", "agendaGradeAtendimentoHorario.usuarioCadsus.nomeMae");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.telefone", "agendaGradeAtendimentoHorario.usuarioCadsus.telefone");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.celular", "agendaGradeAtendimentoHorario.usuarioCadsus.celular");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.sexo", "agendaGradeAtendimentoHorario.usuarioCadsus.sexo");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.cpf", "agendaGradeAtendimentoHorario.usuarioCadsus.cpf");

        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.codigo", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.numeroLogradouro", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.numeroLogradouro");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.logradouro", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.bairro", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.bairro");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.cep", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.cep");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.complementoLogradouro", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.complementoLogradouro");

        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.cidade.codigo", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.cidade.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.cidade.descricao", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.cidade.descricao");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.cidade.estado.codigo", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.cidade.estado.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.cidade.estado.sigla", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.cidade.estado.sigla");

        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.tipoLogradouro.codigo", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.tipoLogradouro.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.tipoLogradouro.descricao", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.tipoLogradouro.descricao");

        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.empresa.codigo", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.empresa.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.empresa.descricao", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.empresa.descricao");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.empresa.sigla", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.empresa.sigla");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.empresa.cnes", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.empresa.cnes");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.procedimento.codigo", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.procedimento.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.procedimento.descricao", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.procedimento.descricao");

        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.codigo", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.numeroFamilia", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.numeroFamilia");

        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.codigo", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.numeroLogradouro", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.numeroLogradouro");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.logradouro", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.bairro", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.bairro");

        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.tipoLogradouro.codigo", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.tipoLogradouro.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.tipoLogradouro.descricao", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.tipoLogradouro.descricao");

        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.equipeMicroArea.codigo", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.equipeMicroArea.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.equipeMicroArea.microArea", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.equipeMicroArea.microArea");
//        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.equipeMicroArea.equipeArea.codigo", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.equipeMicroArea.equipeArea.codigo");
//        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.equipeMicroArea.equipeArea.descricao", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.equipeMicroArea.equipeArea.descricao");
//        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.equipeMicroArea.equipeArea.codigoArea", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.equipeMicroArea.equipeArea.codigoArea");

        hql.addToSelect("(select min(ucc.numeroCartao) from UsuarioCadsusCns ucc where ucc.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and ucc.excluido = 0 )", "usuarioCadsusCns.numeroCartao");
        hql.addToSelect("(select ucd.numeroDocumento from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_RG + " and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoIdent.numeroDocumento");
        hql.addToSelect("(select ucd.numeroDocumentoComplementar from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_RG + " and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoIdent.numeroDocumentoComplementar");
        hql.addToSelect("(select ucd.siglaUf from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_RG + " and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoIdent.siglaUf");
        hql.addToSelect("(select ucd.dataEmissao from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_RG + " and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoIdent.dataEmissao");
        hql.addToSelect("(select ucd.orgaoEmissor.descricao from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_RG + " and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoIdent.orgaoEmissor.descricao");
        hql.addToSelect("(select ucd.numeroMatricula from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and (ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_CASAMENTO + " or ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO + ") and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoCert.numeroMatricula");
        hql.addToSelect("(select ucd.tipoDocumento.descricao from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and (ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_CASAMENTO + " or ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO + ") and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoCert.tipoDocumento.descricao");
        hql.addToSelect("(select ucd.numeroLivro from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and (ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_CASAMENTO + " or ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO + ") and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoCert.numeroLivro");
        hql.addToSelect("(select ucd.numeroFolha from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and (ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_CASAMENTO + " or ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO + ") and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoCert.numeroFolha");
        hql.addToSelect("(select ucd.numeroTermo from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and (ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_CASAMENTO + " or ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO + ") and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoCert.numeroTermo");
        hql.addToSelect("(select ucd.numeroCartorio from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and (ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_CASAMENTO + " or ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO + ") and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoCert.numeroCartorio");
        hql.addToSelect("(select ucd.dataEmissao from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and (ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_CASAMENTO + " or ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO + ") and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoCert.dataEmissao");
//        hql.addToSelect("(select preparacaoExame.descricao from PreparacaoProcedimento preparacaoProcedimento join preparacaoProcedimento.preparacaoExame preparacaoExame where preparacaoProcedimento.procedimento = agendaGradeAtendimentoHorario.solicitacaoAgendamento.procedimento)", "descricaoPreparacaoExame");
        hql.setConvertToLeftJoin(true);

        hql.setTypeSelect(RelatorioImprimirComprovanteAgendamentoDTO.class.getName());
        hql.addToFrom("AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario");

        hql.addToWhereWhithAnd("agendaGradeAtendimentoHorario = ", param.getAgendaGradeAtendimentoHorario());

        hql.addToWhereWhithAnd("agendaGradeAtendimentoHorario.solicitacaoAgendamento = ", param.getSolicitacaoAgendamento());

        if (param.getSolicitacaoAgendamento() != null && param.getAgendaGradeAtendimentoHorario() == null) {
            hql.addToWhereWhithAnd("agendaGradeAtendimentoHorario.status <> ", AgendaGradeAtendimentoHorario.STATUS_CANCELADO);
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioImprimirComprovanteAgendamentoDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelatorioImprimirComprovanteAgendamentoDTOParam param) {
        this.param = param;
    }

}
