package br.com.ksisolucoes.report.entrada.estoque;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.ImpressaoReciboLancamentoEstoqueDTO;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.ImpressaoReciboLancamentoEstoqueDTOParam;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoque;
import ch.lambdaj.Lambda;
import ch.lambdaj.function.compare.ArgumentComparator;
import org.apache.commons.collections.ComparatorUtils;

import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Created by sulivan on 13/11/17.
 */
public class RelatorioImpressaoReciboLancamentoEstoque extends AbstractReport<ImpressaoReciboLancamentoEstoqueDTOParam> implements ReportProperties {

    public RelatorioImpressaoReciboLancamentoEstoque(ImpressaoReciboLancamentoEstoqueDTOParam param) {
        super(param);
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_comprovante_entrega");
    }

    @Override
    public String getXML() {
//        String utilizaLocalizacaoEstoque = null;
//        try {
//            utilizaLocalizacaoEstoque = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("utilizaLocalizacaoEstoque");
//        } catch (DAOException ex) {
//            Logger.getLogger(RelatorioImpressaoReciboLancamentoEstoque.class.getName()).log(Level.SEVERE, null, ex);
//        }
//
//        addParametro("utilizaLocalizacaoEstoque", RepositoryComponentDefault.SIM.equals(utilizaLocalizacaoEstoque));

        return "/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_impressao_comprovante_entrega.jrxml";
    }

    @Override
    public Collection getCollection() throws DAOException, ValidacaoException {
        ImpressaoReciboLancamentoEstoqueDTO dto = new ImpressaoReciboLancamentoEstoqueDTO();

        MovimentoEstoque me = this.param.getMovimentoEstoqueList().get(0);

        dto.setTipoDocumento(me.getTipoDocumento().getDescricao());
        dto.setDataCadastro(me.getDataCadastro());
        dto.setNomeUsuarioCadastro(me.getUsuario().getNome());
        dto.setUnidadeOrigem(carregarEmpresa(me.getId().getEmpresa().getCodigo()));
        dto.setEntrada(me.getTipoDocumento().getFlagTipoMovimento());

        if(me.getEmpresaDestino() != null){
            dto.setUnidadeDestino(carregarEmpresa(me.getEmpresaDestino().getCodigo()));
        }

        final Comparator byDescricaoProduto = new ArgumentComparator(Lambda.on(MovimentoEstoque.class).getProduto().getDescricao());
        final Comparator byLocalizacao = new ArgumentComparator(Lambda.on(MovimentoEstoque.class).getLocalizacaoEstrutura().getMascara());

        List listSort = Lambda.sort(this.param.getMovimentoEstoqueList(), Lambda.on(MovimentoEstoque.class), ComparatorUtils.chainedComparator(Arrays.asList(byDescricaoProduto, byLocalizacao)));

        dto.setMovimentoEstoqueList(listSort);

        return Arrays.asList(dto);
    }

    private Empresa carregarEmpresa(Long codigoEmpresa){
        Empresa proxy = on(Empresa.class);

        return LoadManager.getInstance(Empresa.class)
                .addProperty(path(proxy.getCodigo()))
                .addProperty(path(proxy.getReferencia()))
                .addProperty(path(proxy.getDescricao()))
                .addProperty(path(proxy.getTelefone()))
                .addProperty(path(proxy.getRua()))
                .addProperty(path(proxy.getNumero()))
                .addProperty(path(proxy.getComplemento()))
                .addProperty(path(proxy.getBairro()))
                .addProperty(path(proxy.getCidade().getCodigo()))
                .addProperty(path(proxy.getCidade().getDescricao()))
                .addProperty(path(proxy.getCidade().getEstado().getCodigo()))
                .addProperty(path(proxy.getCidade().getEstado().getDescricao()))
                .setId(codigoEmpresa)
                .start().getVO();
    }
}