package br.com.ksisolucoes.report.programasaude;

import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.vo.programasaude.Hiperdia;
import java.util.Arrays;
import java.util.Collection;

/**
 * <AUTHOR>
 *
 */
public class RelatorioCadastroHiperdia extends AbstractReport {

    private Hiperdia hiperdia;

    public RelatorioCadastroHiperdia(Hiperdia hiperdia) {
        this.hiperdia = hiperdia;
    }

    @Override
    public Collection getCollection() {
        this.addParametro("caminhoLogo", br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp.getInstance().getReportSessaoAplicacao().getCaminhoImagemPadrao());
        
        return Arrays.asList(this.hiperdia);
    }

    public String getXML() {
        return "/br/com/ksisolucoes/report/programasaude/jrxml/relatorio_hiperdia.jrxml";
    }

    @Override
    public String getTitulo() {
        return "Colocar o titulo";
    }
}
