<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_procedimentos_detalhado" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="695487af-2d27-41ec-b51d-120a320256a2">
	<property name="ireport.zoom" value="1.9487171000000079"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.basico.CargaBasicoPadrao"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<import value="br.com.ksisolucoes.vo.basico.Empresa"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="FORMA_APRESENTACAO" class="java.lang.String"/>
	<parameter name="AGRUPAR_UNIDADE" class="java.lang.String"/>
	<queryString language="SQL">
		<![CDATA[]]>
	</queryString>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<field name="procedimento" class="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"/>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="tabelaCbo" class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"/>
	<field name="convenio" class="br.com.ksisolucoes.vo.prontuario.basico.Convenio"/>
	<field name="dataAtendimento" class="java.sql.Timestamp"/>
	<field name="quantidade" class="java.lang.Double"/>
	<field name="cidade" class="br.com.ksisolucoes.vo.basico.Cidade"/>
	<field name="empresaSolicitante" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="equipeProfissional" class="br.com.ksisolucoes.vo.basico.EquipeProfissional"/>
	<variable name="totalQtdFA" class="java.lang.Double" resetType="Group" resetGroup="fa" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<variable name="totalQtdGeral" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<group name="Empresa">
		<groupExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{AGRUPAR_UNIDADE})
    ?
        null
    :
        $F{empresa}]]></groupExpression>
		<groupHeader>
			<band height="15">
				<rectangle radius="10">
					<reportElement mode="Transparent" x="0" y="1" width="802" height="14" uuid="a1f483f5-a57a-4602-bf27-59c3a3d0a547"/>
				</rectangle>
				<textField>
					<reportElement x="0" y="1" width="802" height="13" uuid="2078e9f3-3872-45a4-8e31-da8d41c18a59"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" isBold="true" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{AGRUPAR_UNIDADE})
    ?
        Bundle.getStringApplication("rotulo_unidade") +": "+ Bundle.getStringApplication("rotulo_todas")
    :
        Bundle.getStringApplication("rotulo_unidade") +": "+ $F{empresa}.getDescricaoFormatado() + " - Cnes: " + Coalesce.asString($F{empresa}.getCnes(),"")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="default">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupFooter>
			<band height="15">
				<textField>
					<reportElement x="713" y="4" width="55" height="11" uuid="56c39162-4e35-4f50-88d4-d3933f1cf120"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Total*/
Bundle.getStringApplication("rotulo_total_geral")]]></textFieldExpression>
				</textField>
				<textField pattern="#########0.00">
					<reportElement x="769" y="4" width="33" height="11" uuid="a8887239-1a8d-4f69-898a-ae2ef6dc1301"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalQtdGeral}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="fa" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[Bundle.getStringApplication("rotulo_geral").equals($P{FORMA_APRESENTACAO})
?
    null
:
    (Bundle.getStringApplication("rotulo_profissional").equals($P{FORMA_APRESENTACAO})
    ?
        $F{profissional}
    :
        (Bundle.getStringApplication("rotulo_usuario_cadsus").equals($P{FORMA_APRESENTACAO})
        ?
            $F{usuarioCadsus}
        :
            (Bundle.getStringApplication("rotulo_cbo").equals($P{FORMA_APRESENTACAO})
            ?
                $F{tabelaCbo}.getCbo()!=null
                ?
                    $F{tabelaCbo}
                :
                    null
            :
                (Bundle.getStringApplication("rotulo_convenio").equals($P{FORMA_APRESENTACAO})
                ?
                    $F{convenio}
                :
                    (Bundle.getStringApplication("rotulo_procedimento").equals($P{FORMA_APRESENTACAO})
                    ?
                        $F{procedimento}
                    :
                        (Bundle.getStringApplication("rotulo_municipio").equals($P{FORMA_APRESENTACAO})
                                ?
                                    $F{cidade}.getCodigo()
                                :
                                	(Bundle.getStringApplication("rotulo_unidade_origem").equals($P{FORMA_APRESENTACAO})
                                	?
                                    	$F{empresaSolicitante}.getCodigo()
                                	:
                        null ) ) ) ) )))]]></groupExpression>
		<groupHeader>
			<band height="24">
				<textField>
					<reportElement x="0" y="0" width="802" height="13" uuid="1139007c-d976-4cc1-8888-764a07a27a11"/>
					<textElement>
						<font fontName="Arial" isBold="true" isItalic="true" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_geral").equals($P{FORMA_APRESENTACAO})
?
    ""
:
    (Bundle.getStringApplication("rotulo_profissional").equals($P{FORMA_APRESENTACAO})
    ?
        Bundle.getStringApplication("rotulo_profissional") + ": " + $F{profissional}.getDescricaoFormatado() + " : " + Bundle.getStringApplication("rotulo_ine") + " : " + ($F{equipeProfissional}.getEquipe().getEquipeCnes() != null ? $F{equipeProfissional}.getEquipe().getEquipeCnes() : "Profissional sem equipe")
    :
	(Bundle.getStringApplication("rotulo_unidade_origem").equals($P{FORMA_APRESENTACAO})
	?
		Bundle.getStringApplication("rotulo_unidade_origem") + ": " + $F{empresaSolicitante}.getDescricao()
	:
        (Bundle.getStringApplication("rotulo_usuario_cadsus").equals($P{FORMA_APRESENTACAO})
        ?
            Bundle.getStringApplication("rotulo_usuario_cadsus") + ": " + $F{usuarioCadsus}.getDescricaoFormatado()
        :
            (Bundle.getStringApplication("rotulo_cbo").equals($P{FORMA_APRESENTACAO})
            ?
                $F{tabelaCbo}.getCbo() != null
                ?
                    Bundle.getStringApplication("rotulo_cbo") + ": " + $F{tabelaCbo}.getDescricaoFormatado()
                :
                    Bundle.getStringApplication("rotulo_cbo") + ": " + $F{tabelaCbo}.getDescricao()
            :
                (Bundle.getStringApplication("rotulo_convenio").equals($P{FORMA_APRESENTACAO})
                ?
                    Bundle.getStringApplication("rotulo_convenio") + ": " + ($F{convenio}.getDescricaoFormatado() != null ? $F{convenio}.getDescricaoFormatado() : "Sem convênio")
                :
                    (Bundle.getStringApplication("rotulo_procedimento").equals($P{FORMA_APRESENTACAO})
                    ?
                        Bundle.getStringApplication("rotulo_procedimento") + ": " + $F{procedimento}.getDescricaoFormatado()
                    :
                        (Bundle.getStringApplication("rotulo_municipio").equals($P{FORMA_APRESENTACAO})
                                ?
                                    Bundle.getStringApplication("rotulo_municipio")+": "+($F{cidade}.getDescricao())
                                :
                        "" ) ) ) ) )))]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="427" y="13" width="175" height="11" uuid="56c39162-4e35-4f50-88d4-d3933f1cf120"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Procedimento*/
Bundle.getStringApplication("rotulo_procedimento")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="23" width="802" height="1" uuid="488afd62-69c2-41cc-8335-c9e6296a04b5"/>
				</line>
				<textField>
					<reportElement x="0" y="13" width="152" height="11" uuid="56c39162-4e35-4f50-88d4-d3933f1cf120"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Paciente*/
Bundle.getStringApplication("rotulo_paciente")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="154" y="13" width="36" height="11" uuid="56c39162-4e35-4f50-88d4-d3933f1cf120"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Idade*/
Bundle.getStringApplication("rotulo_idade")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="190" y="13" width="25" height="11" uuid="56c39162-4e35-4f50-88d4-d3933f1cf120"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Sexo*/
Bundle.getStringApplication("rotulo_sexo")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="283" y="13" width="142" height="11" uuid="56c39162-4e35-4f50-88d4-d3933f1cf120"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Profissional*/
Bundle.getStringApplication("rotulo_profissional")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="216" y="13" width="65" height="10" uuid="56c39162-4e35-4f50-88d4-d3933f1cf120"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Data*/
Bundle.getStringApplication("rotulo_data")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="769" y="13" width="33" height="11" uuid="56c39162-4e35-4f50-88d4-d3933f1cf120"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Quantidade*/
Bundle.getStringApplication("rotulo_quantidade")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="604" y="13" width="164" height="11" uuid="17153679-ca1e-490b-8042-da9d2e30b3fb"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Unidade Origem*/
Bundle.getStringApplication("rotulo_unidade_origem")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="13">
				<textField>
					<reportElement x="713" y="2" width="55" height="11" uuid="56c39162-4e35-4f50-88d4-d3933f1cf120"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Total*/
Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
				<textField pattern="#########0.00">
					<reportElement x="769" y="2" width="33" height="11" uuid="a8887239-1a8d-4f69-898a-ae2ef6dc1301"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalQtdFA}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="12" splitType="Stretch">
			<textField>
				<reportElement x="427" y="1" width="175" height="11" uuid="2e4a3049-a73f-460d-84c5-6d67459ff8c2"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{procedimento}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="283" y="1" width="142" height="11" uuid="2e4a3049-a73f-460d-84c5-6d67459ff8c2"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profissional}.getNome()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="190" y="1" width="25" height="11" uuid="2e4a3049-a73f-460d-84c5-6d67459ff8c2"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getSexo() != null
?
    $F{usuarioCadsus}.getSexoAbreviadoFormatado()
:
    ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="154" y="1" width="36" height="11" uuid="2e4a3049-a73f-460d-84c5-6d67459ff8c2"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDataNascimento() != null && !"".equals($F{usuarioCadsus}.getDataNascimento()) && $F{dataAtendimento} != null
?
    Data.getDescricaoIdadeSimples($F{usuarioCadsus}.getDataNascimento(), $F{dataAtendimento})
:
    ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="1" width="152" height="11" uuid="2e4a3049-a73f-460d-84c5-6d67459ff8c2"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH:mm">
				<reportElement x="216" y="1" width="65" height="11" uuid="4ffc8e98-4fe8-4d5a-9ae7-b935eeae87cf"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataAtendimento}]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00">
				<reportElement x="769" y="1" width="33" height="11" uuid="4ffcf6af-ad91-44f6-b0b8-81da5c19231a"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidade}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="604" y="1" width="164" height="11" uuid="e556c9e6-fc8e-40d1-8aed-2ec473a4c11a"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresaSolicitante}.getDescricao()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
