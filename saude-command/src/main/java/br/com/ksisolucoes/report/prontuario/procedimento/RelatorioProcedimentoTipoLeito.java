/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.prontuario.procedimento;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoTipoLeito;
import java.util.Collection;

/**
 *
 * <AUTHOR>
 */
public class RelatorioProcedimentoTipoLeito extends AbstractReport {

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_tipo_leito.jrxml";
    }

    @Override
    public Collection getCollection() throws DAOException{
        return LoadManager.getInstance(ProcedimentoTipoLeito.class).start().getList();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_leito");
    }
}
