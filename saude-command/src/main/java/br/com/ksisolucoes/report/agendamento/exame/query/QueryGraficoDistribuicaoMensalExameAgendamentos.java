package br.com.ksisolucoes.report.agendamento.exame.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.agendamento.exame.dto.GraficoDistribuicaoMensalExameAgendamentosDTO;
import br.com.ksisolucoes.report.agendamento.exame.dto.GraficoDistribuicaoMensalExameAgendamentosDTOParam;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.basico.FaixaEtariaItem;
import br.com.ksisolucoes.vo.basico.FaixaEtariaItemPK;
import java.util.*;
import org.hibernate.Criteria;
import org.hibernate.Query;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QueryGraficoDistribuicaoMensalExameAgendamentos extends CommandQuery<QueryGraficoDistribuicaoMensalExameAgendamentos>
        implements ITransferDataReport<GraficoDistribuicaoMensalExameAgendamentosDTOParam, GraficoDistribuicaoMensalExameAgendamentosDTO> {

    private GraficoDistribuicaoMensalExameAgendamentosDTOParam param;
    private List<GraficoDistribuicaoMensalExameAgendamentosDTO> dtoList;

    @Override
    public void setDTOParam(GraficoDistribuicaoMensalExameAgendamentosDTOParam param) {
        this.param = param;
    }

    private List<GraficoDistribuicaoMensalExameAgendamentosDTO> getResultadosMes(Date mesAtual) throws DAOException {
        HQLHelper hqlMes = new HQLHelper();
        hqlMes.addToSelect("sum(agah.quantidadeVagasOcupadas)", "quantidade");

        if (GraficoDistribuicaoMensalExameAgendamentosDTOParam.FormaApresentacao.UNIDADE.equals(param.getFormaApresentacao())) {
            hqlMes.addToSelectAndGroup("agah.localAgendamento.descricao", "descricaoFormaApresentacao");
        } else if (GraficoDistribuicaoMensalExameAgendamentosDTOParam.FormaApresentacao.FAIXA_ETARIA.equals(param.getFormaApresentacao())) {
            String descricao = "";
            try {
                Criteria cFaixaEtariaItem = getSession().createCriteria(FaixaEtariaItem.class);
                cFaixaEtariaItem.add(Restrictions.eq(VOUtils.montarPath(FaixaEtariaItem.PROP_ID, FaixaEtariaItemPK.PROP_FAIXA_ETARIA), param.getFaixaEtaria()));
                List<FaixaEtariaItem> faixas = cFaixaEtariaItem.list();
                int count = 0;
                if (CollectionUtils.isNotNullEmpty(faixas)) {
                    for (FaixaEtariaItem faixaEtariaItem : faixas) {
                        descricao += " (case when ((extract(years from age(agah.dataAgendamento, usu.dataNascimento)) * 12 + extract(months from age(agah.dataAgendamento, usu.dataNascimento))) >= "
                                + faixaEtariaItem.getIdadeInicial() + " " + " and (extract(years from age(agah.dataAgendamento, usu.dataNascimento)) * 12 + extract(months from age(agah.dataAgendamento, usu.dataNascimento))) <= " + faixaEtariaItem.getIdadeFinal() + " ) " + " then '" + faixaEtariaItem.getDescricao() + "' " + " else ";
                        count++;
                        if (count == faixas.size()) {
                            descricao += " 'outra' ";
                        }
                    }
                    for (FaixaEtariaItem faixaEtariaItem : faixas) {
                        descricao += " end) ";
                    }
                }
            } catch (DAOException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
            hqlMes.addToSelectAndGroup(descricao, "descricaoFormaApresentacao");
        } else if (GraficoDistribuicaoMensalExameAgendamentosDTOParam.FormaApresentacao.UNIDADE_ORIGEM.equals(param.getFormaApresentacao())) {
            hqlMes.addToSelectAndGroup("agah.empresaOrigem.descricao", "descricaoFormaApresentacao");
        } else if (GraficoDistribuicaoMensalExameAgendamentosDTOParam.FormaApresentacao.TIPO_PROCEDIMENTO.equals(param.getFormaApresentacao())) {
            hqlMes.addToSelectAndGroup("agah.tipoProcedimento.descricao", "descricaoFormaApresentacao");
        } else if (GraficoDistribuicaoMensalExameAgendamentosDTOParam.FormaApresentacao.SEXO.equals(param.getFormaApresentacao())) {
            hqlMes.addToSelect("(case when usu.sexo = 'M' "
                    + "               then 'Masculino' "
                    + "               else (case when usu.sexo = 'F' then 'Feminino' else 'Outro' end) "
                    + "               end)", "descricaoFormaApresentacao");
            hqlMes.addToGroup("usu.sexo");
        } else if (GraficoDistribuicaoMensalExameAgendamentosDTOParam.FormaApresentacao.AREA.equals(param.getFormaApresentacao())) {
            hqlMes.addToSelect("coalesce( ea.descricao,'AREA NAO DEFINIDA')", "descricaoFormaApresentacao");
            hqlMes.addToGroup("ea.id.codigo");
            hqlMes.addToGroup("ea.id.cidade");
            hqlMes.addToGroup("ea.descricao");
        }


        if (this.param.getEquipeArea() != null) {
           hqlMes.addToWhereWhithAnd("ea = ",this.param.getEquipeArea());
        }

        hqlMes.addToSelectAndGroup("month(agah.dataAgendamento)", "mes");
        hqlMes.addToSelectAndGroup("year(agah.dataAgendamento)", "ano");

        /**
         * CALCULO DO TOTAL *
         */
        HQLHelper hqlSub = hqlMes.getNewInstanceSubQuery();
        hqlSub.addToSelect("sum(agah2.quantidadeVagasOcupadas)");

        hqlSub.addToFrom("AgendaGradeAtendimentoHorario agah2, Agenda ap2");

        hqlSub.addToWhereWhithAnd("agah2.tipoProcedimento ", param.getTipoProcedimento());
        if (!GraficoDistribuicaoMensalExameAgendamentosDTOParam.TipoAgendamento.AMBOS.equals(param.getTipoAgendamento())) {
            if (GraficoDistribuicaoMensalExameAgendamentosDTOParam.TipoAgendamento.TFD.equals(param.getTipoAgendamento())) {
                hqlSub.addToWhereWhithAnd("agah2.tipoProcedimento.flagTfd = ", RepositoryComponentDefault.SIM);
            } else if (GraficoDistribuicaoMensalExameAgendamentosDTOParam.TipoAgendamento.ENCAMINHAMENTO.equals(param.getTipoAgendamento())) {
                hqlSub.addToWhereWhithAnd("agah2.tipoProcedimento.flagTfd <> ", RepositoryComponentDefault.SIM);
            }
        }
        hqlSub.addToWhereWhithAnd("agah2.localAgendamento ", param.getEmpresa());
        hqlSub.addToWhereWhithAnd("agah2.empresaOrigem in ", param.getEmpresaOrigem());
        hqlSub.addToWhereWhithAnd("agah2.dataAgendamento", Data.adjustRangeHour(Data.adjustRangeDay(mesAtual)));
        hqlSub.addToWhereWhithAnd("agah2.status <>", AgendaGradeAtendimentoHorario.STATUS_CANCELADO);

        hqlMes.addToSelect("(" + hqlSub.getQuery() + ")", "total");
        /**
         * ********************* *
         */
        hqlMes.setTypeSelect(GraficoDistribuicaoMensalExameAgendamentosDTO.class.getName());

        if (GraficoDistribuicaoMensalExameAgendamentosDTOParam.FormaApresentacao.AREA.equals(param.getFormaApresentacao())
                || this.param.getEquipeArea() != null) {
            hqlMes.addToFrom("AgendaGradeAtendimentoHorario agah"
                    + " left join agah.usuarioCadsus usu "
                    + " left join usu.enderecoDomicilio ed "
                    + " left join ed.equipeMicroArea ema"
                    + " left join ema.equipeArea ea");
        } else {
            hqlMes.addToFrom("AgendaGradeAtendimentoHorario agah"
                    + " left join agah.usuarioCadsus usu");
        }
        hqlMes.addToWhereWhithAnd("agah.tipoProcedimento", param.getTipoProcedimento());
        if (!GraficoDistribuicaoMensalExameAgendamentosDTOParam.TipoAgendamento.AMBOS.equals(param.getTipoAgendamento())) {
            if (GraficoDistribuicaoMensalExameAgendamentosDTOParam.TipoAgendamento.TFD.equals(param.getTipoAgendamento())) {
                hqlMes.addToWhereWhithAnd("agah.tipoProcedimento.flagTfd = ", RepositoryComponentDefault.SIM);
            } else if (GraficoDistribuicaoMensalExameAgendamentosDTOParam.TipoAgendamento.ENCAMINHAMENTO.equals(param.getTipoAgendamento())) {
                hqlMes.addToWhereWhithAnd("agah.tipoProcedimento.flagTfd <> ", RepositoryComponentDefault.SIM);
            }
        }
        hqlMes.addToWhereWhithAnd("agah.localAgendamento ", param.getEmpresa());
        hqlMes.addToWhereWhithAnd("agah.empresaOrigem in ", param.getEmpresaOrigem());
        hqlMes.addToWhereWhithAnd("agah.dataAgendamento", Data.adjustRangeHour(Data.adjustRangeDay(mesAtual)));
        hqlMes.addToWhereWhithAnd("agah.status <>", AgendaGradeAtendimentoHorario.STATUS_CANCELADO);

        hqlMes.addToOrder("year(agah.dataAgendamento)");
        hqlMes.addToOrder("month(agah.dataAgendamento)");
        hqlMes.addToOrder("1 desc");

        Query query = getSession().createQuery(hqlMes.getQuery());
        if(Coalesce.asLong(param.getMaximoSerie()) > 0){
            query.setMaxResults(param.getMaximoSerie().intValue());
        }

        hqlMes.applyRestrictions(query);

        return hqlMes.getBeanList((List) query.list());
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        List<GraficoDistribuicaoMensalExameAgendamentosDTO> list = new ArrayList<GraficoDistribuicaoMensalExameAgendamentosDTO>();
        for (Date mesAtual = param.getPeriodo().getDataInicial(); !mesAtual.after(param.getPeriodo().getDataFinal()); mesAtual = Data.addMeses(mesAtual, 1) ) {
            List<GraficoDistribuicaoMensalExameAgendamentosDTO> dtoMes = getResultadosMes(mesAtual);
            if(CollectionUtils.isNotNullEmpty(dtoMes)){
                list.addAll(dtoMes);
            }
        }
        dtoList = list;
    }

    @Override
    public Collection getResult() {
        return dtoList;
    }
}
