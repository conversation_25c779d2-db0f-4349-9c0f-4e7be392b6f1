<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_grafico_cruzamento_dispensacao" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="782" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="27304e98-7c47-4bc0-83a4-e7feb4e8cd1b">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.basico.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="TIPO_DADO" class="br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioDistribuicaoMensalEncaminhamentosDTOParam.TipoDado"/>
	<field name="descricaoFormaApresentacao" class="java.lang.String"/>
	<field name="quantidade" class="java.lang.Long"/>
	<field name="mes" class="java.lang.Integer"/>
	<field name="porcentagem" class="java.lang.Double"/>
	<field name="ano" class="java.lang.Integer"/>
	<group name="CHART">
		<groupExpression><![CDATA[1]]></groupExpression>
		<groupHeader>
			<band height="440" splitType="Stretch">
				<bar3DChart>
					<chart isShowLegend="true" evaluationTime="Group" evaluationGroup="CHART" theme="default">
						<reportElement x="0" y="0" width="782" height="440" uuid="7c487be4-bb37-4021-a550-1e388864b82d"/>
						<chartTitle/>
						<chartSubtitle/>
						<chartLegend position="Bottom"/>
					</chart>
					<categoryDataset>
						<dataset resetType="Group" resetGroup="CHART"/>
						<categorySeries>
							<seriesExpression><![CDATA[$F{descricaoFormaApresentacao}]]></seriesExpression>
							<categoryExpression><![CDATA[""+$F{mes}+"/"+$F{ano}]]></categoryExpression>
							<valueExpression><![CDATA[br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioDistribuicaoMensalEncaminhamentosDTOParam.TipoDado.QUANTIDADE.equals($P{TIPO_DADO})?$F{quantidade}:$F{porcentagem}]]></valueExpression>
							<labelExpression><![CDATA[br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioDistribuicaoMensalEncaminhamentosDTOParam.TipoDado.QUANTIDADE.equals($P{TIPO_DADO})?$F{quantidade}.toString():$F{porcentagem}.toString()]]></labelExpression>
						</categorySeries>
					</categoryDataset>
					<bar3DPlot isShowLabels="true">
						<plot/>
						<itemLabel color="#000000" backgroundColor="#FFFFFF"/>
						<categoryAxisLabelExpression><![CDATA[br.com.ksisolucoes.util.Bundle.getStringApplication("rotulo_periodo")]]></categoryAxisLabelExpression>
						<categoryAxisFormat>
							<axisFormat/>
						</categoryAxisFormat>
						<valueAxisLabelExpression><![CDATA[br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioDistribuicaoMensalEncaminhamentosDTOParam.TipoDado.QUANTIDADE.equals($P{TIPO_DADO})?br.com.ksisolucoes.util.Bundle.getStringApplication("rotulo_quantidade"):"%"]]></valueAxisLabelExpression>
						<valueAxisFormat>
							<axisFormat/>
						</valueAxisFormat>
						<rangeAxisMaxValueExpression><![CDATA[br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioDistribuicaoMensalEncaminhamentosDTOParam.TipoDado.QUANTIDADE.equals($P{TIPO_DADO})?null:100]]></rangeAxisMaxValueExpression>
					</bar3DPlot>
				</bar3DChart>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band splitType="Stretch"/>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
