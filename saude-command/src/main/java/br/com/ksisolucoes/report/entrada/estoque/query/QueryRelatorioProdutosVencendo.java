package br.com.ksisolucoes.report.entrada.estoque.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.QueryRelatorioProdutosVencendoDTO;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.QueryRelatorioProdutosVencendoDTOParam;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioProdutosVencendo extends CommandQuery<QueryRelatorioProdutosVencendo> implements ITransferDataReport<QueryRelatorioProdutosVencendoDTOParam, QueryRelatorioProdutosVencendoDTO> {

    private QueryRelatorioProdutosVencendoDTOParam param;
    private List<QueryRelatorioProdutosVencendoDTO> result;
    
    @Override
    protected void createQuery(HQLHelper hql) {
        
        if (RepositoryComponentDefault.SIM.equals(this.param.getSepararUnidade())) {
            hql.addToSelectAndGroupAndOrder("empresa.codigo", "grupoEstoque.id.estoqueEmpresa.id.empresa.codigo");
            hql.addToSelectAndGroupAndOrder("empresa.descricao", "grupoEstoque.id.estoqueEmpresa.id.empresa.descricao");
        }
        
        hql.addToSelectAndGroup("produto.codigo", "grupoEstoque.id.estoqueEmpresa.id.produto.codigo");
        hql.addToSelectAndGroup("produto.referencia", "grupoEstoque.id.estoqueEmpresa.id.produto.referencia");
        hql.addToSelectAndGroup("produto.descricao", "grupoEstoque.id.estoqueEmpresa.id.produto.descricao");
        hql.addToSelectAndGroup("unidade.unidade", "grupoEstoque.id.estoqueEmpresa.id.produto.unidade.unidade");
        hql.addToSelectAndGroup("grupoEstoque.id.grupo", "grupoEstoque.id.grupo");
        hql.addToSelect("sum(grupoEstoque.estoqueFisico)", "quantidade");
        hql.addToSelect("coalesce(sum(grupoEstoque.estoqueFisico*estoqueEmpresa.precoMedio),0)", "valor");
        hql.addToSelectAndGroup("estoqueEmpresa.precoMedio", "grupoEstoque.id.estoqueEmpresa.precoMedio");
        hql.addToSelectAndGroup("grupoEstoque.dataValidade", "grupoEstoque.dataValidade");
        
        hql.setTypeSelect(QueryRelatorioProdutosVencendoDTO.class.getName());
        hql.addToFrom("GrupoEstoque grupoEstoque"
                + " left join grupoEstoque.id.estoqueEmpresa estoqueEmpresa"
                + " left join estoqueEmpresa.id.produto produto"
                + " left join estoqueEmpresa.id.empresa empresa"
                + " left join produto.unidade unidade"
                + " left join produto.subGrupo subGrupo"
                + " left join subGrupo.roGrupoProduto grupoProduto");
        
        hql.addToWhereWhithAnd("grupoEstoque.dataValidade <=", Data.addDias(Data.getDataAtual(), param.getDiasVencimento()));
        hql.addToWhereWhithAnd("grupoEstoque.dataValidade >", Data.getDataAtual());
        hql.addToWhereWhithAnd("grupoProduto ", param.getGrupoProduto());
        
        hql.addToWhereWhithAnd("grupoEstoque.estoqueFisico > 0");
        
        if (QueryRelatorioProdutosVencendoDTOParam.Ordenacao.DATA_VALIDADE.equals(param.getOrdenacao())) {
            hql.addToOrder("grupoEstoque.dataValidade asc");
        } else if (QueryRelatorioProdutosVencendoDTOParam.Ordenacao.VALOR.equals(param.getOrdenacao())) {
            if (RepositoryComponentDefault.SIM.equals(this.param.getSepararUnidade())) {
                hql.addToOrder("9 desc");
            } else {
                hql.addToOrder("7 desc");
            }
        }
    }

    @Override
    public void setDTOParam(QueryRelatorioProdutosVencendoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>)result);
    }

    @Override
    public List<QueryRelatorioProdutosVencendoDTO> getResult() {
        return result;
    }
    
}
