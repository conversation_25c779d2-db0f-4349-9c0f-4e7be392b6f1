/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.prontuario.exame.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.ImpressaoExameDTO;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.ImpressaoExameDTOParam;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryImpressaoRequisicaoExame extends CommandQuery implements ITransferDataReport<ImpressaoExameDTOParam, ImpressaoExameDTO> {

    private ImpressaoExameDTOParam param;
    private List<ImpressaoExameDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.setTypeSelect(ImpressaoExameDTO.class.getName());

        hql.addToSelect("profissional.codigo", "exameRequisicao.exame.profissional.codigo");
        hql.addToSelect("profissional.referencia", "exameRequisicao.exame.profissional.referencia");
        hql.addToSelect("profissional.nome",                            "exameRequisicao.exame.profissional.nome");
        hql.addToSelect("profissional.unidadeFederacaoConselhoRegistro","exameRequisicao.exame.profissional.unidadeFederacaoConselhoRegistro");
        hql.addToSelect("profissional.numeroRegistro",                  "exameRequisicao.exame.profissional.numeroRegistro");
        
        hql.addToSelect("conselhoClasse.sigla",                         "exameRequisicao.exame.profissional.conselhoClasse.sigla"); 

        hql.addToSelect("empresaSolicitante.codigo", "exameRequisicao.exame.empresaSolicitante.codigo");
        hql.addToSelect("empresaSolicitante.referencia", "exameRequisicao.exame.empresaSolicitante.referencia");
        hql.addToSelect("empresaSolicitante.descricao", "exameRequisicao.exame.empresaSolicitante.descricao");
        hql.addToSelect("empresaSolicitante.sigla", "exameRequisicao.exame.empresaSolicitante.sigla");
        hql.addToSelect("empresaSolicitante.cnes", "exameRequisicao.exame.empresaSolicitante.cnes");

        hql.addToSelect("tipoExame.codigo", "exameRequisicao.exame.tipoExame.codigo");
        hql.addToSelect("tipoExame.descricao", "exameRequisicao.exame.tipoExame.descricao");

        hql.addToSelect("usuarioCadsus.codigo", "exameRequisicao.exame.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "exameRequisicao.exame.usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.apelido", "exameRequisicao.exame.usuarioCadsus.apelido");
        hql.addToSelect("usuarioCadsus.utilizaNomeSocial", "exameRequisicao.exame.usuarioCadsus.utilizaNomeSocial");
        hql.addToSelect("usuarioCadsus.sexo", "exameRequisicao.exame.usuarioCadsus.sexo");
        hql.addToSelect("usuarioCadsus.dataNascimento", "exameRequisicao.exame.usuarioCadsus.dataNascimento");
        hql.addToSelect("usuarioCadsus.cpf", "exameRequisicao.exame.usuarioCadsus.cpf");
        hql.addToSelect("usuarioCadsus.rg", "exameRequisicao.exame.usuarioCadsus.rg");
        hql.addToSelect("usuarioCadsus.nomeMae", "exameRequisicao.exame.usuarioCadsus.nomeMae");
        hql.addToSelect("usuarioCadsus.celular", "exameRequisicao.exame.usuarioCadsus.celular");
        hql.addToSelect("usuarioCadsus.telefone", "exameRequisicao.exame.usuarioCadsus.telefone");
        hql.addToSelect("usuarioCadsus.telefone2", "exameRequisicao.exame.usuarioCadsus.telefone2");
        hql.addToSelect("usuarioCadsus.telefone3", "exameRequisicao.exame.usuarioCadsus.telefone3");
        hql.addToSelect("usuarioCadsus.telefone4", "exameRequisicao.exame.usuarioCadsus.telefone4");

        hql.addToSelect("exameProcedimento.codigo", "exameRequisicao.exameProcedimento.codigo");
        hql.addToSelect("exameProcedimento.procedimento.codigo", "exameRequisicao.exameProcedimento.procedimento.codigo");
        hql.addToSelect("exameProcedimento.descricaoProcedimento", "exameRequisicao.exameProcedimento.descricaoProcedimento");

        hql.addToSelect("exameRequisicao.codigo", "exameRequisicao.codigo");
        hql.addToSelect("exameRequisicao.complemento", "exameRequisicao.complemento");
        hql.addToSelect("exameRequisicao.justificativa", "exameRequisicao.justificativa");
        hql.addToSelect("exameRequisicao.quantidade", "exameRequisicao.quantidade");
        hql.addToSelect("exameRequisicao.flagHiv", "exameRequisicao.flagHiv");
        hql.addToSelect("exameRequisicao.flagRetratamento", "exameRequisicao.flagRetratamento");

        hql.addToSelect("exame.codigo", "exameRequisicao.exame.codigo");
        hql.addToSelect("exame.numeroProtocoloAutorizacao", "exameRequisicao.exame.numeroProtocoloAutorizacao");
        hql.addToSelect("exame.dataCadastro", "exameRequisicao.exame.dataCadastro");
        hql.addToSelect("exame.dataAutorizacao", "exameRequisicao.exame.dataAutorizacao");
        hql.addToSelect("exame.dataSolicitacao", "exameRequisicao.exame.dataSolicitacao");
        hql.addToSelect("exame.flagUrgente", "exameRequisicao.exame.flagUrgente");
        hql.addToSelect("exame.descricaoDadoClinico", "exameRequisicao.exame.descricaoDadoClinico");
        hql.addToSelect("exame.nomePaciente", "exameRequisicao.exame.nomePaciente");
        hql.addToSelect("exame.nomeProfissional", "exameRequisicao.exame.nomeProfissional");

        hql.addToSelect("localExame.codigo", "exameRequisicao.exame.localExame.codigo");
        hql.addToSelect("localExame.referencia", "exameRequisicao.exame.localExame.referencia");
        hql.addToSelect("localExame.cnes", "exameRequisicao.exame.localExame.cnes");
        hql.addToSelect("localExame.descricao", "exameRequisicao.exame.localExame.descricao");
        
        hql.addToSelect("leitoQuarto.codigo", "exameRequisicao.exame.atendimento.leitoQuarto.codigo");
        hql.addToSelect("leitoQuarto.descricao", "exameRequisicao.exame.atendimento.leitoQuarto.descricao");
        
        hql.addToSelect("quartoInternacao.codigo", "exameRequisicao.exame.atendimento.leitoQuarto.quartoInternacao.codigo");
        hql.addToSelect("quartoInternacao.descricao", "exameRequisicao.exame.atendimento.leitoQuarto.quartoInternacao.descricao");
        hql.addToSelect("quartoInternacao.referencia", "exameRequisicao.exame.atendimento.leitoQuarto.quartoInternacao.referencia");
        
        hql.addToSelect("empresa.codigo", "exameRequisicao.exame.atendimento.empresa.codigo");
        hql.addToSelect("empresa.descricao", "exameRequisicao.exame.atendimento.empresa.descricao");
        
        hql.addToSelect("convenio.codigo", "exameRequisicao.exame.atendimento.convenio.codigo");
        hql.addToSelect("convenio.descricao", "exameRequisicao.exame.atendimento.convenio.descricao");

        hql.addToSelect("atendimentoPrincipal.codigo", "exameRequisicao.exame.atendimento.atendimentoPrincipal.codigo");

        hql.addToSelect("(select min(ucc.numeroCartao) from UsuarioCadsusCns ucc where ucc.usuarioCadsus = usuarioCadsus and ucc.excluido = 0 )", "usuarioCadsusCns.numeroCartao");

        {
            hql.addToSelect("endereco.codigo", "enderecoUsuarioCadsus.codigo");
            hql.addToSelect("endereco.logradouro", "enderecoUsuarioCadsus.logradouro");
            hql.addToSelect("endereco.numeroLogradouro", "enderecoUsuarioCadsus.numeroLogradouro");
            hql.addToSelect("endereco.bairro", "enderecoUsuarioCadsus.bairro");
            hql.addToSelect("endereco.cep", "enderecoUsuarioCadsus.cep");
            hql.addToSelect("endereco.complementoLogradouro", "enderecoUsuarioCadsus.complementoLogradouro");
            hql.addToSelect("endereco.pontoReferencia", "enderecoUsuarioCadsus.pontoReferencia");

            hql.addToSelect("tipoLogradouro.codigo", "enderecoUsuarioCadsus.tipoLogradouro.codigo");
            hql.addToSelect("tipoLogradouro.descricao", "enderecoUsuarioCadsus.tipoLogradouro.descricao");
            hql.addToSelect("tipoLogradouro.sigla", "enderecoUsuarioCadsus.tipoLogradouro.sigla");

            hql.addToSelect("cidade.codigo", "enderecoUsuarioCadsus.cidade.codigo");
            hql.addToSelect("cidade.descricao", "enderecoUsuarioCadsus.cidade.descricao");

            hql.addToSelect("estado.codigo", "enderecoUsuarioCadsus.cidade.estado.codigo");
            hql.addToSelect("estado.sigla", "enderecoUsuarioCadsus.cidade.estado.sigla");
            hql.addToSelect("estado.descricao", "enderecoUsuarioCadsus.cidade.estado.descricao");

            hql.addToSelect("enderecoEstruturado.codigo", "enderecoUsuarioCadsus.enderecoEstruturado.codigo");
            hql.addToSelect("enderecoEstruturado.cep", "enderecoUsuarioCadsus.enderecoEstruturado.cep");
            hql.addToSelect("enderecoEstruturado.numero", "enderecoUsuarioCadsus.enderecoEstruturado.numero");

            hql.addToSelect("enderecoEstruturadoLogradouro.codigo", "enderecoUsuarioCadsus.enderecoEstruturado.enderecoEstruturadoLogradouro.codigo");
            hql.addToSelect("enderecoEstruturadoLogradouro.descricao", "enderecoUsuarioCadsus.enderecoEstruturado.enderecoEstruturadoLogradouro.descricao");

            hql.addToSelect("tipoLogradouroEstruturado.codigo", "enderecoUsuarioCadsus.enderecoEstruturado.enderecoEstruturadoLogradouro.tipoLogradouro.codigo");
            hql.addToSelect("tipoLogradouroEstruturado.descricao", "enderecoUsuarioCadsus.enderecoEstruturado.enderecoEstruturadoLogradouro.tipoLogradouro.descricao");
            hql.addToSelect("tipoLogradouroEstruturado.sigla", "enderecoUsuarioCadsus.enderecoEstruturado.enderecoEstruturadoLogradouro.tipoLogradouro.sigla");

            hql.addToSelect("enderecoEstruturadoDistrito.codigo", "enderecoUsuarioCadsus.enderecoEstruturado.enderecoEstruturadoDistrito.codigo");
            hql.addToSelect("enderecoEstruturadoDistrito.descricao", "enderecoUsuarioCadsus.enderecoEstruturado.enderecoEstruturadoDistrito.descricao");

            hql.addToSelect("bairro.codigo", "enderecoUsuarioCadsus.enderecoEstruturado.bairro.codigo");
            hql.addToSelect("bairro.descricao", "enderecoUsuarioCadsus.enderecoEstruturado.bairro.descricao");

            hql.addToSelect("cidadeBairro.codigo", "enderecoUsuarioCadsus.enderecoEstruturado.bairro.cidade.codigo");
            hql.addToSelect("cidadeBairro.descricao", "enderecoUsuarioCadsus.enderecoEstruturado.bairro.cidade.descricao");

            hql.addToSelect("estadoBairro.codigo", "enderecoUsuarioCadsus.enderecoEstruturado.bairro.cidade.estado.codigo");
            hql.addToSelect("estadoBairro.sigla", "enderecoUsuarioCadsus.enderecoEstruturado.bairro.cidade.estado.sigla");
            hql.addToSelect("estadoBairro.descricao", "enderecoUsuarioCadsus.enderecoEstruturado.bairro.cidade.estado.descricao");
        }

        hql.addToFrom("ExameRequisicao exameRequisicao"
                + " left join exameRequisicao.exame exame"
                + " left join exame.tipoExame tipoExame"
                + " left join exameRequisicao.exameProcedimento exameProcedimento"
                + " left join exame.localExame localExame"
                + " left join exame.usuarioCadsus usuarioCadsus"
                + " left join exame.profissional profissional"
                + " left join profissional.conselhoClasse conselhoClasse"
                + " left join exame.atendimento atendimento"
                + " left join exame.atendimento.atendimentoPrincipal atendimentoPrincipal"
                + " left join exame.empresaSolicitante empresaSolicitante"
                + " left join atendimento.leitoQuarto leitoQuarto"
                + " left join leitoQuarto.quartoInternacao quartoInternacao"
                + " left join atendimento.empresa empresa"
                + " left join atendimento.convenio convenio"
                + " left join atendimento.enderecoUsuarioCadsus endereco"
                + " left join endereco.tipoLogradouro tipoLogradouro"
                + " left join endereco.cidade cidade"
                + " left join cidade.estado estado"
                + " left join endereco.enderecoEstruturado enderecoEstruturado"
                + " left join enderecoEstruturado.enderecoEstruturadoLogradouro enderecoEstruturadoLogradouro"
                + " left join enderecoEstruturadoLogradouro.tipoLogradouro tipoLogradouroEstruturado"
                + " left join enderecoEstruturado.enderecoEstruturadoDistrito enderecoEstruturadoDistrito"
                + " left join enderecoEstruturado.bairro bairro"
                + " left join bairro.cidade cidadeBairro"
                + " left join cidadeBairro.estado estadoBairro"
        );

        hql.addToWhereWhithAnd("atendimento = ", this.param.getAtendimento());
        if (CollectionUtils.isNotNullEmpty(param.getCodigosExames())) {
            hql.addToWhereWhithAnd("exame.codigo in ", this.param.getCodigosExames());
        }
        hql.addToWhereWhithAnd("exame.status <> ", Exame.STATUS_CANCELADO);
        
        hql.addToWhereWhithAnd("coalesce(exame.tipoConvenioRealizado,"+TipoExame.CONVENIO_SUS+") = ", param.getTipoConvenio());

        hql.addToOrder("exame.codigo");
        hql.addToOrder("exameRequisicao.codigo");
        hql.addToOrder("exameProcedimento.codigo");

    }

    @Override
    public void setDTOParam(ImpressaoExameDTOParam t) {
        this.param = t;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection<ImpressaoExameDTO> getResult() {
        return result;
    }
}
