<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="sub_relatorio_produto" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="ab53b9b9-af1b-46ba-a9d2-16edc194e78a">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="3.4522712143931105"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="exibirLotes" class="java.lang.String"/>
	<parameter name="utilizaLocalizacaoEstoque" class="java.lang.String"/>
	<field name="estoqueEncomendado" class="java.lang.Double"/>
	<field name="estoqueReservado" class="java.lang.Double"/>
	<field name="estoqueDevolucao" class="java.lang.Double"/>
	<field name="estoqueReservadoDevolucao" class="java.lang.Double"/>
	<field name="estoqueFisico" class="java.lang.Double"/>
	<field name="id" class="br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoquePK">
		<fieldDescription><![CDATA[id]]></fieldDescription>
	</field>
	<field name="roDeposito" class="br.com.ksisolucoes.vo.entradas.estoque.Deposito"/>
	<field name="dataValidade" class="java.util.Date"/>
	<field name="laboratorioFabricante" class="java.lang.String"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="estoqueDisponivel" class="java.lang.Double" resetType="Column">
		<variableExpression><![CDATA[$F{estoqueFisico}+$F{estoqueEncomendado}-$F{estoqueReservado}]]></variableExpression>
	</variable>
	<variable name="EstoqueEncomendado" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueEncomendado} == null ? 0.00 : $F{estoqueEncomendado}]]></variableExpression>
	</variable>
	<variable name="EstoqueReservado" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueReservado} == null ? 0.00 : $F{estoqueReservado}]]></variableExpression>
	</variable>
	<variable name="EstoqueDevolucao" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueDevolucao} == null ? 0.00 : $F{estoqueDevolucao}]]></variableExpression>
	</variable>
	<variable name="EstoqueReservadoDevolucao" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueReservadoDevolucao} == null ? 0.00 : $F{estoqueReservadoDevolucao}]]></variableExpression>
	</variable>
	<variable name="EstoqueFisico" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueFisico} == null ? 0.00 : $F{estoqueFisico}]]></variableExpression>
	</variable>
	<variable name="EstoqueDisponivel" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$V{estoqueDisponivel}]]></variableExpression>
	</variable>
	<variable name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa">
		<variableExpression><![CDATA[$F{id}.getEstoqueEmpresa().getId().getEmpresa()]]></variableExpression>
	</variable>
	<variable name="EstoqueFisicoEmpresa" class="java.lang.Double" resetType="Group" resetGroup="grupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueFisico} == null ? 0.00 : $F{estoqueFisico}]]></variableExpression>
	</variable>
	<variable name="EstoqueReservadoDevolucaoEmpresa" class="java.lang.Double" resetType="Group" resetGroup="grupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueReservadoDevolucao} == null ? 0.00 : $F{estoqueReservadoDevolucao}]]></variableExpression>
	</variable>
	<variable name="EstoqueDevolucaoEmpresa" class="java.lang.Double" resetType="Group" resetGroup="grupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueDevolucao} == null ? 0.00 : $F{estoqueDevolucao}]]></variableExpression>
	</variable>
	<variable name="EstoqueReservadoEmpresa" class="java.lang.Double" resetType="Group" resetGroup="grupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueReservado} == null ? 0.00 : $F{estoqueReservado}]]></variableExpression>
	</variable>
	<variable name="EstoqueEncomendadoEmpresa" class="java.lang.Double" resetType="Group" resetGroup="grupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueEncomendado} == null ? 0.00 : $F{estoqueEncomendado}]]></variableExpression>
	</variable>
	<variable name="EstoqueDisponivelEmpresa" class="java.lang.Double" resetType="Group" resetGroup="grupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[$V{estoqueDisponivel}]]></variableExpression>
	</variable>
	<variable name="EstoqueFisicoDeposito" class="java.lang.Double" resetType="Group" resetGroup="deposito" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueFisico} == null ? 0.00 : $F{estoqueFisico}]]></variableExpression>
	</variable>
	<variable name="EstoqueReservadoDevolucaoDeposito" class="java.lang.Double" resetType="Group" resetGroup="deposito" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueReservadoDevolucao} == null ? 0.00 : $F{estoqueReservadoDevolucao}]]></variableExpression>
	</variable>
	<variable name="EstoqueDevolucaoDeposito" class="java.lang.Double" resetType="Group" resetGroup="deposito" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueDevolucao} == null ? 0.00 : $F{estoqueDevolucao}]]></variableExpression>
	</variable>
	<variable name="EstoqueReservadoDeposito" class="java.lang.Double" resetType="Group" resetGroup="deposito" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueReservado} == null ? 0.00 : $F{estoqueReservado}]]></variableExpression>
	</variable>
	<variable name="EstoqueEncomendadoDeposito" class="java.lang.Double" resetType="Group" resetGroup="deposito" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueEncomendado} == null ? 0.00 : $F{estoqueEncomendado}]]></variableExpression>
	</variable>
	<variable name="EstoqueDisponivelDeposito" class="java.lang.Double" resetType="Group" resetGroup="deposito" calculation="Sum">
		<variableExpression><![CDATA[$V{estoqueDisponivel}]]></variableExpression>
	</variable>
	<group name="grupoEmpresa">
		<groupExpression><![CDATA[$V{empresa}.getCodigo()]]></groupExpression>
		<groupHeader>
			<band height="31" splitType="Stretch">
				<rectangle radius="5">
					<reportElement key="rectangle-1" mode="Opaque" x="0" y="16" width="555" height="14" forecolor="#000000" backcolor="#FFFFFF" uuid="0ccf14fa-2e3c-4a12-8b12-430d67573880"/>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</rectangle>
				<textField evaluationTime="Group" evaluationGroup="grupoEmpresa" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-109" mode="Opaque" x="0" y="0" width="555" height="14" forecolor="#000000" backcolor="#CCCCCC" uuid="a8ca41d2-06ab-4822-bd98-9250d58f3a0a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*DadosDoGrupoDeEstoque*/$V{BUNDLE}.getStringApplication("rotulo_grupo_estoque")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="grupoEmpresa" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-110" mode="Transparent" x="144" y="18" width="55" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="02ebd3a5-fc41-471f-8a7d-576d8c3b8e92"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*EstoqueFisico*/$V{BUNDLE}.getStringApplication("rotulo_estoque_fisico_abv")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="grupoEmpresa" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-111" mode="Transparent" x="202" y="18" width="55" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="dfb58ea5-c071-4275-8b40-3112fdad277c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*EstoqueReservado*/$V{BUNDLE}.getStringApplication("rotulo_estoque_reservado_abv")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="grupoEmpresa" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-112" mode="Transparent" x="260" y="18" width="55" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="ebd7a32f-c077-4221-aa6f-e2ca8530eda9"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*EstoqueEncomendado*/$V{BUNDLE}.getStringApplication("rotulo_estoque_encomendado_abv")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="grupoEmpresa" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-113" mode="Transparent" x="319" y="18" width="55" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="29e0cadc-4e11-4d9d-a18d-c1d8101b624e"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*EstoqueDisponível*/$V{BUNDLE}.getStringApplication("rotulo_estoque_disponivel_abv")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="grupoEmpresa" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-125" mode="Transparent" x="3" y="18" width="87" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="88e9a04e-32a0-4450-ac6c-18125159ff5c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Deposito / GrupoEstoque*/
RepositoryComponentDefault.SIM.equals($P{exibirLotes})
?
    $V{BUNDLE}.getStringApplication("rotulo_deposito") + " - " +
    $V{BUNDLE}.getStringApplication("rotulo_grupo_estoque")
:
    $V{BUNDLE}.getStringApplication("rotulo_deposito")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="grupoEmpresa" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-110" mode="Transparent" x="92" y="18" width="48" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="0f30c59f-143a-4150-a20e-b61bd1e9bd75"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*DataValidade*/$V{BUNDLE}.getStringApplication("rotulo_data_validade_abv")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="grupoEmpresa" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-113" mode="Transparent" x="377" y="18" width="115" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="4092a051-6ee9-4d8b-a051-7bf5bab96407"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Laboratorio fabricante*/$V{BUNDLE}.getStringApplication("rotulo_laboratorio_fabricante_abv")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="grupoEmpresa" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-113" mode="Transparent" x="496" y="18" width="57" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="4092a051-6ee9-4d8b-a051-7bf5bab96407">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{utilizaLocalizacaoEstoque})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Localização*/$V{BUNDLE}.getStringApplication("rotulo_localizacao")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="15" splitType="Stretch">
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-128" mode="Transparent" x="76" y="3" width="64" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="16c4f563-6e47-4fb6-ad43-997cd83f9222"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Totais*/$V{BUNDLE}.getStringApplication("rotulo_total_unidade") +":"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern=" #,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-129" mode="Transparent" x="142" y="3" width="57" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="eda77732-73ee-4c3a-bf4c-5786d819a294"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{EstoqueFisicoEmpresa}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern=" #,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-130" mode="Transparent" x="200" y="3" width="57" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="28c198e3-6234-4321-a11d-f0f599f27317"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{EstoqueReservadoEmpresa}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern=" #,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-131" mode="Transparent" x="258" y="3" width="57" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="f0753b7f-d5d2-4c63-b518-cd6478d0f962"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{EstoqueEncomendadoEmpresa}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern=" #,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-132" mode="Transparent" x="317" y="3" width="57" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="4baf1f98-3a5d-45d3-a63a-88be2f3792d0"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{EstoqueDisponivelEmpresa}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-2" mode="Opaque" x="76" y="2" width="299" height="1" forecolor="#000000" backcolor="#FFFFFF" uuid="c87065a4-8b7e-4fe1-8569-066eb3cae0b9"/>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="deposito">
		<groupExpression><![CDATA[$F{roDeposito}.getCodigo()]]></groupExpression>
		<groupFooter>
			<band height="15">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{exibirLotes})]]></printWhenExpression>
				<line>
					<reportElement key="line-2" mode="Opaque" x="76" y="2" width="299" height="1" forecolor="#000000" backcolor="#FFFFFF" uuid="b10d2a0b-aa07-460b-a571-2a2bcfff3598"/>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-128" mode="Transparent" x="76" y="3" width="64" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="f7150998-223a-4701-90b7-fb72b2370e3e"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Totais*/$V{BUNDLE}.getStringApplication("rotulo_total_deposito") +":"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern=" #,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-129" mode="Transparent" x="142" y="3" width="57" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="c6f70792-beb8-4da8-bef6-0a42ca4f770a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{EstoqueFisicoDeposito}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern=" #,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-130" mode="Transparent" x="200" y="3" width="57" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="bed4f92b-4c6d-4285-8178-d92755e638c7"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{EstoqueReservadoDeposito}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern=" #,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-131" mode="Transparent" x="258" y="3" width="57" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="d95da208-a648-4a33-8273-ade7a5d9b274"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{EstoqueEncomendadoDeposito}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern=" #,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-132" mode="Transparent" x="317" y="3" width="57" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="d3bea49c-0868-4718-b419-d5b6780ae27f"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{EstoqueDisponivelDeposito}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="11" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern=" #,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-115" mode="Transparent" x="144" y="1" width="55" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="f6892976-6d8d-4161-8586-a1475414e9a3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{estoqueFisico} == null ? 0.00 : $F{estoqueFisico}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-116" mode="Transparent" x="202" y="1" width="55" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="7d95b8b0-9246-43b1-b656-ead3326c4aa4"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{estoqueReservado} == null ? 0.00 : $F{estoqueReservado}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern=" #,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-117" mode="Transparent" x="260" y="1" width="55" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="f4567399-f991-4a78-95ff-97a61b156264"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{estoqueEncomendado} == null ? 0.00 : $F{estoqueEncomendado}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern=" #,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-118" mode="Transparent" x="319" y="1" width="55" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="5f65bdb8-5ee7-495f-adca-b9ae1dc9b6ce"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{estoqueDisponivel}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern=" #,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-126" mode="Transparent" x="4" y="1" width="86" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="72493af5-e559-4c94-b32e-224ce599c68a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{exibirLotes})
?
    "("+$F{roDeposito}.getCodigo()+")"+$F{roDeposito}.getDescricao() +" - "+ $F{id}.getGrupo()
:
    "("+$F{roDeposito}.getCodigo()+")"+$F{roDeposito}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-115" mode="Transparent" x="92" y="1" width="48" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="cde791eb-cce8-4f7a-95f2-fe752fcc5b53"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.formatar($F{dataValidade})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-118" mode="Transparent" x="377" y="1" width="115" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="59576963-e94d-4801-b1f3-daec4f0da51f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{laboratorioFabricante}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-118" mode="Transparent" x="496" y="1" width="57" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="59576963-e94d-4801-b1f3-daec4f0da51f">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{utilizaLocalizacaoEstoque})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{id}.getLocalizacaoEstrutura().getMascara()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="15" splitType="Stretch">
			<printWhenExpression><![CDATA[1 == 2]]></printWhenExpression>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-44" mode="Transparent" x="76" y="3" width="64" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="5691d3bc-7c5b-4d11-861c-e1e1411f2aba"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Totais*/$V{BUNDLE}.getStringApplication("rotulo_total_geral") +":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern=" #,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-120" mode="Transparent" x="142" y="3" width="57" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="86ef71ee-1f59-47b2-88c3-34a55c608613"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{EstoqueFisico}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern=" #,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-121" mode="Transparent" x="200" y="3" width="57" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="11b715bd-ff04-41a6-8a43-8be100c7dbbd"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{EstoqueReservado}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern=" #,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-122" mode="Transparent" x="258" y="3" width="57" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="c6c378ef-2317-4a71-a569-16fe58f1a836"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{EstoqueEncomendado}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern=" #,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-123" mode="Transparent" x="317" y="3" width="57" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="7af1fae2-e3cb-4709-ad5c-ce035b270a7f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{EstoqueDisponivel}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line-1" mode="Opaque" x="76" y="2" width="299" height="1" forecolor="#000000" backcolor="#FFFFFF" uuid="7dfaccef-3900-4155-8fac-ddafa6157999"/>
				<graphicElement fill="Solid">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</line>
		</band>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
