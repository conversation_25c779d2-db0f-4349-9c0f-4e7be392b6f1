<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_controle_inventario" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="3197ba51-d7bf-472f-8cbd-************">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="3.797498335832439"/>
	<property name="ireport.x" value="1874"/>
	<property name="ireport.y" value="49"/>
	<import value="br.com.ksisolucoes.vo.entradas.estoque.ControleInventario"/>
	<import value="br.com.ksisolucoes.vo.entradas.estoque.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.report.ReportProperties"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="ParamFormaApresentacao" class="java.lang.String" isForPrompting="false"/>
	<parameter name="situacao" class="java.lang.Long"/>
	<parameter name="utilizaLocalizacaoEstoque" class="java.lang.Boolean"/>
	<field name="class" class="java.lang.Object"/>
	<field name="codigoEmpresa" class="java.lang.String"/>
	<field name="codigoGrupoProduto" class="java.lang.Long"/>
	<field name="codigoLocalizacao" class="java.lang.Long"/>
	<field name="codigoProduto" class="java.lang.String"/>
	<field name="codigoSubGrupo" class="java.lang.Long"/>
	<field name="codigoUsuario" class="java.lang.Long"/>
	<field name="descricaoGrupo" class="java.lang.String"/>
	<field name="descricaoGrupoProduto" class="java.lang.String"/>
	<field name="descricaoLocalizacao" class="java.lang.String"/>
	<field name="descricaoProduto" class="java.lang.String"/>
	<field name="descricaoProdutoFormatado" class="java.lang.String"/>
	<field name="descricaoSubGrupo" class="java.lang.String"/>
	<field name="nomeEmpresa" class="java.lang.String"/>
	<field name="nomeUsuario" class="java.lang.String"/>
	<field name="descricaoEmpresa" class="java.lang.String"/>
	<field name="descricaoUsuario" class="java.lang.String"/>
	<field name="statusFormatado" class="java.lang.String"/>
	<field name="dataLancamento" class="java.util.Date"/>
	<field name="quantidade" class="java.lang.Double"/>
	<field name="grupoEstoque" class="java.lang.String"/>
	<field name="descricaoUsuarioProcessamento" class="java.lang.String"/>
	<field name="dataProcessamento" class="java.util.Date"/>
	<field name="descricaoDeposito" class="java.lang.String"/>
	<field name="localizacaoEstrutura" class="br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura"/>
	<field name="estoqueDivergente" class="java.lang.Double"/>
	<field name="estoque" class="java.lang.Double"/>
	<variable name="valorTotalGrupoEmpresaGeral" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresaGeral" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueDivergente}]]></variableExpression>
	</variable>
	<variable name="valorTotalGrupoUsuario" class="java.lang.Double" resetType="Group" resetGroup="grupoUsuario" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueDivergente}]]></variableExpression>
	</variable>
	<variable name="valorTotalGrupoGrupo" class="java.lang.Double" resetType="Group" resetGroup="GrupoGrupo" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueDivergente}]]></variableExpression>
	</variable>
	<variable name="valorTotalGeral" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueDivergente}]]></variableExpression>
	</variable>
	<variable name="valorEstoqueGrupoEmpresaGeral" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresaGeral" calculation="Sum">
		<variableExpression><![CDATA[$F{estoque}]]></variableExpression>
	</variable>
	<variable name="valorEstoqueGrupoUsuario" class="java.lang.Double" resetType="Group" resetGroup="grupoUsuario" calculation="Sum">
		<variableExpression><![CDATA[$F{estoque}]]></variableExpression>
	</variable>
	<variable name="valorEstoqueGrupoGrupo" class="java.lang.Double" resetType="Group" resetGroup="GrupoGrupo" calculation="Sum">
		<variableExpression><![CDATA[$F{estoque}]]></variableExpression>
	</variable>
	<variable name="valorEstoqueGeral" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{estoque}]]></variableExpression>
	</variable>
	<variable name="valorQtdGrupoEmpresaGeral" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresaGeral" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<variable name="valorQtdGrupoUsuario" class="java.lang.Double" resetType="Group" resetGroup="grupoUsuario" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<variable name="valorQtdGrupoGrupo" class="java.lang.Double" resetType="Group" resetGroup="GrupoGrupo" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<variable name="valorQtdGeral" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<group name="default">
		<groupHeader>
			<band/>
		</groupHeader>
		<groupFooter>
			<band height="10">
				<textField>
					<reportElement x="603" y="1" width="40" height="9" uuid="1f235b32-78b6-4bea-bdb6-3009141af97c">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="6" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_geral")]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0;-#,##0" isBlankWhenNull="true">
					<reportElement x="750" y="1" width="50" height="9" uuid="15e6753b-1c29-4512-a059-4364245dd484">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorTotalGeral}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="611" y="0" width="190" height="1" uuid="a40e3382-379c-4309-8e4f-c35ca00c77ce">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="#,##0;-#,##0" isBlankWhenNull="true">
					<reportElement x="699" y="1" width="50" height="9" uuid="048adfa3-262b-41cd-b7a6-647570b034ee">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorEstoqueGeral}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0;-#,##0" isBlankWhenNull="true">
					<reportElement x="645" y="1" width="50" height="9" uuid="90547767-d1a2-41e3-96fc-f11e6a04f667">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorQtdGeral}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="GrupoEmpresa" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{codigoEmpresa}]]></groupExpression>
		<groupHeader>
			<band height="14" splitType="Stretch">
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-43" mode="Opaque" x="0" y="0" width="802" height="14" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="139c9892-42e6-41d0-b66a-a4b483b81c86">
						<printWhenExpression><![CDATA[!$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*empresa*/Bundle.getStringApplication("rotulo_empresa") + ":" + " " + $F{descricaoEmpresa}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<group name="GrupoEmpresaGeral">
		<groupExpression><![CDATA[$F{codigoEmpresa}]]></groupExpression>
		<groupHeader>
			<band height="27" splitType="Stretch">
				<rectangle radius="5">
					<reportElement key="rectangle-2" mode="Opaque" x="0" y="14" width="802" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="98ba5a98-2821-4282-ad9c-33c5d5575c39">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</rectangle>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-59" mode="Opaque" x="2" y="15" width="180" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="a7b17448-0fb4-4038-8540-5ea548d16b91">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<box rightPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*produto*/Bundle.getStringApplication("rotulo_produto")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-61" mode="Opaque" x="271" y="15" width="35" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="32053797-effd-4c0b-9fa8-eadc1a46bf75">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<box leftPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*situacao*/Bundle.getStringApplication("rotulo_situacao")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-62" mode="Opaque" x="308" y="15" width="40" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="2b1b8e6e-3c68-4bbd-b144-6356610025bf">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*dataLancamento*/Bundle.getStringApplication("rotulo_data_lancamento_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-63" mode="Opaque" x="350" y="15" width="104" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="c5ad8f8e-bb5a-4837-9e2a-fa6e3b33d21b">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*usuario*/Bundle.getStringApplication("rotulo_usuario")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-65" mode="Opaque" x="602" y="15" width="40" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="0f089d0a-c9db-4a39-b01a-11575fed39f4">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*localizacao*/Bundle.getStringApplication("rotulo_localizacao")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-66" mode="Opaque" x="0" y="0" width="802" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="ecc61426-d869-462d-af9e-da7e267fcd3c">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*empresa*/Bundle.getStringApplication("rotulo_empresa") + ":" + " " + $F{descricaoEmpresa}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-60" mode="Opaque" x="238" y="15" width="31" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="392d69c4-a98f-4b7c-906f-fb35ed487818">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*lote*/Bundle.getStringApplication("rotulo_lote")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-79" mode="Opaque" x="454" y="15" width="104" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="cdca5849-0168-465d-971a-4ad42f931f9d">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL) &&
$P{situacao}.equals(ControleInventario.STATUS_PROCESSADO)]]></printWhenExpression>
					</reportElement>
					<box leftPadding="1">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*usuarioProcessamento*/Bundle.getStringApplication("rotulo_usuario_processamento_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-79" mode="Opaque" x="560" y="15" width="40" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="3cc7e59b-68ab-4b55-9015-3a925920c31f">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL) &&
$P{situacao}.equals(ControleInventario.STATUS_PROCESSADO)]]></printWhenExpression>
					</reportElement>
					<box leftPadding="1">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*dataProcessamento*/Bundle.getStringApplication("rotulo_data_processamento_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-47" mode="Opaque" x="182" y="15" width="54" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="d93645f2-d226-4c92-8064-c42b8dc89974">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*deposito*/Bundle.getStringApplication("rotulo_deposito")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-60" mode="Opaque" x="645" y="15" width="50" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="b64e4b44-cd9e-4219-9b1c-e49c0614332b">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*quantidade*/Bundle.getStringApplication("rotulo_quantidade")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-60" mode="Opaque" x="750" y="15" width="50" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="255d5edf-a08c-431f-b6f2-e202c4e8c046">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*diferenca*/Bundle.getStringApplication("rotulo_diferenca")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-60" mode="Opaque" x="699" y="15" width="50" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="6b92154b-3418-4911-99b0-23f0086d055b">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*estoque*/Bundle.getStringApplication("rotulo_estoque")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="10" splitType="Stretch">
				<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
				<line>
					<reportElement x="611" y="0" width="190" height="1" uuid="bbbeb2e1-18c0-4575-a61b-9c9e03618d60">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="#,##0;-#,##0" isBlankWhenNull="true">
					<reportElement x="750" y="1" width="50" height="9" uuid="427300cd-e25b-4400-b03a-08f8268989bf">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorTotalGrupoEmpresaGeral}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="603" y="1" width="40" height="9" uuid="722fef69-85e4-4860-9ed7-6697cbfcc674">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="6" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0;-#,##0" isBlankWhenNull="true">
					<reportElement x="645" y="1" width="50" height="9" uuid="be44ca67-15ff-4793-b725-a15e20b9eae4">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorQtdGrupoEmpresaGeral}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0;-#,##0" isBlankWhenNull="true">
					<reportElement x="699" y="1" width="50" height="9" uuid="d84dc758-1430-4525-b76d-9971fbd67237">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals(ReportProperties.FORMA_APRESENTACAO_GERAL)]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorEstoqueGrupoEmpresaGeral}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="grupoUsuario">
		<groupExpression><![CDATA[$F{codigoUsuario}]]></groupExpression>
		<groupHeader>
			<band height="30" splitType="Stretch">
				<textField evaluationTime="Group" evaluationGroup="grupoUsuario" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-46" mode="Opaque" x="0" y="1" width="802" height="15" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="475b0985-2e3a-4584-8dcf-60935072361e">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( Produto.PROP_USUARIO )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*usuario*/
Bundle.getStringApplication("rotulo_usuario")  + ": " + $F{descricaoUsuario}]]></textFieldExpression>
				</textField>
				<rectangle radius="5">
					<reportElement key="rectangle-3" mode="Opaque" x="0" y="16" width="802" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="b9a11947-273d-4f37-8a7c-d53d5dd072e4">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( Produto.PROP_USUARIO )]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</rectangle>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-67" mode="Opaque" x="2" y="17" width="180" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="e84a9b0d-18ca-4810-a940-6039da8335c8">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( Produto.PROP_USUARIO )]]></printWhenExpression>
					</reportElement>
					<box rightPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*produto*/Bundle.getStringApplication("rotulo_produto")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-69" mode="Opaque" x="271" y="17" width="35" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="066adde0-05d1-4b2f-81d0-3fec3e98118a">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( Produto.PROP_USUARIO )]]></printWhenExpression>
					</reportElement>
					<box leftPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*situacao*/Bundle.getStringApplication("rotulo_situacao")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-70" mode="Opaque" x="308" y="17" width="40" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="048143b2-ab10-41c7-94b8-8c60787ae898">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( Produto.PROP_USUARIO )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*dataLancamento*/Bundle.getStringApplication("rotulo_data_lancamento_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-71" mode="Opaque" x="350" y="17" width="104" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="01b987c6-7113-4c81-9567-a581738bf18e">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( Produto.PROP_USUARIO )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*usuario*/Bundle.getStringApplication("rotulo_usuario")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-73" mode="Opaque" x="602" y="17" width="40" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="ae063d45-56a3-44a8-87f3-b5e04bfa4f18">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( Produto.PROP_USUARIO )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*localizacao*/Bundle.getStringApplication("rotulo_localizacao")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-68" mode="Opaque" x="238" y="17" width="31" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="1bda3ce4-c0c3-43ad-90a3-007470b46d60">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( Produto.PROP_USUARIO )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*lote*/Bundle.getStringApplication("rotulo_lote")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-79" mode="Opaque" x="454" y="17" width="104" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="fa97d5f4-42fa-47ad-bc7c-cb5baecc6374">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( Produto.PROP_USUARIO ) &&
$P{situacao}.equals(ControleInventario.STATUS_PROCESSADO)]]></printWhenExpression>
					</reportElement>
					<box leftPadding="1">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*usuarioProcessamento*/Bundle.getStringApplication("rotulo_usuario_processamento_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-79" mode="Opaque" x="560" y="17" width="40" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="302034bf-a826-44fd-a8c7-4a43b19dd833">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( Produto.PROP_USUARIO ) &&
$P{situacao}.equals(ControleInventario.STATUS_PROCESSADO)]]></printWhenExpression>
					</reportElement>
					<box leftPadding="1">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*dataProcessamento*/Bundle.getStringApplication("rotulo_data_processamento_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-47" mode="Opaque" x="182" y="17" width="54" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="10780ffd-aa8a-4de3-921a-efbb21e531af">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( Produto.PROP_USUARIO )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*deposito*/Bundle.getStringApplication("rotulo_deposito")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-68" mode="Opaque" x="645" y="17" width="50" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="c2f44624-27ed-4ee0-8d1d-d1473dfa4002">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( Produto.PROP_USUARIO )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*quantidade*/Bundle.getStringApplication("rotulo_quantidade")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-68" mode="Opaque" x="750" y="17" width="50" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="6f81cc97-2894-4d79-a618-338d3299e73b">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( Produto.PROP_USUARIO )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*diferenca*/Bundle.getStringApplication("rotulo_diferenca")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-68" mode="Opaque" x="699" y="17" width="50" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="8c9b3793-437d-43c5-b60a-2d181f1ec71e">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( Produto.PROP_USUARIO )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*estoque*/Bundle.getStringApplication("rotulo_estoque")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="10" splitType="Stretch">
				<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( Produto.PROP_USUARIO )]]></printWhenExpression>
				<line>
					<reportElement x="611" y="0" width="190" height="1" uuid="a4522cda-42f9-4a55-8096-b4c57c267ac5">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( Produto.PROP_USUARIO )]]></printWhenExpression>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField>
					<reportElement x="603" y="1" width="40" height="9" uuid="c9f23496-3ded-401d-87a3-a98636da7a38">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( Produto.PROP_USUARIO )]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="6" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0;-#,##0" isBlankWhenNull="true">
					<reportElement x="750" y="1" width="50" height="9" uuid="e0ad7c4c-ce52-4b62-ad72-13f8822c17ec">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( Produto.PROP_USUARIO )]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorTotalGrupoUsuario}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0;-#,##0" isBlankWhenNull="true">
					<reportElement x="645" y="1" width="50" height="9" uuid="d697ce06-1b52-479d-b093-272e0204e817">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( Produto.PROP_USUARIO )]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorQtdGrupoUsuario}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0;-#,##0" isBlankWhenNull="true">
					<reportElement x="699" y="1" width="50" height="9" uuid="de7e6ca3-692e-4636-9484-48c055401499">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( Produto.PROP_USUARIO )]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorEstoqueGrupoUsuario}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="GrupoGrupo" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{codigoSubGrupo}]]></groupExpression>
		<groupHeader>
			<band height="29" splitType="Stretch">
				<rectangle radius="5">
					<reportElement key="rectangle-1" mode="Opaque" x="0" y="15" width="802" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="86aeb064-bf9b-497a-bb30-b8763cd6e3d4">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</rectangle>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-47" mode="Opaque" x="750" y="16" width="50" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="c9739f93-3d69-4b22-acfc-9828a2458f71">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*diferenca*/Bundle.getStringApplication("rotulo_diferenca")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoGrupo" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-43" mode="Opaque" x="0" y="0" width="802" height="14" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="6ec980a1-0d07-4bd1-87a5-0e4cc821a9cd">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*grupo*/ /*subgrupo*/
$F{codigoGrupoProduto} == null  ? Bundle.getStringApplication("rotulo_grupo")  + ": " + Bundle.getStringApplication("rotulo_sem_cadastro"):
Bundle.getStringApplication("rotulo_grupo") + ":" + " " + $F{descricaoGrupoProduto} + " - " + ($F{codigoSubGrupo} == null ? Bundle.getStringApplication("rotulo_subgrupo") + ":" + Bundle.getStringApplication("rotulo_sem_cadastro"):
Bundle.getStringApplication("rotulo_subgrupo") + ":" + " " + $F{descricaoSubGrupo})]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Opaque" x="2" y="16" width="180" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="ac4e43f5-d59f-4c4f-8fab-11823bedcecb">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<box rightPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*produto*/Bundle.getStringApplication("rotulo_produto")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-48" mode="Opaque" x="271" y="16" width="35" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="3dd1b16e-657a-4712-aa6d-587de2793466">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<box leftPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*situacao*/Bundle.getStringApplication("rotulo_situacao")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-49" mode="Opaque" x="308" y="16" width="40" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="0f5f8480-b4e4-4c7e-901e-b440e27eb229">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*dataLancamento*/Bundle.getStringApplication("rotulo_data_lancamento_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-50" mode="Opaque" x="350" y="16" width="104" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="13acb213-174f-495d-adcd-2a7d79ed4d38">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*usuario*/Bundle.getStringApplication("rotulo_usuario")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-52" mode="Opaque" x="603" y="16" width="40" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="cc789a43-eecd-4395-8fad-0851677c23e4">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*localizacao*/Bundle.getStringApplication("rotulo_localizacao")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-47" mode="Opaque" x="238" y="16" width="31" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="6283bb2e-b7d5-40ab-9533-85f1367186d2">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*lote*/Bundle.getStringApplication("rotulo_lote")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-79" mode="Opaque" x="454" y="16" width="104" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="c37029b8-bec5-4529-9bf8-6bf11dfeee8d">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) ) &&
$P{situacao}.equals(ControleInventario.STATUS_PROCESSADO)]]></printWhenExpression>
					</reportElement>
					<box leftPadding="1">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*usuarioProcessamento*/Bundle.getStringApplication("rotulo_usuario_processamento_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-79" mode="Opaque" x="560" y="16" width="40" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="06ce2def-f114-48d3-bbc2-6cc25e0d853c">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) ) &&
$P{situacao}.equals(ControleInventario.STATUS_PROCESSADO)]]></printWhenExpression>
					</reportElement>
					<box leftPadding="1">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*dataProcessamento*/Bundle.getStringApplication("rotulo_data_processamento_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-47" mode="Opaque" x="182" y="16" width="54" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f7d71910-8082-441e-a48f-100849f6b1df">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*deposito*/Bundle.getStringApplication("rotulo_deposito")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-47" mode="Opaque" x="645" y="16" width="50" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="af06241f-30e2-4130-b4dc-08cca8199466">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*quantidade*/Bundle.getStringApplication("rotulo_quantidade")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-47" mode="Opaque" x="699" y="16" width="50" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="ff927e14-da16-4dd0-9833-e46cf7e9da7a">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*estoque*/Bundle.getStringApplication("rotulo_estoque")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="10" splitType="Stretch">
				<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
				<line>
					<reportElement x="611" y="0" width="190" height="1" uuid="9ad5ad7c-c661-41f4-bb99-51d0c2157863">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField>
					<reportElement x="603" y="1" width="40" height="9" uuid="83fd1119-d94a-4f5d-b455-ce50a089f155">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="6" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0;-#,##0" isBlankWhenNull="true">
					<reportElement x="750" y="1" width="50" height="9" uuid="c0be3ce1-e685-47db-af3d-23fad121fff5">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorTotalGrupoGrupo}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0;-#,##0" isBlankWhenNull="true">
					<reportElement x="699" y="1" width="50" height="9" uuid="582f54fe-c9ae-4017-8787-4a118b00692e">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorEstoqueGrupoGrupo}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0;-#,##0" isBlankWhenNull="true">
					<reportElement x="645" y="1" width="50" height="9" uuid="c447b020-**************-031724749847">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO) )]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorQtdGrupoGrupo}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="9" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-5" mode="Opaque" x="2" y="0" width="180" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="68f29258-8317-454a-9a41-81020f7bf9de"/>
				<box rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoProdutoFormatado}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-54" mode="Opaque" x="271" y="0" width="35" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="00cffcf8-1951-4e65-8ab5-f8c52f7b69c4"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{statusFormatado}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-55" mode="Opaque" x="308" y="0" width="40" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="fba55066-f923-4e5f-857f-f9c3a83045e7"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.formatar($F{dataLancamento})]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-56" mode="Opaque" x="350" y="0" width="104" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="be557ec7-124a-4644-a07f-98f4f6afb39d"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoUsuario}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-58" mode="Opaque" x="603" y="0" width="40" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="49d81e68-ba4a-44ae-ab5d-177f94bd5dc4">
					<printWhenExpression><![CDATA[$P{utilizaLocalizacaoEstoque}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{localizacaoEstrutura}.getMascara()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-53" mode="Opaque" x="238" y="0" width="31" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="86071daa-3686-49bf-bd1c-de4057af2fcd"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{grupoEstoque}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-81" mode="Opaque" x="454" y="0" width="104" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="6421c8a1-579f-4b2e-afe4-169836f3fedd">
					<printWhenExpression><![CDATA[$P{situacao}.equals(ControleInventario.STATUS_PROCESSADO)]]></printWhenExpression>
				</reportElement>
				<box leftPadding="1">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoUsuarioProcessamento}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-81" mode="Opaque" x="560" y="0" width="40" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="011716a0-1a08-4127-8fc9-48842cae3ae7">
					<printWhenExpression><![CDATA[$P{situacao}.equals(ControleInventario.STATUS_PROCESSADO)]]></printWhenExpression>
				</reportElement>
				<box leftPadding="1">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.formatar($F{dataProcessamento})]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-53" mode="Opaque" x="182" y="0" width="54" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="1bcac3f0-34b8-4964-8952-f2d1e0a50d10"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoDeposito}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0;-#,##0" isBlankWhenNull="true">
				<reportElement key="textField-53" mode="Opaque" x="645" y="0" width="50" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="1491bde8-30d8-4a0d-9261-5889277f0b9b"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidade}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0;-#,##0" isBlankWhenNull="true">
				<reportElement key="textField-53" mode="Opaque" x="750" y="0" width="50" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="a3a90270-8027-4956-aefa-3797888c0288"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{estoqueDivergente}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0;-#,##0" isBlankWhenNull="true">
				<reportElement key="textField-53" mode="Opaque" x="699" y="0" width="50" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="02ee82d9-6810-46c0-82d3-ead349ec2547"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{estoque}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
