<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="conformidade_tecnica" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="caf7170b-e82f-4514-a1a9-550b053d892a">
	<property name="ireport.zoom" value="1.948717100000031"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="DESC_CABECALHO_PADRAO" class="java.lang.String"/>
	<parameter name="CIDADE_UNIDADE" class="java.lang.String"/>
	<parameter name="UF_UNIDADE" class="java.lang.String"/>
	<field name="requerimentoAnaliseProjeto" class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAnaliseProjeto"/>
	<field name="requerimentoVigilancia" class="br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia"/>
	<field name="vigilanciaEndereco" class="br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco">
		<fieldDescription><![CDATA[requerimentoVigilancia.vigilanciaEndereco]]></fieldDescription>
	</field>
	<field name="dataRodape" class="java.lang.String"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<detail>
		<band height="124">
			<rectangle radius="10">
				<reportElement x="0" y="0" width="555" height="110" uuid="63d51cbe-f4a1-4698-8ea1-ca9bb65745fc"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="13" y="8" width="120" height="16" uuid="6c638a28-47f0-49d1-b08d-78d994c4e74c"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_parecer_nr").toUpperCase() + ":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="13" y="41" width="166" height="16" uuid="0b08cc38-d5ce-4a11-969f-9499de216d85"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_estabelecimento").toUpperCase() + ":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="13" y="73" width="166" height="16" uuid="2bf52c14-683c-4382-afb8-a84b0e7ed89f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_endereco").toUpperCase() + ":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="143" y="8" width="134" height="16" uuid="d89181a3-ea7a-48f9-8eee-043259079585"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_inspecao").toUpperCase() + ":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-10" x="13" y="24" width="120" height="16" uuid="d43978bb-53c2-4d54-ab7a-a9755f8a7c1d"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{requerimentoAnaliseProjeto}.getNumeracaoConformidadeTecnicaFormatado()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement key="textField-10" x="143" y="24" width="134" height="16" uuid="d3f8526f-3008-4b8d-bb62-2fe418a0dee8"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{requerimentoAnaliseProjeto}.getDataInspecao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="13" y="88" width="530" height="16" uuid="6bcea72c-8265-4d2a-ba62-b1a963adecf6"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{vigilanciaEndereco}.getEnderecoFormatadoComCidade()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-10" x="13" y="56" width="530" height="16" uuid="2ee80dca-ec4b-41ce-bbb2-8bd7c985d4a0"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{requerimentoVigilancia}.getNome()]]></textFieldExpression>
			</textField>
			<textField pattern="##/####" isBlankWhenNull="true">
				<reportElement key="textField-10" x="289" y="24" width="150" height="16" uuid="a99f46fa-9d80-4992-96f3-868f408b92bb"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{requerimentoAnaliseProjeto}.getNumeracaoParecerTecnicoFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="289" y="8" width="150" height="16" uuid="01f025d9-cdb7-49dd-9d34-0bf80e5b36d3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_parecer_tecnico_nr").toUpperCase() + ":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-10" x="451" y="24" width="100" height="16" uuid="de40f497-3b93-4fce-9b47-c0f02f8a5188"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{requerimentoVigilancia}.getProtocoloFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="451" y="8" width="100" height="16" uuid="aa6d454e-a049-4aff-aece-1c29d7e19df8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_protocolo").toUpperCase() + ":"]]></textFieldExpression>
			</textField>
		</band>
		<band height="34" splitType="Prevent">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-9" x="0" y="0" width="555" height="16" uuid="6d9577fb-44f7-436c-b5b8-a47fe1ace3e0"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["1. " + Bundle.getStringApplication("rotulo_caracterizacao_edificacao_unidade_inspecionada_correspondencia_aprovado") + ":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="0" y="17" width="555" height="17" uuid="c7ec6c0f-b6b2-43e6-be73-01b3cc60a440"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{requerimentoAnaliseProjeto}.getDescricaoCaracterizacao()]]></textFieldExpression>
			</textField>
		</band>
		<band height="30">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-9" x="0" y="14" width="555" height="16" uuid="d1fc77dd-6730-4456-9b38-eb3e5f298441"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataRodape}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
