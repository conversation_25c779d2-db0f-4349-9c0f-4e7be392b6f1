<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relacao_viagens" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="15254c57-44cd-400c-918c-8260d86c6759">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.9487171000000025"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.celk.util.Coalesce"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="FORMA_APRESENTACAO" class="br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioRelacaoViagensDTOParam.FormaApresentacao"/>
	<field name="roteiroViagem" class="br.com.ksisolucoes.vo.frota.RoteiroViagem"/>
	<field name="veiculoFA" class="java.lang.String"/>
	<field name="motoristaFA" class="java.lang.String"/>
	<field name="numeroPassageiros" class="java.lang.Long"/>
	<field name="destinoFA" class="java.lang.String"/>
	<field name="roteiroViagemPassageiroList" class="java.util.List"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="FA" class="br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioRelacaoViagensDTOParam.FormaApresentacao"/>
	<variable name="totalNumeroPassageirosFA" class="java.lang.Long" resetType="Group" resetGroup="FA" calculation="Sum">
		<variableExpression><![CDATA[$F{numeroPassageiros}]]></variableExpression>
	</variable>
	<variable name="totalGeralNumeroPassageiros" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{numeroPassageiros}]]></variableExpression>
	</variable>
	<variable name="totalViagensFA" class="java.lang.Long" resetType="Group" resetGroup="FA" calculation="Count">
		<variableExpression><![CDATA[$F{roteiroViagem}.getCodigo()]]></variableExpression>
	</variable>
	<variable name="totalGeralViagensFA" class="java.lang.Long" calculation="Count">
		<variableExpression><![CDATA[$F{roteiroViagem}.getCodigo()]]></variableExpression>
	</variable>
	<group name="GERAL">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupFooter>
			<band height="29">
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="371" y="8" width="120" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="64d55cbc-c4ad-4c03-957f-04697f2606d4"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_total_geral_nr_passageiros") + ": "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="491" y="8" width="62" height="10" uuid="31d4de07-5174-4980-a814-6e5b106913e9"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalGeralNumeroPassageiros}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="384" y="5" width="171" height="1" uuid="4c81be06-af8a-4ec5-aa2a-a9f69779c3fd"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField>
					<reportElement x="491" y="19" width="62" height="10" uuid="8a0e53ac-827e-481d-9439-cd7c7468c08c"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalGeralViagensFA}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="371" y="19" width="120" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="9ddc3355-60ab-4951-8904-8adf4fc3254e"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_total_geral_viagens") + ": "]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="FA" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[$V{FA}.VEICULO.equals($P{FORMA_APRESENTACAO})
?
    $F{roteiroViagem}.getVeiculo().getDescricao()
:
    $V{FA}.MOTORISTA.equals($P{FORMA_APRESENTACAO})
    ?
        $F{roteiroViagem}.getMotorista().getNome()
    :
        $V{FA}.DESTINO.equals($P{FORMA_APRESENTACAO})
        ?
            $F{destinoFA}
        :
            null]]></groupExpression>
		<groupHeader>
			<band height="19">
				<printWhenExpression><![CDATA[!$V{FA}.GERAL.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
				<textField isBlankWhenNull="false">
					<reportElement x="1" y="1" width="554" height="15" uuid="b8077e9f-026c-4894-ac96-7b1602e82865">
						<printWhenExpression><![CDATA[!$V{FA}.GERAL.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
					</reportElement>
					<textElement>
						<font fontName="Arial" isBold="true" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{FA}.VEICULO.equals($P{FORMA_APRESENTACAO})
?
    $F{veiculoFA}
:
    $V{FA}.MOTORISTA.equals($P{FORMA_APRESENTACAO})
    ?
        $F{motoristaFA}
    :
        $V{FA}.DESTINO.equals($P{FORMA_APRESENTACAO})
        ?
            $F{destinoFA}
        :
            null]]></textFieldExpression>
				</textField>
			</band>
			<band height="13">
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="2" y="0" width="124" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="64d55cbc-c4ad-4c03-957f-04697f2606d4"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Veiculo*/
$V{BUNDLE}.getStringApplication("rotulo_veiculo")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-4" x="0" y="11" width="555" height="1" uuid="5e93a5ab-19b0-4a37-9c86-d9ba6bf304c2"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="468" y="0" width="86" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="64d55cbc-c4ad-4c03-957f-04697f2606d4"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Nr. Passageiros*/
$V{BUNDLE}.getStringApplication("rotulo_nr_passageiros")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="256" y="0" width="132" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="64d55cbc-c4ad-4c03-957f-04697f2606d4"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Destino*/
$V{BUNDLE}.getStringApplication("rotulo_destino")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="388" y="0" width="80" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="9a24839e-7c81-4416-92b3-105cff8838bc"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Dt Saída*/
$V{BUNDLE}.getStringApplication("rotulo_dt_saida")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="128" y="0" width="126" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="b1b436c1-94c4-4538-83a5-3bbd0fbb94a8"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Motorista*/
$V{BUNDLE}.getStringApplication("rotulo_motorista")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="33">
				<printWhenExpression><![CDATA[!$V{FA}.GERAL.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="371" y="8" width="120" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="64d55cbc-c4ad-4c03-957f-04697f2606d4">
						<printWhenExpression><![CDATA[!$V{FA}.GERAL.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_total_nr_passageiros") + ": "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="491" y="8" width="62" height="10" uuid="31d4de07-5174-4980-a814-6e5b106913e9">
						<printWhenExpression><![CDATA[!$V{FA}.GERAL.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalNumeroPassageirosFA}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="406" y="5" width="149" height="1" uuid="4c81be06-af8a-4ec5-aa2a-a9f69779c3fd">
						<printWhenExpression><![CDATA[!$V{FA}.GERAL.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField>
					<reportElement x="491" y="19" width="62" height="10" uuid="f7e06332-750e-4a90-825b-3447a208b984">
						<printWhenExpression><![CDATA[!$V{FA}.GERAL.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalViagensFA}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="371" y="19" width="120" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="b11fd974-4941-410f-af80-4e300d45dad3">
						<printWhenExpression><![CDATA[!$V{FA}.GERAL.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_total_viagens") + ": "]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<detail>
		<band height="26" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" stretchType="RelativeToBandHeight" mode="Transparent" x="2" y="0" width="124" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="98e104a1-b1d9-489d-bed7-19ba31dcfbe7"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{roteiroViagem}.getVeiculo().getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" stretchType="RelativeToBandHeight" mode="Transparent" x="468" y="0" width="86" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="98e104a1-b1d9-489d-bed7-19ba31dcfbe7"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{numeroPassageiros}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" stretchType="RelativeToBandHeight" mode="Transparent" x="256" y="0" width="132" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="98e104a1-b1d9-489d-bed7-19ba31dcfbe7"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{roteiroViagem}.getCidade().getDescricao() + " - " +  $F{roteiroViagem}.getCidade().getEstado().getSigla()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy HH:mm" isBlankWhenNull="true">
				<reportElement key="textField-57" stretchType="RelativeToBandHeight" mode="Transparent" x="388" y="0" width="80" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="2fb07093-7535-4230-8f7d-c27ed963d101"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{roteiroViagem}.getDataSaida()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" stretchType="RelativeToBandHeight" mode="Transparent" x="128" y="0" width="126" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="0e59a1a4-**************-9c664423cb6f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{roteiroViagem}.getMotorista().getNome()]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement stretchType="RelativeToTallestObject" x="0" y="16" width="555" height="10" uuid="9dd0464c-054b-49e9-9bae-ae6e3f20cab8">
					<printWhenExpression><![CDATA[$F{roteiroViagemPassageiroList}.size() > 0]]></printWhenExpression>
				</reportElement>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{roteiroViagemPassageiroList})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/frota/jrxml/sub_relacao_viagens.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
