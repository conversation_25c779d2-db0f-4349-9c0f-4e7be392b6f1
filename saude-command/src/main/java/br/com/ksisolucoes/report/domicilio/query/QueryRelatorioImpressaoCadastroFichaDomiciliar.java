package br.com.ksisolucoes.report.domicilio.query;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.basico.interfaces.dto.ComponentesDomicilioFichaCadastroDomiciliarDTO;
import br.com.ksisolucoes.report.basico.interfaces.dto.RelatorioImpressaoCadastroFichaDomiciliarDTO;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDomicilio;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioImpressaoCadastroFichaDomiciliar extends CommandQuery<QueryRelatorioImpressaoCadastroFichaDomiciliar> implements ITransferDataReport<Long, RelatorioImpressaoCadastroFichaDomiciliarDTO> {

    private Long codigoEnderecoDomicilio;
    private List<RelatorioImpressaoCadastroFichaDomiciliarDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) throws ValidacaoException {

        hql.addToSelect("p.nome", "profissional.nome");
        hql.addToSelect("p.codigoCns", "profissional.codigoCns");
        hql.addToSelect("e.descricao", "empresa.descricao");
        hql.addToSelect("e.cnes", "empresa.cnes");
        hql.addToSelect("eq.equipeCnes", "equipe.equipeCnes");
        hql.addToSelect("ema.microArea", "equipeMicroArea.microArea");
        hql.addToSelect("ed.dataCadastro", "dataCadastro");
        
        hql.addToSelect("tl.descricao", "enderecoUsuarioCadsus.tipoLogradouro.descricao");
        hql.addToSelect("euc.logradouro", "enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("euc.numeroLogradouro", "enderecoUsuarioCadsus.numeroLogradouro");
        hql.addToSelect("euc.bairro", "enderecoUsuarioCadsus.bairro");
        hql.addToSelect("euc.cep", "enderecoUsuarioCadsus.cep");
        hql.addToSelect("c.descricao", "enderecoUsuarioCadsus.cidade.descricao");
        hql.addToSelect("es.descricao", "enderecoUsuarioCadsus.cidade.estado.descricao");
        hql.addToSelect("es.sigla", "enderecoUsuarioCadsus.cidade.estado.sigla");        
        
        hql.addToSelect("euc.telefone", "enderecoUsuarioCadsus.telefone");
        hql.addToSelect("euc.telefoneReferencia", "enderecoUsuarioCadsus.telefoneReferencia");
        hql.addToSelect("euc.pontoReferencia", "enderecoUsuarioCadsus.pontoReferencia");
        
        hql.addToSelect("ede.situacaoMoradia", "enderecoDomicilioEsus.situacaoMoradia");
        hql.addToSelect("ede.localizacao", "enderecoDomicilioEsus.localizacao");
        hql.addToSelect("ede.condicaoUsoTerra", "enderecoDomicilioEsus.condicaoUsoTerra");
        hql.addToSelect("ede.tipoDomicilio", "enderecoDomicilioEsus.tipoDomicilio");
        hql.addToSelect("ede.numeroComodos", "enderecoDomicilioEsus.numeroComodos");
        hql.addToSelect("ede.numeroMoradores", "enderecoDomicilioEsus.numeroMoradores");
        hql.addToSelect("ede.tipoAcessoDomicilio", "enderecoDomicilioEsus.tipoAcessoDomicilio");
        hql.addToSelect("ede.possuiEnergiaEletrica", "enderecoDomicilioEsus.possuiEnergiaEletrica");
        hql.addToSelect("ede.materialDominante", "enderecoDomicilioEsus.materialDominante");
        hql.addToSelect("ede.abastecimentoAgua", "enderecoDomicilioEsus.abastecimentoAgua");
        hql.addToSelect("ede.esgotamento", "enderecoDomicilioEsus.esgotamento");
        hql.addToSelect("ede.tratamentoAgua", "enderecoDomicilioEsus.tratamentoAgua");
        hql.addToSelect("ede.destinoLixo", "enderecoDomicilioEsus.destinoLixo");
        
        hql.addToSelect("ede.quantos", "enderecoDomicilioEsus.quantos");
        hql.addToSelect("ede.gato", "enderecoDomicilioEsus.gato");
        hql.addToSelect("ede.cachorro", "enderecoDomicilioEsus.cachorro");
        hql.addToSelect("ede.passaro", "enderecoDomicilioEsus.passaro");
        hql.addToSelect("ede.criacao", "enderecoDomicilioEsus.criacao");
        hql.addToSelect("ede.outros", "enderecoDomicilioEsus.outros");

        hql.setTypeSelect(RelatorioImpressaoCadastroFichaDomiciliarDTO.class.getName());
        
        hql.addToFrom("EnderecoDomicilioEsus ede"
                + " right join ede.enderecoDomicilio ed"
                + " left join ed.enderecoUsuarioCadsus euc"
                + " left join euc.tipoLogradouro tl"
                + " left join euc.cidade c"
                + " left join c.estado es"
                + " left join ed.equipeMicroArea ema"
                + " left join ema.equipeProfissional ep"
                + " left join ep.profissional p"
                + " left join ep.equipe eq"
                + " left join eq.empresa e");

        if (this.codigoEnderecoDomicilio == null) {
            throw new ValidacaoException("Erro ao buscar informações da família");
        }
        hql.addToWhereWhithAnd("ed.codigo = ", this.codigoEnderecoDomicilio);
    }
    
    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if(CollectionUtils.isNotNullEmpty(result)){
            UsuarioCadsusDomicilio proxyUCD = on(UsuarioCadsusDomicilio.class);
            List<UsuarioCadsusDomicilio> list = LoadManager.getInstance(UsuarioCadsusDomicilio.class)
                        .addProperties(new HQLProperties(UsuarioCadsus.class, path(proxyUCD.getUsuarioCadsus())).getProperties())
                        .addProperty(path(proxyUCD.getUsuarioCadsus().getRendaFamiliar()))
                        .addProperty(path(proxyUCD.getProntuario()))
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxyUCD.getEnderecoDomicilio().getCodigo()), codigoEnderecoDomicilio))
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxyUCD.getStatus()), BuilderQueryCustom.QueryParameter.DIFERENTE, UsuarioCadsusDomicilio.STATUS_EXCLUIDO))
                        .start().getList();
            
            
            List<ComponentesDomicilioFichaCadastroDomiciliarDTO> dtoList = new ArrayList<ComponentesDomicilioFichaCadastroDomiciliarDTO>();
            
            for(UsuarioCadsusDomicilio ucd : list){
                ComponentesDomicilioFichaCadastroDomiciliarDTO dto = new ComponentesDomicilioFichaCadastroDomiciliarDTO();
                
                dto.setNomeComponente(ucd.getUsuarioCadsus().getNome());
                dto.setDataNascimento(ucd.getUsuarioCadsus().getDataNascimento());
                dto.setRendaFamiliar(ucd.getUsuarioCadsus().getDescricaoRendaFamiliar());
                dto.setFlagResponsavelFamiliarFormatado(ucd.getUsuarioCadsus().getFlagResponsavelFamiliarFormatado());
                dto.setNumeroProntuario(ucd.getProntuario());
                dto.setResideDesde(ucd.getUsuarioCadsus().getResideDesde());
                
                List<UsuarioCadsusCns> cnsList = LoadManager.getInstance(UsuarioCadsusCns.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS), ucd.getUsuarioCadsus()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_EXCLUIDO), BuilderQueryCustom.QueryParameter.IGUAL, RepositoryComponentDefault.NAO_EXCLUIDO,HQLHelper.NOT_RESOLVE_TYPE,RepositoryComponentDefault.NAO_EXCLUIDO))
                        .start().getList();
                
                if (CollectionUtils.isNotNullEmpty(cnsList)) {
                    dto.setCns(cnsList.get(0).getNumeroCartaoFormatado());
                }
                
                dtoList.add(dto);
            }

            result.get(0).setComponentesDomicilioList(dtoList);
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioImpressaoCadastroFichaDomiciliarDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(Long codigoEnderecoDomicilio) {
        this.codigoEnderecoDomicilio = codigoEnderecoDomicilio;
    }

}
