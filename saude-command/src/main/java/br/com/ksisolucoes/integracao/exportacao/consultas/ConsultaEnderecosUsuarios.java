/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.integracao.exportacao.consultas;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEndereco;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEnderecoPK;
import java.util.Date;
import java.util.List;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class ConsultaEnderecosUsuarios extends AbstractCommandTransaction{

    private List<UsuarioCadsusEndereco> usuarioCadsusEnderecoList;
    private UsuarioCadsus usuarioCadsus;
    private Date dataControle;

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public void setDataControle(Date dataControle) {
        this.dataControle = dataControle;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        HQLHelper hql = new HQLHelper();
        hql.setTypeSelect(UsuarioCadsusEndereco.class.getName());

        hql.addToSelect("uce.status",VOUtils.montarPath(UsuarioCadsusEndereco.PROP_STATUS));
        hql.addToSelect("uce.dataAlteracao",VOUtils.montarPath(UsuarioCadsusEndereco.PROP_DATA_ALTERACAO));
        hql.addToSelect("uce.tipoEndereco",VOUtils.montarPath(UsuarioCadsusEndereco.PROP_TIPO_ENDERECO));
        hql.addToSelect("uce.vinculo",VOUtils.montarPath(UsuarioCadsusEndereco.PROP_VINCULO));
        hql.addToSelect("endereco.codigo",VOUtils.montarPath(UsuarioCadsusEndereco.PROP_ID,UsuarioCadsusEnderecoPK.PROP_ENDERECO,EnderecoUsuarioCadsus.PROP_CODIGO));
        hql.addToSelect("endereco.enderecoInterno",VOUtils.montarPath(UsuarioCadsusEndereco.PROP_ID,UsuarioCadsusEnderecoPK.PROP_ENDERECO,EnderecoUsuarioCadsus.PROP_ENDERECO_INTERNO));
        hql.addToSelect("usu.codigo",VOUtils.montarPath(UsuarioCadsusEndereco.PROP_ID,UsuarioCadsusEnderecoPK.PROP_USUARIO_CADSUS,UsuarioCadsus.PROP_CODIGO));

        hql.addToFrom("UsuarioCadsusEndereco uce "
                + " left join uce.id.endereco endereco "
                + " left join uce.id.usuarioCadsus usu");

        hql.addToWhereWhithAnd("uce.dataAlteracao = :dataControle");
        hql.addToWhereWhithAnd("usu = :usuarioCadsus");

        Query query = getSession().createQuery(hql.getQuery());

        query.setParameter("dataControle", dataControle);
        query.setParameter("usuarioCadsus", usuarioCadsus);

        usuarioCadsusEnderecoList = hql.getBeanList((List)query.list(),false);
        

    }

    public List<UsuarioCadsusEndereco> getUsuarioCadsusEnderecoList() {
        return usuarioCadsusEnderecoList;
    }


}
