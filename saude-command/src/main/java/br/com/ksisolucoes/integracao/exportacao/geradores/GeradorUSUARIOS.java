/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.integracao.exportacao.geradores;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.exportacao.consultas.UsuarioDTO;
import br.com.ksisolucoes.integracao.exportacao.consultas.ConsultaUsuarios;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import noNamespace.ROOTDocument.ROOT.DADOS.CADASTROS.ENDERECO;
import noNamespace.ROOTDocument.ROOT.DADOS.CADASTROS.ENDERECO.USUARIOS;
import noNamespace.ROOTDocument.ROOT.DADOS.CADASTROS.ENDERECO.USUARIOS.USUARIO;

/**
 *
 * <AUTHOR>
 */
public class GeradorUSUARIOS {

    private USUARIOS[] usuariosArray;
    private ENDERECO endereco;
    private EnderecoUsuarioCadsus enderecoUsuarioCadsus;
    private Date dataControle;

    public GeradorUSUARIOS(ENDERECO endereco, EnderecoUsuarioCadsus enderecoUsuarioCadsus, Date dataControle) {
        this.endereco = endereco;
        this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
        this.dataControle = dataControle;
    }

    public USUARIOS[] getUsuariosArray() throws DAOException, ValidacaoException {
        if(usuariosArray == null){
            carregar();
        }
        return usuariosArray;
    }

    private void carregar() throws DAOException, ValidacaoException {

         ConsultaUsuarios consultaUsuarios = new ConsultaUsuarios();
         consultaUsuarios.setEnderecoUsuarioCadsus(enderecoUsuarioCadsus);
         consultaUsuarios.setDataControle(dataControle);
         consultaUsuarios.start();

         USUARIOS usuarios = USUARIOS.Factory.newInstance();
         usuariosArray = new USUARIOS[]{usuarios};

         List<UsuarioDTO> dtoList = consultaUsuarios.getUsuariosList();
         if(CollectionUtils.isNotNullEmpty(dtoList)){
             for (UsuarioDTO dto : dtoList) {

                USUARIO usuario = usuarios.addNewUSUARIO();

                if(dto.getDataAlteracao() == null){
                    usuario.setCOUSUARIO(dto.getCodigo().toString());
                    usuario.setSTCONTROLE("I");
                }else{
                    usuario.setCOUSUARIO(dto.getUsuarioInterno());
                    usuario.setSTCONTROLE("A");
                }

                usuario.setNOUSUARIO(dto.getNome());
                usuario.setDTNASCIMENTO(new SimpleDateFormat("dd/MM/yyyy").format(dto.getDataNascimento()));

                usuario.setCOMUNICIPIONASC(dto.getCidadeNascimento()==null?"":dto.getCidadeNascimento().getCodigo().toString());

                usuario.setNOMAE(Coalesce.asString(dto.getNomeMae()));
                usuario.setNOPAI(Coalesce.asString(dto.getNomePai()));
                usuario.setSTPROFISSIONAL(Coalesce.asString(dto.getProfissional()));
                usuario.setSTFREQUENTAESCOLA(Coalesce.asString(dto.getFrequentaEscola()));
                usuario.setDSEMAIL(Coalesce.asString(dto.getEmail()));
                usuario.setDSUSOMUNICIPAL("");
                usuario.setCORACA(dto.getRaca()==null?"":Coalesce.asString(dto.getRaca().getCodigo()));
                usuario.setCOESTADOCIVIL(dto.getEstadoCivil()==null?"":Coalesce.asString(dto.getEstadoCivil().getCodigo()));
                usuario.setCOSITUACAOFAMILIAR(Coalesce.asString(dto.getSituacaoFamiliar()));
                usuario.setCOPAIS(dto.getPaisNascimento()==null?"":Coalesce.asString(dto.getPaisNascimento().getCodigo()));
                usuario.setCOSGRPCBO(dto.getTabelaCbo()==null?"":Coalesce.asString(dto.getTabelaCbo().getCbo()));
                usuario.setCOESCOLARIDADE(dto.getEscolaridade()==null?"":Coalesce.asString(dto.getEscolaridade().getCodigo()));
                if(dto.getTelefone() != null && dto.getTelefone().length() > 3){
                    usuario.setNUDDD(dto.getTelefone().substring(0,2));
                    usuario.setNUTELEFONE(dto.getTelefone().substring(2));
                }else{
                    usuario.setNUDDD("");
                    usuario.setNUTELEFONE("");
                }
                if(dto.getTelefone2() != null && dto.getTelefone2().length() > 3){
                    usuario.setNUDDD2(dto.getTelefone2().substring(0,2));
                    usuario.setNUTELEFONE2(dto.getTelefone2().substring(2));
                }else{
                    usuario.setNUDDD2("");
                    usuario.setNUTELEFONE2("");
                }
                usuario.setCOSEXO(Coalesce.asString(dto.getSexo()));
                usuario.setDTINCLUSAO(new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(dto.getDataInclusao()));
                usuario.setDTPREENCHIMENTOFORM(new SimpleDateFormat("dd/MM/yyyy").format(dto.getDataPreenchimentoForm()));
                usuario.setCOMUNICIPIORESIDENCIA(new DecimalFormat("000000").format(dto.getMunicipioResidencia().getCodigo()));
                usuario.setSTSEMDOCUMENTO(Coalesce.asString(dto.getSemDocumento()));
                usuario.setNUUSUARIONODOMICILIO(Coalesce.asString(dto.getNumeroUsuarioDomicilio()));
                usuario.setSTEXCLUIDO(dto.getExcluido().toString());
                usuario.setDTOPERACAO(new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(dto.getDataAlteracao()));
                usuario.setNUVERSAO("0");
//                usuario.setCOLOTE();
//                usuario.setCODOMICILIO();
                usuario.setSTVIVO(Coalesce.asString(dto.getVivo()));
                usuario.setFLERROS("0");
                usuario.setSTCONFIRMACAOHOMONIMO("0");
                usuario.setSTENVIOCEF("0");

                GeradorGRUPOELOSUSU geradorGRUPOELOSUSU = new GeradorGRUPOELOSUSU(usuario);
                usuario.setGRUPOELOSUSUArray(geradorGRUPOELOSUSU.getGrupoElosUsusArray());

                usuario.addNewGRUPOELOSUSUFED();

                GeradorGRUPODOCUMENTO geradorGRUPODOCUMENTO = new GeradorGRUPODOCUMENTO(usuario, dto);
                usuario.setGRUPODOCUMENTOArray(geradorGRUPODOCUMENTO.getGrupoDocumentoArray());

                GeradorGRUPOCNS geradorGRUPOCNS = new GeradorGRUPOCNS(usuario, dto);
                usuario.setGRUPOCNSArray(geradorGRUPOCNS.getGrupoCnsArraay());

                usuario.addNewGRUPOPIS();

                usuario.addNewGRUPOORIGEM();

                usuario.addNewGRUPOMOTIVO();

                GeradorGRUPORLENDERECO geradorGRUPORLENDERECO = new GeradorGRUPORLENDERECO(usuario, endereco, enderecoUsuarioCadsus, dto, dataControle);
                usuario.setGRUPORLENDERECOArray(geradorGRUPORLENDERECO.getGrupoRlEnderecoArray());

            }
        }
    }

}
