package br.com.ksisolucoes.integracao.bos.datasus.auxilioBrasil;

import br.com.celk.manutencaoAuxilioBrasil.dto.ConsultaManutencaoAuxilioBrasilDTO;
import br.com.celk.manutencaoAuxilioBrasil.dto.ConsultaManutencaoAuxilioBrasilDTOParam;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

import java.util.List;
import java.util.Map;

public class QueryConsultaManutencaoAuxilioBrasil extends CommandQueryPager<QueryConsultaManutencaoAuxilioBrasil> {

    private final ConsultaManutencaoAuxilioBrasilDTOParam param;

    public QueryConsultaManutencaoAuxilioBrasil(ConsultaManutencaoAuxilioBrasilDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(ConsultaManutencaoAuxilioBrasilDTO.class.getName());

        hql.addToSelect("ab.codigo", "codigoAuxilioBrasil");
        hql.addToSelect("ab.dataAtendimento", "dataAtendimento");
        hql.addToSelect("ab.tipoIntegrante", "tipoIntegrante");
        hql.addToSelect("ab.vacinaEmDia", "vacinaEmDia");
        hql.addToSelect("ab.semestre", "semestre");
        hql.addToSelect("ab.ano", "ano");
        hql.addToSelect("ab.peso", "peso");
        hql.addToSelect("ab.altura", "altura");
        hql.addToSelect("ab.gestante", "gestante");
        hql.addToSelect("ab.dataPreNatal", "dataPreNatal");
        hql.addToSelect("ab.dum", "dum");
        hql.addToSelect("ab.situacao", "situacaoAcompanhamento");
        hql.addToSelect("ab.motivoNaoAcompanhamento", "motivoNaoAcompanhamento");
        hql.addToSelect("ab.motivoDeDadoNutricionalNaoColetado", "motivoDeDadoNutricionalNaoColetado");
        hql.addToSelect("ab.motivoDeNaoRealizacaoDoPreNatal", "motivoDescumprimentoPreNatal");
        hql.addToSelect("ab.motivoDeNaoVacinacao", "motivoNaoVacinacao");

        hql.addToSelect("uc.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("uc.nome", "usuarioCadsus.nome");
        hql.addToSelect("uc.nomeMae", "usuarioCadsus.nomeMae");
        hql.addToSelect("uc.dataNascimento", "usuarioCadsus.dataNascimento");
        hql.addToSelect("uc.cpf", "usuarioCadsus.cpf");
        hql.addToSelect("uc.sexo", "usuarioCadsus.sexo");
        hql.addToSelect("uc.nis", "usuarioCadsus.nis");

        hql.addToSelect("euc.keyword", "endereco");
        hql.addToSelect("ema.microArea", "microArea");
        hql.addToSelect("ea.descricao", "area");
        hql.addToSelect("(select min(ucc.numeroCartao) from UsuarioCadsusCns ucc where ucc.usuarioCadsus = uc and ucc.excluido = 0)", "cns");

        hql.addToFrom("AuxilioBrasil ab "
                + " left join ab.usuarioCadsus uc "
                + " left join uc.enderecoDomicilio ed "
                + " left join ed.enderecoUsuarioCadsus euc "
                + " left join ed.equipeMicroArea ema "
                + " left join ema.equipeArea ea");

        if(param.getUsuarioCadsus() != null){
            hql.addToWhereWhithAnd("uc.codigo = ", param.getUsuarioCadsus().getCodigo());
        }

        if(param.getAno() != null){
            hql.addToWhereWhithAnd("ab.ano = ", param.getAno());
        }
        if(param.getSemestre() != null){
            hql.addToWhereWhithAnd("ab.semestre = ", param.getSemestre());
        }

        if(RepositoryComponentDefault.SIM_LONG.equals(param.getFlagApenasCriancas())){
            hql.addToWhereWhithAnd("ab.tipoIntegrante = ", 3L);
        }

        if(param.getSituacao() != null){
            hql.addToWhereWhithAnd("ab.situacao in ", param.getSituacao());
        }

        if(param.getEquipeMicroArea() != null){
            hql.addToWhereWhithAnd("ed.microArea = ", param.getEquipeMicroArea().getMicroArea());
        }

        if(param.getArea() != null){
            hql.addToWhereWhithAnd("ed.area = ", param.getArea().getCodigoArea());
        }

        if(param.getBairro() != null){
            hql.addToWhereWhithAnd(hql.getConsultaLiked("euc.bairro", param.getBairro().getDescricao()));
        }

        if (param.getPropSort() != null) {
            hql.addToOrder(param.getPropSort() + " " + (param.isAscending() ? "asc" : "desc"));
        } else {
            hql.addToOrder("ab.dataAtendimento desc");
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}