package br.com.ksisolucoes.integracao.bos.datasus.qware;


import br.com.celk.util.Util;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.avaliacao.interfaces.dto.AtualizarCadastrarQwareDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDocumento;
import br.com.ksisolucoes.vo.controle.ArquivoQware;
import br.com.ksisolucoes.vo.controle.Qware;

import java.util.Arrays;
import java.util.List;
import java.util.logging.Logger;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR> Prado
 */
public class CadastrarAtualizarCadsusQware extends AbstractCommandTransaction<CadastrarAtualizarCadsusQware> {

    private static final Logger LOG = Logger.getLogger(CadastrarAtualizarCadsusQware.class.getName());
    private ArquivoQware arquivoQware;
    private UsuarioCadsus usuarioCadsus;
    private int countLog = 0;
    private long QTD_USUARIO_CADASTRADOS = 0L;
    private long QTD_USUARIO_ATUALIZADOS = 0L;

    public CadastrarAtualizarCadsusQware(ArquivoQware arquivoQware) {
        this.arquivoQware = arquivoQware;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        List<Qware> usuariosCadsusQware = listarUsuariosCadsusQware(arquivoQware.getCodigo());
        for (Qware usuarioCadsusQware : usuariosCadsusQware) {
            atualizaOuCadastraUsuario(usuarioCadsusQware);
            if (countLog % 100 == 0)
                LOG.info("Q-WARE: REGISTROS PROCESSADOS " + countLog + " DE " + usuariosCadsusQware.size());
            countLog++;
        }


        arquivoQware = (ArquivoQware) this.getSession().get(ArquivoQware.class, arquivoQware.getCodigo());
        arquivoQware.setQuantidadeRegistrosCadastrados(QTD_USUARIO_CADASTRADOS);
        arquivoQware.setQuantidadeRegistrosAtualizados(QTD_USUARIO_ATUALIZADOS);
        getSession().saveOrUpdate(arquivoQware);
        LOG.info("Q-WARE: PROCESSO CADASTRO/ATUALIZAÇÃO FINALIZADO");
    }

    public List<Qware> listarUsuariosCadsusQware(Long codigoArquivoQware) {
        Qware qwareProxy = on(Qware.class);

        return LoadManager.getInstance(Qware.class)
                .addProperty(path(qwareProxy.getCodigo()))
                .addProperty(path(qwareProxy.getNomeIntegrante()))
                .addProperty(path(qwareProxy.getCpf()))
                .addProperty(path(qwareProxy.getNis()))
                .addProperty(path(qwareProxy.getTipoIntegrante()))
                .addProperty(path(qwareProxy.getNomeMae()))
                .addProperty(path(qwareProxy.getDataNascimento()))
                .addProperty(path(qwareProxy.getSexo()))
                .addProperty(path(qwareProxy.getRaca()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Qware.PROP_ARQUIVO_QWARE, ArquivoQware.PROP_CODIGO), codigoArquivoQware))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Qware.PROP_OCORRENCIA), BuilderQueryCustom.QueryParameter.DIFERENTE, RepositoryComponentDefault.SIM_LONG))
                .start().getList();
    }

    private void atualizaOuCadastraUsuario(Qware usuarioCadsusQware) {
        try {
            if (isUsuarioCadsusExiste(usuarioCadsusQware.getCpf())) {
                atualizar(usuarioCadsusQware);
            } else {
                cadastrar(usuarioCadsusQware);
            }
        } catch (Exception e) {
            LOG.info(String.format("Q-WARE: ERRO NO CPF %s.\n MENSAGEM DE ERRO: %s.\n STACK ERROR: %s", usuarioCadsusQware.getCpf(), e.getMessage(), Arrays.toString(e.getStackTrace())));
        } finally {
            limparCache();
        }
    }

    private boolean isUsuarioCadsusExiste(String cpf) {
        return Util.isNotNull(buscarUsuarioCadsus(cpf));
    }

    private UsuarioCadsus buscarUsuarioCadsus(String cpf) {

        UsuarioCadsus usuarioCadsusProxy = on(UsuarioCadsus.class);

        List<UsuarioCadsus> usuariosCadsusList = LoadManager.getInstance(UsuarioCadsus.class)
                .addProperty(path(usuarioCadsusProxy.getCodigo()))
                .addProperty(path(usuarioCadsusProxy.getExcluido()))
                .addProperty(path(usuarioCadsusProxy.getNis()))
                .addProperty(path(usuarioCadsusProxy.getCpf()))
                .addProperty(path(usuarioCadsusProxy.getDataNascimento()))
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_CPF, cpf))
                .start().getList();

        if (!usuariosCadsusList.isEmpty())
            return usuarioCadsus = buscarPaciente(usuariosCadsusList);
        else
            return usuarioCadsus = buscarPacientePelaTabelaDeDocumentos(cpf);
    }

    private UsuarioCadsus buscarPaciente(List<UsuarioCadsus> usuariosCadsusList) {
        for (UsuarioCadsus e : usuariosCadsusList)
            if (isPacienteAtivo(e))
                return e;

        return pacienteInativo(usuariosCadsusList);
    }

    private boolean isPacienteAtivo(UsuarioCadsus e) {
        return !RepositoryComponentDefault.SIM_LONG.equals(e.getExcluido());
    }

    private UsuarioCadsus pacienteInativo(List<UsuarioCadsus> usuariosCadsusList) {
        return usuariosCadsusList.get(0);
    }

    private UsuarioCadsus buscarPacientePelaTabelaDeDocumentos(String cpf) {

        UsuarioCadsusDocumento usuarioCadsusDocumentoProxy = on(UsuarioCadsusDocumento.class);

        List<UsuarioCadsusDocumento> usuarioCadsusDocumentoList = LoadManager.getInstance(UsuarioCadsusDocumento.class)
                .addProperty(path(usuarioCadsusDocumentoProxy.getCodigo()))
                .addProperty(path(usuarioCadsusDocumentoProxy.getUsuarioCadsus().getCodigo()))
                .addProperty(path(usuarioCadsusDocumentoProxy.getUsuarioCadsus().getExcluido()))
                .addProperty(path(usuarioCadsusDocumentoProxy.getUsuarioCadsus().getNis()))
                .addProperty(path(usuarioCadsusDocumentoProxy.getUsuarioCadsus().getCpf()))
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDocumento.PROP_NUMERO_DOCUMENTO, cpf))
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDocumento.PROP_SITUACAO_EXCLUIDO, BuilderQueryCustom.QueryParameter.DIFERENTE, UsuarioCadsusDocumento.STATUS_EXCLUIDO))
                .start().getList();

        return usuarioCadsusDocumentoList.isEmpty() ? null : usuarioCadsusDocumentoList.get(0).getUsuarioCadsus();
    }

    private void atualizar(Qware usuarioCadsusQware) throws DAOException, ValidacaoException {
        LOG.info("Q-WARE: ATUALIZANDO USUARIO CADSUS, CPF: " + usuarioCadsusQware.getCpf());
        if (isNumeroNISNovo(usuarioCadsusQware.getNis())) {
            AtualizarCadastrarQwareDTO dto = buildAtualizarCadastrarQwareDTO(usuarioCadsusQware);
            BOFactory.getBO(BasicoFacade.class).atualizarCadsusQware(dto);
        }
        QTD_USUARIO_ATUALIZADOS++;
    }

    private boolean isNumeroNISNovo(String nis) {
        return !nis.equals(usuarioCadsus.getNis());
    }

    private void cadastrar(Qware usuarioCadsusQware) throws DAOException, ValidacaoException {
        LOG.info("Q-WARE: CADASTRANDO USUARIO CADSUS, CPF: " + usuarioCadsusQware.getCpf());
        AtualizarCadastrarQwareDTO dto = buildAtualizarCadastrarQwareDTO(usuarioCadsusQware);
        BOFactory.getBO(BasicoFacade.class).cadastrarCadsusQware(dto);
        QTD_USUARIO_CADASTRADOS++;
    }

    private AtualizarCadastrarQwareDTO buildAtualizarCadastrarQwareDTO(Qware usuarioCadsusQware) {
        AtualizarCadastrarQwareDTO dto = new AtualizarCadastrarQwareDTO();
        dto.setUsuarioCadsusQware(usuarioCadsusQware);
        dto.setUsuarioCadsus(usuarioCadsus);
        dto.setAno(arquivoQware.getAno());
        dto.setSemestre(arquivoQware.getSemestre());
        return dto;
    }

    private void limparCache() {
        getSession().flush();
        getSession().clear();
    }
}
