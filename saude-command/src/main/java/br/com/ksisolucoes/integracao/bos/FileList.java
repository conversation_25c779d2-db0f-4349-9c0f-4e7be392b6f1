/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.integracao.bos;

import br.com.ksisolucoes.integracao.bos.interfaces.InterfaceCommand;
import java.io.File;
import java.io.FileFilter;
import java.rmi.RemoteException;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class FileList implements InterfaceCommand{

    private List<String> fileList;
    private String path;
    private static FileFilter fileFilter;

    public FileList(String path) {
        this.path = path;
    }

    static{
        fileFilter = new FileFilter() {

            public boolean accept(File pathname) {
                if (pathname.isDirectory()) {
                    return true;
                }

                String extension = getExtension(pathname);
                if (extension != null && extension.equals("xml")) {
                    return true;
                }

                return false;
            }

            public String getExtension(File f) {
                if (f != null) {
                    String fileName = f.getName();
                    int i = fileName.lastIndexOf('.');
                    if (i > 0 && i < fileName.length() - 1) {
                        return fileName.substring(i + 1).toLowerCase();

                    }
                }
                return null;
            }
        };// </editor-fold>
    }

    public List<String> getFileList() {
        return fileList;
    }

    public void start() throws RemoteException {
        File diretorio = new File(path);
        File fList[] = diretorio.listFiles(fileFilter);

        fileList = new ArrayList<String>();
        if(fList != null){
            for (File file : fList) {
                fileList.add(file.getName());
            }
        }
    }

}
