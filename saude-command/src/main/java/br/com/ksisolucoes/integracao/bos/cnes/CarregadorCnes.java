/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.integracao.bos.cnes;

import br.com.ksisolucoes.integracao.bos.interfaces.InterfaceCommand;
import br.com.ksisolucoes.integracao.dao.interfaces.SearchDAO;
import br.com.ksisolucoes.integracao.vos.interfaces.Entidade;
import java.rmi.RemoteException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Classe que carrega todos os dados do cnes apartir da fonte de dados informada
 * <AUTHOR>
 */
public class CarregadorCnes implements InterfaceCommand {

    private Date dataAtualizacao;
    private SearchDAO daoDestino;
    private SearchDAO daoOrigem;
    private List<Entidade> entidades = new ArrayList<Entidade>();

    public CarregadorCnes(Date dataAtualizacao, SearchDAO daoOrigem, SearchDAO daoDestino) {
        this.dataAtualizacao = dataAtualizacao;
        this.daoOrigem = daoOrigem;
        this.daoDestino = daoDestino;
    }

    public void start() throws RemoteException {

        this.entidades.addAll(new CarregadorCnesEstado(daoOrigem, daoDestino).startWithReturn().getEntidades());
//        this.entidades.addAll(new CarregadorCnesCidade(daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesServico(daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesServicoClassificacao(daoOrigem, daoDestino).startWithReturn().getEntidades());
//        this.entidades.addAll(new CarregadorCnesServicoTerceiroBrasil(daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesAtividade(daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesSegmentoTerritorial(daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesTipoConvenio( daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesTipoLogradouro(daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesTipoEquipe(daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesTipoEquipamento(daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesTurnoAtendimento(daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesMotivoDesativacao(daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesEquipeArea(daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesEquipamento(daoOrigem, daoDestino).startWithReturn().getEntidades());
//        this.entidades.addAll(new CarregadorCnesNivelHierarquia(daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesOrgaoEmissor(daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesProfissional(this.dataAtualizacao, daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesEmpresaMantenedora(daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesEmpresa (this.dataAtualizacao, daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesEmpresaServicoClass(this.dataAtualizacao, daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesEmpresaEquipamento(daoOrigem, daoDestino).startWithReturn().getEntidades());
        //this.entidades.addAll(new CarregadorCnesMotivoDesligamento(daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesVinculacao(daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesVinculacaoTipo(daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesVinculacaoSubTipo(daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesEquipe(this.dataAtualizacao, daoOrigem, daoDestino).startWithReturn().getEntidades());
//        this.entidades.addAll(new CarregadorCnesProfissionalHistorico(daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesProfissionalCargaHoraria(this.dataAtualizacao, daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesEquipeProfissional(this.dataAtualizacao, daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesEquipeMicroArea(this.dataAtualizacao, daoOrigem, daoDestino).startWithReturn().getEntidades());
        
        this.entidades.addAll(new CarregadorCnesSubtipoInstalacao(this.dataAtualizacao, daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesInstalacaoFisica(this.dataAtualizacao, daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesEmpresaInstalacaoFisica( daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesAtendimentoPrestado( daoOrigem, daoDestino).startWithReturn().getEntidades());
        this.entidades.addAll(new CarregadorCnesEmpresaAtendimentoPrestado( daoOrigem, daoDestino).startWithReturn().getEntidades());
    }

    public List<Entidade> getRegistros() {
        return this.entidades;
    }
    }
