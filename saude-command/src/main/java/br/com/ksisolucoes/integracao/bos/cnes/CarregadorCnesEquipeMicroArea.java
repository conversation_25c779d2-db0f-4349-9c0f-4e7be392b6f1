/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.integracao.bos.cnes;

import br.com.ksisolucoes.integracao.conversao.MCGenericType;
import br.com.ksisolucoes.integracao.dao.DAOException;
import br.com.ksisolucoes.integracao.dao.interfaces.SearchDAO;
import br.com.ksisolucoes.integracao.dao.util.ValorAtributoImpl;
import br.com.ksisolucoes.integracao.dao.util.ValorAtributoSequence;
import br.com.ksisolucoes.integracao.dao.util.vo.AtributoSync;
import br.com.ksisolucoes.integracao.dao.util.vo.EntidadeSync;
import br.com.ksisolucoes.integracao.dao.util.vo.ModoConversao;
import br.com.ksisolucoes.integracao.vos.AtributoImpl;
import br.com.ksisolucoes.integracao.vos.interfaces.Entidade;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CarregadorCnesEquipeMicroArea extends CarregadorCnesTemplate {

    private Date dataAtualizacao;
    private SearchDAO daoDestino;
    private SearchDAO daoOrigem;

    public CarregadorCnesEquipeMicroArea(Date dataAtualizacao, SearchDAO daoOrigem, SearchDAO daoDestino) {
        this.dataAtualizacao = dataAtualizacao;
        this.daoOrigem = daoOrigem;
        this.daoDestino = daoDestino;
    }

    @Override
    protected List<Entidade> load() throws DAOException {
        EntidadeSync entidadeSync = new EntidadeSync("LFCES038", "equipe_micro_area");
        entidadeSync.addAtributo(new AtributoSync(new AtributoImpl("cd_eqp_micro_area", false, true, false), new ValorAtributoSequence(daoDestino, "seq_gem", "cd_eqp_micro_area", "equipe_micro_area",
                new AtributoImpl("cd_equipe_area"),
                new AtributoImpl("micro_area"))));
        entidadeSync.addAtributo(new AtributoSync("MICROAREA", new AtributoImpl("micro_area", true), new MCGenericType(Long.class)));
        entidadeSync.addAtributo(new AtributoSync("COD_MUN || '-' || COD_AREA", new AtributoImpl("cd_equipe_area",true), new ModoConversao() {

            @Override
            public Object getValorConvertido(String tipo, Object value) throws IllegalArgumentException {
                try {
                    String[] valores = value.toString().split("-");
                    
                    return daoDestino.find("cd_equipe_area", "equipe_area",
                            new AtributoImpl("cod_cid", Long.valueOf(valores[0])),
                            new AtributoImpl("cd_area", Long.valueOf(valores[1])));
                } catch (DAOException ex) {
                    throw new IllegalArgumentException(ex.getMessage());
                }
            }
        }));
        entidadeSync.addAtributo(new AtributoSync("COD_MUN || '-' || COD_AREA || '-' || SEQ_EQUIPE || '-' || PROF_ID", new AtributoImpl("cd_equipe_profissional"), new ModoConversao() {

            @Override
            public Object getValorConvertido(String tipo, Object value) throws IllegalArgumentException {
                try {
                    String[] valores = value.toString().split("-");
                    
                    Object codigoProfissional = daoDestino.find("cd_profissional", "profissional",new AtributoImpl("profissional_id_cnes", new ValorAtributoImpl(valores[3])));
                    
                    Long cd_equipe_area = (Long) daoDestino.find("cd_equipe_area", "equipe_area",
                            new AtributoImpl("cod_cid", Long.valueOf(valores[0])),
                            new AtributoImpl("cd_area", Long.valueOf(valores[1])));
                    
                    Object codigoEquipe = daoDestino.find("cd_equipe", "equipe",
                            new AtributoImpl("cd_equipe_area", cd_equipe_area),
                            new AtributoImpl("seq_equipe", Long.valueOf(valores[2])));
                    
                    return daoDestino.find("cd_equipe_profissional", "equipe_profissional",
                            new AtributoImpl("cd_profissional", codigoProfissional),
                            new AtributoImpl("cd_equipe", codigoEquipe));
                } catch (DAOException ex) {
                    throw new IllegalArgumentException(ex.getMessage());
                }
            }
        }));

        return this.daoOrigem.loadSynchronizer(entidadeSync);
    }
}
