/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.integracao.exportacao.consultas;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.OrgaoEmissor;
import br.com.ksisolucoes.vo.cadsus.TipoDocumentoUsuario;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDocumento;
import java.util.List;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class ConsultaDocumentos extends AbstractCommandTransaction{

    private List<UsuarioCadsusDocumento> documentoList;
    private UsuarioCadsus usuarioCadsus;

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        HQLHelper hql = new HQLHelper();
        hql.setTypeSelect(UsuarioCadsusDocumento.class.getName());

        hql.addToSelect("ucd.codigo",VOUtils.montarPath(UsuarioCadsusDocumento.PROP_CODIGO));
        hql.addToSelect("uc.codigo",VOUtils.montarPath(UsuarioCadsusDocumento.PROP_USUARIO_CADSUS,UsuarioCadsus.PROP_CODIGO));
        hql.addToSelect("uc.usuarioInterno",VOUtils.montarPath(UsuarioCadsusDocumento.PROP_USUARIO_CADSUS,UsuarioCadsus.PROP_USUARIO_INTERNO));
        hql.addToSelect("td.codigo",VOUtils.montarPath(UsuarioCadsusDocumento.PROP_TIPO_DOCUMENTO,TipoDocumentoUsuario.PROP_CODIGO));
        hql.addToSelect("oe.codigo",VOUtils.montarPath(UsuarioCadsusDocumento.PROP_ORGAO_EMISSOR,OrgaoEmissor.PROP_CODIGO));
        hql.addToSelect("ucd.numeroCartorio",VOUtils.montarPath(UsuarioCadsusDocumento.PROP_NUMERO_CARTORIO));
        hql.addToSelect("ucd.numeroLivro",VOUtils.montarPath(UsuarioCadsusDocumento.PROP_NUMERO_LIVRO));
        hql.addToSelect("ucd.numeroFolha",VOUtils.montarPath(UsuarioCadsusDocumento.PROP_NUMERO_FOLHA));
        hql.addToSelect("ucd.numeroTermo",VOUtils.montarPath(UsuarioCadsusDocumento.PROP_NUMERO_TERMO));
        hql.addToSelect("ucd.dataEmissao",VOUtils.montarPath(UsuarioCadsusDocumento.PROP_DATA_EMISSAO));
        hql.addToSelect("ucd.dataChegadaBrasil",VOUtils.montarPath(UsuarioCadsusDocumento.PROP_DATA_CHEGADA_BRASIL));
        hql.addToSelect("ucd.numeroPortaria",VOUtils.montarPath(UsuarioCadsusDocumento.PROP_NUMERO_PORTARIA));
        hql.addToSelect("ucd.dataNaturalizacao",VOUtils.montarPath(UsuarioCadsusDocumento.PROP_DATA_NATURALIZACAO));
        hql.addToSelect("ucd.numeroDocumento",VOUtils.montarPath(UsuarioCadsusDocumento.PROP_NUMERO_DOCUMENTO));
        hql.addToSelect("uc.cpf",VOUtils.montarPath(UsuarioCadsusDocumento.PROP_USUARIO_CADSUS,UsuarioCadsus.PROP_CPF));
        hql.addToSelect("ucd.numeroDocumentoComplementar",VOUtils.montarPath(UsuarioCadsusDocumento.PROP_NUMERO_DOCUMENTO_COMPLEMENTAR));
        hql.addToSelect("ucd.numeroSerie",VOUtils.montarPath(UsuarioCadsusDocumento.PROP_NUMERO_SERIE));
        hql.addToSelect("ucd.numeroZonaEleitoral",VOUtils.montarPath(UsuarioCadsusDocumento.PROP_NUMERO_ZONA_ELEITORAL));
        hql.addToSelect("ucd.numeroSecaoEleitoral",VOUtils.montarPath(UsuarioCadsusDocumento.PROP_NUMERO_SECAO_ELEITORAL));
        hql.addToSelect("ucd.siglaUf",VOUtils.montarPath(UsuarioCadsusDocumento.PROP_SIGLA_UF));
        hql.addToSelect("ucd.situacaoExcluido",VOUtils.montarPath(UsuarioCadsusDocumento.PROP_SITUACAO_EXCLUIDO));
        hql.addToSelect("ucd.dataAlteracao",VOUtils.montarPath(UsuarioCadsusDocumento.PROP_DATA_ALTERACAO));

        hql.addToFrom("UsuarioCadsusDocumento ucd"
                + " left join ucd.id.usuarioCadsus uc"
                + " left join ucd.id.tipoDocumento td"
                + " left join ucd.orgaoEmissor oe");

        hql.addToWhereWhithAnd("ucd.id.usuarioCadsus = :usuarioCadsus");

        Query query = getSession().createQuery(hql.getQuery());

        query.setParameter("usuarioCadsus", usuarioCadsus);

        documentoList = hql.getBeanList((List)query.list(),false);

    }

    public List<UsuarioCadsusDocumento> getDocumentoList() {
        return documentoList;
    }

}
