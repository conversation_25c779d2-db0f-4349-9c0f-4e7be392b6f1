/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.integracao.bos.datasus.cadsus;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.integracao.cadsus.ImportacaoXmlCadsus;
import java.util.Calendar;

/**
 *
 * <AUTHOR>
 */
public class SaveImportacaoXmlCadsus extends SaveVO{

    private ImportacaoXmlCadsus importacaoXmlCadsus;

    public SaveImportacaoXmlCadsus(Object vo) {
        super(vo);
        this.importacaoXmlCadsus = (ImportacaoXmlCadsus) vo;
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if(importacaoXmlCadsus.getDataImportacao() == null){

            Calendar c = Calendar.getInstance();
            c.setTime(Data.getDataAtual());
            c.set(Calendar.MILLISECOND, 0);

            importacaoXmlCadsus.setDataImportacao(c.getTime());
        }
        if(importacaoXmlCadsus.getUsuario() == null){
            importacaoXmlCadsus.setUsuario((Usuario) getSessao().getUsuario());
        }
    }

    public ImportacaoXmlCadsus getImportacaoXmlCadsus(){
        return this.importacaoXmlCadsus;
    }
}
