/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.integracao.exportacao.geradores;

import java.util.ArrayList;
import java.util.List;
import noNamespace.ROOTDocument.ROOT.DADOS.CADASTROS.ENDERECO;
import noNamespace.ROOTDocument.ROOT.DADOS.CADASTROS.ENDERECO.GRUPOELOSEND;

/**
 *
 * <AUTHOR>
 */
public class GeradorGRUPOELOSEND {

    private GRUPOELOSEND[] grupoElosEnderecoArray;
    private ENDERECO endereco;

    public GeradorGRUPOELOSEND(ENDERECO endereco) {
        this.endereco = endereco;
    }

    public GRUPOELOSEND[] getGrupoElosEnderecoArray() {
        if(grupoElosEnderecoArray == null){
            carregar();
        }
        return grupoElosEnderecoArray;
    }

    private void carregar(){

        GRUPOELOSEND grupo = GRUPOELOSEND.Factory.newInstance();

        GRUPOELOSEND.ELOSEND eloEnd = grupo.addNewELOSEND();
        eloEnd.setCOENDERECOINFORMADO(endereco.getCOENDERECO());

        List<GRUPOELOSEND> list = new ArrayList<GRUPOELOSEND>();
        list.add(grupo);

        grupoElosEnderecoArray = list.toArray(new GRUPOELOSEND[list.size()]);
    }
}
