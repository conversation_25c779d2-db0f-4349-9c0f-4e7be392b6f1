package br.com.ksisolucoes.integracao.bos.datasus.qware;

import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.UsuarioCadsusEnderecoDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.prontuario.avaliacao.interfaces.dto.AtualizarCadastrarQwareDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.*;
import br.com.ksisolucoes.vo.controle.Qware;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class CadastrarCadsusQware extends AbstractCommandTransaction<CadastrarCadsusQware> {

    private final AtualizarCadastrarQwareDTO dto;

    UsuarioCadsus usuarioCadsus;
    UsuarioCadsusDocumento  documentoCpf;
    EnderecoUsuarioCadsus   enderecoUsuarioCadsus;
    ArrayList<UsuarioCadsusDocumento> listaDocs;
    List<Long> tipoDocumentosList;

    public CadastrarCadsusQware(AtualizarCadastrarQwareDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        inicializaPropriedades();

        UsuarioCadsusEnderecoDTO usuarioCadsusEnderecoDTO = new UsuarioCadsusEnderecoDTO();
        estruturarUsuarioCadsus(this.dto.getUsuarioCadsusQware());
        listaDocs.add(estruturaDocumentoCPF());
        usuarioCadsusEnderecoDTO.setCartoes(null);
        usuarioCadsusEnderecoDTO.setTipoDocumentos(tipoDocumentosList);
        usuarioCadsusEnderecoDTO.setDocumentos(listaDocs);
        usuarioCadsusEnderecoDTO.setUsuarioCadsusEndereco(estruturaUsuarioEnderecoCadsus(usuarioCadsus));

        usuarioCadsus = (UsuarioCadsus) BOFactory.getBO(UsuarioCadsusFacade.class).cadastrarUsuarioCadsusQware(usuarioCadsusEnderecoDTO);
        salvarRegistroAuxilioBrasil();
    }

    private void salvarRegistroAuxilioBrasil() throws DAOException, ValidacaoException {
        dto.setUsuarioCadsus(usuarioCadsus);
        BOFactory.getBO(BasicoFacade.class).salvarRegistroAuxilioBrasil(dto);
    }

    private void inicializaPropriedades(){

        //USUARIO CADSUS
        usuarioCadsus = new UsuarioCadsus();

        //DOCUMENTOS
        listaDocs    =   new ArrayList<>();
        documentoCpf =   new UsuarioCadsusDocumento();

        //TIPOS DE DOCUMENTOS
        tipoDocumentosList = new ArrayList<>();
        tipoDocumentosList.add(TipoDocumentoUsuario.TIPO_DOCUMENTO_CPF);
        tipoDocumentosList.add(TipoDocumentoUsuario.TIPO_DOCUMENTO_RG);
        tipoDocumentosList.add(TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_CASAMENTO);
        tipoDocumentosList.add(TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO);

        //ENDEREÇO
        enderecoUsuarioCadsus = new EnderecoUsuarioCadsus();
        enderecoUsuarioCadsus.setCidade(getSessao().getEmpresa().getCidade());
    }

    private void estruturarUsuarioCadsus(Qware qware)  {
        usuarioCadsus.setNome(qware.getNomeIntegrante());
        usuarioCadsus.setUtilizaNomeSocial(RepositoryComponentDefault.NAO_LONG);
        usuarioCadsus.setNomeMae(qware.getNomeMae());
        usuarioCadsus.setDataNascimento(qware.getDataNascimento());
        usuarioCadsus.setSexo(qware.getSexo() == 1L ? RepositoryComponentDefault.SIGLA_MASCULINO : RepositoryComponentDefault.SIGLA_FEMININO);
        usuarioCadsus.setBeneficiarioBolsaFamilia(RepositoryComponentDefault.SIM_LONG);
        usuarioCadsus.setNis(qware.getNis());
        usuarioCadsus.setRaca(qware.getRaca());
        usuarioCadsus.setCpf(qware.getCpf());
    }

    private UsuarioCadsusDocumento estruturaDocumentoCPF() {
        documentoCpf.setUsuarioCadsus(usuarioCadsus);
        documentoCpf.setNumeroDocumento(StringUtils.trimToNull(StringUtil.getDigits(usuarioCadsus.getCpf())));
        documentoCpf.setTipoDocumento(new TipoDocumentoUsuario(TipoDocumentoUsuario.TIPO_DOCUMENTO_CPF));
        return documentoCpf;
    }

    private UsuarioCadsusEndereco estruturaUsuarioEnderecoCadsus(UsuarioCadsus usuarioCadsus){
        return  new UsuarioCadsusEndereco(new UsuarioCadsusEnderecoPK(enderecoUsuarioCadsus, usuarioCadsus));
    }
}
