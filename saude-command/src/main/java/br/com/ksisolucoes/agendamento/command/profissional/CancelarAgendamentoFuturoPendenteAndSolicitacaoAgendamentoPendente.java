package br.com.ksisolucoes.agendamento.command.profissional;

import br.com.celk.agendamento.dto.CancelamentoAgendamentoDTO;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Order;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import java.text.SimpleDateFormat;

/**
 *
 * <AUTHOR>
 */
public class CancelarAgendamentoFuturoPendenteAndSolicitacaoAgendamentoPendente extends AbstractCommandTransaction {

    private UsuarioCadsus usuarioCadsus;
    private TipoProcedimento tipoProcedimento;
    private boolean cancelarAgendamento;
    private boolean cancelarSolicitacao;
    private String mensagemRetorno;

    public CancelarAgendamentoFuturoPendenteAndSolicitacaoAgendamentoPendente(UsuarioCadsus usuarioCadsus, TipoProcedimento tipoProcedimento, boolean cancelarAgendamento, boolean cancelarSolicitacao) {
        this.usuarioCadsus = usuarioCadsus;
        this.tipoProcedimento = tipoProcedimento;
        this.cancelarAgendamento = cancelarAgendamento;
        this.cancelarSolicitacao = cancelarSolicitacao;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (usuarioCadsus == null || tipoProcedimento == null) {
            return;
        }

        if (cancelarAgendamento) {
            AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario = (AgendaGradeAtendimentoHorario) this.getSession().createCriteria(AgendaGradeAtendimentoHorario.class)
                    .add(Restrictions.eq(AgendaGradeAtendimentoHorario.PROP_USUARIO_CADSUS, usuarioCadsus))
                    .add(Restrictions.eq(AgendaGradeAtendimentoHorario.PROP_STATUS, AgendaGradeAtendimentoHorario.STATUS_AGENDADO))
                    .add(Restrictions.gt(AgendaGradeAtendimentoHorario.PROP_DATA_AGENDAMENTO, DataUtil.getDataAtual()))
                    .add(Restrictions.eq(AgendaGradeAtendimentoHorario.PROP_TIPO_PROCEDIMENTO, tipoProcedimento))
                    .addOrder(Order.asc(AgendaGradeAtendimentoHorario.PROP_DATA_AGENDAMENTO))
                    .setMaxResults(1).uniqueResult();

            if (agendaGradeAtendimentoHorario != null) {
                CancelamentoAgendamentoDTO cancelamentoAgendamentoDTO = new CancelamentoAgendamentoDTO();
                cancelamentoAgendamentoDTO.setCodigoAgendaGradeAtendimentoHorario(agendaGradeAtendimentoHorario.getCodigo());
                cancelamentoAgendamentoDTO.setMotivoCancelamento(Bundle.getStringApplication("msg_cancelado_automaticamente_processo_marcacao"));
                cancelamentoAgendamentoDTO.setValidarUsuarioAgendamento(false);
                cancelamentoAgendamentoDTO.setValidarAgendamentosDia(false);
                cancelamentoAgendamentoDTO.setReabrirSolicitacaoAgendamento(false);
                cancelamentoAgendamentoDTO.setCancelarSolicitacaoAgendamento(true);
                BOFactory.getBO(AgendamentoFacade.class).cancelarAgendamento(cancelamentoAgendamentoDTO);

                mensagemRetorno = Bundle.getStringApplication("msg_paciente_possuia_agendamento_especialidade_dia_X_unidade_Y_portanto_foi_cancelado",
                        new SimpleDateFormat("dd/MM/yyyy HH:mm", Bundle.getLocale()).format(agendaGradeAtendimentoHorario.getDataAgendamento()),
                        agendaGradeAtendimentoHorario.getLocalAgendamento().getDescricaoFormatado());
            }
        }

        if (cancelarSolicitacao) {
            SolicitacaoAgendamento solicitacaoAgendamento = (SolicitacaoAgendamento) getSession().createCriteria(SolicitacaoAgendamento.class)
                    .add(Restrictions.eq(SolicitacaoAgendamento.PROP_TIPO_PROCEDIMENTO, tipoProcedimento))
                    .add(Restrictions.eq(SolicitacaoAgendamento.PROP_USUARIO_CADSUS, usuarioCadsus))
                    .add(Restrictions.in(SolicitacaoAgendamento.PROP_STATUS, SolicitacaoAgendamento.STATUS_PENDENTES))
                    .addOrder(Order.asc(SolicitacaoAgendamento.PROP_DATA_SOLICITACAO))
                    .setMaxResults(1).uniqueResult();

            if (solicitacaoAgendamento != null) {
                BOFactory.getBO(AgendamentoFacade.class).cancelarSolicitacaoAgendamento(solicitacaoAgendamento.getCodigo(), Bundle.getStringApplication("msg_cancelado_automaticamente_processo_marcacao"), true);
                if (mensagemRetorno == null) {
                    mensagemRetorno = Bundle.getStringApplication("msg_paciente_possuia_solicitacao_agendamento_especialidade_data_X_unidade_Y_portanto_foi_cancelada",
                            new SimpleDateFormat("dd/MM/yyyy", Bundle.getLocale()).format(solicitacaoAgendamento.getDataSolicitacao()),
                            solicitacaoAgendamento.getEmpresa().getDescricaoFormatado());
                }
            }
        }
    }

    public String getMensagemRetorno() {
        return mensagemRetorno;
    }

}
