package br.com.ksisolucoes.bo.agendamento.exame;

import br.com.celk.laboratorio.exames.dto.ProcedimentoHelper;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTOParam;
import br.com.ksisolucoes.agendamento.exame.dto.angedamentosolictacaolote.AgendaDTO;
import br.com.ksisolucoes.bo.agendamento.exame.cenarios.BuildCenariosFiltraAgendasDisponiveisTest;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.ExameCotaPpi;
import ch.lambdaj.Lambda;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ProcedimentoHelper.class})
public class FiltraAgendasDisponiveisTest {

    @Before
    public void setup() {
        PowerMockito.mockStatic(ProcedimentoHelper.class);
    }

    @Test
    public void deveFiltrarAgendasDisponiveisValidandoCotaFinanceira() throws ValidacaoException {

        when(ProcedimentoHelper.getValorExameProcedimento(BuildCenariosFiltraAgendasDisponiveisTest.exame_1, BuildCenariosFiltraAgendasDisponiveisTest.empresa_1)).thenReturn(15D);
        when(ProcedimentoHelper.getValorExameProcedimento(BuildCenariosFiltraAgendasDisponiveisTest.exame_1, BuildCenariosFiltraAgendasDisponiveisTest.empresa_2)).thenReturn(20D);
        when(ProcedimentoHelper.getValorExameProcedimento(BuildCenariosFiltraAgendasDisponiveisTest.exame_2, BuildCenariosFiltraAgendasDisponiveisTest.empresa_1)).thenReturn(15D);
        when(ProcedimentoHelper.getValorExameProcedimento(BuildCenariosFiltraAgendasDisponiveisTest.exame_2, BuildCenariosFiltraAgendasDisponiveisTest.empresa_2)).thenReturn(15D);

        List<AgendaDTO> agendas = BuildCenariosFiltraAgendasDisponiveisTest.agendasComCotasDisponiveis();
        AgendaGradeAtendimentoDTOParam param = BuildCenariosFiltraAgendasDisponiveisTest.agendaGradeAtendimentoDTOParam();
        param.setTipoProcedimento(BuildCenariosFiltraAgendasDisponiveisTest.tipoProcedimentoControlaCotas);


        List<AgendaDTO> agendasDisponiveis = new FiltraAgendasDisponiveis().filtrarAgendasDisponiveis(agendas, param, ExameCotaPpi.TipoTeto.FINANCEIRO.value());

        assertNotNull("lista de agendas não pode ser null", agendasDisponiveis);
        assertFalse("lista de agenda disponivel não pode ser vazia", agendasDisponiveis.isEmpty());
        assertEquals("deve possuir 2 agendas com horarios disponíveis", 2, agendasDisponiveis.size());
    }

    @Test
    public void naoDeveRetornarAgendasComCotasFinanceirasDisponiveis() throws ValidacaoException {

        when(ProcedimentoHelper.getValorExameProcedimento(BuildCenariosFiltraAgendasDisponiveisTest.exame_1, BuildCenariosFiltraAgendasDisponiveisTest.empresa_1)).thenReturn(15D);
        when(ProcedimentoHelper.getValorExameProcedimento(BuildCenariosFiltraAgendasDisponiveisTest.exame_1, BuildCenariosFiltraAgendasDisponiveisTest.empresa_2)).thenReturn(20D);
        when(ProcedimentoHelper.getValorExameProcedimento(BuildCenariosFiltraAgendasDisponiveisTest.exame_2, BuildCenariosFiltraAgendasDisponiveisTest.empresa_1)).thenReturn(15D);
        when(ProcedimentoHelper.getValorExameProcedimento(BuildCenariosFiltraAgendasDisponiveisTest.exame_2, BuildCenariosFiltraAgendasDisponiveisTest.empresa_2)).thenReturn(15D);

        List<AgendaDTO> agendas = BuildCenariosFiltraAgendasDisponiveisTest.agendasSemCotasDisponiveis();
        AgendaGradeAtendimentoDTOParam param = BuildCenariosFiltraAgendasDisponiveisTest.agendaGradeAtendimentoDTOParam();
        param.setTipoProcedimento(BuildCenariosFiltraAgendasDisponiveisTest.tipoProcedimentoControlaCotas);


        List<AgendaDTO> agendasDisponiveis = new FiltraAgendasDisponiveis().filtrarAgendasDisponiveis(agendas, param, ExameCotaPpi.TipoTeto.FINANCEIRO.value());

        assertNotNull("lista de agendas não pode ser null", agendasDisponiveis);
        assertTrue("deve retornar lista de agenda disponivel vazia", agendasDisponiveis.isEmpty());
    }

    @Test
    public void deveFiltrarAgendasDisponiveisValidandoCotaFinanceiraSemExamePrestadorCompetenciaConfigurada() throws ValidacaoException {

        List<AgendaDTO> agendas = BuildCenariosFiltraAgendasDisponiveisTest.agendasSemCotasDisponiveis();
        Lambda.forEach(agendas).setExamePrestadorCompetencia(null);
        AgendaGradeAtendimentoDTOParam param = BuildCenariosFiltraAgendasDisponiveisTest.agendaGradeAtendimentoDTOParam();
        param.setTipoProcedimento(BuildCenariosFiltraAgendasDisponiveisTest.tipoProcedimentoControlaCotas);


        List<AgendaDTO> agendasDisponiveis = new FiltraAgendasDisponiveis().filtrarAgendasDisponiveis(agendas, param, ExameCotaPpi.TipoTeto.FINANCEIRO.value());

        assertNotNull("lista de agendas não pode ser null", agendasDisponiveis);
        assertTrue("deve retornar lista de agenda disponivel vazia", agendasDisponiveis.isEmpty());
    }

    @Test
    public void deveFiltrarAgendasDisponiveisValidandoCotaFinanceiraComTipoTetoNull() throws ValidacaoException {

        List<AgendaDTO> agendas = BuildCenariosFiltraAgendasDisponiveisTest.agendasSemCotasDisponiveis();
        AgendaGradeAtendimentoDTOParam param = BuildCenariosFiltraAgendasDisponiveisTest.agendaGradeAtendimentoDTOParam();
        param.setTipoProcedimento(BuildCenariosFiltraAgendasDisponiveisTest.tipoProcedimentoControlaCotas);


        List<AgendaDTO> agendasDisponiveis = new FiltraAgendasDisponiveis().filtrarAgendasDisponiveis(agendas, param, null);

        assertNotNull("lista de agendas não pode ser null", agendasDisponiveis);
        assertTrue("deve retornar lista de agenda disponivel vazia", agendasDisponiveis.isEmpty());
    }

    @Test
    public void deveFiltrarAgendasDisponiveisSemValidacaoCotas() throws ValidacaoException {

        List<AgendaDTO> agendas = BuildCenariosFiltraAgendasDisponiveisTest.agendasSemCotasDisponiveis();
        AgendaGradeAtendimentoDTOParam param = BuildCenariosFiltraAgendasDisponiveisTest.agendaGradeAtendimentoDTOParam();
        param.setTipoProcedimento(BuildCenariosFiltraAgendasDisponiveisTest.tipoProcedimentoNaoControlaCotas);


        List<AgendaDTO> agendasDisponiveis = new FiltraAgendasDisponiveis().filtrarAgendasDisponiveis(agendas, param, ExameCotaPpi.TipoTeto.FINANCEIRO.value());

        assertNotNull("lista de agendas não pode ser null", agendasDisponiveis);
        assertFalse("lista de agenda disponivel não pode ser vazia", agendasDisponiveis.isEmpty());
        assertEquals("deve possuir 2 agendas com horarios disponíveis", 2, agendasDisponiveis.size());
    }
}