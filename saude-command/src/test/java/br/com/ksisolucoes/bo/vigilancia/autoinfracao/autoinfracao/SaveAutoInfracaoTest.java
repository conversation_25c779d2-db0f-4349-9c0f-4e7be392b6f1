package br.com.ksisolucoes.bo.vigilancia.autoinfracao.autoinfracao;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaEnum;
import junit.framework.TestCase;
import org.joda.time.LocalDate;

import java.util.Date;

public class SaveAutoInfracaoTest extends TestCase {

    public void setUp() throws Exception {
        super.setUp();
    }

    public void testCalcularDataPrazoDefesa_parametroConfiguracaoNullNaoDeveQuebrar() {
        SaveAutoInfracao saveAutoInfracao = new SaveAutoInfracao(getAutoInfracao());

        assertNull(saveAutoInfracao.calcularDataPrazoDefesa(null, new ConfiguracaoVigilancia()));
        assertNull(saveAutoInfracao.calcularDataPrazoDefesa(1L, null));
    }

    public void testCalcularDataPrazoDefesa_deveRetornarDataCumprimento() {
        SaveAutoInfracao saveAutoInfracao = new SaveAutoInfracao(getAutoInfracao());

        Long prazo = 1L;
        Date dataPrazoDefesa = DataUtil.addDiasUteis(new LocalDate("2020-01-01").toDate(), prazo.intValue());

        ConfiguracaoVigilancia configuracaoVigilancia = new ConfiguracaoVigilancia();
        configuracaoVigilancia.setTipoDatabaseCalculoPrazoDefesa(ConfiguracaoVigilanciaEnum.TipoDatabaseCalculoDefesa.RECEBIMENTO.value());
        configuracaoVigilancia.setTipoCalculoPrazoDefesa(ConfiguracaoVigilanciaEnum.TipoCalculoDefesa.DIAS_UTEIS.value());

        assertEquals(dataPrazoDefesa, saveAutoInfracao.calcularDataPrazoDefesa(prazo, configuracaoVigilancia));
    }

    public void testGetDataCumprimentoPrazo_dataBaseNullNaoDeveQuebrar() {
        SaveAutoInfracao saveAutoInfracao = new SaveAutoInfracao(getAutoInfracao());

        Long prazo = 1L;
        assertNull(saveAutoInfracao.getDataCumprimentoPrazo(new ConfiguracaoVigilancia(), null, prazo));
    }

    public void testGetDataCumprimentoPrazo_deveRetornarUmaDataDeCumprimentoConformeRegras() {
        SaveAutoInfracao saveAutoInfracao = new SaveAutoInfracao(getAutoInfracao());
        ConfiguracaoVigilancia configuracaoVigilancia = new ConfiguracaoVigilancia();
        configuracaoVigilancia.setTipoCalculoPrazoDefesa(ConfiguracaoVigilanciaEnum.TipoCalculoDefesa.DIAS_UTEIS.value());

        Long prazo = 1L;
        Date dataBase = new LocalDate("2020-01-01").toDate();

        assertEquals(DataUtil.addDiasUteis(dataBase, prazo.intValue()), saveAutoInfracao.getDataCumprimentoPrazo(configuracaoVigilancia, dataBase, prazo));

        configuracaoVigilancia.setTipoCalculoPrazoDefesa(null);
        configuracaoVigilancia.setInicioProximoDiaUtilAutoInfracao(RepositoryComponentDefault.SIM_LONG);
        assertEquals(Data.addDias(DataUtil.getProximaDataUtil(dataBase), prazo.intValue() - 1), saveAutoInfracao.getDataCumprimentoPrazo(configuracaoVigilancia, dataBase, prazo));

        configuracaoVigilancia.setInicioProximoDiaUtilAutoInfracao(null);
        assertEquals(Data.addDias(dataBase, prazo.intValue()), saveAutoInfracao.getDataCumprimentoPrazo(configuracaoVigilancia, dataBase, prazo));
    }

    private AutoInfracao getAutoInfracao() {
        AutoInfracao autoInfracao = new AutoInfracao();
        autoInfracao.setCodigo(5l);
        autoInfracao.setDataRecebimento(new LocalDate("2020-01-01").toDate());
        autoInfracao.setDataInfracao(new LocalDate("2020-12-31").toDate());
        return autoInfracao;
    }

    public void testGetDateBase_deveRetornarUmaDataBaseConformeRegras() {
        SaveAutoInfracao saveAutoInfracao = new SaveAutoInfracao(getAutoInfracao());
        ConfiguracaoVigilancia configuracaoVigilancia = new ConfiguracaoVigilancia();
        configuracaoVigilancia.setTipoDatabaseCalculoPrazoDefesa(ConfiguracaoVigilanciaEnum.TipoDatabaseCalculoDefesa.RECEBIMENTO.value());
        assertEquals(new LocalDate("2020-01-01").toDate(), saveAutoInfracao.getDateBase(configuracaoVigilancia));
        configuracaoVigilancia.setTipoDatabaseCalculoPrazoDefesa(ConfiguracaoVigilanciaEnum.TipoDatabaseCalculoDefesa.INFRACAO.value());
        assertEquals(new LocalDate("2020-12-31").toDate(), saveAutoInfracao.getDateBase(configuracaoVigilancia));
    }

    public void testGetDateBase_tipoDatabaseCalculoDefesaIgualRecebimentoEDataRecebimentoNullDeveRetornarNull() {
        AutoInfracao autoInfracao = getAutoInfracao();
        autoInfracao.setDataRecebimento(null);

        SaveAutoInfracao saveAutoInfracao = new SaveAutoInfracao(autoInfracao);
        ConfiguracaoVigilancia configuracaoVigilancia = new ConfiguracaoVigilancia();
        configuracaoVigilancia.setTipoDatabaseCalculoPrazoDefesa(ConfiguracaoVigilanciaEnum.TipoDatabaseCalculoDefesa.RECEBIMENTO.value());

        assertNull(saveAutoInfracao.getDateBase(configuracaoVigilancia));
    }
}