package br.com.ksisolucoes.bo.vigilancia.investigacao.investigacaoagravocovid19;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoCovid19;
import org.junit.Test;

import static org.junit.Assert.*;

public class SaveInvestigacaoAgravoCovid19Test {

    @Test
    public void antesSave_devePopularValoresNulos() throws ValidacaoException, DAOException {
        InvestigacaoAgravoCovid19 vo = new InvestigacaoAgravoCovid19();
        SaveInvestigacaoAgravoCovid19 saveInvestigacaoAgravoCovid19 = new SaveInvestigacaoAgravoCovid19(vo);
        saveInvestigacaoAgravoCovid19.antesSave();
        assertEquals(RepositoryComponentDefault.NAO_LONG, vo.getColetado());
        assertEquals(RepositoryComponentDefault.NAO_LONG, vo.getSolicitado());
        assertEquals(RepositoryComponentDefault.NAO_LONG, vo.getDorGarganta());
        assertEquals(RepositoryComponentDefault.NAO_LONG, vo.getDispneia());
        assertEquals(RepositoryComponentDefault.NAO_LONG, vo.getFebre());
        assertEquals(RepositoryComponentDefault.NAO_LONG, vo.getTosse());
        assertEquals(RepositoryComponentDefault.NAO_LONG, vo.getDoencaRespDescompensada());
        assertEquals(RepositoryComponentDefault.NAO_LONG, vo.getDoencaCardCronica());
        assertEquals(RepositoryComponentDefault.NAO_LONG, vo.getDiabetes());
        assertEquals(RepositoryComponentDefault.NAO_LONG, vo.getDoencasRenaisAvancado());
        assertEquals(RepositoryComponentDefault.NAO_LONG, vo.getImunossupressao());
        assertEquals(RepositoryComponentDefault.NAO_LONG, vo.getGestanteAltoRisco());
        assertEquals(RepositoryComponentDefault.NAO_LONG, vo.getPortadorDoencaCromossomica());
        assertEquals(RepositoryComponentDefault.NAO_LONG, vo.getCancelado());
        assertEquals(RepositoryComponentDefault.NAO_LONG, vo.getIgnorado());
        assertEquals(RepositoryComponentDefault.NAO_LONG, vo.getObito());
        assertEquals(RepositoryComponentDefault.NAO_LONG, vo.getCura());
        assertEquals(RepositoryComponentDefault.NAO_LONG, vo.getInternado());
        assertEquals(RepositoryComponentDefault.NAO_LONG, vo.getInternadoUti());
        assertEquals(RepositoryComponentDefault.NAO_LONG, vo.getTratamentoDomiciliar());
        assertEquals(RepositoryComponentDefault.SIM, vo.getFlagInformacoesComplementares());
    }

    @Test
    public void antesSave_NaoDeveSubstituirValores() throws ValidacaoException, DAOException {
        InvestigacaoAgravoCovid19 vo = new InvestigacaoAgravoCovid19();
        vo.setColetado(RepositoryComponentDefault.SIM_LONG);
        vo.setSolicitado(RepositoryComponentDefault.SIM_LONG);
        vo.setDorGarganta(RepositoryComponentDefault.SIM_LONG);
        vo.setDispneia(RepositoryComponentDefault.SIM_LONG);
        vo.setFebre(RepositoryComponentDefault.SIM_LONG);
        vo.setTosse(RepositoryComponentDefault.SIM_LONG);
        vo.setDoencaRespDescompensada(RepositoryComponentDefault.SIM_LONG);
        vo.setDoencaCardCronica(RepositoryComponentDefault.SIM_LONG);
        vo.setDiabetes(RepositoryComponentDefault.SIM_LONG);
        vo.setDoencasRenaisAvancado(RepositoryComponentDefault.SIM_LONG);
        vo.setImunossupressao(RepositoryComponentDefault.SIM_LONG);
        vo.setGestanteAltoRisco(RepositoryComponentDefault.SIM_LONG);
        vo.setPortadorDoencaCromossomica(RepositoryComponentDefault.SIM_LONG);
        vo.setCancelado(RepositoryComponentDefault.SIM_LONG);
        vo.setIgnorado(RepositoryComponentDefault.SIM_LONG);
        vo.setObito(RepositoryComponentDefault.SIM_LONG);
        vo.setCura(RepositoryComponentDefault.SIM_LONG);
        vo.setInternado(RepositoryComponentDefault.SIM_LONG);
        vo.setInternadoUti(RepositoryComponentDefault.SIM_LONG);
        vo.setTratamentoDomiciliar(RepositoryComponentDefault.SIM_LONG);
        vo.setFlagInformacoesComplementares(RepositoryComponentDefault.NAO);

        SaveInvestigacaoAgravoCovid19 saveInvestigacaoAgravoCovid19 = new SaveInvestigacaoAgravoCovid19(vo);
        saveInvestigacaoAgravoCovid19.antesSave();

        assertEquals(RepositoryComponentDefault.SIM_LONG, vo.getColetado());
        assertEquals(RepositoryComponentDefault.SIM_LONG, vo.getSolicitado());
        assertEquals(RepositoryComponentDefault.SIM_LONG, vo.getDorGarganta());
        assertEquals(RepositoryComponentDefault.SIM_LONG, vo.getDispneia());
        assertEquals(RepositoryComponentDefault.SIM_LONG, vo.getFebre());
        assertEquals(RepositoryComponentDefault.SIM_LONG, vo.getTosse());
        assertEquals(RepositoryComponentDefault.SIM_LONG, vo.getDoencaRespDescompensada());
        assertEquals(RepositoryComponentDefault.SIM_LONG, vo.getDoencaCardCronica());
        assertEquals(RepositoryComponentDefault.SIM_LONG, vo.getDiabetes());
        assertEquals(RepositoryComponentDefault.SIM_LONG, vo.getDoencasRenaisAvancado());
        assertEquals(RepositoryComponentDefault.SIM_LONG, vo.getImunossupressao());
        assertEquals(RepositoryComponentDefault.SIM_LONG, vo.getGestanteAltoRisco());
        assertEquals(RepositoryComponentDefault.SIM_LONG, vo.getPortadorDoencaCromossomica());
        assertEquals(RepositoryComponentDefault.SIM_LONG, vo.getCancelado());
        assertEquals(RepositoryComponentDefault.SIM_LONG, vo.getIgnorado());
        assertEquals(RepositoryComponentDefault.SIM_LONG, vo.getObito());
        assertEquals(RepositoryComponentDefault.SIM_LONG, vo.getCura());
        assertEquals(RepositoryComponentDefault.SIM_LONG, vo.getInternado());
        assertEquals(RepositoryComponentDefault.SIM_LONG, vo.getInternadoUti());
        assertEquals(RepositoryComponentDefault.SIM_LONG, vo.getTratamentoDomiciliar());
        assertEquals(RepositoryComponentDefault.NAO, vo.getFlagInformacoesComplementares());
    }
}