# Correção: Conflito com método setEnabled do WebComponent

## 🐛 Problema Identificado

O método `setEnabled(boolean)` já existe na classe pai `WebComponent` do Wicket, causando conflito com nossa implementação personalizada.

## ✅ Solução Implementada

### **Antes (Problemático):**
```java
public class ReCaptchaV3WebComponent extends WebComponent {
    private boolean enabled = true;
    
    // CONFLITO: Este método já existe na classe pai
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
}
```

### **Depois (Corrigido):**
```java
public class ReCaptchaV3WebComponent extends WebComponent {
    private boolean enabled = true;
    
    // Método específico para reCAPTCHA
    public void setReCaptchaEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    // Override que combina ambos os estados
    @Override
    public boolean isEnabled() {
        return enabled && super.isEnabled();
    }
}
```

## 🔧 Mudanças Realizadas

### **1. Renomeação do Método**
- **Antes**: `setEnabled(boolean enabled)`
- **Depois**: `setReCaptchaEnabled(boolean enabled)`

### **2. Override do isEnabled()**
```java
@Override
public boolean isEnabled() {
    return enabled && super.isEnabled();
}
```

**Lógica:**
- `enabled` = Estado específico do reCAPTCHA
- `super.isEnabled()` = Estado do componente Wicket
- **Resultado**: reCAPTCHA só funciona se ambos estiverem habilitados

### **3. Atualização do loadConfiguration()**
```java
private void loadConfiguration() {
    // ... código de carregamento da chave ...
    
    // Verificar se o reCAPTCHA está habilitado globalmente
    try {
        String recaptchaEnabled = BOFactory.getBO(CommomFacade.class)
            .modulo(Modulos.SISTEMA)
            .getParametro("GoogleReCaptchaEnabled");
        setReCaptchaEnabled("S".equalsIgnoreCase(recaptchaEnabled));
    } catch (Exception e) {
        setReCaptchaEnabled(true);
    }
}
```

## 🎯 Benefícios da Correção

### **Compatibilidade com Wicket**
- ✅ **Não sobrescreve** métodos da classe pai
- ✅ **Respeita hierarquia** do Wicket
- ✅ **Funciona com** `component.setEnabled(false)` do Wicket

### **Controle Duplo**
- ✅ **Wicket enabled**: Controla se o componente está ativo
- ✅ **reCAPTCHA enabled**: Controla se o reCAPTCHA está habilitado
- ✅ **Ambos necessários**: Para o reCAPTCHA funcionar

### **Flexibilidade**
- ✅ **Desabilitar via Wicket**: `component.setEnabled(false)`
- ✅ **Desabilitar via reCAPTCHA**: `component.setReCaptchaEnabled(false)`
- ✅ **Desabilitar via parâmetro**: `GoogleReCaptchaEnabled = N`

## 📋 Como Usar Agora

### **Método 1: Via Parâmetro do Sistema**
```java
// Configurar no sistema: GoogleReCaptchaEnabled = S/N
ReCaptchaV3WebComponent component = new ReCaptchaV3WebComponent("recaptcha");
// Automaticamente habilitado/desabilitado conforme parâmetro
```

### **Método 2: Via Código (reCAPTCHA específico)**
```java
ReCaptchaV3WebComponent component = new ReCaptchaV3WebComponent("recaptcha");
component.setReCaptchaEnabled(false); // Desabilita apenas o reCAPTCHA
```

### **Método 3: Via Wicket (componente geral)**
```java
ReCaptchaV3WebComponent component = new ReCaptchaV3WebComponent("recaptcha");
component.setEnabled(false); // Desabilita o componente inteiro
```

### **Método 4: Combinado**
```java
ReCaptchaV3WebComponent component = new ReCaptchaV3WebComponent("recaptcha");

// Ambos devem estar true para funcionar
component.setEnabled(true);           // Wicket enabled
component.setReCaptchaEnabled(true);  // reCAPTCHA enabled

// Verificar estado final
boolean funcionando = component.isEnabled(); // true apenas se ambos true
```

## 🔍 Estados Possíveis

| Wicket Enabled | reCAPTCHA Enabled | `isEnabled()` | Comportamento |
|----------------|-------------------|---------------|---------------|
| `true` | `true` | `true` | ✅ **Funciona normalmente** |
| `true` | `false` | `false` | ❌ reCAPTCHA desabilitado |
| `false` | `true` | `false` | ❌ Componente desabilitado |
| `false` | `false` | `false` | ❌ Ambos desabilitados |

## 🔄 Migração Necessária

### **Se Você Usava Antes:**
```java
// ANTES (não funciona mais)
component.setEnabled(false);
```

### **Use Agora:**
```java
// OPÇÃO 1: Desabilitar apenas reCAPTCHA
component.setReCaptchaEnabled(false);

// OPÇÃO 2: Desabilitar componente inteiro (igual ao antes)
component.setEnabled(false);

// OPÇÃO 3: Configurar via parâmetro do sistema
// GoogleReCaptchaEnabled = N
```

## ⚠️ Pontos de Atenção

### **Backward Compatibility**
- ✅ **`component.setEnabled(false)`** ainda funciona
- ✅ **`component.isEnabled()`** ainda funciona
- ❌ **`component.setEnabled(true/false)` para reCAPTCHA** não funciona mais

### **Debugging**
```java
// Para debug, verificar ambos os estados
System.out.println("Wicket enabled: " + component.isEnabledInHierarchy());
System.out.println("reCAPTCHA enabled: " + component.isEnabled());
```

### **Configuração Recomendada**
```java
// Deixar Wicket controlar o componente
component.setEnabled(isComponenteNecessario());

// Deixar parâmetro do sistema controlar reCAPTCHA
// GoogleReCaptchaEnabled = S/N

// Resultado: Controle automático e flexível
```

## ✅ Status da Correção

- [x] **Método renomeado** para `setReCaptchaEnabled`
- [x] **Override do isEnabled()** implementado
- [x] **loadConfiguration()** atualizado
- [x] **Compatibilidade** com Wicket mantida
- [x] **Controle duplo** implementado
- [x] **Documentação** atualizada

## 🎉 Resultado

Agora o componente:
- ✅ **Respeita a hierarquia** do Wicket
- ✅ **Não causa conflitos** com métodos da classe pai
- ✅ **Oferece controle granular** (Wicket + reCAPTCHA)
- ✅ **Mantém compatibilidade** com uso padrão do Wicket
- ✅ **Funciona automaticamente** com parâmetros do sistema

A correção resolve o conflito e oferece ainda mais flexibilidade! 🚀
