# reCAPTCHA v3 - Teste Manual

## 🔍 Situação Atual

O token ainda está chegando vazio no backend. Preciso dos logs do JavaScript para identificar o problema.

## 📋 Informações Necessárias

### **1. Logs do Console do Navegador**

<PERSON>bra DevTools (F12) → Console e me envie TODOS os logs que aparecem, especialmente:

```javascript
=== SCHEDULING reCAPTCHA v3 execution ===
=== SETTING UP FORM LISTENERS ===
=== AUTO-EXECUTING reCAPTCHA v3 ===
=== STARTING reCAPTCHA v3 execution ===
=== TOKEN RECEIVED ===
=== TOKEN SET SUCCESSFULLY ===
```

**E também qualquer erro:**
```javascript
ERROR: grecaptcha is undefined
ERROR: Empty token received from Google
ERROR: Field not found
```

### **2. Teste Manual Adicionado**

Agora há uma função de teste manual disponível. No console do navegador, execute:

```javascript
testRecaptchaV3_reCaptcha()
```

Isso deve mostrar logs como:
```javascript
=== MANUAL TEST STARTED ===
grecaptcha available: true
Site key: 6LczT2UrAAAAADMdo-TAGnSrVEtqD_bqI94uKzwL
Field found: YES
Field current value: 
Field after manual set: TEST_TOKEN_12345
=== MANUAL TOKEN RECEIVED ===
Token: 03AGdBq26...
Token set in field: 03AGdBq26...
```

## 🔧 Testes para Executar

### **Teste 1: Verificar se Script Está Carregando**
```javascript
// Execute no console
console.log('grecaptcha available:', typeof grecaptcha !== 'undefined');
console.log('grecaptcha.ready available:', typeof grecaptcha !== 'undefined' && typeof grecaptcha.ready === 'function');
console.log('grecaptcha.execute available:', typeof grecaptcha !== 'undefined' && typeof grecaptcha.execute === 'function');
```

### **Teste 2: Verificar Campo Oculto**
```javascript
// Execute no console
var field = document.getElementById('recaptcha_token_reCaptcha');
console.log('Field found:', field ? 'YES' : 'NO');
if (field) {
    console.log('Field ID:', field.id);
    console.log('Field name:', field.name);
    console.log('Field value:', '"' + field.value + '"');
    console.log('Field type:', field.type);
    console.log('Field in form:', field.form ? 'YES' : 'NO');
}
```

### **Teste 3: Listar Todos os Campos Ocultos**
```javascript
// Execute no console
var hiddenFields = document.querySelectorAll('input[type="hidden"]');
console.log('Total hidden fields:', hiddenFields.length);
hiddenFields.forEach(function(field, index) {
    console.log('Field ' + index + ':', {
        id: field.id,
        name: field.name,
        value: field.value,
        inForm: field.form ? 'YES' : 'NO'
    });
});
```

### **Teste 4: Verificar Formulários**
```javascript
// Execute no console
var forms = document.querySelectorAll('form');
console.log('Total forms:', forms.length);
forms.forEach(function(form, index) {
    console.log('Form ' + index + ':', {
        id: form.id,
        action: form.action,
        method: form.method,
        elements: form.elements.length
    });
    
    var recaptchaField = form.querySelector('#recaptcha_token_reCaptcha');
    console.log('  reCAPTCHA field in form:', recaptchaField ? 'YES' : 'NO');
});
```

### **Teste 5: Executar reCAPTCHA Manualmente**
```javascript
// Execute no console
if (typeof grecaptcha !== 'undefined' && grecaptcha.ready) {
    grecaptcha.ready(function() {
        console.log('Executing manual reCAPTCHA...');
        grecaptcha.execute('6LczT2UrAAAAADMdo-TAGnSrVEtqD_bqI94uKzwL', {action: 'manual_test'}).then(function(token) {
            console.log('Manual token received:', token);
            console.log('Token length:', token.length);
            
            var field = document.getElementById('recaptcha_token_reCaptcha');
            if (field) {
                field.value = token;
                console.log('Token set in field successfully');
                console.log('Field value after setting:', field.value);
            } else {
                console.log('Field not found for manual setting');
            }
        }).catch(function(error) {
            console.error('Manual reCAPTCHA error:', error);
        });
    });
} else {
    console.log('grecaptcha not available for manual test');
}
```

## 🚨 Possíveis Problemas

### **Problema 1: Script do Google Não Carrega**
**Sintomas:** `grecaptcha available: false`
**Causas:**
- Chave do site inválida
- Domínio não autorizado
- Bloqueador de anúncios
- Problema de rede

### **Problema 2: Campo Não Encontrado**
**Sintomas:** `Field found: NO`
**Causas:**
- ID do campo incorreto
- Campo não sendo renderizado
- Campo fora do formulário

### **Problema 3: Token Não Gerado**
**Sintomas:** `grecaptcha available: true` mas nenhum token
**Causas:**
- Chave do site incorreta
- Ação não permitida
- Limite de requisições atingido

### **Problema 4: Token Perdido**
**Sintomas:** Token gerado mas campo vazio no submit
**Causas:**
- Formulário sendo resetado
- Campo sendo removido
- Múltiplos submits

## 📞 Informações para Enviar

Para resolver o problema, preciso de:

1. **Todos os logs** do console do navegador (copie e cole tudo)
2. **Resultado do teste manual** (`testRecaptchaV3_reCaptcha()`)
3. **Resultado dos 5 testes** acima
4. **Screenshot** da aba Network mostrando se o script do Google carregou
5. **Chave do site** que está sendo usada (se não for sensível)

## 🎯 Próximos Passos

1. **Recarregue a página** com DevTools aberto
2. **Execute todos os testes** no console
3. **Colete todos os logs** e resultados
4. **Me envie as informações** para análise

Com essas informações detalhadas, vou conseguir identificar exatamente onde está o problema! 🔍
