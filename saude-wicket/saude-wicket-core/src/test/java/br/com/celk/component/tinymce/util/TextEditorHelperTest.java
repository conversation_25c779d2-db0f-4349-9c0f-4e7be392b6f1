package br.com.celk.component.tinymce.util;

import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.EstabelecimentoCerest;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.basico.OrgaoEmissor;
import br.com.ksisolucoes.vo.cadsus.*;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({
        LoadManager.class,
        UsuarioCadsusHelper.class,
        LoadManager.class,
        DataUtil.class,
})
public class TextEditorHelperTest {

    private String textoModelo;
    private TextEditorHelper builder;
    @Mock
    private LoadManager eucMock;
    @Mock
    private LoadManager profissionalMock;
    @Mock
    private LoadManager usuarioCadsusMock;

    @Before
    public void setUp() throws Exception {
        mockStatic(UsuarioCadsusHelper.class);
        mockStatic(LoadManager.class);
        mockStatic(DataUtil.class);
        mockLoadManager(eucMock, EnderecoUsuarioCadsus.class);
        mockLoadManager(profissionalMock, Profissional.class);
        mockLoadManager(usuarioCadsusMock, UsuarioCadsus.class);

        Date data = new GregorianCalendar(2021, Calendar.JANUARY, 22, 10, 33, 10).getTime();
        when(DataUtil.getDataAtual()).thenReturn(data);

        textoModelo = getTextoModelo();
        builder = TextEditorHelper.builder(textoModelo);
    }

    private void mockLoadManager(LoadManager loadManager, Class clazz) {
        when(LoadManager.getInstance(clazz)).thenReturn(loadManager);
        when(loadManager.start()).thenReturn(loadManager);
        when(loadManager.startLeitura()).thenReturn(loadManager);
        when(loadManager.setId(any())).thenReturn(loadManager);
        when(loadManager.addProperty(any())).thenReturn(loadManager);
        when(loadManager.addParameter(ArgumentMatchers.any())).thenReturn(loadManager);
    }

    @Test
    public void builderTest() throws Exception {
        Assert.assertNotNull(builder);
        Assert.assertNotNull(builder.parse());
    }

    @Test
    public void parseTest() throws Exception {
        String parsedText = builder.parse();
        Assert.assertFalse("Nao deve conter caracteres | e @", parsedText.contains("|") || parsedText.contains("@"));
    }

    @Test
    public void parseEstabelecimentoCerestTest() throws Exception {
        builder.setEstabelecimentoCerest(getEstabelecimentoCerest());
        String parsedText = builder.parse();

        Assert.assertTrue(parsedText.contains(getEstabelecimentoCerest().getCnpj()));
        Assert.assertTrue(parsedText.contains(getEstabelecimentoCerest().getRazaoSocial()));
    }

    @Test
    public void parseProfissionalTest() throws Exception {
        when(profissionalMock.getVO()).thenReturn(getProfissional());
        builder.setProfissional(getProfissional());
        builder.setTabelaCbo(getTabelaCbo());
        String parsedText = builder.parse();

        Assert.assertTrue(parsedText.contains(getProfissional().getNome()));
        Assert.assertTrue(parsedText.contains(getProfissional().getNumeroRegistro()));
        Assert.assertTrue(parsedText.contains(getProfissional().getUnidadeFederacaoConselhoRegistro()));
        Assert.assertTrue(parsedText.contains(getProfissional().getConselhoClasse().getSigla()));
        Assert.assertTrue(parsedText.contains(getTabelaCbo().getDescricao()));
    }

    @Test
    public void parseUsuarioCadsusTest() throws Exception {
        when(eucMock.getVO()).thenReturn(getEnderecoUsuarioCadsus());
        builder.setUsuarioCadsus(getUsuarioCadsus());
        String parsedText = builder.parse();

        Assert.assertTrue(parsedText.contains(getUsuarioCadsus().getNomeSocial()));
        Assert.assertTrue(parsedText.contains(Coalesce.asString(getUsuarioCadsus().getRg())));
        Assert.assertTrue(parsedText.contains(getUsuarioCadsus().getCpfFormatado()));
        Assert.assertTrue(parsedText.contains(getUsuarioCadsus().getTabelaCbo().getDescricao()));
        Assert.assertTrue(parsedText.contains(Data.formatar(getUsuarioCadsus().getDataNascimento())));
        Assert.assertTrue(parsedText.contains(getUsuarioCadsus().getIdade().toString()));
        Assert.assertTrue(parsedText.contains(getUsuarioCadsus().getCns()));
        Assert.assertTrue(parsedText.contains(getUsuarioCadsus().getTelefoneFormatado()));
        Assert.assertTrue(parsedText.contains(getUsuarioCadsus().getCelularFormatado()));
        Assert.assertTrue(parsedText.contains(getUsuarioCadsus().getNomeMae()));
        Assert.assertTrue(parsedText.contains(getUsuarioCadsus().getNomePai()));
        Assert.assertTrue(parsedText.contains(getUsuarioCadsus().getSexoFormatado()));
        Assert.assertTrue(parsedText.contains(getEnderecoUsuarioCadsus().getEnderecoComplementoFormatadoComCidade()));
    }

    @Test
    public void parseTextoSemGruposTest() throws Exception {
        builder.setTexto1("Primeiro texto");
        builder.setTexto2("Segundo texto");
        builder.setTexto3("Terceiro texto");
        builder.setTexto4("4 texto");
        builder.setCodigo1(1000004L);

        Date dataFormulario = new GregorianCalendar(2020, Calendar.MARCH, 10).getTime();
        builder.setDataFormulario(dataFormulario);
        Date dataHoraChegada = new GregorianCalendar(2015, Calendar.OCTOBER, 7, 9, 18, 50).getTime();
        builder.setDataHoraChegada(dataHoraChegada);
        String parsedText = builder.parse();

        Assert.assertTrue(parsedText.contains(new SimpleDateFormat("dd/MM/yyyy").format(DataUtil.getDataAtual())));
        Assert.assertTrue(parsedText.contains(new SimpleDateFormat("dd/MM/yyyy - HH:mm:ss").format(DataUtil.getDataAtual())));

        Assert.assertTrue(parsedText.contains(Data.formatar(dataFormulario)));
        Assert.assertTrue(parsedText.contains(new SimpleDateFormat("dd/MM/yyyy - HH:mm:ss").format(dataHoraChegada)));

        Assert.assertTrue(parsedText.contains("Primeiro texto"));
        Assert.assertTrue(parsedText.contains("Segundo texto"));
        Assert.assertTrue(parsedText.contains("Terceiro texto"));
        Assert.assertTrue(parsedText.contains("4 texto"));
        Assert.assertTrue(parsedText.contains("1000004"));
    }

    private EstabelecimentoCerest getEstabelecimentoCerest() {
        EstabelecimentoCerest estabelecimento = new EstabelecimentoCerest();
        estabelecimento.setCnpj("12.345.678/9000-11");
        estabelecimento.setRazaoSocial("RAZAO SOCIAL CEREST");
        return estabelecimento;
    }

    private UsuarioCadsus getUsuarioCadsus() {
        UsuarioCadsus usuarioCadsus = new UsuarioCadsus();
        usuarioCadsus.setNome("PACIENTE MARIA SOUZA");
        usuarioCadsus.setApelido("MARI");
        usuarioCadsus.setRg("445577");
        usuarioCadsus.setCpf("123.456.789-01");
        usuarioCadsus.setTabelaCbo(getTabelaCbo());
        usuarioCadsus.setDataNascimento(new Date());
        usuarioCadsus.setNomeMae("JOANA SOUZA");
        usuarioCadsus.setNomePai("PEDRO SOUZA");
        usuarioCadsus.setSexo("M");
        usuarioCadsus.setEnderecoUsuarioCadsus(getEnderecoUsuarioCadsus());

        return usuarioCadsus;
    }

    private EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        EnderecoUsuarioCadsus enderecoUsuarioCadsus = new EnderecoUsuarioCadsus();
        enderecoUsuarioCadsus.setCodigo(111L);

        enderecoUsuarioCadsus.setCodigo(78L);
        enderecoUsuarioCadsus.setLogradouro("DO COMERCIO");

        TipoLogradouroCadsus tipoLograd = new TipoLogradouroCadsus();
        tipoLograd.setDescricao("AVENIDA");
        enderecoUsuarioCadsus.setTipoLogradouro(tipoLograd);
        enderecoUsuarioCadsus.setComplementoLogradouro("APTO 101");
        enderecoUsuarioCadsus.setNumeroLogradouro("452");
        enderecoUsuarioCadsus.setLote("10");
        enderecoUsuarioCadsus.setQuadra("70");
        enderecoUsuarioCadsus.setBairro("CAMPECHE");
        enderecoUsuarioCadsus.setCep("88444777");

        Cidade cidade = new Cidade();
        cidade.setCodigo(133L);
        cidade.setDescricao("FLORIANOPOLIS");

        Estado estado = new Estado();
        estado.setCodigo(54L);
        estado.setSigla("SC");
        cidade.setEstado(estado);

        return enderecoUsuarioCadsus;
    }

    private Profissional getProfissional() {
        Profissional profissional = new Profissional();
        profissional.setCodigo(123L);
        profissional.setNome("PROFISSIONAL JOAO ANTONIO");
        profissional.setNumeroRegistro("987654-31");
        profissional.setUnidadeFederacaoConselhoRegistro("SC");
        OrgaoEmissor orgaoEmissor = new OrgaoEmissor();
        orgaoEmissor.setCodigo(56L);
        orgaoEmissor.setSigla("CRM");
        profissional.setConselhoClasse(orgaoEmissor);

        return profissional;
    }

    private TabelaCbo getTabelaCbo() {
        TabelaCbo tabelaCbo = new TabelaCbo();
        tabelaCbo.setDescricao("SUS");
        return tabelaCbo;
    }

    @NotNull
    private String getTextoModelo() {
        return "cnpjEstabelecimento: |@cnpjEstabelecimento|\n" +
                "razaoSocialEstabelecimento: |@razaoSocialEstabelecimento|\n" +
                "profissional: |@profissional|\n" +
                "numeroRegistro: |@numeroRegistro|\n" +
                "ufregistroprofissional: |@ufregistroprofissional|\n" +
                "conselhoClasse: |@conselhoClasse|\n" +
                "usuarioCadsus: |@usuarioCadsus|\n" +
                "rg: |@rg|\n" +
                "cpf: |@cpf|\n" +
                "cbo: |@cbo|\n" +
                "dataNascimento: |@dataNascimento|\n" +
                "idade: |@idade|\n" +
                "cns: |@cns|\n" +
                "telefone: |@telefone|\n" +
                "celular: |@celular|\n" +
                "nomemae: |@nomemae|\n" +
                "nomepai: |@nomepai|\n" +
                "sexo: |@sexo|\n" +
                "endereco: |@endereco|\n" +
                "cbo: |@cbo|\n" +
                "data: |@data|\n" +
                "dataHora: |@dataHora|\n" +
                "dataFormulario: |@dataFormulario|\n" +
                "dataHoraChegadaPaciente: |@dataHoraChegadaPaciente|\n" +
                "texto1Formulario: |@texto1Formulario|\n" +
                "texto2Formulario: |@texto2Formulario|\n" +
                "texto3Formulario: |@texto3Formulario|\n" +
                "texto4Formulario: |@texto4Formulario|\n" +
                "codigo1Formulario: |@codigo1Formulario|";
    }
}
