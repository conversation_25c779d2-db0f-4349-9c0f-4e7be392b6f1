package br.com.celk.view.atendimento.prontuario.panel.encaminhamento.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.RowNumberColumn;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoPacienteDTO;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoReservadosDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import ch.lambdaj.Lambda;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlAgendarAtendimentoEncaminhamentoEspecialista extends Panel {

    private Form<AgendaGradeAtendimentoDTO> form;
    private AgendaGradeAtendimentoPacienteDTO agendaGradeAtendimentoPacienteDTO;
    private AbstractAjaxButton btnContinuar;
    private Table<AgendaGradeAtendimentoReservadosDTO> tblHorariosReservados;
    private List<AgendaGradeAtendimentoReservadosDTO> horariosReservados;

    public boolean disableContinuar = false;

    public PnlAgendarAtendimentoEncaminhamentoEspecialista(String id) {
        super(id);
        init();
    }

    private void init() {
        form = new Form("form");

        AgendaGradeAtendimentoDTO proxy = Lambda.on(AgendaGradeAtendimentoDTO.class);

        form.add(new DisabledInputField(path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getData())));
        form.add(new DisabledInputField("hora", new PropertyModel(this, "agendaGradeAtendimentoPacienteDTO.agendaGradeHorario.descricaoHora")));
        form.add(new DisabledInputField(path(proxy.getTipoProcedimento().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getAgendaGradeAtendimento().getTipoAtendimentoAgenda().getDescricao())));

        form.add(tblHorariosReservados = new Table("tblHorariosReservados", getColumnsAgenda(), getCollectionProviderAgenda()));

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                PnlAgendarAtendimentoEncaminhamentoEspecialista.this.onConfirmar(target, agendaGradeAtendimentoPacienteDTO);
            }
        });

        form.add(btnContinuar = new AbstractAjaxButton("btnContinuar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onContinuar(target, agendaGradeAtendimentoPacienteDTO);
            }
        });

        form.add(new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        add(form);
    }

    public List<IColumn> getColumnsAgenda() {
        List<IColumn> columns = new ArrayList<>();
        AgendaGradeAtendimentoReservadosDTO proxy = on(AgendaGradeAtendimentoReservadosDTO.class);

        columns.add(new RowNumberColumn(bundle("posicao")));
        columns.add(new DateColumn(bundle("dia"), path(proxy.getAgendaGradeAtendimentoDTO().getAgendaGradeAtendimento().getAgendaGrade().getData())).setPattern("dd/MM"));
        columns.add(createColumn(bundle("horarios"), proxy.getHorariosReservados()));

        return columns;
    }

    public ICollectionProvider getCollectionProviderAgenda() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object o) throws DAOException, ValidacaoException {
                return horariosReservados;
            }
        };
    }

    public abstract void onConfirmar(AjaxRequestTarget target, AgendaGradeAtendimentoPacienteDTO dto) throws ValidacaoException, DAOException;

    public abstract void onContinuar(AjaxRequestTarget target, AgendaGradeAtendimentoPacienteDTO dto) throws ValidacaoException, DAOException;

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void update(AjaxRequestTarget target) {
        if (!form.getModel().getObject().getTipoProcedimento().habilitaAgendamentoGrupo() || disableContinuar) {
            btnContinuar.setVisible(false);
            tblHorariosReservados.setVisible(false);
            target.add(btnContinuar);
            target.add(tblHorariosReservados);

        } else {
            tblHorariosReservados.populate(target);
        }

        target.add(form);
    }

    public void setModel(AgendaGradeAtendimentoDTO dto) {
        form.setModel(new CompoundPropertyModel<>(dto));
    }

    public void setAgendaGradeAtendimentoPacienteDTO(AgendaGradeAtendimentoPacienteDTO agendaGradeAtendimentoPacienteDTO) {
        this.agendaGradeAtendimentoPacienteDTO = agendaGradeAtendimentoPacienteDTO;
    }

    public void setHorariosReservados(List<AgendaGradeAtendimentoReservadosDTO> horariosReservados) {
        this.horariosReservados = horariosReservados;
    }

    public void setDisableContinuar(boolean disableContinuar) {
        this.disableContinuar = disableContinuar;
    }
}
