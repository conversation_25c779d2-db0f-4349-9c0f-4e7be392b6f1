package br.com.celk.component.tree.node.usercomponent;

import br.com.celk.component.checkbox.Check;
import br.com.celk.component.checkbox.Check.Selection;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public interface ISelectableUserComponent extends IUserComponent{

    public Check getCheck();
    
    public void validateParentSelection(AjaxRequestTarget target);
    
    public void validateChildSelection(AjaxRequestTarget target);

    public boolean isSelected();
    
    public Selection getSelection();

    public void setSelected(AjaxRequestTarget target, Selection selected);
    
    public void activate(AjaxRequestTarget target, boolean active);
    
    public Component getComponent();
    
}
