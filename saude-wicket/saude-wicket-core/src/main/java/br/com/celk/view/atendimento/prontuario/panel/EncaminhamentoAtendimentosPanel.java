package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.table.Table;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.QueryProfissionalTipoAtendimentoDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.ProfissionalFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.web.encaminhamento.dto.EncaminhamentoAtendimentosDTO;
import br.com.ksisolucoes.bo.prontuario.web.encaminhamento.interfaces.LoadInterceptorEloNaturezaTipoEncaminhamento;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.encaminhamento.AtendimentoEncaminhamento;
import br.com.ksisolucoes.vo.prontuario.encaminhamento.EloNaturezaTipoEncaminhamento;
import br.com.ksisolucoes.vo.prontuario.encaminhamento.EncaminhamentoTipo;
import ch.lambdaj.Lambda;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR> S. Schmoeller
 */
public class EncaminhamentoAtendimentosPanel extends ProntuarioCadastroPanel {

    private List<AtendimentoEncaminhamento> atendimentoEncaminhamentosList;
    private IModel<EncaminhamentoAtendimentosDTO> model;
    private Table tblEncaminhamentosRealizados;
    private DropDown dropDownProfissional;
    private DropDown<EloNaturezaTipoEncaminhamento> dropDownEncaminhamento;

    public EncaminhamentoAtendimentosPanel(String id, String titulo) {
        super(id, titulo);
    }

    @Override
    public void postConstruct() {
        super.postConstruct();

        Form form = new Form("form", model = new CompoundPropertyModel<EncaminhamentoAtendimentosDTO>(new EncaminhamentoAtendimentosDTO()));

        EncaminhamentoAtendimentosDTO proxy = on(EncaminhamentoAtendimentosDTO.class);


        form.add(getDropDownEncaminhamentos(path(proxy.getEloNaturezaTipoEncaminhamento())).setLabel(new Model(bundle("encaminhamento"))));

        form.add(getDropDownProfissional(path(proxy.getProfissional())));

        form.add(new AbstractAjaxButton("btnGerarEncaminhamento") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                ajuste(target);
            }
        });
        form.add(tblEncaminhamentosRealizados = new Table("tblEncaminhamentosRealizados", getColumns(), getCollectionProvider()));
        tblEncaminhamentosRealizados.populate();
        add(form);
    }

    private void ajuste(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (model.getObject().getAtendimento() == null) {
            model.getObject().setAtendimento(getAtendimento());
        }

        if (model.getObject().getEloNaturezaTipoEncaminhamento() != null && model.getObject().getProfissional() != null) {
            gerarEncaminhamento(target);
        } else {
            getPage().warn("Todos os campos do encaminhamento tem que ser preenchidos");
        }

    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        AtendimentoEncaminhamento proxy = on(AtendimentoEncaminhamento.class);

        columns.add(createColumn(bundle("data"), proxy.getAtendimento().getDataAtendimento()));
        columns.add(createColumn(bundle("profissional"), proxy.getProfissional().getNome()));
        columns.add(createColumn(bundle("tipoAtendimento"), proxy.getEncaminhamentoTipo().getDescricao()));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                AtendimentoEncaminhamento proxy = Lambda.on(AtendimentoEncaminhamento.class);

                atendimentoEncaminhamentosList = LoadManager.getInstance(AtendimentoEncaminhamento.class)
                        .addProperties(new HQLProperties(AtendimentoEncaminhamento.class).getProperties())
                        .addProperties(new HQLProperties(Atendimento.class, path(proxy.getAtendimento())).getProperties())
                        .addProperties(new HQLProperties(Profissional.class, path(proxy.getProfissional())).getProperties())
                        .addProperties(new HQLProperties(EncaminhamentoTipo.class, path(proxy.getEncaminhamentoTipo())).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getAtendimento().getUsuarioCadsus()), getAtendimento().getUsuarioCadsus()))
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getAtendimento()), getAtendimento()))
                        .start().getList();

                return atendimentoEncaminhamentosList;
            }
        };
    }

    private DropDown getDropDownProfissional(String id) {
        this.dropDownProfissional = new DropDown(id);
        this.dropDownProfissional.addAjaxUpdateValue();
        this.dropDownProfissional.addChoice(null, "");
        return this.dropDownProfissional;
    }

    private DropDown getDropDownEncaminhamentos(String id) {
        if (this.dropDownEncaminhamento == null) {
            this.dropDownEncaminhamento = new DropDown(id);
            this.dropDownEncaminhamento.addAjaxUpdateValue();
            dropDownEncaminhamento.addChoice(null, "");

            List<EloNaturezaTipoEncaminhamento> elos = LoadManager.getInstance(EloNaturezaTipoEncaminhamento.class)
                    .addProperties(new HQLProperties(EloNaturezaTipoEncaminhamento.class).getProperties())
                    .addProperties(new HQLProperties(EncaminhamentoTipo.class, EloNaturezaTipoEncaminhamento.PROP_ENCAMINHAMENTO_TIPO).getProperties())
                    .addProperties(new HQLProperties(NaturezaProcuraTipoAtendimento.class, EloNaturezaTipoEncaminhamento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO).getProperties())
                    .addProperties(new HQLProperties(TipoAtendimento.class, EloNaturezaTipoEncaminhamento.PROP_TIPO_ATENDIMENTO).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EloNaturezaTipoEncaminhamento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO), getAtendimento().getNaturezaProcuraTipoAtendimento()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EloNaturezaTipoEncaminhamento.PROP_TIPO_ATENDIMENTO), QueryCustom.QueryCustomParameter.DIFERENTE, getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EloNaturezaTipoEncaminhamento.PROP_ENCAMINHAMENTO_TIPO, EncaminhamentoTipo.PROP_VISIVEL), RepositoryComponentDefault.SIM_LONG.longValue()))
                    .addInterceptor(new LoadInterceptorEloNaturezaTipoEncaminhamento(getAtendimento().getEmpresa()))
                    .start().getList();

            for (EloNaturezaTipoEncaminhamento eloNaturezaTipoEncaminhamento : elos) {
                if (eloNaturezaTipoEncaminhamento.getEncaminhamentoTipo().getTipo() != EncaminhamentoTipo.Tipo.ALTA.value() && eloNaturezaTipoEncaminhamento.getEncaminhamentoTipo().getTipo() != EncaminhamentoTipo.Tipo.ALTA_MEDICACAO.value()) {
                    dropDownEncaminhamento.addChoice(eloNaturezaTipoEncaminhamento, eloNaturezaTipoEncaminhamento.getEncaminhamentoTipo().getDescricao());
                }
            }

            this.dropDownEncaminhamento.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    carregarProfissionais(target);
                }
            });
        }

        return this.dropDownEncaminhamento;
    }

    private void carregarProfissionais(AjaxRequestTarget target) {
        dropDownProfissional.limpar(target);

        dropDownProfissional.removeAllChoices(target);
        dropDownProfissional.addChoice(null, "");

        try {
            if (model.getObject().getEloNaturezaTipoEncaminhamento() != null) {
                QueryProfissionalTipoAtendimentoDTOParam param = new QueryProfissionalTipoAtendimentoDTOParam();
                param.setEmpresa(getAtendimento().getEmpresa());
                param.setTipoAtendimento(model.getObject().getEloNaturezaTipoEncaminhamento().getTipoAtendimento());

                List<Profissional> profissionais = BOFactoryWicket.getBO(ProfissionalFacade.class).consultaProfissionalTipoAtendimento(param);
                for (Profissional profissional : profissionais) {
                    if (profissional.getCodigo() != getAtendimento().getProfissional().getCodigo()) {
                        dropDownProfissional.addChoice(profissional, profissional.getNome());
                    }
                }
            }
        } catch (SGKException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private void gerarEncaminhamento(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        EncaminhamentoAtendimentosDTO dto = model.getObject();
        dto.setAtendimento(getAtendimento());
        dto.setEncaminhamentoTipo(model.getObject().getEloNaturezaTipoEncaminhamento().getEncaminhamentoTipo());
        for (AtendimentoEncaminhamento atendimentoEncaminhamento : atendimentoEncaminhamentosList) {
            if (atendimentoEncaminhamento.getProfissional().getCodigo() == dto.getProfissional().getCodigo().longValue() && atendimentoEncaminhamento.getEncaminhamentoTipo().getCodigo() == dto.getEncaminhamentoTipo().getCodigo().longValue()) {
                throw new ValidacaoException(bundle("encaminhamentoRepete", atendimentoEncaminhamento.getProfissional().getDescricaoFormatado()));
            }
        }
        BOFactoryWicket.getBO(AtendimentoFacade.class).gerarEncaminhamentoAtendimentos(dto);

        tblEncaminhamentosRealizados.update(target);
    }

}
