package br.com.celk.view.basico.cidade.pnl;

import br.com.celk.component.consulta.PnlConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.basico.cidade.customize.CustomizeConsultaCidade;
import br.com.ksisolucoes.vo.basico.Cidade;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class PnlConsultaCidade extends PnlConsulta<Cidade> {

    public PnlConsultaCidade(String id, IModel<Cidade> model, boolean required) {
        super(id, model, required);
    }

    public PnlConsultaCidade(String id, IModel<Cidade> model) {
        super(id, model);
    }

    public PnlConsultaCidade(String id, boolean required) {
        super(id, required);
    }

    public PnlConsultaCidade(String id) {
        super(id);
    }

    @Override
    public IConsultaConfigurator<Cidade> getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator<CustomizeConsultaCidade>() {

            @Override
            public CustomizeConsultaCidade getCustomizeConsultaInstance() {
                return new CustomizeConsultaCidade();
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("cidades");
    }

}
