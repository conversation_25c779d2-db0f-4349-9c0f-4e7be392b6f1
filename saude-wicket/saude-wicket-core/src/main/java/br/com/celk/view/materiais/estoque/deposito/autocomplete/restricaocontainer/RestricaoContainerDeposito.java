package br.com.celk.view.materiais.estoque.deposito.autocomplete.restricaocontainer;

import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.inputfield.InputField;
import br.com.ksisolucoes.bo.basico.dto.QueryConsultaDepositoDTOParam;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class RestricaoContainerDeposito extends Panel implements IRestricaoContainer<QueryConsultaDepositoDTOParam> {

    private InputField<String> txtDescricao;
    
    private QueryConsultaDepositoDTOParam param = new QueryConsultaDepositoDTOParam();
    
    public RestricaoContainerDeposito(String id) {
        super(id);
        
        WebMarkupContainer root = new WebMarkupContainer("root", new CompoundPropertyModel(param));
        
        root.add(txtDescricao = new InputField<String>("descricao"));
        
        add(root);
    }

    @Override
    public QueryConsultaDepositoDTOParam getRestricoes() {
        return param;
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        txtDescricao.limpar(target);
    }

    @Override
    public Component getComponentRequestFocus() {
        return txtDescricao;
    }
}
