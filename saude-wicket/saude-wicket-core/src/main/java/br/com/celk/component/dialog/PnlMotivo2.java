package br.com.celk.component.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.InputArea;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlMotivo2 extends Panel {

    private AbstractAjaxButton btnFechar;
    private InputArea<String> txtMotivo;

    private String motivo;

    public PnlMotivo2(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(txtMotivo = new InputArea("motivo"));

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (motivo == null) {
                    throw new ValidacaoException("Por favor, informe o motivo.");
                }
                onConfirmar(target, motivo);
                limpar(target);
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                limpar(target);
                onFechar(target);
            }
        });

        add(form);

        btnFechar.setDefaultFormProcessing(false);

        txtMotivo.add(new AttributeModifier("maxlength", getMaxLengthMotivo()));
    }

    public abstract Long getMaxLengthMotivo();

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public abstract void onConfirmar(AjaxRequestTarget target, String motivo) throws ValidacaoException, DAOException;

    public void limpar(AjaxRequestTarget target) {
        this.txtMotivo.limpar(target);
        target.focusComponent(txtMotivo);
    }
}
