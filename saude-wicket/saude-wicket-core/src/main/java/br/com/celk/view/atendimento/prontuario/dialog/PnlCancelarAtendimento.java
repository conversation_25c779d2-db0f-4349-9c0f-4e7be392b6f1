package br.com.celk.view.atendimento.prontuario.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.model.LoadableObjectModel;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.Coalesce;
import br.com.celk.view.basico.motivocancelamento.pnl.PnlConsultaMotivoCancelamento;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlCancelarAtendimento extends Panel {

    private LoadableObjectModel<Atendimento> model;
    private PnlConsultaMotivoCancelamento pnlConsultaMotivoCancelamento;
    private InputArea txaObservacao;

    public PnlCancelarAtendimento(String id) {
        super(id);
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(model = new LoadableObjectModel<Atendimento>(Atendimento.class, new Atendimento())));

        form.add(pnlConsultaMotivoCancelamento = new PnlConsultaMotivoCancelamento(Atendimento.PROP_MOTIVO));
        form.add(txaObservacao = new InputArea(Atendimento.PROP_OBS_CANCELAMENTO));

        form.add(new AbstractAjaxButton("btnOk") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (validarCancelar(target)) {
                    onOk(target, model.getObject());
                }
            }
        });

        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        add(form);
    }

    public abstract void onOk(AjaxRequestTarget target, Atendimento atendimento) throws ValidacaoException, DAOException;

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void setAtendimento(Atendimento atendimento) {
        model.setObject(atendimento);
    }

    public void limpar(AjaxRequestTarget target) {
        pnlConsultaMotivoCancelamento.limpar(target);
        txaObservacao.limpar(target);
        target.focusComponent(pnlConsultaMotivoCancelamento.getTxtCodigo());
    }

    public boolean validarCancelar(AjaxRequestTarget target) {
        try {
            if (pnlConsultaMotivoCancelamento.getComponentValue() == null && Coalesce.asString(txaObservacao.getComponentValue()).trim().length() == 0) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_motivo_observacao_cancelamento_deve_ser_definido"));
            }

            Atendimento atendimento = model.getObject();

            if (RepositoryComponentDefault.SIM.equals(atendimento.getUsuarioCadsus().getRecemNascido())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_atendimento_recem_nascido_nao_cancelado"));
            }

            if (atendimento.getNaturezaProcuraTipoAtendimento() != null) {

                NaturezaProcuraTipoAtendimento npta = LoadManager.getInstance(NaturezaProcuraTipoAtendimento.class)
                        .addProperty(VOUtils.montarPath(NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_FLAG_CANCELA_PRIMEIRO_ATENDIMENTO))
                        .addParameter(new QueryCustomParameter(NaturezaProcuraTipoAtendimento.PROP_CODIGO, atendimento.getNaturezaProcuraTipoAtendimento().getCodigo()))
                        .start().getVO();

                if (npta != null && RepositoryComponentDefault.SIM_LONG.equals(npta.getTipoAtendimento().getFlagCancelaPrimeiroAtendimento())) {
                    if (!atendimento.equals(atendimento.getAtendimentoPrincipal())) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_somente_primeiro_atendimento_finalizar"));
                    }
                }
            }

            if (!atendimento.getStatus().equals(Atendimento.STATUS_AGUARDANDO) && !atendimento.getStatus().equals(Atendimento.STATUS_PENDENTE_LANCAMENTO_PRODUCAO)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_nao_permitido_cancelar_atendimentos_status_X", atendimento.getDescricaoStatus()));
            }
        } catch (ValidacaoException e) {
            MessageUtil.modalWarn(target, this, e);
            return false;
        }

        return true;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
    }
}
