package br.com.celk.system.asyncprocess;

import br.com.celk.system.asyncprocess.interfaces.IAsyncReportNotification;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
public class AsyncProcessHelper {

    public static void removerReportPanel(AjaxRequestTarget target, Component component, AsyncProcess asyncProcess) throws ValidacaoException, DAOException {
        BOFactoryWicket.getBO(BasicoFacade.class).removerAsyncProcess(Arrays.asList(asyncProcess));
        ApplicationSession.get().getAsyncCache().remove(asyncProcess);
        asyncProcess.setStatus(AsyncProcess.STATUS_EXCLUIDO);
        IAsyncReportNotification asyncReportNotification = component.findParent(IAsyncReportNotification.class);
        if (asyncReportNotification != null) {
            asyncReportNotification.notifyReport(target, asyncProcess);
        }
    }

    public static StatusAsyncProcess resolveStatus(AsyncProcess asyncProcess) {
        StatusAsyncProcess statusAsyncProcess = null;
        if (asyncProcess.getStatus().equals(AsyncProcess.STATUS_PROCESSANDO)) {
            statusAsyncProcess = StatusAsyncProcess.PROCESSANDO;
        } else if (asyncProcess.getStatus().equals(AsyncProcess.STATUS_CONCLUIDO_ERRO)) {
            statusAsyncProcess = StatusAsyncProcess.CONCLUIDO_ERRO;
        } else if (asyncProcess.getStatus().equals(AsyncProcess.STATUS_CONCLUIDO_EXITO)) {
            statusAsyncProcess = StatusAsyncProcess.CONCLUIDO_EXITO;
        } else if (asyncProcess.getStatus().equals(AsyncProcess.STATUS_AGUARDANDO_PROCESSAMENTO) && AsyncProcess.CATEGORIA_FILA_POR_HORA.equals(asyncProcess.getCategoriaFila())) {
            statusAsyncProcess = StatusAsyncProcess.AGUARDANDO_PROCESSAMENTO_POR_HORA;
        } else if (asyncProcess.getStatus().equals(AsyncProcess.STATUS_AGUARDANDO_PROCESSAMENTO)) {
            statusAsyncProcess = StatusAsyncProcess.AGUARDANDO_PROCESSAMENTO;
        } else if (asyncProcess.getStatus().equals(AsyncProcess.STATUS_SEM_PAGINAS)) {
            statusAsyncProcess = StatusAsyncProcess.SEM_PAGINAS;
        } else if (asyncProcess.getStatus().equals(AsyncProcess.STATUS_MUITAS_PAGINAS)) {
            statusAsyncProcess = StatusAsyncProcess.MUITAS_PAGINAS;
        }
        return statusAsyncProcess;
    }

    public enum StatusAsyncProcess {

        CONCLUIDO_EXITO("hudson blue", BundleManager.getString("concluido")),
        AGUARDANDO_PROCESSAMENTO("celk-icon ampulheta", BundleManager.getString("aguardandoProcessamento")),
        AGUARDANDO_PROCESSAMENTO_POR_HORA("celk-icon calendar", BundleManager.getString("agendado")),
        CONCLUIDO_ERRO("hudson red", BundleManager.getString("erro")),
        PROCESSANDO("celk-icon loading", BundleManager.getString("processando")),
        SEM_PAGINAS("hudson yellow", BundleManager.getString("semPaginas")),
        MUITAS_PAGINAS("hudson orange", "Muitas páginas"),
        ;
        private String icon;
        private String title;

        private StatusAsyncProcess(String icon, String title) {
            this.icon = icon;
            this.title = title;
        }

        public String icon() {
            return icon;
        }

        public String title() {
            return title;
        }
    }

}
