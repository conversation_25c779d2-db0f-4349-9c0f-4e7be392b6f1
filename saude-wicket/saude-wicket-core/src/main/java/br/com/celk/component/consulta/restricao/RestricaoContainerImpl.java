package br.com.celk.component.consulta.restricao;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.markup.repeater.RepeatingView;

public class RestricaoContainerImpl extends Panel implements IRestricaoContainer<List<QueryParameter>>{

    private RepeatingView panel;
    private ICustomizeConsultaQuery customizeConsultaQuery;
    private Map<String, BuilderQueryCustom.QueryParameter> filterProperties = new LinkedHashMap<String, BuilderQueryCustom.QueryParameter>();
    private List<IRestricao<BuilderQueryCustom.QueryParameter>> restricoes = new ArrayList<IRestricao<BuilderQueryCustom.QueryParameter>>();
    private PanelRestricao first;

    public RestricaoContainerImpl(String id, ICustomizeConsultaQuery customizeConsultaQuery) {
        super(id);
        
        this.customizeConsultaQuery = customizeConsultaQuery;

        setOutputMarkupId(true);

        panel = new RepeatingView("restricoes");
        
        add(panel);

        panel.newChildId();
        
        initRestricoes();
    }
    
    private void initRestricoes() {
        customizeConsultaQuery.consultaCustomizeFilterProperties(filterProperties);
        first  = addRestricao(false);
    }

    public String newItemId() {
        return panel.newChildId();
    }

    public PanelRestricao addRestricao(boolean enableBtnRemove) {
        PanelRestricao panelRestricao = new PanelRestricao(newItemId(), customizeConsultaQuery.getClassConsulta(), filterProperties, enableBtnRemove);
        panel.add(panelRestricao);
        restricoes.add(panelRestricao);
        return panelRestricao;
    }
    
    public void addRestricao(AjaxRequestTarget target) {
        addRestricao(true);
        target.add(this);
    }
    
    public void removeRestricao(AjaxRequestTarget target, IRestricao restricao) {
        restricoes.remove(restricao);
        panel.remove((Component) restricao);
        target.add(this);
    }
    

    @Override
    public List<QueryParameter> getRestricoes() {
        List<QueryParameter> parameters = new ArrayList<QueryParameter>();

        for (IRestricao<QueryParameter> iRestricao : restricoes) {
            parameters.add(iRestricao.getRestricao());
        }
        
        return parameters;
    }
    
    @Override
    public void limpar(AjaxRequestTarget target){
        for (IRestricao<QueryParameter> iRestricao : restricoes) {
            if (!iRestricao.equals(first)) {
                panel.remove((Component) iRestricao);
            }
        }

        first.limpar(target);
        
        restricoes.clear();
        restricoes.add(first);
        
        target.add(this);
    }

    @Override
    public Component getComponentRequestFocus() {
        return (Component) first.getComponent();
    }
}
