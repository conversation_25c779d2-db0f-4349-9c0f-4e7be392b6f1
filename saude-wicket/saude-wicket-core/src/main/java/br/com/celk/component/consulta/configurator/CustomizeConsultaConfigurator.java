package br.com.celk.component.consulta.configurator;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.consulta.restricao.RestricaoContainerImpl;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;

/**
 *
 * <AUTHOR>
 */
public abstract class CustomizeConsultaConfigurator<T extends ICustomizeConsultaQuery> extends ConsultaConfigurator {

    private T customizeConsultaQuery;

    @Override
    public IRestricaoContainer getRestricaoContainerInstance(String id) {
        return new RestricaoContainerImpl(id, getCustomizeConsultaQuery());
    }

    @Override
    public IPagerProvider getDataProviderInstance() {
        return new CustomizeConsultaPagerProvider(getCustomizeConsultaQuery()) {
            @Override
            public void customQuery(ICustomizeConsultaQuery customizeConsultaQuery) {
                CustomizeConsultaConfigurator.this.customQuery((T) customizeConsultaQuery);
            }

            @Override
            public SortParam getDefaultSort() {
                return CustomizeConsultaConfigurator.this.getDefaultSort();
            }

            @Override
            public List<BuilderQueryCustom.QueryParameter>  getSearchParam(String searchCriteria) {
                return CustomizeConsultaConfigurator.this.getSearchParam(searchCriteria);
            }

            @Override
            public List getInterceptors() {
                return getLoadInterceptor();
            }
            
            
        };
    }

    @Override
    public void getColumns(List<IColumn> columns) {
        HashMap<String, String> viewProperties = new HashMap<String, String>();

        getCustomizeConsultaQuery().consultaCustomizeViewProperties(viewProperties);

        ColumnFactory columnFactory = new ColumnFactory(getReferenceClass());

        for (Map.Entry<String, String> entry : viewProperties.entrySet()) {
            columns.add(columnFactory.createSortableColumn(entry.getKey(), entry.getValue()));
        }
    }

    private T getCustomizeConsultaQuery() {
        if (this.customizeConsultaQuery == null) {
            this.customizeConsultaQuery = getCustomizeConsultaInstance();
        }

        return this.customizeConsultaQuery;
    }

    @Override
    public Class getReferenceClass() {
        return getCustomizeConsultaQuery().getClassConsulta();
    }

    public abstract T getCustomizeConsultaInstance();

    public void customQuery(T customizeConsultaQuery) {
    }

    public SortParam getDefaultSort() {
        return null;
    }

    public List<BuilderQueryCustom.QueryParameter> getSearchParam(String searchCriteria) {
        return null;
    }
    
    public List<LoadInterceptor> getLoadInterceptor(){return null;}
}
