package br.com.celk.view.agenda.tipoprocedimento.autocomplete;

import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.settings.PnlInformarTipoProcedimento;
import br.com.ksisolucoes.agendamento.dto.DadosAgendamentoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoPacienteDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ConsultaUsuarioCadsusDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgInformarTipoProcedimento extends Window {

    private PnlInformarTipoProcedimento pnlInformarTipoProcedimento;

    public DlgInformarTipoProcedimento(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("informeTipoProcedimento");
            }
        });

        setInitialWidth(500);
        setInitialHeight(50);

        setResizable(false);

        setContent(pnlInformarTipoProcedimento = new PnlInformarTipoProcedimento(getContentId()) {

            @Override
            public void onOk(AjaxRequestTarget target, TipoProcedimento tipoProcedimento, AgendaGradeAtendimentoPacienteDTO agendaGradeAtendimentoPacienteDTO) throws ValidacaoException, DAOException {
                DlgInformarTipoProcedimento.this.onOk(target, tipoProcedimento, agendaGradeAtendimentoPacienteDTO);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                DlgInformarTipoProcedimento.this.fechar(target);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {

            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                return false;
            }
        });
    }

    public abstract void onOk(AjaxRequestTarget target, TipoProcedimento tipoProcedimento, AgendaGradeAtendimentoPacienteDTO agendaGradeAtendimentoPacienteDTO) throws ValidacaoException, DAOException;
    public abstract void onFechar(AjaxRequestTarget target);

    public void show(AjaxRequestTarget target, DadosAgendamentoDTO dadosAgendamentoDTO, AgendaGradeAtendimentoPacienteDTO agendaGradeAtendimentoPacienteDTO) {
        show(target);
        pnlInformarTipoProcedimento.setObject(target, dadosAgendamentoDTO, agendaGradeAtendimentoPacienteDTO);
    }

    public void fechar(AjaxRequestTarget target) {
        pnlInformarTipoProcedimento.limpar(target);
        close(target);
        onFechar(target);
    }
}