  function loadScroll()
  {
      console.log("Carregado tratamento do Scroll")
      if(!scrollDownId){
        console.error("Defina um scrollDownId");
      }

      if(!carregarMaisId){
          console.error("Defina um carregarMaisId");
       }

      var lastScrollTop = 0;
      var calledInternal;

      //para não rodar duas vezes num espaço curto de tempo
      var runned = false;

      setInterval(function(){
          runned = false;
      }, 1000)//Testar o tempo necessário


      var tableBody = $('#'+scrollDownId).find('.dataTables_scrollBody');
      if(scrollLoaded){
        calledInternal = true;
        tableBody.scrollTop(tableBody[0].scrollHeight -575);
      }

      tableBody.unbind('scroll')
      tableBody.bind('scroll', function()
                                  {
                                   var st = $(this).scrollTop();
                                   //Verifica se o scroll foi feito para baixo
                                   if (st > lastScrollTop){
                                      if(!calledInternal && !runned && $(this).scrollTop() + $(this).innerHeight()>=$(this)[0].scrollHeight)
                                      {

                                            $("#" + carregarMaisId).trigger("click");
                                            runned = true;
                                      }
                                   }
                                   lastScrollTop = st;
                                   if(calledInternal){
                                    calledInternal = false;
                                   }

                                  })
  }


