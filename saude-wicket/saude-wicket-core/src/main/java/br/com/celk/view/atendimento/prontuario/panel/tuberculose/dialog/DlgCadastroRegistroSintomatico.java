package br.com.celk.view.atendimento.prontuario.panel.tuberculose.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseSintomatico;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgCadastroRegistroSintomatico extends Window{

    private PnlCadastroRegistroSintomatico pnlCadastroRegistroSintomatico;

    public DlgCadastroRegistroSintomatico(String id){
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("registroSintomatico");
            }
        });
                
        setInitialWidth(600);
        setInitialHeight(250);
        setResizable(true);
        
        setContent(pnlCadastroRegistroSintomatico = new PnlCadastroRegistroSintomatico(getContentId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, TuberculoseSintomatico tuberculoseSintomatico) throws ValidacaoException, DAOException {
                close(target);
                DlgCadastroRegistroSintomatico.this.onConfirmar(target, tuberculoseSintomatico);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target, TuberculoseSintomatico tuberculoseSintomatico) throws ValidacaoException, DAOException;
    
    public void show(AjaxRequestTarget target, TuberculoseSintomatico tuberculoseSintomatico){
        show(target);
        pnlCadastroRegistroSintomatico.setObject(target, tuberculoseSintomatico);
    }    
}