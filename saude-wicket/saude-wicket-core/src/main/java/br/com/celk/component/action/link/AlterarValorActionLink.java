package br.com.celk.component.action.link;

import br.com.celk.system.bundle.BundleManager;

/**
 *
 * <AUTHOR>
 */
public class AlterarValorActionLink extends AbstractLinkPanel{

    public AlterarValorActionLink(String id) {
        super(id);
        init();
    }

    private void init() {
        setDirtyForm(true);
    }
    
    @Override
    public String getCustomTitle() {
        return BundleManager.getString("alterarValorTotal");
    }

    @Override
    public String getCustomClass() {
        return "icon shopping-bag";
    }

}
