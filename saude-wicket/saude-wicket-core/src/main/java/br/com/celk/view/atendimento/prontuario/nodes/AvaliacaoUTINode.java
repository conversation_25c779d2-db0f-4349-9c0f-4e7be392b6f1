package br.com.celk.view.atendimento.prontuario.nodes;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.resources.Icon32;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.prontuario.nodes.annotations.ProntuarioNode;
import br.com.celk.view.atendimento.prontuario.panel.AvaliacaoUTIPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;

/**
 *
 * <AUTHOR>
 */
@ProntuarioNode(NodesAtendimentoRef.AVALIACAO_UTI)
public class AvaliacaoUTINode extends ProntuarioNodeImp {

    @Override
    public ProntuarioCadastroPanel getPanel(String id) {
        return new AvaliacaoUTIPanel(id);
    }

    @Override
    public Icon32 getIcone() {
        return Icon32.TEMPERATURE_4;
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("controleSinaisVitais");
    }
}
