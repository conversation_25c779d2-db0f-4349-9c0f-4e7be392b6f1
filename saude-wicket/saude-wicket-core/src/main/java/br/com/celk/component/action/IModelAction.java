package br.com.celk.component.action;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public interface IModelAction<T> extends Serializable{

    public void action(AjaxRequestTarget target, T modelObject) throws ValidacaoException, DAOException;
    
}
