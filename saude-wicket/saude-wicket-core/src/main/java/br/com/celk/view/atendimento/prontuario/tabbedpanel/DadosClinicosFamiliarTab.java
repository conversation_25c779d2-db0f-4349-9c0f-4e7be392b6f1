package br.com.celk.view.atendimento.prontuario.tabbedpanel;

import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.helper.atendimento.HistoricoPacienteLoadHelper;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.DadosClinicoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NoHistoricoClinicoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.web.historico.dto.HistoricoMedicamentoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusPatologia;
import br.com.ksisolucoes.vo.programasaude.ProgramaSaudeUsuario;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoPrimario;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class DadosClinicosFamiliarTab extends TabPanel<NoHistoricoClinicoDTO> {

    private Atendimento ultimoAtendimento;

    private Table tblProgramasSaude;
    private Table tblHistoricoAvaliacoes;
    private Table tblPrincipaisPatologias;
    private Table tblMedicamentos;
    private Table tblUltimoAtendimento;

    public DadosClinicosFamiliarTab(String id, NoHistoricoClinicoDTO object) {
        super(id, object);
        init();
    }

    public void init() {
        setOutputMarkupId(true);

        NoHistoricoClinicoDTO proxy = on(NoHistoricoClinicoDTO.class);

        add(tblProgramasSaude = new Table("tblProgramasSaude", getColumnsProgramasSaude(), getCollectionProviderProgramasSaude()));
        tblProgramasSaude.populate();

        add(tblHistoricoAvaliacoes = new Table("tblHistoricoAvaliacoes", getColumnsHistoricoAvaliacoes(), getCollectionProviderHistoricoAvaliacoes()));
        tblHistoricoAvaliacoes.populate();
        tblHistoricoAvaliacoes.setScrollY("272px");
        tblHistoricoAvaliacoes.setScrollX("768px");

        add(tblPrincipaisPatologias = new Table("tblPrincipaisPatologias", getColumnsPrincipaisPatologias(), getCollectionProviderPrincipaisPatologias()));
        tblPrincipaisPatologias.populate();

        add(tblMedicamentos = new Table("tblMedicamentos", getColumnsMedicamentos(), getCollectionProviderMedicamentos()));
        tblMedicamentos.populate();

        add(tblUltimoAtendimento = new Table("tblUltimoAtendimento", getColumnsUltimoAtendimento(), getCollectionProviderUltimoAtendimento()));
        tblUltimoAtendimento.populate();
    }

    private ICollectionProvider getCollectionProviderUltimoAtendimento() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                List<Atendimento> list = new ArrayList();
                if (ultimoAtendimento != null) {
                    list.add(ultimoAtendimento);
                }
                return list;
            }
        };
    }

    private List<IColumn> getColumnsUltimoAtendimento() {
        List<IColumn> columns = new ArrayList();

        Atendimento proxy = on(Atendimento.class);

        columns.add(new DateTimeColumn(bundle("dataAtendimento"), path(proxy.getDataAtendimento())));
        columns.add(createColumn(bundle("tipoAtendimento"), proxy.getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricao()));
        columns.add(createColumn(bundle("unidade"), proxy.getEmpresa().getDescricao()));

        return columns;
    }

    private List<IColumn> getColumnsProgramasSaude() {
        List<IColumn> columns = new ArrayList();

        ProgramaSaudeUsuario proxy = on(ProgramaSaudeUsuario.class);

        columns.add(createColumn(bundle("programa"), proxy.getDescricaoProgramaSaude()));
        columns.add(new DateTimeColumn(bundle("dataCadastro"), path(proxy.getDataCadastro())));

        return columns;
    }

    private ICollectionProvider getCollectionProviderProgramasSaude() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (object.getDadosClinicoDTO() != null) {
                    return object.getDadosClinicoDTO().getProgramasSaudeList();
                }
                return null;
            }
        };
    }

    private List<IColumn> getColumnsHistoricoAvaliacoes() {
        List<IColumn> columns = new ArrayList();

        AtendimentoPrimario proxy = on(AtendimentoPrimario.class);

        columns.add(new DateTimeColumn(bundle("data"), path(proxy.getAtendimento().getDataAtendimento())));
        columns.add(new DoubleColumn(bundle("pesoKg"), path(proxy.getPeso())).setCasasDecimais(3));
        columns.add(createColumn(bundle("temperatura"), proxy.getTemperatura()));
        columns.add(createColumn(bundle("pas"), proxy.getPressaoArterialSistolica()));
        columns.add(createColumn(bundle("pad"), proxy.getPressaoArterialDiastolica()));
        columns.add(new DoubleColumn(bundle("alturaCm"), path(proxy.getAltura())).setCasasDecimais(1));
        columns.add(createColumn(bundle("glicemia"), proxy.getGlicemia()));
        columns.add(createColumn(bundle("imc"), proxy.getImc()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderHistoricoAvaliacoes() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (object.getDadosClinicoDTO() != null) {
                    return object.getDadosClinicoDTO().getAtendimentoPrimarioList();
                }
                return null;
            }
        };
    }

    private List<IColumn> getColumnsPrincipaisPatologias() {
        List<IColumn> columns = new ArrayList();

        UsuarioCadsusPatologia proxy = on(UsuarioCadsusPatologia.class);

        columns.add(new DateTimeColumn(bundle("data"), path(proxy.getDataAtendimento())));
        columns.add(createColumn(bundle("cid"), proxy.getCid().getCodigo()));
        columns.add(createColumn(bundle("descricao"), proxy.getCid().getDescricao()));
        columns.add(createColumn(bundle("observacao"), proxy.getObservacao()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderPrincipaisPatologias() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (object.getDadosClinicoDTO() != null) {
                    return object.getDadosClinicoDTO().getPatologiaList();
                }
                return null;
            }
        };
    }

    private List<IColumn> getColumnsMedicamentos() {
        List<IColumn> columns = new ArrayList();

        HistoricoMedicamentoDTO proxy = on(HistoricoMedicamentoDTO.class);

        columns.add(createColumn(bundle("medicamento"), proxy.getNomeProduto()));
        columns.add(createColumn(bundle("posologia"), proxy.getPosologia()));
        columns.add(createColumn(bundle("cid"), proxy.getCodigoCid()));
        columns.add(new DateColumn(bundle("ultimaReceita"), path(proxy.getDataUltimaReceita())));
        columns.add(createColumn(bundle("tipoReceita"), proxy.getDescricaoTipoReceita()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderMedicamentos() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (object.getDadosClinicoDTO() != null) {
                    return object.getDadosClinicoDTO().getHistoricoMedicamentoList();
                }
                return null;
            }
        };
    }

    public void setDados(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus) {
        try {

            object.setUsuarioCadsus(usuarioCadsus);
            object.getDadosClinicoDTO().setPatologiaList(HistoricoPacienteLoadHelper.getPatologiaList(usuarioCadsus.getCodigo()));
            object.getDadosClinicoDTO().setAtendimentoPrimarioList(HistoricoPacienteLoadHelper.getAtendimentoPrimarioList(usuarioCadsus.getCodigo()));
            object.getDadosClinicoDTO().setUltimoAtendimentoPrimario(HistoricoPacienteLoadHelper.getUltimoAtendimentoPrimario(usuarioCadsus.getCodigo()));
            object.getDadosClinicoDTO().setHistoricoMedicamentoList(BOFactoryWicket.getBO(AtendimentoFacade.class).carregarHistoricoMedicamentosPaciente(usuarioCadsus.getCodigo(), null));

            this.ultimoAtendimento = BOFactoryWicket.getBO(AtendimentoFacade.class).consultarUltimoHistoricoAtendimentoProntuarioFamiliar(usuarioCadsus.getCodigo());

        } catch (DAOException|ValidacaoException e) {
            Loggable.log.error(e);
        }

        tblProgramasSaude.update(target);
        tblHistoricoAvaliacoes.update(target);
        tblPrincipaisPatologias.update(target);
        tblMedicamentos.update(target);
        tblUltimoAtendimento.update(target);
    }

    public void clear(AjaxRequestTarget target) {
        this.ultimoAtendimento = null;

        object.setUsuarioCadsus(null);
        object.setDadosClinicoDTO(new DadosClinicoDTO());

        tblProgramasSaude.update(target);
        tblHistoricoAvaliacoes.update(target);
        tblPrincipaisPatologias.update(target);
        tblMedicamentos.update(target);
        tblUltimoAtendimento.update(target);
    }

    @Override
    public String getTitle() {
        return bundle("dadosClinicos");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);

        response.render(OnDomReadyHeaderItem.forScript(JScript.initExpandLinks()));
    }

}
