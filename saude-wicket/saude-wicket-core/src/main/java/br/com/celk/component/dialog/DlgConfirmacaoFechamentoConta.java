package br.com.celk.component.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgConfirmacaoFechamentoConta extends Window {

    private PnlConfirmacaoFechamentoConta pnlConfirmacao;
    private String message;
    private int width;
    private int height;

    public DlgConfirmacaoFechamentoConta(String id) {
        super(id);
        this.message = "";
        init();
    }

    public DlgConfirmacaoFechamentoConta(String id, String message) {
        super(id);
        this.message = message;
        width = 500;
        height = 60;
        init();
    }

    public DlgConfirmacaoFechamentoConta(String id, String message, int width, int height) {
        super(id);
        this.message = message;
        this.width = width;
        this.height = height;
        init();
    }

    private void init() {
        setTitle(getDialogTitle());

        setInitialHeight(height);
        setInitialWidth(width);
        setResizable(false);

        setContent(pnlConfirmacao = new PnlConfirmacaoFechamentoConta(getContentId(), message) {

            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
                DlgConfirmacaoFechamentoConta.this.onConfirmar(target);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
                DlgConfirmacaoFechamentoConta.this.onFechar(target);
            }

            @Override
            public String getConfirmarLabel() {
                return DlgConfirmacaoFechamentoConta.this.getConfirmarLabel();
            }

            @Override
            public String getFecharLabel() {
                return DlgConfirmacaoFechamentoConta.this.getFecharLabel();
            }

            @Override
            public void configurarButtons(AbstractAjaxButton btnConfirmar, AbstractAjaxButton btnFechar) {
                DlgConfirmacaoFechamentoConta.this.configurarButtons(btnConfirmar, btnFechar);
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
    }

    public String getConfirmarLabel() {
        return BundleManager.getString("confirmar");
    }

    public String getFecharLabel() {
        return BundleManager.getString("fechar");
    }

    public String getDialogTitle() {
        return BundleManager.getString("confirmar");
    }

    public void setMessage(AjaxRequestTarget target, String message) {
        pnlConfirmacao.setMessage(target, message);
    }

    public void configurarButtons(AbstractAjaxButton btnConfirmar, AbstractAjaxButton btnFechar) {
        btnConfirmar.add(new AttributeModifier("value", getConfirmarLabel()));
        btnFechar.add(new AttributeModifier("value", getFecharLabel()));
    }

}
