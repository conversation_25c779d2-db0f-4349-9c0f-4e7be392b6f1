package br.com.celk.component.treetable.pageable.customdatatable;

import br.com.celk.bo.treetable.interfaces.dto.TreeTableDTO;
import org.apache.wicket.Component;
import org.apache.wicket.extensions.markup.html.repeater.data.grid.ICellPopulator;
import org.apache.wicket.extensions.markup.html.repeater.tree.table.NodeBorder;
import org.apache.wicket.extensions.markup.html.repeater.tree.table.NodeModel;
import org.apache.wicket.markup.html.panel.EmptyPanel;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.IModel;

public class ITreeColumn<T extends TreeTableDTO> extends CustomTreeColumn<TreeTableDTO, String> {

    public ITreeColumn(IModel<String> displayModel) {
        super(displayModel);
    }

    @Override
    public void populateItem(Item<ICellPopulator<TreeTableDTO>> cellItem, String componentId, IModel<TreeTableDTO> rowModel) {
        if (rowModel.getObject().isHeader()) {
            Component component = new EmptyPanel(componentId);
            cellItem.add(component);
        } else {
            rowModel.getObject().setDescricao("");
            NodeModel<TreeTableDTO> nodeModel = (NodeModel<TreeTableDTO>) rowModel;
            Component nodeComponent = getTree().newNodeComponent(componentId, nodeModel.getWrappedModel());
            nodeComponent.add(new NodeBorder(nodeModel.getBranches()));
            cellItem.add(nodeComponent);
        }
    }
}
