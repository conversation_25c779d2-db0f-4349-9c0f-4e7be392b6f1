package br.com.celk.component.tokenautocomplete.behavior;

import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxEventBehavior;
import org.apache.wicket.markup.head.IHeaderResponse;

/**
 *
 * <AUTHOR>
 */
public abstract class OnAddBehavior extends AjaxEventBehavior{

    public OnAddBehavior() {
        super("onAdd");
    }

    @Override
    public void onConfigure(Component component) {
        component.add(new AttributeModifier("onadd", getCallbackUrl().toString()));
    }

    @Override
    public void renderHead(Component component, IHeaderResponse response) {
    }

}
