package br.com.celk.view.atendimento.prontuario.nodes;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.prontuario.nodes.annotations.ProntuarioNode;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.atendimento.prontuario.panel.ProcedimentosPanel;

/**
 *
 * <AUTHOR>
 */
@ProntuarioNode(NodesAtendimentoRef.PROCEDIMENTOS)
public class ProcedimentosNode extends ProntuarioNodeImp{

    @Override
    public ProntuarioCadastroPanel getPanel(String id) {
        return new ProcedimentosPanel(id, getTitulo());
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("procedimentos");
    }

}
