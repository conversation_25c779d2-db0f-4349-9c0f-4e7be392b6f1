package br.com.celk.component.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AutorizacaoInternacaoHospitalarDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

import java.io.Serializable;

public abstract class DlgAutorizacaoDoLeito<T extends Serializable> extends Window {

    private final String titulo;
    private PnlAutorizacaoDoLeito pnlAutorizacaoDoLeito;
    private AutorizacaoInternacaoHospitalarDTO aihDto;

    public DlgAutorizacaoDoLeito(String id, String titulo) {
        super(id);
        this.titulo = titulo;
        init();
    }

    public DlgAutorizacaoDoLeito(String id, String titulo, AutorizacaoInternacaoHospitalarDTO aihDto) {
        super(id);
        this.titulo = titulo;
        this.aihDto = aihDto;
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        setInitialWidth(700);
        setInitialHeight(400);

        setResizable(false);

        setTitle(titulo);

        setContent(pnlAutorizacaoDoLeito = new PnlAutorizacaoDoLeito(getContentId(), aihDto) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, String motivo, AutorizacaoInternacaoHospitalarDTO dtoAih) throws ValidacaoException, DAOException {
                close(target);
                DlgAutorizacaoDoLeito.this.onConfirmar(target, motivo, dtoAih);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) {
                close(target);
            }

            @Override
            public void onExcluir(AjaxRequestTarget target, AutorizacaoInternacaoHospitalarDTO object) throws ValidacaoException, DAOException {
                close(target);
                if (object.getReservaLeito() != null) {
                    BOFactoryWicket.delete(object.getReservaLeito());
                }
                DlgAutorizacaoDoLeito.this.onExcluir(target, object);
            }

            @Override
            public void onEditar(AjaxRequestTarget target, AutorizacaoInternacaoHospitalarDTO object) throws ValidacaoException, DAOException {
                close(target);
                DlgAutorizacaoDoLeito.this.onExcluir(target, object);
            }

            @Override
            public Long getMaxLengthMotivo() {
                return DlgAutorizacaoDoLeito.this.getMaxLengthMotivo();
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target, String motivo, AutorizacaoInternacaoHospitalarDTO object) throws ValidacaoException, DAOException;

    public abstract void onExcluir(AjaxRequestTarget target, AutorizacaoInternacaoHospitalarDTO object) throws ValidacaoException, DAOException;

    public abstract void onEditar(AjaxRequestTarget target, AutorizacaoInternacaoHospitalarDTO object) throws ValidacaoException, DAOException;

    public Long getMaxLengthMotivo() {
        return 200L;// Tamanho default
    }

    public void show(AjaxRequestTarget target, AutorizacaoInternacaoHospitalarDTO aihDto) {
        pnlAutorizacaoDoLeito.setObject(target, aihDto);
        show(target);
    }

    public void showEditar(AjaxRequestTarget target, String motivo, AutorizacaoInternacaoHospitalarDTO aihDto) {
        pnlAutorizacaoDoLeito.setObjectEditar(target, motivo, aihDto);
        show(target);
    }
}
