/*
 * Copyright 2012 claudio.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package br.com.celk.component.link;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.log.Loggable;
import com.itextpdf.html2pdf.HtmlConverter;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.markup.ComponentTag;
import org.apache.wicket.markup.html.link.Link;
import org.apache.wicket.request.handler.resource.ResourceReferenceRequestHandler;
import org.apache.wicket.request.resource.ResourceReference;
import org.apache.wicket.request.resource.SharedResourceReference;

import java.io.*;

/**
 * <AUTHOR>
 */
public abstract class RtfReportLink extends Link {

    public static final String TARGET_BLANK = "_blank";
    public static final String TARGET_SELF = "_self";
    public static final String TARGET_PARENT = "_parent";
    public static final String TARGET_TOP = "_top";

    private String target;


    public RtfReportLink(String id) {
        this(id, TARGET_BLANK);
    }

    public RtfReportLink(String id, String target) {
        super(id);
        if (target != null) {
            add(new AttributeModifier("target", target));
        }
    }

    @Override
    protected void disableLink(ComponentTag tag) {
        String clazz = (String) tag.getAttributes().get("class");

        if (clazz == null) {
            clazz = "";
        }

        clazz += " disabled ";

        tag.getAttributes().put("class", clazz);

        super.disableLink(tag);
    }

    @Override
    public void onClick() {
        imprimirOpenOffice();
    }

    private void imprimirOpenOffice() {
        try {
            String absolutePath;
            if (getRtfText() != null
                    && ".RTF".equals(Coalesce.asString(getRtfText().substring(getRtfText().indexOf("."), getRtfText().length())).toUpperCase())) {

                File newFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".pdf");
                OutputStream os = new FileOutputStream(newFile);

                HtmlConverter.convertToPdf(new FileInputStream(getRtfText()), os);

                absolutePath = newFile.getAbsolutePath();
            } else {
                absolutePath = getRtfText();
            }

            ResourceReference resourceReference = new SharedResourceReference("staticReport");

            ResourceReferenceRequestHandler referenceRequestHandler = new ResourceReferenceRequestHandler(resourceReference);

            referenceRequestHandler.getPageParameters().add("retornoLocal", absolutePath);

            getRequestCycle().scheduleRequestHandlerAfterCurrent(referenceRequestHandler);
        } catch (ReportException | IOException e) {
            Loggable.log.error(e.getMessage(), e);
        }
    }

    public abstract String getRtfText() throws ReportException;

}
