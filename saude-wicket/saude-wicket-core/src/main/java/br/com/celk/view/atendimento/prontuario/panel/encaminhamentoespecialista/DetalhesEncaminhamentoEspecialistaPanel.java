package br.com.celk.view.atendimento.prontuario.panel.encaminhamentoespecialista;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputarea.DisabledInputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.methods.WicketMethods;
import br.com.celk.view.atendimento.prontuario.panel.EncaminhamentoEspecialistaPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.SoapDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.encaminhamento.interfaces.dto.EncaminhamentoConsultaDTO;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento;
import br.com.ksisolucoes.vo.prontuario.basico.EncaminhamentoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.EncaminhamentoConsulta;
import br.com.ksisolucoes.vo.prontuario.basico.TipoEncaminhamento;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class DetalhesEncaminhamentoEspecialistaPanel extends ProntuarioCadastroPanel {

    private EncaminhamentoConsulta encaminhamentoConsulta;
    private Form<EncaminhamentoConsultaDTO> form;
    private WebMarkupContainer containerExameResultado;
    private WebMarkupContainer containerCancelamento;
    private WebMarkupContainer containerUrgente;
    private WebMarkupContainer containerAgendamento;
    private boolean origemPainelSoap;
    private SoapDTO.ContainerTelaSoap containerTelaSoap;

    public DetalhesEncaminhamentoEspecialistaPanel(String id, Long codigo, boolean origemPainelSoap, SoapDTO.ContainerTelaSoap containerTelaSoap) {
        super(id, bundle("encaminhamento"));
        carregarEncaminhamentoConsulta(codigo);
        this.origemPainelSoap = origemPainelSoap;
        this.containerTelaSoap = containerTelaSoap;
    }

    private void carregarEncaminhamentoConsulta(Long codigo) {
        encaminhamentoConsulta = LoadManager.getInstance(EncaminhamentoConsulta.class)
                .addProperties(new HQLProperties(EncaminhamentoConsulta.class).getProperties())
                .addProperties(new HQLProperties(Encaminhamento.class, EncaminhamentoConsulta.PROP_ENCAMINHAMENTO).getProperties())
                .addProperty(VOUtils.montarPath(EncaminhamentoConsulta.PROP_ENCAMINHAMENTO, Encaminhamento.PROP_ENCAMINHAMENTO_AGENDAMENTO, EncaminhamentoAgendamento.PROP_DATA_AGENDAMENTO))
                .addProperty(VOUtils.montarPath(EncaminhamentoConsulta.PROP_ENCAMINHAMENTO, Encaminhamento.PROP_ENCAMINHAMENTO_AGENDAMENTO, EncaminhamentoAgendamento.PROP_STATUS))
                .addProperty(VOUtils.montarPath(EncaminhamentoConsulta.PROP_ENCAMINHAMENTO, Encaminhamento.PROP_TIPO_ENCAMINHAMENTO, TipoEncaminhamento.PROP_FLAG_GERAR_AGENDAMENTO))
                .addProperty(VOUtils.montarPath(EncaminhamentoConsulta.PROP_ENCAMINHAMENTO, Encaminhamento.PROP_ENCAMINHAMENTO_AGENDAMENTO, EncaminhamentoAgendamento.PROP_LOCAL_AGENDAMENTO, Empresa.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(EncaminhamentoConsulta.PROP_ENCAMINHAMENTO, Encaminhamento.PROP_ENCAMINHAMENTO_AGENDAMENTO, EncaminhamentoAgendamento.PROP_LOCAL_AGENDAMENTO, Empresa.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(EncaminhamentoConsulta.PROP_ENCAMINHAMENTO, Encaminhamento.PROP_ENCAMINHAMENTO_AGENDAMENTO, EncaminhamentoAgendamento.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(EncaminhamentoConsulta.PROP_ENCAMINHAMENTO, Encaminhamento.PROP_ENCAMINHAMENTO_AGENDAMENTO, EncaminhamentoAgendamento.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                .addParameter(new QueryCustom.QueryCustomParameter(EncaminhamentoConsulta.PROP_CODIGO, codigo))
                .start().getVO();

    }

    @Override
    public void postConstruct() {
        super.postConstruct();

        EncaminhamentoConsultaDTO proxy = on(EncaminhamentoConsultaDTO.class);

        form = new Form<EncaminhamentoConsultaDTO>("form", new CompoundPropertyModel(this));
        form.setOutputMarkupId(true);

        form.add(new DisabledInputField(path(proxy.getEncaminhamentoConsulta().getEncaminhamento().getDataCadastro())));
        form.add(new DisabledInputField(path(proxy.getEncaminhamentoConsulta().getEncaminhamento().getTipoEncaminhamento().getDescricaoFormatado())));
        form.add(new DisabledInputField(path(proxy.getEncaminhamentoConsulta().getEncaminhamento().getDescricaoStatus())));
        
        form.add(new DisabledInputArea(path(proxy.getEncaminhamentoConsulta().getMotivoConsulta())));

        form.add(containerExameResultado = new WebMarkupContainer("containerExameResultado"));
        containerExameResultado.setOutputMarkupId(true);
        containerExameResultado.add(new DisabledInputArea(path(proxy.getEncaminhamentoConsulta().getExameResultado())));

        form.add(containerCancelamento = new WebMarkupContainer("containerCancelamento"));
        containerCancelamento.setOutputMarkupId(true);
        containerCancelamento.add(new DisabledInputArea(path(proxy.getEncaminhamentoConsulta().getEncaminhamento().getDescricaoCancelamento())));
        
        form.add(containerUrgente = new WebMarkupContainer("containerUrgente"));
        containerUrgente.setOutputMarkupId(true);

        containerUrgente.add(getDropDownUrgente());
        containerUrgente.add(new DisabledInputArea(path(proxy.getEncaminhamentoConsulta().getEncaminhamento().getJustificativaUrgencia())));

        form.add(containerAgendamento = new WebMarkupContainer("containerAgendamento"));
        containerAgendamento.setOutputMarkupId(true);

        containerAgendamento.add(new DateChooser(path(proxy.getEncaminhamentoConsulta().getEncaminhamento().getEncaminhamentoAgendamento().getDataAgendamento())).setEnabled(false));
        containerAgendamento.add(new DisabledInputField(path(proxy.getEncaminhamentoConsulta().getEncaminhamento().getEncaminhamentoAgendamento().getLocalAgendamento().getDescricaoFormatado())));
        containerAgendamento.add(new DisabledInputField(path(proxy.getEncaminhamentoConsulta().getEncaminhamento().getEncaminhamentoAgendamento().getProfissional().getDescricaoFormatado())));
        containerAgendamento.add(new DisabledInputField(path(proxy.getEncaminhamentoConsulta().getEncaminhamento().getEncaminhamentoAgendamento().getDescricaoStatus())));
        
        form.add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                EncaminhamentoEspecialistaPanel encaminhamentoEspecialistaPanel = new EncaminhamentoEspecialistaPanel(getProntuarioController().panelId(), false, origemPainelSoap, containerTelaSoap);
                getProntuarioController().changePanel(target, encaminhamentoEspecialistaPanel);
            }
        });

        add(form);
    }

    private DropDown getDropDownUrgente() {
        DropDown urgente = new DropDown("encaminhamentoConsulta.encaminhamento.flagUrgencia");

        urgente.addChoice(RepositoryComponentDefault.URGENTE_NAO, WicketMethods.bundle("nao", this));
        urgente.addChoice(RepositoryComponentDefault.URGENTE_SIM, WicketMethods.bundle("sim", this));
        urgente.setEnabled(false);

        return urgente;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.initExpandLinks()));
        if (StringUtils.trimToNull(encaminhamentoConsulta.getExameResultado()) != null) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerExameResultado)));
        }
        
        if (Encaminhamento.STATUS_CANCELADO.equals(encaminhamentoConsulta.getEncaminhamento().getStatus())) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerCancelamento)));
        }

        if (RepositoryComponentDefault.URGENTE_SIM.equals(encaminhamentoConsulta.getEncaminhamento().getFlagUrgencia())) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerUrgente)));
        }

        if (encaminhamentoConsulta.getEncaminhamento().getEncaminhamentoAgendamento().getDataAgendamento() != null) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerAgendamento)));
        }
    }
}