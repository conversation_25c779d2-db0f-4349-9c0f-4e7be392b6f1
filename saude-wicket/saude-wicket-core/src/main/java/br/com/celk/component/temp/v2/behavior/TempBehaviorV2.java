package br.com.celk.component.temp.v2.behavior;

import br.com.celk.component.behavior.DefaultComponentBehavior;
import br.com.celk.component.temp.interfaces.ITempStoreComponent;
import br.com.celk.component.temp.v2.TempBehaviorFactory;
import br.com.celk.resources.Resources;
import br.com.celk.system.bundle.BundleManager;
import org.apache.wicket.Component;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;

/**
 *
 * <AUTHOR>
 */
public class TempBehaviorV2 extends DefaultComponentBehavior {

    @Override
    protected void onBind() {
        if (!(getComponent() instanceof ITempStoreComponent)) {
            throw new IllegalStateException(BundleManager.getString("componenteXNaoImplementaInterfaceTempStoreComponent", getComponent().getClass().getSimpleName()));
        }
        ITempStoreComponent tempStoreComponent = (ITempStoreComponent) getComponent();
        tempStoreComponent.setTempBehaviorStrategy(new TempBehaviorFactory().createStrategy(getComponent()));
        tempStoreComponent.getTempBehaviorStrategy().onBind(getComponent());
    }

    @Override
    public void renderHead(Component component, IHeaderResponse response) {
        super.renderHead(component, response);
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_TEMP_COMPONENTS));
        ITempStoreComponent tempStoreComponent = (ITempStoreComponent) getComponent();
        tempStoreComponent.getTempBehaviorStrategy().onRenderHead(component, response);
    }

}
