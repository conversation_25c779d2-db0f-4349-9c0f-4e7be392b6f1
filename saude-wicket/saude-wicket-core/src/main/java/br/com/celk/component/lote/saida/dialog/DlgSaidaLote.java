package br.com.celk.component.lote.saida.dialog;

import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoGrupoEstoqueItemDTO;
import org.apache.wicket.ajax.AjaxRequestTarget;

import java.util.List;

/**
 * <AUTHOR>
 */
public class DlgSaidaLote extends Window {

    private PanelSaidaLote panelSaidaLote;

    public DlgSaidaLote(String id) {
        super(id);
        init();
    }

    private void init() {
        setMinimalWidth(900);
        setMinimalHeight(300);

        setContent(panelSaidaLote = new PanelSaidaLote(getContentId()) {

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });

        setTitle(BundleManager.getString("lotes"));
    }

    public void setList(List<MovimentoGrupoEstoqueItemDTO> itens) {
        panelSaidaLote.setList(itens);
    }

    public void update(AjaxRequestTarget target) {
        panelSaidaLote.update(target);
    }

    public void add(ISelectionAction<MovimentoGrupoEstoqueItemDTO> selectionAction) {
        panelSaidaLote.add(selectionAction);
    }

    public void limparSelecionado() {
        panelSaidaLote.limparSelecionado();
    }

    public void limparSelecionado(AjaxRequestTarget target) {
        panelSaidaLote.limparSelecionado(target);
    }

    @Override
    public void show(AjaxRequestTarget target) {
        super.show(target);
        panelSaidaLote.abrirPanel(target);
    }

}
