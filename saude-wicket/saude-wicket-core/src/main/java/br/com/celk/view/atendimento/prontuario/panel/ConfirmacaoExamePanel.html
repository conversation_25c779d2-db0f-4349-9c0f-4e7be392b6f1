<wicket:extend>
    <form wicket:id="form">
        <div class="field">
            <div class="span-horizontal">
                <fieldset>
                    <h2><label><wicket:message key="dadosExame"/></label></h2>
                    <div class="field"><label><wicket:message key="tipoExame" /></label><input type="text" size="60" wicket:id="atendimentoExame.tipoExame.descricao" /></div>
                    <div class="field"><label><wicket:message key="convenio"/></label><select wicket:id="atendimentoExame.atendimento.convenio"/></div>
                    <div class="field"><label><wicket:message key="profissionalSolicitante" /></label><input type="text" size="60" wicket:id="atendimentoExame.nomeProfissional" /></div>
                    <div class="field"><label><wicket:message key="dataEntrega"/></label><div class="group" wicket:id="atendimentoExame.dataEntrega"/></div>
                    <div class="field">
                        <div class="span-horizontal">
                            <fieldset>
                                <h2><label><wicket:message key="observacao"/></label></h2>
                                <div class="field">
                                    <div class="span-horizontal">
                                        <textarea class="textarea-only no-resize" wicket:id="atendimentoExame.observacao" />
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>
                </fieldset>
            </div>
        </div>
        <div class="field">
            <div class="span-horizontal">
                <fieldset>
                    <h2><label><wicket:message key="anexo" /></label></h2>
                    <div class="field">
                        <div class="span-horizontal">
                            <input type="file" wicket:id="upload" accept="application/pdf"><input type="submit" wicket:id="btnAnexar" class="btn-light-blue" wicket:message="value:anexar" ></input>
                        </div>
                        <div class="span-horizontal">
                            <label wicket:id="mensagemAnexoDTO.nomeArquivoOriginal" style="width: auto" ></label>
                            <a href="#" wicket:id="btnRemoverAnexo" class="icon trash" tabindex="-1" wicket:message="title:removerAnexo"/>
                        </div>
                    </div>
                </fieldset>
            </div>
        </div>
        <div class="field">
            <div class="span-horizontal">
                <fieldset>
                    <h2><label><wicket:message key="examesSolicitados"/></label></h2>
                    <div class="field">
                        <div class="span-horizontal">
                            <div wicket:id="tblExamesSolicitados" />
                        </div>
                    </div>
                </fieldset>
            </div>
        </div>
        <form wicket:id="formExameIndividual">
            <div class="field">
                <div class="span-horizontal">
                    <fieldset>
                        <h2><a class="expand" wicket:message="data-expandir:+,data-recolher:-"/> <label><wicket:message key="exameIndividual"/></label></h2>
                        <div class="field expand"><label><wicket:message key="exame"/></label><div class="group" wicket:id="exameProcedimento" /></div>
                        <div class="field expand">
                            <div class="span-horizontal">
                                <input type="button" class="arrow-bottom" wicket:message="value:adicionar" wicket:id="btnAdicionar" />
                            </div>
                        </div>

                    </fieldset>
                </div>
            </div>
        </form>
        <div class="field">
            <div class="span-horizontal">
                <div wicket:id="tblExamesIndividuais"/>
            </div>
        </div>
        <div class="field">
            <div class="span-horizontal">
                <input type="button" class="button print" wicket:message="value:imprimirComprovante" wicket:id="btnImprimir" />
                <input type="button" class="button print" wicket:message="value:imprimirLaudo" wicket:id="btnImprimirLaudo" />
            </div>
        </div>
    </form>
</wicket:extend>