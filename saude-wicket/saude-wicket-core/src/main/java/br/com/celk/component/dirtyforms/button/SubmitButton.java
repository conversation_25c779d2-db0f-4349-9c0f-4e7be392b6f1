package br.com.celk.component.dirtyforms.button;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxCallListener;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 */
public class SubmitButton extends AbstractAjaxButton{ 

    private ISubmitAction submitAction;
    
    public SubmitButton(String id, ISubmitAction submitAction) {
        super(id);
        this.submitAction = submitAction;
    }

    @Override
    public final void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
        submitAction.onSubmit(target, form);
    }

    @Override
    protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
        super.updateAjaxAttributes(attributes);
        attributes.getAjaxCallListeners().add(new AjaxCallListener(){

            @Override
            public CharSequence getBeforeHandler(Component component) {
                return " $('form.dirty').dirtyForms('setClean');";
            }
        });
    }

}
