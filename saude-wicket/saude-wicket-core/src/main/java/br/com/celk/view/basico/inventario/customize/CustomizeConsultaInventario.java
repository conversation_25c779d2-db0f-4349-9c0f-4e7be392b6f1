package br.com.celk.view.basico.inventario.customize;

import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.Inventario;
import br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura;

import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaInventario extends CustomizeConsultaAdapter{

    public CustomizeConsultaInventario() {
        super();
    }

    @Override
    public void consultaCustomizeFilterProperties(Map<String, QueryParameter> filterProperties) {
        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(Inventario.PROP_DESCRICAO_INVENTARIO, QueryParameter.CONSULTA_LIKED));
    }

    @Override
    public void consultaCustomizeViewProperties(Map<String, String> properties) {
        properties.put(BundleManager.getString("codigo"), VOUtils.montarPath(Inventario.PROP_CODIGO));
        properties.put(BundleManager.getString("dataInventario"), VOUtils.montarPath(Inventario.PROP_DATA_INVENTARIO));
        properties.put(BundleManager.getString("descricao"), VOUtils.montarPath(Inventario.PROP_DESCRICAO_INVENTARIO));
        properties.put(BundleManager.getString("estabelecimento"), VOUtils.montarPath(Inventario.PROP_EMPRESA, Empresa.PROP_DESCRICAO));
        properties.put(BundleManager.getString("localizacaoEstrutura"), VOUtils.montarPath(Inventario.PROP_LOCALIZACAO_ESTRUTURA, LocalizacaoEstrutura.PROP_DESCRICAO_ESTRUTURA));
    }

    @Override
    public Class getClassConsulta() {
        return Inventario.class;
    }

    @Override
    public String[] getProperties() {
        return VOUtils.mergeProperties(new HQLProperties(Inventario.class).getProperties(),
                new HQLProperties(LocalizacaoEstrutura.class, Inventario.PROP_LOCALIZACAO_ESTRUTURA).getProperties());
    }

}
