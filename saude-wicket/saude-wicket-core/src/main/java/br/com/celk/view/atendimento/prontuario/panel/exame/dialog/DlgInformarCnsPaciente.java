package br.com.celk.view.atendimento.prontuario.panel.exame.dialog;

import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ConsultaUsuarioCadsusDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgInformarCnsPaciente extends Window{

    private PnlInformarCnsPaciente pnlInformarCnsPaciente;

    public DlgInformarCnsPaciente(String id){
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("confirmacaoCnsPaciente");
            }
        });
                
        setInitialWidth(500);
        setInitialHeight(100);
        setResizable(true);
        
        setContent(pnlInformarCnsPaciente = new PnlInformarCnsPaciente(getContentId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus, String numeroCartao) throws ValidacaoException, DAOException {
                close(target);
                DlgInformarCnsPaciente.this.onConfirmar(target, usuarioCadsus, numeroCartao);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus, String numeroCartao) throws ValidacaoException, DAOException;
    
    public void show(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus){
        show(target);
        pnlInformarCnsPaciente.setDTO(target,usuarioCadsus);
    }    
}