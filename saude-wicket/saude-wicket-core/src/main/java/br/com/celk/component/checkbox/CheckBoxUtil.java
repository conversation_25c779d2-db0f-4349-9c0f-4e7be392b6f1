package br.com.celk.component.checkbox;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.util.Valor;
import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CheckBoxUtil {
    
    public static Long getSomatorio(Collection<CheckBoxLongValue> buttons){
        Long soma = 0L;

        for (CheckBoxLongValue check : buttons) {
            soma += Coalesce.asLong(check.getComponentValue());
        }

        return soma;
    }

    public static void selecionarSomatorio(Collection<CheckBoxLongValue> buttons, Long soma){
        List<Long> valores = Valor.resolveSomatorio(soma);

        for (CheckBoxLongValue check : buttons) {
            if(valores.contains(check.getLongValue())){
                check.setModelObject(check.getLongValue());
            }
        }
    }
    
}
