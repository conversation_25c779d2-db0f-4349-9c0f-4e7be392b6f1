package br.com.celk.system.authorization.interfaces;

import org.apache.wicket.Component;
import org.apache.wicket.authorization.Action;

/**
 *
 * <AUTHOR>
 */
public class DefaultPermissionValidator implements IPermissionValidator{

    @Override
    public boolean validarSemPermissao(Component component, Action action) {
        return false;
    }

    @Override
    public boolean validarComPermissao(Component component, Action action) {
        return true;
    }

}
