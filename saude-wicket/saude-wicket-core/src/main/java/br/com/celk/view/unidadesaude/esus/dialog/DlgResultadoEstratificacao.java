package br.com.celk.view.unidadesaude.esus.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public class DlgResultadoEstratificacao extends Window {

    private PnlResultadoEstratificacao pnlResultadoEstratificacao;

    public DlgResultadoEstratificacao(String id) {
        super(id);
        init();
    }

    private void init() {
        setContent(getPnlComponentesDomicilio());

        setTitle(BundleManager.getString("estratificacaoRiscoFamiliar"));

        setInitialWidth(810);
        setInitialHeight(600);
        setResizable(false);
    }

    private Component getPnlComponentesDomicilio() {
        if (this.pnlResultadoEstratificacao == null) {
            this.pnlResultadoEstratificacao = new PnlResultadoEstratificacao(getContentId()) {
                @Override
                public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                    close(target);
                }
            };
        }

        return this.pnlResultadoEstratificacao;
    }

    public void setModelObject(EnderecoDomicilio enderecoDomicilio) {
        pnlResultadoEstratificacao.setModelObject(enderecoDomicilio);
    }
}
