package br.com.celk.component.table.column;

import br.com.celk.component.action.*;
import br.com.celk.component.action.link.*;
import br.com.celk.component.table.selection.DTOSelection;
import org.apache.wicket.Component;
import org.apache.wicket.extensions.markup.html.repeater.data.table.AbstractColumn;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;

import java.io.Serializable;

public abstract class MultipleActionCustomColumn<T> extends AbstractColumn{

    private CustomActionPanel customActionPanel;
    
    public MultipleActionCustomColumn() {
        this("");
    }
    
    public MultipleActionCustomColumn(String header) {
        super(new Model<String>(header), null);
    }

    @Override
    public void populateItem(final Item item, final String componentId, IModel rowModel) {
        T object;
        if (rowModel.getObject() instanceof DTOSelection) {
            object = (T) ((DTOSelection)rowModel.getObject()).getBean();
        } else {
            object = (T) rowModel.getObject();
        }
        item.add(getComponent(componentId));
        customizeColumn(object);
    }

    protected <K extends IActionPanel> K addAction(Class<K> clazz, IAction action){
        return customActionPanel.addAction(clazz, action);
    }
    
    protected ActionLinkPanel addAction(ActionType actionType, IAction action){
        return customActionPanel.addAction(actionType, action);
    }
    
    protected <E extends Serializable> AjaxReportActionLinkPanel addAction(ActionType actionType, E object, IAjaxReportAction<E> action){
        return customActionPanel.addAction(actionType, object, action);
    }
    
    public <E extends Serializable> ModelActionLinkPanel addAction(ActionType actionType, E object, IModelAction<E> action){
        return customActionPanel.addAction(actionType, object, action);
    }
    
    protected <E extends Serializable> ReportActionLinkPanel addAction(ActionType actionType, E object, IReportAction<E> action){
        return customActionPanel.addAction(actionType, object, action);
    }

    protected <E extends Serializable> HtmlMultiReportActionLinkPanel addAction(ActionType actionType, E object, IHtmlMultiReportAction<E> action){
        return customActionPanel.addAction(actionType, object, action);
    }

    protected <E extends Serializable> HtmlReportActionLinkPanel addAction(ActionType actionType, E object, IHtmlReportAction<E> action){
        return customActionPanel.addAction(actionType, object, action);
    }

    protected <E extends Serializable> RtfReportActionLinkPanel addAction(ActionType actionType, E object, IRtfReportAction<E> action){
        return customActionPanel.addAction(actionType, object, action);
    }

    protected <E extends Serializable> FileActionLinkPanel addAction(ActionType actionType, E object, IFileAction<E> action){
        return customActionPanel.addAction(actionType, object, action);
    }
    
    private Component getComponent(String componentId){
        customActionPanel = new CustomActionPanel(componentId);
        customActionPanel.setOutputMarkupPlaceholderTag(true);
        return customActionPanel;
    }

    public abstract void customizeColumn(final T rowObject);

}
