package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.atendimento.condicaopaciente.ClassificacaoAtendimentoPorCondicaoSaude;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.checkbox.CheckBoxUtil;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.temp.v2.TempHelperV2;
import br.com.celk.component.temp.v2.behavior.TempBehaviorV2;
import br.com.celk.component.temp.v2.behavior.TempFormBehaviorV2;
import br.com.celk.component.temp.v2.store.interfaces.impl.DefaultTempStoreStrategyV2;
import br.com.celk.component.temp.v3.TempHelperV3;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.unidadesaude.CiapHelper;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.prontuario.panel.dialog.DlgInformarCiap;
import br.com.celk.view.atendimento.prontuario.panel.dialog.DlgInformarCipe;
import br.com.celk.view.atendimento.prontuario.panel.dialog.DlgInformarClassificacaoAtendimento;
import br.com.celk.view.atendimento.prontuario.panel.dialog.DlgInformarConduta;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.prontuario.basico.ciap.autocomplete.AutoCompleteConsultaCiap;
import br.com.celk.view.prontuario.basico.diagnosticoenfermagem.autocomplete.AutoCompleteConsultaDiagnosticoEnfermagemSae;
import br.com.celk.view.prontuario.procedimento.autocomplete.AutoCompleteConsultaProcedimento;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.QueryConsultaProfissionalCargaHorariaDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.ProfissionalFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.AtendimentoHelper;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.LoadInterceptorClassificacaoCbo;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.DadosEnfermagemDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.covid.CondutaCovid19;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendIndividualItem;
import br.com.ksisolucoes.vo.controle.UsuarioEmpresa;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import ch.lambdaj.Lambda;
import org.apache.wicket.ajax.AjaxEventBehavior;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.request.resource.CssResourceReference;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class DadosEnfermagemPanel extends ProntuarioCadastroPanel {

    private InputField txtDiasRetorno;
    private InputArea txaObservacaoRetorno;
    private DropDown<ClassificacaoAtendimento> cbxTipoAtendimento;
    private Form<DadosEnfermagemDTO> form;
    private WebMarkupContainer containerRetorno;
    private WebMarkupContainer containerNasf;
    private DropDown<Conduta> cbxConduta;
    private DropDown<Long> cbxCondutaCovid;
    private AutoCompleteConsultaCiap autoCompleteConsultaCiap;
    private WebMarkupContainer containerCiap;
    private CompoundPropertyModel<CondutaAtendimento> modelCondutaAtendimento;
    private DlgInformarConduta dlgInformarConduta;
    private DlgInformarClassificacaoAtendimento dlgInformarClassificacaoAtendimento;
    private DlgInformarCiap dlgInformarCiap;

    private DlgInformarCipe dlgInformarCipe;
    private CheckBoxLongValue checkAtendimentoCompartilhado;
    private boolean atendimentoCompartilhado;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private WebMarkupContainer containerProfissionalAuxiliar;
    private DropDown dropDownTipoAtendimento;
    private DateChooser dchDumGestante;
    private LongField txtIdadeGestacional;
    private LongField txtNumeroGestasPrevias;
    private LongField txtNumeroPartos;
    private String sexo;
    private DropDown<TabelaCbo> cbxCboProfissionalAuxiliar;

    private CheckBoxLongValue checkBoxNasfAvaliacaoDiagnostico;
    private CheckBoxLongValue checkBoxNasfProcedimentosClinicosTerapeuticos;
    private CheckBoxLongValue checkBoxNasfPrescricaoTerapeutica;

    private DropDown dropDownRacionalidadeSaude;
    private DropDown dropDownAtencaoDomiliciar;
    private DropDown dropDownGravidezPlanejada;
    DropDown<Long> dropDownVacina;
    private DropDown dropDownConsulta;
    private AutoCompleteConsultaProcedimento autoCompleteConsultaProcedimento;

    private final boolean enable;

    private List<CheckBoxLongValue> lstCheckBoxNasf = new ArrayList<CheckBoxLongValue>();

    private String CSS_FILE = "DadosEnfermagemPanel.css";

    private AutoCompleteConsultaDiagnosticoEnfermagemSae autoCompleteConsultaDiagnosticoEnfermagemSae;
    private AbstractAjaxLink btnAdicionarCipe;
    private WebMarkupContainer containerCipe;

    public DadosEnfermagemPanel(String id, String titulo) {
        super(id, titulo);
        enable = true;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        DadosEnfermagemDTO on = on(DadosEnfermagemDTO.class);

        form = new Form("form", new CompoundPropertyModel<>(new DadosEnfermagemDTO()));

        form.add(dropDownConsulta = DropDownUtil.getSimNaoLongDropDown(path(on.getFlagConsulta()), false, false));
        dropDownConsulta.addAjaxUpdateValue();
        dropDownConsulta.add(new Tooltip().setText("msgToolTipoConsultaAtendimento"));

        containerCiap = new WebMarkupContainer("containerCiap");
        containerCiap.add(autoCompleteConsultaCiap = new AutoCompleteConsultaCiap(path(on.getCiap())));
        autoCompleteConsultaCiap.addAjaxUpdateValue();
        autoCompleteConsultaCiap.setOutputMarkupId(true);
        autoCompleteConsultaCiap.add(new TempBehaviorV2());
        autoCompleteConsultaCiap.add(new AjaxEventBehavior("onchange") {
            @Override
            protected void onEvent(AjaxRequestTarget target) {
                Ciap ciap = (Ciap) autoCompleteConsultaCiap.getModelObject();
                validarCiapSexoPaciente(target, ciap);
            }
        });
        containerCiap.add(new AbstractAjaxLink("btnAdicionarCiapAtendimento") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                viewDlgInformarCiap(target);
            }
        });

        form.add(containerCiap);

        validarVisibilidadeCipe();

        containerCipe = new WebMarkupContainer("containerCipe");
        containerCipe.add(autoCompleteConsultaDiagnosticoEnfermagemSae = new AutoCompleteConsultaDiagnosticoEnfermagemSae(path(on.getCipe())));
        autoCompleteConsultaDiagnosticoEnfermagemSae.addAjaxUpdateValue();
        autoCompleteConsultaDiagnosticoEnfermagemSae.setOutputMarkupId(true);
        autoCompleteConsultaDiagnosticoEnfermagemSae.add(new TempBehaviorV2());
        containerCipe.add(btnAdicionarCipe = new AbstractAjaxLink("btnAdicionarCipe") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                viewDlgInformarCipe(target);
            }
        });

        containerCipe.setEnabled(form.getModel().getObject().isVisibleCipe());
        containerCipe.setVisible(form.getModel().getObject().isVisibleCipe());
        form.add(containerCipe);

        form.add(cbxCondutaCovid = getCbxCondutaCovid(path(on.getCondutaCovid())));
        cbxCondutaCovid.add(new TempBehaviorV2());

        form.add(cbxConduta = getCbxConduta(path(on.getConduta())));
        cbxConduta.addRequiredClass();
        cbxConduta.add(new TempBehaviorV2());
        form.add(new AbstractAjaxLink("btnAdicionarConduta") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                viewDlgInformarConduta(target);
            }
        });

        cbxTipoAtendimento = new DropDown(path(on.getClassificacaoAtendimento()));
        form.add(dropDownTipoAtendimento = DropDownUtil.getIEnumDropDown(path(on.getTipoAtendimentoEsus()), EsusFichaAtendIndividualItem.TipoAtendimento.values(), true, false, true));
        dropDownTipoAtendimento.addRequiredClass();
        dropDownTipoAtendimento.add(new TempBehaviorV2());
        form.add(dropDownAtencaoDomiliciar = DropDownUtil.getIEnumDropDown(path(on.getAtencaoDomiciliar()), EsusFichaAtendIndividualItem.ModalidadeAD.values(), true, false, true));
        dropDownAtencaoDomiliciar.add(new TempBehaviorV2());
        form.add(dropDownVacina = DropDownUtil.getNaoSimLongDropDown(path(on.getVacinaEmDia()), false, false));
        dropDownVacina.addAjaxUpdateValue();
        dropDownVacina.add(new TempBehaviorV2());
        form.add(containerRetorno = new WebMarkupContainer("containerRetorno"));
        containerRetorno.setOutputMarkupId(true);

        containerRetorno.add(txtDiasRetorno = new InputField(path(on.getDiasRetorno())));
        containerRetorno.add(txaObservacaoRetorno = new InputArea(path(on.getObservacaoRetorno())));
        carregaCiapAtendimentoPrimario();

        add(form);

        txtDiasRetorno.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                avaliarDiasRetorno(target);
            }
        });

        txtDiasRetorno.add(new TempBehaviorV2());
        txaObservacaoRetorno.add(new TempBehaviorV2());

        form.add(new TempFormBehaviorV2(new DefaultTempStoreStrategyV2(getAtendimento(), getIdentificador().toString(), DadosEnfermagemDTO.class)));

        form.add(dropDownRacionalidadeSaude = (DropDown) DropDownUtil.getIEnumDropDown(path(on.getRacionalidadeSaude()), EsusFichaAtendIndividualItem.RacionalidadeSaude.values(), true, false, true)
                .setLabel(new Model(bundle("racionalidadeSaude"))).add(new TempBehaviorV2()));

        DropDown dropDownLocalAtendimento;
        form.add(dropDownLocalAtendimento = (DropDown) DropDownUtil.getIEnumDropDown(path(on.getLocalAtendimento()), Empresa.LocalAtendimento.values(), false, false, false, true).add(new TempBehaviorV2()));
        dropDownLocalAtendimento.removeChoice(Empresa.LocalAtendimento.HOSPITAL.value());
        dropDownLocalAtendimento.removeChoice(Empresa.LocalAtendimento.UNIDADE_PRONTO_ATENDIMENTO.value());
        dropDownLocalAtendimento.removeChoice(Empresa.LocalAtendimento.CACON_UNACON.value());
        dropDownLocalAtendimento.removeChoice(Empresa.LocalAtendimento.HOSPITAL_SOS_URGENCIA_EMERGENCIA.value());
        dropDownLocalAtendimento.removeChoice(Empresa.LocalAtendimento.HOSPITAL_SOS_DEMAIS_SETORES.value());

        initContainerProfisAuxiliar(on);

        containerNasf = new WebMarkupContainer("containerNasf");
        containerNasf.setOutputMarkupId(true);

        containerNasf.add(checkBoxNasfAvaliacaoDiagnostico = new CheckBoxLongValue("nasfAvaliacaoDiagnostico", EsusFichaAtendIndividualItem.Nasf.AVALIACAO_DIAGNOSTICO.sum(), new Model<Long>()));
        containerNasf.add(checkBoxNasfProcedimentosClinicosTerapeuticos = new CheckBoxLongValue("nasfProcedimentosClinicosTerapeuticos", EsusFichaAtendIndividualItem.Nasf.PROCEDIMENTOS_CLINICOS_TERAPEUTICOS.sum(), new Model<Long>()));
        containerNasf.add(checkBoxNasfPrescricaoTerapeutica = new CheckBoxLongValue("nasfPrescricaoTerapeutica", EsusFichaAtendIndividualItem.Nasf.PRESCRICAO_TERAPEUTICA.sum(), new Model<Long>()));

        checkBoxNasfAvaliacaoDiagnostico.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                atualizarNasf(target);
            }
        });

        checkBoxNasfProcedimentosClinicosTerapeuticos.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                atualizarNasf(target);
            }
        });

        checkBoxNasfPrescricaoTerapeutica.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                atualizarNasf(target);
            }
        });

        lstCheckBoxNasf.add(checkBoxNasfAvaliacaoDiagnostico);
        lstCheckBoxNasf.add(checkBoxNasfProcedimentosClinicosTerapeuticos);
        lstCheckBoxNasf.add(checkBoxNasfPrescricaoTerapeutica);

        form.add(containerNasf);

        form.add(dchDumGestante = (DateChooser) new DateChooser(path(on.getDumGestante())).setEnabled(enable));
        dchDumGestante.addAjaxUpdateValue();
        dchDumGestante.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                setIdadeGestacional(target);
            }
        });
        dchDumGestante.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        dchDumGestante.add(new TempBehaviorV2());
        form.add(dropDownGravidezPlanejada = (DropDown) DropDownUtil.getSimNaoLongDropDown(path(on.getGravidezPlanejada()), true, false).setEnabled(enable));
        dropDownGravidezPlanejada.add(new TempBehaviorV2());
        form.add(txtIdadeGestacional = new LongField(path(on.getIdadeGestacional())));
        txtIdadeGestacional.add(new TempBehaviorV2());
        txtIdadeGestacional.setEnabled(false);
        txtIdadeGestacional.setConvertZeroToNull(true).setVMin(1l).setVMax(42l);

        form.add(txtNumeroGestasPrevias = new LongField(path(on.getNumeroGestasPrevias())));
        txtNumeroGestasPrevias.add(new TempBehaviorV2());
        txtNumeroGestasPrevias.setEnabled(enable);
        txtNumeroGestasPrevias.setVMax(99l);
        form.add(txtNumeroPartos = new LongField(path(on.getNumeroPartos())));
        txtNumeroPartos.add(new TempBehaviorV2());
        txtNumeroPartos.setEnabled(enable);
        txtNumeroPartos.setVMax(99l);

        if (form.getModel().getObject().isCarregarDadosPreNatal() || RepositoryComponentDefault.SIM_LONG.equals(getAtendimento().getFlagGestante())) {
            UsuarioCadsusDado usuarioCadsusDado = LoadManager.getInstance(UsuarioCadsusDado.class)
                    .addProperty(UsuarioCadsusDado.PROP_IDADE_GESTACIONAL)
                    .addProperty(UsuarioCadsusDado.PROP_GESTANTE)
                    .setId(getAtendimento().getUsuarioCadsus().getCodigo())
                    .start().getVO();

            if (usuarioCadsusDado == null) {
                try {
                    usuarioCadsusDado = new UsuarioCadsusDado();
                    usuarioCadsusDado.setCodigo(getAtendimento().getUsuarioCadsus().getCodigo());
                    usuarioCadsusDado.setGestante(getAtendimento().getFlagGestante());
                    usuarioCadsusDado.setDataDados(DataUtil.getDataAtual());
                    BOFactoryWicket.save(usuarioCadsusDado);
                } catch (DAOException | ValidacaoException e) {
                    br.com.ksisolucoes.util.log.Loggable.log.error(e);
                }
            }

            if (getAtendimento().getUsuarioCadsus().getSexo() != null) {
                sexo = getAtendimento().getUsuarioCadsus().getSexo();
            }

            if (UsuarioCadsus.SEXO_MASCULINO.equals(sexo)) {
                dchDumGestante.setEnabled(false);
                dropDownGravidezPlanejada.setEnabled(false);
                txtNumeroGestasPrevias.setEnabled(false);
                txtNumeroPartos.setEnabled(false);
            } else {
                dchDumGestante.setEnabled(true);
                dropDownGravidezPlanejada.setEnabled(true);
                txtNumeroGestasPrevias.setEnabled(true);
                txtNumeroPartos.setEnabled(true);
                setIdadeGestacional(null);
            }
            if (form.getModel().getObject().getDumGestante() == null) {
                dchDumGestante.setComponentValue(AtendimentoHelper.getUltimoAtendimentoGestante(getAtendimento()));
            }


            Long tipoAtendimentoEsus = getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getTipoAtendimentoEsus();
            if (tipoAtendimentoEsus != null) {
                if(TipoAtendimento.TipoAtendimentoEsus.ESCUTA_INICIAL_ORIENTACAO.value().equals(tipoAtendimentoEsus)){
                    form.getModel().getObject().setTipoAtendimentoEsus((Long) EsusFichaAtendIndividualItem.TipoAtendimento.ESCUTA_INICIAL_ORIENTACAO.value());
                } else if(TipoAtendimento.TipoAtendimentoEsus.CONSULTA_DIA.value().equals(tipoAtendimentoEsus)){
                    form.getModel().getObject().setTipoAtendimentoEsus((Long) EsusFichaAtendIndividualItem.TipoAtendimento.CONSULTA_NO_DIA.value());
                } else if(TipoAtendimento.TipoAtendimentoEsus.ATENDIMENTO_URGENCIA.value().equals(tipoAtendimentoEsus)){
                    form.getModel().getObject().setTipoAtendimentoEsus((Long) EsusFichaAtendIndividualItem.TipoAtendimento.ATENDIMENTO_URGENCIA.value());
                } else if(TipoAtendimento.TipoAtendimentoEsus.CONSULTA_AGENDADA.value().equals(tipoAtendimentoEsus)){
                    form.getModel().getObject().setTipoAtendimentoEsus((Long) EsusFichaAtendIndividualItem.TipoAtendimento.CONSULTA_AGENDADA.value());
                } else if(TipoAtendimento.TipoAtendimentoEsus.CONSULTA_AGENDADA_PROGRAMADA_CUIDADO_CONTINUADO.value().equals(tipoAtendimentoEsus)){
                    form.getModel().getObject().setTipoAtendimentoEsus((Long) EsusFichaAtendIndividualItem.TipoAtendimento.CONSULTA_AGENDADA_PROGRAMADA_CUIDADO_CONTINUADO.value());
                }
            }

            form.getModel().getObject().setCarregarDadosPreNatal(false);
            new TempHelperV3().save(form);
        }

        new TempHelperV2().save(form);

        txaObservacaoRetorno.setEnabled(false);
        avaliarDiasRetorno(null);

        CheckBoxUtil.selecionarSomatorio(lstCheckBoxNasf, Coalesce.asLong(form.getModel().getObject().getNasfs()));
        EquipeProfissional equipeProfissional = null;
        if (getAtendimento().getProfissional() != null) {
            UsuarioEmpresa usuarioEmpresa = LoadManager.getInstance(UsuarioEmpresa.class)
                    .addProperties(new HQLProperties(UsuarioEmpresa.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioEmpresa.PROP_EMPRESA), getAtendimento().getEmpresa()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioEmpresa.PROP_USUARIO), getAtendimento().getUsuarioAtendendo()))
                    .setMaxResults(1).start().getVO();
            if (usuarioEmpresa != null && usuarioEmpresa.getEmpresaBpa() != null) {
                equipeProfissional = LoadManager.getInstance(EquipeProfissional.class)
                        .addProperties(new HQLProperties(EquipeProfissional.class).getProperties())
                        .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_ATENDIMENTO_NASFS))
                        .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_STATUS, EquipeProfissional.STATUS_ATIVO))
                        .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_PROFISSIONAL, getAtendimento().getProfissional()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA), usuarioEmpresa.getEmpresaBpa()))
                        .setMaxResults(1).start().getVO();
            }
        }
        if (equipeProfissional == null || RepositoryComponentDefault.NAO_LONG.equals(equipeProfissional.getEquipe().getAtendimentoNasfs())) {
            equipeProfissional = LoadManager.getInstance(EquipeProfissional.class)
                    .addProperty(EquipeProfissional.PROP_CODIGO)
                    .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_ATENDIMENTO_NASFS))
                    .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_STATUS, EquipeProfissional.STATUS_ATIVO))
                    .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_PROFISSIONAL, getAtendimento().getProfissional()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA), getAtendimento().getEmpresa()))
                    .setMaxResults(1).start().getVO();
        }

        if (equipeProfissional == null || (equipeProfissional != null && equipeProfissional.getEquipe() != null
                && RepositoryComponentDefault.NAO_LONG.equals(Coalesce.asLong(equipeProfissional.getEquipe().getAtendimentoNasfs())))) {
            containerNasf.setVisible(false);
            form.getModel().getObject().setNasfs(0L);
            new TempHelperV2().save(form);
        }

        if (form.getModel().getObject().isCarregarCondutaLocalAtendimento()) {
            if (getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getConduta() != null) {
                form.getModel().getObject().setConduta(getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getConduta());
            }
            if (getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getLocalAtendimento() != null) {
                form.getModel().getObject().setLocalAtendimento(getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getLocalAtendimento());
            }

            form.getModel().getObject().setCarregarCondutaLocalAtendimento(false);
            new TempHelperV2().save(form);
        }
        if (form.getModel().getObject().getAtendimentoCompartilhado() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(form.getModel().getObject().getAtendimentoCompartilhado())) {
                containerProfissionalAuxiliar.setVisible(true);
            } else {
                containerProfissionalAuxiliar.setVisible(false);
            }
        }
        if (getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getFlagConsulta() != null) {
            dropDownConsulta.setComponentValue(getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getFlagConsulta());
        }
        configurarFlagConsulta();
        dropDownConsulta.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                configurarFlagConsulta();
                target.add(cbxConduta);
                target.add(cbxTipoAtendimento);
                target.add(dropDownTipoAtendimento);
            }
        });

        form.add(getCbxTipoAtendimento());
        cbxTipoAtendimento.addRequiredClass();
        cbxTipoAtendimento.add(new TempBehaviorV2());
        cbxTipoAtendimento.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                dlgInformarClassificacaoAtendimento = null;
                target.add(cbxTipoAtendimento);
            }
        });

        form.add(new AbstractAjaxLink("btnAdicionarClassificacaoAtendimento") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                viewDlgInformarClassificacaoAtendimento(target);
            }
        });
        if (RepositoryComponentDefault.SEXO_FEMININO.equals(getAtendimento().getUsuarioCadsus().getSexo())) {
            PreNatal preNatal = UsuarioCadsusHelper.getPreNatal(getAtendimento().getUsuarioCadsus());

            if (preNatal != null) {
                if (preNatal.getDataUltimaMenstruacao() != null) {
                    dchDumGestante.setComponentValue(preNatal.getDataUltimaMenstruacao());
                    dchDumGestante.setEnabled(false);
                    txtIdadeGestacional.setComponentValue(Data.calcularIdadeGestacional(preNatal.getDataUltimaMenstruacao(), getAtendimento().getDataChegada()));
                }
                if (preNatal.getGravidezPlanejada() != null) {
                    dropDownGravidezPlanejada.setComponentValue(preNatal.getGravidezPlanejada());
                }
                if (preNatal.getPartos() != null) {
                    txtNumeroPartos.setComponentValue(preNatal.getPartos());
                }
                if (preNatal.getPartos() != null) {
                    txtNumeroGestasPrevias.setComponentValue(preNatal.getGestacoes());
                }
            } else {
                dchDumGestante.setComponentValue(null);
                txtIdadeGestacional.setComponentValue(null);
                setIdadeGestacional(null);
            }
        }
    }

    private void validarVisibilidadeCipe() {
        List<TabelaCbo> tabelaCbos = new ArrayList<>();
        try {
            tabelaCbos = encontrarCboProfissional(getAtendimento().getEmpresa(), getAtendimento().getProfissional());
        } catch (ValidacaoException e) {
            Logger.getLogger(EvolucaoProntuarioPanel.class.getName()).log(Level.SEVERE, null, e);
        } catch (DAOException e) {
            Logger.getLogger(EvolucaoProntuarioPanel.class.getName()).log(Level.SEVERE, null, e);
        }

        boolean isEnfermeiro = false;
        if (br.com.celk.util.CollectionUtils.isNotNullEmpty(tabelaCbos)) {
            for (TabelaCbo tabelaCbo : tabelaCbos) {
                isEnfermeiro = tabelaCbo.getCbo().startsWith(TabelaCbo.INICIO_CBO_ENFERMEIRO);
            }
        }

        if (isEnfermeiro) {
            form.getModel().getObject().setVisibleCipe(true);
        } else {
            form.getModel().getObject().setVisibleCipe(false);
        }

        new TempHelperV2().save(form);
    }

    private void viewDlgInformarCipe(AjaxRequestTarget target) {
        if (dlgInformarCipe == null) {
            getProntuarioController().addWindow(target, dlgInformarCipe = new DlgInformarCipe(getProntuarioController().newWindowId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, List<AtendimentoCipe> cipeList) throws ValidacaoException, DAOException {
                    adicionarCipe(cipeList);
                }
            });
        }
        dlgInformarCipe.show(target, form.getModel().getObject().getCipeList());
    }

    private void adicionarCipe(List<AtendimentoCipe> cipeList) {
        form.getModel().getObject().setCipeList(cipeList);
        new TempHelperV2().save(form);
    }

    private List<TabelaCbo> encontrarCboProfissional(Empresa empresa, Profissional profissional) throws ValidacaoException, DAOException {
        QueryConsultaProfissionalCargaHorariaDTOParam param = new QueryConsultaProfissionalCargaHorariaDTOParam();
        param.setEmpresa(empresa);
        param.setProfissional(profissional);

        List<TabelaCbo> cbos = BOFactoryWicket.getBO(ProfissionalFacade.class).consultaCbosProfissional(param);
        if (br.com.celk.util.CollectionUtils.isNotNullEmpty(cbos)) {
            return cbos;
        } else {
            return null;
        }
    }

    private void configurarFlagConsulta() {
        if (RepositoryComponentDefault.NAO_LONG.equals(dropDownConsulta.getComponentValue())) {
            cbxConduta.removeRequiredClass();
            cbxTipoAtendimento.removeRequiredClass();
            dropDownTipoAtendimento.removeRequiredClass();
        } else {
            cbxConduta.addRequiredClass();
            cbxTipoAtendimento.addRequiredClass();
            dropDownTipoAtendimento.addRequiredClass();
        }
        new TempHelperV2().save(form);
    }

    private void initContainerProfisAuxiliar(DadosEnfermagemDTO proxy) {
        form.add(checkAtendimentoCompartilhado = new CheckBoxLongValue(path(proxy.getAtendimentoCompartilhado())));
        checkAtendimentoCompartilhado.setOutputMarkupId(true);
        checkAtendimentoCompartilhado.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (RepositoryComponentDefault.SIM_LONG.equals(form.getModel().getObject().getAtendimentoCompartilhado())) {
                    containerProfissionalAuxiliar.setVisible(true);
                } else {
                    containerProfissionalAuxiliar.setVisible(false);
                    autoCompleteConsultaProfissional.limpar(target);
                    cbxCboProfissionalAuxiliar.removeAllChoices();
                    cbxCboProfissionalAuxiliar.limpar(target);

                    new TempHelperV2().save(autoCompleteConsultaProfissional);
                    new TempHelperV2().save(cbxCboProfissionalAuxiliar);
                }
                target.add(containerProfissionalAuxiliar);
                new TempHelperV2().save(checkAtendimentoCompartilhado);
            }
        });

        form.add(containerProfissionalAuxiliar = new WebMarkupContainer("containerProfissionalAuxiliar"));
        containerProfissionalAuxiliar.setOutputMarkupPlaceholderTag(true);
        containerProfissionalAuxiliar.setVisible(false);

        containerProfissionalAuxiliar.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional(path(proxy.getProfissionalAuxiliar())));
        autoCompleteConsultaProfissional.setPossuiCnsSim(true);
//        autoCompleteConsultaProfissional.setPeriodoEmpresa(true);
        autoCompleteConsultaProfissional.add(new TempBehaviorV2());
        autoCompleteConsultaProfissional.add(new ConsultaListener<Profissional>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Profissional profissional) {
                populateCboProfissionalAuxiliar(target);
            }
        });

        autoCompleteConsultaProfissional.add(new RemoveListener<Profissional>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Profissional object) {
                populateCboProfissionalAuxiliar(target);
            }
        });

        containerProfissionalAuxiliar.add(cbxCboProfissionalAuxiliar = new DropDown<>(path(proxy.getCboProfissionalAuxiliar())));
        cbxCboProfissionalAuxiliar.add(new TempBehaviorV2());
        populateCboProfissionalAuxiliar(null);

    }

    private void populateCboProfissionalAuxiliar(AjaxRequestTarget target) {
        cbxCboProfissionalAuxiliar.removeAllChoices();

        if (target != null) {
            cbxCboProfissionalAuxiliar.limpar(target);
        }

        Profissional profissionalAuxiliar = (Profissional) autoCompleteConsultaProfissional.getComponentValue();
        if (profissionalAuxiliar != null) {
            QueryConsultaProfissionalCargaHorariaDTOParam param = new QueryConsultaProfissionalCargaHorariaDTOParam();
            //param.setEmpresa(getAtendimento().getEmpresa());
            param.setProfissional(profissionalAuxiliar);

            try {
                List<TabelaCbo> cbos = BOFactory.getBO(ProfissionalFacade.class).consultaCbosProfissional(param);
                if (br.com.celk.util.CollectionUtils.isNotNullEmpty(cbos)) {
                    if (cbos.size() == 1) {
                        TabelaCbo cbo = cbos.get(0);
                        cbxCboProfissionalAuxiliar.addChoice(cbo, cbo.getDescricao());
                        cbxCboProfissionalAuxiliar.setComponentValue(cbo);
                    } else {
                        cbxCboProfissionalAuxiliar.addChoice(null, "");
                        for (TabelaCbo cbo : cbos) {
                            cbxCboProfissionalAuxiliar.addChoice(cbo, cbo.getDescricao());
                        }
                    }
                }
            } catch (DAOException | ValidacaoException e) {
                Loggable.log.error(e.getMessage(), e);
            }
        }

        new TempHelperV2().save(cbxCboProfissionalAuxiliar);

        if (target != null) {
            target.add(cbxCboProfissionalAuxiliar);
        }
    }

    private void atualizarNasf(AjaxRequestTarget target) {
        form.getModel().getObject().setNasfs(CheckBoxUtil.getSomatorio(lstCheckBoxNasf));
        new TempHelperV2().save(form);
    }

    private void viewDlgInformarCiap(AjaxRequestTarget target) {
        if (dlgInformarCiap == null) {
            getProntuarioController().addWindow(target, dlgInformarCiap = new DlgInformarCiap(getProntuarioController().newWindowId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, List<CiapAtendimento> ciapAtendimentoList) throws ValidacaoException, DAOException {
                    adicionarCiapAtendimento(target, ciapAtendimentoList);
                }
            });
        }
        dlgInformarCiap.show(target, form.getModel().getObject().getCiapAtendimentoList());
    }

    private void adicionarCiapAtendimento(AjaxRequestTarget target,List<CiapAtendimento> ciapAtendimentoList) {
        form.getModel().getObject().setCiapAtendimentList(ciapAtendimentoList);
        adicionaClassificacaoAtendimentoCiap(target);
        new TempHelperV2().save(form);
    }

    private void adicionaClassificacaoAtendimentoCiap(AjaxRequestTarget target) {
        try {
            List<CiapAtendimento> ciapProblemaCondicaoList = form.getModel().getObject().getCiapAtendimentoList();
            List<AtendimentoClassificacaoAtendimento> atendimentoClassificacaoAtendimentoList = form.getModel().getObject().getAtendimentoClassificacaoAtendimentoList();

            if(ciapProblemaCondicaoList != null) {
                for (CiapAtendimento ciapAtendimento : ciapProblemaCondicaoList) {
                    String cdEsusCiap = CiapHelper.getClassificacaoAtendimentoCodigoAB(ciapAtendimento.getCiap().getReferencia());
                    ClassificacaoAtendimento classificacaoAtendimento = (ClassificacaoAtendimento) HibernateSessionFactory.getSession().createCriteria(ClassificacaoAtendimento.class)
                            .add(Restrictions.eq(ClassificacaoAtendimento.PROP_CODIGO_ESUS, cdEsusCiap))
                            .uniqueResult();

                    if (classificacaoAtendimento != null) {
                        if (form.getModel().getObject().getClassificacaoAtendimento() == null) {
                            cbxTipoAtendimento.setComponentValue(classificacaoAtendimento);
                        } else {
                            AtendimentoClassificacaoAtendimento ca = new AtendimentoClassificacaoAtendimento();
                            ca.setClassificacaoAtendimento(classificacaoAtendimento);
                            ca.setAtendimento(getAtendimento());
                            if(!isClassifAdicionada(ca)) {
                                atendimentoClassificacaoAtendimentoList.add(ca);
                                form.getModel().getObject().getAtendimentoClassificacaoAtendimentoList().add(ca);
                            }
                        }
                        target.add(cbxTipoAtendimento);
                    }
                }
            }

            adicionarClassificacaoAtendimento(atendimentoClassificacaoAtendimentoList);
        } catch (DAOException e) {
            e.printStackTrace();
        }
    }

    private boolean isClassifAdicionada(AtendimentoClassificacaoAtendimento atendClassificacao) {
        List<Long> classificacoesAtendList = Lambda.extract(form.getModel().getObject().getAtendimentoClassificacaoAtendimentoList(), on(AtendimentoClassificacaoAtendimento.class).getClassificacaoAtendimento().getCodigo());
        return classificacoesAtendList.contains(atendClassificacao.getClassificacaoAtendimento().getCodigo()) ||
                (getAtendimento().getClassificacaoAtendimento() != null
                        && getAtendimento().getClassificacaoAtendimento().getCodigo().equals(atendClassificacao.getClassificacaoAtendimento().getCodigo()));
    }

    private void viewDlgInformarClassificacaoAtendimento(AjaxRequestTarget target) {
        if (dlgInformarClassificacaoAtendimento == null) {
            getProntuarioController().addWindow(target, dlgInformarClassificacaoAtendimento = new DlgInformarClassificacaoAtendimento(getProntuarioController().newWindowId(), form.getModel().getObject().getClassificacaoAtendimentoRemovidosList(), getAtendimento().getUsuarioCadsus()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, List<AtendimentoClassificacaoAtendimento> atendimentoClassificacaoAtendimentoList) throws ValidacaoException, DAOException {
                    adicionarClassificacaoAtendimento(atendimentoClassificacaoAtendimentoList);
                    new TempHelperV2().save(form);
                }
            });
        }
         dlgInformarClassificacaoAtendimento.show(target, getAtendimento(), form.getModel().getObject().getAtendimentoClassificacaoAtendimentoList());
    }

    private void adicionarClassificacaoAtendimento(List<AtendimentoClassificacaoAtendimento> atendimentoClassificacaoAtendimentoList) {
        form.getModel().getObject().setAtendimentoClassificacaoAtendimentoList(atendimentoClassificacaoAtendimentoList);
        new TempHelperV2().save(form);
    }

    private void viewDlgInformarConduta(AjaxRequestTarget target) {
        if (dlgInformarConduta == null) {
            getProntuarioController().addWindow(target, dlgInformarConduta = new DlgInformarConduta(getProntuarioController().newWindowId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, List<CondutaAtendimento> condutaList) throws ValidacaoException, DAOException {
                    adicionarConduta(condutaList);
                }
            });
        }
        dlgInformarConduta.show(target, form.getModel().getObject().getCondutaAtendimentoList(), false);
    }

    private void adicionarConduta(List<CondutaAtendimento> condutaList) {
        form.getModel().getObject().setCondutaAtendimentoList(condutaList);
        new TempHelperV2().save(form);
    }

    private DropDown getCbxConduta(String id) {
        if (cbxConduta == null) {
            cbxConduta = new DropDown(id);
            updateCbxConduta();
        }
        return cbxConduta;
    }

    private DropDown getCbxCondutaCovid(String id) {
        if (cbxCondutaCovid == null) {
            cbxCondutaCovid = new DropDown(id);
            cbxCondutaCovid.removeAllChoices();
            CondutaCovid19[] condutasCovid19s = CondutaCovid19.values();

            cbxCondutaCovid.addChoice(null, "");
            for (CondutaCovid19 condutaCovid19s : condutasCovid19s) {
                cbxCondutaCovid.addChoice(condutaCovid19s.value(), condutaCovid19s.descricao());
            }
        }
        return cbxCondutaCovid;
    }

    private void updateCbxConduta() {
        cbxConduta.removeAllChoices();

        LoadManager load = LoadManager.getInstance(Conduta.class)
                .addSorter(new QueryCustom.QueryCustomSorter(Conduta.PROP_DESCRICAO))
                .addParameter(new QueryCustom.QueryCustomParameter(Conduta.PROP_TIPO_CONDUTA, Conduta.TipoConduta.CONSULTA_MEDICA.value()));

        List<Conduta> lstConduta = load.start().getList();
        cbxConduta.addChoice(null, "");

        for (Conduta item : lstConduta) {
            cbxConduta.addChoice(item, item.getDescricao());
        }
    }

    private void carregaCiapAtendimentoPrimario() {
        if (getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getClassificacaoAtendimento() != null) {
            ClassificacaoAtendimento classificacaoAtendimento = LoadManager.getInstance(ClassificacaoAtendimento.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(ClassificacaoAtendimento.PROP_CODIGO, getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getClassificacaoAtendimento().getCodigo()))
                    .start().getVO();

            if (ClassificacaoAtendimento.Situacao.ATIVO.value().equals(classificacaoAtendimento.getSituacao())) {
                if (ClassificacaoAtendimento.ClassificacaoEsus.OUTROS.value().equals(classificacaoAtendimento.getClassificacaoEsus())) {
                    Atendimento atendimento = LoadManager.getInstance(Atendimento.class)
                            .addProperties(new HQLProperties(Ciap.class, Atendimento.PROP_CIAP).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(Atendimento.PROP_CODIGO, getAtendimento().getAtendimentoPrincipal().getCodigo())).start().getVO();
                    if (atendimento != null) {
                        form.getModel().getObject().setCiap(atendimento.getCiap());
                        form.getModel().getObject().setCiap(atendimento.getCiap());
                        autoCompleteConsultaCiap.getModel().setObject(atendimento.getCiap());
                        autoCompleteConsultaCiap.setComponentValue(atendimento.getCiap());

                        new TempHelperV2().save(form);
                    }
                }
            } else {
                warn(BundleManager.getString("msgClassificacaoAtendimento"));

            }
        }
    }

    private DropDown getCbxTipoAtendimento() {
        List<ClassificacaoAtendimento> caList;
        caList = LoadManager.getInstance(ClassificacaoAtendimento.class)
                .addProperty(ClassificacaoAtendimento.PROP_CODIGO)
                .addProperty(ClassificacaoAtendimento.PROP_CODIGO_ESUS)
                .addProperty(ClassificacaoAtendimento.PROP_DESCRICAO)
                .addProperty(ClassificacaoAtendimento.PROP_CLASSIFICACAO_ESUS)
                .addInterceptor(new LoadInterceptorClassificacaoCbo(getAtendimento().getProfissional(), getAtendimento().getEmpresa()))
                .addSorter(new QueryCustom.QueryCustomSorter(ClassificacaoAtendimento.PROP_ORDEM))
                .addSorter(new QueryCustom.QueryCustomSorter(ClassificacaoAtendimento.PROP_CODIGO))
                .addParameter(new QueryCustomParameter(ClassificacaoAtendimento.PROP_CLASSIFICACAO_ESUS, BuilderQueryCustom.QueryParameter.DIFERENTE,
                        ClassificacaoAtendimento.ClassificacaoEsus.VIGILANCIA_SAUDE_BUCAL.value(), HQLHelper.NOT_RESOLVE_TYPE, ClassificacaoAtendimento.ClassificacaoEsus.OUTROS.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(ClassificacaoAtendimento.PROP_SITUACAO, ClassificacaoAtendimento.Situacao.ATIVO.value()))
                .start().getList();

        ClassificacaoAtendimento classAtendPadrao = getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getClassificacaoAtendimento();
        cbxTipoAtendimento.addChoice(null, "");
        boolean classificacaoPadrao = false;
        if (CollectionUtils.isNotNullEmpty(caList)) {
            for (ClassificacaoAtendimento ca : caList) {
                if (RepositoryComponentDefault.SEXO_FEMININO.equals(getAtendimento().getUsuarioCadsus().getSexo())
                        || (!"ABP001".equals(ca.getCodigoEsus())
                        && !"ABP022".equals(ca.getCodigoEsus())
                        && !"ABP002".equals(ca.getCodigoEsus()))
                ) {
                    cbxTipoAtendimento.addChoice(ca, ca.getDescricao());
                    if (ca.equals(classAtendPadrao)) {
                        classificacaoPadrao = true;
                    }
                }
            }
        }
        try {
            ClassificacaoAtendimentoPorCondicaoSaude classifAtendCondicaoSaude = new ClassificacaoAtendimentoPorCondicaoSaude(caList, getAtendimento(), form.getModel().getObject().getClassificacaoAtendimento(), form.getModel().getObject().getAtendimentoClassificacaoAtendimentoList());

            if(classifAtendCondicaoSaude.isMostraClassificacaoPorCondicaosaude()) {
                if (form.getModel().getObject().getClassificacaoAtendimento() == null) {
                    form.getModel().getObject().setClassificacaoAtendimento(classifAtendCondicaoSaude.getClassificacaoAtendimento());
                }
                adicionarClassificacaoAtendimento(classifAtendCondicaoSaude.getAtendimentoClassificacaoAtendimentoList());
            } else if (classificacaoPadrao && getAtendimento().getClassificacaoAtendimento() == null) {
                form.getModel().getObject().setClassificacaoAtendimento(classAtendPadrao);
            } else {
                if (form.getModel().getObject().getClassificacaoAtendimento() == null) {
                    ClassificacaoAtendimento classificacaoAtendimento = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("defineTipoAtendimentoUtilizadoTelaAtendimento");
                    if (classificacaoAtendimento != null) {
                        form.getModel().getObject().setClassificacaoAtendimento(classificacaoAtendimento);
                    }
                }
            }
        } catch (DAOException ex) {
            Loggable.log.error(ex);
        }
        new TempHelperV2().save(form);

        if (TipoAtendimento.TiposAtendimento.ATENDIMENTO_PEQUENA_CIRURGIA.value().equals(getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getTipoAtendimento())) {
            cbxTipoAtendimento.setEnabled(false);
        }

        return cbxTipoAtendimento;
    }

    private void avaliarDiasRetorno(AjaxRequestTarget target) {
        if (target != null) {
            if (txtDiasRetorno.getModel().getObject() == null) {
                txaObservacaoRetorno.limpar(target);
                JScript.hideFieldset(target, containerRetorno);
            } else {
                target.add(txaObservacaoRetorno);
                target.focusComponent(txaObservacaoRetorno);
                JScript.showFieldset(target, containerRetorno);
            }
        }
        txaObservacaoRetorno.setEnabled(txtDiasRetorno.getModel().getObject() != null);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response); //To change body of generated methods, choose Tools | Templates.
        response.render(CssHeaderItem.forReference(new CssResourceReference(this.getClass(), CSS_FILE)));
        if (txtDiasRetorno.getModel().getObject() != null) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerRetorno)));
        }
        response.render(OnLoadHeaderItem.forScript(JScript.showFieldset(containerCiap)));
    }

    public void validarCiapSexoPaciente(AjaxRequestTarget target, Ciap ciap) {
        boolean ciapInvalido = CiapHelper.isInvalidoCiapSexoPaciente(ciap, getAtendimento());
        if (ciapInvalido) {
            String msg = Bundle.getStringApplication("ciap_nao_valido_sexo_paciente");
            MessageUtil.warn(target, this, msg);
        }
    }

    private void setIdadeGestacional(AjaxRequestTarget target) {
        if (dchDumGestante.getComponentValue() != null) {
            txtIdadeGestacional.setComponentValue(Data.calcularIdadeGestacional(dchDumGestante.getComponentValue(), getAtendimento().getDataChegada()));
            if (target != null) {
                target.add(txtIdadeGestacional);
            }
        }
    }
}
