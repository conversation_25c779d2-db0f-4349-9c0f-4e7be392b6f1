package br.com.celk.view.atendimento.prontuario.panel.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Conduta;
import br.com.ksisolucoes.vo.prontuario.basico.CondutaAtendimento;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgInformarConduta extends Window{
    
    private PnlInformarConduta pnlInformarConduta;
    
    public DlgInformarConduta(String id){
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("conduta");
            }
        });
                
        setInitialWidth(600);
        setInitialHeight(300);
        setResizable(true);
        
        setContent(pnlInformarConduta = new PnlInformarConduta(getContentId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, List<CondutaAtendimento> condutaList) throws ValidacaoException, DAOException {
                close(target);
                DlgInformarConduta.this.onConfirmar(target, condutaList);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target, List<CondutaAtendimento> condutaList) throws ValidacaoException, DAOException;
    
    public void show(AjaxRequestTarget target, List<CondutaAtendimento> condutaList, boolean apenasRetorno){
        show(target);
        pnlInformarConduta.setObject(target, condutaList, apenasRetorno);
    }    
}