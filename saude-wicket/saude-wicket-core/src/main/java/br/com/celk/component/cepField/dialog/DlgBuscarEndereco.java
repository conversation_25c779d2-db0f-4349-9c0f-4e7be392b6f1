package br.com.celk.component.cepField.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.basico.dto.CepWSDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 * Created by laudecir on 28/05/18.
 */
public abstract class DlgBuscarEndereco extends Window {

    private PnlBuscarEndereco pnlBuscarEndereco;

    public DlgBuscarEndereco(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupPlaceholderTag(true);
        setTitle(BundleManager.getString("buscarEndereco"));

        setInitialWidth(850);
        setInitialHeight(440);
        setResizable(true);

        setContent(pnlBuscarEndereco = new PnlBuscarEndereco(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public void onSelected(AjaxRequestTarget target, CepWSDTO dto) {
                DlgBuscarEndereco.this.onSelected(target, dto);
                close(target);
            }
        });
    }

    @Override
    public void show(AjaxRequestTarget target) {
        super.show(target);
        pnlBuscarEndereco.limpar(target);
    }

    public abstract void onSelected(AjaxRequestTarget target, CepWSDTO dto);
}
