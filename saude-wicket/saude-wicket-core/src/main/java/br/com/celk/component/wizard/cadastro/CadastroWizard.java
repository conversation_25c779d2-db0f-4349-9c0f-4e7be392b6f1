package br.com.celk.component.wizard.cadastro;

import br.com.celk.component.wizard.AjaxWizard;
import br.com.celk.component.wizard.IAjaxWizardStep;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.util.MessageUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.ReflectionUtil;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.DictionaryData;
import java.lang.reflect.InvocationTargetException;
import java.util.Iterator;
import org.apache.wicket.Component;
import org.apache.wicket.MarkupContainer;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class CadastroWizard<T> extends AjaxWizard {

    protected T object;
    private boolean viewOnly;

    public CadastroWizard(String id) {
        this(id, null);
    }

    public CadastroWizard(String id, T object) {
        this(id, object, false);
    }
    
    public CadastroWizard(String id, T object, boolean viewOnly) {
        super(id);
        this.object = object;
        this.viewOnly = viewOnly;
        init();
    }

    private void init() {
        newInstance();
        setDefaultModel(new CompoundPropertyModel<T>(object));
    }

    @Override
    public void onActiveStepChanged(final IAjaxWizardStep newStep) {
        super.onActiveStepChanged(newStep);
        if (viewOnly) {
            enableFields((MarkupContainer)newStep);
        }
    }
    
    private void enableFields(MarkupContainer parent){
        Iterator<Component> iterator = parent.iterator();
        while(iterator.hasNext()){
            Component next = iterator.next();
            next.setEnabled(!viewOnly);
        }
    }

    public void newInstance() {
        if (object == null) {
            try {
                object = getReferenceClass().newInstance();
                customNewInstance(object);
            } catch (InstantiationException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            } catch (IllegalAccessException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }
    }
    
    @Override
    protected Component newButtonBar(String id) {
        return new CadastroWizardBar(id, this) {

            @Override
            public Class getResponsePage() {
                return CadastroWizard.this.getResponsePage();
            }

            @Override
            public boolean isFinishEnable() {
                return !viewOnly;
            }
        };
    }

    public void customNewInstance(T object) {
    }
    
    public abstract Class<T> getReferenceClass();

    @Override
    public final void onFinish(AjaxRequestTarget target) {
        try {
            salvar(target);
        } catch (DAOException ex) {
            MessageUtil.modalError(target, this, ex);
        } catch (ValidacaoException ex) {
            MessageUtil.modalWarn(target, this, ex);
        }
    }
    
    public void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        Object returnObject = salvar(object);
        
        Page page = null;
        try {
            page = (Page) getResponsePage().newInstance();
            setResponsePage(page);
            
            getSession().getFeedbackMessages().info(page, getMsgSalvo(returnObject));
            customResponsePage(page);
        } catch (InstantiationException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (IllegalAccessException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        
    }
    
    public String getMsgSalvo(Object returnObject){
        String msg = BundleManager.getString("registro_salvo_sucesso");
            
        String identificador = null;
        
        if (getReferenceClass() != null && getReferenceClass().isAnnotationPresent(DictionaryData.class)) {
            DictionaryData referencia = (DictionaryData) getReferenceClass().getAnnotation(DictionaryData.class);
            try {
                identificador = (String) ReflectionUtil.getValueByProperty(returnObject, referencia.referenceField());
                msg += " "+BundleManager.getString("referencia")+": "+identificador;
            } catch (NoSuchMethodException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            } catch (IllegalAccessException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            } catch (InvocationTargetException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }
        
        if (identificador == null && returnObject instanceof CodigoManager && ((CodigoManager)returnObject).getCodigoManager()!=null) {
            identificador = ((CodigoManager)returnObject).getCodigoManager().toString();
            msg += " "+BundleManager.getString("codigo")+": "+identificador;
        }
        return msg;
    }
    
    public abstract Class getResponsePage();
    
    public void customResponsePage(Page page) {}
    
    public Object salvar(T object) throws DAOException, ValidacaoException{
        return BOFactoryWicket.save(object);
    }
}
