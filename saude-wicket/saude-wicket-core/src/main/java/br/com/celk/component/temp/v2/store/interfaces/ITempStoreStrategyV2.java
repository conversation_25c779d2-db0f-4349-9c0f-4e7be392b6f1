package br.com.celk.component.temp.v2.store.interfaces;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public interface ITempStoreStrategyV2 extends Serializable{

    public void store(Object value) throws ValidacaoException, DAOException;
    
    public void delete() throws ValidacaoException, DAOException;
    
    public Object load() throws ValidacaoException, DAOException;
    
}
