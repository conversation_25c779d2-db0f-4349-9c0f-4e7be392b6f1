package br.com.celk.view.basico.empresa.pnl;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.cepField.CepWsField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.utils.ComponentUtils;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.ksisolucoes.TipoEstabelecimento;
import br.com.ksisolucoes.bo.basico.dto.CadastroEmpresaDTO;
import br.com.ksisolucoes.bo.basico.dto.CepWSDTO;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import ch.lambdaj.Lambda;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlCadastrarEmpresa extends Panel {

    private CompoundPropertyModel<CadastroEmpresaDTO> model;
    private WebMarkupContainer container;
    private InputField txtDescricao;
    private AutoCompleteConsultaCidade autoCompleteConsultaCidade;
    private DropDown<Long> dropDownTipoEstabelecimento;
    private List<Long> lstTiposEstabelecimento = TipoEstabelecimento.codigos();

    private CepWsField cepWsField;
    private InputField txtLogradouro;
    private InputField txtBairro;
    private InputField txtComplemento;
    private InputField txtNumero;


    public PnlCadastrarEmpresa(String id) {
        super(id);
        init();
    }

    private void init() {
        Form form = new Form("form", model = new CompoundPropertyModel(new CadastroEmpresaDTO()));

        CadastroEmpresaDTO proxy = Lambda.on(CadastroEmpresaDTO.class);

        form.add(container = new WebMarkupContainer("container"));
        container.setOutputMarkupId(true);
        container.add(txtDescricao = new InputField(path(proxy.getEmpresa().getDescricao())));
        txtDescricao.addRequiredClass();
        txtDescricao.setLabel(new Model(bundle("descricao")));
        container.add(new UpperField(path(proxy.getEmpresa().getReferencia())));
        container.add(new InputField(path(proxy.getEmpresa().getCnes())));
        container.add(new UpperField(path(proxy.getEmpresa().getSigla())));
        container.add(getDropDownTipoEstabelecimento(path(proxy.getEmpresa().getTipoUnidade())));
        container.add(new InputField(path(proxy.getEmpresa().getCnpj())));
        container.add(DropDownUtil.getIEnumDropDown(path(proxy.getEmpresa().getLocalAtendimento()), Empresa.LocalAtendimento.values(), false, false));
        container.add(new InputField(path(proxy.getEmpresa().getFantasia())));
        container.add(autoCompleteConsultaCidade = new AutoCompleteConsultaCidade(path(proxy.getEmpresa().getCidade())));
        autoCompleteConsultaCidade.getTxtDescricao().addRequiredClass();
        autoCompleteConsultaCidade.setLabel(new Model(bundle("cidade")));
        container.add(txtBairro = new InputField(path(proxy.getEmpresa().getBairro())));
        container.add(txtLogradouro = new InputField(path(proxy.getEmpresa().getRua())));
        container.add(txtComplemento = new InputField(path(proxy.getEmpresa().getComplemento())));

        container.add(txtNumero = new InputField(path(proxy.getEmpresa().getNumero())));
//        container.add(new InputField(path(proxy.getEmpresa().getCep())));
        container.add(cepWsField = new CepWsField(path(proxy.getEmpresa().getCep())) {
            @Override
            public void load(AjaxRequestTarget target, CepWSDTO cepWSDTO) {
                autoCompleteConsultaCidade.setComponentValue(cepWSDTO.getCidade());
                txtLogradouro.setComponentValue(cepWSDTO.getLogradouro());
                txtBairro.setComponentValue(cepWSDTO.getBairro());
                txtComplemento.setComponentValue(cepWSDTO.getComplemento());

                target.add(autoCompleteConsultaCidade);
                target.add(txtLogradouro);
                target.add(txtBairro);
                target.add(txtComplemento);

                target.focusComponent(txtNumero);
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
            }

            @Override
            public void unload(AjaxRequestTarget target) {
                txtLogradouro.limpar(target);
                txtComplemento.limpar(target);
                autoCompleteConsultaCidade.limpar(target);
            }
        });
        container.add(new InputField(path(proxy.getEmpresa().getTelefone())));
        container.add(new InputField(path(proxy.getEmpresa().getFax())));
        container.add(new InputField(path(proxy.getEmpresa().getEmail())));
        container.add(new InputField(path(proxy.getEmpresa().getContato())));

        form.add(new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                PnlCadastrarEmpresa.this.onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        form.add(new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(target);
            }
        });

        add(form);
    }

    public abstract void onSalvar(AjaxRequestTarget target, Empresa empresa) throws ValidacaoException, DAOException;

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        CadastroEmpresaDTO dto = model.getObject();
        { // Validações
            if (StringUtils.trimToNull(dto.getEmpresa().getDescricao()) == null) {
                throw new ValidacaoException(bundle("msgInformeDescricao"));
            }
            if (dto.getEmpresa().getCidade() == null) {
                throw new ValidacaoException(bundle("msgInformeCidade"));
            }
        }

        Empresa empresaSalva = BOFactoryWicket.getBO(BasicoFacade.class).cadastrarEmpresa(dto);
        onSalvar(target, empresaSalva);
    }

    private DropDown getDropDownTipoEstabelecimento(String id) {
        if (this.dropDownTipoEstabelecimento == null) {
            this.dropDownTipoEstabelecimento = new DropDown(id);
            configTiposEstabelecimentos();
        }
        return this.dropDownTipoEstabelecimento;
    }

    private void configTiposEstabelecimentos() {
        this.dropDownTipoEstabelecimento.removeAllChoices();
        for (Long tipoEstabelecimento : lstTiposEstabelecimento) {
            this.dropDownTipoEstabelecimento.addChoice(tipoEstabelecimento, Empresa.getTipoUnidadeFormatado(tipoEstabelecimento));
        }
    }

    public void setLstTiposEstabelecimento(List<Long> lstTiposEstabelecimento) {
        this.lstTiposEstabelecimento = lstTiposEstabelecimento;
        configTiposEstabelecimentos();
    }

    public void limpar(AjaxRequestTarget target) {
        model.setObject(new CadastroEmpresaDTO());
        ComponentUtils.limparContainer(container, target);
    }
}
