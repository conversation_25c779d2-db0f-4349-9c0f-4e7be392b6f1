package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.tabbedpanel.cadastro.CadastroTabbedPanel;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.DorCronicaDTO;
import org.apache.wicket.extensions.markup.html.tabs.ITab;

import java.util.List;

/**
 * <AUTHOR>
 */
public class DorCronicaTabbedPanel extends CadastroTabbedPanel<DorCronicaDTO> {

    public DorCronicaTabbedPanel(String id, DorCronicaDTO object, boolean viewOnly, List<ITab> tabs, boolean buttonBackVisible) {
        super(id, object, viewOnly, tabs, buttonBackVisible);
    }

    @Override
    public Class<DorCronicaDTO> getReferenceClass() {
        return DorCronicaDTO.class;
    }

    @Override
    public Class getResponsePage() {
        return null;
    }
}
