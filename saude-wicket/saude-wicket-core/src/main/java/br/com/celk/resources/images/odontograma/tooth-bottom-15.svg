<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
        xmlns:dc="http://purl.org/dc/elements/1.1/"
        xmlns:cc="http://creativecommons.org/ns#"
        xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
        xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
        version="1.0"
        width="104"
        height="255"
        viewBox="0 0 1040 2550"
        preserveAspectRatio="xMidYMid meet"
        id="svg2"
        name="38"
        inkscape:version="0.92.2 (unknown)"
        sodipodi:docname="tooth-bottom-15.svg">
  <defs
     id="defs9">
    <filter
       inkscape:label="Opacity"
       style="color-interpolation-filters:sRGB"
       id="opacityFilter">
      <feColorMatrix
         values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 -0 "
         result="colormatrix"
         id="feColorMatrix390" />
      <feComposite
         in2="colormatrix"
         operator="arithmetic"
         k2="0.5"
         result="composite"
         id="feComposite392"
         k1="0"
         k3="0"
         k4="0" />
    </filter>
  </defs>
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="1863"
     inkscape:window-height="1059"
     id="namedview228"
     showgrid="false"
     inkscape:zoom="1.9437558"
     inkscape:cx="-192.99159"
     inkscape:cy="75.034432"
     inkscape:window-x="1977"
     inkscape:window-y="21"
     inkscape:window-maximized="1"
     inkscape:current-layer="svg2" />
  <metadata
     id="metadata376">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title />
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <!--<defs id="defs374" />-->
  <g
     id="g18"
     transform="matrix(1,0,0,-1,83.955465,1717.0593)">
    <path
       d="m 138.52951,1704.3558 c -124.000001,-44 -173.000001,-207 -113.000001,-377 20,-58 27,-106 36,-238 30,-453.99997 107.000001,-688.00003 284.000001,-872.00003 66,-67.99997 102,-81.99997 129,-48 11,13 22,19 26,14 5,-5 27,-34.99997 51,-65.99997 24,-32.000004 64,-71.000004 89,-88.000004 73,-48 112,-37.0000002 112,32 0,36 1,37 25,26 19,-9 29,-8 49,6 37,24.000004 42,69.999974 16,167.999974 -34,124 -47,200 -61,337.99996 -20,212.00006 10,528.00007 59,625.00007 33,66 42,187 18,268 -22,75 -81,148 -147,181 -78,39 -111,34 -174,-24 -50,-47 -68,-47 -145,0 -111,67 -179,82 -254,55 z m 133,-35 c 19,-5 65,-28 103,-51 86,-52 126,-51 190,6 36,33 45,37 80,32 147,-20 240,-234 172,-396 -20,-48 -47,-76 -117,-122 -136,-89 -283,-103 -409,-41 -98,48 -170,118 -215.000001,210 -34,69 -38,84 -38,156 0,68 4,88 25,126 45.000001,80 115.000001,107 209.000001,80 z m -26,-589 c 67,-38 143,-61 206,-61 83,0 222,48 290,101 13,11 26,18 28,16 1,-2 -4,-51 -12,-108 -32,-225.00001 -11,-584.00003 45,-757.00003 24,-76 26,-139.99997 5,-147.99997 -41,-16 -167,109.99997 -232,231.99997 -58,110 -92,216.99996 -112,358.00004 -13,89.99998 -21,121.99998 -32,123.99998 -20,4 -39,-76 -46,-192.99996 -6,-103.0001 13,-226.00006 53,-343.00006 25,-72 25,-112 0,-112 -15,0 -99,84 -142,140 -43,57 -111,209.0001 -134,300.00006 -29,111.99996 -58,313.99996 -66.000001,457.99997 l -7,117 49.000001,-45 c 27,-25 75,-61 107,-79 z m 228,-580.00003 c 35,-108 108,-238 175,-310 61,-65.99997 84,-118.999974 66,-148.999974 -21,-32.0000002 -143,88.000004 -202,199.999974 -63,117 -82,191 -86,332.99996 l -4,130.00008 15,-68.99998 c 8,-38.0001 24,-97.99996 36,-135.00006 z"
       style="fill:#2d2d2d;stroke:none"
       id="contorno"
       inkscape:connector-curvature="0" />
    <path
       d="m 121.52951,1656.3558 c -19,-12 -45.000001,-43 -59.000001,-67 -21,-38 -25,-58 -25,-126 0,-72 4,-87 38,-156 45.000001,-92 117.000001,-162 215.000001,-210 126,-62 273,-48 409,41 95,62 125,107 138,207 9,63 -16,164 -51,216 -34,49 -93,88 -142,95 -35,5 -44,1 -80,-32 -64,-57 -104,-58 -190,-6 -112,68 -191,80 -253,38 z"
       style="fill:#ffffff;stroke:none"
       id="coroa"
       inkscape:connector-curvature="0">
      <title></title>
    </path>
    <path
       d="m 746.12351,678.92613 -296.699,226.42576 -298.262,-223.69136 c -24.832,115.59376 -47.691,280.80856 -54.629001,405.70307 l -7.012,116.9922 49.004001,-45 c 92,-85 213.008,-140 313.008,-140 83,0 222,47.9961 290,100.9961 13,11 25.988,17.9961 27.988,15.9961 1,-2 -3.992,-50.9883 -11.992,-107.9883 -14.142,-99.43431 -16.413,-225.27741 -11.406,-349.43357 z"
       style="fill:#ffffff;stroke:none"
       id="colo"
       inkscape:connector-curvature="0">
      <title></title>
    </path>
    <path
       d="m 702.23651,36.172176 c -38.227,3.0625 -138.082,107.175784 -189.707,205.175694 -63,117 -81.996,191.0079 -85.996,333.00786 l -4.004,130.00008 15,-69.00388 c 37,-176.00016 114.996,-341.00006 210.996,-445.00016 61,-65.99991 83.996,-119.003814 65.996,-149.003814 -2.625,-4 -6.824,-5.61328 -12.285,-5.17578 z m 96.641,85.820314 c -47.863,1.1051 -162.403,118.98428 -223.34,233.35928 -58,110.0001 -92.012,217.00786 -112.012,358.00794 -13,89.99998 -20.992,122.00388 -31.992,124.00388 -20,4 -38.996,-76.0078 -45.996,-193.00776 -6,-103.0001 12.988,-226.00797 52.988,-343.00797 25,-72 25,-111.9921 0,-111.9921 -15,0 -98.992,84.00001 -141.992,140.00001 -43,57 -111.004,209.0001 -134.004,300.00006 -3.848,14.8599 -7.569,34.6219 -11.367,52.3047 l 298.262,223.69136 296.699,-226.42576 c 6.322,-156.78436 25.154,-311.03216 56.406,-407.57826 24,-76 26,-139.98819 5,-147.98819 -2.563,-1 -5.461,-1.44086 -8.652,-1.36719 z"
       style="fill:#ffffff;stroke:none"
       id="raiz"
       inkscape:connector-curvature="0">
      <title></title>
    </path>
  </g>
  <!--<g-->
  <!--id="watermark"-->
  <!--transform="matrix(2.6148239,0,0,2.6148239,-189.19789,408.10759)"-->
  <!--style="display:none">-->
  <!--<path-->
  <!--d="m 326.195,365.955 c -21.313,-7.268 -60.938,-46.296 -79.713,-78.513 -21.729,-37.285 -24.798,-37.948 -45.701,-9.894 -6.463,8.676 -10.801,11.541 -13.771,9.095 -3.303,-2.72 -3.9,-2.187 -2.511,2.236 2.363,7.521 -26.986,22.298 -33.456,16.845 -3.466,-2.922 -4.219,-1.804 -3.61,5.365 0.424,4.979 -1.659,10.395 -4.629,12.034 -2.971,1.637 -4.662,5.235 -3.757,7.994 3.037,9.266 -3.685,11.714 -9.007,3.28 -4.952,-7.846 -5.147,-7.745 -4.298,2.215 0.535,6.277 -1.738,11.762 -5.709,13.779 -3.629,1.844 -10.046,6.011 -14.263,9.262 -4.215,3.25 -9.562,4.31 -11.882,2.355 -6.164,-5.196 -6.688,-22.399 -0.78,-25.656 2.793,-1.54 3.539,-5.595 1.656,-9.009 -2.754,-4.995 -0.199,-9.321 13.064,-22.117 11.118,-10.727 15.619,-17.331 13.819,-20.275 -1.817,-2.972 8.786,-18.264 33.218,-47.913 19.736,-23.952 37.267,-45.531 38.955,-47.952 1.688,-2.422 0.544,-9.478 -2.543,-15.68 -3.087,-6.203 -12.133,-32.894 -20.102,-59.312 -7.97,-26.42 -16.338,-53.363 -18.596,-59.874 -14.872,-42.874 -16.914,-51.3 -14.513,-59.885 1.477,-5.274 6.104,-12.003 10.283,-14.952 8.189,-5.78 33.025,0.013 45.634,10.645 13.422,11.314 43.306,83.648 45.087,109.131 2.369,33.884 5.998,34.797 29.605,7.447 31.547,-36.547 56.715,-61.382 65.924,-65.049 13.271,-5.284 35.892,-0.106 45.132,10.33 4.532,5.118 8.684,16.295 9.227,24.839 0.977,15.328 0.746,15.69 -17.315,27.463 -11.883,7.744 -32.341,28.074 -58.303,57.938 l -39.998,46.008 17.196,39.24 c 21.365,48.75 30.999,67.901 40.535,80.58 9.104,12.104 10.811,26.363 3.44,28.778 -3.27,1.072 -3.33,2.104 -0.151,2.574 2.919,0.433 4.976,3.025 4.569,5.763 -0.403,2.738 -6.134,3.137 -12.736,0.885 z"-->
  <!--id="path2"-->
  <!--sodipodi:insensitive="true"-->
  <!--inkscape:connector-curvature="0"-->
  <!--style="fill:#ff0000" />-->
  <!--</g>-->
  <path
     d="m 663.74459,2197.9562 c -55.72974,-19.0046 -159.34214,-121.0559 -208.43546,-205.2977 -56.8175,-97.4937 -64.8424,-99.2273 -119.50006,-25.8711 -16.89961,22.6862 -28.24272,30.1777 -36.00874,23.7819 -8.63677,-7.1124 -10.19782,-5.7187 -6.56583,5.8467 6.17883,19.6661 -70.56363,58.3053 -87.48154,44.0467 -9.06298,-7.6405 -11.03195,-4.7171 -9.43952,14.0285 1.10869,13.0193 -4.33799,27.1811 -12.10402,31.4668 -7.76864,4.2805 -12.19031,13.6886 -9.82389,20.9029 7.94122,24.229 -9.63563,30.6301 -23.55172,8.5767 -12.94861,-20.516 -13.4585,-20.2519 -11.23851,5.7918 1.39893,16.4132 -4.54457,30.7556 -14.92803,36.0297 -9.4892,4.8217 -26.268524,15.7177 -37.295236,24.2185 -11.021483,8.4981 -25.002946,11.2698 -31.069338,6.1579 -16.117774,-13.5867 -17.487942,-58.5695 -2.039562,-67.086 7.303203,-4.0268 9.253861,-14.6299 4.330148,-23.5569 -7.201225,-13.0611 -0.52035,-24.3728 34.160059,-57.8321 29.071609,-28.0492 40.840939,-45.3175 36.134249,-53.0155 -4.75113,-7.7713 22.97385,-47.7572 86.85922,-125.2841 51.60617,-62.6303 97.44665,-119.0556 101.86047,-125.3861 4.41382,-6.3331 1.42246,-24.7833 -6.6495,-41.0004 -8.07196,-16.2197 -31.72566,-86.012 -52.56319,-155.0904 -20.84015,-69.0837 -42.72099,-139.5349 -48.62526,-156.56 -38.88767,-112.108 -44.22714,-134.1405 -37.94894,-156.5887 3.86209,-13.7906 15.96088,-31.3858 26.88823,-39.0969 21.41279,-15.1137 86.35456,0.034 119.32487,27.8348 35.09617,29.5841 113.23757,218.7248 117.89457,285.3584 6.19452,88.6007 15.68371,90.988 77.41186,19.4726 82.48985,-95.564 148.29974,-160.5031 172.37965,-170.0917 34.70133,-13.8167 93.85126,-0.2772 118.01223,27.0111 11.85039,13.3827 22.70714,42.6086 24.12698,64.9496 2.55469,40.0801 1.95066,41.0266 -45.27567,71.8109 -31.07195,20.2492 -84.56602,73.4086 -152.45208,151.4977 l -104.58773,120.3028 44.96452,102.6058 c 55.86571,127.4726 81.05692,177.5491 105.99188,210.7025 23.80536,31.6498 28.26886,68.9346 8.995,75.2494 -8.55048,2.8031 -8.70737,5.5016 -0.39484,6.7305 7.63267,1.1322 13.01136,7.9099 11.94713,15.0693 -1.05377,7.1593 -16.03933,8.2027 -33.3024,2.3141 z"
     id="watermark"
     style="display:none;fill:#ff0000;stroke-width:2.61482382"
     sodipodi:insensitive="true"
     inkscape:connector-curvature="0" />
</svg>
