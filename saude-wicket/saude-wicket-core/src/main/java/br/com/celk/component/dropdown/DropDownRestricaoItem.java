package br.com.celk.component.dropdown;

import br.com.celk.component.interfaces.IComponent;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class DropDownRestricaoItem implements Serializable{
    
    private BuilderQueryCustom.QueryParameter parameter;
    private IComponent component;

    public DropDownRestricaoItem(BuilderQueryCustom.QueryParameter parameter, IComponent value) {
        this.parameter = parameter;
        this.component = value;
    }

    public QueryParameter getParameter() {
        return parameter;
    }

    public IComponent getComponent() {
        return component;
    }

}
