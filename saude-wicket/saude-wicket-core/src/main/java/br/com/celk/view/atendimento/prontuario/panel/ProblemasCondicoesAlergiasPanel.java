package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.tabbedpanel.cadastro.CadastroTab;
import br.com.celk.component.tabbedpanel.cadastro.ITabPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.atendimento.prontuario.tabbedpanel.GrupoAlergiaReacaoAdversaTab;
import br.com.celk.view.atendimento.prontuario.tabbedpanel.GrupoProblemasCondicaoTab;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NoProblemasCondicoesAlergiasDTO;
import org.apache.wicket.extensions.markup.html.tabs.ITab;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ProblemasCondicoesAlergiasPanel extends ProntuarioCadastroPanel {

    private NoProblemasCondicoesAlergiasDTO noProblemasCondicoesAlergiasDTO;

    public ProblemasCondicoesAlergiasPanel(String id, String titulo) {
        super(id, titulo);
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        List<ITab> tabs = new ArrayList<ITab>();
        noProblemasCondicoesAlergiasDTO = new NoProblemasCondicoesAlergiasDTO();
        noProblemasCondicoesAlergiasDTO.setAtendimento(getAtendimento());
        noProblemasCondicoesAlergiasDTO.setUsuarioCadsus(getAtendimento().getUsuarioCadsus());
        tabs.add(new CadastroTab<NoProblemasCondicoesAlergiasDTO>(noProblemasCondicoesAlergiasDTO) {

            @Override
            public ITabPanel<NoProblemasCondicoesAlergiasDTO> newTabPanel(String id, NoProblemasCondicoesAlergiasDTO noProblemasCondicoesAlergiasDTO) {
                return new GrupoProblemasCondicaoTab(id, noProblemasCondicoesAlergiasDTO);
            }
        });
        tabs.add(new CadastroTab<NoProblemasCondicoesAlergiasDTO>(noProblemasCondicoesAlergiasDTO) {

            @Override
            public ITabPanel<NoProblemasCondicoesAlergiasDTO> newTabPanel(String id, NoProblemasCondicoesAlergiasDTO noProblemasCondicoesAlergiasDTO) {
                return new GrupoAlergiaReacaoAdversaTab(id, noProblemasCondicoesAlergiasDTO);
            }
        });

        add(new GrupoProblemasCondicaoTabbedPanel("wizard", noProblemasCondicoesAlergiasDTO, false, tabs, false));
    }
}
