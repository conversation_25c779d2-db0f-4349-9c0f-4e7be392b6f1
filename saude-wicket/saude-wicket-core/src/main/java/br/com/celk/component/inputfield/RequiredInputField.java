package br.com.celk.component.inputfield;

import java.io.Serializable;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 * @param <T>
 */
public class RequiredInputField<T extends Serializable> extends InputField<T> {

    public RequiredInputField(String id) {
        super(id);
        init();
    }

    public RequiredInputField(String id, Class<T> type) {
        super(id, type);
        init();
    }

    public RequiredInputField(String id, IModel<T> model) {
        super(id, model);
        init();
    }

    public RequiredInputField(String id, IModel<T> model, Class<T> type) {
        super(id, model, type);
        init();
    }

    private void init() {
        setRequired(true);
        addRequiredClass();
    }

}
