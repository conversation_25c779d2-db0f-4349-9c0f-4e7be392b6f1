package br.com.celk.view.basico.motivocancelamento.pnl;

import br.com.celk.component.consulta.PnlConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.basico.motivocancelamento.customize.CustomizeConsultaMotivoCancelamento;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.vo.basico.MotivoCancelamento;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class PnlConsultaMotivoCancelamento extends PnlConsulta<MotivoCancelamento> {

    public PnlConsultaMotivoCancelamento(String id, IModel<MotivoCancelamento> model, boolean required) {
        super(id, model, required);
    }

    public PnlConsultaMotivoCancelamento(String id, IModel<MotivoCancelamento> model) {
        super(id, model);
    }

    public PnlConsultaMotivoCancelamento(String id, boolean required) {
        super(id, required);
    }

    public PnlConsultaMotivoCancelamento(String id) {
        super(id);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {

            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaMotivoCancelamento();
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("motivosCancelamento");
    }

}
