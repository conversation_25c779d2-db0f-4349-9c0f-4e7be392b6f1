package br.com.celk.view.atendimento.prontuario.tabbedpanel;

import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NoHistoricoClinicoDTO;
import br.com.ksisolucoes.vo.prontuario.basico.Puerperio;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class PreNatalPartoTab extends TabPanel<NoHistoricoClinicoDTO> {

    private WebMarkupContainer containerParto;

    public PreNatalPartoTab(String id, NoHistoricoClinicoDTO object) {
        super(id, object);
        init();
    }

    public void init() {
        NoHistoricoClinicoDTO proxy = on(NoHistoricoClinicoDTO.class);

        containerParto = new WebMarkupContainer("containerParto");

        containerParto.add(new DateChooser(path(proxy.getDadosClinicoDTO().getPuerperio().getDataParto())));
        containerParto.add(DropDownUtil.getIEnumDropDown(path(proxy.getDadosClinicoDTO().getPuerperio().getTipoParto()), Puerperio.TipoParto.values(), true));
        containerParto.add(DropDownUtil.getIEnumDropDown(path(proxy.getDadosClinicoDTO().getPuerperio().getLocalOcorrencia()), Puerperio.LocalOcorrencia.values(), true));
        containerParto.add(new InputField<String>(path(proxy.getDadosClinicoDTO().getPuerperio().getEstabelecimento())));
        containerParto.add(new InputField(path(proxy.getDadosClinicoDTO().getPuerperio().getIdadeGestacional())));
        containerParto.setEnabled(false);

        add(containerParto);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
    }

    @Override
    public String getTitle() {
        return bundle("parto");
    }

}