package br.com.celk.component.interfaces;

import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.navigation.paging.IPageableItems;

/**
 *
 * <AUTHOR>
 */
public interface IPageableComponent<T> extends IPageableItems{
    
    public List<IColumn> getColumns();
    
    public long getRowCount();
    
    public Component getComponent();
    
    public boolean hasNextPage();
    
    public boolean hasPreviousPage();
    
    public void firstPage(AjaxRequestTarget target);
    
    public void nextPage(AjaxRequestTarget target);
    
    public void previousPage(AjaxRequestTarget target);
    
    public void lastPage(AjaxRequestTarget target);
    
    public void populate(AjaxRequestTarget target);
    
    public void setCurrentPage(AjaxRequestTarget target, long page);

}
