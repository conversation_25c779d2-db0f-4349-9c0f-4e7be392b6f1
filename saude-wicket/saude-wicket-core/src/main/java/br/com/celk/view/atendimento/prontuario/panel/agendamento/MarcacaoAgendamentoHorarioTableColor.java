package br.com.celk.view.atendimento.prontuario.panel.agendamento;

import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.table.CustomColorSelectionTableRow;
import br.com.celk.component.table.SelectionTable;
import br.com.celk.component.table.TableColorEnum;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTO;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.IModel;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
public class MarcacaoAgendamentoHorarioTableColor extends SelectionTable {
    private LinkedHashMap<Date, TableColorEnum> map;

    public MarcacaoAgendamentoHorarioTableColor(String id, List<IColumn> columns, ICollectionProvider collectionProvider) {
        super(id, columns, collectionProvider);
        map = new LinkedHashMap<Date, TableColorEnum>();
    }

    @Override
    protected Item newRowItem(String id, int index, IModel model) {
        return new CustomColorSelectionTableRow(id, index, model, MarcacaoAgendamentoHorarioTableColor.this) {
            @Override
            public TableColorEnum getColor() {
                AgendaGradeAtendimentoDTO dto = (AgendaGradeAtendimentoDTO) getRowObject();
                Date data = dto.getAgendaGradeAtendimento().getAgendaGrade().getData();
                if (map.isEmpty()) {
                    map.put(data, TableColorEnum.POSITIVA);
                    return TableColorEnum.POSITIVA;
                }
                if (map.containsKey(data)) {
                    return map.get(data);
                }
                TableColorEnum classe = (TableColorEnum) map.values().toArray()[map.values().size() - 1];
                if (classe.equals(TableColorEnum.POSITIVA)) {
                    classe = TableColorEnum.NEGATIVA;
                } else {
                    classe = TableColorEnum.POSITIVA;
                }

                map.put(data, classe);
                return classe;
            }
        };
    }

}