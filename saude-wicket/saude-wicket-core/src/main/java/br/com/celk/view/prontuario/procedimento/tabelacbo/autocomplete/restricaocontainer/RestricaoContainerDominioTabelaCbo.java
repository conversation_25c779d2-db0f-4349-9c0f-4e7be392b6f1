package br.com.celk.view.prontuario.procedimento.tabelacbo.autocomplete.restricaocontainer;

import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.inputfield.InputField;
import br.com.ksisolucoes.bo.prontuario.procedimento.interfaces.dto.QueryConsultaDominioTabelaCboDTOParam;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class RestricaoContainerDominioTabelaCbo extends Panel implements IRestricaoContainer<QueryConsultaDominioTabelaCboDTOParam> {

    private InputField<String> txtCodigo;
    private InputField<String> txtDescricao;
    
    private QueryConsultaDominioTabelaCboDTOParam param = new QueryConsultaDominioTabelaCboDTOParam();
    
    public RestricaoContainerDominioTabelaCbo(String id) {
        super(id);
        
        WebMarkupContainer root = new WebMarkupContainer("root", new CompoundPropertyModel(param));
        
        root.add(txtCodigo = new InputField<String>("codigo"));
        root.add(txtDescricao = new InputField<String>("descricao"));
        
        add(root);
    }

    @Override
    public QueryConsultaDominioTabelaCboDTOParam getRestricoes() {
        return param;
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        txtCodigo.limpar(target);
        txtDescricao.limpar(target);
    }

    @Override
    public Component getComponentRequestFocus() {
        return txtCodigo;
    }

}
