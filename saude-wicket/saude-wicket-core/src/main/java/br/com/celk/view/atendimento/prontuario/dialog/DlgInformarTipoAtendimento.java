package br.com.celk.view.atendimento.prontuario.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgInformarTipoAtendimento extends Window {

    private PnlInformarTipoAtendimento pnlInformarTipoAtendimento;

    public DlgInformarTipoAtendimento(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("informeTipoAtendimento");
            }
        });

        setInitialWidth(700);
        setInitialHeight(50);

        setResizable(false);

        setContent(pnlInformarTipoAtendimento = new PnlInformarTipoAtendimento(getContentId()) {

            @Override
            public void onOk(AjaxRequestTarget target, TipoAtendimento tipoAtendimento) throws ValidacaoException, DAOException {
                if (tipoAtendimento != null) {
                    fechar(target);
                    DlgInformarTipoAtendimento.this.onOk(target, tipoAtendimento);
                }
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                fechar(target);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {

            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                fechar(target);
                return true;
            }
        });
    }

    public abstract void onOk(AjaxRequestTarget target, TipoAtendimento tipoAtendimento) throws ValidacaoException, DAOException;

    public void onFechar(AjaxRequestTarget target) {
        pnlInformarTipoAtendimento.limpar(target);
    }

    private void fechar(AjaxRequestTarget target) {
        DlgInformarTipoAtendimento.this.onFechar(target);
        close(target);
    }
}