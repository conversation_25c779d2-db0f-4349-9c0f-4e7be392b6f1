@import "defines.scss";

#login-page {

    #warning-browser{
        display: none;
    }

    .browsers { 
        margin-top: 5px; 
        overflow: hidden;
        a {
            float: left;
            text-align: center;
            margin: 5px;
            width: 10%;
        }
    }

    .browsers-info { 
        margin-top: 5px; 
        overflow: hidden;
        text-align: center;
        a {
            float: left;
            width: 10%;
        }
    }

    div.between-infos{
        margin-top: 20px;
    }

    div.login-infos{
        margin-left: auto;
        width: 600px;
    }

    h3{
        font-size: 15px;
        font-weight: bold;
    }

    .login-center-div{
        margin-right: auto;
        width: 350px;
    }

    div.relative-space{
        height: 15vh; 
    }

    fieldset.login-fieldset{
        background: linear-gradient(#ECF6F7,#ECF6F7);
        border-color: #0D8280;
        padding: 0px 10px;
        width: 308px;
    }

    fieldset fieldset {
        border: 0 none;
        padding: 30px;
        select{
            width: 160px;
        }
    }

    .field {
        border: 0 none;
    }

    a.img{
        display: block;
    }

    a.gem-logo{
        background: url("../images/logos/celk-saude.png") no-repeat scroll left center transparent;
        background-size: 50%;
        height: 80px;
        cursor: default !important;
        &:hover{
            cursor: default !important;
        }
    }

    input[type="submit"]{
        margin-left: 0;
        height: 35px;
        float: left;
    }

    input.doc-empty{
        margin-top: 5px;
        position: relative;
        left: 50%;
        transform: translate(-50%);
        -webkit-transform: translate(-50%);
    }

    *::-moz-focus-inner {
        border: 0; 
        padding: 0;
        margin: 0; 
    }

    input{
        font-size: 20px;
    }

    input[type="text"], input[type="password"]{
        width: 300px;
        height: 24px;
    }

    div.login-label{
        padding: 0;
        text-align: left !important;
        color: #AAA;
        margin-bottom: 15px;
        font-size: 13px;
        font-weight: bold;
    }

    .unidade-label{
        color: #000;
        font-size: 15px;
        width: auto;
        padding: 0px 5px;
    }

    .logo{
        width: 120px; 
        position: relative;
        left: 50%;
        transform: translate(-50%, -0%);
        -webkit-transform: translate(-50%, -0%);
        margin-top: 20px;
        margin-bottom: 10px;
        &:hover{
            cursor: default;
        }
    }

    .div-unidades{
        position: relative;
        margin-top: 10px;
        overflow: auto;
        max-height: 60vh;
    }

    .unidades-fieldset{
        margin-top: 10px;
        position: absolute;
        left: 50%;
        transform: translate(-50%);
        -webkit-transform: translate(-50%);
    }

    nav#unidadebar{
        div{
            div.selected{
                color: #0EA633;
                background: #DFD !important;
                &:hover{
                    color: #000;
                    background: linear-gradient(#0EC6C3, #0A8A91) !important;
                }
            }
            div.nodes-linha{
                border: 1px solid;
                border-radius: 8px;
                margin: 5px;
                background: linear-gradient(#FFFFFF, #E6E6E6);
                text-align: center;
                height: 85px;
                &:hover{
                    background: linear-gradient(#0EC6C3, #0A8A91);
                }
                a{
                    div{
                        padding: 6px 0px 30px 0px;
                        line-height: 15px;
                        &:hover, &.active{
                            border-color: #333333;
                            text-decoration: none;
                            color: #FFF;
                        }
                    }
                    outline: 0;
                    text-decoration: none;
                    span{
                        display: block;
                        margin: 0px auto 4px;
                        opacity: 0.9;
                    }
                }
            }
        }
    }
}
