package br.com.celk.bo.service.rest.agendamento.dto;

import java.io.Serializable;

public class ExternalSaveAnamneseResponseDTO implements Serializable {

    private boolean erro;
    private String msgErro;

    public boolean isErro() {
        return erro;
    }

    public void setErro(boolean erro) {
        this.erro = erro;
    }

    public String getMsgErro() {
        return msgErro;
    }

    public void setMsgErro(String msgErro) {
        this.msgErro = msgErro;
    }

    @Override
    public String toString() {
        return "ExternalSaveAnamneseResponseDTO{" +
                "erro=" + erro +
                ", msgErro='" + msgErro + '\'' +
                '}';
    }
}
