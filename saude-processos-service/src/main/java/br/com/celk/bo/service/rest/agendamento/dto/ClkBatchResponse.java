package br.com.celk.bo.service.rest.agendamento.dto;

public class ClkBatchResponse<T> {

    private T results;
    private boolean containErrors = false;

    public boolean isContainErrors() {
        return containErrors;
    }

    public void setContainErrors(boolean containErrors) {
        this.containErrors = containErrors;
    }

    public T getResults() {
        return results;
    }

    public void setResults(T results) {
        this.results = results;
    }

    @Override
    public String toString() {
        return "ClkListResponse{" +
                "results=" + results +
                ", containErrors=" + containErrors +
                '}';
    }
}
