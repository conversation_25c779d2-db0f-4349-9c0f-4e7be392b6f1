package br.com.celk.bo.service.rest.agendamento.dto;

import br.com.celk.appcidadao.dto.agenda.AgendaDTO;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeHorario;

public class TimeExternalScheduleDTOMapper {
    TimeExternalScheduleDTO timeScheduleDTO;

    public TimeExternalScheduleDTOMapper fromAgendaDTO(AgendaDTO agendaDTO) {
        timeScheduleDTO = new TimeExternalScheduleDTO();
        timeScheduleDTO.setTimeScheduleId(agendaDTO.getIdAgendaGradeHorario());
        timeScheduleDTO.setTimeSchedule(agendaDTO.getHora());
        return this;
    }

    public TimeExternalScheduleDTOMapper fromAgendaGradeHorario(AgendaGradeHorario agendaGradeHorario) {
        timeScheduleDTO = new TimeExternalScheduleDTO();
        timeScheduleDTO.setTimeScheduleId(agendaGradeHorario.getCodigo());
        timeScheduleDTO.setTimeSchedule(agendaGradeHorario.getHora());
        return this;
    }

    public TimeExternalScheduleDTO map() {
        return timeScheduleDTO;
    }
}
