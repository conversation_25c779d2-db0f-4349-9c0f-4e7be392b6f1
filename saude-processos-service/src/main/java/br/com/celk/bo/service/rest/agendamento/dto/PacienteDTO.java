package br.com.celk.bo.service.rest.agendamento.dto;

import br.com.celk.appcidadao.dto.agenda.CareUnitDTO;

public class PacienteDTO {

    private Long codigo;
    private String nome;
    private String cpf;
    private String cns;
    private String dataNascimento;
    private String nomeMae;
    private String equipeReferencia;
    private CareUnitDTO unidadeReferencia;
    private EnderecoDTO endereco;
    private Long grupoVacinacao;
    private String nomeGrupoVacinacao;

    public String getNomeGrupoVacinacao() {
        return nomeGrupoVacinacao;
    }

    public void setNomeGrupoVacinacao(String nomeGrupoVacinacao) {
        this.nomeGrupoVacinacao = nomeGrupoVacinacao;
    }

    public Long getGrupoVacinacao() {
        return grupoVacinacao;
    }

    public void setGrupoVacinacao(Long grupoVacinacao) {
        this.grupoVacinacao = grupoVacinacao;
    }

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(String dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getCns() {
        return cns;
    }

    public void setCns(String cns) {
        this.cns = cns;
    }

    public String getNomeMae() {
        return nomeMae;
    }

    public void setNomeMae(String nomeMae) {
        this.nomeMae = nomeMae;
    }

    public String getEquipeReferencia() {
        return equipeReferencia;
    }

    public void setEquipeReferencia(String equipeReferencia) {
        this.equipeReferencia = equipeReferencia;
    }

    public CareUnitDTO getUnidadeReferencia() {
        return unidadeReferencia;
    }

    public void setUnidadeReferencia(CareUnitDTO unidadeReferencia) {
        this.unidadeReferencia = unidadeReferencia;
    }

    public EnderecoDTO getEndereco() {
        return endereco;
    }

    public void setEndereco(EnderecoDTO endereco) {
        this.endereco = endereco;
    }
}
