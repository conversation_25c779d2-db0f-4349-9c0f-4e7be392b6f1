package br.com.celk.bo.service.rest.hospital;

import br.com.celk.bo.hospital.integracao.interfaces.facade.IntegracaoFacade;
import br.com.celk.bo.service.rest.RestRetorno;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.hospital.pedido.dto.ItemPedidoHospitalDTO;
import br.com.ksisolucoes.bo.hospital.pedido.dto.PedidoHospitalDTO;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.List;

import static javax.ws.rs.core.Response.Status.INTERNAL_SERVER_ERROR;

/**
 * REST Web Service
 *
 * <AUTHOR> Marques
 */
@Path("/integracao-ambulatorial")
public class IntegracaoAmbulatorialRestServices {

    private Response sendError(SGKException ex) {
        return Response.status(INTERNAL_SERVER_ERROR)
                .header("codeMessage", RestRetorno.ERRO_NAO_CATALOGADO.getMensagem())
                .entity(ex.getMessage())
                .build();
    }

    @POST
    @Path("/pedido")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Response savePedidoHospital(PedidoHospitalDTO pedido, @Context HttpServletRequest request) {
        try {
            pedido = verificarItens(pedido);
            if (PedidoHospitalDTO.Status.ERRO.value().equals(pedido.getStatus()))
                return Response.status(Response.Status.BAD_REQUEST).entity(pedido).build();
            BOFactoryWicket.getBO(IntegracaoFacade.class).integrarPedido(pedido);
        } catch (SGKException ex) {
            return sendError(ex);
        }
        return Response.ok(pedido).build();
    }

    private PedidoHospitalDTO verificarItens(PedidoHospitalDTO pedidoHospitalDTO) {
        List<ItemPedidoHospitalDTO> itens = new ArrayList<>();
        for (ItemPedidoHospitalDTO item : pedidoHospitalDTO.getItemPedidoHospitalDTOList()) {
            boolean existeItem = LoadManager.getInstance(Produto.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(Produto.PROP_REFERENCIA, item.getCdProdutoAmbulatorial()))
                    .exists();
            item.setStatus(existeItem ? PedidoHospitalDTO.Status.SUCESSO : PedidoHospitalDTO.Status.ERRO);
            itens.add(item);
            if (!existeItem) {
                pedidoHospitalDTO.setStatus(PedidoHospitalDTO.Status.ERRO);
            }
        }
        pedidoHospitalDTO.setItemPedidoHospitalDTOList(itens);
        return pedidoHospitalDTO;
    }
}
