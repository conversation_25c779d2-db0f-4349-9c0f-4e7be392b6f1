package br.com.celk.util;

import org.hamcrest.Factory;
import org.hamcrest.Matcher;
import org.hamcrest.core.SubstringMatcher;

/**
 *
 * <AUTHOR>
 */
public class StringContainsIgnoreCase extends SubstringMatcher  {

    protected StringContainsIgnoreCase (String substring) {
        super(substring);		
    }

    @Override
    protected boolean evalSubstringOf(String s) {
        return contains(StringUtil.removeAcentos(s.toUpperCase()), StringUtil.removeAcentos(substring.toUpperCase()));
    }

    @Override
    protected String relationship() {
        return "containing ignore case";
    }

    @Factory
    public static Matcher<String> containsStringIgnoreCase(String substring) {
        return new StringContainsIgnoreCase (substring);
    }
	
    private boolean contains(String str, String searchStr) {
        if (str == null || searchStr == null) {
            return false;
        }
        return str.indexOf(searchStr) >= 0;
    }
	
}