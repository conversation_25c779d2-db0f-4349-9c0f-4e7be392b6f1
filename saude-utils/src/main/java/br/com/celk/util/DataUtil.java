package br.com.celk.util;

import de.jollyday.Holiday;
import de.jollyday.HolidayManager;
import net.objectlab.kit.datecalc.common.DateCalculator;
import net.objectlab.kit.datecalc.common.DefaultHolidayCalendar;
import net.objectlab.kit.datecalc.common.HolidayHandlerType;
import net.objectlab.kit.datecalc.joda.LocalDateKitCalculatorsFactory;
import org.joda.time.*;
import org.joda.time.chrono.ISOChronology;

import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeConstants;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

public class DataUtil {

    public static final Long TURNO_MANHA = 1L;
    public static final Long TURNO_TARDE = 2L;
    public static final Long TURNO_NOITE = 3L;

    public static final Long PERIODICIDADE_MENSAL = 1L;
    public static final Long PERIODICIDADE_BIMESTRAL = 2L;
    public static final Long PERIODICIDADE_TRIMESTRAL = 3L;
    public static final Long PERIODICIDADE_SEMESTRAL = 6L;
    public static final Long PERIODICIDADE_ANUAL = 12L;

    public static String toIso8601(Date data) {
        SimpleDateFormat sdf;
        sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        return sdf.format(data);
    }

    public static String formatIntegracao(Date data) {
        SimpleDateFormat sdf;
        sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        return sdf.format(data);
    }

    public static String formatIntegracaoDataEnvio(Date data) {
        SimpleDateFormat sdf;
        sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(data);
    }

    private static class WeekendIterator implements Iterator<List<Date>> {

        private final LocalDate end;
        private LocalDate nextDate;

        public WeekendIterator(Date start, Date end) {
            this.end = new LocalDate(end);
            this.nextDate = new LocalDate(start).withDayOfWeek(DateTimeConstants.SATURDAY);
        }

        public boolean hasNext() {
            return nextDate.compareTo(end) <= 0;
        }

        public List<Date> next() {
            LocalDate saturday = nextDate;
            LocalDate monday = saturday.plusDays(1);

            nextDate = nextDate.plusWeeks(1);

            if (monday.isAfter(end)) {
                return Arrays.asList(saturday.toDate());
            }

            return Arrays.asList(saturday.toDate(), monday.toDate());
        }

        @Override
        public void remove() {
            throw new UnsupportedOperationException();
        }
    }

    private static class DayIterator implements Iterator<Date> {

        private final LocalDate end;
        private LocalDate nextDate;

        public DayIterator(Date start, Date end) {
            this.end = new LocalDate(end);
            this.nextDate = new LocalDate(start);
        }

        public boolean hasNext() {
            return nextDate.compareTo(end) <= 0;
        }

        public Date next() {
            LocalDate localDate = nextDate;
            nextDate = nextDate.plusDays(1);
            return localDate.toDate();
        }

        @Override
        public void remove() {
            throw new UnsupportedOperationException();
        }
    }

    /**
     * Retorna a data atual
     *
     * @return data atual.
     */
    public static Date getDataAtual() {
        return new Date();
    }

    /**
     * Retorna a data atual sem hora (00:00:00.000)
     *
     * @return data atual sem hora (00:00:00.000).
     */
    public static Date getDataAtualSemHora() {
        Calendar calendar = new GregorianCalendar();

        calendar.setTime(getDataAtual());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }

    public static Date getDataPrimeiroDiaMesAtual() {
        return adjustDataPrimeiroDiaMes(getDataAtual());
    }

    public static Date getDataUltimoDiaMesAtual() {
        return adjustDataUltimoDiaMes(getDataAtual());
    }

    public static Date adjustDataPrimeiroDiaMes(Date data) {
        Calendar calendar = new GregorianCalendar();

        calendar.setTime(data);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));

        return calendar.getTime();
    }

    public static Date adjustDataUltimoDiaMes(Date data) {
        Calendar calendar = new GregorianCalendar();

        calendar.setTime(data);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);

        return calendar.getTime();
    }

    /**
     * Retorna os segundos, conforme horas e minutos passados por parâmetro
     *
     * @param value
     * @return segundos.
     */
    public static Long getTransformToSeconds(String value) {
        if (value != null) {
            value = value.replaceAll("[^0-9]", "");

            Long hora = Long.valueOf(value.substring(0, 2));
            Long segundos = hora * 3600;
            Long min = Long.valueOf(value.substring(2, 4));
            segundos += min * 60;

            return segundos;
        }

        return 0L;
    }

    /**
     * Retorna a hora e data formatados conforme segundos passado por parâmetro
     *
     * @param value
     * @return hora e data formatados.
     */
    public static String getSecondsToHourMin(String value) {
        if (value != null) {
            Long aux = Long.valueOf(value);

            Long lnHor = (aux / 3600);
            Long lnHor1 = (aux % 3600);
            Long lnMin = (lnHor1 / 60);

            return new StringBuilder().append(lnHor < 10 ? "0" + lnHor : lnHor).append(":").append(lnMin < 10 ? "0" + lnMin : lnMin).toString();
        }
        return null;
    }

    /**
     * Retorna a data em horas, minutos, segundos e milisegundos passado por
     * parâmetro
     *
     * @param data
     * @param horario
     * @return a data em horas, minutos, segundos e milisegundos passado por
     * parâmetro.
     */
    public static Date getDataHora(Date data, String horario) {
        horario = horario.replaceAll("[^0-9]", "");
        int hora = Integer.parseInt(horario.substring(0, 2));
        int minuto = Integer.parseInt(horario.substring(2, 4));

        Calendar c = GregorianCalendar.getInstance();
        c.setTime(data);
        c.set(Calendar.HOUR_OF_DAY, hora);
        c.set(Calendar.MINUTE, minuto);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);

        return c.getTime();
    }

    /**
     * Retorna data conforme mes e ano informado.
     *
     * @param data
     * @return
     */
    public static final Date getMonthYearForDate(String data) {
        Calendar calendar = GregorianCalendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.MONTH, Integer.valueOf(data.substring(0, 2)) - 1);
        calendar.set(Calendar.YEAR, Integer.valueOf(data.substring(3, 7)));

        return calendar.getTime();
    }


    /**
     * Retorna data conforme dia e mes informado.
     *
     * @param data
     * @return
     */
    public static final Date getDayMonthFromDate(String data) {
        Calendar calendar = GregorianCalendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, Integer.valueOf(data.substring(0, 2)));
        calendar.set(Calendar.MONTH, Integer.valueOf(data.substring(3, 5)) - 1);
        calendar.set(Calendar.YEAR, 1);

        return calendar.getTime();
    }

    /**
     * Retorna mes e ano de uma data com separador underline.
     *
     * @param data
     * @return
     */
    public static final String getFormatarMesAnoUnderline(Date data) {
        if (data != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("MM_yyyy", new Locale("pt", "BR"));
            String mesAno = sdf.format(data);
            return mesAno;
        }
        return null;
    }

    /**
     * Retorna mes e ano de uma data com separador traço.
     *
     * @param data
     * @return
     */
    public static final String getFormatarMesAnoTraco(Date data) {
        if (data != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("MM-yyyy", new Locale("pt", "BR"));
            String mesAno = sdf.format(data);
            return mesAno;
        }
        return null;
    }

    /**
     * Retorna dia, mes e ano de uma data com separador underline.
     *
     * @param data
     * @return
     */
    public static final String getFormatarDiaMesAnoUnderline(Date data) {
        if (data != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("MM/yyyy", new Locale("pt", "BR"));
            String mesAno = sdf.format(data);
            return mesAno;
        }
        return null;
    }

    /**
     * Retorna data formatada em mes e ano.
     *
     * @param data
     * @return
     */
    public static final String getFormatarMesAno(Date data) {
        if (data != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("MM/yyyy", new Locale("pt", "BR"));
            String mesAno = sdf.format(data);
            return mesAno;
        }
        return null;
    }

    /**
     * Retorna data formatada em mes e ano.
     *
     * @param data
     * @return
     */
    public static final String getFormatarDiaMes(Date data) {
        if (data != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM", new Locale("pt", "BR"));
            String diaMes = sdf.format(data);
            return diaMes;
        }
        return null;
    }

    /**
     * Retorna data formatada em dia, mes e ano.
     *
     * @param data
     * @return
     */
    public static final String getFormatarDiaMesAno(Date data) {
        if (data != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy", new Locale("pt", "BR"));
            String mesAno = sdf.format(data);
            return mesAno;
        }
        return null;
    }

    /**
     * Retorna data formatada em dia, mes, ano, hora e minutos.
     *
     * @param data
     * @return
     */
    public static final String getFormatarDiaMesAnoHoraMinuto(Date data) {
        if (data != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm", new Locale("pt", "BR"));
            String dataStr = sdf.format(data);
            return dataStr;
        }
        return null;
    }

    /**
     * Retorna data formatada hora minuto ex: 22:15.
     *
     * @param data
     * @return
     */
    public static final String getFormatarHoraMinuto(Date data) {
        if (data != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm", new Locale("pt", "BR"));
            String dataStr = sdf.format(data);
            return dataStr;
        }
        return null;
    }

    /**
     * Retorna data formatada em dia, mes e ano.
     *
     * @param data
     * @return
     */
    public static final String getFormatarDiaMesAnoTraco(Date data) {
        if (data != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy", new Locale("pt", "BR"));
            String mesAno = sdf.format(data);
            return mesAno;
        }
        return null;
    }

    public static final String getFormatarAnoMesDiaTraco(Date data) {
        if (data != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", new Locale("pt", "BR"));
            String mesAno = sdf.format(data);
            return mesAno;
        }
        return null;
    }

    public static final String getDescricaoDiaSemanaAbv(Date data) {
        DateTime dt = new DateTime(data);
        return dt.dayOfWeek().getAsShortText();
    }

    public static final String getDescricaoDiaSemana(Date data) {
        DateTime dt = new DateTime(data);
        return dt.dayOfWeek().getAsText();
    }

    /**
     * Retorna o mês da data atual.<br />
     * Jan = 0, Fev = 1, Mar = 2, ..., Dez = 11; <br /><br />
     * Exemplos:<br />
     * 10/01/2014, return = 0<br />
     * 01/03/2014, return = 2<br />
     * 31/08/2014, return = 7
     *
     * @return int
     */
    public static final int getMes() {
        return getMes(getDataAtual());
    }

    /**
     * Retorna o mês de acordo com a data informada.<br />
     * Exemplos:<br />
     * 10/01/2014, return = 1<br />
     * 01/03/2014, return = 3<br />
     * 31/08/2014, return = 8
     *
     * @param data
     * @return int
     */
    public static final int getMes(Date data) {
        DateTime dt = new DateTime(data);
        return dt.monthOfYear().get();
    }

    /**
     * Retorna o ano da data atual;
     *
     * @return int
     */
    public static final int getAno() {
        return getAno(getDataAtual());
    }

    /**
     * Retorna o ano de acordo com a data informada;
     *
     * @param data
     * @return int
     */
    public static final int getAno(Date data) {
        Calendar calendar = GregorianCalendar.getInstance();
        calendar.setTime(data);

        return calendar.get(Calendar.YEAR);
    }

    public static final int getDia(Date data) {
        Calendar calendar = GregorianCalendar.getInstance();
        calendar.setTime(data);

        return calendar.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * Retorna a periodicidade da data atual;
     *
     * @param periodicidade
     * @return
     */
    public static final DatePeriod getPeriodicidade(Long periodicidade) {
        return getPeriodicidade(periodicidade, getDataAtual(), false);
    }

    /**
     * Retorna a periodicidade de acordo com a data informada;
     *
     * @param periodicidade
     * @param data
     * @return DatePeriod
     */
    public static final DatePeriod getPeriodicidade(Long periodicidade, Date data, boolean ajustarDataFinal) {
        if (periodicidade == null || data == null) {
            return null;
        }
        Calendar calendar = null;
        Calendar calendarFinal = null;

        int mes = getMes(data);
        int ano = getAno(data);
        if (PERIODICIDADE_MENSAL.equals(periodicidade)) {
            //Diminuir 1 do mês para ficar certo com o Calendar, Janeiro inicia com 0
            calendar = new GregorianCalendar(ano, mes - 1, 1);
        } else if (PERIODICIDADE_BIMESTRAL.equals(periodicidade)) {
            if (mes == DateTimeConstants.JANUARY || mes == DateTimeConstants.FEBRUARY) {
                calendar = new GregorianCalendar(ano, Calendar.JANUARY, 1);
            } else if (mes == DateTimeConstants.MARCH || mes == DateTimeConstants.APRIL) {
                calendar = new GregorianCalendar(ano, Calendar.MARCH, 1);
            } else if (mes == DateTimeConstants.MAY || mes == DateTimeConstants.JUNE) {
                calendar = new GregorianCalendar(ano, Calendar.MAY, 1);
            } else if (mes == DateTimeConstants.JULY || mes == DateTimeConstants.AUGUST) {
                calendar = new GregorianCalendar(ano, Calendar.JULY, 1);
            } else if (mes == DateTimeConstants.SEPTEMBER || mes == DateTimeConstants.OCTOBER) {
                calendar = new GregorianCalendar(ano, Calendar.SEPTEMBER, 1);
            } else if (mes == DateTimeConstants.NOVEMBER || mes == DateTimeConstants.DECEMBER) {
                calendar = new GregorianCalendar(ano, Calendar.NOVEMBER, 1);
            } else {
                return null;
            }
        } else if (PERIODICIDADE_TRIMESTRAL.equals(periodicidade)) {
            if (mes >= DateTimeConstants.JANUARY && mes <= DateTimeConstants.MARCH) {
                calendar = new GregorianCalendar(ano, Calendar.JANUARY, 1);
            } else if (mes >= DateTimeConstants.APRIL && mes <= DateTimeConstants.JUNE) {
                calendar = new GregorianCalendar(ano, Calendar.APRIL, 1);
            } else if (mes >= DateTimeConstants.JULY && mes <= DateTimeConstants.SEPTEMBER) {
                calendar = new GregorianCalendar(ano, Calendar.JULY, 1);
            } else if (mes >= DateTimeConstants.OCTOBER && mes <= DateTimeConstants.DECEMBER) {
                calendar = new GregorianCalendar(ano, Calendar.OCTOBER, 1);
            } else {
                return null;
            }
        } else if (PERIODICIDADE_SEMESTRAL.equals(periodicidade)) {
            if (mes >= DateTimeConstants.JANUARY && mes <= DateTimeConstants.JUNE) {
                calendar = new GregorianCalendar(ano, Calendar.JANUARY, 1);
                calendarFinal = new GregorianCalendar(ano, Calendar.JUNE, 30);
            } else if (mes >= DateTimeConstants.JULY && mes <= DateTimeConstants.DECEMBER) {
                calendar = new GregorianCalendar(ano, Calendar.JULY, 1);
                calendarFinal = new GregorianCalendar(ano, Calendar.DECEMBER, 31);
            } else {
                return null;
            }
        } else if (PERIODICIDADE_ANUAL.equals(periodicidade)) {
            calendar = new GregorianCalendar(ano, Calendar.JANUARY, 1);
        }

        if (ajustarDataFinal && calendarFinal != null) {
            return new DatePeriod(calendar.getTime(), calendarFinal.getTime());
        }
        return new DatePeriod(calendar.getTime(), data);
    }

    /**
     * Retorna a junção da data com a hora.
     * <br><b>Ex.:</b> <i>data</i>: <b>29/07/2014</b> 00:00:00.000
     * <br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<i>hora</i>:
     * 01/01/1900 <b>17:20:32.784</b>
     * <br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b>=</b>
     * 29/07/2014 17:20:32.784
     *
     * @param data
     * @param hora
     * @return Date
     */
    public static Date mergeDataHora(Date data, Date hora) {
        if (data == null || hora == null) {
            return null;
        }

        GregorianCalendar dateMerged = new GregorianCalendar();
        dateMerged.setTime(data);

        GregorianCalendar hourToMerge = new GregorianCalendar();
        hourToMerge.setTime(hora);

        dateMerged.set(Calendar.HOUR_OF_DAY, hourToMerge.get(Calendar.HOUR_OF_DAY));
        dateMerged.set(Calendar.MINUTE, hourToMerge.get(Calendar.MINUTE));
        dateMerged.set(Calendar.SECOND, hourToMerge.get(Calendar.SECOND));
        dateMerged.set(Calendar.MILLISECOND, hourToMerge.get(Calendar.MILLISECOND));

        return dateMerged.getTime();
    }

    /**
     * Retorna a junção da data Date com a hora String no formato HH24:MM.
     * <br><b>Ex.:</b> <i>data</i>: <b>29/07/2014</b> 00:00:00.000
     * <br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<i>hora</i>: <b>17:20</b>
     * <br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b>=</b> 29/07/2014 17:20:00.000
     *
     * @param data
     * @param hora
     * @return
     */
    public static Date mergeDataHora(Date data, String hora) {
        if (data == null || hora == null) {
            return null;
        }
        Calendar dataMerged = new GregorianCalendar();
        dataMerged.setTime(data);

        String[] horaMinuto = hora.split(":");
        int horaInt = Integer.parseInt(horaMinuto[0]);
        int minutoInt = Integer.parseInt(horaMinuto[1]);

        dataMerged.set(Calendar.HOUR_OF_DAY, horaInt);
        dataMerged.set(Calendar.MINUTE, minutoInt);

        return dataMerged.getTime();
    }

    /**
     * Compara duas horas
     *
     * @return integer (resultado da comparacao)
     * <br> - (negativo) => hora1 menor que hora2
     * <br> - (zero) => hora1 igual a hora2
     * <br> - (positivo) => hora1 maior que hora2
     */
    public static int compareHour(Date hora1, Date hora2) {
        Calendar c1 = new GregorianCalendar();
        Calendar c2 = new GregorianCalendar();

        c1.setTime(hora1);
        c2.setTime(hora2);

        c2.set(Calendar.DAY_OF_MONTH, c1.get(Calendar.DAY_OF_MONTH));
        c2.set(Calendar.MONTH, c1.get(Calendar.MONTH));
        c2.set(Calendar.YEAR, c1.get(Calendar.YEAR));

        return c1.compareTo(c2);
    }

    /**
     * Retorna os dias de diferença entre duas datas.
     * <br>
     * Nota: data2 - data1
     *
     * @param data1
     * @param data2
     * @return long (retorna os dias de diferença entre as duas datas)
     */
    public static int getDiasDiferenca(Date data1, Date data2) {
        return Days.daysBetween(new LocalDate(data1), new LocalDate(data2)).getDays();
    }

    /**
     * Converte uma data em um XMLGregorianCalendar em modelo sem hora
     *
     * @param data
     * @return
     * @throws ParseException
     * @throws DatatypeConfigurationException
     */
    public static XMLGregorianCalendar converterData(Date data) throws ParseException, DatatypeConfigurationException {
        GregorianCalendar c = new GregorianCalendar();
        c.setTime(data);
        XMLGregorianCalendar xMLGregorianCalendar = DatatypeFactory.newInstance().newXMLGregorianCalendar(c.get(Calendar.YEAR),
                c.get(Calendar.MONTH) + 1, c.get(Calendar.DAY_OF_MONTH), DatatypeConstants.FIELD_UNDEFINED, DatatypeConstants.FIELD_UNDEFINED, DatatypeConstants.FIELD_UNDEFINED, DatatypeConstants.FIELD_UNDEFINED, DatatypeConstants.FIELD_UNDEFINED);
        return xMLGregorianCalendar;
    }

    /**
     * Retorna o mês abreviado. Exemplos:<br />
     * 1, return = Jan<br />
     * 3, return = Mar<br />
     * 7, return = Jul 12, return = Dez
     *
     * @param mes
     * @return String
     */
    @Deprecated
    public static String getMesAbreviado(Long mes) {
        if (mes == null) {
            return "";
        }

        DateTime data = new DateTime(new DateTime().getYear(), mes.intValue(), 1, 0, 0);

        return getMesAbreviado(data.toDate());
    }

    /**
     * Retorna o mês abreviado. Exemplos:<br />
     * 10/01/2014, return = Jan<br />
     * 01/03/2014, return = Mar<br />
     * 31/07/2014, return = Jul
     *
     * @param data
     * @return String
     */
    @Deprecated
    public static String getMesAbreviado(Date data) {
        if (data == null) {
            return "";
        }

        DateTime dateTime = new DateTime(new DateTime().getYear(), getMes(data) + 1, 1, 0, 0);

        return dateTime.monthOfYear().getAsShortText();
    }

    /**
     * Retorna a hora de acordo com a data informada.<br />
     * Exemplos:<br />
     * 10/01/2014 14:18:58, return = 14<br />
     * 01/03/2014 09:20:05, return = 9<br />
     * 31/08/2014 20:12:15, return = 20
     *
     * @param data
     * @return int
     */
    public static final int getHora(Date data) {
        Calendar calendar = GregorianCalendar.getInstance();
        calendar.setTime(data);

        return calendar.get(Calendar.HOUR_OF_DAY);
    }

    /**
     * Retorna o minuto de acordo com a data informada.<br />
     * Exemplos:<br />
     * 10/01/2014 14:18:58, return = 18<br />
     * 01/03/2014 09:20:05, return = 20<br />
     * 31/08/2014 20:12:15, return = 12
     *
     * @param data
     * @return int
     */
    public static final int getMinuto(Date data) {
        Calendar calendar = GregorianCalendar.getInstance();
        calendar.setTime(data);

        return calendar.get(Calendar.MINUTE);
    }

    /**
     * Retorna o segundo de acordo com a data informada.<br />
     * Exemplos:<br />
     * 10/01/2014 14:18:58, return = 58<br />
     * 01/03/2014 09:20:05, return = 5<br />
     * 31/08/2014 20:12:15, return = 15
     *
     * @param data
     * @return int
     */
    public static final int getSegundo(Date data) {
        Calendar calendar = GregorianCalendar.getInstance();
        calendar.setTime(data);

        return calendar.get(Calendar.SECOND);
    }

    /**
     * Retorna a descrição do mês. Exemplos:<br />
     * 1, return = Janeiro<br />
     * 3, return = Março<br />
     * 7, return = Julho 12, return = Dezembro
     *
     * @param mes
     * @return String
     */
    public static String getDescricaoMes(Long mes) {
        if (mes == null) {
            return "";
        }

        DateTime data = new DateTime(new DateTime().getYear(), mes.intValue(), 1, 0, 0);

        return getDescricaoMes(data.toDate());
    }

    /**
     * Retorna a descrição do mês. Exemplos:<br />
     * 10/01/2014, return = Janeiro<br />
     * 01/03/2014, return = Março<br />
     * 31/07/2014, return = Julho
     *
     * @param data
     * @return String
     */
    public static String getDescricaoMes(Date data) {
        if (data == null) {
            return "";
        }

        DateTime dateTime = new DateTime(new DateTime().getYear(), getMes(data), 1, 0, 0);

        return dateTime.monthOfYear().getAsText();
    }

    /**
     * Retorna os feriados brasileiros de um ano passado por parâmetro.
     *
     * @param ano
     * @return
     */
    public static Set<LocalDate> getFeriadosBrasileiros(int ano) {
        HolidayManager gerenciadorDeFeriados = HolidayManager.getInstance(de.jollyday.HolidayCalendar.BRAZIL);
        Set<Holiday> feriados = gerenciadorDeFeriados.getHolidays(ano);

        Set<LocalDate> dataDosFeriados = new HashSet<LocalDate>();
        for (Holiday h : feriados) {
            dataDosFeriados.add(new LocalDate(h.getDate(), ISOChronology.getInstance()));
        }

        return dataDosFeriados;
    }

    /**
     * Verifica se a data passada por parâmetro é um dia útil. Exemplos:<br />
     * false - sábado<br />
     * false - domingo<br />
     * false - natal<br />
     * true - segunda<br />
     *
     * @param data
     * @return boolean
     */
    @Deprecated
    public static boolean getDiaUtil(Date data) {
        //Popula com os feriados brasileiros
        DefaultHolidayCalendar calendarioDeFeriados = new DefaultHolidayCalendar(getFeriadosBrasileiros(getAno(data)));

        LocalDateKitCalculatorsFactory.getDefaultInstance().registerHolidays("BR", calendarioDeFeriados);
        DateCalculator<LocalDate> calendario = LocalDateKitCalculatorsFactory.getDefaultInstance().getDateCalculator("BR", HolidayHandlerType.FORWARD);

        return !calendario.isNonWorkingDay(new LocalDate(new DateTime(data)));
    }

    public static Date addDiasUteis(Date database, int dias) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(database);

        int diasUteis = 0;
        while (diasUteis < dias || !getDiaUtil(calendar.getTime())) {
            if (getDiaUtil(calendar.getTime())) {
                diasUteis++;
            }

            calendar.add(Calendar.DATE, 1);
        }

        return calendar.getTime();
    }

    public static Date getProximaDataUtil(Date database) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(database);
        calendar.add(Calendar.DATE, 1);

        boolean retornoEncontrado = false;
        while (!retornoEncontrado) {
            retornoEncontrado = getDiaUtil(calendar.getTime());
            if (!retornoEncontrado) {
                calendar.add(Calendar.DATE, 1);
            }
        }
        return calendar.getTime();
    }

    /**
     * Retorna os anos de diferença entre duas datas.
     * <br>
     * Nota: data2 - data1
     *
     * @param dataInicial
     * @param dataFinal
     * @return long (retorna os anos de diferença entre as duas datas)configurc
     */
    public static Long getAnosDiferenca(Date dataInicial, Date dataFinal) {
        return (long) Years.yearsBetween(new DateTime(dataInicial), new DateTime(dataFinal)).getYears();
    }

    /**
     * Retorna as horas de diferença entre duas datas.
     * <br>
     * Nota: data2 - data1
     *
     * @param dataInicial
     * @param dataFinal
     * @return long (retorna as horas de diferença entre as duas datas)
     */
    public static Long getHorasDiferenca(Date dataInicial, Date dataFinal) {
        return (long) Hours.hoursBetween(new DateTime(dataInicial), new DateTime(dataFinal)).getHours();
    }

    /**
     * Retorna os minutos de diferença entre duas datas.
     * <br>
     * Nota: data2 - data1
     *
     * @param dataInicial
     * @param dataFinal
     * @return long (retorna os minutos de diferença entre as duas datas)
     */
    public static Long getMinutosDiferenca(Date dataInicial, Date dataFinal) {
        return (long) Minutes.minutesBetween(new DateTime(dataInicial), new DateTime(dataFinal)).getMinutes();
    }

    /**
     * Retorna os segundo de diferença entre duas datas.
     * <br>
     * Nota: data2 - data1
     *
     * @param dataInicial
     * @param dataFinal
     * @return long (retorna os segundo de diferença entre as duas datas)
     */
    public static Long getSegundosDiferenca(Date dataInicial, Date dataFinal) {
        return (long) Seconds.secondsBetween(new DateTime(dataInicial), new DateTime(dataFinal)).getSeconds();
    }

    /**
     * Retorna os meses de diferença entre duas datas.
     * <br>
     * Nota: data2 - data1
     *
     * @param dataInicial
     * @param dataFinal
     * @return long (retorna os meses de diferença entre as duas datas)
     */
    public static Long getMesesDiferenca(Date dataInicial, Date dataFinal) {
        return (long) Months.monthsBetween(new DateTime(dataInicial), new DateTime(dataFinal)).getMonths();
    }

    public static int getAnoAtual() {
        return new DateTime().getYear();
    }

    public static DateTime getFormatarData(Date data, String format) {
//        DateTimeFormat.forPattern(format).parseDateTime(data.toString());
        return DateTime.parse(new DateTime(data).toString(format));
    }

    public static String getDataFormatadaMesString() {
        return getDataFormatadaMesString(new DateTime().toDate());
    }

    public static String getDataFormatadaMesString(Date data) {
        DateTime dt = new DateTime(data);
        return dt.toString("dd") + " de " + dt.monthOfYear().getAsText() + " de " + dt.toString("yyyy");
    }

    public static boolean isHoje(Date data) {
        Date dataSemHora = zerarHoraData(data);
        return dataSemHora.compareTo(getDataAtualSemHora()) == 0;
    }

    public static Date zerarHoraData(Date data) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(data);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static Date setFullTimeData(Date data) {
        return defineHorarioData(data, 23, 59, 59, 999);
    }

    public static boolean dataMaiorQueAtual(Date data) {
        Date dataAtual = getDataAtualSemHora();
        Date dataSemHora = zerarHoraData(data);

        return dataSemHora.compareTo(dataAtual) == 1;
    }

    public static Long turnoData(Date data) {
        DatePeriod matutino = new DatePeriod(defineHorarioData(data, 0, 0, 0, 0), defineHorarioData(data, 11, 59, 59, 999));
        DatePeriod vespertino = new DatePeriod(defineHorarioData(data, 12, 0, 0, 0), defineHorarioData(data, 17, 59, 59, 999));
        DatePeriod noturno = new DatePeriod(defineHorarioData(data, 18, 0, 0, 0), defineHorarioData(data, 23, 59, 59, 999));

        if (matutino.betweenDate(data)) {
            return TURNO_MANHA;
        }

        if (vespertino.betweenDate(data)) {
            return TURNO_TARDE;
        }

        if (noturno.betweenDate(data)) {
            return TURNO_NOITE;
        }

        return null;
    }

    public static Date defineHorarioDataAtual(int hora, int minuto) {
        return defineHorarioDataAtual(hora, minuto, 0);
    }

    public static Date defineHorarioDataAtual(int hora, int minuto, int segundo) {
        return defineHorarioDataAtual(hora, minuto, segundo, 0);
    }

    public static Date defineHorarioDataAtual(int hora, int minuto, int segundo, int milisegundo) {
        return defineHorarioData(getDataAtualSemHora(), hora, minuto, segundo, milisegundo);
    }

    public static Date defineHorarioData(Date database, int hora, int minuto, int segundo, int milisegundo) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(database);
        calendar.set(Calendar.HOUR_OF_DAY, hora);
        calendar.set(Calendar.MINUTE, minuto);
        calendar.set(Calendar.SECOND, segundo);
        calendar.set(Calendar.MILLISECOND, milisegundo);
        return calendar.getTime();
    }

    public static Date getDateSetYear(Date data, Integer ano) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(data);
        cal.set(Calendar.YEAR, ano);
        return cal.getTime();
    }

    public static Date adjustDateToCompetencia(Date data) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(data);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static Date getDateSetDay(Date data, Integer day) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(data);
        calendar.set(Calendar.DAY_OF_MONTH, day);
        return calendar.getTime();
    }

    public static Date zerarHora(Date data) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(data);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static Date finalHora(Date data) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(data);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 59);
        return calendar.getTime();
    }

    public static int mesAnoCompareToAtual(Date mesAnoDate) {
        Date mesAnoAtual = getDataUltimoDiaMesAtual();
        Date adjustDate = adjustDataUltimoDiaMes(mesAnoDate);
        return adjustDate.compareTo(mesAnoAtual);
    }

    public static int dataCompareToAtual(Date data) {
        Date dataAtual = getDataAtual();
        return data.compareTo(dataAtual);
    }

    public static int dataCompareTo(Date data, Date data2) {
        return data.compareTo(data2);
    }

    public static List<Date> getDatesPeriod(Date start, Date end) {
        List<Date> dates = new ArrayList();
        DayIterator dayIterator = new DayIterator(start, end);
        while (dayIterator.hasNext()) {
            dates.add(dayIterator.next());
        }
        return dates;
    }

    public static boolean isWeekend(Date data) {
        DateTime dateTime = new DateTime(data);
        return dateTime.getDayOfWeek() >= DateTimeConstants.SATURDAY;
    }

    public static List<Date> getHolidaysPeriod(Date start, Date end) {
        List<Date> holidays = new ArrayList();

        HolidayManager holidayManager = HolidayManager.getInstance(de.jollyday.HolidayCalendar.BRAZIL);
        Set<Holiday> set = holidayManager.getHolidays(new Interval(start.getTime(), end.getTime()));

        for (Holiday holiday : set) {
            holidays.add(holiday.getDate().toDate());
        }
        return holidays;
    }

    public static List<Date> getWeekendDaysPeriod(Date start, Date end) {
        List<Date> weekendDays = new ArrayList();
        WeekendIterator dayOfWeekIterator = new WeekendIterator(start, end);
        while (dayOfWeekIterator.hasNext()) {
            weekendDays.addAll(dayOfWeekIterator.next());
        }
        return weekendDays;
    }

    /**
     * @param data
     * @return long (Retorna o número da semana do mês da data passada por parâmetro)
     */
    public static Long getWeekOfMonth(Date data) {
        Calendar c = Calendar.getInstance();
        c.setTime(data);
        c.setMinimalDaysInFirstWeek(1);
        return new Long(c.get(Calendar.WEEK_OF_MONTH));
    }

    public static String getDataPorExtenso(Date data) {
        DateFormat formatter = new SimpleDateFormat("EEEE, dd 'de' MMMM 'de' yyyy");

        synchronized (formatter) {
            formatter.setTimeZone(TimeZone.getDefault());
            return formatter.format(data);
        }

    }

    public static Date stringToDateHourMinMs(String data) throws Exception {
        if (data == null || data.equals(""))
            return null;

        Date date = null;
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
            date = formatter.parse(data);
        } catch (ParseException e) {
            throw e;
        }
        return date;
    }

    public static java.sql.Date stringToDateHour(String data) throws Exception {
        if (data == null || data.equals(""))
            return null;

        java.sql.Date date = null;
        try {
            DateFormat formatter = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
            date = new java.sql.Date(formatter.parse(data).getTime());
        } catch (ParseException e) {
            throw e;
        }
        return date;
    }

    public static java.sql.Date stringToFormater(String data) throws Exception {
        if (data == null || data.equals(""))
            return null;

        java.sql.Date date = null;
        try {
            DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            date = new java.sql.Date(formatter.parse(data).getTime());
        } catch (ParseException e) {
            throw e;
        }
        return date;
    }

    public static java.sql.Date stringParaDataHora(String data) throws Exception {
        if (data == null || data.equals(""))
            return null;

        java.sql.Date date = null;
        try {
            DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            date = new java.sql.Date(formatter.parse(data).getTime());
        } catch (ParseException e) {
            throw e;
        }
        return date;
    }

    public static java.sql.Date stringToDate(String data) throws Exception {
        if (data == null || data.equals(""))
            return null;

        java.sql.Date date = null;
        try {
            DateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
            date = new java.sql.Date(formatter.parse(data).getTime());
        } catch (ParseException e) {
            throw e;
        }
        return date;
    }

    public static Date adjustDataAddMinutos(Date data, int minutos) {
        Calendar calendar = new GregorianCalendar();

        calendar.setTime(data);
        calendar.add(Calendar.MINUTE, minutos);
        return calendar.getTime();
    }

    public static Date adjustDataAddDias(Date data, int dias) {
        return LocalDateTime.fromDateFields(data).plusDays(dias).toDate();
    }

    public static Date adjustDataRemoveDias(Date data, int dias) {
        return LocalDateTime.fromDateFields(data).minusDays(dias).toDate();
    }

    public static Date getDataAcrescimoDiasDataAtual(Integer dias) {
        Calendar calendar = new GregorianCalendar();
        if (dias == null || dias == 0) {
            return calendar.getTime();
        }

        calendar.add(Calendar.DAY_OF_WEEK, dias);
        return calendar.getTime();
    }

    public static Date getDataAcrescimoDias(Date data, Integer dias) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(data);
        if (dias == null || dias == 0) {
            return calendar.getTime();
        }

        calendar.add(Calendar.DAY_OF_WEEK, dias);
        return calendar.getTime();
    }

    public static String formatDate24H(Date date) {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
    }

    public static String formatDateSemSegundo24H(Date date) {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm").format(date);
    }

    /**
     * Verifica se a primeira data vem ANTES da segunda
     *
     * @param data1
     * @param data2
     * @return
     */
    public static boolean isDateBefore(Date data1, Date data2) {
        if (data1 == null || data2 == null) return false;
        return (data1.compareTo(data2) < 0);
    }

    /**
     * Verifica se a primeira data vem ANTES da segunda
     *
     * @param data1
     * @param data2
     * @return
     */
    public static boolean isDateBeforeSemHora(Date data1, Date data2) {
        if (data1 == null || data2 == null) return false;
        data1 = zerarHoraData(data1);
        data2 = zerarHoraData(data2);

        return (data1.compareTo(data2) < 0);
    }

    /**
     * Verifica se a primeira data vem DEPOIS da segunda
     *
     * @param data1
     * @param data2
     * @return
     */
    public static boolean isDateAfter(Date data1, Date data2) {
        if (data1 == null || data2 == null) return false;
        return (data1.compareTo(data2) > 0);
    }

    /**
     * Verifica se a primeira data vem DEPOIS da segunda
     *
     * @param data1
     * @param data2
     * @return
     */
    public static boolean isDateAfterSemHora(Date data1, Date data2) {
        if (data1 == null || data2 == null) return false;
        data1 = zerarHoraData(data1);
        data2 = zerarHoraData(data2);
        return (data1.compareTo(data2) > 0);
    }

    /**
     * Verifica se a primeira data é IGUAL a segunda
     *
     * @param data1
     * @param data2
     * @return
     */
    public static boolean isDateEqual(Date data1, Date data2) {
        if (data1 == null || data2 == null) return false;
        return (data1.compareTo(data2) == 0);
    }

    public static boolean isDateEqualSemHora(Date data1, Date data2) {
        if (data1 == null || data2 == null) return false;
        data1 = zerarHoraData(data1);
        data2 = zerarHoraData(data2);
        return (data1.compareTo(data2) == 0);
    }

    public static int getSemester(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int month = calendar.get(Calendar.MONTH);

        return (month < 6) ? 1 : 2;
    }

    public static final String getFormatarDiaMesAnoSomenteNumeros(Date data) {
        if (data != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("ddMMyyyy", new Locale("pt", "BR"));
            String diaMesAno = sdf.format(data);
            return diaMesAno;
        }
        return null;
    }

    public static Date addAnos(Date data, int anos){
        Calendar calendar = new GregorianCalendar();

        calendar.setTime(data);
        calendar.add(Calendar.YEAR, anos);
        return calendar.getTime();
    }

    public static int calcularDiferencaEmDias(Date data1, Date data2) {
        long diferencaEmMilissegundos = Math.abs(data2.getTime() - data1.getTime());
        return (int) TimeUnit.DAYS.convert(diferencaEmMilissegundos, TimeUnit.MILLISECONDS);
    }
}
