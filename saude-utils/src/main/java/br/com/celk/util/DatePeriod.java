package br.com.celk.util;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class DatePeriod implements Serializable {

    private Date dataInicial;
    private Date dataFinal;

    public DatePeriod(Date dataInicial, Date dataFinal) {
        this.dataInicial = dataInicial;
        this.dataFinal = dataFinal;
    }

    public Date getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = dataInicial;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public boolean betweenDate(Date data) {
        return data.compareTo(dataInicial) >= 0 && data.compareTo(dataFinal) <= 0;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 59 * hash + (this.dataInicial != null ? this.dataInicial.hashCode() : 0);
        hash = 59 * hash + (this.dataFinal != null ? this.dataFinal.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final DatePeriod other = (DatePeriod) obj;
        if (this.dataInicial != other.dataInicial && (this.dataInicial == null || !this.dataInicial.equals(other.dataInicial))) {
            return false;
        }
        if (this.dataFinal != other.dataFinal && (this.dataFinal == null || !this.dataFinal.equals(other.dataFinal))) {
            return false;
        }
        return true;
    }

}
